<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard POC</title>

    <script src="./css-reference.js"></script>
    <script src="./js-reference.js"></script>
  </head>

  <body>
    <!-- Dashboard Header -->
    <script src="./navbar.js"></script>
    <script src="./widgetsection.js"></script>

    <div id="dashboard-main">
      <div class="container-fluid">
        <!-- Grid Container -->
        <div class="row">
          <div class="col-12 px-2">
            <div id="grid-container" class="grid-stack">
              <script src="cobalt.js"></script>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="./modals.js"></script>

    <!-- Footer section  -->
    <script src="./footer.js"></script>
    <!-- Required <PERSON>ripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://gridstackjs.com/node_modules/gridstack/dist/gridstack-all.js"></script>

    <script src="js/gridstack-autoscroll-auto.js"></script>
    <!-- Widget Comments Script -->
    <script src="js/widget-comments.js"></script>

    <!-- Inline Editing Script -->
    <script src="js/inlineEdit.js"></script>

    <!-- Section Auto-Resize Script -->
    <script src="js/sectionAutoResize.js"></script>

    <!-- CSS for sizeToContent support -->
    <style>
      /* Simplified grid styling */
      .grid-stack {
        min-height: 50px; /* Reduced from 100px */
      }

      .grid-stack-item {
        min-height: auto !important;
      }

      .grid-stack-item-content {
        overflow: auto;
      }

      /* Simplified section container styling */
      .section-container-widget {
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .section-content {
        flex: 1;
        overflow: auto;
      }

      /* Simplified nested grid styling */
      .grid-stack-nested {
        height: 100% !important;
        overflow: auto;
      }

      .grid-stack-item-nested {
        min-height: auto !important;
      }

      /* Improved dropzone styling */
      .grid-stack-nested:empty::before {
        content: "Drop widgets here";
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(0, 177, 156, 0.6);
        font-style: italic;
        height: 100%;
        min-height: 80px; /* Reduced from 150px */
      }

      /* Active dropzone styling */
      .active-dropzone {
        border-color: rgba(0, 177, 156, 0.5) !important;
        background-color: rgba(0, 177, 156, 0.1) !important;
      }

      /* Remove excessive transitions */
      .grid-stack-item {
        transition: none; /* Remove height transitions */
      }

      /* Ensure proper widget content sizing */
      .widget {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .widget-body {
        flex: 1;
        overflow: auto;
      }

      /* Enhanced drag and drop visual feedback */
      .grid-stack-item.ui-draggable-dragging {
        z-index: 1000;
        opacity: 0.8;
        transform: rotate(2deg);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
      }

      /* Prevent section containers from moving during drag operations */
      .section-container-widget.drag-target {
        border: 2px dashed rgba(0, 177, 156, 0.7);
        background-color: rgba(0, 177, 156, 0.1);
      }

      /* Stable positioning during drag operations */
      .grid-stack-item.ui-draggable-dragging .section-container-widget {
        pointer-events: none;
      }

      /* Better nested grid drop zone indication */
      .grid-stack-nested.drag-over {
        border: 2px solid rgba(0, 177, 156, 0.8);
        background-color: rgba(0, 177, 156, 0.15);
        box-shadow: inset 0 0 10px rgba(0, 177, 156, 0.3);
      }

      /* Visual feedback for active drag operations in nested grids */
      .grid-stack-nested.drag-active {
        border: 2px dashed rgba(0, 177, 156, 0.6);
        background-color: rgba(0, 177, 156, 0.08);
      }

      /* Prevent layout shifts during drag operations */
      .grid-stack-item.ui-draggable-dragging {
        transition: none !important;
      }

      .grid-stack-item.ui-resizable-resizing {
        transition: none !important;
      }

      /* Visual feedback for drag operations */
      body.dragging-in-nested-grid .grid-stack-nested {
        background-color: rgba(0, 177, 156, 0.05);
      }

      body.main-grid-dragging .grid-stack-nested {
        border-color: rgba(0, 177, 156, 0.5);
      }

      /* Smooth transitions for auto-resize when not dragging */
      .grid-stack-item:not(.ui-draggable-dragging):not(.ui-resizable-resizing) {
        transition: height 0.2s ease-out;
      }
    </style>

    <script type="text/javascript">
      // Initialize widget counter
      let widgetCount = 0;

      // Console helper functions for testing auto-resize
      window.testAutoResize = {
        // Add a section container
        addSection: function () {
          return addSectionContainerWidget();
        },

        // Add test widgets to the most recent section
        addTestWidgets: function (sectionId) {
          if (!sectionId) {
            // Find the most recent section
            const sections = document.querySelectorAll(".section-content");
            if (sections.length === 0) {
              console.error(
                "No section containers found. Add one first with testAutoResize.addSection()"
              );
              return;
            }
            sectionId = sections[sections.length - 1].id;
          }

          const container = document.getElementById(sectionId);
          if (!container) {
            console.error("Section not found:", sectionId);
            return;
          }

          const gridElement = container.querySelector(".grid-stack");
          if (!gridElement || !gridElement.gridstack) {
            console.error("Could not find grid in section container");
            return;
          }

          const nestedGrid = gridElement.gridstack;

          // Clear existing widgets
          nestedGrid.removeAll();

          // Add widgets with different heights
          nestedGrid.addWidget({
            x: 0,
            y: 0,
            w: 2,
            h: 2,
            content:
              '<div class="widget p-2 h-100" style="background: #28a745; color: white;"><div class="widget-header"><strong>Small (2h)</strong></div><div class="widget-body d-flex align-items-center justify-content-center"><div>Small Content</div></div></div>',
          });

          nestedGrid.addWidget({
            x: 2,
            y: 0,
            w: 2,
            h: 4,
            content:
              '<div class="widget p-2 h-100" style="background: #ffc107; color: black;"><div class="widget-header"><strong>Medium (4h)</strong></div><div class="widget-body d-flex align-items-center justify-content-center"><div>Medium Content<br>More lines<br>Extra content</div></div></div>',
          });

          nestedGrid.addWidget({
            x: 4,
            y: 0,
            w: 2,
            h: 6,
            content:
              '<div class="widget p-2 h-100" style="background: #dc3545; color: white;"><div class="widget-header"><strong>Tall (6h)</strong></div><div class="widget-body d-flex align-items-center justify-content-center"><div>Tall Content<br>Line 1<br>Line 2<br>Line 3<br>Line 4<br>Line 5</div></div></div>',
          });

          console.log(
            `Added test widgets to section ${sectionId} - should auto-resize to accommodate tallest widget (6 units)`
          );
        },

        // Show monitored sections
        showMonitored: function () {
          if (window.sectionAutoResize) {
            const sections = window.sectionAutoResize.getMonitoredSections();
            console.log("Monitored sections:", sections);
            return sections;
          } else {
            console.error("Auto-resize system not initialized");
            return [];
          }
        },

        // Manually trigger resize for a section
        resizeSection: function (sectionId) {
          if (window.sectionAutoResize) {
            window.sectionAutoResize.resizeSection(sectionId);
            console.log(`Triggered resize for section: ${sectionId}`);
          } else {
            console.error("Auto-resize system not initialized");
          }
        },

        // Manually retry monitoring for a section
        retrySection: function (sectionId) {
          if (window.sectionAutoResize) {
            window.sectionAutoResize.retrySection(sectionId);
            console.log(`Retrying monitoring for section: ${sectionId}`);
          } else {
            console.error("Auto-resize system not initialized");
          }
        },

        // Get all section containers and their monitoring status
        getStatus: function () {
          const sections = document.querySelectorAll(
            ".section-container-widget"
          );
          const status = [];

          sections.forEach((section, index) => {
            const sectionId =
              section.querySelector(".section-content")?.id ||
              `unknown-${index}`;
            const hasNestedGrid = !!section.querySelector(".grid-stack");
            const isMonitored = window.sectionAutoResize
              ? window.sectionAutoResize
                  .getMonitoredSections()
                  .includes(sectionId)
              : false;

            status.push({
              sectionId,
              hasNestedGrid,
              isMonitored,
              element: section,
            });
          });

          console.table(status);
          return status;
        },
      };

      // Log usage instructions
      console.log(`
🎯 Auto-Resize Test Functions:
- testAutoResize.addSection() - Add a new section container
- testAutoResize.addTestWidgets() - Add widgets with different heights to latest section
- testAutoResize.showMonitored() - Show all monitored sections
- testAutoResize.resizeSection(sectionId) - Manually trigger resize
- testAutoResize.retrySection(sectionId) - Retry monitoring for a section
- testAutoResize.getStatus() - Get status of all sections and their monitoring

Example usage:
1. testAutoResize.addSection()
2. testAutoResize.addTestWidgets()
3. testAutoResize.getStatus() - Check if monitoring is working
4. If monitoring failed, use testAutoResize.retrySection(sectionId)
      `);

      function addEvents(grid, id) {
        let g = id !== undefined ? "grid" + id + " " : "";

        grid
          .on("added removed change", function (event, items) {
            let str = "";
            items.forEach(function (item) {
              str +=
                " (" +
                item.x +
                "," +
                item.y +
                " " +
                item.w +
                "x" +
                item.h +
                ")";
            });
            console.log(
              g + event.type + " " + items.length + " items (x,y w h):" + str
            );
          })
          .on("enable", function (event) {
            let grid = event.target;
            console.log(g + "enable");
          })
          .on("disable", function (event) {
            let grid = event.target;
            console.log(g + "disable");
          })
          .on("dragstart", function (event, el) {
            let n = el.gridstackNode;
            let x = el.getAttribute("gs-x"); // verify node (easiest) and attr are the same
            let y = el.getAttribute("gs-y");
            console.log(
              g +
                "dragstart " +
                (n.content || "") +
                " pos: (" +
                n.x +
                "," +
                n.y +
                ") = (" +
                x +
                "," +
                y +
                ")"
            );
          })
          .on("drag", function (event, el) {
            let n = el.gridstackNode;
            let x = el.getAttribute("gs-x"); // verify node (easiest) and attr are the same
            let y = el.getAttribute("gs-y");
            // console.log(g + 'drag ' + (n.content || '') + ' pos: (' + n.x + ',' + n.y + ') = (' + x + ',' + y + ')');
          })
          .on("dragstop", function (event, el) {
            let n = el.gridstackNode;
            let x = el.getAttribute("gs-x"); // verify node (easiest) and attr are the same
            let y = el.getAttribute("gs-y");
            console.log(
              g +
                "dragstop " +
                (n.content || "") +
                " pos: (" +
                n.x +
                "," +
                n.y +
                ") = (" +
                x +
                "," +
                y +
                ")"
            );
          })
          .on("dropped", function (event, previousNode, newNode) {
            if (previousNode) {
              console.log(
                g + "dropped - Removed widget from grid:",
                previousNode
              );
            }
            if (newNode) {
              console.log(g + "dropped - Added widget in grid:", newNode);
            }
          })
          .on("resizestart", function (event, el) {
            let n = el.gridstackNode;
            let rec = el.getBoundingClientRect();
            console.log(
              `${g} resizestart ${n.content || ""} size: (${n.w}x${
                n.h
              }) = (${Math.round(rec.width)}x${Math.round(rec.height)})px`
            );
          })
          .on("resize", function (event, el) {
            let n = el.gridstackNode;
            let rec = el.getBoundingClientRect();
            console.log(
              `${g} resize ${n.content || ""} size: (${n.w}x${
                n.h
              }) = (${Math.round(rec.width)}x${Math.round(rec.height)})px`
            );
          })
          .on("resizestop", function (event, el) {
            let n = el.gridstackNode;
            let rec = el.getBoundingClientRect();
            console.log(
              `${g} resizestop ${n.content || ""} size: (${n.w}x${
                n.h
              }) = (${Math.round(rec.width)}x${Math.round(rec.height)})px`
            );
          });
      }
      // This render callback is used to set the innerHTML when loading a widget
      GridStack.renderCB = function (el, widget) {
        if (widget.content) el.innerHTML = widget.content;
      };

      // Global variables
      let staticGrid = false;
      let count = 0;

      // MCP (Model-Controller-Presenter) Pattern Implementation

      // Model - Stores the state and data
      class GridModel {
        constructor() {
          this.selectedNode = null;
          this.grids = {};
        }

        setSelectedNode(node) {
          this.selectedNode = node;
        }

        getSelectedNode() {
          return this.selectedNode;
        }

        registerGrid(id, gridInstance) {
          this.grids[id] = gridInstance;
        }

        getGrid(id) {
          return this.grids[id];
        }

        getAllGrids() {
          return Object.values(this.grids);
        }
      }

      // Controller - Handles the business logic
      class GridController {
        constructor(model) {
          this.model = model;
        }

        selectNode(node) {
          // Deselect previously selected node if any
          const previousNode = this.model.getSelectedNode();
          if (previousNode) {
            this.deselectNode(previousNode);
          }

          // Select the new node
          this.model.setSelectedNode(node);

          // Highlight the selected node
          if (node) {
            node.classList.add("selected-node");
          }
        }

        deselectNode(node) {
          if (node) {
            node.classList.remove("selected-node");
          }
          if (this.model.getSelectedNode() === node) {
            this.model.setSelectedNode(null);
          }
        }

        getSelectedNodeInfo() {
          const selectedNode = this.model.getSelectedNode();
          if (!selectedNode) {
            return { status: "error", message: "No node selected" };
          }

          // Find which grid this node belongs to
          const gridNode = selectedNode.closest(".grid-stack-item");
          if (!gridNode || !gridNode.gridstackNode) {
            return {
              status: "error",
              message: "Selected node is not a grid item",
            };
          }

          // Get the grid instance
          const gridElement = gridNode.closest(".grid-stack");
          if (!gridElement || !gridElement.gridstack) {
            return {
              status: "error",
              message: "Cannot find grid for selected node",
            };
          }

          const grid = gridElement.gridstack;
          const gridstackNode = gridNode.gridstackNode;

          return {
            status: "success",
            node: gridstackNode,
            grid: grid,
            element: gridNode,
            content: selectedNode.innerHTML,
            position: {
              x: gridstackNode.x,
              y: gridstackNode.y,
              w: gridstackNode.w,
              h: gridstackNode.h,
            },
            gridId: grid.opts.id || "unknown",
          };
        }

        removeSelectedNode() {
          const selectedNode = this.model.getSelectedNode();
          if (!selectedNode) {
            return { status: "error", message: "No node selected" };
          }

          // Find which grid this node belongs to
          const gridNode = selectedNode.closest(".grid-stack-item");
          if (!gridNode || !gridNode.gridstackNode) {
            return {
              status: "error",
              message: "Selected node is not a grid item",
            };
          }

          // Get the grid instance
          const gridElement = gridNode.closest(".grid-stack");
          if (!gridElement || !gridElement.gridstack) {
            return {
              status: "error",
              message: "Cannot find grid for selected node",
            };
          }

          const grid = gridElement.gridstack;

          // Remove the node from the grid
          grid.removeWidget(gridNode);

          // Clear the selection
          this.model.setSelectedNode(null);

          return { status: "success", message: "Node removed successfully" };
        }
      }

      // Presenter - Handles the UI updates
      class GridPresenter {
        constructor(model, controller) {
          this.model = model;
          this.controller = controller;
          this.setupEventListeners();
        }

        setupEventListeners() {
          // Add click event listeners to all grid items
          document.addEventListener("click", (event) => {
            // Check if clicked on a grid item content
            const gridItemContent = event.target.closest(
              ".grid-stack-item-content"
            );
            if (gridItemContent) {
              this.controller.selectNode(gridItemContent);
              event.stopPropagation();
            } else if (!event.target.closest(".grid-stack-item")) {
              // Clicked outside any grid item, deselect
              const selectedNode = this.model.getSelectedNode();
              if (selectedNode) {
                this.controller.deselectNode(selectedNode);
              }
            }
          });
        }
      }

      // Nested grid child layouts
      let sub1 = [
        { x: 0, y: 0 },
        { x: 1, y: 0 },
        { x: 2, y: 0 },
        { x: 3, y: 0 },
        { x: 0, y: 1 },
        { x: 1, y: 1 },
      ];
      let sub2 = [
        { x: 0, y: 0, h: 2 },
        { x: 1, y: 1, w: 2 },
      ];
      // Assign a simple content (incrementing count) for each nested child
      [...sub1, ...sub2].forEach((d) => (d.content = String(count++)));

      // Main grid options (including nested grids)
      let options = {
        staticGrid: staticGrid,
        cellHeight: 60, // Changed from 'auto' to fixed height
        margin: 5,
        minRow: 2,
        acceptWidgets: true,
        id: "main",
        sizeToContent: false, // Disable auto-resizing for main grid
        resizable: { handles: "se,e,s,sw,w" },
        // Remove the children array to start with an empty grid
        children: [], // Empty array for clean initial state
      };

      // Create the main grid inside the dedicated grid container
      let grid = GridStack.addGrid(
        document.getElementById("grid-container"),
        options
      );

      // Optionally attach debug event handlers
      let gridEls = GridStack.getElements(".grid-stack");
      gridEls.forEach(function (gridEl) {
        let g = gridEl.gridstack;
        if (typeof addEvents === "function") {
          addEvents(g, g.opts.id);
        }
      });

      // Initialize the MCP pattern
      const gridModel = new GridModel();
      const gridController = new GridController(gridModel);
      const gridPresenter = new GridPresenter(gridModel, gridController);

      // Register the main grid with the model
      gridModel.registerGrid("main", grid);

      // Function to check the selected node
      function checkSelectedNode() {
        const info = gridController.getSelectedNodeInfo();

        if (info.status === "error") {
          alert(info.message);
          return;
        }

        // Create a formatted message with the node information
        const message =
          `Selected Node Information:\n\n` +
          `Grid ID: ${info.gridId}\n` +
          `Position: (${info.position.x}, ${info.position.y})\n` +
          `Size: ${info.position.w}x${info.position.h}\n` +
          `Content: ${info.content.substring(0, 50)}${
            info.content.length > 50 ? "..." : ""
          }`;

        alert(message);
        console.log("Selected Node Details:", info);
      }

      // Function to remove the selected node
      function removeSelectedNode() {
        const result = gridController.removeSelectedNode();
        if (result.status === "error") {
          alert(result.message);
        } else {
          console.log(result.message);
        }
      }

      // Functions to switch grid mode
      function setStatic(val) {
        staticGrid = val;
        grid.setStatic(staticGrid);
      }

      // Add a new simple widget to the main grid
      function addWidget() {
        grid.addWidget({ x: 0, y: 100, content: "new item" });
      }

      // Function to create a section container widget
      function addSectionContainerWidget() {
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 6,
          h: 4,
          content: `
            <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;">
              <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                <div>
                 Section Container
                </div>
                <div>
                  <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                    <i class="las la-times"></i>
                  </button>
                </div>
              </div>
              <div class="nested-grid-container" style="height: calc(100% - 40px); overflow: hidden;"></div>
            </div>
          `,
        });

        // Initialize nested grid after the widget is added to DOM
        setTimeout(() => {
          const nestedGridContainer = widget.querySelector(
            ".nested-grid-container"
          );
          const nestedGridElement = document.createElement("div");
          nestedGridElement.className = "grid-stack";
          nestedGridContainer.appendChild(nestedGridElement);

          const nestedGrid = GridStack.init(
            {
              column: 6,
              margin: 5,
              cellHeight: 50,
              acceptWidgets: true,
              float: true,
              children: [],
              animate: false,
              disableOneColumnMode: true,
              removable: true,
              removeTimeout: 0,
            },
            nestedGridElement
          );

          // Store the grid instance on the element for later use
          nestedGridElement.gridstack = nestedGrid;
        }, 0);
      }

      // Function to remove a section container
      function removeSectionContainer(button) {
        // Find the section container widget
        const sectionContainer = button.closest(".section-container-widget");
        if (!sectionContainer) {
          console.error("Could not find section container");
          return;
        }

        // Find the grid-stack-item that contains this section
        const gridItem = sectionContainer.closest(".grid-stack-item");
        if (!gridItem) {
          console.error("Could not find grid item");
          return;
        }

        // Find the nested grid inside this section
        const nestedGridContainer = sectionContainer.querySelector(
          ".nested-grid-container"
        );
        if (!nestedGridContainer) {
          console.error("Could not find nested grid container");
          return;
        }

        const nestedGridElement =
          nestedGridContainer.querySelector(".grid-stack");
        if (nestedGridElement) {
          // Find and dispose all chart instances in this section
          const chartContainers =
            nestedGridElement.querySelectorAll(".chart-container");
          chartContainers.forEach((container) => {
            if (container.chart) {
              container.chart.dispose();
            }
          });

          // Find and destroy all Handsontable instances in this section
          const spreadsheetContainers = nestedGridElement.querySelectorAll(
            ".spreadsheet-container"
          );
          spreadsheetContainers.forEach((container) => {
            if (container.hotInstance) {
              container.hotInstance.destroy();
            }
          });

          // Destroy the nested grid instance if it exists
          if (nestedGridElement.gridstack) {
            nestedGridElement.gridstack.destroy(true);
          }
        }

        // Get the main grid instance and remove the section widget
        if (window.grid && window.grid.engine) {
          try {
            window.grid.removeWidget(gridItem);
          } catch (error) {
            console.error("Error removing widget:", error);
          }
        } else {
          console.error("Main grid not initialized");
          // Fallback: remove the grid item directly from DOM if grid instance is not available
          gridItem.remove();
        }
      }

      // Add a new widget to a nested grid specified by selector (e.g. '.sub1' or '.sub2')
      function addNewWidget(selector) {
        let subGrid = document.querySelector(selector).gridstack;
        let node = {
          x: Math.round(6 * Math.random()),
          y: Math.round(5 * Math.random()),
          w: Math.round(1 + Math.random()),
          h: Math.round(1 + Math.random()),
          content: String(count++),
        };
        subGrid.addWidget(node);
        return false;
      }

      // Function to remove a widget
      function removeWidget(element) {
        // Find the widget container
        const widgetElement = element.closest(".grid-stack-item");
        if (widgetElement) {
          // Find any chart containers within the widget
          const chartContainers =
            widgetElement.querySelectorAll(".chart-container");

          // Clean up any amCharts instances and observers
          chartContainers.forEach((container) => {
            // Clean up amCharts v4 instances
            if (container.chart) {
              console.log("Disposing amCharts instance");
              container.chart.dispose();
            }

            // Clean up resize observers
            if (container.resizeObserver) {
              console.log("Disconnecting resize observer");
              container.resizeObserver.disconnect();
            }
          });

          // Remove the widget from the grid
          grid.removeWidget(widgetElement);
        } else {
          console.error("Could not find widget element");
        }
      }

      // Save, Destroy, and Load functions to test serialization
      // Removed save, destroy, and load functions
    </script>

    <!-- Widget Category Switching Script -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Widget category switching
        const categories = document.querySelectorAll(".widget-category");
        const allWidgets = document.querySelectorAll(".widget-item");

        categories.forEach((category) => {
          category.addEventListener("click", function () {
            // Remove active class from all categories
            categories.forEach((c) => c.classList.remove("active"));

            // Add active class to clicked category
            this.classList.add("active");

            // Get the selected category
            const selectedCategory = this.getAttribute("data-category");

            // Show/hide widgets based on category
            allWidgets.forEach((widget) => {
              if (
                widget.classList.contains(`widget-category-${selectedCategory}`)
              ) {
                widget.style.display = "flex";
              } else {
                widget.style.display = "none";
              }
            });
          });
        });

        // Show "insert" category widgets by default
        document
          .querySelector('.widget-category[data-category="insert"]')
          .click();
      });
    </script>

    <!-- Section Gallery Offcanvas -->

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Section Gallery button click handler
        const sectionGalleryBtn = document.querySelector(
          ".section-gallery-btn"
        );
        if (sectionGalleryBtn) {
          sectionGalleryBtn.addEventListener("click", function () {
            // Check if there's an existing instance and dispose it
            const offcanvasElement = document.getElementById(
              "sectionGalleryOffcanvas"
            );
            const existingOffcanvas =
              bootstrap.Offcanvas.getInstance(offcanvasElement);

            if (existingOffcanvas) {
              existingOffcanvas.dispose();
            }

            // Create a new offcanvas instance with explicit options
            const offcanvas = new bootstrap.Offcanvas(offcanvasElement, {
              backdrop: true,
              keyboard: true,
              scroll: false,
            });

            // Show the offcanvas
            offcanvas.show();
          });
        }

        // Template search functionality
        const templateSearch = document.getElementById("templateSearch");
        const templates = document.querySelectorAll(".section-template");

        templateSearch.addEventListener("input", function (e) {
          const searchTerm = e.target.value.toLowerCase();

          templates.forEach((template) => {
            const title = template
              .querySelector(".template-title")
              .textContent.toLowerCase();
            const description = template
              .querySelector(".template-description")
              .textContent.toLowerCase();
            const widgets = Array.from(
              template.querySelectorAll(".widget-item")
            ).map((widget) => widget.textContent.toLowerCase());

            const matches =
              title.includes(searchTerm) ||
              description.includes(searchTerm) ||
              widgets.some((widget) => widget.includes(searchTerm));

            template.style.display = matches ? "block" : "none";
          });
        });

        // Tab switching functionality
        const tabs = document.querySelectorAll(".section-gallery-tab");

        tabs.forEach((tab) => {
          tab.addEventListener("click", function () {
            // Remove active class from all tabs
            tabs.forEach((t) => t.classList.remove("active"));

            // Add active class to clicked tab
            this.classList.add("active");

            const category = this.getAttribute("data-category");

            templates.forEach((template) => {
              if (category === "all") {
                template.style.display = "block";
              } else {
                template.style.display =
                  template.getAttribute("data-type") === category
                    ? "block"
                    : "none";
              }
            });
          });
        });

        // Preview button functionality
        const previewButtons = document.querySelectorAll(
          ".template-preview-btn"
        );

        previewButtons.forEach((button) => {
          button.addEventListener("click", function (e) {
            e.stopPropagation(); // Prevent template selection
            const template = this.closest(".section-template");
            const templateTitle =
              template.querySelector(".template-title").textContent;

            // Here you can implement the preview functionality
            console.log("Preview requested for:", templateTitle);
          });
        });

        // Template selection
        templates.forEach((template) => {
          template.addEventListener("click", function () {
            const templateTitle =
              this.querySelector(".template-title").textContent;
            console.log("Template selected:", templateTitle);
            // Here you can implement the template selection functionality
          });
        });
      });
    </script>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Section Gallery template click handler
        document.querySelectorAll(".section-template").forEach((template) => {
          template.addEventListener("click", function () {
            const type = this.getAttribute("data-type");

            // Call the appropriate function based on type
            switch (type) {
              case "analytics":
                createAnalyticsSection();
                return;
              case "dashboard":
                createDashboardSection();
                return;
              case "comparison":
                createComparisonSection();
                return;
              case "summary":
                createSummarySection();
                return;
              case "trend":
                createTrendSection();
                return;
            }

            const title =
              this.querySelector(".template-title").textContent.trim();
            const widgets = Array.from(
              this.querySelectorAll(".widget-item")
            ).map((widget) => ({
              type: widget.textContent
                .trim()
                .toLowerCase()
                .replace(/\s+/g, "-"),
              title: widget.textContent.trim(),
            }));

            // Create a new section container
            const sectionContainer = document.createElement("div");
            sectionContainer.className = "grid-stack-item";
            sectionContainer.setAttribute("gs-w", "12");
            sectionContainer.setAttribute("gs-h", "6");

            // Create the section content
            const sectionContent = document.createElement("div");
            sectionContent.className =
              "grid-stack-item-content section-container";

            // Create section header
            const sectionHeader = document.createElement("div");
            sectionHeader.className = "section-header";
            sectionHeader.innerHTML = `
              <h5 class="section-title">${title}</h5>
              <div class="section-actions">
                <button class="btn btn-sm btn-outline-secondary edit-section">
                  <i class="las la-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger remove-section">
                  <i class="las la-trash"></i>
                </button>
              </div>
            `;

            // Create grid container for widgets
            const gridContainer = document.createElement("div");
            gridContainer.className = "widget-grid";
            gridContainer.style.cssText =
              "display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; padding: 1rem; height: calc(100% - 48px);";

            // Add widgets to the grid
            widgets.forEach((widget) => {
              const widgetContainer = document.createElement("div");
              widgetContainer.className = "widget-container";
              widgetContainer.style.cssText =
                "background: #fff; border: 1px solid rgba(0,0,0,0.1); border-radius: 0px; padding: 1rem; height: 100%;";

              // Add widget content based on type
              switch (widget.type) {
                case "performance-metrics-widget":
                case "distribution-chart-widget":
                case "trend-analysis-widget":
                  widgetContainer.innerHTML = `<div class="chart-widget" style="height: 100%;"></div>`;
                  break;
                case "revenue-tracker-widget":
                case "expense-monitor-widget":
                case "budget-analysis-widget":
                  widgetContainer.innerHTML = `<div class="financial-widget" style="height: 100%;"></div>`;
                  break;
                case "gallery-grid-widget":
                case "video-player-widget":
                case "audio-player-widget":
                  widgetContainer.innerHTML = `<div class="media-widget" style="height: 100%;"></div>`;
                  break;
                case "data-table-widget":
                case "pdf-report-widget":
                case "print-layout-widget":
                  widgetContainer.innerHTML = `<div class="report-widget" style="height: 100%;"></div>`;
                  break;
                default:
                  widgetContainer.innerHTML = `<div class="default-widget" style="height: 100%;"></div>`;
              }

              gridContainer.appendChild(widgetContainer);
            });

            // Assemble the section container
            sectionContent.appendChild(sectionHeader);
            sectionContent.appendChild(gridContainer);
            sectionContainer.appendChild(sectionContent);

            // Add to the grid
            const grid = document.querySelector(".grid-stack");
            if (grid && window.grid) {
              window.grid.addWidget(sectionContainer);
            }

            // Close the offcanvas
            const offcanvasElement = document.querySelector(
              ".section-gallery-offcanvas"
            );
            const offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
            offcanvas.hide();
          });
        });
      });
    </script>

    <script>
      // Function to initialize line chart
      function initializeLineChart(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) {
          console.error("Container not found:", containerId);
          return;
        }

        // Create root element
        var root = am5.Root.new(containerId);

        // Set themes
        root.setThemes([am5themes_Animated.new(root)]);

        // Create chart
        var chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "none",
            wheelY: "none",
          })
        );

        // Create axes
        var xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "category",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll(data.categories.map((category) => ({ category })));

        var yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        // Create series
        var series = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: data.series[0].name,
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "category",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        // Set data
        series.data.setAll(
          data.series[0].data.map((value, index) => ({
            category: data.categories[index],
            value: value,
          }))
        );

        // Add cursor
        chart.set("cursor", am5xy.XYCursor.new(root, {}));

        // Add legend
        var legend = chart.rightAxes.push(
          am5.Legend.new(root, {
            centerY: am5.p50,
            y: am5.p50,
            layout: root.verticalLayout,
          })
        );
        legend.data.setAll(chart.series.values);

        // Save chart instance to container for later access
        container.chart = chart;
      }

      // Function to initialize spreadsheet
      function initializeSpreadsheet(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) {
          console.error("Container not found:", containerId);
          return;
        }

        new Handsontable(container, {
          data: data,
          rowHeaders: true,
          colHeaders: true,
          height: "100%",
          width: "100%",
          licenseKey: "non-commercial-and-evaluation",
          stretchH: "all",
          autoColumnSize: true,
          contextMenu: true,
          manualRowResize: true,
          manualColumnResize: true,
        });
      }

      // Function to remove individual widget from nested grid
      function removeWidget(button) {
        const widgetItem = button.closest(".grid-stack-item");
        if (!widgetItem) return;

        // Get the nested grid instance
        const nestedGrid = widgetItem.closest(".grid-stack").gridstack;
        if (!nestedGrid) return;

        // Check if it's a chart widget and clean up
        const chartContainer = widgetItem.querySelector(".chart-container");
        if (chartContainer && chartContainer.chart) {
          chartContainer.chart.dispose();
        }

        // Check if it's a spreadsheet widget and clean up
        const spreadsheetContainer = widgetItem.querySelector(
          ".spreadsheet-container"
        );
        if (spreadsheetContainer && spreadsheetContainer.hotInstance) {
          spreadsheetContainer.hotInstance.destroy();
        }

        // Remove the widget from the nested grid
        nestedGrid.removeWidget(widgetItem);
      }

      // Function to create analytics section with spreadsheet and chart
      function createAnalyticsSection() {
        // Create a section container widget
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 12,
          h: 8,
          content: `
            <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;">
              <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                <div>
                  <i class="las la-chart-line"></i> Analytics Section
                </div>
                <div>
                  <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                    <i class="las la-times"></i>
                  </button>
                </div>
              </div>
              <div class="nested-grid-container" style="height: calc(100% - 40px); overflow: hidden;"></div>
            </div>
          `,
        });

        // Wait for the widget to be added to the DOM
        setTimeout(() => {
          const nestedGridContainer = widget.querySelector(
            ".nested-grid-container"
          );
          const nestedGridElement = document.createElement("div");
          nestedGridElement.className = "grid-stack";
          nestedGridContainer.appendChild(nestedGridElement);

          const nestedGrid = GridStack.init(
            {
              column: 12,
              cellHeight: "auto",
              margin: 5,
              float: true,
              children: [],
              removable: true,
              removeTimeout: 0,
              disableOneColumnMode: true,
              animate: false,
            },
            nestedGridElement
          );

          nestedGridElement.gridstack = nestedGrid;

          const spreadsheetId = `spreadsheet-${Date.now()}`;
          const chartId = `chart-${Date.now()}`;

          // Add spreadsheet widget to the nested grid (left side)
          nestedGrid.addWidget({
            x: 0,
            y: 0,
            w: 6,
            h: 6,
            content: `
              <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
                <div class="widget-header mb-2 mb-2">
                  <div>
                    <i class="las la-table"></i> Analytics Data
                  </div>
                  <div>
                    <button class="btn btn-sm btn-link text-dark" data-bs-toggle="offcanvas" data-bs-target="#spreadsheetSettings" aria-controls="spreadsheetSettings">
                      <i class="las la-cog"></i>
                    </button>
                  </div>
                </div>
                <div id="${spreadsheetId}" class="spreadsheet-container" style="flex: 1; overflow: hidden;"></div>
              </div>
            `,
          });

          // Add chart widget to the nested grid (right side)
          nestedGrid.addWidget({
            x: 6,
            y: 0,
            w: 6,
            h: 6,
            content: `
              <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
                <div class="widget-header mb-2 mb-2">
                  <div>
                    <i class="las la-chart-line"></i> Analytics Chart
                  </div>
                  <div>
                    <button class="btn btn-sm btn-link text-dark" data-bs-toggle="offcanvas" data-bs-target="#lineChartSettings" aria-controls="lineChartSettings">
                      <i class="las la-cog"></i>
                    </button>
                  </div>
                </div>
                <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
              </div>
            `,
          });

          // Initialize spreadsheet with sample data
          const spreadsheetData = [
            ["Metric", "Q1", "Q2", "Q3", "Q4"],
            ["Revenue", 100000, 120000, 150000, 180000],
            ["Users", 5000, 6000, 7500, 9000],
            ["Engagement", 75, 78, 82, 85],
            ["Conversion", 2.5, 2.8, 3.2, 3.5],
          ];

          // Wait a bit for the DOM to be ready
          setTimeout(() => {
            const spreadsheetContainer = document.getElementById(spreadsheetId);
            if (spreadsheetContainer) {
              const hot = new Handsontable(spreadsheetContainer, {
                data: spreadsheetData,
                rowHeaders: true,
                colHeaders: true,
                height: "100%",
                licenseKey: "non-commercial-and-evaluation",
                stretchH: "all",
              });
              spreadsheetContainer.hotInstance = hot;
            }

            const chartContainer = document.getElementById(chartId);
            if (chartContainer) {
              const root = am5.Root.new(chartId);
              root.setThemes([am5themes_Animated.new(root)]);

              const chart = root.container.children.push(
                am5xy.XYChart.new(root, {
                  panX: false,
                  panY: false,
                  wheelX: "none",
                  wheelY: "none",
                })
              );

              const cursor = chart.set(
                "cursor",
                am5xy.XYCursor.new(root, {
                  behavior: "none",
                })
              );
              cursor.lineY.set("visible", false);

              const xAxis = chart.xAxes.push(
                am5xy.CategoryAxis.new(root, {
                  categoryField: "quarter",
                  renderer: am5xy.AxisRendererX.new(root, {}),
                  tooltip: am5.Tooltip.new(root, {}),
                })
              );

              const yAxis = chart.yAxes.push(
                am5xy.ValueAxis.new(root, {
                  min: 0,
                  renderer: am5xy.AxisRendererY.new(root, {}),
                })
              );

              const series = chart.series.push(
                am5xy.LineSeries.new(root, {
                  name: "Revenue",
                  xAxis: xAxis,
                  yAxis: yAxis,
                  valueYField: "value",
                  categoryXField: "quarter",
                  tooltip: am5.Tooltip.new(root, {
                    labelText: "{valueY}",
                  }),
                })
              );

              const data = [
                { quarter: "Q1", value: 100000 },
                { quarter: "Q2", value: 120000 },
                { quarter: "Q3", value: 150000 },
                { quarter: "Q4", value: 180000 },
              ];

              xAxis.data.setAll(data);
              series.data.setAll(data);

              series.appear(1000);
              chart.appear(1000, 100);

              chartContainer.chart = root;
            }
          }, 100);

          closeGallery();
        }, 100);
      }

      // Function to remove a section container
      function removeSectionContainer(button) {
        // Find the section container widget
        const sectionContainer = button.closest(".section-container-widget");
        if (!sectionContainer) {
          console.error("Could not find section container");
          return;
        }

        // Find the grid-stack-item that contains this section
        const gridItem = sectionContainer.closest(".grid-stack-item");
        if (!gridItem) {
          console.error("Could not find grid item");
          return;
        }

        // Find the nested grid inside this section
        const nestedGridContainer = sectionContainer.querySelector(
          ".nested-grid-container"
        );
        if (!nestedGridContainer) {
          console.error("Could not find nested grid container");
          return;
        }

        const nestedGridElement =
          nestedGridContainer.querySelector(".grid-stack");
        if (nestedGridElement) {
          // Find and dispose all chart instances in this section
          const chartContainers =
            nestedGridElement.querySelectorAll(".chart-container");
          chartContainers.forEach((container) => {
            if (container.chart) {
              container.chart.dispose();
            }
          });

          // Find and destroy all Handsontable instances in this section
          const spreadsheetContainers = nestedGridElement.querySelectorAll(
            ".spreadsheet-container"
          );
          spreadsheetContainers.forEach((container) => {
            if (container.hotInstance) {
              container.hotInstance.destroy();
            }
          });

          // Destroy the nested grid instance if it exists
          if (nestedGridElement.gridstack) {
            nestedGridElement.gridstack.destroy(true);
          }
        }

        // Get the main grid instance and remove the section widget
        if (window.grid && window.grid.engine) {
          try {
            window.grid.removeWidget(gridItem);
          } catch (error) {
            console.error("Error removing widget:", error);
          }
        } else {
          console.error("Main grid not initialized");
          // Fallback: remove the grid item directly from DOM if grid instance is not available
          gridItem.remove();
        }
      }

      // Function to create dashboard section
      function createDashboardSection() {
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 12,
          h: 12,
          content: `
            <div class="section-container-widget p-2" style="height: 100%; display: flex; flex-direction: column;">
              <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                <div data-editable="true" title="Click to edit title" class="editable-title">
                  Dashboard Section
                </div>
                <div>
                  <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                    <i class="las la-times"></i>
                  </button>
                </div>
              </div>
              <div class="nested-grid-container" style="flex: 1; position: relative;">
                <div class="grid-stack" style="position: absolute; inset: 0; padding: 10px;"></div>
              </div>
            </div>
          `,
        });

        setTimeout(() => {
          const nestedGridContainer = widget.querySelector(
            ".nested-grid-container .grid-stack"
          );

          const nestedGrid = GridStack.init(
            {
              column: 12,
              cellHeight: 60,
              margin: 10,
              float: true,
              children: [],
              removable: true,
              removeTimeout: 0,
              disableOneColumnMode: true,
              animate: false,
              minHeight: 150,
              alwaysShowResizeHandle: true,
              resizable: {
                handles: "all",
              },
            },
            nestedGridContainer
          );

          // Create a unique ID for the stack chart
          const stackChartId = `stackchart-${Date.now()}`;

          // Add Stack Chart Widget (Monthly Trends)
          nestedGrid.addWidget({
            x: 0,
            y: 0,
            w: 8,
            h: 6,
            content: `
              <div class="stack-chart-widget p-2">
                <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                  <div>
                    <i class="las la-layer-group"></i> Monthly Trends
                  </div>
                  <div>
                    <button class="btn btn-sm btn-link text-dark"
                            data-bs-toggle="offcanvas"
                            data-bs-target="#stackChartSettings"
                            aria-controls="stackChartSettings"
                            onclick="initStackChartSettings('${stackChartId}')">
                      <i class="las la-cog"></i>
                    </button>
                  </div>
                </div>
                <div id="${stackChartId}" class="chart-container"></div>
              </div>
            `,
          });

          // Add KPI Cards with adjusted heights
          nestedGrid.addWidget({
            x: 8,
            y: 0,
            w: 4,
            h: 3,
            content: createKPICardWidget("Total Revenue", "$150,000", "+15%"),
            minHeight: 150,
          });

          nestedGrid.addWidget({
            x: 8,
            y: 3,
            w: 4,
            h: 3,
            content: createKPICardWidget("Active Users", "25,000", "+8%"),
            minHeight: 150,
          });

          // Add Data Table with more height
          nestedGrid.addWidget({
            x: 0,
            y: 6,
            w: 12,
            h: 6,
            content: createDataTableWidget("Performance Metrics"),
            minHeight: 300,
          });

          // Add styles to ensure proper height and scrolling
          const style = document.createElement("style");
          style.textContent = `
            .grid-stack > .grid-stack-item > .grid-stack-item-content {
              inset: 0;
              height: 100%;
              overflow: auto;
            }
            .grid-stack-item-content {
              height: 100%;
            }
            .card {
              height: 100%;
              overflow: hidden;
            }
            .card-body {
              height: 100%;
              overflow: hidden;
            }
            .table-responsive {
              overflow-y: auto;
              max-height: calc(100% - 40px);
            }
            .chart-container {
              height: calc(100% - 40px) !important;
            }
          `;
          document.head.appendChild(style);

          // Initialize widgets and update layout
          initializeWidgets();

          // Initialize the stack chart
          setTimeout(() => {
            try {
              if (window.initStackedColumnChart) {
                // Check if the chart container exists
                const chartContainer = document.getElementById(stackChartId);
                if (chartContainer) {
                  // Check if the chart has already been initialized
                  if (!chartContainer.hasAttribute("data-initialized")) {
                    window.initStackedColumnChart(stackChartId);
                    // Mark the chart as initialized
                    chartContainer.setAttribute("data-initialized", "true");
                  } else {
                    console.log("Chart already initialized, skipping");
                  }
                } else {
                  console.error("Chart container not found:", stackChartId);
                }
              } else {
                console.error("initStackedColumnChart function not found");
              }
            } catch (error) {
              console.error("Error initializing stack chart:", error);
            }
          }, 200);

          nestedGrid.compact();

          // Force a resize after a brief delay to ensure proper layout
          setTimeout(() => {
            window.dispatchEvent(new Event("resize"));
            nestedGrid.compact();
          }, 400);

          closeGallery();
        }, 100);
      }

      // Update the Line Chart Widget to ensure proper height
      function createLineChartWidget(title) {
        return `
          <div class="card">
            <div class="card-body d-flex flex-column">
              <h6 class="card-title text-muted mb-3">${title}</h6>
              <div class="chart-container" style="position: relative;"></div>
            </div>
          </div>
        `;
      }

      // Update the KPI Card Widget with minimum height
      function createKPICardWidget(title, value, change) {
        return `
          <div class="card">
            <div class="card-body d-flex flex-column justify-content-between">
              <h6 class="card-title text-muted">${title}</h6>
              <div class="d-flex align-items-center justify-content-between">
                <h3 class="mb-0">${value}</h3>
                <span class="badge ${
                  change.startsWith("+") ? "bg-success" : "bg-danger"
                }">${change}</span>
              </div>
            </div>
          </div>
        `;
      }

      // Update the Data Table Widget with minimum height
      function createDataTableWidget(title) {
        return `
          <div class="card">
            <div class="card-body d-flex flex-column">
              <h6 class="card-title text-muted mb-3">${title}</h6>
              <div class="table-responsive">
                <table class="table table-sm table-hover">
                  <thead>
                    <tr>
                      <th>Metric</th>
                      <th>Current</th>
                      <th>Previous</th>
                      <th>Change</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Revenue</td>
                      <td>$150,000</td>
                      <td>$130,000</td>
                      <td class="text-success">+15%</td>
                    </tr>
                    <tr>
                      <td>Users</td>
                      <td>25,000</td>
                      <td>23,000</td>
                      <td class="text-success">+8%</td>
                    </tr>
                    <tr>
                      <td>Conversion</td>
                      <td>3.2%</td>
                      <td>2.8%</td>
                      <td class="text-success">+14%</td>
                    </tr>
                    <tr>
                      <td>Engagement</td>
                      <td>68%</td>
                      <td>65%</td>
                      <td class="text-success">+4%</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        `;
      }

      // Function to create comparison section
      function createComparisonSection() {
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 12,
          h: 8,
          content: `
            <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;">
              <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                <div>
                  <i class="las la-balance-scale"></i> Comparison Section
                </div>
                <div>
                  <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                    <i class="las la-times"></i>
                  </button>
                </div>
              </div>
              <div class="nested-grid-container" style="height: calc(100% - 40px); overflow: hidden;"></div>
            </div>
          `,
        });

        setTimeout(() => {
          const nestedGridContainer = widget.querySelector(
            ".nested-grid-container"
          );
          const nestedGridElement = document.createElement("div");
          nestedGridElement.className = "grid-stack";
          nestedGridContainer.appendChild(nestedGridElement);

          const nestedGrid = GridStack.init(
            {
              column: 12,
              cellHeight: "auto",
              margin: 5,
              float: true,
              children: [],
              removable: true,
              removeTimeout: 0,
              disableOneColumnMode: true,
              animate: false,
            },
            nestedGridElement
          );

          nestedGridElement.gridstack = nestedGrid;

          // Add Bar Chart for Comparison
          nestedGrid.addWidget({
            x: 0,
            y: 0,
            w: 6,
            h: 6,
            content: createBarChartWidget("Product Comparison"),
          });

          // Add Percentage Comparison
          nestedGrid.addWidget({
            x: 6,
            y: 0,
            w: 6,
            h: 3,
            content: createPercentageWidget("Market Share"),
          });

          // Add Comparison Table
          nestedGrid.addWidget({
            x: 6,
            y: 3,
            w: 6,
            h: 3,
            content: createComparisonTableWidget("Metrics Comparison"),
          });

          initializeWidgets();
          closeGallery();
        }, 100);
      }

      // Function to create summary section
      function createSummarySection() {
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 12,
          h: 8,
          content: `
            <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;">
              <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                <div>
                  <i class="las la-list-alt"></i> Summary Section
                </div>
                <div>
                  <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                    <i class="las la-times"></i>
                  </button>
                </div>
              </div>
              <div class="nested-grid-container" style="height: calc(100% - 40px); overflow: hidden;"></div>
            </div>
          `,
        });

        setTimeout(() => {
          const nestedGridContainer = widget.querySelector(
            ".nested-grid-container"
          );
          const nestedGridElement = document.createElement("div");
          nestedGridElement.className = "grid-stack";
          nestedGridContainer.appendChild(nestedGridElement);

          const nestedGrid = GridStack.init(
            {
              column: 12,
              cellHeight: "auto",
              margin: 5,
              float: true,
              children: [],
              removable: true,
              removeTimeout: 0,
              disableOneColumnMode: true,
              animate: false,
            },
            nestedGridElement
          );

          nestedGridElement.gridstack = nestedGrid;

          // Add Text Summary
          nestedGrid.addWidget({
            x: 0,
            y: 0,
            w: 12,
            h: 2,
            content: createTextSummaryWidget("Executive Summary"),
          });

          // Add Distribution Chart
          nestedGrid.addWidget({
            x: 0,
            y: 2,
            w: 6,
            h: 4,
            content: createPieChartWidget("Category Distribution"),
          });

          // Add Key Points List
          nestedGrid.addWidget({
            x: 6,
            y: 2,
            w: 6,
            h: 4,
            content: createKeyPointsWidget("Key Findings"),
          });

          initializeWidgets();
          closeGallery();
        }, 100);
      }

      // Function to create trend analysis section
      function createTrendSection() {
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 12,
          h: 8,
          content: `
            <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;">
              <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                <div>
                  <i class="las la-chart-line"></i> Trend Analysis Section
                </div>
                <div>
                  <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                    <i class="las la-times"></i>
                  </button>
                </div>
              </div>
              <div class="nested-grid-container" style="height: calc(100% - 40px); overflow: hidden;"></div>
            </div>
          `,
        });

        setTimeout(() => {
          const nestedGridContainer = widget.querySelector(
            ".nested-grid-container"
          );
          const nestedGridElement = document.createElement("div");
          nestedGridElement.className = "grid-stack";
          nestedGridContainer.appendChild(nestedGridElement);

          const nestedGrid = GridStack.init(
            {
              column: 12,
              cellHeight: "auto",
              margin: 5,
              float: true,
              children: [],
              removable: true,
              removeTimeout: 0,
              disableOneColumnMode: true,
              animate: false,
            },
            nestedGridElement
          );

          nestedGridElement.gridstack = nestedGrid;

          // Add Time Series Chart
          nestedGrid.addWidget({
            x: 0,
            y: 0,
            w: 8,
            h: 4,
            content: createTimeSeriesWidget("Historical Trends"),
          });

          // Add Area Chart
          nestedGrid.addWidget({
            x: 8,
            y: 0,
            w: 4,
            h: 4,
            content: createAreaChartWidget("Growth Pattern"),
          });

          // Add Trend Table
          nestedGrid.addWidget({
            x: 0,
            y: 4,
            w: 12,
            h: 4,
            content: createTrendTableWidget("Trend Metrics"),
          });

          initializeWidgets();
          closeGallery();
        }, 100);
      }

      // Helper functions for widget creation
      function createLineChartWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-line"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createKPICardWidget(title, value, change) {
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-square"></i> ${title}
              </div>
            </div>
            <div class="d-flex flex-column justify-content-center align-items-center h-100">
              <h3 class="mb-0">${value}</h3>
              <span class="text-success">${change}</span>
            </div>
          </div>
        `;
      }

      function createDataTableWidget(title) {
        const tableId = `table-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-table"></i> ${title}
              </div>
            </div>
            <div id="${tableId}" class="spreadsheet-container" style="flex: 1 1 auto; min-height: 200px; width: 100%; height: 100%; position: relative;"></div>
          </div>
        `;
      }

      function createBarChartWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-bar"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createPercentageWidget(title) {
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-percentage"></i> ${title}
              </div>
            </div>
            <div class="d-flex justify-content-around align-items-center h-100">
              <div class="text-center">
                <h4>Product A</h4>
                <h2>45%</h2>
              </div>
              <div class="text-center">
                <h4>Product B</h4>
                <h2>35%</h2>
              </div>
              <div class="text-center">
                <h4>Others</h4>
                <h2>20%</h2>
              </div>
            </div>
          </div>
        `;
      }

      function createComparisonTableWidget(title) {
        const tableId = `table-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-arrows-alt-h"></i> ${title}
              </div>
            </div>
            <div id="${tableId}" class="spreadsheet-container" style="flex: 1 1 auto; min-height: 200px; width: 100%; height: 100%; position: relative;"></div>
          </div>
        `;
      }

      function createTextSummaryWidget(title) {
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-file-alt"></i> ${title}
              </div>
            </div>
            <div class="p-3" style="flex: 1; overflow: auto;">
              <p>This is an executive summary of the key findings and insights from our analysis. The data shows significant trends in market performance and user engagement.</p>
              <p>Key highlights include increased revenue growth, improved user retention, and expanding market presence.</p>
            </div>
          </div>
        `;
      }

      function createPieChartWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-pie"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createKeyPointsWidget(title) {
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-list"></i> ${title}
              </div>
            </div>
            <div class="p-3" style="flex: 1; overflow: auto;">
              <ul class="list-unstyled">
                <li class="mb-2"><i class="las la-check text-success me-2"></i> Revenue increased by 25% YoY</li>
                <li class="mb-2"><i class="las la-check text-success me-2"></i> Customer satisfaction reached 92%</li>
                <li class="mb-2"><i class="las la-check text-success me-2"></i> New market penetration successful</li>
                <li class="mb-2"><i class="las la-check text-success me-2"></i> Product adoption rate up by 15%</li>
              </ul>
            </div>
          </div>
        `;
      }

      function createTimeSeriesWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-line"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createAreaChartWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-area"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createTrendTableWidget(title) {
        const tableId = `table-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-table"></i> ${title}
              </div>
            </div>
            <div id="${tableId}" class="spreadsheet-container" style="flex: 1 1 auto; min-height: 200px; width: 100%; height: 100%; position: relative;"></div>
          </div>
        `;
      }

      function initializeWidgets() {
        // Initialize all chart containers
        document.querySelectorAll(".chart-container").forEach((container) => {
          if (!container.chart) {
            const root = am5.Root.new(container.id);
            root.setThemes([am5themes_Animated.new(root)]);

            // Create chart based on container's parent widget type
            if (container.closest('[class*="line-chart"]')) {
              createLineChart(root);
            } else if (container.closest('[class*="bar-chart"]')) {
              createBarChart(root);
            } else if (container.closest('[class*="pie-chart"]')) {
              createPieChart(root);
            } else if (container.closest('[class*="area-chart"]')) {
              createAreaChart(root);
            }

            container.chart = root;
          }
        });

        // Initialize all spreadsheet containers
        document
          .querySelectorAll(".spreadsheet-container")
          .forEach((container) => {
            if (!container.hotInstance) {
              console.log(
                "Initializing Handsontable for container:",
                container.id
              );
              console.log(
                "Container dimensions:",
                container.offsetWidth,
                "x",
                container.offsetHeight
              );

              // Ensure the container has proper dimensions
              if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                console.warn(
                  "Container has zero dimensions, setting explicit size"
                );
                container.style.width = "100%";
                container.style.height = "300px";
                container.style.minHeight = "200px";
              }

              try {
                const hot = new Handsontable(container, {
                  data: generateSampleData(),
                  rowHeaders: true,
                  colHeaders: true,
                  height: "100%",
                  width: "100%",
                  licenseKey: "non-commercial-and-evaluation",
                  stretchH: "all",
                  autoColumnSize: true,
                  contextMenu: true,
                  manualColumnResize: true,
                  manualRowResize: true,
                });
                container.hotInstance = hot;
                console.log(
                  "Handsontable initialized successfully for",
                  container.id
                );
              } catch (error) {
                console.error("Error initializing Handsontable:", error);
              }
            }
          });
      }

      function generateSampleData() {
        return [
          ["Category", "Value 1", "Value 2", "Value 3"],
          ["A", 100, 120, 140],
          ["B", 200, 220, 240],
          ["C", 300, 320, 340],
          ["D", 400, 420, 440],
        ];
      }

      function createLineChart(root) {
        const chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "none",
            wheelY: "none",
          })
        );

        const data = [
          { date: "2023-01", value: 100 },
          { date: "2023-02", value: 120 },
          { date: "2023-03", value: 140 },
          { date: "2023-04", value: 130 },
          { date: "2023-05", value: 170 },
        ];

        const xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "date",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll(data);

        const yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        const series = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Series",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "date",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        series.data.setAll(data);
        chart.set("cursor", am5xy.XYCursor.new(root, {}));
      }

      function createBarChart(root) {
        const chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "none",
            wheelY: "none",
          })
        );

        const data = [
          { category: "A", value1: 100, value2: 90 },
          { category: "B", value1: 120, value2: 110 },
          { category: "C", value1: 140, value2: 130 },
          { category: "D", value1: 130, value2: 120 },
        ];

        const xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "category",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll(data);

        const yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        const series = chart.series.push(
          am5xy.ColumnSeries.new(root, {
            name: "Series",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value1",
            categoryXField: "category",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        series.data.setAll(data);
        chart.set("cursor", am5xy.XYCursor.new(root, {}));
      }

      function createPieChart(root) {
        const chart = root.container.children.push(
          am5percent.PieChart.new(root, {
            layout: root.verticalLayout,
          })
        );

        const data = [
          { category: "A", value: 30 },
          { category: "B", value: 25 },
          { category: "C", value: 20 },
          { category: "D", value: 15 },
          { category: "E", value: 10 },
        ];

        const series = chart.series.push(
          am5percent.PieSeries.new(root, {
            valueField: "value",
            categoryField: "category",
            endAngle: 270,
          })
        );

        series.data.setAll(data);
        series.appear(1000, 100);
      }

      function createAreaChart(root) {
        const chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "none",
            wheelY: "none",
          })
        );

        const data = [
          { date: "2023-01", value: 100 },
          { date: "2023-02", value: 120 },
          { date: "2023-03", value: 140 },
          { date: "2023-04", value: 130 },
          { date: "2023-05", value: 170 },
        ];

        const xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "date",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll(data);

        const yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        const series = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Series",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "date",
            fill: am5.color(0x68ad5c),
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        series.fills.template.setAll({
          fillOpacity: 0.3,
          visible: true,
        });

        series.data.setAll(data);
        chart.set("cursor", am5xy.XYCursor.new(root, {}));
      }

      function closeGallery() {
        const offcanvasElement = document.querySelector(
          ".section-gallery-offcanvas"
        );
        const offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
        if (offcanvas) {
          offcanvas.hide();
        }
      }
    </script>

    <!-- Add this before the closing body tag -->
    <!-- Spreadsheet Settings Offcanvas -->

    <script>
      // Function to remove a section container
      function removeSectionContainer(button) {
        // Find the section container widget
        const sectionContainer = button.closest(".section-container-widget");
        if (!sectionContainer) {
          console.error("Could not find section container");
          return;
        }

        // Find the grid-stack-item that contains this section
        const gridItem = sectionContainer.closest(".grid-stack-item");
        if (!gridItem) {
          console.error("Could not find grid item");
          return;
        }

        // Find the nested grid inside this section
        const nestedGridContainer = sectionContainer.querySelector(
          ".nested-grid-container"
        );
        if (!nestedGridContainer) {
          console.error("Could not find nested grid container");
          return;
        }

        const nestedGridElement =
          nestedGridContainer.querySelector(".grid-stack");
        if (nestedGridElement) {
          // Find and dispose all chart instances in this section
          const chartContainers =
            nestedGridElement.querySelectorAll(".chart-container");
          chartContainers.forEach((container) => {
            if (container.chart) {
              container.chart.dispose();
            }
          });

          // Find and destroy all Handsontable instances in this section
          const spreadsheetContainers = nestedGridElement.querySelectorAll(
            ".spreadsheet-container"
          );
          spreadsheetContainers.forEach((container) => {
            if (container.hotInstance) {
              container.hotInstance.destroy();
            }
          });

          // Destroy the nested grid instance if it exists
          if (nestedGridElement.gridstack) {
            nestedGridElement.gridstack.destroy(true);
          }
        }

        // Get the main grid instance and remove the section widget
        if (window.grid && window.grid.engine) {
          try {
            window.grid.removeWidget(gridItem);
          } catch (error) {
            console.error("Error removing widget:", error);
          }
        } else {
          console.error("Main grid not initialized");
          // Fallback: remove the grid item directly from DOM if grid instance is not available
          gridItem.remove();
        }
      }

      // Function to apply spreadsheet settings
      function applySpreadsheetSettings() {
        const title = document.getElementById("spreadsheetTitle").value;
        const showRowNumbers =
          document.getElementById("showRowNumbers").checked;
        const showColumnHeaders =
          document.getElementById("showColumnHeaders").checked;

        // Find the active spreadsheet container
        const activeContainer = document.querySelector(
          ".spreadsheet-container"
        );
        if (activeContainer && activeContainer.hotInstance) {
          const hot = activeContainer.hotInstance;

          // Update settings
          hot.updateSettings({
            rowHeaders: showRowNumbers,
            colHeaders: showColumnHeaders,
          });

          // Update title if provided
          if (title) {
            const headerDiv = activeContainer
              .closest(".grid-stack-item")
              .querySelector(".widget-header mb-2 div:first-child");
            if (headerDiv) {
              headerDiv.innerHTML = `<i class="las la-table"></i> ${title}`;
            }
          }
        }

        // Close the offcanvas
        const offcanvas = bootstrap.Offcanvas.getInstance(
          document.getElementById("spreadsheetSettings")
        );
        if (offcanvas) {
          offcanvas.hide();
        }
      }
    </script>

    <!-- Initialize the main grid -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        if (!window.grid) {
          window.grid = GridStack.init(
            {
              column: 12,
              cellHeight: 60, // Changed from 'auto' to fixed height
              margin: 5,
              float: true,
              animate: false,
              removable: true,
              removeTimeout: 0,
              disableOneColumnMode: true,
              sizeToContent: false, // Disable auto-resizing for main grid
            },
            ".grid-stack"
          );
        }
      });

      function removeSectionContainer(button) {
        if (!window.grid) {
          console.error("Grid not initialized");
          return;
        }

        // Find the section container widget
        const sectionContainer = button.closest(".section-container-widget");
        if (!sectionContainer) {
          console.error("Could not find section container");
          return;
        }

        // Find the grid-stack-item that contains this section
        const gridItem = sectionContainer.closest(".grid-stack-item");
        if (!gridItem) {
          console.error("Could not find grid item");
          return;
        }

        // Find the nested grid inside this section
        const nestedGridContainer = sectionContainer.querySelector(
          ".nested-grid-container"
        );
        if (!nestedGridContainer) {
          console.error("Could not find nested grid container");
          return;
        }

        const nestedGridElement =
          nestedGridContainer.querySelector(".grid-stack");
        if (nestedGridElement) {
          // Find and dispose all chart instances in this section
          const chartContainers =
            nestedGridElement.querySelectorAll(".chart-container");
          chartContainers.forEach((container) => {
            if (container.chart) {
              try {
                container.chart.dispose();
              } catch (e) {
                console.error("Error disposing chart:", e);
              }
            }
          });

          // Find and destroy all Handsontable instances in this section
          const spreadsheetContainers = nestedGridElement.querySelectorAll(
            ".spreadsheet-container"
          );
          spreadsheetContainers.forEach((container) => {
            if (container.hotInstance) {
              try {
                container.hotInstance.destroy();
              } catch (e) {
                console.error("Error destroying spreadsheet:", e);
              }
            }
          });

          // Destroy the nested grid instance if it exists
          if (nestedGridElement.gridstack) {
            try {
              nestedGridElement.gridstack.destroy(true);
            } catch (e) {
              console.error("Error destroying nested grid:", e);
            }
          }
        }

        try {
          // Remove the widget from the main grid
          window.grid.removeWidget(gridItem);
        } catch (error) {
          console.error("Error removing widget:", error);
          // Fallback: remove the grid item directly from DOM
          try {
            gridItem.remove();
          } catch (e) {
            console.error("Error removing grid item from DOM:", e);
          }
        }
      }
    </script>

    <!-- Workspace functionality -->
    <script src="js/workspace.js"></script>
    <!-- Pie Chart Compact Mode -->
    <script src="js/pie-chart-compact-mode.js"></script>
    <!-- Smart Widget Composer -->
    <!-- <script src="js/smartWidgetComposer.js"></script> -->

    <script src="./js/widget-sidebar.js"></script>
    <script src="./test-widget-sidebar.js"></script>

    <!-- Add this before the closing body tag -->

    <!-- Chart functions moved to external file -->
    <script src="dummy_chart.js"></script>

    <script>
      // Setup drag-in functionality after DOM is ready
      let textSidebarContent = [
        {
          w: 4,
          h: 4,
          content: `
          <div class="text-widget p-2">
            <div class="widget-header mb-2 fw-bold">
              <i class="las la-font"></i> Text Widget
            </div>
            <div class="text-container">
              <div class="text-content" contenteditable="true" style="
                min-height: 100px;
                padding: 10px;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                outline: none;
                font-family: Arial, sans-serif;
                font-size: 14px;
                line-height: 1.5;
                color: #333;
                background-color: #fff;
              ">
                <p>Click here to edit text...</p>
              </div>
            </div>
          </div>
        `,
        },
      ];

      let tableSidebarContent = [
        {
          w: 6,
          h: 6,
          content: `
          <div class="table-widget p-2">
            <div class="widget-header mb-2 fw-bold">
              <i class="las la-table"></i> Table Widget
            </div>
            <div class="table-container">
              <div class="table-responsive">
                <table class="table table-bordered table-striped">
                  <thead>
                    <tr>
                      <th>Column 1</th>
                      <th>Column 2</th>
                      <th>Column 3</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td contenteditable="true">Row 1, Col 1</td>
                      <td contenteditable="true">Row 1, Col 2</td>
                      <td contenteditable="true">Row 1, Col 3</td>
                    </tr>
                    <tr>
                      <td contenteditable="true">Row 2, Col 1</td>
                      <td contenteditable="true">Row 2, Col 2</td>
                      <td contenteditable="true">Row 2, Col 3</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        `,
        },
      ];

      let lineSeparatorSidebarContent = [
        {
          w: 2,
          h: 1,
          content: `
          <div class="line-separator-widget p-2">
            <div class="widget-header mb-2 fw-bold">
              <i class="las la-minus"></i> Line Separator
            </div>
            <div class="line-separator-container">
              <div class="line-separator horizontal solid" style="
                border-top: 2px solid #333;
                width: 100%;
                height: 0;
                margin: 20px 0;
              "></div>
            </div>
          </div>
        `,
        },
      ];

      let imageSidebarContent = [
        {
          w: 4,
          h: 4,
          content: `
          <div class="image-widget p-2">
            <div class="widget-header mb-2 mb-2">
              <i class="las la-image"></i> Image Widget
            </div>
            <div class="image-container d-flex align-items-center justify-content-center">
              <div class="text-center text-muted">
                <i class="las la-cloud-upload-alt la-3x mb-3"></i>
                <p>No image selected.<br>Use settings to upload an image.</p>
              </div>
            </div>
          </div>
        `,
        },
      ];

      let notesSectionSidebarContent = [
        {
          w: 6,
          h: 2,
          content: `
          <div class="notes-section-widget p-2">
            <div class="widget-header mb-2 fw-bold">
              <i class="las la-sticky-note"></i> Notes Section
            </div>
            <div class="notes-section-container">
              <footer class="widget-footer">
                <div class="mb-0">
                  <span class="size10 notes-keyInsight" style="display: none">
                    <i class="las la-clipboard size14"></i> Notes :
                    <span class="notes-content">The Smart Cube Knowledge Repository</span>
                    <br>
                  </span>

                  <span class="size10 source-keyInsight" style="">
                    <i class="las la-database size14"></i> Source :
                    <span class="source-content">The Smart Cube Research and Analysis</span>
                    <br>
                  </span>
                  
                  <span class="size10 last-update-keyInsight" style="">
                    <i class="las size14 la-calendar-minus"></i> Last update :
                    <span class="last-update-content">Apr-2025</span>
                  </span>
                  
                  <span class="size10 next-update-keyInsight" style="">
                    <i class="las size14 la-calendar-plus"></i> Next update :
                    <span class="next-update-content">May-2025</span>
                  </span>
                </div>
              </footer>
            </div>
          </div>
        `,
        },
      ];

      // Setup drag-in for text widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="text"]',
        undefined,
        textSidebarContent
      );

      // Setup drag-in for table widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="table"]',
        undefined,
        tableSidebarContent
      );

      // Setup drag-in for line separator widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="line-separator"]',
        undefined,
        lineSeparatorSidebarContent
      );

      // Setup drag-in for image widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="image"]',
        undefined,
        imageSidebarContent
      );

      // Setup drag-in for notes section widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="notes-section"]',
        undefined,
        notesSectionSidebarContent
      );
    </script>
  </body>
</html>
