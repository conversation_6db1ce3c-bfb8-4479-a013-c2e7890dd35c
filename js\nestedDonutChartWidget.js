let nestedDonutChartCounter = 0;

// Add a Nested Donut Chart widget using amCharts v5
function addNestedDonutChartWidget(initialConfig = {}) {
  const widgetId = `nested-donut-chart-${nestedDonutChartCounter++}`;
  const chartContainerId = `nested-donut-chart-container-${widgetId}`;
  const settingsId = `nestedDonutChartSettings-${widgetId}`;

  // Default data structure for nested donut
  const defaultData = {
    year2021: [
      { category: "Category A", value: 35 },
      { category: "Category B", value: 25 },
      { category: "Category C", value: 15 },
      { category: "Category D", value: 25 },
    ],
    year2022: [
      { category: "Category A", value: 40 },
      { category: "Category B", value: 20 },
      { category: "Category C", value: 20 },
      { category: "Category D", value: 20 },
    ],
    year2023: [
      { category: "Category A", value: 45 },
      { category: "Category B", value: 15 },
      { category: "Category C", value: 25 },
      { category: "Category D", value: 15 },
    ],
  };

  const config = {
    title: initialConfig.title || "Nested Donut Chart",
    data: initialConfig.data || defaultData,
    // Metadata fields with default placeholders
    notes:
      initialConfig.notes || "Default Notes: Please provide specific notes.",
    source:
      initialConfig.source ||
      "Default Source: Please provide a specific source.",
    lastUpdate: initialConfig.lastUpdate || "N/A",
    nextUpdate: initialConfig.nextUpdate || "N/A",
  };

  const grid = window.grid;
  const newWidget = grid.addWidget({
    w: initialConfig.w || 12,
    h: initialConfig.h || 12,
    content: `
      <div class="nested-donut-chart-widget widget p-2" id="${widgetId}" style="height: 100%; display: flex; flex-direction: column;">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div class="widget-title editable-title" data-editable="true" title="Click to edit title">
             
              <span>${config.title}</span>
          </div>
          <div class="widget-actions">
            <button class="btn btn-link" data-bs-toggle="offcanvas" data-bs-target="#${settingsId}" aria-controls="${settingsId}">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-link ms-1" onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex;">
          <div id="${chartContainerId}" class="chart-container" style="width: 100%; height: 100%; position: relative; min-height: 300px;"></div>
        </div>
        <div class="widget-footer mt-2" style="padding: 0.5rem 0; border-top: 1px solid #e5e9f0; font-size: 10px; color: #6c757d; text-align: left;">
          ${
            config.notes
              ? `<div><i class="las la-clipboard"></i> Notes : ${config.notes}</div>`
              : ""
          }
          ${
            config.source
              ? `<div><i class="las la-database"></i> Source : ${config.source}</div>`
              : ""
          }
          ${
            config.lastUpdate || config.nextUpdate
              ? `
          <div class="d-flex mt-1">
            ${
              config.lastUpdate
                ? `<span><i class="las la-calendar-alt"></i> Last update : ${config.lastUpdate}</span>`
                : "<span></span>"
            }
            ${
              config.nextUpdate
                ? `<span class="ms-3"><i class="las la-calendar-plus"></i> Next update : ${config.nextUpdate}</span>`
                : ""
            }
          </div>`
              : ""
          }
        </div>
      </div>
    `,
  });

  // Store config on the widget element
  const widgetElement = document.getElementById(widgetId);
  widgetElement.widgetConfig = config;
  widgetElement.chartContainerId = chartContainerId;

  // Initialize the chart after a short delay
  setTimeout(() => {
    if (typeof am5 !== "undefined" && typeof am5percent !== "undefined") {
      initializeNestedDonutChart(chartContainerId, config);
    } else {
      console.error("amCharts core or percent libraries not loaded properly");
    }
  }, 100);

  // Make title editable
  if (typeof makeTitleEditable === "function") {
    makeTitleEditable(widgetId);
  }

  // Create settings panel
  createNestedDonutChartSettingsOffcanvas(
    settingsId,
    widgetId,
    chartContainerId,
    config
  );
}

function initializeNestedDonutChart(chartContainerId, config) {
  const chartContainerElement = document.getElementById(chartContainerId);
  if (!chartContainerElement) return;

  // Dispose existing chart if any
  if (chartContainerElement.amRoot) {
    chartContainerElement.amRoot.dispose();
  }

  // Create root element
  const root = am5.Root.new(chartContainerId);
  chartContainerElement.amRoot = root;

  // Set themes
  root.setThemes([am5themes_Animated.new(root)]);

  // Create chart
  const chart = root.container.children.push(
    am5percent.PieChart.new(root, {
      layout: root.verticalLayout,
      innerRadius: am5.percent(50),
      paddingTop: 0,
      paddingBottom: 0,
      paddingLeft: 0,
      paddingRight: 0,
    })
  );

  // Get unique categories across all years
  const categories = new Set();
  Object.values(config.data).forEach((yearData) => {
    yearData.forEach((item) => categories.add(item.category));
  });

  // Create series for each year
  const yearData = Object.entries(config.data);
  const totalYears = yearData.length;

  // Create color set from brand colors
  let colors;
  if (window.chartConfig && window.chartConfig.brandColors) {
    colors = am5.ColorSet.new(root, {
      colors: window.chartConfig.brandColors.map((hex) => am5.color(hex)),
      reuse: true,
    });
  } else {
    colors = am5.ColorSet.new(root, {
      colors: [
        am5.color("#91cc75"), // Light green
        am5.color("#5470c6"), // Blue
        am5.color("#fac858"), // Yellow
        am5.color("#ee6666"), // Red
      ],
      reuse: true,
    });
  }

  // Create series
  yearData.forEach(([year, data], index) => {
    const series = chart.series.push(
      am5percent.PieSeries.new(root, {
        name: year,
        valueField: "value",
        categoryField: "category",
        radius: am5.percent(92 - index * 20), // Adjusted spacing
        innerRadius: am5.percent(77 - index * 20), // Adjusted spacing
      })
    );

    series.set("colors", colors);
    series.data.setAll(data);

    // Configure labels
    series.labels.template.setAll({
      fontSize: 11,
      maxWidth: 100,
      text: "{category}",
      oversizedBehavior: "truncate",
      inside: true,
      textType: "adjusted",
      radius: 10,
      centerX: am5.percent(50),
      centerY: am5.percent(50),
      paddingTop: 0,
      paddingBottom: 0,
      paddingLeft: 0,
      paddingRight: 0,
    });

    // Disable ticks
    series.ticks.template.set("forceHidden", true);

    // Configure slices
    series.slices.template.setAll({
      cornerRadius: 0,
      tooltipText: "{category}: {value}",
    });

    // Make labels follow the slices
    series.labels.template.adapters.add(
      "rotation",
      function (rotation, target) {
        return (
          target.dataItem.get("slice").get("startAngle") +
          target.dataItem.get("slice").get("arc") / 2
        );
      }
    );
  });

  // Create legend for categories
  const legend = chart.children.push(
    am5.Legend.new(root, {
      centerX: am5.percent(50),
      x: am5.percent(50),
      marginTop: 15,
      marginBottom: 15,
      layout: root.horizontalLayout,
      maxWidth: am5.percent(90),
      maxHeight: 100,
      useDefaultMarker: true,
    })
  );

  // Configure legend labels
  legend.labels.template.setAll({
    fontSize: 11,
    maxWidth: 150,
    oversizedBehavior: "truncate",
    textDecoration: "none",
  });

  legend.valueLabels.template.setAll({
    fontSize: 11,
  });

  // Create legend data for categories
  const legendData = Array.from(categories).map((category) => {
    return {
      name: category,
      // Get the color index for this category
      color: colors.next(),
    };
  });

  // Set legend data
  legend.data.setAll(legendData);

  // Add resize observer
  const resizeObserver = new ResizeObserver(() => {
    // Get the container dimensions
    const width = chartContainerElement.clientWidth;
    const height = chartContainerElement.clientHeight;

    // Update root dimensions which will resize the chart
    root.resize();
  });

  resizeObserver.observe(chartContainerElement);
  chartContainerElement.resizeObserver = resizeObserver;

  // Initial sizing
  root.resize();

  // Play initial series animation
  chart.appear(1000, 100);
}

function createNestedDonutChartSettingsOffcanvas(
  settingsId,
  widgetId,
  chartContainerId,
  currentConfig
) {
  const offcanvasContainer =
    document.getElementById("offcanvasContainer") || document.body;

  // Convert data object to pretty-printed JSON string
  const dataJson = JSON.stringify(currentConfig.data, null, 2);

  const offcanvasHtml = `
    <div class="offcanvas offcanvas-end" tabindex="-1" id="${settingsId}" aria-labelledby="${settingsId}Label">
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="${settingsId}Label">Nested Donut Chart Settings</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div class="offcanvas-body">
        <div class="mb-3">
          <label class="form-label">Chart Title</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-chartTitle" value="${currentConfig.title}">
        </div>

        <div class="mb-3">
          <label class="form-label">Data (JSON format)</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-data" rows="10" spellcheck="false">${dataJson}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Notes</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-notes" rows="2">${currentConfig.notes}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Source</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-source" rows="2">${currentConfig.source}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Last Update</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-lastUpdate" value="${currentConfig.lastUpdate}">
        </div>

        <div class="mb-3">
          <label class="form-label">Next Update</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-nextUpdate" value="${currentConfig.nextUpdate}">
        </div>

        <button class="btn btn-primary w-100" onclick="applyNestedDonutChartSettings('${widgetId}', '${settingsId}', '${chartContainerId}')">Apply Changes</button>
      </div>
    </div>
  `;

  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = offcanvasHtml;
  offcanvasContainer.appendChild(tempDiv.firstElementChild);
}

function applyNestedDonutChartSettings(widgetId, settingsId, chartContainerId) {
  const widgetElement = document.getElementById(widgetId);
  if (!widgetElement || !widgetElement.widgetConfig) return;

  const currentConfig = widgetElement.widgetConfig;

  // Get values from form
  currentConfig.title = document.getElementById(
    `${settingsId}-chartTitle`
  ).value;

  try {
    const dataInput = document.getElementById(`${settingsId}-data`).value;
    currentConfig.data = JSON.parse(dataInput);
  } catch (e) {
    console.error("Invalid JSON in Data:", e);
    alert("Error: Invalid JSON format in Data. Please correct and try again.");
    return;
  }

  currentConfig.notes = document.getElementById(`${settingsId}-notes`).value;
  currentConfig.source = document.getElementById(`${settingsId}-source`).value;
  currentConfig.lastUpdate = document.getElementById(
    `${settingsId}-lastUpdate`
  ).value;
  currentConfig.nextUpdate = document.getElementById(
    `${settingsId}-nextUpdate`
  ).value;

  // Update widget title
  const titleSpan = widgetElement.querySelector(".widget-title span");
  if (titleSpan) {
    titleSpan.textContent = currentConfig.title;
  }

  // Update footer
  if (typeof updateWidgetFooter === "function") {
    updateWidgetFooter(widgetElement, currentConfig);
  }

  // Re-initialize chart
  initializeNestedDonutChart(chartContainerId, currentConfig);

  // Close settings panel
  const offcanvasElement = document.getElementById(settingsId);
  if (offcanvasElement) {
    const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvasElement);
    if (offcanvasInstance) {
      offcanvasInstance.hide();
    }
  }
}

// Export functions
window.addNestedDonutChartWidget = addNestedDonutChartWidget;
window.applyNestedDonutChartSettings = applyNestedDonutChartSettings;
