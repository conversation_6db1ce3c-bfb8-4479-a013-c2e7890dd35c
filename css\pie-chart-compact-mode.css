/**
 * Pie Chart Compact Mode
 * Provides a clean, minimalist view for pie charts by hiding the header
 * while keeping settings and close buttons accessible
 */

/* Toggle button for compact mode */
.compact-mode-toggle {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 115, 101, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
  opacity: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.widget:hover .compact-mode-toggle {
  opacity: 1;
}

.compact-mode-toggle:hover {
  background: rgba(0, 115, 101, 0.1);
  transform: scale(1.1);
}

.compact-mode-toggle i {
  font-size: 14px;
  color: #007365;
}

/* Compact mode styles */
.pie-chart-compact-mode .widget-header {
  height: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.pie-chart-compact-mode .widget-body {
  padding-top: 0;
}

/* Floating action buttons for settings and close */
.pie-chart-compact-mode .floating-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 8px;
  z-index: 100;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.pie-chart-compact-mode:hover .floating-actions {
  opacity: 1;
}

.floating-action-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: white;
  border: 1px solid rgba(0, 115, 101, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.floating-action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.floating-action-button.settings {
  background: white;
}

.floating-action-button.settings:hover {
  background: rgba(0, 115, 101, 0.1);
}

.floating-action-button.close {
  background: white;
}

.floating-action-button.close:hover {
  background: rgba(220, 53, 69, 0.1);
}

.floating-action-button i {
  font-size: 16px;
}

.floating-action-button.settings i {
  color: #007365;
}

.floating-action-button.close i {
  color: #dc3545;
}

/* Floating title that appears when hovering in compact mode */
.floating-title {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(-5px);
  transition: all 0.2s ease;
  pointer-events: none;
  z-index: 10;
  max-width: 80%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pie-chart-compact-mode:hover .floating-title {
  opacity: 1;
  transform: translateY(0);
}

.floating-title i {
  margin-right: 4px;
  color: #007365;
}

/* Dark theme support */
.dark-theme .compact-mode-toggle,
.dark-theme .floating-action-button {
  background: rgba(45, 55, 72, 0.9);
  border-color: rgba(77, 182, 172, 0.3);
}

.dark-theme .compact-mode-toggle:hover {
  background: rgba(77, 182, 172, 0.2);
}

.dark-theme .floating-action-button.settings:hover {
  background: rgba(77, 182, 172, 0.2);
}

.dark-theme .floating-action-button.close:hover {
  background: rgba(220, 53, 69, 0.2);
}

.dark-theme .floating-title {
  background: rgba(45, 55, 72, 0.9);
  color: #e2e8f0;
}

.dark-theme .compact-mode-toggle i,
.dark-theme .floating-action-button.settings i {
  color: #4db6ac;
}
