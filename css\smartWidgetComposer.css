/* Smart Widget Composer Styles - Compact Version */

/* Ocean Teal Theme Variables */
:root {
  --ocean-teal: #20b2aa;
  --forest-green: #228b22;
  --ocean-teal-light: rgba(32, 178, 170, 0.1);
  --ocean-teal-dark: #1a9999;
  --ocean-teal-hover: #17a2a2;

  /* Bootstrap Primary Color Override - Ocean Teal */
  --bs-primary: #20b2aa;
  --bs-primary-rgb: 32, 178, 170;
}

/* Increase offcanvas width for Smart Widget Composer */
#smartWidgetComposerOffcanvas {
  --bs-offcanvas-width: 600px;
  --bs-offcanvas-bg: #ffffff;
  --bs-offcanvas-border-color: var(--ocean-teal-light);
}

@media (min-width: 992px) {
  #smartWidgetComposerOffcanvas {
    --bs-offcanvas-width: 700px;
  }
}

@media (min-width: 1200px) {
  #smartWidgetComposerOffcanvas {
    --bs-offcanvas-width: 800px;
  }
}

/* Tab Configuration Offcanvas - Much Wider for Side-by-Side Layout */
#tabConfigOffcanvas {
  --bs-offcanvas-width: 900px;
}

@media (min-width: 1200px) {
  #tabConfigOffcanvas {
    --bs-offcanvas-width: 1100px;
  }
}

@media (min-width: 1400px) {
  #tabConfigOffcanvas {
    --bs-offcanvas-width: 1300px;
  }
}

/* Tab Configuration Offcanvas Header */
#tabConfigOffcanvas .offcanvas-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--bs-border-color, #dee2e6);
  background: var(--bs-light, #f8f9fa);
}

#tabConfigOffcanvas .offcanvas-header-content {
  flex: 1;
}

#tabConfigOffcanvas .offcanvas-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--bs-dark, #212529);
  margin-bottom: 4px;
}

#tabConfigOffcanvas .offcanvas-subtitle {
  font-size: 0.875rem;
  color: var(--bs-secondary, #6c757d);
  margin: 0;
}

#tabConfigOffcanvas .offcanvas-close {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  color: var(--bs-secondary, #6c757d);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.1rem;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

#tabConfigOffcanvas .offcanvas-close:hover {
  background: var(--bs-danger, #dc3545);
  color: white;
  transform: scale(1.05);
}

/* Tab Configuration Layout - Side by Side */
.tab-config-layout {
  display: flex;
  height: 100%;
  min-height: calc(100vh - 120px);
}

.tab-config-panel {
  flex: 1;
  border-right: 1px solid var(--bs-border-color, #dee2e6);
  display: flex;
  flex-direction: column;
  background: white;
}

.tab-preview-panel {
  flex: 0.7;
  display: flex;
  flex-direction: column;
  background: var(--bs-light, #f8f9fa);
}

.config-panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.config-panel-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--bs-border-color, #dee2e6);
  background: var(--bs-light, #f8f9fa);
}

.preview-panel-header {
  padding: 20px;
  border-bottom: 1px solid var(--bs-border-color, #dee2e6);
  background: white;
}

.preview-panel-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.preview-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--bs-dark, #212529);
  margin-bottom: 4px;
}

.preview-subtitle {
  font-size: 0.875rem;
  color: var(--bs-secondary, #6c757d);
  margin: 0;
}

/* Configuration Sections */
.config-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--bs-dark, #212529);
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.section-title i {
  color: var(--ocean-teal);
  margin-right: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

/* Tab Configuration Items */
.tab-config-item {
  background: var(--bs-light, #f8f9fa);
  border: 1px solid var(--bs-border-color, #dee2e6);
  border-radius: 8px;
  margin-bottom: 16px;
  transition: all 0.2s ease;
}

.tab-config-item:hover {
  border-color: var(--ocean-teal);
  box-shadow: 0 2px 8px rgba(32, 178, 170, 0.1);
}

.tab-config-list {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 4px;
}

/* Custom Scrollbar for Config List */
.tab-config-list::-webkit-scrollbar {
  width: 6px;
}

.tab-config-list::-webkit-scrollbar-track {
  background: var(--bs-light, #f8f9fa);
  border-radius: 3px;
}

.tab-config-list::-webkit-scrollbar-thumb {
  background: var(--bs-secondary, #6c757d);
  border-radius: 3px;
}

.tab-config-list::-webkit-scrollbar-thumb:hover {
  background: var(--ocean-teal);
}

/* Tab Position Configuration */
.tab-position-config {
  background: white;
  border: 1px solid var(--bs-border-color, #dee2e6);
  border-radius: 8px;
}

/* Preview Container */
.tab-preview-container {
  background: white;
  border: 1px solid var(--bs-border-color, #dee2e6);
  border-radius: 8px;
  padding: 20px;
  min-height: 300px;
}

/* Form Elements within Tab Config */
#tabConfigOffcanvas .form-control,
#tabConfigOffcanvas .form-select {
  border-radius: 6px;
  border: 1px solid var(--bs-border-color, #dee2e6);
  padding: 8px 12px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

#tabConfigOffcanvas .form-control:focus,
#tabConfigOffcanvas .form-select:focus {
  border-color: var(--ocean-teal);
  box-shadow: 0 0 0 0.2rem rgba(32, 178, 170, 0.25);
}

#tabConfigOffcanvas .form-label {
  font-weight: 500;
  color: var(--bs-dark, #212529);
  margin-bottom: 6px;
  font-size: 0.875rem;
}

/* Buttons within Tab Config */
#tabConfigOffcanvas .btn {
  border-radius: 6px;
  font-weight: 500;
  padding: 8px 16px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

#tabConfigOffcanvas .btn-sm {
  padding: 6px 12px;
  font-size: 0.8125rem;
}

#tabConfigOffcanvas .btn-primary {
  background: var(--bs-primary, #20b2aa);
  border-color: var(--bs-primary, #20b2aa);
}

#tabConfigOffcanvas .btn-primary:hover {
  background: #1a9b95;
  border-color: #1a9b95;
}

#tabConfigOffcanvas .btn-outline-secondary {
  color: var(--bs-secondary, #6c757d);
  border-color: var(--bs-secondary, #6c757d);
}

#tabConfigOffcanvas .btn-outline-secondary:hover {
  background: var(--bs-secondary, #6c757d);
  border-color: var(--bs-secondary, #6c757d);
  color: white;
}

#tabConfigOffcanvas .btn-outline-danger {
  color: var(--bs-danger, #dc3545);
  border-color: var(--bs-danger, #dc3545);
}

#tabConfigOffcanvas .btn-outline-danger:hover {
  background: var(--bs-danger, #dc3545);
  border-color: var(--bs-danger, #dc3545);
  color: white;
}

/* Button Groups */
#tabConfigOffcanvas .btn-group .btn {
  border-radius: 4px;
  margin-left: 2px;
}

#tabConfigOffcanvas .btn-group .btn:first-child {
  margin-left: 0;
}

/* Badge Styling */
#tabConfigOffcanvas .badge {
  font-size: 0.75rem;
  padding: 6px 10px;
  border-radius: 4px;
}

/* Empty State Styling */
#tabConfigOffcanvas .text-center.text-muted {
  padding: 40px 20px;
  background: var(--bs-light, #f8f9fa);
  border-radius: 8px;
  border: 2px dashed var(--bs-border-color, #dee2e6);
}

#tabConfigOffcanvas .text-center.text-muted i {
  font-size: 2.5rem;
  color: var(--bs-primary, #20b2aa);
  opacity: 0.6;
  margin-bottom: 12px;
}

/* Dark Theme Support for Tab Configuration */
[data-bs-theme="dark"] #tabConfigOffcanvas {
  --bs-border-color: #495057;
  --bs-light: #343a40;
  --bs-dark: #f8f9fa;
  --bs-secondary: #adb5bd;
}

[data-bs-theme="dark"] #tabConfigOffcanvas .offcanvas-header {
  background: #343a40;
  border-bottom-color: #495057;
}

[data-bs-theme="dark"] #tabConfigOffcanvas .tab-config-panel {
  background: #212529;
}

[data-bs-theme="dark"] #tabConfigOffcanvas .tab-preview-panel {
  background: #343a40;
}

[data-bs-theme="dark"] #tabConfigOffcanvas .config-panel-footer {
  background: #343a40;
  border-top-color: #495057;
}

[data-bs-theme="dark"] #tabConfigOffcanvas .preview-panel-header {
  background: #212529;
  border-bottom-color: #495057;
}

[data-bs-theme="dark"] #tabConfigOffcanvas .tab-config-item {
  background: #343a40;
  border-color: #495057;
}

[data-bs-theme="dark"] #tabConfigOffcanvas .tab-config-item:hover {
  border-color: #02104f;
  box-shadow: 0 2px 8px rgba(13, 110, 253, 0.2);
}

[data-bs-theme="dark"] #tabConfigOffcanvas .tab-position-config {
  background: #212529;
  border-color: #495057;
}

[data-bs-theme="dark"] #tabConfigOffcanvas .tab-preview-container {
  background: #212529;
  border-color: #495057;
}

[data-bs-theme="dark"] #tabConfigOffcanvas .form-control,
[data-bs-theme="dark"] #tabConfigOffcanvas .form-select {
  background: #212529;
  border-color: #495057;
  color: #f8f9fa;
}

[data-bs-theme="dark"] #tabConfigOffcanvas .form-control:focus,
[data-bs-theme="dark"] #tabConfigOffcanvas .form-select:focus {
  background: #212529;
  border-color: #02104f;
  color: #f8f9fa;
}

[data-bs-theme="dark"] #tabConfigOffcanvas .text-center.text-muted {
  background: #343a40;
  border-color: #495057;
  color: #adb5bd;
}

/* Responsive Design for Tab Config */
@media (max-width: 1200px) {
  #tabConfigOffcanvas {
    --bs-offcanvas-width: 800px;
  }

  .tab-config-layout {
    flex-direction: column;
  }

  .tab-config-panel {
    border-right: none;
    border-bottom: 1px solid var(--bs-border-color, #dee2e6);
  }

  .config-panel-content {
    max-height: 50vh;
  }

  .preview-panel-content {
    max-height: 40vh;
  }
}

@media (max-width: 768px) {
  #tabConfigOffcanvas {
    --bs-offcanvas-width: 100vw;
  }

  .config-panel-content,
  .preview-panel-content {
    padding: 16px;
  }

  #tabConfigOffcanvas .offcanvas-header {
    padding: 16px;
  }

  .tab-config-layout {
    flex-direction: column;
  }

  .config-panel-content {
    max-height: 60vh;
  }

  .preview-panel-content {
    max-height: 40vh;
  }
}

.composer-container {
  padding: 0;
}

.composer-section {
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--bs-border-color);
}

.composer-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.composer-section-title {
  color: var(--ocean-teal);
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
}

.composer-section-title i {
  color: var(--ocean-teal);
  font-size: 1.1em;
}

/* Step Navigation - Compact */
.composer-steps {
  margin-bottom: 1rem;
}

.step-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.step-indicator::before {
  content: "";
  position: absolute;
  top: 35%;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--bs-border-color);
  z-index: 1;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  position: relative;
  z-index: 2;
  background: var(--bs-body-bg);
  padding: 0 0.75rem;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: var(--ocean-teal);
}

.step-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--bs-secondary);
  transition: color 0.3s ease;
}

.step.active .step-label {
  color: var(--ocean-teal);
}

/* Template Grid - Compact */
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.75rem;
}

.template-card {
  border: 2px solid var(--bs-border-color);
  border-radius: 6px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--bs-body-bg);
}

.template-card:hover {
  border-color: var(--ocean-teal);
  box-shadow: 0 2px 8px rgba(32, 178, 170, 0.15);
  transform: translateY(-1px);
}

.template-card.active {
  border-color: var(--ocean-teal);
  background: rgba(32, 178, 170, 0.1);
}

.template-preview {
  margin-bottom: 0.5rem;
  height: 60px;
  border-radius: 4px;
  background: var(--bs-gray-100);
  padding: 6px;
}

.preview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 3px;
  height: 100%;
}

.preview-grid.analytics {
  grid-template-columns: 2fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.preview-grid.analytics .preview-item:first-child {
  grid-row: span 2;
}

.preview-grid.custom-preview {
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
}

.preview-item {
  border-radius: 2px;
  background: var(--ocean-teal);
  opacity: 0.7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
}

.preview-item.chart {
  background: var(--ocean-teal);
}

.preview-item.kpi {
  background: var(--forest-green);
}

.preview-item.table {
  background: #6c757d;
}

.preview-item.text {
  background: var(--ocean-teal-light);
}

.preview-item.wide {
  grid-column: span 2;
}

.preview-item.empty {
  background: transparent;
  border: 2px dashed var(--bs-border-color);
  color: var(--bs-secondary);
}

.template-info h6 {
  margin-bottom: 0.15rem;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Layout Configuration - Compact */
.layout-preview-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Custom Layout Builder - Compact */
.layout-builder-toolbar {
  padding: 0.75rem;
  background: var(--bs-gray-50);
  border-radius: 6px;
  margin-bottom: 0.75rem;
}

/* Custom Layout Grid - Full Width Layout */
.custom-layout-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  min-height: 200px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

/* Widget Selector Card - Always Full Width at Top */
.widget-selector-card {
  order: -1; /* Always appears first */
  width: 100%;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  animation: slideIn 0.3s ease-out;
}

.widget-selector-card.enhanced {
  border: 2px solid var(--ocean-teal);
  box-shadow: 0 4px 12px rgba(32, 178, 170, 0.15);
}

/* Improve widget type grid for full width */
.widget-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.widget-type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  text-align: center;
}

.widget-type-option:hover {
  border-color: var(--ocean-teal);
  background: rgba(32, 178, 170, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(32, 178, 170, 0.15);
}

.widget-type-icon {
  font-size: 2rem;
  color: var(--ocean-teal);
  margin-bottom: 0.5rem;
}

.widget-type-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #495057;
}

.widget-type-option:hover .widget-type-label {
  color: var(--ocean-teal);
}

/* Layout Slots - Full Width */
.layout-slot {
  width: 100%;
  min-height: 120px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.layout-slot:hover {
  border-color: var(--ocean-teal);
  box-shadow: 0 4px 12px rgba(32, 178, 170, 0.15);
  transform: translateY(-2px);
}

/* Remove size-based width constraints */
.layout-slot.small,
.layout-slot.medium,
.layout-slot.large {
  width: 100%;
}

/* Adjust slot preview for full width layout */
.slot-preview {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
  margin-top: 0.75rem;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.layout-slot-placeholder {
  border: 2px dashed var(--bs-border-color);
  border-radius: 6px;
  padding: 1rem;
  text-align: center;
  color: var(--bs-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.layout-slot-placeholder i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--bs-border-color);
}

.selector-header h6 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.widget-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.widget-type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem;
  border: 1px solid var(--bs-border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  background: var(--bs-body-bg);
}

.widget-type-option:hover {
  border-color: var(--ocean-teal);
  background: rgba(32, 178, 170, 0.1);
  transform: translateY(-1px);
}

.widget-type-icon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
  color: var(--bs-primary);
}

.widget-type-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--ocean-teal);
}

.layout-slot {
  border: 2px solid var(--bs-border-color);
  border-radius: 6px;
  padding: 0.75rem;
  background: var(--bs-body-bg);
  transition: all 0.3s ease;
}

.layout-slot:hover {
  border-color: var(--ocean-teal);
  box-shadow: 0 2px 8px rgba(32, 178, 170, 0.1);
}

.layout-slot.small {
  min-height: 120px;
}

.layout-slot.large {
  min-height: 200px;
}

.slot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: var(--bs-gray-50);
  border-radius: 4px;
  border: 1px solid var(--bs-border-color);
  min-height: 40px;
}

.slot-title {
  font-weight: 600;
  font-size: 0.85rem;
  color: var(--ocean-teal);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  min-width: 0; /* Allow text truncation */
}

.slot-title i {
  font-size: 1.1rem;
  color: var(--ocean-teal);
  flex-shrink: 0;
}

.slot-title span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.slot-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.size-selector {
  display: flex;
  align-items: center;
}

.size-selector select {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  height: 32px;
  min-width: 80px;
  border: 1px solid var(--bs-border-color);
  border-radius: 4px;
  background: var(--bs-body-bg);
}

.size-selector select:focus {
  border-color: var(--ocean-teal);
  box-shadow: 0 0 0 0.2rem rgba(32, 178, 170, 0.25);
  outline: none;
}

.slot-remove-btn {
  background: none;
  border: none;
  color: var(--bs-danger);
  font-size: 1rem;
  padding: 0.25rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slot-remove-btn:hover {
  background: rgba(220, 53, 69, 0.1);
  color: var(--bs-danger);
  transform: scale(1.1);
}

.slot-title-input {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  height: 32px;
  border: 1px solid var(--bs-border-color);
  border-radius: 4px;
  background: var(--bs-body-bg);
  flex: 1;
  min-width: 0;
}

.slot-title-input:focus {
  border-color: var(--ocean-teal);
  box-shadow: 0 0 0 0.2rem rgba(32, 178, 170, 0.25);
  outline: none;
}

/* Dark theme adjustments for slot header */
[data-bs-theme="dark"] .slot-header {
  background: var(--bs-gray-800);
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"] .size-selector select,
[data-bs-theme="dark"] .slot-title-input {
  background: var(--bs-gray-800);
  border-color: var(--bs-gray-600);
  color: var(--bs-gray-100);
}

[data-bs-theme="dark"] .size-selector select:focus,
[data-bs-theme="dark"] .slot-title-input:focus {
  border-color: var(--ocean-teal);
  background: var(--bs-gray-700);
}

[data-bs-theme="dark"] .slot-remove-btn:hover {
  background: rgba(220, 53, 69, 0.2);
}

.slot-preview {
  height: 60px;
  background: var(--bs-gray-100);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--bs-secondary);
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.layout-slot:hover .slot-preview {
  background: rgba(32, 178, 170, 0.1);
  color: var(--ocean-teal);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Dark theme adjustments */
[data-bs-theme="dark"] .widget-selector-card {
  background: var(--bs-dark);
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"] .widget-type-option {
  background: var(--bs-dark);
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"] .widget-type-option:hover {
  background: rgba(32, 178, 170, 0.1);
}

[data-bs-theme="dark"] .layout-slot {
  background: var(--bs-dark);
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"] .layout-slot:hover {
  border-color: var(--ocean-teal);
}

[data-bs-theme="dark"] .slot-preview {
  background: var(--bs-gray-800);
}

[data-bs-theme="dark"] .layout-slot:hover .slot-preview {
  background: rgba(32, 178, 170, 0.2);
}

/* Mobile responsiveness - Compact */
@media (max-width: 768px) {
  .widget-type-grid {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
  }

  .slot-header {
    flex-direction: column;
    gap: 0.25rem;
  }

  .slot-actions {
    width: 100%;
    justify-content: space-between;
  }

  .slot-title-input {
    width: 100%;
  }
}

/* Preview styles - Compact */
.kpi-preview {
  text-align: center;
  padding: 0.5rem;
}

.kpi-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: var(--ocean-teal);
}

.kpi-label {
  font-size: 0.7rem;
  color: var(--bs-secondary);
}

.chart-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 2rem;
  color: var(--ocean-teal);
}

.pie-preview {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: conic-gradient(
    var(--ocean-teal) 0deg 120deg,
    var(--bs-gray-300) 120deg 200deg,
    var(--bs-gray-200) 200deg 360deg
  );
  margin: 0 auto;
}

.bar-preview {
  display: flex;
  align-items: end;
  justify-content: center;
  gap: 2px;
  height: 100%;
  position: relative;
}

.bar-preview::before,
.bar-preview::after {
  content: "";
  width: 8px;
  background: var(--ocean-teal);
  border-radius: 1px;
}

.bar-preview::before {
  height: 60%;
}

.bar-preview::after {
  height: 80%;
}

.line-preview {
  position: relative;
  height: 100%;
  background: linear-gradient(
    135deg,
    transparent 40%,
    var(--ocean-teal) 40%,
    var(--ocean-teal) 42%,
    transparent 42%,
    transparent 58%,
    var(--ocean-teal) 58%,
    var(--ocean-teal) 60%,
    transparent 60%
  );
  background-size: 12px 12px;
}

.line-preview::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background: var(--ocean-teal);
  border-radius: 50%;
  box-shadow: -15px -8px 0 -2px var(--ocean-teal),
    -8px 5px 0 -2px var(--ocean-teal), 8px -3px 0 -2px var(--ocean-teal),
    15px 8px 0 -2px var(--ocean-teal);
}

.bubble-preview {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bubble-preview::before,
.bubble-preview::after {
  content: "";
  position: absolute;
  border-radius: 50%;
  background: var(--ocean-teal);
  opacity: 0.6;
}

.bubble-preview::before {
  width: 20px;
  height: 20px;
  top: 20%;
  left: 30%;
}

.bubble-preview::after {
  width: 15px;
  height: 15px;
  bottom: 30%;
  right: 25%;
}

.world-map-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: 4px;
}

.world-map-preview::before {
  content: "";
  position: absolute;
  width: 35px;
  height: 20px;
  background: var(--ocean-teal);
  border-radius: 8px 4px 6px 2px;
  opacity: 0.8;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.world-map-preview::after {
  content: "";
  position: absolute;
  width: 12px;
  height: 8px;
  background: var(--forest-green);
  border-radius: 2px;
  opacity: 0.6;
  top: 60%;
  left: 35%;
}

.word-cloud-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  background: linear-gradient(45deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 4px;
  overflow: hidden;
}

.word-cloud-preview::before {
  content: "DATA";
  position: absolute;
  font-size: 0.6rem;
  font-weight: bold;
  color: var(--ocean-teal);
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.word-cloud-preview::after {
  content: "ANALYTICS INSIGHTS";
  position: absolute;
  font-size: 0.4rem;
  font-weight: 600;
  color: var(--forest-green);
  bottom: 35%;
  left: 50%;
  transform: translate(-50%, 50%);
  white-space: nowrap;
}

.table-preview {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 0.25rem;
}

.table-row {
  height: 8px;
  background: var(--bs-gray-300);
  border-radius: 1px;
}

.table-row:first-child {
  background: var(--ocean-teal);
}

.text-preview {
  padding: 0.25rem;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.text-line {
  height: 6px;
  background: var(--bs-gray-400);
  border-radius: 1px;
}

.text-line.short {
  width: 70%;
}

.media-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bs-gray-200);
  color: var(--bs-gray-600);
  font-size: 1.5rem;
}

.section-preview {
  border: 2px dashed var(--bs-border-color);
  border-radius: 4px;
  padding: 0.5rem;
  text-align: center;
  color: var(--bs-secondary);
}

.final-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.preview-widget {
  border: 1px solid var(--bs-border-color);
  border-radius: 6px;
  padding: 0.75rem;
  background: var(--bs-body-bg);
}

.preview-widget.small {
  min-height: 120px;
}

.preview-widget.large {
  min-height: 200px;
}

.widget-header {
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.widget-body {
  height: 80px;
  background: var(--bs-gray-100);
  border-radius: 4px;
}

.composer-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  margin-top: 1rem;
  border-top: 1px solid var(--bs-border-color);
}

.data-editor-section {
  margin-top: 1rem;
}

.section-title {
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--ocean-teal);
}

/* Dark theme template cards */
[data-bs-theme="dark"] .template-card {
  background: var(--bs-dark);
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"] .template-card:hover {
  box-shadow: 0 2px 8px rgba(32, 178, 170, 0.15);
}

[data-bs-theme="dark"] .template-card.active {
  background: rgba(32, 178, 170, 0.1);
}

[data-bs-theme="dark"] .template-preview {
  background: var(--bs-gray-800);
}

[data-bs-theme="dark"] .layout-slot {
  background: var(--bs-dark);
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"] .slot-preview {
  background: var(--bs-gray-800);
}

[data-bs-theme="dark"] .layout-builder-toolbar {
  background: var(--bs-gray-800);
}

[data-bs-theme="dark"] .custom-layout-grid {
  background: var(--bs-dark);
}

[data-bs-theme="dark"] .section-preview {
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"] .preview-widget {
  background: var(--bs-dark);
  border-color: var(--bs-gray-700);
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .template-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .step-indicator {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .step-indicator::before {
    display: none;
  }

  .composer-navigation {
    flex-direction: column;
    gap: 0.5rem;
  }
}

.composer-step {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tab Widget Styles - Compact */
.tab-widget-wrapper {
  position: relative;
  background: var(--bs-body-bg);
  border: 1px solid var(--bs-border-color);
  border-radius: 6px;
  overflow: hidden;
}

.tab-widget-wrapper .widget-content {
  padding: 0;
  background: transparent;
  border: none;
  border-radius: 0;
  box-shadow: none;
  margin: 0;
}

.tab-widget-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bs-body-bg);
  border-radius: 6px;
  overflow: hidden;
}

.tab-widget-container .nav-tabs {
  border-bottom: 1px solid var(--bs-border-color);
  background: var(--bs-gray-50);
  margin: 0;
  padding: 0 0.75rem;
  min-height: 40px;
}

.tab-widget-container .nav-tabs .nav-item {
  margin-bottom: 0;
}

.tab-widget-container .nav-tabs .nav-link {
  border: none;
  border-radius: 0;
  padding: 0.5rem 0.75rem;
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--bs-secondary);
  background: transparent;
  transition: all 0.3s ease;
  position: relative;
}

.tab-widget-container .nav-tabs .nav-link:hover {
  border: none;
  background: rgba(var(--bs-primary-rgb), 0.1);
  color: var(--ocean-teal);
}

.tab-widget-container .nav-tabs .nav-link.active {
  background: var(--bs-body-bg);
  color: var(--ocean-teal);
  border: none;
  border-bottom: 2px solid var(--ocean-teal);
}

.tab-widget-container .tab-content {
  flex: 1;
  padding: 0.75rem;
  background: var(--bs-body-bg);
  overflow-y: auto;
  min-height: 150px;
}

/* Bottom tabs positioning - Market Insights style */
.tab-widget-container.bottom-tabs {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
}

.tab-widget-container.bottom-tabs .tab-content {
  order: 1;
  flex: 1;
  padding-bottom: 60px; /* Space for absolutely positioned tabs */
  overflow: auto;
}

.tab-widget-container.bottom-tabs .nav-tabs {
  order: 2;
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  border-bottom: none !important;
  border-top: 1px solid #dee2e6;
  background: #f8f9fa;
  margin: 0 !important;
  border-radius: 0 !important;
  padding: 0;
  display: flex;
  justify-content: center;
  z-index: 10 !important;
}

.tab-widget-container.bottom-tabs .nav-tabs .nav-link {
  border: none !important;
  background: transparent !important;
  color: #6c757d !important;
  padding: 8px 16px !important;
  margin: 0 !important;
  border-radius: 0 !important;
  transition: all 0.2s ease !important;
  min-width: auto !important;
  text-align: center !important;
}

.tab-widget-container.bottom-tabs .nav-tabs .nav-link:hover {
  background: rgba(0, 177, 156, 0.1) !important;
  color: #00b19c !important;
  border: none !important;
}

.tab-widget-container.bottom-tabs .nav-tabs .nav-link.active {
  background: #00b19c !important;
  color: white !important;
  border: none !important;
}

/* Image styling for Market Insights tabs */
.tab-widget-container.bottom-tabs .nav-tabs .nav-link img {
  height: 24px !important;
  width: auto !important;
  margin-bottom: 5px !important;
  transition: all 0.2s ease !important;
}

.tab-widget-container.bottom-tabs .nav-tabs .nav-link:hover img {
  filter: brightness(1.2) !important;
}

.tab-widget-container.bottom-tabs .nav-tabs .nav-link.active img {
  filter: brightness(0) invert(1) !important;
}

/* Text styling for Market Insights tabs */
.tab-widget-container.bottom-tabs .nav-tabs .nav-link span {
  font-size: 11px !important;
  line-height: 1.2 !important;
  text-align: center !important;
  margin: 0 !important;
  display: block !important;
}

/* Icon styling for fallback tabs */
.tab-widget-container.bottom-tabs .nav-tabs .nav-link i {
  font-size: 18px !important;
  margin-bottom: 4px !important;
  transition: all 0.2s ease !important;
}

/* Remove default nav-item margins */
.tab-widget-container.bottom-tabs .nav-tabs .nav-item {
  margin-bottom: 0 !important;
}

/* Button focus states for Market Insights */
.tab-widget-container.bottom-tabs .nav-tabs .nav-link[type="button"] {
  cursor: pointer !important;
  outline: none !important;
}

.tab-widget-container.bottom-tabs .nav-tabs .nav-link[type="button"]:focus {
  box-shadow: none !important;
  outline: none !important;
}

/* Dark theme support for Market Insights tabs */
[data-bs-theme="dark"] .tab-widget-container.bottom-tabs .nav-tabs {
  background: #2d3748 !important;
  border-top-color: #4a5568 !important;
}

[data-bs-theme="dark"] .tab-widget-container.bottom-tabs .nav-tabs .nav-link {
  color: #a0aec0 !important;
}

[data-bs-theme="dark"]
  .tab-widget-container.bottom-tabs
  .nav-tabs
  .nav-link:hover {
  background: rgba(0, 177, 156, 0.2) !important;
  color: #00b19c !important;
}

[data-bs-theme="dark"]
  .tab-widget-container.bottom-tabs
  .nav-tabs
  .nav-link.active {
  background: #00b19c !important;
  color: white !important;
}

/* Dark theme image adjustments */
[data-bs-theme="dark"]
  .tab-widget-container.bottom-tabs
  .nav-tabs
  .nav-link
  img {
  filter: brightness(0.8) !important;
}

[data-bs-theme="dark"]
  .tab-widget-container.bottom-tabs
  .nav-tabs
  .nav-link:hover
  img {
  filter: brightness(1) !important;
}

[data-bs-theme="dark"]
  .tab-widget-container.bottom-tabs
  .nav-tabs
  .nav-link.active
  img {
  filter: brightness(0) invert(1) !important;
}

/* Grid stack integration - Compact */
.grid-stack-item .tab-widget-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.grid-stack-item .tab-widget-wrapper .widget-content {
  height: 100%;
  position: relative;
  overflow: visible;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.grid-stack-item .tab-widget-wrapper .widget-content .tab-container {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.grid-stack-item
  .tab-widget-wrapper
  .widget-content
  .tab-widget-container.bottom-tabs {
  height: 100%;
  position: relative;
  flex: 1;
}

.grid-stack-item
  .tab-widget-wrapper
  .widget-content
  .tab-widget-container.bottom-tabs
  .tab-content {
  height: 100%;
  padding-bottom: 60px; /* Space for absolutely positioned tabs */
}

/* Ensure the grid-stack-item-content has proper height handling */
.grid-stack-item-content {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Make sure widget wrapper fills the grid item completely */
.grid-stack-item-content .widget-wrapper {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Ensure widget header doesn't interfere with height calculations */
.grid-stack-item-content .widget-wrapper .widget-header {
  flex-shrink: 0;
}

/* Make widget content fill remaining space */
.grid-stack-item-content .widget-wrapper .widget-content {
  flex: 1 !important;
  height: auto !important;
  min-height: 0 !important;
}

/* Dark theme adjustments */
[data-bs-theme="dark"] .grid-stack-item .tab-widget-wrapper {
  background: var(--bs-dark);
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"] .grid-stack-item .tab-widget-wrapper .widget-content {
  background: var(--bs-dark);
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"]
  .grid-stack-item
  .tab-widget-wrapper
  .widget-content
  .tab-container {
  background: var(--bs-dark);
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"]
  .grid-stack-item
  .tab-widget-wrapper
  .widget-content
  .tab-widget-container.bottom-tabs {
  background: var(--bs-dark);
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"]
  .grid-stack-item
  .tab-widget-wrapper
  .widget-content
  .tab-widget-container.bottom-tabs
  .tab-content {
  background: var(--bs-dark);
  border-color: var(--bs-gray-700);
}

/* Specific positioning for nav-tabs within grid-stack */
.grid-stack-item-content .tab-widget-container.bottom-tabs .nav-tabs {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  margin: 0 !important;
  z-index: 10 !important;
}

/* Ensure proper height calculation for tab content within grid-stack */
.grid-stack-item-content .tab-widget-container.bottom-tabs .tab-content {
  height: calc(100% - 60px) !important;
  padding-bottom: 0 !important;
  overflow: auto;
}

/* Override any conflicting styles for grid-stack integration */
.grid-stack-item-content .tab-widget-container.bottom-tabs {
  position: relative !important;
  height: 100% !important;
  overflow: hidden !important;
}

/* Ensure widget body has proper positioning for bottom tabs */
.tab-widget-container.bottom-tabs .widget-body {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-bottom: 0; /* Remove padding - tabs are now absolutely positioned */
  padding-left: 0; /* Remove left padding to allow edge-to-edge tabs */
  padding-right: 0; /* Remove right padding to allow edge-to-edge tabs */
}

/* Ensure card body has relative positioning for absolute tabs */
.tab-widget-container.bottom-tabs .card-body {
  position: relative;
  overflow: hidden; /* Prevent layout shifts */
  height: 100%;
}

/* ===== CLEAN WIDGET MANAGEMENT DESIGN ===== */

/* Widget Management Container */
.widget-management {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

/* Widget Add Bar - Clean and Compact */
.widget-add-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.widget-add-bar .widget-type-select {
  flex: 1;
  max-width: 200px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 0.875rem;
  padding: 8px 12px;
  background: white;
  transition: all 0.2s ease;
}

.widget-add-bar .widget-type-select:focus {
  border-color: #02104f;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.widget-add-bar .btn-add-widget {
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.widget-add-bar .btn-add-widget:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

/* Widget List Container */
.widget-list-container {
  padding: 20px;
}

.widget-list-container.empty {
  padding: 40px 20px;
}

/* Widget Cards - Clean and Modern */
.widget-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  overflow: hidden;
}

.widget-card:hover {
  border-color: var(--ocean-teal);
  box-shadow: 0 2px 8px rgba(32, 178, 170, 0.15);
  transform: translateY(-1px);
}

.widget-card:last-child {
  margin-bottom: 0;
}

/* Widget Card Header */
.widget-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #fafbfc;
  border-bottom: 1px solid #f0f0f0;
}

.widget-card-main {
  padding: 20px;
}

/* Widget Info Section */
.widget-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.widget-type-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 10px;
  background: #e7f3ff;
  color: #02104f;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 70px;
  justify-content: center;
}

.widget-type-badge.kpi {
  background: #e7f3ff;
  color: #02104f;
}

.widget-type-badge.chart {
  background: #f0f9f0;
  color: #198754;
}

.widget-type-badge.text {
  background: #fff3e0;
  color: #fd7e14;
}

.widget-type-badge.table {
  background: #f8f0ff;
  color: #6f42c1;
}

.widget-title-input {
  border: none;
  background: transparent;
  font-size: 0.95rem;
  font-weight: 500;
  color: #212529;
  padding: 6px 0;
  flex: 1;
  min-width: 0;
}

.widget-title-input:focus {
  outline: none;
  background: white;
  border-radius: 4px;
  padding: 6px 8px;
  box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
}

/* Widget Actions */
.widget-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  color: #6c757d;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.btn-icon:hover {
  background: #f8f9fa;
  color: #495057;
  transform: scale(1.05);
}

.btn-icon.btn-danger:hover {
  background: #dc3545;
  color: white;
}

/* Widget Configuration Row */
.widget-config-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  align-items: end;
}

.widget-config-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.widget-config-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
}

.widget-config-input,
.widget-config-select {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 0.875rem;
  background: white;
  transition: all 0.2s ease;
}

.widget-config-input:focus,
.widget-config-select:focus {
  border-color: #02104f;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  outline: none;
}

/* Empty State - Clean and Engaging */
.empty-widgets-state {
  text-align: center;
  color: #6c757d;
  padding: 40px 20px;
}

.empty-widgets-state i {
  font-size: 3rem;
  color: #dee2e6;
  margin-bottom: 16px;
  display: block;
}

.empty-widgets-state h6 {
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.empty-widgets-state p {
  font-size: 0.875rem;
  margin: 0;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .widget-config-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .widget-add-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .widget-add-bar .widget-type-select {
    max-width: none;
  }

  .widget-card-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .widget-actions {
    justify-content: center;
  }
}

/* Dark Theme Support */
[data-bs-theme="dark"] .widget-management {
  background: #212529;
  border-color: #495057;
}

[data-bs-theme="dark"] .widget-add-bar {
  background: #343a40;
  border-color: #495057;
}

[data-bs-theme="dark"] .widget-card {
  background: #212529;
  border-color: #495057;
}

[data-bs-theme="dark"] .widget-card-header {
  background: #343a40;
  border-color: #495057;
}

[data-bs-theme="dark"] .widget-type-badge {
  background: rgba(13, 110, 253, 0.2);
  color: #6ea8fe;
}

[data-bs-theme="dark"] .widget-title-input {
  color: #f8f9fa;
}

[data-bs-theme="dark"] .widget-config-input,
[data-bs-theme="dark"] .widget-config-select {
  background: #343a40;
  border-color: #495057;
  color: #f8f9fa;
}

[data-bs-theme="dark"] .empty-widgets-state {
  color: #adb5bd;
}

/* Left tabs positioning */
.tab-widget-container.left-tabs {
  display: flex;
  flex-direction: row;
}

.tab-widget-container.left-tabs .nav-tabs {
  flex-direction: column;
  border-right: 1px solid var(--bs-border-color);
  border-bottom: none;
  min-width: 120px;
  margin-right: 10px;
}

.tab-widget-container.left-tabs .nav-tabs .nav-item {
  margin-bottom: 2px;
}

.tab-widget-container.left-tabs .nav-tabs .nav-link {
  border: none;
  border-right: 2px solid transparent;
  border-radius: 0;
  text-align: left;
  padding: 8px 12px;
}

.tab-widget-container.left-tabs .nav-tabs .nav-link:hover {
  background: rgba(var(--bs-primary-rgb), 0.1);
  border-right-color: var(--ocean-teal);
}

.tab-widget-container.left-tabs .nav-tabs .nav-link.active {
  background: var(--bs-body-bg);
  color: var(--ocean-teal);
  border-right: 2px solid var(--ocean-teal);
}

.tab-widget-container.left-tabs .tab-content {
  flex: 1;
  border: none;
}

/* Right tabs positioning */
.tab-widget-container.right-tabs {
  display: flex;
  flex-direction: row-reverse;
}

.tab-widget-container.right-tabs .nav-tabs {
  flex-direction: column;
  border-left: 1px solid var(--bs-border-color);
  border-bottom: none;
  min-width: 120px;
  margin-left: 10px;
}

.tab-widget-container.right-tabs .nav-tabs .nav-item {
  margin-bottom: 2px;
}

.tab-widget-container.right-tabs .nav-tabs .nav-link {
  border: none;
  border-left: 2px solid transparent;
  border-radius: 0;
  text-align: right;
  padding: 8px 12px;
}

.tab-widget-container.right-tabs .nav-tabs .nav-link:hover {
  background: rgba(var(--bs-primary-rgb), 0.1);
  border-left-color: var(--ocean-teal);
}

.tab-widget-container.right-tabs .nav-tabs .nav-link.active {
  background: var(--bs-body-bg);
  color: var(--ocean-teal);
  border-left: 2px solid var(--ocean-teal);
}

.tab-widget-container.right-tabs .tab-content {
  flex: 1;
  border: none;
}

/* Dark theme support for left and right tabs */
[data-bs-theme="dark"] .tab-widget-container.left-tabs .nav-tabs {
  background: var(--bs-gray-800);
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"] .tab-widget-container.left-tabs .nav-tabs .nav-link {
  color: var(--bs-gray-300);
}

[data-bs-theme="dark"]
  .tab-widget-container.left-tabs
  .nav-tabs
  .nav-link:hover {
  background: rgba(var(--bs-primary-rgb), 0.2);
  color: var(--bs-primary);
}

[data-bs-theme="dark"]
  .tab-widget-container.left-tabs
  .nav-tabs
  .nav-link.active {
  background: var(--bs-dark);
  color: var(--bs-primary);
}

[data-bs-theme="dark"] .tab-widget-container.right-tabs .nav-tabs {
  background: var(--bs-gray-800);
  border-color: var(--bs-gray-700);
}

[data-bs-theme="dark"] .tab-widget-container.right-tabs .nav-tabs .nav-link {
  color: var(--bs-gray-300);
}

[data-bs-theme="dark"]
  .tab-widget-container.right-tabs
  .nav-tabs
  .nav-link:hover {
  background: rgba(var(--bs-primary-rgb), 0.2);
  color: var(--bs-primary);
}

[data-bs-theme="dark"]
  .tab-widget-container.right-tabs
  .nav-tabs
  .nav-link.active {
  background: var(--bs-dark);
  color: var(--bs-primary);
}

/* Ocean Teal Button Theme Override */
.btn-outline-primary {
  color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
  background-color: transparent !important;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary:active {
  color: white !important;
  background-color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 177, 156, 0.25) !important;
}

.btn-primary {
  color: white !important;
  background-color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
  color: white !important;
  background-color: var(--forest-green, #007365) !important;
  border-color: var(--forest-green, #007365) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 177, 156, 0.25) !important;
}

/* Widget Selector Card Styling */
.widget-selector-card .btn-outline-primary {
  color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
}

.widget-selector-card .btn-outline-primary:hover {
  background-color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
  color: white !important;
}

/* Custom Layout Builder Button Group */
#custom-layout-builder .btn-group .btn-outline-primary {
  color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
  background-color: white !important;
  transition: all 0.2s ease !important;
}

#custom-layout-builder .btn-group .btn-outline-primary:hover {
  background-color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(0, 177, 156, 0.2) !important;
}

#custom-layout-builder .btn-group .btn-outline-primary:active {
  transform: translateY(0) !important;
  box-shadow: 0 1px 2px rgba(0, 177, 156, 0.2) !important;
}

/* Smart Widget Composer Offcanvas */
#smartWidgetComposerOffcanvas .btn-primary {
  background-color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
}

#smartWidgetComposerOffcanvas .btn-primary:hover {
  background-color: var(--forest-green, #007365) !important;
  border-color: var(--forest-green, #007365) !important;
}

#smartWidgetComposerOffcanvas .btn-outline-primary {
  color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
}

#smartWidgetComposerOffcanvas .btn-outline-primary:hover {
  background-color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
  color: white !important;
}

/* Tab Configuration Buttons */
#tabConfigOffcanvas .btn-primary {
  background-color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
  color: white !important;
}

#tabConfigOffcanvas .btn-primary:hover {
  background-color: var(--forest-green, #007365) !important;
  border-color: var(--forest-green, #007365) !important;
  color: white !important;
}

/* Layout Slot Actions */
.layout-slot .btn-outline-primary {
  color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
}

.layout-slot .btn-outline-primary:hover {
  background-color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
  color: white !important;
}

/* Dark Theme Support */
body.dark-theme .btn-outline-primary {
  color: var(--ocean-teal-light, #cce9e6) !important;
  border-color: var(--ocean-teal-light, #cce9e6) !important;
}

body.dark-theme .btn-outline-primary:hover {
  background-color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
  color: white !important;
}

body.dark-theme .btn-primary {
  background-color: var(--ocean-teal, #00b19c) !important;
  border-color: var(--ocean-teal, #00b19c) !important;
}

body.dark-theme .btn-primary:hover {
  background-color: var(--forest-green, #007365) !important;
  border-color: var(--forest-green, #007365) !important;
}

/* Smart Widget Composer Offcanvas */
#smartWidgetComposerOffcanvas {
  --bs-offcanvas-bg: #ffffff;
  --bs-offcanvas-border-color: var(--ocean-teal-light);
}

#smartWidgetComposerOffcanvas .offcanvas-header {
  background: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  color: white;
}

#smartWidgetComposerOffcanvas .offcanvas-title {
  color: white;
  font-weight: 600;
}

#smartWidgetComposerOffcanvas .btn-close {
  filter: brightness(0) invert(1);
}

#smartWidgetComposerOffcanvas .nav-pills .nav-link {
  color: var(--ocean-teal);
  border: 1px solid var(--ocean-teal-light);
  margin-bottom: 5px;
}

#smartWidgetComposerOffcanvas .nav-pills .nav-link:hover {
  background-color: var(--ocean-teal-light);
  color: white;
  border-color: var(--ocean-teal);
}

#smartWidgetComposerOffcanvas .nav-pills .nav-link.active {
  background-color: var(--ocean-teal);
  border-color: var(--ocean-teal-dark);
  color: white;
}

#smartWidgetComposerOffcanvas .form-control {
  border-color: var(--ocean-teal-light);
}

#smartWidgetComposerOffcanvas .form-control:focus {
  border-color: var(--ocean-teal);
  box-shadow: 0 0 0 0.2rem rgba(32, 178, 170, 0.25);
}

#smartWidgetComposerOffcanvas .form-label {
  color: var(--ocean-teal-dark);
  font-weight: 500;
}

#smartWidgetComposerOffcanvas .text-primary {
  color: var(--bs-primary, #20b2aa);
}

#smartWidgetComposerOffcanvas .border-primary {
  border-color: var(--ocean-teal) !important;
}

#smartWidgetComposerOffcanvas .bg-primary {
  background-color: var(--ocean-teal) !important;
}

#smartWidgetComposerOffcanvas .btn-outline-secondary {
  color: var(--ocean-teal);
  border-color: var(--ocean-teal-light);
}

#smartWidgetComposerOffcanvas .btn-outline-secondary:hover {
  background-color: var(--ocean-teal);
  border-color: var(--ocean-teal);
  color: white;
}

#smartWidgetComposerOffcanvas .list-group-item {
  border-color: var(--ocean-teal-light);
}

#smartWidgetComposerOffcanvas .list-group-item:hover {
  background-color: rgba(32, 178, 170, 0.1);
}

#smartWidgetComposerOffcanvas .list-group-item.active {
  background-color: var(--ocean-teal);
  border-color: var(--ocean-teal);
  color: white;
}

#smartWidgetComposerOffcanvas .badge {
  background-color: var(--ocean-teal);
}

#smartWidgetComposerOffcanvas .alert-primary {
  background-color: rgba(32, 178, 170, 0.1);
  border-color: var(--ocean-teal-light);
  color: var(--ocean-teal-dark);
}

/* Dark theme support for Smart Widget Composer Offcanvas */
[data-bs-theme="dark"] #smartWidgetComposerOffcanvas {
  --bs-offcanvas-bg: #1a1a1a;
  --bs-offcanvas-color: #ffffff;
}

[data-bs-theme="dark"] #smartWidgetComposerOffcanvas .offcanvas-header {
  background: linear-gradient(135deg, var(--ocean-teal-dark) 0%, #0f7a7a 100%);
  border-bottom-color: var(--ocean-teal);
}

[data-bs-theme="dark"] #smartWidgetComposerOffcanvas .nav-pills .nav-link {
  color: var(--ocean-teal-light);
  border-color: var(--ocean-teal);
}

[data-bs-theme="dark"]
  #smartWidgetComposerOffcanvas
  .nav-pills
  .nav-link:hover {
  background-color: var(--ocean-teal);
  border-color: var(--ocean-teal-light);
}

[data-bs-theme="dark"] #smartWidgetComposerOffcanvas .form-control {
  background-color: #2a2a2a;
  border-color: var(--ocean-teal);
  color: #ffffff;
}

[data-bs-theme="dark"] #smartWidgetComposerOffcanvas .form-label {
  color: var(--ocean-teal-light);
}

[data-bs-theme="dark"] #smartWidgetComposerOffcanvas .list-group-item {
  background-color: #2a2a2a;
  border-color: var(--ocean-teal);
  color: #ffffff;
}

[data-bs-theme="dark"] #smartWidgetComposerOffcanvas .list-group-item:hover {
  background-color: rgba(32, 178, 170, 0.2);
}

/* Active state styles for button highlighting */
.btn-outline-primary.active,
.btn-outline-primary:active {
  background-color: var(--ocean-teal) !important;
  border-color: var(--ocean-teal) !important;
  color: white !important;
  box-shadow: 0 0 0 0.2rem rgba(32, 178, 170, 0.25) !important;
}

.btn-outline-primary.active:hover,
.btn-outline-primary:active:hover {
  background-color: #1a9b95 !important;
  border-color: #1a9b95 !important;
  color: white !important;
}

/* Custom layout builder button group active states */
#custom-layout-builder .btn-group .btn-outline-primary.active {
  background-color: var(--ocean-teal) !important;
  border-color: var(--ocean-teal) !important;
  color: white !important;
}

#custom-layout-builder .btn-group .btn-outline-primary.active:hover {
  background-color: #1a9b95 !important;
  border-color: #1a9b95 !important;
  color: white !important;
}

/* Focus states for accessibility */
.btn-outline-primary:focus,
.btn-outline-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(32, 178, 170, 0.25) !important;
}

/* Dark theme active states */
body.dark-theme .btn-outline-primary.active,
body.dark-theme .btn-outline-primary:active {
  background-color: var(--ocean-teal) !important;
  border-color: var(--ocean-teal) !important;
  color: white !important;
}

body.dark-theme .btn-outline-primary.active:hover,
body.dark-theme .btn-outline-primary:active:hover {
  background-color: #1a9b95 !important;
  border-color: #1a9b95 !important;
  color: white !important;
}

/* Inline editing for slot titles */
.slot-title.editing {
  background: var(--bs-body-bg);
  border: 1px solid var(--ocean-teal);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
}

.slot-title.editing span {
  display: none;
}

.slot-title.editing input {
  background: transparent;
  border: none;
  outline: none;
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--ocean-teal);
  width: 100%;
  padding: 0;
  margin: 0;
}

.slot-title:not(.editing) {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.slot-title:not(.editing):hover {
  background: rgba(32, 178, 170, 0.05);
  border-radius: 4px;
  padding: 0.25rem 0.5rem;
}

/* Improved widget type options */
.widget-type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 0.5rem;
  border: 1px solid var(--bs-border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  background: var(--bs-body-bg);
  min-height: 80px;
  position: relative;
}

.widget-type-option:hover {
  border-color: var(--ocean-teal);
  background: rgba(32, 178, 170, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(32, 178, 170, 0.15);
}

.widget-type-icon {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
  color: var(--ocean-teal);
  transition: transform 0.2s ease;
}

.widget-type-option:hover .widget-type-icon {
  transform: scale(1.1);
}

.widget-type-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--bs-body-color);
  line-height: 1.2;
  margin-top: auto;
}

.widget-type-option:hover .widget-type-label {
  color: var(--ocean-teal);
}

/* Improved selector header */
.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--bs-border-color);
}

.selector-header h6 {
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
  color: var(--ocean-teal);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.selector-header h6 i {
  font-size: 1.2rem;
  color: var(--ocean-teal);
}

.selector-header button {
  background: none;
  border: 1px solid var(--bs-border-color);
  color: var(--bs-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selector-header button:hover {
  border-color: var(--bs-danger);
  color: var(--bs-danger);
  background: rgba(220, 53, 69, 0.1);
}

/* Improved widget selector card */
.widget-selector-card {
  border: 2px solid var(--ocean-teal);
  border-radius: 8px;
  padding: 1rem;
  background: var(--bs-body-bg);
  box-shadow: 0 4px 16px rgba(32, 178, 170, 0.1);
  animation: slideIn 0.3s ease-out;
}

/* Improved widget type grid */
.widget-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
  gap: 0.75rem;
  margin-top: 0.5rem;
}

[data-bs-theme="dark"] .layout-slot:hover .slot-preview {
  background: rgba(32, 178, 170, 0.2);
}

[data-bs-theme="dark"] .world-map-preview {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

[data-bs-theme="dark"] .world-map-preview::before {
  background: var(--ocean-teal);
  opacity: 0.9;
}

[data-bs-theme="dark"] .world-map-preview::after {
  background: var(--forest-green);
  opacity: 0.8;
}

[data-bs-theme="dark"] .word-cloud-preview {
  background: linear-gradient(45deg, #1a1a1a 0%, #2d2d2d 100%);
}

[data-bs-theme="dark"] .word-cloud-preview::before {
  color: var(--ocean-teal);
}

[data-bs-theme="dark"] .word-cloud-preview::after {
  color: var(--forest-green);
}

/* Enhanced Widget Selector Styles */
.widget-selector-card.enhanced {
  border: 2px solid var(--ocean-teal);
  box-shadow: 0 4px 12px rgba(32, 178, 170, 0.15);
}

.widget-selector-card.enhanced .selector-header h6 {
  color: var(--ocean-teal);
  font-weight: 600;
}

.existing-widgets-info {
  background: rgba(32, 178, 170, 0.05);
  border: 1px solid rgba(32, 178, 170, 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 15px;
}

.existing-widgets-info small {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
}

.existing-widgets-info .las {
  color: var(--ocean-teal);
}

/* Improved widget selector toggle behavior */
.widget-selector-card {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Better button active states */
#custom-layout-builder .btn-group .btn-outline-primary.active {
  background-color: var(--ocean-teal);
  border-color: var(--ocean-teal);
  color: white;
  box-shadow: 0 2px 8px rgba(32, 178, 170, 0.3);
}

#custom-layout-builder .btn-group .btn-outline-primary.active:hover {
  background-color: #1a9b95;
  border-color: #1a9b95;
}

/* Save Template Button */
.save-template-btn {
  background: var(--ocean-teal) !important;
  color: white !important;
  border: 1px solid var(--ocean-teal) !important;
  transition: all 0.3s ease;
}

.save-template-btn:hover {
  background: #1a9d8a !important;
  border-color: #1a9d8a !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(32, 178, 170, 0.3);
}

/* Template Preview Card in Modal */
.template-preview-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
}

.template-preview-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.template-preview-content .preview-item {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  flex-shrink: 0;
}

.template-preview-content .preview-item.kpi {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.template-preview-content .preview-item.chart,
.template-preview-content .preview-item.pie-chart,
.template-preview-content .preview-item.bar-chart,
.template-preview-content .preview-item.line-chart {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.template-preview-content .preview-item.handsontable {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.template-preview-content .preview-item.text {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.template-preview-content .preview-item.image {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.template-preview-content .preview-item.tab-container {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.template-preview-content .preview-item.price-chart {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* Custom Template Cards */
.template-card.custom-template {
  border: 2px solid var(--ocean-teal);
  background: rgba(32, 178, 170, 0.05);
  position: relative;
}

.template-card.custom-template::before {
  content: "SAVED";
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: var(--ocean-teal);
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  z-index: 1;
}

.template-card.custom-template:hover {
  border-color: #1a9d8a;
  background: rgba(32, 178, 170, 0.1);
  box-shadow: 0 4px 12px rgba(32, 178, 170, 0.2);
}

.template-actions {
  display: flex;
  gap: 0.25rem;
  justify-content: center;
}

.template-actions .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* Dark theme adjustments */
[data-bs-theme="dark"] .template-preview-card {
  background: #2c3e50;
  border-color: #34495e;
}

[data-bs-theme="dark"] .template-preview-content {
  background: #34495e;
  border-color: #4a5568;
}

[data-bs-theme="dark"] .template-card.custom-template {
  background: rgba(32, 178, 170, 0.1);
  border-color: var(--ocean-teal);
}

[data-bs-theme="dark"] .template-card.custom-template:hover {
  background: rgba(32, 178, 170, 0.15);
}

/* Save button for sections */
.section-container .section-header .save-section-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.section-container .section-header .save-section-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Template meta information */
.template-meta {
  margin-top: 8px;
  margin-bottom: 8px;
}

.template-meta .badge {
  font-size: 10px;
  padding: 4px 8px;
}

/* Enhanced template preview for sections */
.preview-grid.custom-preview {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2px;
  padding: 8px;
  min-height: 60px;
}

.preview-grid.custom-preview .preview-item {
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  min-height: 25px;
}

/* Section template specific styles */
.template-card.section-template {
  border-left: 3px solid var(--ocean-teal);
}

.template-card.section-template .template-preview {
  background: linear-gradient(
    135deg,
    rgba(0, 123, 255, 0.1) 0%,
    rgba(102, 126, 234, 0.1) 100%
  );
}

/* Save section template modal */
#saveSectionTemplateModal .template-preview-card {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
}

#saveSectionTemplateModal .template-preview-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

#saveSectionTemplateModal .preview-item.section {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--ocean-teal) 0%, #4a90e2 100%);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

/* Dark theme adjustments */
body.dark-theme #saveSectionTemplateModal .template-preview-card {
  background: #2d3748;
  border-color: #4a5568;
}

body.dark-theme .template-card.section-template .template-preview {
  background: linear-gradient(
    135deg,
    rgba(0, 123, 255, 0.2) 0%,
    rgba(102, 126, 234, 0.2) 100%
  );
}
