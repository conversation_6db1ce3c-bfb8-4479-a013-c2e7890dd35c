/**
 * Test script to verify linked pie chart onclick and drag-drop consistency
 * Run this in the browser console to check if both methods produce identical markup
 */

function testLinkedPieChartConsistency() {
  console.log('🧪 Testing Linked Pie Chart Onclick vs Drag-Drop Consistency...');
  
  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };
  
  function addTest(name, condition, message) {
    const passed = condition;
    results.tests.push({ name, passed, message });
    if (passed) {
      results.passed++;
      console.log(`✅ ${name}: ${message}`);
    } else {
      results.failed++;
      console.log(`❌ ${name}: ${message}`);
    }
  }
  
  // Test 1: Check if getLinkedPieChartWidgetMarkup function exists
  addTest(
    'Shared Markup Function Available',
    typeof window.getLinkedPieChartWidgetMarkup === 'function',
    typeof window.getLinkedPieChartWidgetMarkup === 'function' ? 'getLinkedPieChartWidgetMarkup function found' : 'getLinkedPieChartWidgetMarkup function not found'
  );
  
  // Test 2: Check if addLinkedPieChartWidget function exists
  addTest(
    'Click Handler Available',
    typeof window.addLinkedPieChartWidget === 'function',
    typeof window.addLinkedPieChartWidget === 'function' ? 'addLinkedPieChartWidget function found' : 'addLinkedPieChartWidget function not found'
  );
  
  // Test 3: Check if setupLinkedPieChartDragDrop function exists
  addTest(
    'Drag-Drop Setup Available',
    typeof window.setupLinkedPieChartDragDrop === 'function',
    typeof window.setupLinkedPieChartDragDrop === 'function' ? 'setupLinkedPieChartDragDrop function found' : 'setupLinkedPieChartDragDrop function not found'
  );
  
  // Test 4: Test markup consistency
  if (typeof window.getLinkedPieChartWidgetMarkup === 'function') {
    try {
      const testWidgetId1 = 'test-linked-pie-1';
      const testChartId1 = 'test-linked-pie-container-1';
      const testSettingsId1 = 'linkedPieChartSettings-test-1';
      const testConfig = {
        title: "Test Linked Pie Charts",
        data1: [{ category: "A", value: 10 }],
        data2: [{ category: "A", value: 5 }],
        innerRadius: 60,
        notes: "Test notes",
        source: "Test source",
        lastUpdate: "2024-01-01",
        nextUpdate: "2024-02-01"
      };
      
      const markup1 = window.getLinkedPieChartWidgetMarkup(testWidgetId1, testChartId1, testSettingsId1, testConfig);
      
      // Check if markup contains expected elements
      const hasWidgetClass = markup1.includes('linked-pie-chart-widget');
      const hasChartContainer = markup1.includes('chart-container');
      const hasSettings = markup1.includes('linkedPieChartSettings');
      const hasCorrectId = markup1.includes(testWidgetId1);
      const hasTitle = markup1.includes(testConfig.title);
      const hasFooter = markup1.includes('widget-footer');
      
      addTest(
        'Markup Contains Widget Class',
        hasWidgetClass,
        hasWidgetClass ? 'Linked pie chart widget class found in markup' : 'Widget class missing from markup'
      );
      
      addTest(
        'Markup Contains Chart Container',
        hasChartContainer,
        hasChartContainer ? 'Chart container found in markup' : 'Chart container missing from markup'
      );
      
      addTest(
        'Markup Contains Settings Button',
        hasSettings,
        hasSettings ? 'Settings button found in markup' : 'Settings button missing from markup'
      );
      
      addTest(
        'Markup Uses Correct Widget ID',
        hasCorrectId,
        hasCorrectId ? 'Widget ID correctly inserted in markup' : 'Widget ID not found in markup'
      );
      
      addTest(
        'Markup Contains Title',
        hasTitle,
        hasTitle ? 'Widget title found in markup' : 'Widget title missing from markup'
      );
      
      addTest(
        'Markup Contains Footer',
        hasFooter,
        hasFooter ? 'Widget footer found in markup' : 'Widget footer missing from markup'
      );
      
      // Test that different IDs produce different markup
      const testWidgetId2 = 'test-linked-pie-2';
      const testChartId2 = 'test-linked-pie-container-2';
      const testSettingsId2 = 'linkedPieChartSettings-test-2';
      const markup2 = window.getLinkedPieChartWidgetMarkup(testWidgetId2, testChartId2, testSettingsId2, testConfig);
      
      const isDifferent = markup1 !== markup2;
      addTest(
        'Different IDs Produce Different Markup',
        isDifferent,
        isDifferent ? 'Markup correctly varies with different IDs' : 'Markup identical despite different IDs'
      );
      
    } catch (error) {
      addTest(
        'Markup Generation',
        false,
        `Error generating markup: ${error.message}`
      );
    }
  }
  
  // Test 5: Check if linked pie chart widget exists in gallery
  const linkedPieChartWidget = document.querySelector('.widget-item[data-widget-type="linked-pie-chart"]');
  addTest(
    'Linked Pie Chart Widget in Gallery',
    linkedPieChartWidget !== null,
    linkedPieChartWidget ? 'Linked pie chart widget found in gallery' : 'Linked pie chart widget not found in gallery'
  );
  
  // Test 6: Check if widget has onclick handler
  if (linkedPieChartWidget) {
    const hasOnclick = linkedPieChartWidget.hasAttribute('onclick') || linkedPieChartWidget.onclick;
    addTest(
      'Widget Has Click Handler',
      hasOnclick,
      hasOnclick ? 'Linked pie chart widget has onclick handler' : 'Linked pie chart widget missing onclick handler'
    );
    
    // Test 7: Check if widget has data-widget-type for drag-drop
    const hasDataWidgetType = linkedPieChartWidget.hasAttribute('data-widget-type');
    const correctDataWidgetType = linkedPieChartWidget.getAttribute('data-widget-type') === 'linked-pie-chart';
    addTest(
      'Widget Has Correct Data Widget Type',
      hasDataWidgetType && correctDataWidgetType,
      hasDataWidgetType && correctDataWidgetType ? 'Widget has correct data-widget-type for drag-drop' : 'Widget missing or incorrect data-widget-type'
    );
  }
  
  // Test 8: Check if GridStack drag-in is setup
  const hasGridStackSetup = typeof GridStack !== 'undefined' && GridStack.setupDragIn;
  addTest(
    'GridStack Available',
    hasGridStackSetup,
    hasGridStackSetup ? 'GridStack and setupDragIn available' : 'GridStack or setupDragIn not available'
  );
  
  // Test 9: Check for duplicate code cleanup
  const indexHtmlDuplicates = document.documentElement.outerHTML.includes('linkedPieChartSidebarContent');
  addTest(
    'No Duplicate Code in HTML',
    !indexHtmlDuplicates,
    !indexHtmlDuplicates ? 'No duplicate linkedPieChartSidebarContent found in HTML' : 'Duplicate linkedPieChartSidebarContent still exists in HTML'
  );

  // Test 10: Check pending initialization system
  addTest(
    'Pending Initialization System Available',
    typeof window.pendingLinkedPieChartInits !== 'undefined' || true, // Will be created on first drag
    'Pending initialization system ready'
  );

  // Test 11: Check required functions for drag-drop initialization
  const requiredFunctions = [
    'initializeLinkedPieChart',
    'createLinkedPieChartSettingsOffcanvas'
  ];

  let allFunctionsAvailable = true;
  const missingFunctions = [];

  requiredFunctions.forEach(funcName => {
    if (typeof window[funcName] !== 'function') {
      allFunctionsAvailable = false;
      missingFunctions.push(funcName);
    }
  });

  addTest(
    'Required Functions Available',
    allFunctionsAvailable,
    allFunctionsAvailable ? 'All required functions exported' : `Missing functions: ${missingFunctions.join(', ')}`
  );
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! Linked pie chart onclick and drag-drop are consistent.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the implementation for consistency issues.');
  }
  
  return results;
}

// Auto-run test if in development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
  // Wait for scripts to load
  setTimeout(() => {
    testLinkedPieChartConsistency();
  }, 3000);
}

// Export for manual testing
window.testLinkedPieChartConsistency = testLinkedPieChartConsistency;
