/* Tabbed Container Widget Styles */
.tabbed-container-widget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 0px;
  overflow: hidden;
  position: relative;
}

/* Widget Header */
.tabbed-container-widget .widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  background: white;
  border-bottom: 1px solid #e5e9f0;
  z-index: 2;
}

.tabbed-container-widget .widget-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.tabbed-container-widget .widget-header i {
  font-size: 16px;
  color: #007365;
}

/* Tabs Navigation */
.tabbed-container-widget .tabs-navigation {
  position: relative;
  z-index: 1;
}

.tabbed-container-widget .nav-tabs {
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 10px;
}

.tabbed-container-widget .nav-tabs .nav-link {
  color: #495057;
  border: 1px solid transparent;
  border-radius: 0;
  margin-bottom: -1px;
  padding: 8px 15px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.tabbed-container-widget .nav-tabs .nav-link:hover {
  border-color: #e9ecef #e9ecef #dee2e6;
  color: #007365;
}

.tabbed-container-widget .nav-tabs .nav-link.active {
  color: #007365;
  background-color: white;
  border-color: #dee2e6 #dee2e6 white;
}

/* Pills style */
.tabbed-container-widget .nav-pills .nav-link {
  border-radius: 4px;
  margin-right: 4px;
  padding: 6px 15px;
}

.tabbed-container-widget .nav-pills .nav-link.active {
  background-color: #007365;
  color: white;
}

/* Underlined style */
.tabbed-container-widget .nav-tabs-underlined {
  border-bottom: none;
}

.tabbed-container-widget .nav-tabs-underlined .nav-link {
  border: none;
  border-bottom: 2px solid transparent;
  padding-left: 2px;
  padding-right: 2px;
  margin: 0 10px;
}

.tabbed-container-widget .nav-tabs-underlined .nav-link:hover {
  border: none;
  border-bottom: 2px solid rgba(0, 115, 101, 0.5);
}

.tabbed-container-widget .nav-tabs-underlined .nav-link.active {
  border: none;
  border-bottom: 2px solid #007365;
  font-weight: 600;
}

/* Tab Content */
.tabbed-container-widget .tab-content {
  flex: 1;
  overflow: auto;
  position: relative;
}

.tabbed-container-widget .tab-pane {
  height: 100%;
  min-height: 200px;
  position: relative;
}

/* Nested Grid Container */
.tabbed-container-widget .nested-grid-container {
  height: 100%;
  min-height: 200px;
  position: relative;
  padding: 5px;
}

.tabbed-container-widget .grid-stack {
  background: #f8f9fa;
  border-radius: 4px;
}

.tabbed-container-widget .nested-grid .grid-stack-item-content {
  background: white;
  border: 1px solid #e5e9f0;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tabbed-container-widget .nav-tabs .nav-link,
  .tabbed-container-widget .nav-pills .nav-link {
    padding: 6px 10px;
    font-size: 12px;
  }
  
  .tabbed-container-widget .widget-header {
    height: 40px;
    padding: 0 12px;
  }
  
  .tabbed-container-widget .widget-header > div:first-child {
    font-size: 13px;
  }
}

/* Dark Theme Support */
.dark-theme .tabbed-container-widget {
  background-color: #1a1a2d;
  border-color: #2d2d3d;
}

.dark-theme .tabbed-container-widget .widget-header {
  background: #1e1e2d;
  border-color: #2d2d3d;
}

.dark-theme .tabbed-container-widget .widget-header > div:first-child {
  color: #a1a5b7;
}

.dark-theme .tabbed-container-widget .widget-header i {
  color: #009e8b;
}

.dark-theme .tabbed-container-widget .nav-tabs {
  border-color: #2d2d3d;
}

.dark-theme .tabbed-container-widget .nav-tabs .nav-link {
  color: #a1a5b7;
}

.dark-theme .tabbed-container-widget .nav-tabs .nav-link:hover {
  border-color: #2d2d3d;
  color: #009e8b;
}

.dark-theme .tabbed-container-widget .nav-tabs .nav-link.active {
  color: #009e8b;
  background-color: #1a1a2d;
  border-color: #2d2d3d #2d2d3d #1a1a2d;
}

.dark-theme .tabbed-container-widget .nav-pills .nav-link.active {
  background-color: #009e8b;
  color: #1a1a2d;
}

.dark-theme .tabbed-container-widget .grid-stack {
  background: #151521;
}

.dark-theme .tabbed-container-widget .nested-grid .grid-stack-item-content {
  background: #1e1e2d;
  border-color: #2d2d3d;
} 