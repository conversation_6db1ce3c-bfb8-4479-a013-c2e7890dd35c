// Returns the HTML markup for a text widget, given textId and settingsId
function getTextWidgetMarkup({ textId, settingsId }) {
  return `
    <div class="text-widget p-2">
      <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
        <div>
           Text
        </div>
        <div>
          <button class="btn btn-sm btn-link text-dark"
                  data-bs-toggle="offcanvas"
                  data-bs-target="#${settingsId}"
                  aria-controls="${settingsId}">
            <i class="las la-cog"></i>
          </button>
          <button class="btn btn-sm btn-link text-dark ms-1"
                  onclick="removeWidget(this)">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      <div id="${textId}" class="text-container">
        <div class="text-content" contenteditable="true" style="
          min-height: 100px;
          padding: 10px;
          border: 1px solid #e0e0e0;
          border-radius: 4px;
          outline: none;
          font-family: Arial, sans-serif;
          font-size: 14px;
          line-height: 1.5;
          color: #333;
          background-color: #fff;
        ">
          <p>Click here to edit text...</p>
        </div>
      </div>
    </div>
  `;
}

// Add a text widget
function addTextWidget() {
  console.log("Adding text widget");
  const textId = "text-" + Date.now();
  const settingsId = "settings-" + textId;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 4,
    h: 4,
    content: getTextWidgetMarkup({ textId, settingsId }),
  });

  // Create settings panel in the offcanvas container
  const offcanvasContainer = document.getElementById("offcanvasContainer");
  const settingsPanel = document.createElement("div");
  settingsPanel.className = "offcanvas offcanvas-end";
  settingsPanel.id = settingsId;
  settingsPanel.setAttribute("tabindex", "-1");
  settingsPanel.innerHTML = `
    <div class="offcanvas-header">
      <h5 class="offcanvas-title">Text Settings</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- Text Content -->
      <div class="mb-3">
        <label for="${settingsId}-content" class="form-label">Text Content</label>
        <textarea class="form-control" id="${settingsId}-content" rows="4" placeholder="Enter your text content here..."></textarea>
      </div>

      <!-- Font Family -->
      <div class="mb-3">
        <label class="form-label">Font Family</label>
        <select class="form-select" id="${settingsId}-fontFamily">
          <option value="Arial, sans-serif">Arial</option>
          <option value="Georgia, serif">Georgia</option>
          <option value="Times New Roman, serif">Times New Roman</option>
          <option value="Helvetica, sans-serif">Helvetica</option>
          <option value="Verdana, sans-serif">Verdana</option>
          <option value="Courier New, monospace">Courier New</option>
        </select>
      </div>

      <!-- Font Size -->
      <div class="mb-3">
        <label class="form-label">Font Size (px)</label>
        <input type="range" class="form-range" min="10" max="36" value="14" id="${settingsId}-fontSize">
        <div class="d-flex justify-content-between">
          <small>10px</small>
          <small id="${settingsId}-fontSize-display">14px</small>
          <small>36px</small>
        </div>
      </div>

      <!-- Text Alignment -->
      <div class="mb-3">
        <label class="form-label">Text Alignment</label>
        <select class="form-select" id="${settingsId}-textAlign">
          <option value="left">Left</option>
          <option value="center">Center</option>
          <option value="right">Right</option>
          <option value="justify">Justify</option>
        </select>
      </div>

      <!-- Text Color -->
      <div class="mb-3">
        <label for="${settingsId}-textColor" class="form-label">Text Color</label>
        <input type="color" class="form-control form-control-color" id="${settingsId}-textColor" value="#333333">
      </div>

      <!-- Background Color -->
      <div class="mb-3">
        <label for="${settingsId}-bgcolor" class="form-label">Background Color</label>
        <input type="color" class="form-control form-control-color" id="${settingsId}-bgcolor" value="#ffffff">
      </div>

      <!-- Line Height -->
      <div class="mb-3">
        <label class="form-label">Line Height</label>
        <input type="range" class="form-range" min="1" max="3" step="0.1" value="1.5" id="${settingsId}-lineHeight">
        <div class="d-flex justify-content-between">
          <small>1.0</small>
          <small id="${settingsId}-lineHeight-display">1.5</small>
          <small>3.0</small>
        </div>
      </div>

      <!-- Text Formatting -->
      <div class="mb-3">
        <label class="form-label">Text Formatting</label>
        <div class="d-flex gap-2">
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="${settingsId}-bold">
            <label class="form-check-label" for="${settingsId}-bold">
              <strong>Bold</strong>
            </label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="${settingsId}-italic">
            <label class="form-check-label" for="${settingsId}-italic">
              <em>Italic</em>
            </label>
          </div>
          <div class="form-check">
            <input class="form-check-input" type="checkbox" id="${settingsId}-underline">
            <label class="form-check-label" for="${settingsId}-underline">
              <u>Underline</u>
            </label>
          </div>
        </div>
      </div>

      <!-- Border -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-border" checked>
          <label class="form-check-label" for="${settingsId}-border">Show Border</label>
        </div>
      </div>

      <!-- Border Radius -->
      <div class="mb-3">
        <label class="form-label">Border Radius (px)</label>
        <input type="range" class="form-range" min="0" max="20" value="4" id="${settingsId}-radius">
        <div class="d-flex justify-content-between">
          <small>0px</small>
          <small id="${settingsId}-radius-display">4px</small>
          <small>20px</small>
        </div>
      </div>

      <!-- Padding -->
      <div class="mb-3">
        <label class="form-label">Padding (px)</label>
        <input type="range" class="form-range" min="5" max="30" value="10" id="${settingsId}-padding">
        <div class="d-flex justify-content-between">
          <small>5px</small>
          <small id="${settingsId}-padding-display">10px</small>
          <small>30px</small>
        </div>
      </div>

      <!-- Apply Button -->
      <button class="btn btn-primary w-100" onclick="applyTextSettings('${textId}', '${settingsId}')">
        Apply Changes
      </button>
    </div>
  `;
  offcanvasContainer.appendChild(settingsPanel);

  // Initialize the text widget
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing text widget");
      initTextWidget(textId, settingsId);
    } catch (error) {
      console.error("Error initializing text widget:", error);
    }
  }, 100);

  return widget;
}

// Function to initialize the text widget
function initTextWidget(textId, settingsId) {
  console.log("Initializing text widget:", textId);
  const container = document.getElementById(textId);
  const textContent = container.querySelector(".text-content");

  if (!container || !textContent) {
    console.error("Text container not found:", textId);
    return;
  }

  // Set up real-time range slider updates
  const fontSize = document.getElementById(`${settingsId}-fontSize`);
  const fontSizeDisplay = document.getElementById(
    `${settingsId}-fontSize-display`
  );
  const lineHeight = document.getElementById(`${settingsId}-lineHeight`);
  const lineHeightDisplay = document.getElementById(
    `${settingsId}-lineHeight-display`
  );
  const radius = document.getElementById(`${settingsId}-radius`);
  const radiusDisplay = document.getElementById(`${settingsId}-radius-display`);
  const padding = document.getElementById(`${settingsId}-padding`);
  const paddingDisplay = document.getElementById(
    `${settingsId}-padding-display`
  );

  if (fontSize && fontSizeDisplay) {
    fontSize.addEventListener("input", function () {
      fontSizeDisplay.textContent = this.value + "px";
    });
  }

  if (lineHeight && lineHeightDisplay) {
    lineHeight.addEventListener("input", function () {
      lineHeightDisplay.textContent = this.value;
    });
  }

  if (radius && radiusDisplay) {
    radius.addEventListener("input", function () {
      radiusDisplay.textContent = this.value + "px";
    });
  }

  if (padding && paddingDisplay) {
    padding.addEventListener("input", function () {
      paddingDisplay.textContent = this.value + "px";
    });
  }

  // Load current content into settings
  const contentTextarea = document.getElementById(`${settingsId}-content`);
  if (contentTextarea) {
    contentTextarea.value = textContent.textContent || textContent.innerText;

    // Update content when typing in textarea
    contentTextarea.addEventListener("input", function () {
      textContent.innerHTML = this.value.replace(/\n/g, "<br>");
    });
  }

  // Set up contenteditable focus and blur events
  textContent.addEventListener("focus", function () {
    if (this.textContent.trim() === "Click here to edit text...") {
      this.innerHTML = "";
    }
  });

  textContent.addEventListener("blur", function () {
    if (this.textContent.trim() === "") {
      this.innerHTML = "<p>Click here to edit text...</p>";
    }
    // Update textarea content
    if (contentTextarea) {
      contentTextarea.value = this.textContent || this.innerText;
    }
  });

  // Sync contenteditable with textarea
  textContent.addEventListener("input", function () {
    if (contentTextarea) {
      contentTextarea.value = this.textContent || this.innerText;
    }
  });

  console.log("Text widget initialized:", textId);
}

// Function to apply text settings
function applyTextSettings(textId, settingsId) {
  const container = document.getElementById(textId);
  const textContent = container.querySelector(".text-content");
  if (!container || !textContent) return;

  // Get all settings values
  const content = document.getElementById(`${settingsId}-content`).value;
  const fontFamily = document.getElementById(`${settingsId}-fontFamily`).value;
  const fontSize = document.getElementById(`${settingsId}-fontSize`).value;
  const textAlign = document.getElementById(`${settingsId}-textAlign`).value;
  const textColor = document.getElementById(`${settingsId}-textColor`).value;
  const bgcolor = document.getElementById(`${settingsId}-bgcolor`).value;
  const lineHeight = document.getElementById(`${settingsId}-lineHeight`).value;
  const bold = document.getElementById(`${settingsId}-bold`).checked;
  const italic = document.getElementById(`${settingsId}-italic`).checked;
  const underline = document.getElementById(`${settingsId}-underline`).checked;
  const showBorder = document.getElementById(`${settingsId}-border`).checked;
  const borderRadius = document.getElementById(`${settingsId}-radius`).value;
  const padding = document.getElementById(`${settingsId}-padding`).value;

  // Apply content
  if (content.trim()) {
    textContent.innerHTML = content.replace(/\n/g, "<br>");
  }

  // Apply font styles
  textContent.style.fontFamily = fontFamily;
  textContent.style.fontSize = fontSize + "px";
  textContent.style.textAlign = textAlign;
  textContent.style.color = textColor;
  textContent.style.backgroundColor = bgcolor;
  textContent.style.lineHeight = lineHeight;
  textContent.style.padding = padding + "px";
  textContent.style.borderRadius = borderRadius + "px";

  // Apply text formatting
  textContent.style.fontWeight = bold ? "bold" : "normal";
  textContent.style.fontStyle = italic ? "italic" : "normal";
  textContent.style.textDecoration = underline ? "underline" : "none";

  // Apply border
  if (showBorder) {
    textContent.style.border = "1px solid #e0e0e0";
  } else {
    textContent.style.border = "none";
  }

  // Store the settings in the container dataset for persistence
  container.dataset.textSettings = JSON.stringify({
    content,
    fontFamily,
    fontSize,
    textAlign,
    textColor,
    bgcolor,
    lineHeight,
    bold,
    italic,
    underline,
    showBorder,
    borderRadius,
    padding,
  });

  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(
    document.getElementById(settingsId)
  );
  if (offcanvas) {
    offcanvas.hide();
  }
}

// Function to load text settings from stored data
function loadTextSettings(textId, settingsId) {
  const container = document.getElementById(textId);
  if (!container || !container.dataset.textSettings) return;

  try {
    const settings = JSON.parse(container.dataset.textSettings);

    // Load settings into form elements
    document.getElementById(`${settingsId}-content`).value =
      settings.content || "";
    document.getElementById(`${settingsId}-fontFamily`).value =
      settings.fontFamily || "Arial, sans-serif";
    document.getElementById(`${settingsId}-fontSize`).value =
      settings.fontSize || 14;
    document.getElementById(`${settingsId}-textAlign`).value =
      settings.textAlign || "left";
    document.getElementById(`${settingsId}-textColor`).value =
      settings.textColor || "#333333";
    document.getElementById(`${settingsId}-bgcolor`).value =
      settings.bgcolor || "#ffffff";
    document.getElementById(`${settingsId}-lineHeight`).value =
      settings.lineHeight || 1.5;
    document.getElementById(`${settingsId}-bold`).checked =
      settings.bold || false;
    document.getElementById(`${settingsId}-italic`).checked =
      settings.italic || false;
    document.getElementById(`${settingsId}-underline`).checked =
      settings.underline || false;
    document.getElementById(`${settingsId}-border`).checked =
      settings.showBorder !== false;
    document.getElementById(`${settingsId}-radius`).value =
      settings.borderRadius || 4;
    document.getElementById(`${settingsId}-padding`).value =
      settings.padding || 10;

    // Update display values
    document.getElementById(`${settingsId}-fontSize-display`).textContent =
      (settings.fontSize || 14) + "px";
    document.getElementById(`${settingsId}-lineHeight-display`).textContent =
      settings.lineHeight || 1.5;
    document.getElementById(`${settingsId}-radius-display`).textContent =
      (settings.borderRadius || 4) + "px";
    document.getElementById(`${settingsId}-padding-display`).textContent =
      (settings.padding || 10) + "px";
  } catch (error) {
    console.error("Error loading text settings:", error);
  }
}

// Export the functions to global scope
window.addTextWidget = addTextWidget;
window.initTextWidget = initTextWidget;
window.applyTextSettings = applyTextSettings;
window.loadTextSettings = loadTextSettings;
