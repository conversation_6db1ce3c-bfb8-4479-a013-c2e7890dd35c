.skeltonLoader .timeline-item {
    background-color: #f6fcfe;
    border-color: #e5e6e9 #dfe0e4 #d0d1d5;
    border-radius: 0px;
    padding: 9px;
    margin: 0 auto;
    max-width: 100%;
    min-height: auto;
}

@keyframes placeHolderShimmer1 {
    0% {
        background-position: -1054px 0;
    }

    100% {
        background-position: 1054px 0;
    }
}

.skeltonLoader .animated-background {
    -webkit-animation-duration: 2.0s;
    animation-duration: 2.0s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-name: placeHolderShimmer1;
    animation-name: placeHolderShimmer1;
    background: #f7f8f8;
    background: linear-gradient(to right, #92c6d27a 8%, #8dbac461 18%, #92c0ca4d 33%);
    background-size: 1024px 104px;
    height: 302px;
    position: relative;
}

.skeltonLoader .background-masker {
    background: #f6fcfe;
    position: absolute;
}

/* Every thing below this is just positioning */
.skeltonLoader .background-masker.header-top,
.skeltonLoader .background-masker.header-bottom,
.skeltonLoader .background-masker.subheader-bottom {
    top: 0;
    left: 40px;
    right: 0;
    height: 10px;
}

.skeltonLoader .background-masker.header-left,
.skeltonLoader .background-masker.subheader-left,
.skeltonLoader .background-masker.header-right,
.skeltonLoader .background-masker.subheader-right {
    top: 10px;
    left: 40px;
    height: 8px;
    width: 10px;
}

.skeltonLoader .background-masker.header-bottom {
    top: 18px;
    height: 6px;
}

.skeltonLoader .background-masker.subheader-left,
.skeltonLoader .background-masker.subheader-right {
    top: 24px;
    height: 12px;
}

.skeltonLoader .background-masker.header-right,
.skeltonLoader .background-masker.subheader-right {
    width: auto;
    left: 300px;
    right: 0;
}

.skeltonLoader .background-masker.subheader-right {
    left: 100%;
}

.skeltonLoader .background-masker.subheader-bottom {
    top: 30px;
    height: 0px;
}

.skeltonLoader .background-masker.content-top,
.skeltonLoader .background-masker.content-second-line,
.skeltonLoader .background-masker.content-third-line,
.skeltonLoader .background-masker.content-second-end,
.skeltonLoader .background-masker.content-third-end,
.skeltonLoader .background-masker.content-first-end {
    top: 40px;
    left: 0;
    right: 0;
    height: 6px;
}

.skeltonLoader .background-masker.content-top {
    height: 20px;
}

.skeltonLoader .background-masker.content-first-end,
.skeltonLoader .background-masker.content-second-end,
.skeltonLoader .background-masker.content-third-end {
    width: auto;
    left: 380px;
    right: 0;
    top: 60px;
    height: 8px;
}

.skeltonLoader .background-masker.content-second-line {
    top: 68px;
}

.skeltonLoader .background-masker.content-second-end {
    left: 100%;
    top: 74px;
}

.skeltonLoader .background-masker.content-third-line {
    top: 82px;
}

.skeltonLoader .background-masker.content-third-end {
    left: 300px;
    top: 88px;
}
.skeltonLoader {
    position: absolute;
    /* width: 100%; */
    background: #f6fcfe;
    z-index: 0;
    left: 0;
}
.skeltonBackground {
    background: #f6fcfe;
    padding: 10px;
    position: absolute;
}
.skeltonWrapper {
    padding-left: 0;
    padding-right: 0;
    position: relative;
}

