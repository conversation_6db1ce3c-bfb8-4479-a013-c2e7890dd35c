/* Stacked Column Chart Widget Styles */
.stacked-column-chart-widget {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  background-color: white !important;
  border-radius: 0px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #e5e9f0 !important;
}

.stacked-column-chart-widget .widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
  background: #ffffff;
  border-bottom: 1px solid #e5e9f0;
}

.stacked-column-chart-widget .widget-body {
  flex: 1 1 auto !important;
  min-height: 0 !important;
  position: relative !important;
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

.stacked-column-chart-widget .chart-container {
  flex: 1 !important;
  width: 100% !important;
  height: 100% !important;
  min-height: 300px !important;
  position: relative !important;
}

/* Ensure grid stack items can grow based on content */
.grid-stack-item-content {
  overflow: visible !important;
  height: 100% !important;
}

/* Chart Settings Panel */
.chart-settings {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.chart-settings label {
  color: #212529;
  font-weight: 500;
  margin-bottom: 0.375rem;
  font-size: 12px;
}

.chart-settings .form-select {
  border-color: #ced4da;
  border-radius: 6px;
  padding: 0.375rem 0.75rem;
  font-size: 12px;
}

.chart-settings .form-range {
  height: 4px;
  border-radius: 2px;
}

.chart-settings .form-range::-webkit-slider-thumb {
  background: #00b19c;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Override widget-height-fix.css styles */
.stacked-column-chart-widget .chart-container {
  height: 100% !important;
  min-height: 300px !important;
}

/* Dark Theme Adjustments */
.dark-theme .stacked-column-chart-widget {
  background-color: #1a1a1a;
  border-color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark-theme .stacked-column-chart-widget .widget-header {
  border-color: #333;
  background-color: #222;
}

.dark-theme .stacked-column-chart-widget .widget-title {
  color: #fff;
}
