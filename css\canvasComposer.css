/* Canvas Composer Styles - GridStack Version */
.canvas-composer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Ensure modals and backdrops appear above Canvas Studio */
.modal.show {
  z-index: 15000 !important;
}

.modal-backdrop.show {
  z-index: 14000 !important;
}

/* <PERSON>vas Header */
.canvas-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.canvas-title {
  margin: 0;
  color: #2d3748;
  font-weight: 600;
  font-size: 1.5rem;
}

.canvas-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.canvas-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  font-size: 0.875rem;
}

.canvas-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.canvas-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.canvas-btn-secondary {
  background: white;
  color: #4a5568;
  border: 2px solid #e2e8f0;
}

.canvas-btn-secondary:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

/* Canvas Main Area */
.canvas-main {
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
}

/* Canvas Workspace with GridStack */
.canvas-workspace {
  flex: 1;
  background: #f8fafc;
  position: relative;
  overflow: auto;
  padding: 2rem;
}

.canvas-grid {
  min-height: 100%;
  background: transparent;
}

/* GridStack Widget Styling */
.grid-stack-item {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.grid-stack-item:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.grid-stack-item.ui-draggable-dragging {
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  transform: rotate(2deg);
  border-color: #667eea;
  z-index: 1000;
}

.grid-stack-item-content {
  padding: 1rem;
  height: 100%;
  border-radius: 10px;
  background: white;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Widget Headers in GridStack */
.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f1f5f9;
}

.widget-title {
  font-weight: 600;
  color: #2d3748;
  font-size: 1.1rem;
  margin: 0;
}

.widget-controls {
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  position: relative;
  z-index: 10;
}

.grid-stack-item:hover .widget-controls {
  opacity: 1;
}

/* Override dashboard-style.css button resets for Canvas Composer */
.canvas-composer-overlay .widget-control-btn,
.grid-stack-item .widget-control-btn {
  width: 28px !important;
  height: 28px !important;
  border: none !important;
  background: #f8fafc !important;
  border-radius: 6px !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
  color: #64748b !important;
  position: relative !important;
  z-index: 15 !important;
  pointer-events: auto !important;
}

.canvas-composer-overlay .widget-control-btn:hover,
.grid-stack-item .widget-control-btn:hover {
  background: #e2e8f0 !important;
  color: #475569 !important;
  transform: scale(1.05) !important;
}

.canvas-composer-overlay .widget-control-btn.delete:hover,
.grid-stack-item .widget-control-btn.delete:hover {
  background: #fee2e2 !important;
  color: #dc2626 !important;
}

/* Ensure icons inside control buttons are visible */
.canvas-composer-overlay .widget-control-btn i,
.grid-stack-item .widget-control-btn i {
  pointer-events: none !important;
  font-size: 14px !important;
  color: inherit !important;
}

/* Widget Content Area */
.widget-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* Chart containers */
.chart-container {
  width: 100%;
  height: 100%;
  min-height: 250px;
}

/* KPI Widget Styling */
.kpi-widget {
  text-align: center;
  padding: 1rem;
}

.kpi-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.kpi-label {
  font-size: 1rem;
  color: #64748b;
  font-weight: 500;
}

/* Floating Widget Palette */
.floating-widget-palette {
  position: absolute;
  top: 2rem;
  right: 2rem;
  width: 300px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  z-index: 1000;
  transition: all 0.3s ease;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.palette-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 16px 16px 0 0;
}

.palette-title {
  margin: 0;
  font-weight: 600;
  font-size: 1.1rem;
}

.palette-toggle {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.palette-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
}

.palette-content {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

.palette-content.collapsed {
  display: none;
}

/* Widget Categories */
.widget-category {
  margin-bottom: 2rem;
}

.widget-category:last-child {
  margin-bottom: 0;
}

.category-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
  font-size: 1rem;
}

.category-title i {
  font-size: 1.2rem;
  color: #667eea;
}

.widget-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

/* Draggable Widgets */
.draggable-widget {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.draggable-widget::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(102, 126, 234, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.draggable-widget:hover::before {
  left: 100%;
}

.draggable-widget:hover {
  background: white;
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.draggable-widget:active {
  transform: translateY(0);
}

.widget-icon {
  font-size: 1.8rem;
  color: #667eea;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 1;
}

.widget-name {
  font-weight: 500;
  color: #2d3748;
  font-size: 0.875rem;
  position: relative;
  z-index: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .canvas-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .canvas-controls {
    width: 100%;
    justify-content: center;
  }

  .floating-widget-palette {
    position: fixed;
    top: auto;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    max-height: 50vh;
    border-radius: 16px 16px 0 0;
  }

  .canvas-workspace {
    padding: 1rem;
  }

  .widget-items {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
}

/* Canvas Toolbar */
.canvas-toolbar {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 0.75rem;
  display: flex;
  gap: 0.5rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
}

.toolbar-btn {
  background: transparent;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  color: #64748b;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
}

.toolbar-btn:hover {
  background: #f1f5f9;
  color: #475569;
}

.toolbar-btn.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.toolbar-btn i {
  font-size: 1rem;
}

/* Canvas Zoom Controls */
.canvas-zoom-controls {
  position: absolute;
  bottom: 2rem;
  right: 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
}

.zoom-btn {
  background: transparent;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-btn:hover {
  background: #f1f5f9;
  color: #475569;
}

.zoom-btn i {
  font-size: 1.2rem;
}

/* Tab assignment mode styles */
.grid-stack-item.tab-assignment-mode {
  transition: all 0.2s ease;
  border: 2px dashed #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.grid-stack-item.tab-assignment-mode:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.grid-stack-item.dragging-for-tab-assignment {
  opacity: 0.7 !important;
  transform: rotate(2deg) scale(1.05);
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.tab-drop-zone {
  transition: all 0.3s ease;
  position: relative;
}

.tab-drop-zone:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.tab-assignment-overlay {
  backdrop-filter: blur(4px);
}

/* Enhanced toast styles */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 20000;
}

.canvas-toast {
  min-width: 300px;
  margin-bottom: 10px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.canvas-toast.success {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.canvas-toast.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.canvas-toast.info {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

/* Toast animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}
