/* Inline Editing Styles for Widget Titles */

/* Editable title styling */
.editable-title {
  cursor: text;
  position: relative;
}

.editable-title:hover::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: var(--ocean-teal, #00b19c);
}

/* Input field styling during editing */
.title-edit-input {
  background: transparent;
  border: none;
  border-bottom: 1px solid var(--ocean-teal, #00b19c);
  outline: none;
  padding: 0;
  margin: 0;
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  font-family: inherit;
  width: calc(100% - 24px); /* Adjust for icon */
}

.title-edit-input:focus {
  border-bottom: 2px solid var(--ocean-teal, #00b19c);
}

/* Dark theme support */
.dark-theme .editable-title:hover::after {
  background-color: var(--ocean-teal-light, #cce9e6);
}

.dark-theme .title-edit-input {
  background: transparent;
  border-bottom: 1px solid var(--ocean-teal-light, #cce9e6);
  color: #fff;
}

.dark-theme .title-edit-input:focus {
  border-bottom: 2px solid var(--ocean-teal-light, #cce9e6);
}
