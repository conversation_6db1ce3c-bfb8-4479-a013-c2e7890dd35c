/* Specific fixes for stacked column chart height issues */

/* Override widget-height-fix.css styles */
.stacked-column-chart-widget {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.stacked-column-chart-widget .widget-body {
  flex: 1 1 auto !important;
  min-height: 0 !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.stacked-column-chart-widget .chart-container {
  flex: 1 !important;
  height: 100% !important;
  min-height: 300px !important;
  position: relative !important;
}

/* Ensure grid stack items containing stacked column charts have proper height */
.grid-stack-item-content:has(.stacked-column-chart-widget) {
  height: 100% !important;
  overflow: visible !important;
}
