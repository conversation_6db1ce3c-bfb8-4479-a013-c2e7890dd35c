/**
 * Price Chart Widget Implementation
 * Advanced charting widget for price trends, forecasting, and technical analysis
 */

// Global price chart counter
let priceChartCounter = 0;

/**
 * Wait for AmCharts dependencies to load
 */
function waitForAmCharts() {
  return new Promise((resolve, reject) => {
    function checkOtherDependencies() {
      return (
        typeof window.am5 !== "undefined" &&
        typeof window.am5xy !== "undefined" &&
        typeof window.am5stock !== "undefined"
      );
    }

    function attemptResolve() {
      if (checkOtherDependencies()) {
        console.log("AmCharts dependencies available");
        resolve();
      } else {
        console.log("AmCharts dependencies not ready, checking again in 100ms");
        setTimeout(attemptResolve, 100);
      }
    }

    // Start checking
    attemptResolve();

    // Set a maximum wait time
    setTimeout(() => {
      if (!checkOtherDependencies()) {
        reject(
          new Error("AmCharts dependencies failed to load within timeout")
        );
      }
    }, 10000);
  });
}

/**
 * Add a Price Chart Widget to the grid
 */
async function addPriceChartWidget() {
  try {
    await waitForAmCharts();
    console.log("AmCharts dependencies loaded successfully");

    const chartId = `price-chart-${++priceChartCounter}`;

    const widget = grid.addWidget({
      x: 0,
      y: 0,
      w: 8,
      h: 6,
      content: createPriceChartHtml(chartId),
    });

    // Initialize the chart
    setTimeout(async () => {
      try {
        await initializePriceChart(chartId);
        console.log("Price chart initialized successfully");
      } catch (error) {
        console.error("Error initializing price chart:", error);
        showChartError(chartId, error.message);
      }
    }, 100);

    return widget;
  } catch (error) {
    console.error("Failed to load AmCharts dependencies:", error);
    return grid.addWidget({
      x: 0,
      y: 0,
      w: 8,
      h: 6,
      content: `
        <div class="widget price-chart-widget">
          <div class="alert alert-danger">
            Failed to load chart dependencies. Please try again later.
            <br>
            <small class="text-muted">${error.message}</small>
          </div>
        </div>
      `,
    });
  }
}

/**
 * Create Price Chart widget HTML structure
 */
function createPriceChartHtml(chartId) {
  return `
    <div class="widget price-chart-widget" id="${chartId}-container">
      <div class="widget-header">
        <div class="widget-title">
          <i class="las la-chart-line"></i>
          <span class="widget-title-text">Price Forecast Chart</span>
        </div>
        <div class="widget-actions">
          <button class="widget-action-btn" onclick="togglePriceChartSettings('${chartId}')" title="Settings">
            <i class="las la-cog"></i>
          </button>
          <button class="widget-action-btn" onclick="refreshPriceChart('${chartId}')" title="Refresh">
            <i class="las la-sync-alt"></i>
          </button>
          <button class="widget-action-btn" onclick="toggleFullscreen('${chartId}')" title="Fullscreen">
            <i class="las la-expand-arrows-alt"></i>
          </button>
          <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      
      <div class="widget-content price-chart-content">
        <div class="chart-toolbar">
          <div class="chart-controls">
            <select class="chart-type-select" onchange="changePriceChartType('${chartId}', this.value)">
              <option value="line">Line Chart</option>
              <option value="candlestick">Candlestick</option>
              <option value="ohlc">OHLC</option>
              <option value="area">Area Chart</option>
            </select>
            
            <select class="chart-period-select" onchange="changePriceChartPeriod('${chartId}', this.value)">
              <option value="1D">1 Day</option>
              <option value="1W" selected>1 Week</option>
              <option value="1M">1 Month</option>
              <option value="3M">3 Months</option>
              <option value="6M">6 Months</option>
              <option value="1Y">1 Year</option>
            </select>
            
            <div class="chart-toggles">
              <label class="toggle-label">
                <input type="checkbox" id="${chartId}-forecast" onchange="toggleForecast('${chartId}', this.checked)" checked>
                <span>Show Forecast</span>
              </label>
              
              <label class="toggle-label">
                <input type="checkbox" id="${chartId}-volume" onchange="toggleVolume('${chartId}', this.checked)">
                <span>Show Volume</span>
              </label>
              
              <label class="toggle-label">
                <input type="checkbox" id="${chartId}-indicators" onchange="toggleIndicators('${chartId}', this.checked)">
                <span>Technical Indicators</span>
              </label>
            </div>
          </div>
          
          <div class="chart-info">
            <div class="price-info" id="${chartId}-price-info">
              <span class="current-price">$2,442.40</span>
              <span class="price-change positive">+$62.75 (+2.57%)</span>
            </div>
          </div>
        </div>
        
        <div class="chart-container" id="${chartId}"></div>
        
        <div class="chart-legend" id="${chartId}-legend">
          <div class="legend-item">
            <div class="legend-color" style="background: #3b82f6;"></div>
            <span>Historical Price</span>
          </div>
          <div class="legend-item forecast-legend">
            <div class="legend-color" style="background: #f59e0b;"></div>
            <span>Price Forecast</span>
          </div>
          <div class="legend-item confidence-legend">
            <div class="legend-color" style="background: rgba(245, 158, 11, 0.3);"></div>
            <span>Confidence Interval</span>
          </div>
        </div>
      </div>
      
      <div class="price-chart-settings" id="${chartId}-settings" style="display: none;">
        <div class="settings-section">
          <label>Chart Title:</label>
          <input type="text" id="${chartId}-title" value="Price Forecast Chart" class="form-control">
        </div>
        
        <div class="settings-section">
          <label>Commodity/Asset:</label>
          <select id="${chartId}-asset" class="form-control">
            <option value="wheat">Wheat</option>
            <option value="corn">Corn</option>
            <option value="soybeans">Soybeans</option>
            <option value="rice">Rice</option>
            <option value="coffee">Coffee</option>
            <option value="sugar">Sugar</option>
            <option value="cotton">Cotton</option>
            <option value="gold">Gold</option>
            <option value="oil">Crude Oil</option>
          </select>
        </div>
        
        <div class="settings-section">
          <label>Forecast Model:</label>
          <select id="${chartId}-model" class="form-control">
            <option value="linear">Linear Regression</option>
            <option value="arima">ARIMA</option>
            <option value="lstm">LSTM Neural Network</option>
            <option value="prophet">Facebook Prophet</option>
            <option value="ensemble">Ensemble Model</option>
          </select>
        </div>
        
        <div class="settings-section">
          <label>Forecast Period:</label>
          <select id="${chartId}-forecast-period" class="form-control">
            <option value="7">7 Days</option>
            <option value="14">14 Days</option>
            <option value="30" selected>30 Days</option>
            <option value="90">90 Days</option>
            <option value="180">6 Months</option>
            <option value="365">1 Year</option>
          </select>
        </div>
        
        <div class="settings-section">
          <label>Confidence Level:</label>
          <select id="${chartId}-confidence" class="form-control">
            <option value="80">80%</option>
            <option value="90">90%</option>
            <option value="95" selected>95%</option>
            <option value="99">99%</option>
          </select>
        </div>
        
        <div class="settings-actions">
          <button class="btn btn-primary" onclick="applyPriceChartSettings('${chartId}')">Apply</button>
          <button class="btn btn-secondary" onclick="togglePriceChartSettings('${chartId}')">Cancel</button>
        </div>
      </div>
    </div>
  `;
}

/**
 * Initialize Price Chart with AmCharts
 */
async function initializePriceChart(chartId) {
  const root = am5.Root.new(chartId);

  // Set themes
  root.setThemes([
    am5themes_Animated.new(root),
    am5themes_Responsive.new(root),
  ]);

  // Create chart
  const chart = root.container.children.push(
    am5xy.XYChart.new(root, {
      panX: true,
      panY: true,
      wheelX: "panX",
      wheelY: "zoomX",
      pinchZoomX: true,
      paddingLeft: 0,
    })
  );

  // Create axes
  const xAxis = chart.xAxes.push(
    am5xy.DateAxis.new(root, {
      maxZoomCount: 50,
      baseInterval: { timeUnit: "day", count: 1 },
      renderer: am5xy.AxisRendererX.new(root, {
        minorGridEnabled: true,
        minGridDistance: 60,
      }),
      tooltip: am5.Tooltip.new(root, {}),
    })
  );

  const yAxis = chart.yAxes.push(
    am5xy.ValueAxis.new(root, {
      renderer: am5xy.AxisRendererY.new(root, {
        pan: "zoom",
      }),
    })
  );

  // Generate sample data
  const data = generatePriceData();

  // Create main price series
  const priceSeries = chart.series.push(
    am5xy.LineSeries.new(root, {
      name: "Price",
      xAxis: xAxis,
      yAxis: yAxis,
      valueYField: "price",
      valueXField: "date",
      stroke: am5.color("#3b82f6"),
      fill: am5.color("#3b82f6"),
      tooltip: am5.Tooltip.new(root, {
        labelText: "Price: ${valueY}",
      }),
    })
  );

  priceSeries.data.setAll(data.historical);

  // Create forecast series
  const forecastSeries = chart.series.push(
    am5xy.LineSeries.new(root, {
      name: "Forecast",
      xAxis: xAxis,
      yAxis: yAxis,
      valueYField: "forecast",
      valueXField: "date",
      stroke: am5.color("#f59e0b"),
      strokeDasharray: [5, 5],
      tooltip: am5.Tooltip.new(root, {
        labelText: "Forecast: ${valueY}",
      }),
    })
  );

  forecastSeries.data.setAll(data.forecast);

  // Create confidence interval series
  const confidenceSeries = chart.series.push(
    am5xy.LineSeries.new(root, {
      name: "Confidence Upper",
      xAxis: xAxis,
      yAxis: yAxis,
      valueYField: "upper",
      valueXField: "date",
      stroke: am5.color("rgba(245, 158, 11, 0.3)"),
      fill: am5.color("rgba(245, 158, 11, 0.1)"),
    })
  );

  const confidenceLowerSeries = chart.series.push(
    am5xy.LineSeries.new(root, {
      name: "Confidence Lower",
      xAxis: xAxis,
      yAxis: yAxis,
      valueYField: "lower",
      valueXField: "date",
      stroke: am5.color("rgba(245, 158, 11, 0.3)"),
      fill: am5.color("rgba(245, 158, 11, 0.1)"),
    })
  );

  confidenceSeries.data.setAll(data.forecast);
  confidenceLowerSeries.data.setAll(data.forecast);

  // Add cursor
  const cursor = chart.set(
    "cursor",
    am5xy.XYCursor.new(root, {
      behavior: "zoomX",
    })
  );
  cursor.lineY.set("visible", false);

  // Add scrollbar
  chart.set(
    "scrollbarX",
    am5.Scrollbar.new(root, {
      orientation: "horizontal",
    })
  );

  // Store chart reference
  document.getElementById(chartId).chart = chart;
  document.getElementById(chartId).root = root;

  // Update price info
  updatePriceInfo(chartId, data.historical[data.historical.length - 1]);
}

/**
 * Generate sample price data with forecast
 */
function generatePriceData() {
  const historical = [];
  const forecast = [];
  const basePrice = 2380;
  let currentPrice = basePrice;
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - 30);

  // Generate historical data
  for (let i = 0; i < 30; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);

    // Add some realistic price movement
    const change = (Math.random() - 0.5) * 40;
    currentPrice += change;
    currentPrice = Math.max(currentPrice, basePrice * 0.8); // Floor
    currentPrice = Math.min(currentPrice, basePrice * 1.3); // Ceiling

    historical.push({
      date: date.getTime(),
      price: Math.round(currentPrice * 100) / 100,
    });
  }

  // Generate forecast data
  const forecastStart = new Date();
  const trend = 0.5; // Slight upward trend

  for (let i = 0; i < 30; i++) {
    const date = new Date(forecastStart);
    date.setDate(date.getDate() + i);

    // Simple linear forecast with some randomness
    const forecastPrice = currentPrice + trend * i + (Math.random() - 0.5) * 20;
    const confidence = 50; // Confidence interval

    forecast.push({
      date: date.getTime(),
      forecast: Math.round(forecastPrice * 100) / 100,
      upper: Math.round((forecastPrice + confidence) * 100) / 100,
      lower: Math.round((forecastPrice - confidence) * 100) / 100,
    });
  }

  return { historical, forecast };
}

/**
 * Update price info display
 */
function updatePriceInfo(chartId, latestData) {
  const priceInfo = document.getElementById(`${chartId}-price-info`);
  if (priceInfo && latestData) {
    const change = Math.random() > 0.5 ? 1 : -1;
    const changeValue = (Math.random() * 100).toFixed(2);
    const changePercent = (Math.random() * 5).toFixed(2);

    priceInfo.innerHTML = `
      <span class="current-price">$${latestData.price}</span>
      <span class="price-change ${change > 0 ? "positive" : "negative"}">
        ${change > 0 ? "+" : "-"}$${changeValue} (${
      change > 0 ? "+" : "-"
    }${changePercent}%)
      </span>
    `;
  }
}

/**
 * Toggle Price Chart settings panel
 */
function togglePriceChartSettings(chartId) {
  const settingsPanel = document.getElementById(`${chartId}-settings`);
  const chartContent = document.querySelector(
    `#${chartId}-container .price-chart-content`
  );

  if (settingsPanel.style.display === "none") {
    settingsPanel.style.display = "block";
    chartContent.style.display = "none";
  } else {
    settingsPanel.style.display = "none";
    chartContent.style.display = "block";
  }
}

/**
 * Apply Price Chart settings
 */
function applyPriceChartSettings(chartId) {
  const title = document.getElementById(`${chartId}-title`).value;
  const asset = document.getElementById(`${chartId}-asset`).value;
  const model = document.getElementById(`${chartId}-model`).value;

  // Update widget title
  const widget = document.getElementById(`${chartId}-container`);
  widget.querySelector(".widget-title-text").textContent = title;

  // Regenerate chart with new settings
  refreshPriceChart(chartId);

  // Hide settings panel
  togglePriceChartSettings(chartId);
}

/**
 * Refresh Price Chart data
 */
function refreshPriceChart(chartId) {
  const chart = document.getElementById(chartId)?.chart;
  if (chart) {
    // Generate new data
    const data = generatePriceData();

    // Update series data
    chart.series.getIndex(0).data.setAll(data.historical);
    chart.series.getIndex(1).data.setAll(data.forecast);
    chart.series.getIndex(2).data.setAll(data.forecast);
    chart.series.getIndex(3).data.setAll(data.forecast);

    // Update price info
    updatePriceInfo(chartId, data.historical[data.historical.length - 1]);
  }
}

/**
 * Change chart type
 */
function changePriceChartType(chartId, type) {
  console.log(`Changing chart type to: ${type}`);
  // Implementation for different chart types would go here
}

/**
 * Change chart period
 */
function changePriceChartPeriod(chartId, period) {
  console.log(`Changing chart period to: ${period}`);
  // Implementation for different time periods would go here
}

/**
 * Toggle forecast visibility
 */
function toggleForecast(chartId, show) {
  const chart = document.getElementById(chartId)?.chart;
  if (chart) {
    const forecastSeries = chart.series.getIndex(1);
    const confidenceUpper = chart.series.getIndex(2);
    const confidenceLower = chart.series.getIndex(3);

    forecastSeries.set("visible", show);
    confidenceUpper.set("visible", show);
    confidenceLower.set("visible", show);

    // Toggle legend visibility
    const legendItems = document.querySelectorAll(
      `#${chartId}-legend .forecast-legend, #${chartId}-legend .confidence-legend`
    );
    legendItems.forEach((item) => {
      item.style.display = show ? "flex" : "none";
    });
  }
}

/**
 * Toggle volume display
 */
function toggleVolume(chartId, show) {
  console.log(`Toggle volume: ${show}`);
  // Implementation for volume display would go here
}

/**
 * Toggle technical indicators
 */
function toggleIndicators(chartId, show) {
  console.log(`Toggle technical indicators: ${show}`);
  // Implementation for technical indicators would go here
}

/**
 * Toggle fullscreen mode
 */
function toggleFullscreen(chartId) {
  const container = document.getElementById(`${chartId}-container`);
  if (container.requestFullscreen) {
    container.requestFullscreen();
  }
}

/**
 * Show chart error
 */
function showChartError(chartId, message) {
  const container = document.getElementById(chartId);
  if (container) {
    container.innerHTML = `
      <div class="alert alert-danger">
        Failed to initialize chart: ${message}
        <br>
        <button class="btn btn-sm btn-primary mt-2" onclick="initializePriceChart('${chartId}')">
          Retry
        </button>
      </div>
    `;
  }
}
