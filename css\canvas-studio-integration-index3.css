/**
 * Canvas Studio - Premium Professional Design System
 * Inspired by <PERSON><PERSON>, Linear, Notion - Modern Enterprise Interface
 */

/* ===== CANVAS STUDIO - MODERN DESIGN SYSTEM ===== */

/* CSS Custom Properties - Design Tokens */
:root {
  /* Primary Colors - Modern Indigo Palette */
  --primary-50: #eef2ff;
  --primary-100: #e0e7ff;
  --primary-200: #c7d2fe;
  --primary-300: #a5b4fc;
  --primary-400: #818cf8;
  --primary-500: #6366f1;
  --primary-600: #4f46e5;
  --primary-700: #4338ca;
  --primary-800: #3730a3;
  --primary-900: #312e81;

  /* Neutral Colors - Clean Grays */
  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;

  /* Semantic Colors */
  --success-500: #10b981;
  --success-600: #059669;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: var(--neutral-50);
  --bg-tertiary: var(--neutral-100);
  --bg-accent: var(--primary-50);

  /* Text Colors */
  --text-primary: var(--neutral-900);
  --text-secondary: var(--neutral-600);
  --text-tertiary: var(--neutral-500);
  --text-accent: var(--primary-600);

  /* Border Colors */
  --border-light: var(--neutral-200);
  --border-medium: var(--neutral-300);
  --border-accent: var(--primary-200);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Typography */
  --font-sans: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Canvas Studio Integration - Theme-Matched Design */
  --ocean-teal: #00b19c;
  --denali-blue: #02104f;
  --light-teal: #e6f7f5;
  --dark-teal: #008a7a;
  --text-dark: #333333;
  --text-light: #666666;
  --bg-light: #f8f9fa;
  --border-color: #e9ecef;
  --white: #ffffff;
  --danger: #dc3545;
  --success: #28a745;
  --warning: #ffc107;
}

/* ===== MODAL OVERRIDES ===== */
#canvasStudioModal.modal {
  --bs-modal-zindex: 1055;
}

#canvasStudioModal .modal-dialog {
  max-width: 100vw;
  margin: 0;
  height: 100vh;
}

#canvasStudioModal .modal-content {
  height: 100vh;
  border: none;
  border-radius: 0;
  background: var(--bg-secondary);
}

#canvasStudioModal .modal-header {
  background: linear-gradient(
    135deg,
    var(--primary-600) 0%,
    var(--primary-700) 100%
  );
  color: white;
  border-bottom: none;
  padding: var(--spacing-lg) var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

#canvasStudioModal .modal-title {
  font-family: var(--font-sans);
  font-weight: var(--font-weight-semibold);
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

#canvasStudioModal .btn-close {
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  opacity: 1;
  width: 32px;
  height: 32px;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;
}

#canvasStudioModal .btn-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* ===== MAIN WORKSPACE LAYOUT ===== */
.canvas-studio-workspace {
  display: grid;
  grid-template-columns: 280px 1fr 350px;
  grid-template-rows: 1fr;
  height: calc(100vh - 120px);
  gap: 0;
  max-width: 100vw;
  width: 100%;
  overflow: hidden;
  background: #f8fafc;
}

/* ===== LEFT SIDEBAR - SECTIONS ===== */
.canvas-studio-sidebar {
  background: #ffffff;
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 280px;
  min-width: 280px;
  max-width: 280px;
}

.sidebar-header {
  padding: 1.5rem;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  border-bottom: none;
}

.sidebar-header h6 {
  margin: 0 0 1rem 0;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: rgba(255, 255, 255, 0.9);
}

.container-actions .btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.container-actions .btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
}

.containers-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.container-item {
  background: white;
  border: 1px solid rgba(226, 232, 240, 0.6);
  border-radius: 1rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.container-item:hover {
  border-color: #a5b4fc;
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.15);
  transform: translateY(-3px);
}

.container-item.selected {
  border-color: #6366f1;
  background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.25);
}

.container-item.selected::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  border-radius: 0 0.5rem 0.5rem 0;
}

/* Clear All Button */
.sidebar-content .btn-outline-danger {
  background: transparent;
  border: 1px solid #ef4444;
  color: #ef4444;
  border-radius: 0.75rem;
  padding: 0.75rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-top: 1.5rem;
}

.sidebar-content .btn-outline-danger:hover {
  background: #ef4444;
  color: white;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
  transform: translateY(-2px);
}

/* ===== MAIN CANVAS AREA ===== */
.canvas-studio-main {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  overflow: hidden;
}

.canvas-header {
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.canvas-title h6 {
  margin: 0;
  font-weight: 600;
  font-size: 1.25rem;
  color: #1e293b;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.canvas-subtitle {
  color: #64748b;
  font-size: 0.875rem;
  margin-top: 0.25rem;
  font-weight: 400;
}

.canvas-area {
  flex: 1;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.canvas-content {
  height: 100%;
  position: relative;
}

.canvas-empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  padding: 3rem;
  background: white;
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(226, 232, 240, 0.6);
  max-width: 400px;
  width: 90%;
}

.canvas-empty-state i {
  font-size: 3rem;
  color: #6366f1;
  margin-bottom: 1.5rem;
  display: block;
}

.canvas-empty-state h4 {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.canvas-empty-state p {
  color: #64748b;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.canvas-empty-state .btn-primary {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  border: none;
  padding: 0.875rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
  transition: all 0.2s ease;
}

.canvas-empty-state .btn-primary:hover {
  background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.6);
  transform: translateY(-2px);
}

/* ===== RIGHT SIDEBAR - WIDGET LIBRARY ===== */
.canvas-studio-widgets {
  background: #ffffff;
  border-left: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 350px;
  min-width: 350px;
  max-width: 350px;
}

.widgets-header {
  padding: 2rem 1.5rem;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  border-bottom: none;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.widgets-header h4 {
  margin: 0;
  font-size: 1.375rem;
  font-weight: 700;
  color: white;
  letter-spacing: -0.025em;
  text-align: center;
}

.widgets-search {
  padding: 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.widgets-search input {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid #e5e7eb;
  border-radius: 0.75rem;
  font-size: 0.95rem;
  background: white;
  color: #374151;
  transition: all 0.3s ease;
  font-weight: 500;
}

.widgets-search input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  background: #fefefe;
}

.widgets-search input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

/* ===== WIDGET LIBRARY STYLING ===== */
.widgets-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  background: white;
  height: 0;
}

.widget-category {
  margin-bottom: 0;
  border-bottom: 1px solid #f1f5f9;
  display: block;
}

.widget-category:last-child {
  border-bottom: none;
}

.widget-category-header {
  padding: 1.25rem 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: block;
  width: 100%;
}

.widget-category-header:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.category-header-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.category-icon {
  color: #3b82f6;
  font-size: 1.25rem;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.category-info {
  flex: 1;
  min-width: 0;
}

.category-name {
  font-weight: 700;
  font-size: 0.95rem;
  color: #1f2937;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.075em;
}

.category-description {
  font-size: 0.8rem;
  color: #6b7280;
  line-height: 1.4;
  font-weight: 400;
}

.category-count {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  font-size: 0.8rem;
  font-weight: 700;
  padding: 0.375rem 0.75rem;
  border-radius: 0.5rem;
  min-width: 24px;
  text-align: center;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.widget-category-content {
  background: white;
  display: block;
  overflow: hidden;
  transition: all 0.3s ease;
  padding: 1rem 1.5rem;
}

/* Collapsed state */
.widget-category.collapsed .widget-category-content {
  display: none;
}

/* Widget Grid Container */
.widgets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 0.75rem;
  width: 100%;
}

.widget-item,
.widget-library-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid #f1f5f9;
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  user-select: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-height: 100px;
  justify-content: center;
  gap: 0.5rem;
}

.widget-item:hover,
.widget-library-item:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.15);
}

.widget-item:active,
.widget-library-item:active,
.widget-library-item.clicked {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-color: #2563eb;
  transform: translateY(0);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);
}

/* Remove the old "Click to Add" badge */
.widget-item::before,
.widget-library-item::before {
  display: none;
}

/* Add a subtle hover overlay */
.widget-item::after,
.widget-library-item::after {
  content: "Click to Add";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.9) 0%,
    rgba(37, 99, 235, 0.9) 100%
  );
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0;
  transition: all 0.3s ease;
  border-radius: 0.75rem;
}

.widget-item:hover::after,
.widget-library-item:hover::after {
  opacity: 1;
}

.widget-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.widget-icon {
  color: #3b82f6;
  font-size: 1.75rem;
  width: auto;
  text-align: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
  margin-bottom: 0.25rem;
}

.widget-item:hover .widget-icon,
.widget-library-item:hover .widget-icon {
  color: #2563eb;
  transform: scale(1.1);
}

.widget-info {
  flex: 1;
  min-width: 0;
  width: 100%;
}

.widget-name {
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  font-size: 0.8rem;
  line-height: 1.2;
  text-align: center;
}

.widget-description {
  display: none;
}

/* Enhanced focus states for accessibility */
.widget-item:focus,
.widget-library-item:focus {
  outline: 3px solid #3b82f6;
  outline-offset: 2px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Special styling for different widget types */
.widget-item[data-widget-type="tab-container"] {
  border-color: #10b981;
}

.widget-item[data-widget-type="tab-container"] .widget-icon {
  color: #10b981;
}

.widget-item[data-widget-type="tab-container"]:hover {
  border-color: #059669;
  box-shadow: 0 8px 16px rgba(16, 185, 129, 0.15);
}

.widget-item[data-widget-type="tab-container"]:hover .widget-icon {
  color: #059669;
}

/* Chart widgets */
.widget-item[data-category="Charts"] {
  border-color: #f59e0b;
}

.widget-item[data-category="Charts"] .widget-icon {
  color: #f59e0b;
}

.widget-item[data-category="Charts"]:hover {
  border-color: #d97706;
  box-shadow: 0 8px 16px rgba(245, 158, 11, 0.15);
}

.widget-item[data-category="Charts"]:hover .widget-icon {
  color: #d97706;
}

/* Data widgets */
.widget-item[data-category="Data"] {
  border-color: #8b5cf6;
}

.widget-item[data-category="Data"] .widget-icon {
  color: #8b5cf6;
}

.widget-item[data-category="Data"]:hover {
  border-color: #7c3aed;
  box-shadow: 0 8px 16px rgba(139, 92, 246, 0.15);
}

.widget-item[data-category="Data"]:hover .widget-icon {
  color: #7c3aed;
}

/* Metrics widgets */
.widget-item[data-category="Metrics"] {
  border-color: #ef4444;
}

.widget-item[data-category="Metrics"] .widget-icon {
  color: #ef4444;
}

.widget-item[data-category="Metrics"]:hover {
  border-color: #dc2626;
  box-shadow: 0 8px 16px rgba(239, 68, 68, 0.15);
}

.widget-item[data-category="Metrics"]:hover .widget-icon {
  color: #dc2626;
}

/* Content widgets */
.widget-item[data-category="Content"] {
  border-color: #06b6d4;
}

.widget-item[data-category="Content"] .widget-icon {
  color: #06b6d4;
}

.widget-item[data-category="Content"]:hover {
  border-color: #0891b2;
  box-shadow: 0 8px 16px rgba(6, 182, 212, 0.15);
}

.widget-item[data-category="Content"]:hover .widget-icon {
  color: #0891b2;
}

/* Media widgets */
.widget-item[data-category="Media"] {
  border-color: #ec4899;
}

.widget-item[data-category="Media"] .widget-icon {
  color: #ec4899;
}

.widget-item[data-category="Media"]:hover {
  border-color: #db2777;
  box-shadow: 0 8px 16px rgba(236, 72, 153, 0.15);
}

.widget-item[data-category="Media"]:hover .widget-icon {
  color: #db2777;
}

/* Location widgets */
.widget-item[data-category="Location"] {
  border-color: #84cc16;
}

.widget-item[data-category="Location"] .widget-icon {
  color: #84cc16;
}

.widget-item[data-category="Location"]:hover {
  border-color: #65a30d;
  box-shadow: 0 8px 16px rgba(132, 204, 22, 0.15);
}

.widget-item[data-category="Location"]:hover .widget-icon {
  color: #65a30d;
}

/* Smooth animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.widget-category {
  animation: slideIn 0.4s ease forwards;
}

/* Custom scrollbar for widget library */
.widgets-content::-webkit-scrollbar {
  width: 8px;
}

.widgets-content::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 4px;
}

.widgets-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #d1d5db 0%, #9ca3af 100%);
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.widgets-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #9ca3af 0%, #6b7280 100%);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .canvas-studio-workspace {
    grid-template-columns: 260px 1fr 350px;
  }
}

@media (max-width: 992px) {
  .canvas-studio-workspace {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
  }

  .canvas-studio-sidebar,
  .canvas-studio-widgets {
    border: none;
    border-top: 1px solid var(--border-light);
    max-height: 200px;
  }
}

/* ===== SCROLLBAR STYLING ===== */
.sidebar-content::-webkit-scrollbar,
.widget-library::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track,
.widget-library::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

.sidebar-content::-webkit-scrollbar-thumb,
.widget-library::-webkit-scrollbar-thumb {
  background: var(--neutral-300);
  border-radius: var(--radius-sm);
}

.sidebar-content::-webkit-scrollbar-thumb:hover,
.widget-library::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-400);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease forwards;
}

/* ===== ACCESSIBILITY ===== */
*:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Modal Styling */
.canvas-studio-modal .modal-dialog {
  max-width: 95vw;
  height: 90vh;
  margin: 2.5vh auto;
}

.canvas-studio-modal .modal-content {
  height: 100%;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.canvas-studio-modal .modal-body {
  padding: 0;
  height: 100%;
}

/* Main Workspace Layout */
.canvas-studio-workspace {
  display: grid;
  grid-template-columns: 300px 1fr 350px;
  height: 100%;
  background: var(--bg-light);
  gap: 0;
}

/* Sidebar Styling */
.canvas-studio-sidebar {
  background: var(--white);
  border-right: 2px solid var(--border-color);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sidebar-header {
  padding: 20px;
  background: var(--denali-blue);
  color: var(--white);
  border-bottom: 2px solid var(--ocean-teal);
}

.sidebar-header h4 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.sidebar-section {
  border-bottom: 1px solid var(--border-color);
}

.sidebar-section-header {
  padding: 15px 20px;
  background: var(--light-teal);
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
  color: var(--denali-blue);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sidebar-section-content {
  padding: 0;
}

/* Container Items */
.container-item {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--white);
}

.container-item:hover {
  background: var(--light-teal);
}

.container-item.selected {
  background: var(--ocean-teal);
  color: var(--white);
}

.container-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.container-icon {
  color: var(--ocean-teal);
  font-size: 1.1rem;
}

.container-item.selected .container-icon {
  color: var(--white);
}

.container-info {
  flex: 1;
}

.container-name {
  font-weight: 600;
  margin-bottom: 2px;
}

.container-meta {
  font-size: 0.85rem;
  opacity: 0.8;
}

.container-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.container-item:hover .container-actions {
  opacity: 1;
}

/* Layout Groups */
.layout-groups-section {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
}

.layout-group-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 0;
  border-bottom: 1px solid var(--border-color);
}

.layout-group-item:last-child {
  border-bottom: none;
}

.layout-group-icon {
  color: var(--ocean-teal);
}

.layout-group-info {
  flex: 1;
}

.layout-group-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.layout-group-meta {
  font-size: 0.8rem;
  color: var(--text-light);
}

/* Main Canvas Area */
.canvas-studio-main {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--white);
}

.canvas-header {
  padding: 20px;
  background: var(--white);
  border-bottom: 2px solid var(--border-color);
  display: flex;
  justify-content: between;
  align-items: center;
}

.canvas-title {
  margin: 0;
  color: var(--denali-blue);
  font-size: 1.4rem;
  font-weight: 600;
}

.canvas-actions {
  display: flex;
  gap: 10px;
}

.canvas-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: var(--bg-light);
}

.canvas-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-light);
  text-align: center;
}

.canvas-empty-state i {
  font-size: 4rem;
  margin-bottom: 20px;
  color: var(--ocean-teal);
}

.canvas-empty-state h3 {
  margin-bottom: 10px;
  color: var(--denali-blue);
}

/* Widget Library */
.canvas-studio-widgets {
  background: var(--white);
  border-left: 2px solid var(--border-color);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.widgets-header {
  padding: 20px;
  background: var(--denali-blue);
  color: var(--white);
  border-bottom: 2px solid var(--ocean-teal);
}

.widgets-header h4 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.widgets-search {
  padding: 15px 20px;
  background: var(--light-teal);
  border-bottom: 1px solid var(--border-color);
}

.widgets-search input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  background: var(--white);
  font-size: 0.9rem;
}

.widgets-search input:focus {
  outline: none;
  border-color: var(--ocean-teal);
  box-shadow: 0 0 0 2px rgba(0, 177, 156, 0.2);
}

.widgets-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.widget-category {
  border-bottom: 1px solid var(--border-color);
}

.widget-category-header {
  padding: 12px 20px;
  background: var(--light-teal);
  font-weight: 600;
  color: var(--denali-blue);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.widget-item {
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--white);
}

.widget-item:hover {
  background: var(--light-teal);
  transform: translateX(4px);
}

.widget-item:last-child {
  border-bottom: none;
}

.widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--light-teal);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px 8px 0 0;
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.widget-title span {
  font-weight: 500;
  color: var(--denali-blue);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.widget-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Buttons */
.btn-ocean {
  background: var(--ocean-teal);
  border: 1px solid var(--ocean-teal);
  color: var(--white);
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-ocean:hover {
  background: var(--dark-teal);
  border-color: var(--dark-teal);
  color: var(--white);
}

.btn-denali {
  background: var(--denali-blue);
  border: 1px solid var(--denali-blue);
  color: var(--white);
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-denali:hover {
  background: #001a3d;
  border-color: #001a3d;
  color: var(--white);
}

.btn-outline-ocean {
  background: transparent;
  border: 1px solid var(--ocean-teal);
  color: var(--ocean-teal);
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-outline-ocean:hover {
  background: var(--ocean-teal);
  color: var(--white);
}

/* GridStack Styling */
.grid-stack {
  background: var(--white);
  min-height: 400px;
  border: 2px dashed var(--border-color);
  margin: 20px 0;
}

.grid-stack-placeholder {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-light);
}

.grid-stack-placeholder i {
  font-size: 3rem;
  margin-bottom: 15px;
  color: var(--ocean-teal);
}

.grid-stack-item {
  background: var(--white);
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.grid-stack-item-content {
  padding: 15px;
  height: 100%;
}

/* Tab Groups */
.tab-group {
  background: var(--white);
  border: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.tab-nav {
  display: flex;
  background: var(--light-teal);
  border-bottom: 1px solid var(--border-color);
}

.tab-btn {
  padding: 12px 20px;
  background: transparent;
  border: none;
  color: var(--denali-blue);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-right: 1px solid var(--border-color);
}

.tab-btn:hover {
  background: var(--ocean-teal);
  color: var(--white);
}

.tab-btn.active {
  background: var(--ocean-teal);
  color: var(--white);
}

.tab-content {
  padding: 20px;
  min-height: 300px;
}

/* Collapsible Groups */
.collapsible-group {
  background: var(--white);
  border: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.collapsible-header {
  padding: 15px 20px;
  background: var(--light-teal);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: var(--denali-blue);
}

.collapsible-header:hover {
  background: var(--ocean-teal);
  color: var(--white);
}

.collapsible-content {
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

/* Grid Areas */
.grid-area {
  background: var(--white);
  border: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.grid-area-header {
  padding: 15px 20px;
  background: var(--light-teal);
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
  color: var(--denali-blue);
}

.mini-grid {
  display: grid;
  gap: 10px;
  padding: 20px;
}

.mini-grid.cols-2 {
  grid-template-columns: 1fr 1fr;
}

.mini-grid.cols-3 {
  grid-template-columns: 1fr 1fr 1fr;
}

.mini-grid.cols-4 {
  grid-template-columns: 1fr 1fr 1fr 1fr;
}

.mini-widget {
  background: var(--bg-light);
  border: 1px solid var(--border-color);
  padding: 15px;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-size: 0.9rem;
}

.mini-placeholder {
  background: var(--light-teal);
  border: 2px dashed var(--ocean-teal);
  padding: 20px;
  text-align: center;
  color: var(--ocean-teal);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mini-placeholder:hover {
  background: var(--ocean-teal);
  color: var(--white);
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-light);
}

.empty-state i {
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: var(--ocean-teal);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .canvas-studio-workspace {
    grid-template-columns: 250px 1fr 350px;
  }
}

@media (max-width: 992px) {
  .canvas-studio-workspace {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
  }

  .canvas-studio-sidebar,
  .canvas-studio-widgets {
    height: auto;
    max-height: 300px;
  }
}

/* Utility Classes */
.text-ocean {
  color: var(--ocean-teal) !important;
}

.text-denali {
  color: var(--denali-blue) !important;
}

.bg-ocean {
  background-color: var(--ocean-teal) !important;
}

.bg-denali {
  background-color: var(--denali-blue) !important;
}

.border-ocean {
  border-color: var(--ocean-teal) !important;
}

.border-denali {
  border-color: var(--denali-blue) !important;
}

/* ===== COLLAPSIBLE CATEGORY FUNCTIONALITY ===== */
.widget-category.collapsed .category-header-content::after {
  content: "\f054";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  margin-left: auto;
  color: #6b7280;
  font-size: 0.875rem;
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.widget-category:not(.collapsed) .category-header-content::after {
  content: "\f078";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  margin-left: auto;
  color: #6b7280;
  font-size: 0.875rem;
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

/* Widget Preview Styles */
.widget-card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  transition: all 0.2s ease;
  overflow: hidden;
}

.widget-card:hover {
  border-color: var(--ocean-teal);
  box-shadow: 0 4px 12px rgba(32, 178, 170, 0.15);
  transform: translateY(-1px);
}

.widget-card .card-header {
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  padding: 12px 16px;
  min-height: 48px;
}

.widget-card .widget-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1e293b;
  font-weight: 600;
  font-size: 14px;
}

.widget-card .widget-title i {
  font-size: 16px;
  color: var(--ocean-teal);
}

.widget-card .widget-actions {
  display: flex;
  gap: 4px;
}

.widget-card .card-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  color: #64748b;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
}

.widget-card .card-btn:hover {
  background: #e2e8f0;
  color: #1e293b;
}

.widget-card .card-body {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Chart Preview Styles */
.chart-preview,
.table-preview,
.text-preview,
.image-preview,
.video-preview,
.map-preview,
.widget-preview {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.chart-placeholder,
.table-placeholder,
.text-placeholder,
.image-placeholder,
.video-placeholder,
.map-placeholder,
.widget-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
}

.chart-placeholder p,
.table-placeholder p,
.text-placeholder p,
.image-placeholder p,
.video-placeholder p,
.map-placeholder p,
.widget-placeholder p {
  margin: 0;
  font-weight: 500;
  color: #475569;
  font-size: 14px;
}

.chart-placeholder small,
.table-placeholder small,
.text-placeholder small,
.image-placeholder small,
.video-placeholder small,
.map-placeholder small,
.widget-placeholder small {
  color: #64748b;
  font-size: 12px;
  line-height: 1.4;
}

/* KPI Preview Styles */
.kpi-preview {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kpi-content {
  width: 100%;
  max-width: 200px;
}

.kpi-main {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.kpi-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--ocean-teal), var(--forest-green));
  color: white;
  font-size: 18px;
  flex-shrink: 0;
}

.kpi-info {
  flex: 1;
  text-align: left;
}

.kpi-value {
  font-size: 20px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.2;
}

.kpi-label {
  font-size: 12px;
  color: #64748b;
  margin-top: 2px;
}

.kpi-trend {
  text-align: center;
}

.trend-positive {
  color: var(--emerald-green);
  font-size: 12px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* Tab Container Preview Styles */
.tab-container-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-nav-preview {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 12px;
}

.tab-btn-preview {
  padding: 8px 12px;
  font-size: 12px;
  color: #64748b;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-btn-preview.active {
  color: var(--ocean-teal);
  border-bottom-color: var(--ocean-teal);
  font-weight: 500;
}

.tab-content-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-align: center;
}

.tab-placeholder p {
  margin: 0;
  font-weight: 500;
  color: #475569;
  font-size: 14px;
}

.tab-placeholder small {
  color: #64748b;
  font-size: 12px;
}

/* Responsive adjustments for smaller widgets */
@media (max-width: 768px) {
  .widget-card .card-header {
    padding: 10px 12px;
    min-height: 44px;
  }

  .widget-card .widget-title {
    font-size: 13px;
  }

  .widget-card .card-body {
    padding: 12px;
  }

  .chart-placeholder,
  .table-placeholder,
  .text-placeholder,
  .image-placeholder,
  .video-placeholder,
  .map-placeholder,
  .widget-placeholder {
    padding: 15px;
  }

  .kpi-main {
    gap: 8px;
  }

  .kpi-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .kpi-value {
    font-size: 16px;
  }
}

/* Widget drag handle styles */
.widget-drag-handle {
  cursor: move;
  cursor: grab;
  padding: 4px;
  color: #6c757d;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.widget-drag-handle:hover {
  color: #495057;
  cursor: grab;
}

.widget-drag-handle:active {
  cursor: grabbing;
}

/* Ensure grid stack items are draggable */
.grid-stack-item {
  cursor: default;
}

.grid-stack-item.ui-draggable-dragging {
  cursor: grabbing !important;
}

.grid-stack-item.ui-draggable-dragging .widget-drag-handle {
  cursor: grabbing !important;
}

/* Widget header layout */
.widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 0.375rem 0.375rem 0 0;
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  font-weight: 500;
  color: #495057;
}

.widget-actions {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Prevent text selection during drag */
.grid-stack-item.ui-draggable-dragging * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* ===== TAB CONTAINER STYLES ===== */

/* Tab Container */
.tab-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.tab-container[data-tab-position="top"] {
  display: flex;
  flex-direction: column;
}

.tab-container[data-tab-position="bottom"] {
  display: flex;
  flex-direction: column-reverse;
}

/* Tab Content Area */
.tab-content-area {
  flex: 1;
  min-height: 400px;
  background: var(--bg-primary);
}

.tab-content {
  display: none;
  height: 100%;
}

.tab-content.active {
  display: flex;
}

/* Tab Header */
.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-light);
}

.tab-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.tab-info h5 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.tab-info h5 i {
  color: var(--primary-600);
}

.tab-widget-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.tab-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.tab-actions .btn {
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  transition: all 0.2s ease;
}

.tab-actions .btn-outline-primary {
  border-color: var(--primary-300);
  color: var(--primary-600);
}

.tab-actions .btn-outline-primary:hover {
  background: var(--primary-600);
  border-color: var(--primary-600);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.tab-actions .btn-outline-secondary {
  border-color: var(--neutral-300);
  color: var(--neutral-600);
}

.tab-actions .btn-outline-secondary:hover {
  background: var(--neutral-600);
  border-color: var(--neutral-600);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Tab Navigation */
.tab-navigation {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
  padding: var(--spacing-sm);
}

.tab-nav-scroll {
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tab-nav-scroll::-webkit-scrollbar {
  display: none;
}

.tab-nav-list {
  display: flex;
  gap: var(--spacing-xs);
  min-width: max-content;
}

.tab-nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: 0.75rem 1rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
}

.tab-nav-item:hover {
  background: var(--bg-accent);
  border-color: var(--primary-200);
  color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.tab-nav-item.active {
  background: var(--primary-600);
  border-color: var(--primary-600);
  color: white;
  box-shadow: var(--shadow-md);
}

.tab-nav-item.active:hover {
  background: var(--primary-700);
  border-color: var(--primary-700);
}

.tab-name {
  font-weight: var(--font-weight-medium);
}

.tab-badge {
  background: rgba(255, 255, 255, 0.2);
  color: inherit;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: var(--font-weight-semibold);
  min-width: 1.5rem;
  text-align: center;
}

.tab-nav-item:not(.active) .tab-badge {
  background: var(--neutral-200);
  color: var(--neutral-600);
}

.tab-nav-item:hover:not(.active) .tab-badge {
  background: var(--primary-100);
  color: var(--primary-700);
}

.tab-close {
  background: none;
  border: none;
  color: inherit;
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.2s ease;
  margin-left: var(--spacing-xs);
}

.tab-close:hover {
  opacity: 1;
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.tab-nav-item.add-tab {
  background: var(--bg-secondary);
  border: 2px dashed var(--border-medium);
  color: var(--text-tertiary);
  min-width: 3rem;
  justify-content: center;
}

.tab-nav-item.add-tab:hover {
  background: var(--primary-50);
  border-color: var(--primary-300);
  color: var(--primary-600);
}

/* Tab Container in Sidebar */
.container-tabs {
  margin-top: var(--spacing-sm);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.tab-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.tab-item:hover {
  background: var(--bg-accent);
  transform: translateX(2px);
}

.tab-item.active {
  background: var(--primary-100);
  color: var(--primary-700);
  font-weight: var(--font-weight-medium);
}

.tab-count {
  background: var(--neutral-200);
  color: var(--neutral-600);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: var(--font-weight-semibold);
  min-width: 1.25rem;
  text-align: center;
}

.tab-item.active .tab-count {
  background: var(--primary-200);
  color: var(--primary-700);
}

.tab-remove {
  background: none;
  border: none;
  color: var(--neutral-400);
  padding: 0.25rem;
  border-radius: var(--radius-sm);
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  margin-left: var(--spacing-sm);
}

.tab-item:hover .tab-remove {
  opacity: 1;
}

.tab-remove:hover {
  color: var(--error-500);
  background: var(--error-50);
  transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .tab-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .tab-actions {
    align-self: stretch;
    justify-content: flex-end;
  }

  .tab-nav-list {
    flex-wrap: wrap;
  }

  .tab-nav-item {
    flex: 1;
    min-width: 120px;
  }
}

/* Tab Widget Styles */
.tab-container-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.tab-container-widget[data-tab-position="bottom"] {
  flex-direction: column-reverse;
}

/* Tab Navigation */
.widget-tab-nav {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 8px 12px;
  min-height: 48px;
}

.tab-container-widget[data-tab-position="bottom"] .widget-tab-nav {
  border-bottom: none;
  border-top: 1px solid #e9ecef;
}

.tab-nav-scroll {
  overflow-x: auto;
  scrollbar-width: thin;
}

.tab-nav-scroll::-webkit-scrollbar {
  height: 4px;
}

.tab-nav-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.tab-nav-scroll::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 2px;
}

.tab-nav-list {
  display: flex;
  gap: 4px;
  min-width: max-content;
}

.tab-nav-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
}

.tab-nav-item:hover {
  background: #ffffff;
  border-color: #e2e8f0;
  color: #475569;
}

.tab-nav-item.active {
  background: #ffffff;
  border-color: #3b82f6;
  color: #3b82f6;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.1);
}

.tab-name {
  font-weight: 500;
}

.tab-badge {
  background: #e2e8f0;
  color: #64748b;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  line-height: 1.2;
}

.tab-nav-item.active .tab-badge {
  background: #dbeafe;
  color: #3b82f6;
}

.tab-close {
  background: none;
  border: none;
  color: #94a3b8;
  font-size: 12px;
  padding: 2px;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.15s ease;
  margin-left: 4px;
}

.tab-close:hover {
  background: #f1f5f9;
  color: #ef4444;
}

/* Tab Content Area */
.tab-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px;
  position: relative;
}

.tab-content {
  flex: 1;
  display: none;
  padding: 12px;
}

.tab-content.active {
  display: flex;
}

.tab-content-inner {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tab-grid {
  flex: 1;
  min-height: 150px;
}

/* Tab Widget Controls */
.tab-widget-controls {
  padding: 8px 12px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 8px;
  justify-content: center;
}

.tab-widget-controls .btn {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}

/* Section Highlight Effect */
.section-highlight {
  animation: sectionPulse 0.6s ease-in-out;
  border: 2px dashed #3b82f6 !important;
  background: rgba(59, 130, 246, 0.05) !important;
}

@keyframes sectionPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0.1);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Improved Grid Stack Placeholder for Tabs */
.tab-grid .grid-stack-placeholder {
  background: #f8fafc;
  border: 2px dashed #cbd5e0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: 120px;
  color: #64748b;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

.tab-grid .grid-stack-placeholder i {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.6;
}

.tab-grid .grid-stack-placeholder p {
  margin: 0;
  font-weight: 500;
}

.tab-grid .grid-stack-placeholder small {
  margin-top: 4px;
  opacity: 0.8;
}
