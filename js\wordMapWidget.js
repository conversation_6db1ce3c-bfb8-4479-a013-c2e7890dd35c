// Word Map Widget using amCharts v4
function addWordMapWidget() {
  console.log("Adding Word Map widget");
  const chartId = "wordmap-" + Date.now();

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 8,
    content: `
      <div class="word-map-widget p-2">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
           Word Map
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#wordMapSettings"
                    aria-controls="wordMapSettings"
                    onclick="initWordMapSettings('${chartId}')">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div id="${chartId}" class="chart-container"></div>
      </div>
    `,
  });

  // Initialize the chart with a slight delay
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing word map");
      window.initWordMap(chartId);
    } catch (error) {
      console.error("Error initializing word map:", error);
    }
  }, 1000);

  return widget;
}

// Initialize a word map using amCharts v4
window.initWordMap = async function (containerId) {
  console.log("Starting word map initialization for container:", containerId);
  const container = document.getElementById(containerId);

  if (!container) {
    console.error("Chart container not found:", containerId);
    return;
  }

  // Make sure the container fills the available space
  container.style.width = "100%";
  container.style.height = "100%";
  container.style.overflow = "hidden";

  console.log(
    "Container dimensions:",
    container.offsetWidth,
    "x",
    container.offsetHeight
  );

  // Create map instance
  let chart = am4core.create(containerId, am4maps.MapChart);

  // Set map definition
  chart.geodata = am4geodata_worldLow;

  // Set projection
  chart.projection = new am4maps.projections.Miller();

  // Create map polygon series
  let polygonSeries = chart.series.push(new am4maps.MapPolygonSeries());

  // Make map load polygon data from GeoJSON
  polygonSeries.useGeodata = true;

  // Configure series
  let polygonTemplate = polygonSeries.mapPolygons.template;
  polygonTemplate.tooltipText = "{name}: {value}";
  polygonTemplate.fill = am4core.color("#74B266");

  // Create hover state and set alternative fill color
  let hs = polygonTemplate.states.create("hover");
  hs.properties.fill = am4core.color("#367B25");

  // Add heat rule
  polygonSeries.heatRules.push({
    property: "fill",
    target: polygonSeries.mapPolygons.template,
    min: am4core.color("#EFEFEF"),
    max: am4core.color(window.chartConfig.brandColors[0])
  });

  // Add heat legend
  let heatLegend = chart.createChild(am4maps.HeatLegend);
  heatLegend.series = polygonSeries;
  heatLegend.align = "right";
  heatLegend.valign = "bottom";
  heatLegend.width = am4core.percent(20);
  heatLegend.marginRight = am4core.percent(4);
  heatLegend.minValue = 0;
  heatLegend.maxValue = 40000000;

  // Load data from tsc2.0.json
  const chartData = await getWordMapData();

  // Default data in case loading fails
  let defaultData = [
    { id: "US", value: 100 },
    { id: "GB", value: 75 },
    { id: "CN", value: 90 },
    { id: "IN", value: 80 },
    { id: "AU", value: 50 },
    { id: "JP", value: 85 },
    { id: "DE", value: 70 },
    { id: "FR", value: 65 },
    { id: "IT", value: 55 },
    { id: "BR", value: 45 },
    { id: "CA", value: 60 },
    { id: "ZA", value: 40 },
    { id: "RU", value: 75 }
  ];

  // If we successfully loaded data from the file, use it
  if (chartData && chartData.data && chartData.data.length > 0) {
    polygonSeries.data = chartData.data;
  } else {
    polygonSeries.data = defaultData;
  }

  // Add chart title
  let title = chart.titles.create();
  title.text = chartData?.title || "Global Distribution";
  title.fontSize = 16;
  title.marginBottom = 15;

  // Add zoom control
  chart.zoomControl = new am4maps.ZoomControl();
  chart.zoomControl.align = "left";
  chart.zoomControl.valign = "top";

  // Store chart instance in the container for later access
  container.chart = chart;

  // Add resize event listener to handle container resizing
  const resizeObserver = new ResizeObserver(() => {
    chart.invalidateSize();
  });

  resizeObserver.observe(container);

  // Store the observer in the container for cleanup
  container.resizeObserver = resizeObserver;
};

// Initialize word map settings panel
window.initWordMapSettings = function (chartId) {
  console.log("Initializing word map settings for:", chartId);

  // Store the current chart ID for the settings panel
  document.getElementById("wordMapSettings").dataset.chartId = chartId;

  const container = document.getElementById(chartId);
  if (!container || !container.chart) {
    console.error("Chart not found or not initialized");
    return;
  }

  const chart = container.chart;

  // Load chart title
  const chartTitle = chart.titles.getIndex(0);
  if (chartTitle) {
    document.getElementById("wordMapSettings-chartTitle").value = chartTitle.text;
  }

  // Load data into the table
  const dataBody = document.getElementById("wordMapDataBody");
  dataBody.innerHTML = ""; // Clear existing rows

  const polygonSeries = chart.series.getIndex(0);
  if (polygonSeries && polygonSeries.data) {
    polygonSeries.data.forEach((item) => {
      addWordMapDataRow(item.id, item.value);
    });
  }
};

// Apply word map settings
window.applyWordMapSettings = function () {
  const settingsPanel = document.getElementById("wordMapSettings");
  const chartId = settingsPanel.dataset.chartId;

  const container = document.getElementById(chartId);
  if (!container || !container.chart) {
    console.error("Chart not found or not initialized");
    return;
  }

  const chart = container.chart;

  // Update chart title
  const chartTitle = chart.titles.getIndex(0);
  if (chartTitle) {
    chartTitle.text = document.getElementById("wordMapSettings-chartTitle").value;
  }

  // Collect data from the table
  const dataRows = document.querySelectorAll("#wordMapDataBody tr");
  const newData = [];

  dataRows.forEach(row => {
    const countryInput = row.querySelector(".country-input");
    const valueInput = row.querySelector(".value-input");

    if (countryInput && countryInput.value.trim()) {
      newData.push({
        id: countryInput.value.trim(),
        value: parseFloat(valueInput.value) || 0
      });
    }
  });

  // Update chart data if we have rows
  if (newData.length > 0) {
    const polygonSeries = chart.series.getIndex(0);
    if (polygonSeries) {
      polygonSeries.data = newData;
    }
  }

  // Close the settings panel
  const offcanvasElement = bootstrap.Offcanvas.getInstance(settingsPanel);
  if (offcanvasElement) {
    offcanvasElement.hide();
  }
};

// Helper function to add a data row with specified values
function addWordMapDataRow(country, value) {
  const dataBody = document.getElementById("wordMapDataBody");
  const rowId = "wordmap-row-" + Date.now() + "-" + Math.floor(Math.random() * 1000);

  const row = document.createElement("tr");
  row.id = rowId;
  row.innerHTML = `
    <td>
      <input type="text" class="form-control form-control-sm country-input" value="${country || ''}" placeholder="Country code (e.g., US, GB)">
    </td>
    <td>
      <input type="number" class="form-control form-control-sm value-input" value="${value || 0}">
    </td>
    <td>
      <button class="btn btn-sm btn-outline-danger" onclick="removeWordMapDataRow('${rowId}')">
        <i class="las la-trash"></i>
      </button>
    </td>
  `;

  dataBody.appendChild(row);
}

// Function to add a new empty data row
function addNewWordMapDataRow() {
  addWordMapDataRow("", 0);
}

// Function to remove a data row
function removeWordMapDataRow(rowId) {
  const row = document.getElementById(rowId);
  if (row) {
    row.remove();
  }
}
