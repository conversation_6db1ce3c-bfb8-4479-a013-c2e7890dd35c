// Smart Widget Composer - Professional Offcanvas Design

// Global variables for Smart Widget Composer
let selectedTemplate = null;
let currentStep = 1;
let layoutSlots = [];
let customSlots = [];

function createSmartWidgetComposerOffcanvas() {
  try {
    const offcanvasHTML = `
        <!-- Smart Widget Composer Offcanvas -->
        <div class="offcanvas offcanvas-end" tabindex="-1" id="smartWidgetComposerOffcanvas" aria-labelledby="smartWidgetComposerOffcanvasLabel">
            <div class="offcanvas-header">
                <h5 class="offcanvas-title" id="smartWidgetComposerOffcanvasLabel">
                   Smart Widget Composer
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body">
                <!-- Step Navigation -->
                <div class="composer-steps mb-4">
                    <div class="step-indicator">
                        <div class="step active" data-step="1">
                            <div class="step-number">1</div>
                            <div class="step-label">Template</div>
                        </div>
                        <div class="step" data-step="2">
                            <div class="step-number">2</div>
                            <div class="step-label">Layout</div>
                        </div>
                        <div class="step" data-step="3">
                            <div class="step-number">3</div>
                            <div class="step-label">Configure</div>
                        </div>
                    </div>
                </div>

                <!-- Step 1: Template Selection -->
                <div class="composer-step" id="step-1">
                    <div class="data-editor-section mb-4">
                        <div class="section-title mb-3">Choose a Template</div>
                        <div class="template-grid">
                            <div class="template-card" data-template="executive">
                                <div class="template-preview">
                                    <div class="preview-grid">
                                        <div class="preview-item kpi"></div>
                                        <div class="preview-item kpi"></div>
                                        <div class="preview-item chart"></div>
                                        <div class="preview-item table"></div>
                                    </div>
                                </div>
                                <div class="template-info">
                                    <h6>Executive Dashboard</h6>
                                    <small class="text-muted">KPIs, charts, and data tables</small>
                                </div>
                            </div>
                            
                            <div class="template-card" data-template="analytics">
                                <div class="template-preview">
                                    <div class="preview-grid analytics">
                                        <div class="preview-item chart wide"></div>
                                        <div class="preview-item chart"></div>
                                        <div class="preview-item chart"></div>
                                    </div>
                                </div>
                                <div class="template-info">
                                    <h6>Analytics View</h6>
                                    <small class="text-muted">Multiple charts and visualizations</small>
                                </div>
                            </div>
                            
                            <div class="template-card" data-template="comparison">
                                <div class="template-preview">
                                    <div class="preview-grid">
                                        <div class="preview-item chart"></div>
                                        <div class="preview-item chart"></div>
                                        <div class="preview-item table wide"></div>
                                    </div>
                                </div>
                                <div class="template-info">
                                    <h6>Comparison View</h6>
                                    <small class="text-muted">Side-by-side comparisons</small>
                                </div>
                            </div>
                            
                            <div class="template-card" data-template="report">
                                <div class="template-preview">
                                    <div class="preview-grid">
                                        <div class="preview-item text wide"></div>
                                        <div class="preview-item chart"></div>
                                        <div class="preview-item table"></div>
                                    </div>
                                </div>
                                <div class="template-info">
                                    <h6>Report Layout</h6>
                                    <small class="text-muted">Text, charts, and tables</small>
                                </div>
                            </div>
                            
                            <div class="template-card" data-template="custom">
                                <div class="template-preview">
                                    <div class="preview-grid custom-preview">
                                        <div class="preview-item empty">
                                            <i class="las la-plus"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="template-info">
                                    <h6>Custom Layout</h6>
                                    <small class="text-muted">Build from scratch</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Layout Configuration -->
                <div class="composer-step d-none" id="step-2">
                    <div class="data-editor-section mb-4">
                        <div class="section-title mb-3">Configure Layout</div>
                        
                        <!-- Template Layout Preview -->
                        <div id="template-layout-preview" class="mb-4">
                            <!-- Dynamic content based on selected template -->
                        </div>
                        
                        <!-- Custom Layout Builder (for custom template) -->
                        <div id="custom-layout-builder" class="d-none">
                            <div class="layout-builder-toolbar mb-3">
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addLayoutSlot('chart')">
                                        <i class="las la-chart-pie me-1"></i>Chart
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addLayoutSlot('data')">
                                        <i class="las la-table me-1"></i>Data
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addLayoutSlot('text')">
                                        <i class="las la-font me-1"></i>Text
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="addLayoutSlot('media')">
                                        <i class="las la-image me-1"></i>Media
                                    </button>
                                </div>
                            </div>
                            <div class="custom-layout-grid" id="custom-layout-grid">
                                <div class="layout-slot-placeholder">
                                    <i class="las la-plus"></i>
                                    <span>Click buttons above to add widgets</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Final Configuration -->
                <div class="composer-step d-none" id="step-3">
                    <div class="data-editor-section mb-4">
                        <div class="section-title mb-3">Section Configuration</div>
                        
                        <div class="mb-3">
                            <label for="sectionTitle" class="form-label">Section Title</label>
                            <input type="text" class="form-control" id="sectionTitle" placeholder="Enter section title" value="New Dashboard Section">
                        </div>
                        
                        <div class="mb-3">
                            <label for="sectionSize" class="form-label">Section Size</label>
                            <select class="form-select" id="sectionSize">
                                <option value="small">Small (6 columns)</option>
                                <option value="medium" selected>Medium (8 columns)</option>
                                <option value="large">Large (10 columns)</option>
                                <option value="full">Full Width (12 columns)</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Preview</label>
                            <div class="section-preview" id="final-preview">
                                <!-- Preview will be generated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="composer-navigation">
                    <button type="button" class="btn-sm btn btn-secondary" id="prevStep" onclick="previousStep()" disabled>
                        <i class="las la-arrow-left me-1"></i>Previous
                    </button>
                    <button type="button" class="btn-sm btn btn-primary" id="nextStep" onclick="nextStep()">
                        Next<i class="las la-arrow-right ms-1"></i>
                    </button>
                    <button type="button" class="btn btn-success d-none" id="createSection" onclick="createSmartSection()">
                        <i class="las la-plus me-1"></i>Create Section
                    </button>
                </div>
            </div>
        </div>
    `;

    // Remove existing offcanvas if it exists
    const existingOffcanvas = document.getElementById(
      "smartWidgetComposerOffcanvas"
    );
    if (existingOffcanvas) {
      existingOffcanvas.remove();
    }

    // Add the offcanvas to the body
    document.body.insertAdjacentHTML("beforeend", offcanvasHTML);

    // Initialize the offcanvas functionality
    initializeComposerOffcanvas();
  } catch (error) {}
}

function initializeComposerOffcanvas() {
  // Reset composer state
  resetComposer();

  // Load saved templates and update template grid
  updateTemplateGrid();

  // Add event listeners for template selection
  document.querySelectorAll(".template-card").forEach((card) => {
    card.addEventListener("click", function () {
      // Remove active class from all cards
      document
        .querySelectorAll(".template-card")
        .forEach((c) => c.classList.remove("active"));

      // Add active class to clicked card
      this.classList.add("active");

      // Set selected template
      selectedTemplate = this.dataset.template;

      // Handle custom templates
      if (selectedTemplate.startsWith("custom-")) {
        const savedTemplates = getSavedTemplates();
        const customTemplate = savedTemplates.find(
          (t) => t.id === selectedTemplate
        );
        if (customTemplate) {
          // Set layout slots to the custom template's slots
          layoutSlots = [...customTemplate.slots];
        }
      } else if (selectedTemplate.startsWith("section-")) {
        // Handle section templates
        const savedTemplates = getSavedTemplates();
        const sectionTemplate = savedTemplates.find(
          (t) => t.id === selectedTemplate
        );
        if (sectionTemplate) {
          // Set layout slots to the section template's slots
          layoutSlots = [...sectionTemplate.slots];
        }
      }

      // Enable next button
      document.getElementById("nextStep").disabled = false;
    });
  });
}

function nextStep() {
  if (currentStep < 3) {
    // Hide current step
    document.getElementById(`step-${currentStep}`).classList.add("d-none");
    document
      .querySelector(`.step[data-step="${currentStep}"]`)
      .classList.remove("active");

    currentStep++;

    // Show next step
    document.getElementById(`step-${currentStep}`).classList.remove("d-none");
    document
      .querySelector(`.step[data-step="${currentStep}"]`)
      .classList.add("active");

    // Update navigation buttons
    document.getElementById("prevStep").disabled = false;

    if (currentStep === 2) {
      setupLayoutStep();
    } else if (currentStep === 3) {
      document.getElementById("nextStep").classList.add("d-none");
      document.getElementById("createSection").classList.remove("d-none");
      setupFinalStep();
    }
  }
}

function previousStep() {
  if (currentStep > 1) {
    // Hide current step
    document.getElementById(`step-${currentStep}`).classList.add("d-none");
    document
      .querySelector(`.step[data-step="${currentStep}"]`)
      .classList.remove("active");

    currentStep--;

    // Show previous step
    document.getElementById(`step-${currentStep}`).classList.remove("d-none");
    document
      .querySelector(`.step[data-step="${currentStep}"]`)
      .classList.add("active");

    // Update navigation buttons
    if (currentStep === 1) {
      document.getElementById("prevStep").disabled = true;
    }

    if (currentStep === 2) {
      document.getElementById("nextStep").classList.remove("d-none");
      document.getElementById("createSection").classList.add("d-none");
    }
  }
}

function setupLayoutStep() {
  const layoutPreview = document.getElementById("template-layout-preview");
  const customBuilder = document.getElementById("custom-layout-builder");

  if (selectedTemplate === "custom") {
    layoutPreview.classList.add("d-none");
    customBuilder.classList.remove("d-none");
    setupCustomBuilder();
  } else {
    layoutPreview.classList.remove("d-none");
    customBuilder.classList.add("d-none");
    setupTemplatePreview();
  }
}

function setupTemplatePreview() {
  const templates = {
    executive: [
      { type: "kpi", title: "Revenue KPI", size: "small" },
      { type: "kpi", title: "Users KPI", size: "small" },
      { type: "pie-chart", title: "Sales Distribution", size: "medium" },
      { type: "handsontable", title: "Data Table", size: "large" },
    ],
    analytics: [
      { type: "line-chart", title: "Trend Analysis", size: "large" },
      { type: "bar-chart", title: "Category Comparison", size: "medium" },
      { type: "bubble-chart", title: "Correlation View", size: "medium" },
    ],
    comparison: [
      { type: "bar-chart", title: "Product A", size: "medium" },
      { type: "bar-chart", title: "Product B", size: "medium" },
      { type: "handsontable", title: "Comparison Data", size: "large" },
    ],
    report: [
      { type: "text", title: "Executive Summary", size: "large" },
      { type: "line-chart", title: "Performance Chart", size: "medium" },
      { type: "handsontable", title: "Supporting Data", size: "medium" },
    ],
  };

  // Check if this is a section template (custom template with section type)
  if (selectedTemplate.startsWith("section-")) {
    const savedTemplates = getSavedTemplates();
    const sectionTemplate = savedTemplates.find(
      (t) => t.id === selectedTemplate
    );

    if (sectionTemplate && sectionTemplate.type === "section") {
      // Use the section template's slots
      layoutSlots = [...sectionTemplate.slots];
      console.log(
        "Loading section template:",
        sectionTemplate.name,
        "with",
        layoutSlots.length,
        "slots"
      );
    } else {
      console.error("Section template not found:", selectedTemplate);
      layoutSlots = [];
    }
  } else {
    // Use predefined template slots
    layoutSlots = templates[selectedTemplate] || [];
  }

  // Ensure layoutSlots is not empty
  if (layoutSlots.length === 0) {
    console.warn("No slots found for template:", selectedTemplate);
    document.getElementById("template-layout-preview").innerHTML = `
      <div class="alert alert-warning">
        <i class="las la-exclamation-triangle me-2"></i>
        No widgets found in this template. Please select a different template or create a custom layout.
      </div>
    `;
    return;
  }

  const previewHTML = layoutSlots
    .map(
      (slot, index) => `
        <div class="layout-slot ${slot.size}" data-index="${index}">
            <div class="slot-header">
                <div class="slot-title" ondblclick="startInlineEdit(${index}, this)" title="Double-click to edit title">
                    <i class="las ${getWidgetIcon(slot.type)}"></i>
                    <span>${slot.title}</span>
                </div>
                <div class="slot-actions">
                    <button class="btn btn-sm btn-outline-secondary" onclick="editSlot(${index})" title="Edit Widget">
                        <i class="las la-edit"></i>
                    </button>
                </div>
            </div>
            <div class="slot-preview">
                ${getSlotPreview(slot.type)}
            </div>
        </div>
    `
    )
    .join("");

  document.getElementById("template-layout-preview").innerHTML = `
        <div class="layout-preview-grid">
            ${previewHTML}
        </div>
    `;
}

function setupCustomBuilder() {
  customSlots = [];
  updateCustomLayoutGrid();
}

function addLayoutSlot(category) {
  // Check if a widget selector is already showing
  const existingSelector = document.querySelector(".widget-selector-card");
  if (existingSelector) {
    // If selector is showing, remove it (toggle behavior)
    cancelWidgetSelection();
    return;
  }

  // Add button highlighting functionality
  const buttonGroup = document.querySelector(
    "#custom-layout-builder .btn-group"
  );
  if (buttonGroup) {
    // Remove active class from all buttons in the group
    const buttons = buttonGroup.querySelectorAll(".btn-outline-primary");
    buttons.forEach((btn) => btn.classList.remove("active"));

    // Add active class to the clicked button
    const clickedButton = event ? event.target : null;
    if (
      clickedButton &&
      clickedButton.classList.contains("btn-outline-primary")
    ) {
      clickedButton.classList.add("active");
    } else {
      // Fallback: find button by category
      buttons.forEach((btn) => {
        const btnText = btn.textContent.toLowerCase().trim();
        if (btnText.includes(category.toLowerCase())) {
          btn.classList.add("active");
        }
      });
    }
  }

  // Check if widgets already exist
  if (customSlots.length > 0) {
    // Show a more contextual message for existing widgets
    showEnhancedWidgetSelector(category);
  } else {
    // Show regular widget selector for empty layout
    showWidgetSelector(category);
  }
}

function showWidgetSelector(category) {
  const widgetTypes = getWidgetsByCategory(category);
  const grid = document.getElementById("custom-layout-grid");

  // CRITICAL FIX: Remove any existing widget selector cards to prevent duplicates
  const existingSelectors = grid.querySelectorAll(".widget-selector-card");
  existingSelectors.forEach((selector) => selector.remove());

  // Remove placeholder if it exists
  const placeholder = grid.querySelector(".layout-slot-placeholder");
  if (placeholder) {
    placeholder.remove();
  }

  // Create widget selector card
  const selectorCard = document.createElement("div");
  selectorCard.className = "widget-selector-card";
  selectorCard.innerHTML = `
    <div class="selector-header">
      <h6><i class="las ${getCategoryIcon(category)} me-2"></i>Choose ${
    category.charAt(0).toUpperCase() + category.slice(1)
  } Widget</h6>
      <button class="btn btn-sm btn-outline-secondary" onclick="cancelWidgetSelection()">
        <i class="las la-times"></i>
      </button>
    </div>
    <div class="widget-type-grid">
      ${widgetTypes
        .map(
          (widget) => `
        <div class="widget-type-option" onclick="addCustomWidget('${
          widget.type
        }', '${widget.label}', '${category}')">
          <div class="widget-type-icon">
            <i class="las ${getWidgetIcon(widget.type)}"></i>
          </div>
          <div class="widget-type-label">${widget.label}</div>
        </div>
      `
        )
        .join("")}
    </div>
  `;

  grid.appendChild(selectorCard);
}

function addCustomWidget(type, label, category) {
  const newSlot = {
    type: type,
    title: label,
    category: category,
    size: "medium",
  };

  customSlots.push(newSlot);

  // CRITICAL FIX: Remove ALL widget selector cards, not just the first one
  const selectorCards = document.querySelectorAll(".widget-selector-card");
  selectorCards.forEach((card) => card.remove());

  // Remove active states from buttons after successful widget addition
  const buttonGroup = document.querySelector(
    "#custom-layout-builder .btn-group"
  );
  if (buttonGroup) {
    const buttons = buttonGroup.querySelectorAll(".btn-outline-primary");
    buttons.forEach((btn) => btn.classList.remove("active"));
  }

  updateCustomLayoutGrid();
}

function cancelWidgetSelection() {
  // CRITICAL FIX: Remove ALL widget selector cards, not just the first one
  const selectorCards = document.querySelectorAll(".widget-selector-card");
  selectorCards.forEach((card) => card.remove());

  // Remove active states from buttons
  const buttonGroup = document.querySelector(
    "#custom-layout-builder .btn-group"
  );
  if (buttonGroup) {
    const buttons = buttonGroup.querySelectorAll(".btn-outline-primary");
    buttons.forEach((btn) => btn.classList.remove("active"));
  }

  // Show placeholder if no widgets exist
  if (customSlots.length === 0) {
    updateCustomLayoutGrid();
  }
}

function getCategoryIcon(category) {
  const icons = {
    chart: "la-chart-pie",
    data: "la-table",
    text: "la-font",
    media: "la-image",
  };
  return icons[category] || "la-cube";
}

function updateCustomLayoutGrid() {
  const grid = document.getElementById("custom-layout-grid");

  if (customSlots.length === 0) {
    grid.innerHTML = `
            <div class="layout-slot-placeholder">
                <i class="las la-plus"></i>
                <span>Click buttons above to add widgets</span>
            </div>
        `;
    return;
  }

  const slotsHTML = customSlots
    .map(
      (slot, index) => `
        <div class="layout-slot ${slot.size}" data-index="${index}">
            <div class="slot-header">
                <div class="slot-title" ondblclick="startInlineEdit(${index}, this)" title="Double-click to edit title">
                    <i class="las ${getWidgetIcon(slot.type)}"></i>
                    <span>${slot.title}</span>
                </div>
                <div class="slot-actions">
                    <div class="size-selector">
                        <select class="form-select form-select-sm" onchange="updateSlotSize(${index}, this.value)">
                            <option value="small" ${
                              slot.size === "small" ? "selected" : ""
                            }>Small</option>
                            <option value="medium" ${
                              slot.size === "medium" ? "selected" : ""
                            }>Medium</option>
                            <option value="large" ${
                              slot.size === "large" ? "selected" : ""
                            }>Large</option>
                        </select>
                    </div>
                    <button class="slot-remove-btn" onclick="removeCustomSlot(${index})" title="Remove Widget">
                        <i class="las la-times"></i>
                    </button>
                </div>
            </div>
            <div class="slot-preview">
                ${getSlotPreview(slot.type)}
            </div>
        </div>
    `
    )
    .join("");

  grid.innerHTML = slotsHTML;
}

// Add inline editing functionality
function startInlineEdit(index, titleElement) {
  if (titleElement.classList.contains("editing")) return;

  const span = titleElement.querySelector("span");
  const currentTitle = span.textContent;

  titleElement.classList.add("editing");

  const input = document.createElement("input");
  input.type = "text";
  input.value = currentTitle;
  input.setAttribute("data-original", currentTitle);

  titleElement.appendChild(input);
  input.focus();
  input.select();

  // Handle save on Enter or blur
  function saveEdit() {
    const newTitle = input.value.trim() || currentTitle;
    customSlots[index].title = newTitle;
    span.textContent = newTitle;
    titleElement.classList.remove("editing");
    input.remove();
  }

  // Handle cancel on Escape
  function cancelEdit() {
    titleElement.classList.remove("editing");
    input.remove();
  }

  input.addEventListener("blur", saveEdit);
  input.addEventListener("keydown", (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      saveEdit();
    } else if (e.key === "Escape") {
      e.preventDefault();
      cancelEdit();
    }
  });
}

function updateSlotSize(index, size) {
  customSlots[index].size = size;
  updateCustomLayoutGrid();
}

function updateSlotTitle(index, title) {
  customSlots[index].title = title;
}

function removeCustomSlot(index) {
  customSlots.splice(index, 1);
  updateCustomLayoutGrid();
}

function setupFinalStep() {
  const finalSlots = selectedTemplate === "custom" ? customSlots : layoutSlots;

  const previewHTML = finalSlots
    .map(
      (slot) => `
        <div class="preview-widget ${slot.size}">
            <div class="widget-header">
                <i class="las ${getWidgetIcon(slot.type)} me-2"></i>
                ${slot.title}
            </div>
            <div class="widget-body">
                ${getSlotPreview(slot.type)}
            </div>
        </div>
    `
    )
    .join("");

  document.getElementById("final-preview").innerHTML = `
        <div class="final-preview-grid">
            ${previewHTML}
        </div>
    `;
}

function getSlotPreview(type) {
  const previews = {
    kpi: '<div class="kpi-preview"><div class="kpi-value">$125K</div><div class="kpi-label">Revenue</div></div>',
    "pie-chart":
      '<div class="chart-preview"><div class="pie-preview"></div></div>',
    "bar-chart":
      '<div class="chart-preview"><div class="bar-preview"></div></div>',
    "line-chart":
      '<div class="chart-preview"><div class="line-preview"></div></div>',
    "bubble-chart":
      '<div class="chart-preview"><div class="bubble-preview"></div></div>',
    "world-map":
      '<div class="chart-preview"><div class="world-map-preview"></div></div>',
    "word-cloud":
      '<div class="chart-preview"><div class="word-cloud-preview"></div></div>',
    handsontable:
      '<div class="table-preview"><div class="table-row"></div><div class="table-row"></div><div class="table-row"></div></div>',
    text: '<div class="text-preview"><div class="text-line"></div><div class="text-line short"></div></div>',
    image: '<div class="media-preview"><i class="las la-image"></i></div>',
    video: '<div class="media-preview"><i class="las la-video"></i></div>',
    "price-chart":
      '<div class="chart-preview"><div class="line-preview"></div></div>',
    "pdf-viewer":
      '<div class="media-preview"><i class="las la-file-pdf"></i></div>',
    "tab-container":
      '<div class="media-preview"><i class="las la-folder-open"></i></div>',
  };

  return previews[type] || '<div class="generic-preview"></div>';
}

function getWidgetsByCategory(category) {
  const widgets = {
    chart: [
      { type: "pie-chart", label: "Pie Chart" },
      { type: "bar-chart", label: "Bar Chart" },
      { type: "line-chart", label: "Line Chart" },
      { type: "bubble-chart", label: "Bubble Chart" },
      { type: "world-map", label: "World Map" },
      { type: "word-cloud", label: "Word Cloud" },
    ],
    data: [
      { type: "handsontable", label: "Data Table" },
      { type: "kpi", label: "KPI Widget" },
      { type: "price-chart", label: "Price Chart" },
      { type: "tab-container", label: "Tab Container" },
    ],
    text: [
      { type: "text", label: "Text Widget" },
      { type: "pdf-viewer", label: "PDF Viewer" },
    ],
    media: [
      { type: "image", label: "Image Widget" },
      { type: "video", label: "Video Widget" },
    ],
  };

  return widgets[category] || [];
}

function getWidgetIcon(type) {
  const icons = {
    kpi: "la-tachometer-alt",
    "pie-chart": "la-chart-pie",
    "bar-chart": "la-chart-bar",
    "line-chart": "la-chart-line",
    "bubble-chart": "la-braille",
    "world-map": "la-globe",
    "word-cloud": "la-cloud",
    handsontable: "la-table",
    "price-chart": "la-chart-area",
    "tab-container": "la-folder-open",
    text: "la-font",
    "pdf-viewer": "la-file-pdf",
    image: "la-image",
    video: "la-video",
  };

  return icons[type] || "la-cube";
}

function createSmartSection() {
  const title =
    document.getElementById("sectionTitle").value || "New Dashboard Section";
  const size = document.getElementById("sectionSize").value;
  const finalSlots = selectedTemplate === "custom" ? customSlots : layoutSlots;

  if (finalSlots.length === 0) {
    alert("Please add at least one widget to the section.");
    return;
  }

  const dimensions = getSectionDimensions(size, finalSlots);
  const sectionId = `section-${Date.now()}`;

  // Create the section container
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: dimensions.width,
    h: dimensions.height,
    content: `
            <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;" data-section-id="${sectionId}">
                <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
                    <div>
                         ${title}
                    </div>
                    <div>
                        <button class="btn btn-sm btn-link text-primary save-section-btn" onclick="showSaveSectionTemplateModal('${sectionId}')" title="Save as Template">
                            <i class="las la-save"></i>
                        </button>
                        <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                            <i class="las la-times"></i>
                        </button>
                    </div>
                </div>
                <div class="nested-grid-container" style="height: calc(100% - 40px); overflow: hidden;"></div>
            </div>
        `,
  });

  // Initialize nested grid
  const nestedGridContainer = widget.querySelector(".nested-grid-container");
  const nestedGrid = GridStack.init(
    {
      column: 12,
      cellHeight: 60,
      margin: 5,
      resizable: { handles: "se" },
      acceptWidgets: true,
    },
    nestedGridContainer
  );

  // Add widgets to the nested grid
  addTemplateWidgets(nestedGrid, finalSlots);

  // Store section configuration for template saving
  const sectionElement = widget.querySelector(".section-container-widget");
  sectionElement.setAttribute(
    "data-section-config",
    JSON.stringify({
      title: title,
      size: size,
      slots: finalSlots,
      template: selectedTemplate,
    })
  );

  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(
    document.getElementById("smartWidgetComposerOffcanvas")
  );
  offcanvas.hide();

  // Reset the composer
  resetComposer();

  // Show success message
  showNotification("Section created successfully!", "success");
}

function addTemplateWidgets(nestedGrid, slots) {
  // Calculate optimal grid layout based on number of slots
  const totalSlots = slots.length;
  let currentX = 0;
  let currentY = 0;
  let currentRowHeight = 0;
  const maxColumns = 12;

  slots.forEach((slot, index) => {
    const widgetDimensions = getWidgetDimensions(slot.size);

    // Check if widget fits in current row
    if (currentX + widgetDimensions.width > maxColumns) {
      // Move to next row
      currentX = 0;
      currentY += currentRowHeight;
      currentRowHeight = widgetDimensions.height;
    } else {
      // Update current row height to accommodate tallest widget
      currentRowHeight = Math.max(currentRowHeight, widgetDimensions.height);
    }

    // Create widget content based on type
    const widgetContent = createWidgetContent(slot.type, slot.title);

    // Add the widget to the nested grid
    const widget = nestedGrid.addWidget({
      x: currentX,
      y: currentY,
      w: widgetDimensions.width,
      h: widgetDimensions.height,
      content: widgetContent,
    });

    // Initialize widget after it's added to DOM
    setTimeout(() => {
      initializeNestedWidget(widget, slot.type);
    }, 100);

    // Update position for next widget
    currentX += widgetDimensions.width;
  });
}

function initializeNestedWidget(widget, type) {
  const widgetElement = widget.querySelector(".widget-wrapper");
  if (!widgetElement) return;

  const uniqueId = widgetElement.id;

  switch (type) {
    case "pie-chart":
      initializePieChart(uniqueId);
      break;
    case "bar-chart":
      initializeBarChart(uniqueId);
      break;
    case "line-chart":
      initializeLineChart(uniqueId);
      break;
    case "handsontable":
      initializeDataTable(uniqueId);
      break;
    case "tab-container":
      initializeTabContainer(uniqueId);
      break;
    case "price-chart":
      initializePriceChart(uniqueId);
      break;
    default:
      // No initialization needed for other widget types
      break;
  }
}

function initializePieChart(widgetId) {
  const chartContainer = document.getElementById(`${widgetId}-chart`);
  if (!chartContainer) return;

  // Sample pie chart data
  const data = [
    { name: "Desktop", value: 45, color: "#007bff" },
    { name: "Mobile", value: 35, color: "#28a745" },
    { name: "Tablet", value: 20, color: "#ffc107" },
  ];

  // Create simple pie chart visualization
  chartContainer.innerHTML = `
    <div style="display: flex; align-items: center; justify-content: center; height: 100%;">
      <div style="width: 120px; height: 120px; border-radius: 50%; background: conic-gradient(
        #007bff 0deg 162deg,
        #28a745 162deg 288deg,
        #ffc107 288deg 360deg
      );"></div>
    </div>
  `;
}

function initializeBarChart(widgetId) {
  const chartContainer = document.getElementById(`${widgetId}-chart`);
  if (!chartContainer) return;

  // Create simple bar chart visualization
  chartContainer.innerHTML = `
    <div style="display: flex; align-items: end; justify-content: center; height: 100%; gap: 8px; padding: 20px;">
      <div style="width: 30px; height: 60%; background: #007bff; border-radius: 2px;"></div>
      <div style="width: 30px; height: 80%; background: #28a745; border-radius: 2px;"></div>
      <div style="width: 30px; height: 40%; background: #ffc107; border-radius: 2px;"></div>
      <div style="width: 30px; height: 70%; background: #dc3545; border-radius: 2px;"></div>
    </div>
  `;
}

function initializeLineChart(widgetId) {
  const chartContainer = document.getElementById(`${widgetId}-chart`);
  if (!chartContainer) return;

  // Create simple line chart visualization
  chartContainer.innerHTML = `
    <div style="display: flex; align-items: center; justify-content: center; height: 100%;">
      <svg width="200" height="100" style="border: 1px solid #dee2e6;">
        <polyline points="20,80 60,40 100,60 140,20 180,50" 
                  fill="none" stroke="#007bff" stroke-width="3"/>
        <circle cx="20" cy="80" r="3" fill="#007bff"/>
        <circle cx="60" cy="40" r="3" fill="#007bff"/>
        <circle cx="100" cy="60" r="3" fill="#007bff"/>
        <circle cx="140" cy="20" r="3" fill="#007bff"/>
        <circle cx="180" cy="50" r="3" fill="#007bff"/>
      </svg>
    </div>
  `;
}

function initializeDataTable(widgetId) {
  const tableContainer = document.getElementById(`${widgetId}-table`);
  if (!tableContainer) return;

  // Create simple data table
  tableContainer.innerHTML = `
    <div style="height: 100%; overflow: auto;">
      <table class="table table-striped table-sm">
        <thead>
          <tr>
            <th>Product</th>
            <th>Sales</th>
            <th>Growth</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Product A</td>
            <td>$12,500</td>
            <td class="text-success">+15%</td>
          </tr>
          <tr>
            <td>Product B</td>
            <td>$8,750</td>
            <td class="text-success">+8%</td>
          </tr>
          <tr>
            <td>Product C</td>
            <td>$6,200</td>
            <td class="text-danger">-3%</td>
          </tr>
        </tbody>
      </table>
    </div>
  `;
}

function initializeTabContainer(widgetId) {
  // Initialize global tab configurations if not exists
  if (!window.tabConfigurations) {
    window.tabConfigurations = {};
  }

  // Only initialize if configuration doesn't already exist
  if (!window.tabConfigurations[widgetId]) {
    window.tabConfigurations[widgetId] = {
      tabs: [
        {
          id: "tab1",
          title: "Tab 1",
          widgets: [], // Start with empty widgets array
        },
        {
          id: "tab2",
          title: "Tab 2",
          widgets: [], // Start with empty widgets array
        },
      ],
      tabPosition: "top",
    };

    // Render the tab widget content
    const widgetElement = document.getElementById(widgetId);
    if (widgetElement) {
      const widgetContent = widgetElement.querySelector(".widget-content");
      if (widgetContent) {
        widgetContent.innerHTML = renderTabWidget(
          window.tabConfigurations[widgetId]
        );

        // Initialize GridStack for each tab that has multi-widget content
        setTimeout(() => {
          ensureTabGridStacksInitialized(widgetId);
        }, 100);
      }
    }
  }
}

function initializePriceChart(widgetId) {
  const chartContainer = document.getElementById(`${widgetId}-chart`);
  if (!chartContainer) return;

  // Create price chart visualization
  chartContainer.innerHTML = `
    <div style="display: flex; flex-direction: column; height: 100%; padding: 10px;">
      <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
        <span style="font-weight: bold; color: #28a745;">$1,234.56</span>
        <span style="color: #28a745;">*****%</span>
      </div>
      <div style="flex: 1; position: relative;">
        <svg width="100%" height="100%" style="border: 1px solid #dee2e6;">
          <polyline points="10,80 30,60 50,70 70,40 90,45 110,30 130,35 150,20 170,25 190,15" 
                    fill="none" stroke="#28a745" stroke-width="2"/>
          <circle cx="190" cy="15" r="3" fill="#28a745"/>
        </svg>
      </div>
    </div>
  `;
}

function createWidgetContent(type, title) {
  const uniqueId = `widget-${type}-${Date.now()}-${Math.random()
    .toString(36)
    .substr(2, 9)}`;

  switch (type) {
    case "kpi":
      return `
        <div class="widget-wrapper kpi-widget-wrapper" id="${uniqueId}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-tachometer-alt"></i>
              <span class="widget-title-text">${title}</span>
            </div>
            <div class="widget-actions">
              <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-content kpi-content">
            <div class="kpi-main">
              <div class="kpi-icon">
                <i class="las la-dollar-sign"></i>
              </div>
              <div class="kpi-info">
                <div class="kpi-value">$124,856</div>
                <div class="kpi-label">${title}</div>
              </div>
              <div class="kpi-trend">
                <span class="trend-indicator positive">
                  <i class="las la-arrow-up"></i>
                  <span class="trend-value">+12.5%</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      `;

    case "pie-chart":
      return `
        <div class="widget-wrapper chart-widget-wrapper" id="${uniqueId}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-chart-pie"></i>
              <span class="widget-title-text">${title}</span>
            </div>
            <div class="widget-actions">
              <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-content">
            <div id="${uniqueId}-chart" class="chart-container" style="height: 100%; width: 100%;"></div>
          </div>
        </div>
      `;

    case "bar-chart":
      return `
        <div class="widget-wrapper chart-widget-wrapper" id="${uniqueId}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-chart-bar"></i>
              <span class="widget-title-text">${title}</span>
            </div>
            <div class="widget-actions">
              <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-content">
            <div id="${uniqueId}-chart" class="chart-container" style="height: 100%; width: 100%;"></div>
          </div>
        </div>
      `;

    case "line-chart":
      return `
        <div class="widget-wrapper chart-widget-wrapper" id="${uniqueId}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-chart-line"></i>
              <span class="widget-title-text">${title}</span>
            </div>
            <div class="widget-actions">
              <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-content">
            <div id="${uniqueId}-chart" class="chart-container" style="height: 100%; width: 100%;"></div>
          </div>
        </div>
      `;

    case "handsontable":
      return `
        <div class="widget-wrapper table-widget-wrapper" id="${uniqueId}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-table"></i>
              <span class="widget-title-text">${title}</span>
            </div>
            <div class="widget-actions">
              <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-content">
            <div id="${uniqueId}-table" class="spreadsheet-container" style="height: 100%; width: 100%;"></div>
          </div>
        </div>
      `;

    case "text":
      return `
        <div class="widget-wrapper text-widget-wrapper" id="${uniqueId}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-font"></i>
              <span class="widget-title-text">${title}</span>
            </div>
            <div class="widget-actions">
              <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-content">
            <div class="text-content" style="padding: 1rem;">
              <h4>${title}</h4>
              <p>This is a sample text widget. You can customize this content to display any text, markdown, or HTML content you need.</p>
              <p>Click the settings button to configure this widget.</p>
            </div>
          </div>
        </div>
      `;

    case "image":
      return `
        <div class="widget-wrapper media-widget-wrapper" id="${uniqueId}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-image"></i>
              <span class="widget-title-text">${title}</span>
            </div>
            <div class="widget-actions">
              <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-content">
            <div class="image-placeholder" style="height: 100%; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border: 2px dashed #dee2e6;">
              <div class="text-center">
                <i class="las la-image" style="font-size: 3rem; color: #6c757d;"></i>
                <p class="mt-2 mb-0 text-muted">Image Placeholder</p>
              </div>
            </div>
          </div>
        </div>
      `;

    case "tab-container":
      return `
        <div class="widget-wrapper tab-container-wrapper" id="${uniqueId}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-folder-open"></i>
              <span class="widget-title-text">${title}</span>
            </div>
            <div class="widget-actions">
              <button class="widget-action-btn" onclick="configureTabWidget('${uniqueId}')" title="Configure Tabs">
                <i class="las la-cog"></i>
              </button>
              <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-content">
            <div class="tab-container" id="${uniqueId}-tabs">
              <div class="tab-navigation">
                <button class="tab-btn active" data-tab="tab1">Tab 1</button>
                <button class="tab-btn" data-tab="tab2">Tab 2</button>
              </div>
              <div class="tab-content-container">
                <div class="tab-content active" id="tab1">
                  <div class="tab-gridstack-container" id="gridstack-tab1" style="min-height: 300px;">
                    <!-- Tab 1 content will be added here -->
                  </div>
                </div>
                <div class="tab-content" id="tab2">
                  <div class="tab-gridstack-container" id="gridstack-tab2" style="min-height: 300px;">
                    <!-- Tab 2 content will be added here -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      `;

    case "bubble-chart":
      return `
        <div class="widget-wrapper chart-widget-wrapper" id="${uniqueId}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-braille"></i>
              <span class="widget-title-text">${title}</span>
            </div>
            <div class="widget-actions">
              <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-content">
            <div id="${uniqueId}-chart" class="chart-container" style="height: 100%; width: 100%;"></div>
          </div>
        </div>
      `;

    case "price-chart":
      return `
        <div class="widget-wrapper chart-widget-wrapper" id="${uniqueId}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-chart-area"></i>
              <span class="widget-title-text">${title}</span>
            </div>
            <div class="widget-actions">
              <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-content">
            <div id="${uniqueId}-chart" class="chart-container" style="height: 100%; width: 100%;"></div>
          </div>
        </div>
      `;

    default:
      return `
        <div class="widget-wrapper" id="${uniqueId}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-cube"></i>
              <span class="widget-title-text">${title}</span>
            </div>
            <div class="widget-actions">
              <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-content">
            <div class="default-widget-content">
              <p>Unknown widget type: ${type}</p>
            </div>
          </div>
        </div>
      `;
  }
}

function getWidgetDimensions(size) {
  const dimensions = {
    small: { width: 4, height: 3 },
    medium: { width: 6, height: 4 },
    large: { width: 12, height: 5 },
  };

  return dimensions[size] || dimensions.medium;
}

function getSectionDimensions(size, slots = []) {
  // Calculate height based on content if slots are provided
  if (slots && slots.length > 0) {
    let totalHeight = 0;
    let currentRowWidth = 0;
    let currentRowHeight = 0;
    const maxColumns = 12;

    slots.forEach((slot) => {
      const widgetDimensions = getWidgetDimensions(slot.size);
      if (currentRowWidth + widgetDimensions.width > maxColumns) {
        // Add completed row height
        totalHeight += currentRowHeight;
        currentRowWidth = widgetDimensions.width;
        currentRowHeight = widgetDimensions.height;
      } else {
        currentRowWidth += widgetDimensions.width;
        currentRowHeight = Math.max(currentRowHeight, widgetDimensions.height);
      }
    });

    // Add the last row height
    totalHeight += currentRowHeight;

    // Calculate height: header (2) + content + padding (2)
    const calculatedHeight = 2 + totalHeight + 2;

    const dimensions = {
      small: { width: 6, height: Math.max(calculatedHeight, 8) },
      medium: { width: 8, height: Math.max(calculatedHeight, 10) },
      large: { width: 10, height: Math.max(calculatedHeight, 12) },
      full: { width: 12, height: Math.max(calculatedHeight, 14) },
    };

    return dimensions[size] || dimensions.medium;
  }

  // Default dimensions when no slots provided
  const dimensions = {
    small: { width: 6, height: 8 },
    medium: { width: 8, height: 10 },
    large: { width: 10, height: 12 },
    full: { width: 12, height: 14 },
  };

  return dimensions[size] || dimensions.medium;
}

function resetComposer() {
  currentStep = 1;
  selectedTemplate = null;
  layoutSlots = [];
  customSlots = [];

  // Reset UI
  document
    .querySelectorAll(".composer-step")
    .forEach((step) => step.classList.add("d-none"));
  document.getElementById("step-1").classList.remove("d-none");

  document
    .querySelectorAll(".step")
    .forEach((step) => step.classList.remove("active"));
  document.querySelector('.step[data-step="1"]').classList.add("active");

  document
    .querySelectorAll(".template-card")
    .forEach((card) => card.classList.remove("active"));

  document.getElementById("prevStep").disabled = true;
  document.getElementById("nextStep").disabled = true;
  document.getElementById("nextStep").classList.remove("d-none");
  document.getElementById("createSection").classList.add("d-none");
}

function showNotification(message, type = "info") {
  // Create a simple notification
  const notification = document.createElement("div");
  notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
  notification.style.cssText =
    "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";
  notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

  document.body.appendChild(notification);

  // Auto-remove after 3 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      notification.remove();
    }
  }, 3000);
}

// Initialize when DOM is ready
document.addEventListener("DOMContentLoaded", function () {
  createSmartWidgetComposerOffcanvas();
});

// Add the missing openSmartWidgetComposer function
function openSmartWidgetComposer() {
  // Ensure the offcanvas exists before trying to open it
  if (!document.getElementById("smartWidgetComposerOffcanvas")) {
    createSmartWidgetComposerOffcanvas();
  }

  // Reset the composer to step 1
  resetComposer();

  const offcanvas = new bootstrap.Offcanvas(
    document.getElementById("smartWidgetComposerOffcanvas")
  );
  offcanvas.show();
}

function editSlot(index) {
  const slot = layoutSlots[index];

  // Create a simple modal for editing template slots
  const editModal = `
        <div class="modal fade" id="editSlotModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Widget Slot</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Title</label>
                            <input type="text" class="form-control" id="editSlotTitle" value="${
                              slot.title
                            }">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Size</label>
                            <select class="form-select" id="editSlotSize">
                                <option value="small" ${
                                  slot.size === "small" ? "selected" : ""
                                }>Small</option>
                                <option value="medium" ${
                                  slot.size === "medium" ? "selected" : ""
                                }>Medium</option>
                                <option value="large" ${
                                  slot.size === "large" ? "selected" : ""
                                }>Large</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="saveTemplateSlotEdit(${index})">Save Changes</button>
                    </div>
                </div>
            </div>
        </div>
    `;

  // Remove existing modal if any
  const existingModal = document.getElementById("editSlotModal");
  if (existingModal) existingModal.remove();

  document.body.insertAdjacentHTML("beforeend", editModal);
  const modal = new bootstrap.Modal(document.getElementById("editSlotModal"));
  modal.show();
}

function saveTemplateSlotEdit(index) {
  const title = document.getElementById("editSlotTitle").value;
  const size = document.getElementById("editSlotSize").value;

  layoutSlots[index] = {
    ...layoutSlots[index],
    title: title,
    size: size,
  };

  setupTemplatePreview();

  const modal = bootstrap.Modal.getInstance(
    document.getElementById("editSlotModal")
  );
  modal.hide();
}

// Tab Widget Configuration System
function configureTabWidget(widgetId) {
  // Initialize tab configuration if it doesn't exist
  if (!window.tabConfigurations) {
    window.tabConfigurations = {};
  }
  if (!window.tabConfigurations[widgetId]) {
    window.tabConfigurations[widgetId] = { tabs: [], tabPosition: "top" };
  }

  const currentConfig = window.tabConfigurations[widgetId];

  // Create or get the offcanvas container
  let offcanvasContainer = document.getElementById("offcanvasContainer");
  if (!offcanvasContainer) {
    offcanvasContainer = document.createElement("div");
    offcanvasContainer.id = "offcanvasContainer";
    document.body.appendChild(offcanvasContainer);
  }

  // Create the tab configuration offcanvas with side-by-side layout
  offcanvasContainer.innerHTML = `
    <div class="offcanvas offcanvas-end" tabindex="-1" id="tabConfigOffcanvas" aria-labelledby="tabConfigOffcanvasLabel">
      <div class="offcanvas-header">
        <div class="offcanvas-header-content">
          <h5 class="offcanvas-title" id="tabConfigOffcanvasLabel">
            <i class="fas fa-layer-group me-2"></i>Tab Configuration
          </h5>
          <p class="offcanvas-subtitle mb-0">Configure tabs, content, and layout for your tab container widget</p>
        </div>
        <button type="button" class="offcanvas-close" data-bs-dismiss="offcanvas" aria-label="Close">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="offcanvas-body p-0">
        <div class="tab-config-layout">
          <!-- Left Panel - Configuration -->
          <div class="tab-config-panel">
            <div class="config-panel-content">
              <!-- Tab Position Configuration -->
              <div class="config-section">
                <h6 class="section-title">
                  <i class="fas fa-compass me-2"></i>Tab Position
                </h6>
                <div class="tab-position-config p-3 mb-3">
                  <div class="row">
                    <div class="col-6">
                      <label class="form-label">Position</label>
                      <select class="form-select" onchange="updateTabPosition('${widgetId}', this.value)">
                        <option value="top" ${
                          currentConfig.tabPosition === "top" ? "selected" : ""
                        }>Top</option>
                        <option value="bottom" ${
                          currentConfig.tabPosition === "bottom"
                            ? "selected"
                            : ""
                        }>Bottom</option>
                      </select>
                    </div>
                    <div class="col-6 d-flex align-items-end">
                      <span class="badge bg-primary">
                        <i class="fas fa-info-circle me-1"></i>
                        ${currentConfig.tabs.length} Tab${
    currentConfig.tabs.length !== 1 ? "s" : ""
  }
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Tab Configuration List -->
              <div class="config-section">
                <div class="section-header d-flex justify-content-between align-items-center mb-3">
                  <h6 class="section-title mb-0">
                    <i class="fas fa-list me-2"></i>Tab Configuration
                  </h6>
                  <button class="btn btn-primary btn-sm" onclick="addNewTab('${widgetId}')">
                    <i class="fas fa-plus me-1"></i>Add Tab
                  </button>
                </div>
                
                <div class="tab-config-list" id="tab-config-list">
                  ${renderTabConfigList(currentConfig.tabs, widgetId)}
                </div>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="config-panel-footer">
              <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary flex-fill" data-bs-dismiss="offcanvas">
                  <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button class="btn btn-primary flex-fill" onclick="saveTabConfiguration('${widgetId}')">
                  <i class="fas fa-save me-1"></i>Save Configuration
                </button>
              </div>
            </div>
          </div>

          <!-- Right Panel - Live Preview -->
          <div class="tab-preview-panel">
            <div class="preview-panel-header">
              <h6 class="preview-title">
                <i class="fas fa-eye me-2"></i>Live Preview
              </h6>
              <p class="preview-subtitle">See how your tabs will look</p>
            </div>
            <div class="preview-panel-content">
              <div class="tab-preview-container" id="tab-preview-container">
                ${renderTabPreview(currentConfig.tabs, widgetId)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Show the offcanvas
  const offcanvasElement = document.getElementById("tabConfigOffcanvas");
  const offcanvas = new bootstrap.Offcanvas(offcanvasElement);
  offcanvas.show();

  // Refresh the preview initially
  setTimeout(() => refreshTabPreview(widgetId), 100);
}

function renderTabConfigList(tabs, widgetId) {
  return tabs
    .map((tab, index) => {
      return `
        <div class="tab-config-item p-3 mb-3 border rounded">
          <div class="d-flex align-items-center justify-content-between">
            <div class="flex-grow-1 me-3">
              <div class="row">
                <div class="col-md-12">
                  <label class="form-label">Tab Title</label>
                  <input 
                    type="text" 
                    class="form-control" 
                    value="${tab.title || ""}" 
                    onchange="updateTabTitle('${widgetId}', ${index}, this.value)"
                    placeholder="Enter tab title"
                  />
                </div>
              </div>
            </div>
            <div class="flex-shrink-0">
              <div class="btn-group" role="group">
                ${
                  index > 0
                    ? `<button class="btn btn-outline-secondary btn-sm" onclick="moveTab('${widgetId}', ${index}, 'up')" title="Move Up"><i class="fas fa-chevron-up"></i></button>`
                    : ""
                }
                ${
                  index < tabs.length - 1
                    ? `<button class="btn btn-outline-secondary btn-sm" onclick="moveTab('${widgetId}', ${index}, 'down')" title="Move Down"><i class="fas fa-chevron-down"></i></button>`
                    : ""
                }
                <button class="btn btn-outline-danger btn-sm" onclick="removeTab('${widgetId}', ${index})" title="Remove Tab"><i class="fas fa-trash"></i></button>
              </div>
            </div>
          </div>
          
          ${renderTabContentConfig(tab, widgetId, index)}
        </div>
      `;
    })
    .join("");
}

function refreshTabConfigOffcanvas(widgetId) {
  // Get the current configuration
  const currentConfig = window.tabConfigurations[widgetId];
  if (!currentConfig) return;

  // Update the tab configuration list
  const tabConfigList = document.getElementById("tab-config-list");
  if (tabConfigList) {
    tabConfigList.innerHTML = renderTabConfigList(currentConfig.tabs, widgetId);
  }

  // Update the tab count badge
  const tabCountBadge = document.querySelector(".badge.bg-primary");
  if (tabCountBadge) {
    tabCountBadge.innerHTML = `
      <i class="fas fa-info-circle me-1"></i>
      ${currentConfig.tabs.length} Tab${
      currentConfig.tabs.length !== 1 ? "s" : ""
    }
    `;
  }

  // Refresh the preview
  refreshTabPreview(widgetId);
}

function renderTabContentConfig(tab, widgetId, index) {
  const widgets = tab.widgets || [];

  return `
    <div class="tab-content-config">
      <!-- Clean Widget Management -->
      <div class="widget-management">
        <!-- Widget Add Bar -->
        <div class="widget-add-bar">
          <select id="widget-type-selector-${widgetId}-${index}" class="widget-type-select">
            <option value="kpi">📊 KPI Widget</option>
            <option value="chart">📈 Chart Widget</option>
            <option value="text">📝 Text Widget</option>
            <option value="table">📋 Table Widget</option>
          </select>
          <button class="btn btn-primary btn-sm btn-add-widget" onclick="addWidgetToTab('${widgetId}', ${index})" title="Add Selected Widget">
            <i class="fas fa-plus"></i>
            Add Widget
          </button>
        </div>
        
        <!-- Widget List -->
        <div class="widget-list-container ${
          widgets.length === 0 ? "empty" : ""
        }">
          ${
            widgets.length === 0
              ? renderEmptyWidgetState()
              : renderCleanWidgetsList(widgets, widgetId, index)
          }
        </div>
      </div>
    </div>
  `;
}

function renderEmptyWidgetState() {
  return `
    <div class="empty-widgets-state">
      <i class="fas fa-layer-group"></i>
      <h6>No widgets yet</h6>
      <p>Select a widget type above and click "Add Widget" to start building your tab layout.</p>
    </div>
  `;
}

function renderCleanWidgetsList(widgets, widgetId, tabIndex) {
  return widgets
    .map(
      (widget, widgetIndex) => `
    <div class="widget-card">
      <!-- Widget Card Header -->
      <div class="widget-card-header">
        <div class="widget-info">
          <span class="widget-type-badge ${widget.type}">
            ${getWidgetTypeIcon(widget.type)} ${getWidgetTypeLabel(widget.type)}
          </span>
          <input 
            type="text" 
            class="widget-title-input" 
            value="${widget.title || ""}" 
            onchange="updateTabWidgetTitle('${widgetId}', ${tabIndex}, ${widgetIndex}, this.value)"
            placeholder="Widget title..."
          />
        </div>
        <div class="widget-actions">
          <button class="btn-icon btn-danger" onclick="removeTabWidget('${widgetId}', ${tabIndex}, ${widgetIndex})" title="Remove widget">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      </div>
      
      <!-- Widget Configuration -->
      <div class="widget-card-main">
        <div class="widget-config-row">
          <div class="widget-config-group">
            <label class="widget-config-label">Widget Type</label>
            <select 
              class="widget-config-select" 
              onchange="updateTabWidgetType('${widgetId}', ${tabIndex}, ${widgetIndex}, this.value)"
            >
              <option value="kpi" ${
                widget.type === "kpi" ? "selected" : ""
              }>KPI Widget</option>
              <option value="chart" ${
                widget.type === "chart" ? "selected" : ""
              }>Chart Widget</option>
              <option value="table" ${
                widget.type === "table" ? "selected" : ""
              }>Table Widget</option>
              <option value="text" ${
                widget.type === "text" ? "selected" : ""
              }>Text Widget</option>
            </select>
          </div>
          <div class="widget-config-group">
            <label class="widget-config-label">Widget Size</label>
            <select 
              class="widget-config-select" 
              onchange="updateTabWidgetSize('${widgetId}', ${tabIndex}, ${widgetIndex}, this.value)"
            >
              <option value="small" ${
                widget.size === "small" ? "selected" : ""
              }>Small</option>
              <option value="medium" ${
                widget.size === "medium" ? "selected" : ""
              }>Medium</option>
              <option value="large" ${
                widget.size === "large" ? "selected" : ""
              }>Large</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  `
    )
    .join("");
}

// Helper functions for widget type display
function getWidgetTypeIcon(type) {
  const icons = {
    kpi: "📊",
    chart: "📈",
    text: "📝",
    table: "📋",
  };
  return icons[type] || "📊";
}

function getWidgetTypeLabel(type) {
  const labels = {
    kpi: "KPI",
    chart: "Chart",
    text: "Text",
    table: "Table",
  };
  return labels[type] || "KPI";
}

// Toggle widget configuration visibility
function toggleWidgetConfig(button) {
  const widgetCard = button.closest(".widget-card");
  const configSection = widgetCard.querySelector(".widget-specific-config");
  const icon = button.querySelector("i");

  if (configSection.style.display === "none") {
    configSection.style.display = "block";
    icon.className = "fas fa-chevron-up";
    button.title = "Hide configuration";
  } else {
    configSection.style.display = "none";
    icon.className = "fas fa-cog";
    button.title = "Configure widget";
  }
}

function renderSingleWidgetContentConfig(tab, widgetId, index) {
  const contentType = tab.type || "text";

  switch (contentType) {
    case "text":
      return `
        <label class="form-label">Text Content</label>
        <textarea 
          class="form-control" 
          rows="4" 
          onchange="updateTabContent('${widgetId}', ${index}, this.value)"
          placeholder="Enter your text content here..."
        >${tab.content || ""}</textarea>
      `;
    case "chart":
      return `
        <div class="row">
          <div class="col-md-6">
            <label class="form-label">Chart Type</label>
            <select 
              class="form-select" 
              onchange="updateTabChartType('${widgetId}', ${index}, this.value)"
            >
              <option value="pie" ${
                tab.chartType === "pie" ? "selected" : ""
              }>Pie Chart</option>
              <option value="bar" ${
                tab.chartType === "bar" ? "selected" : ""
              }>Bar Chart</option>
              <option value="line" ${
                tab.chartType === "line" ? "selected" : ""
              }>Line Chart</option>
            </select>
          </div>
          <div class="col-md-6">
            <label class="form-label">Chart Title</label>
            <input 
              type="text" 
              class="form-control" 
              value="${tab.chartTitle || ""}" 
              onchange="updateTabChartTitle('${widgetId}', ${index}, this.value)"
              placeholder="Enter chart title"
            />
          </div>
        </div>
      `;
    case "kpi":
      return `
        <div class="row">
          <div class="col-md-6">
            <label class="form-label">KPI Value</label>
            <input 
              type="text" 
              class="form-control" 
              value="${tab.kpiValue || ""}" 
              onchange="updateTabKpiValue('${widgetId}', ${index}, this.value)"
              placeholder="e.g., 1,234"
            />
          </div>
          <div class="col-md-6">
            <label class="form-label">KPI Label</label>
            <input 
              type="text" 
              class="form-control" 
              value="${tab.kpiLabel || ""}" 
              onchange="updateTabKpiLabel('${widgetId}', ${index}, this.value)"
              placeholder="e.g., Total Sales"
            />
          </div>
        </div>
      `;
    case "table":
      return `
        <label class="form-label">Table Data (JSON format)</label>
        <textarea 
          class="form-control" 
          rows="6" 
          onchange="updateTabTableData('${widgetId}', ${index}, this.value)"
          placeholder='[{"name": "John", "value": 100}, {"name": "Jane", "value": 200}]'
        >${tab.tableData || ""}</textarea>
      `;
    default:
      return "";
  }
}

function renderTabWidgetContentConfig(widget, widgetId, tabIndex, widgetIndex) {
  const baseConfig = `
    <div class="widget-config-row">
      <div class="widget-config-group">
        <label class="widget-config-label">Data Source</label>
        <input 
          type="text" 
          class="widget-config-input" 
          value="${widget.dataSource || ""}" 
          onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'dataSource', this.value)"
          placeholder="Enter data source..."
        />
      </div>
      <div class="widget-config-group">
        <label class="widget-config-label">Refresh Rate (seconds)</label>
        <input 
          type="number" 
          class="widget-config-input" 
          value="${widget.refreshRate || 30}" 
          onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'refreshRate', this.value)"
          min="5"
          max="3600"
        />
      </div>
    </div>
  `;

  switch (widget.type) {
    case "kpi":
      return (
        baseConfig +
        `
        <div class="widget-config-row">
          <div class="widget-config-group">
            <label class="widget-config-label">KPI Value Field</label>
            <input 
              type="text" 
              class="widget-config-input" 
              value="${widget.valueField || ""}" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'valueField', this.value)"
              placeholder="e.g., total_sales"
            />
          </div>
          <div class="widget-config-group">
            <label class="widget-config-label">Unit/Suffix</label>
            <input 
              type="text" 
              class="widget-config-input" 
              value="${widget.unit || ""}" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'unit', this.value)"
              placeholder="e.g., $, %, units"
            />
          </div>
        </div>
        <div class="widget-config-row">
          <div class="widget-config-group">
            <label class="widget-config-label">Target Value (optional)</label>
            <input 
              type="number" 
              class="widget-config-input" 
              value="${widget.target || ""}" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'target', this.value)"
              placeholder="Target value for comparison"
            />
          </div>
          <div class="widget-config-group">
            <label class="widget-config-label">Color Theme</label>
            <select 
              class="widget-config-select" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'colorTheme', this.value)"
            >
              <option value="blue" ${
                widget.colorTheme === "blue" ? "selected" : ""
              }>Blue</option>
              <option value="green" ${
                widget.colorTheme === "green" ? "selected" : ""
              }>Green</option>
              <option value="orange" ${
                widget.colorTheme === "orange" ? "selected" : ""
              }>Orange</option>
              <option value="red" ${
                widget.colorTheme === "red" ? "selected" : ""
              }>Red</option>
              <option value="purple" ${
                widget.colorTheme === "purple" ? "selected" : ""
              }>Purple</option>
            </select>
          </div>
        </div>
      `
      );

    case "chart":
      return (
        baseConfig +
        `
        <div class="widget-config-row">
          <div class="widget-config-group">
            <label class="widget-config-label">Chart Type</label>
            <select 
              class="widget-config-select" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'chartType', this.value)"
            >
              <option value="line" ${
                widget.chartType === "line" ? "selected" : ""
              }>Line Chart</option>
              <option value="bar" ${
                widget.chartType === "bar" ? "selected" : ""
              }>Bar Chart</option>
              <option value="pie" ${
                widget.chartType === "pie" ? "selected" : ""
              }>Pie Chart</option>
              <option value="area" ${
                widget.chartType === "area" ? "selected" : ""
              }>Area Chart</option>
              <option value="scatter" ${
                widget.chartType === "scatter" ? "selected" : ""
              }>Scatter Plot</option>
            </select>
          </div>
          <div class="widget-config-group">
            <label class="widget-config-label">X-Axis Field</label>
            <input 
              type="text" 
              class="widget-config-input" 
              value="${widget.xAxisField || ""}" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'xAxisField', this.value)"
              placeholder="e.g., date, category"
            />
          </div>
        </div>
        <div class="widget-config-row">
          <div class="widget-config-group">
            <label class="widget-config-label">Y-Axis Field</label>
            <input 
              type="text" 
              class="widget-config-input" 
              value="${widget.yAxisField || ""}" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'yAxisField', this.value)"
              placeholder="e.g., value, count"
            />
          </div>
          <div class="widget-config-group">
            <label class="widget-config-label">Group By (optional)</label>
            <input 
              type="text" 
              class="widget-config-input" 
              value="${widget.groupBy || ""}" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'groupBy', this.value)"
              placeholder="e.g., region, product"
            />
          </div>
        </div>
      `
      );

    case "table":
      return (
        baseConfig +
        `
        <div class="widget-config-row">
          <div class="widget-config-group">
            <label class="widget-config-label">Columns (comma-separated)</label>
            <input 
              type="text" 
              class="widget-config-input" 
              value="${widget.columns || ""}" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'columns', this.value)"
              placeholder="e.g., name, value, date"
            />
          </div>
          <div class="widget-config-group">
            <label class="widget-config-label">Rows Per Page</label>
            <select 
              class="widget-config-select" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'rowsPerPage', this.value)"
            >
              <option value="5" ${
                widget.rowsPerPage === "5" ? "selected" : ""
              }>5 rows</option>
              <option value="10" ${
                widget.rowsPerPage === "10" ? "selected" : ""
              }>10 rows</option>
              <option value="25" ${
                widget.rowsPerPage === "25" ? "selected" : ""
              }>25 rows</option>
              <option value="50" ${
                widget.rowsPerPage === "50" ? "selected" : ""
              }>50 rows</option>
            </select>
          </div>
        </div>
        <div class="widget-config-row">
          <div class="widget-config-group">
            <label class="widget-config-label">Sort By</label>
            <input 
              type="text" 
              class="widget-config-input" 
              value="${widget.sortBy || ""}" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'sortBy', this.value)"
              placeholder="Column to sort by"
            />
          </div>
          <div class="widget-config-group">
            <label class="widget-config-label">Sort Order</label>
            <select 
              class="widget-config-select" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'sortOrder', this.value)"
            >
              <option value="asc" ${
                widget.sortOrder === "asc" ? "selected" : ""
              }>Ascending</option>
              <option value="desc" ${
                widget.sortOrder === "desc" ? "selected" : ""
              }>Descending</option>
            </select>
          </div>
        </div>
      `
      );

    case "text":
      return (
        baseConfig +
        `
        <div class="widget-config-row">
          <div class="widget-config-group full-width">
            <label class="widget-config-label">Text Content</label>
            <textarea 
              class="widget-config-textarea" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'content', this.value)"
              placeholder="Enter your text content here..."
              rows="4"
            >${widget.content || ""}</textarea>
          </div>
        </div>
        <div class="widget-config-row">
          <div class="widget-config-group">
            <label class="widget-config-label">Text Alignment</label>
            <select 
              class="widget-config-select" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'textAlign', this.value)"
            >
              <option value="left" ${
                widget.textAlign === "left" ? "selected" : ""
              }>Left</option>
              <option value="center" ${
                widget.textAlign === "center" ? "selected" : ""
              }>Center</option>
              <option value="right" ${
                widget.textAlign === "right" ? "selected" : ""
              }>Right</option>
            </select>
          </div>
          <div class="widget-config-group">
            <label class="widget-config-label">Font Size</label>
            <select 
              class="widget-config-select" 
              onchange="updateTabWidgetProperty('${widgetId}', ${tabIndex}, ${widgetIndex}, 'fontSize', this.value)"
            >
              <option value="small" ${
                widget.fontSize === "small" ? "selected" : ""
              }>Small</option>
              <option value="medium" ${
                widget.fontSize === "medium" ? "selected" : ""
              }>Medium</option>
              <option value="large" ${
                widget.fontSize === "large" ? "selected" : ""
              }>Large</option>
            </select>
          </div>
        </div>
      `
      );

    default:
      return baseConfig;
  }
}

// Content Type Management Functions
function setTabContentType(widgetId, tabIndex, contentType) {
  if (
    !window.tabConfigurations[widgetId] ||
    !window.tabConfigurations[widgetId].tabs[tabIndex]
  ) {
    return;
  }

  const tab = window.tabConfigurations[widgetId].tabs[tabIndex];

  if (contentType === "multi") {
    tab.type = "multi-widget";
    tab.layout = "single-column";
    tab.widgets = tab.widgets || [];

    if (tab.content || tab.chartType || tab.kpiValue) {
      const existingWidget = {
        id: `widget-${Date.now()}`,
        type: tab.originalType || "kpi",
        title: tab.title || "Widget 1",
        size: "medium",
      };

      if (tab.content) existingWidget.content = tab.content;
      if (tab.chartType) existingWidget.chartType = tab.chartType;
      if (tab.chartTitle) existingWidget.chartTitle = tab.chartTitle;
      if (tab.kpiValue) existingWidget.kpiValue = tab.kpiValue;
      if (tab.kpiLabel) existingWidget.kpiLabel = tab.kpiLabel;
      if (tab.tableData) existingWidget.tableData = tab.tableData;

      tab.widgets = [existingWidget];
    }
  } else {
    tab.type = "text";
    tab.content = "";
  }

  refreshTabConfigOffcanvas(widgetId);
}

function setTabLayout(widgetId, tabIndex, layout) {
  if (
    !window.tabConfigurations[widgetId] ||
    !window.tabConfigurations[widgetId].tabs[tabIndex]
  ) {
    return;
  }

  window.tabConfigurations[widgetId].tabs[tabIndex].layout = layout;
  refreshTabConfigOffcanvas(widgetId);
}

// Widget Management Functions
function addWidgetToTab(widgetId, tabIndex) {
  if (
    !window.tabConfigurations[widgetId] ||
    !window.tabConfigurations[widgetId].tabs[tabIndex]
  ) {
    return;
  }

  const tab = window.tabConfigurations[widgetId].tabs[tabIndex];
  if (!tab.widgets) {
    tab.widgets = [];
  }

  // Get the selected widget type from the dropdown
  const selectorId = `widget-type-selector-${widgetId}-${tabIndex}`;
  const selector = document.getElementById(selectorId);
  const selectedType = selector ? selector.value : "kpi";

  const newWidget = {
    id: `widget-${Date.now()}`,
    type: selectedType,
    title: `Widget ${tab.widgets.length + 1}`,
    size: "medium",
  };

  // Set default values based on the widget type
  switch (selectedType) {
    case "kpi":
      newWidget.kpiValue = "1,234";
      newWidget.kpiLabel = "Sample KPI";
      break;
    case "chart":
      newWidget.chartType = "pie";
      newWidget.chartTitle = "Sample Chart";
      break;
    case "text":
      newWidget.content = "Sample text content";
      break;
    case "table":
      newWidget.tableData = '[{"name": "Item 1", "value": 100}]';
      break;
    default:
      // Fallback to KPI if type is unknown
      newWidget.type = "kpi";
      newWidget.kpiValue = "1,234";
      newWidget.kpiLabel = "Sample KPI";
  }

  tab.widgets.push(newWidget);
  refreshTabConfigOffcanvas(widgetId);
}

function removeTabWidget(widgetId, tabIndex, widgetIndex) {
  if (
    !window.tabConfigurations[widgetId] ||
    !window.tabConfigurations[widgetId].tabs[tabIndex] ||
    !window.tabConfigurations[widgetId].tabs[tabIndex].widgets[widgetIndex]
  ) {
    return;
  }

  window.tabConfigurations[widgetId].tabs[tabIndex].widgets.splice(
    widgetIndex,
    1
  );
  refreshTabConfigOffcanvas(widgetId);
}

function moveTabWidget(widgetId, tabIndex, widgetIndex, direction) {
  const widgets = window.tabConfigurations[widgetId]?.tabs[tabIndex]?.widgets;
  if (!widgets) return;

  const newIndex = direction === "up" ? widgetIndex - 1 : widgetIndex + 1;
  if (newIndex < 0 || newIndex >= widgets.length) return;

  [widgets[widgetIndex], widgets[newIndex]] = [
    widgets[newIndex],
    widgets[widgetIndex],
  ];
  refreshTabConfigOffcanvas(widgetId);
}

// Widget Property Update Functions
function updateTabWidgetType(widgetId, tabIndex, widgetIndex, type) {
  const widget =
    window.tabConfigurations[widgetId]?.tabs[tabIndex]?.widgets[widgetIndex];
  if (!widget) return;

  widget.type = type;

  // Set default values based on type
  switch (type) {
    case "kpi":
      widget.kpiValue = widget.kpiValue || "1,234";
      widget.kpiLabel = widget.kpiLabel || "Sample KPI";
      break;
    case "chart":
      widget.chartType = widget.chartType || "pie";
      widget.chartTitle = widget.chartTitle || "Sample Chart";
      break;
    case "text":
      widget.content = widget.content || "Sample text content";
      break;
    case "table":
      widget.tableData =
        widget.tableData || '[{"name": "Item 1", "value": 100}]';
      break;
  }

  refreshTabConfigOffcanvas(widgetId);
}

function updateTabWidgetTitle(widgetId, tabIndex, widgetIndex, title) {
  const widget =
    window.tabConfigurations[widgetId]?.tabs[tabIndex]?.widgets[widgetIndex];
  if (!widget) return;

  widget.title = title;
  refreshTabConfigOffcanvas(widgetId);
}

function updateTabWidgetSize(widgetId, tabIndex, widgetIndex, size) {
  const widget =
    window.tabConfigurations[widgetId]?.tabs[tabIndex]?.widgets[widgetIndex];
  if (!widget) return;

  widget.size = size;
  refreshTabConfigOffcanvas(widgetId);
}

function updateTabWidgetKpiValue(widgetId, tabIndex, widgetIndex, value) {
  const widget =
    window.tabConfigurations[widgetId]?.tabs[tabIndex]?.widgets[widgetIndex];
  if (!widget) return;

  widget.kpiValue = value;
}

function updateTabWidgetKpiLabel(widgetId, tabIndex, widgetIndex, label) {
  const widget =
    window.tabConfigurations[widgetId]?.tabs[tabIndex]?.widgets[widgetIndex];
  if (!widget) return;

  widget.kpiLabel = label;
}

function updateTabWidgetChartType(widgetId, tabIndex, widgetIndex, chartType) {
  const widget =
    window.tabConfigurations[widgetId]?.tabs[tabIndex]?.widgets[widgetIndex];
  if (!widget) return;

  widget.chartType = chartType;
  refreshTabConfigOffcanvas(widgetId);
}

function updateTabWidgetChartTitle(widgetId, tabIndex, widgetIndex, title) {
  const widget =
    window.tabConfigurations[widgetId]?.tabs[tabIndex]?.widgets[widgetIndex];
  if (!widget) return;

  widget.chartTitle = title;
}

function updateTabWidgetTextContent(widgetId, tabIndex, widgetIndex, content) {
  const widget =
    window.tabConfigurations[widgetId]?.tabs[tabIndex]?.widgets[widgetIndex];
  if (!widget) return;

  widget.content = content;
}

function updateTabWidgetTableData(widgetId, tabIndex, widgetIndex, data) {
  const widget =
    window.tabConfigurations[widgetId]?.tabs[tabIndex]?.widgets[widgetIndex];
  if (!widget) return;

  widget.tableData = data;
}

// Generic function to update any widget property
function updateTabWidgetProperty(
  widgetId,
  tabIndex,
  widgetIndex,
  property,
  value
) {
  const widget =
    window.tabConfigurations[widgetId]?.tabs[tabIndex]?.widgets[widgetIndex];
  if (!widget) return;

  widget[property] = value;

  // Save the configuration
  const widgetElement = getWidgetById(widgetId);
  if (widgetElement) {
    widgetElement.tabs = window.tabConfigurations[widgetId].tabs;
    saveWidgets();
  }
}

// Preview and Rendering Functions
function renderMultiWidgetPreview(tab) {
  const widgets = tab.widgets || [];
  const layout = tab.layout || "single-column";

  if (widgets.length === 0) {
    return `
      <div class="text-center text-muted py-4">
        <i class="fas fa-layer-group"></i>
        <p class="mt-2">No widgets configured.<br>Add widgets to see the preview.</p>
      </div>
    `;
  }

  const layoutClass = getLayoutClass(layout);

  return `
    <div class="multi-widget-preview ${layoutClass}">
      ${widgets.map((widget) => renderWidgetPreview(widget)).join("")}
    </div>
  `;
}

function getLayoutClass(layout) {
  switch (layout) {
    case "single-column":
      return "single-column-layout";
    case "two-column":
      return "two-column-layout";
    case "grid-2x2":
      return "grid-2x2-layout";
    case "sidebar-main":
      return "sidebar-main-layout";
    default:
      return "single-column-layout";
  }
}

function renderWidgetPreview(widget) {
  const sizeClass =
    widget.size === "large"
      ? "widget-large"
      : widget.size === "small"
      ? "widget-small"
      : "widget-medium";

  let content = "";
  switch (widget.type) {
    case "kpi":
      content = `
        <div class="card text-center">
          <div class="card-body">
            <h6 class="card-title">${widget.kpiValue || "1,234"}</h6>
            <p class="card-text small">${widget.kpiLabel || "Sample KPI"}</p>
          </div>
        </div>
      `;
      break;
    case "chart":
      content = `
        <div class="text-center">
          <h6 class="mb-2">${widget.chartTitle || "Chart"}</h6>
          <div class="chart-placeholder bg-light p-2 rounded">
            <i class="las la-chart-${
              widget.chartType || "pie"
            }" style="font-size: 2rem; color: #6c757d;"></i>
          </div>
        </div>
      `;
      break;
    case "text":
      content = `
        <div class="text-content">
          <h6>${widget.title}</h6>
          <p class="small">${widget.content || "Text content..."}</p>
        </div>
      `;
      break;
    case "table":
      content = `
        <div class="table-responsive">
          <h6 class="mb-2">${widget.title}</h6>
          <table class="table table-sm">
            <thead><tr><th>Col 1</th><th>Col 2</th></tr></thead>
            <tbody><tr><td>Data</td><td>Data</td></tr></tbody>
          </table>
        </div>
      `;
      break;
  }

  return `
    <div class="widget-preview-item ${sizeClass}">
      ${content}
    </div>
  `;
}

function renderTabPreview(tabs, widgetId) {
  if (!tabs || tabs.length === 0) return "";

  const config = window.tabConfigurations[widgetId];
  const tabPosition = config?.tabPosition || "top";

  // Determine the correct CSS class for tab positioning
  let tabPositionClass = "";
  switch (tabPosition) {
    case "bottom":
      tabPositionClass = "bottom-tabs";
      break;
    case "left":
      tabPositionClass = "left-tabs";
      break;
    case "right":
      tabPositionClass = "right-tabs";
      break;
    default:
      tabPositionClass = ""; // top is default, no class needed
  }

  // Default icons for tabs (can be customized later)
  const defaultIcons = [
    "las la-chart-line",
    "las la-crystal-ball",
    "las la-table",
    "las la-cog",
    "las la-info-circle",
    "las la-star",
    "las la-heart",
    "las la-bookmark",
  ];

  return `
    <div class="tab-widget-preview ${tabPositionClass}">
      
    <ul class="nav nav-tabs" role="tablist">
      ${tabs
        .map((tab, index) => {
          // For bottom tabs, use Market Insights style with icons
          if (tabPosition === "bottom") {
            const iconClass =
              tab.icon || defaultIcons[index % defaultIcons.length];
            return `
                <li class="nav-item" role="presentation">
                  <button class="nav-link d-flex flex-column align-items-center py-2 ${
                    index === 0 ? "active" : ""
                  }" 
                          id="preview-tab-${tab.id}" 
                          data-bs-toggle="tab" 
                          data-bs-target="#preview-pane-${tab.id}" 
                          type="button" 
                          role="tab">
                    <i class="${iconClass}" style="font-size: 18px; margin-bottom: 4px;"></i>
                    <span style="font-size: 11px; line-height: 1.2;">${
                      tab.title
                    }</span>
                  </button>
                </li>
              `;
          } else {
            // Regular tabs for top, left, right positions
            return `
                <li class="nav-item" role="presentation">
                  <button class="nav-link ${
                    index === 0 ? "active" : ""
                  }" id="preview-tab-${
              tab.id
            }" data-bs-toggle="tab" data-bs-target="#preview-pane-${
              tab.id
            }" type="button" role="tab">
                    ${tab.title}
                  </button>
                </li>
              `;
          }
        })
        .join("")}
    </ul>
    <div class="tab-content">
      ${tabs
        .map(
          (tab, index) => `
        <div class="tab-pane fade ${
          index === 0 ? "show active" : ""
        }" id="preview-pane-${tab.id}" role="tabpanel">
          ${renderTabContentPreview(tab)}
        </div>
      `
        )
        .join("")}
    </div>
    </div>
  `;
}

function renderTabContentPreview(tab) {
  return renderMultiWidgetPreview(tab);
}

function renderSingleWidgetPreview(tab) {
  switch (tab.type) {
    case "text":
      return `<div class="text-content">${
        tab.content || "Text content will appear here"
      }</div>`;
    case "chart":
      return `
        <div class="text-center">
          <h6>${tab.chartTitle || "Chart Title"}</h6>
          <div class="chart-placeholder bg-light p-4 rounded">
            <i class="las la-chart-${
              tab.chartType || "pie"
            }" style="font-size: 3rem; color: #6c757d;"></i>
            <p class="mt-2 mb-0">${tab.chartType || "pie"} Chart Preview</p>
          </div>
        </div>
      `;
    case "kpi":
      return `
        <div class="text-center">
          <div class="card">
            <div class="card-body">
              <h5 class="card-title">${tab.kpiValue || "1,234"}</h5>
              <p class="card-text">${tab.kpiLabel || "Sample KPI"}</p>
            </div>
          </div>
        </div>
      `;
    case "table":
      return `
        <div class="table-responsive">
          <table class="table table-striped table-sm">
            <thead><tr><th>Column 1</th><th>Column 2</th><th>Column 3</th></tr></thead>
            <tbody>
              <tr><td>Data 1</td><td>Data 2</td><td>Data 3</td></tr>
              <tr><td>Data 4</td><td>Data 5</td><td>Data 6</td></tr>
            </tbody>
          </table>
        </div>
      `;
    default:
      return `<p>Content preview for ${tab.type}</p>`;
  }
}

function refreshTabPreview(widgetId) {
  const preview = document.getElementById("tab-preview-container");
  if (preview && window.tabConfigurations[widgetId]) {
    preview.innerHTML = renderTabPreview(
      window.tabConfigurations[widgetId].tabs,
      widgetId
    );
  }
}

function renderTabWidget(config) {
  if (!config || !config.tabs || config.tabs.length === 0) {
    return `
      <div class="text-center text-muted p-4">
        <i class="fas fa-plus-circle" style="font-size: 2rem;"></i>
        <p class="mt-2">No tabs configured</p>
      </div>
    `;
  }

  const tabPosition = config.tabPosition || "top";

  // Determine the correct CSS class for tab positioning
  let tabPositionClass = "";
  switch (tabPosition) {
    case "bottom":
      tabPositionClass = "bottom-tabs";
      break;
    case "left":
      tabPositionClass = "left-tabs";
      break;
    case "right":
      tabPositionClass = "right-tabs";
      break;
    default:
      tabPositionClass = ""; // top is default, no class needed
  }

  // Default icons for tabs (can be customized later)
  const defaultIcons = [
    "las la-chart-line",
    "las la-crystal-ball",
    "las la-table",
    "las la-cog",
    "las la-info-circle",
    "las la-star",
    "las la-heart",
    "las la-bookmark",
  ];

  return `
    <div class="tab-widget-container ${tabPositionClass}">
      <ul class="nav nav-tabs" role="tablist" ${
        tabPosition === "bottom" ? 'style="margin: 0; border-radius: 0"' : ""
      }>
        ${config.tabs
          .map((tab, index) => {
            // For bottom tabs, use Market Insights style with images or icons
            if (tabPosition === "bottom") {
              // Check if tab has an image property (Market Insights style)
              if (tab.image) {
                return `
                    <li class="nav-item" role="presentation">
                      <button class="nav-link d-flex flex-column align-items-center py-2 ${
                        index === 0 ? "active" : ""
                      }" 
                              id="tab-${tab.id}" 
                              data-bs-toggle="tab" 
                              data-bs-target="#pane-${tab.id}" 
                              type="button" 
                              role="tab">
                        <img src="${tab.image}" 
                             alt="${tab.title}"
                             style="height: 24px; width: auto; margin-bottom: 5px;" />
                        <span style="font-size: 11px">${tab.title}</span>
                      </button>
                    </li>
                  `;
              } else {
                // Fallback to icons for bottom tabs
                const iconClass =
                  tab.icon || defaultIcons[index % defaultIcons.length];
                return `
                    <li class="nav-item" role="presentation">
                      <button class="nav-link d-flex flex-column align-items-center py-2 ${
                        index === 0 ? "active" : ""
                      }" 
                              id="tab-${tab.id}" 
                              data-bs-toggle="tab" 
                              data-bs-target="#pane-${tab.id}" 
                              type="button" 
                              role="tab">
                        <i class="${iconClass}" style="font-size: 18px; margin-bottom: 4px;"></i>
                        <span style="font-size: 11px; line-height: 1.2;">${
                          tab.title
                        }</span>
                      </button>
                    </li>
                  `;
              }
            } else {
              // Regular tabs for top, left, right positions
              return `
                  <li class="nav-item" role="presentation">
                    <button class="nav-link ${
                      index === 0 ? "active" : ""
                    }" id="tab-${
                tab.id
              }" data-bs-toggle="tab" data-bs-target="#pane-${
                tab.id
              }" type="button" role="tab">
                    ${tab.title}
                  </button>
                </li>
              `;
            }
          })
          .join("")}
      </ul>
      <div class="tab-content">
        ${config.tabs
          .map(
            (tab, index) => `
          <div class="tab-pane fade ${
            index === 0 ? "show active" : ""
          }" id="pane-${tab.id}" role="tabpanel">
            ${renderTabContentForWidget(tab)}
          </div>
        `
          )
          .join("")}
      </div>
    </div>
  `;
}

function renderTabContentForWidget(tab) {
  return renderMultiWidgetContent(tab);
}

function renderMultiWidgetContent(tab) {
  const widgets = tab.widgets || [];
  const tabId = tab.id || `tab-${Date.now()}`;

  if (widgets.length === 0) {
    return `
      <div class="text-center text-muted py-4">
        <i class="fas fa-layer-group" style="font-size: 2rem;"></i>
        <p class="mt-2">No widgets in this tab yet.<br>Use the configuration panel to add widgets.</p>
      </div>
    `;
  }

  // Create GridStack container for this tab
  return `
    <div class="tab-gridstack-container" id="gridstack-${tabId}" style="min-height: 300px;">
      ${widgets.map((widget) => renderWidgetInTabGrid(widget)).join("")}
    </div>
  `;
}

function renderWidgetInTabGrid(widget) {
  const widgetId = widget.id || `widget-${Date.now()}`;
  const x = widget.x || 0;
  const y = widget.y || 0;
  const w = widget.w || 3;
  const h = widget.h || 2;

  return `
    <div class="grid-stack-item" gs-x="${x}" gs-y="${y}" gs-w="${w}" gs-h="${h}" gs-id="${widgetId}">
      <div class="grid-stack-item-content">
        <div class="widget-content">
          ${renderWidgetContentInTab(widget)}
        </div>
      </div>
    </div>
  `;
}

function renderWidgetContentInTab(widget) {
  switch (widget.type) {
    case "kpi":
      return `
        <div class="kpi-widget text-center h-100 d-flex flex-column justify-content-center">
          <h3 class="kpi-value text-primary mb-1">${
            widget.kpiValue || "1,234"
          }</h3>
          <p class="kpi-label text-muted mb-0">${
            widget.kpiLabel || "Sample KPI"
          }</p>
        </div>
      `;
    case "chart":
      return `
        <div class="chart-widget h-100 d-flex flex-column">
          <h6 class="widget-title mb-2">${
            widget.chartTitle || widget.title || "Chart"
          }</h6>
          <div class="chart-placeholder bg-light flex-grow-1 d-flex align-items-center justify-content-center rounded">
            <div class="text-center">
              <i class="las la-chart-${
                widget.chartType || "pie"
              }" style="font-size: 2rem; color: #6c757d;"></i>
              <p class="mt-1 mb-0 small">${widget.chartType || "pie"} Chart</p>
            </div>
          </div>
        </div>
      `;
    case "table":
      return `
        <div class="table-widget h-100 d-flex flex-column">
          <h6 class="widget-title mb-2">${widget.title || "Data Table"}</h6>
          <div class="table-responsive flex-grow-1">
            <table class="table table-striped table-sm">
              <thead><tr><th>Column 1</th><th>Column 2</th><th>Column 3</th></tr></thead>
              <tbody>
                <tr><td>Data 1</td><td>Data 2</td><td>Data 3</td></tr>
                <tr><td>Data 4</td><td>Data 5</td><td>Data 6</td></tr>
              </tbody>
            </table>
          </div>
        </div>
      `;
    case "text":
      return `
        <div class="text-widget h-100 d-flex flex-column">
          <h6 class="widget-title mb-2">${widget.title || "Text Content"}</h6>
          <div class="text-content flex-grow-1">
            ${widget.content || "Text content will appear here"}
          </div>
        </div>
      `;
    default:
      return `
        <div class="widget-placeholder h-100 d-flex align-items-center justify-content-center">
          <div class="text-center text-muted">
            <i class="fas fa-cube" style="font-size: 2rem;"></i>
            <p class="mt-2 mb-0">${widget.type} Widget</p>
          </div>
        </div>
      `;
  }
}

function updateTabTitle(widgetId, tabIndex, title) {
  if (window.tabConfigurations[widgetId]?.tabs[tabIndex]) {
    window.tabConfigurations[widgetId].tabs[tabIndex].title = title;
    refreshTabPreview(widgetId);
  }
}

function updateTabType(widgetId, tabIndex, type) {
  // This function is no longer needed since all tabs are widget containers
  // Keeping for backward compatibility but it doesn't do anything
  console.log(
    "updateTabType called but no longer needed - all tabs are widget containers"
  );
}

function addNewTab(widgetId) {
  if (!window.tabConfigurations[widgetId]) return;

  const newTab = {
    id: `tab${Date.now()}`,
    title: `Tab ${window.tabConfigurations[widgetId].tabs.length + 1}`,
    widgets: [],
  };

  window.tabConfigurations[widgetId].tabs.push(newTab);
  refreshTabConfigOffcanvas(widgetId);
}

function removeTab(widgetId, tabIndex) {
  if (!window.tabConfigurations[widgetId]) return;
  if (window.tabConfigurations[widgetId].tabs.length <= 1) {
    alert("You must have at least one tab.");
    return;
  }

  window.tabConfigurations[widgetId].tabs.splice(tabIndex, 1);
  refreshTabConfigOffcanvas(widgetId);
}

function moveTab(widgetId, tabIndex, direction) {
  if (!window.tabConfigurations[widgetId]) return;

  const tabs = window.tabConfigurations[widgetId].tabs;
  const newIndex = direction === "up" ? tabIndex - 1 : tabIndex + 1;

  if (newIndex < 0 || newIndex >= tabs.length) return;

  // Swap tabs
  [tabs[tabIndex], tabs[newIndex]] = [tabs[newIndex], tabs[tabIndex]];
  refreshTabConfigOffcanvas(widgetId);
}

function updateTabPosition(widgetId, position) {
  if (!window.tabConfigurations[widgetId]) return;

  window.tabConfigurations[widgetId].tabPosition = position;
  refreshTabConfigOffcanvas(widgetId);
}

function saveTabConfiguration(widgetId) {
  // Update the actual widget in the DOM
  const widgetElement = document.getElementById(widgetId);
  if (widgetElement) {
    const widgetContent = widgetElement.querySelector(".widget-content");
    if (widgetContent && window.tabConfigurations[widgetId]) {
      widgetContent.innerHTML = renderTabWidget(
        window.tabConfigurations[widgetId]
      );

      // Initialize GridStack for each tab that has multi-widget content
      setTimeout(() => {
        ensureTabGridStacksInitialized(widgetId);
      }, 100);
    }
  }

  // Close the offcanvas
  const offcanvasElement = document.getElementById("tabConfigOffcanvas");
  if (offcanvasElement) {
    const offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
    if (offcanvas) {
      offcanvas.hide();
    }
  }

  // Show success notification
  showNotification("Tab configuration saved successfully!", "success");
}

// Initialize GridStack for tabs
function initializeTabGridStack(tabId) {
  const gridElement = document.getElementById(`gridstack-${tabId}`);
  if (!gridElement) return;

  // Check if GridStack is already initialized
  if (gridElement.gridstack) {
    return gridElement.gridstack;
  }

  const grid = GridStack.init(
    {
      cellHeight: 60,
      margin: 10,
      resizable: {
        handles: "e, se, s, sw, w",
      },
      draggable: {
        handle: ".grid-stack-item-content",
      },
    },
    gridElement
  );

  // Save layout changes
  grid.on("change", function (event, items) {
    if (items) {
      items.forEach((item) => {
        updateTabWidgetPosition(tabId, item.id, {
          x: item.x,
          y: item.y,
          w: item.w,
          h: item.h,
        });
      });
    }
  });

  return grid;
}

// Helper function to ensure all tab grids are properly initialized
function ensureTabGridStacksInitialized(widgetId) {
  if (!window.tabConfigurations || !window.tabConfigurations[widgetId]) {
    return;
  }

  const config = window.tabConfigurations[widgetId];
  if (!config.tabs) return;

  config.tabs.forEach((tab) => {
    if (tab.widgets && tab.widgets.length > 0) {
      const gridElement = document.getElementById(`gridstack-${tab.id}`);
      if (gridElement && !gridElement.gridstack) {
        console.log(`Initializing GridStack for tab: ${tab.id}`);
        initializeTabGridStack(tab.id);
      }
    }
  });
}

function updateTabWidgetPosition(tabId, widgetId, position) {
  // Find the tab configuration and update widget position
  Object.keys(window.tabConfigurations || {}).forEach((containerWidgetId) => {
    const config = window.tabConfigurations[containerWidgetId];
    const tab = config.tabs.find((t) => t.id === tabId);
    if (tab && tab.widgets) {
      const widget = tab.widgets.find((w) => w.id === widgetId);
      if (widget) {
        widget.x = position.x;
        widget.y = position.y;
        widget.w = position.w;
        widget.h = position.h;
      }
    }
  });
}

function getTabPositionClass(position) {
  switch (position) {
    case "bottom":
      return "tabs-bottom";
    case "left":
      return "tabs-left";
    case "right":
      return "tabs-right";
    default:
      return "tabs-top";
  }
}

function showEnhancedWidgetSelector(category) {
  const widgetTypes = getWidgetsByCategory(category);
  const grid = document.getElementById("custom-layout-grid");
  const existingWidgetsOfCategory = customSlots.filter(
    (slot) => slot.category === category
  );

  // CRITICAL FIX: Remove any existing widget selector cards to prevent duplicates
  const existingSelectors = grid.querySelectorAll(".widget-selector-card");
  existingSelectors.forEach((selector) => selector.remove());

  // Create enhanced widget selector card
  const selectorCard = document.createElement("div");
  selectorCard.className = "widget-selector-card enhanced";
  selectorCard.innerHTML = `
    <div class="selector-header">
      <h6><i class="las ${getCategoryIcon(category)} me-2"></i>Add Another ${
    category.charAt(0).toUpperCase() + category.slice(1)
  } Widget</h6>
      <button class="btn btn-sm btn-outline-secondary" onclick="cancelWidgetSelection()">
        <i class="las la-times"></i>
      </button>
    </div>
    ${
      existingWidgetsOfCategory.length > 0
        ? `
    <div class="existing-widgets-info">
      <small class="text-muted">
        <i class="las la-info-circle me-1"></i>
        You have ${existingWidgetsOfCategory.length} ${category} widget${
            existingWidgetsOfCategory.length > 1 ? "s" : ""
          } already. Add another one below:
      </small>
    </div>
    `
        : ""
    }
    <div class="widget-type-grid">
      ${widgetTypes
        .map(
          (widget) => `
        <div class="widget-type-option" onclick="addCustomWidget('${
          widget.type
        }', '${widget.label}', '${category}')">
          <div class="widget-type-icon">
            <i class="las ${getWidgetIcon(widget.type)}"></i>
          </div>
          <div class="widget-type-label">${widget.label}</div>
        </div>
      `
        )
        .join("")}
    </div>
  `;

  grid.appendChild(selectorCard);
}

// Template Management Functions - Prototype Implementation
function showSaveTemplateModal(widgetId) {
  const widget = document.getElementById(widgetId);
  if (!widget) return;

  // Extract widget configuration
  const widgetConfig = extractWidgetConfiguration(widgetId);

  const modalHTML = `
    <div class="modal fade" id="saveTemplateModal" tabindex="-1" aria-labelledby="saveTemplateModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="saveTemplateModalLabel">
              <i class="las la-save me-2"></i>Save as Template
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="templateName" class="form-label">Template Name</label>
              <input type="text" class="form-control" id="templateName" placeholder="Enter template name..." value="${
                widgetConfig.title
              } Template">
            </div>
            <div class="mb-3">
              <label for="templateDescription" class="form-label">Description (Optional)</label>
              <textarea class="form-control" id="templateDescription" rows="3" placeholder="Describe this template..."></textarea>
            </div>
            <div class="template-preview-card">
              <h6>Template Preview:</h6>
              <div class="template-preview-content">
                <div class="preview-item ${widgetConfig.type}">
                  <i class="las ${getWidgetIcon(widgetConfig.type)}"></i>
                  <span>${widgetConfig.title}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="saveWidgetAsTemplate('${widgetId}')">
              <i class="las la-save me-1"></i>Save Template
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Remove existing modal if any
  const existingModal = document.getElementById("saveTemplateModal");
  if (existingModal) existingModal.remove();

  document.body.insertAdjacentHTML("beforeend", modalHTML);
  const modal = new bootstrap.Modal(
    document.getElementById("saveTemplateModal")
  );
  modal.show();
}

function extractWidgetConfiguration(widgetId) {
  const widget = document.getElementById(widgetId);
  if (!widget) return null;

  const titleElement = widget.querySelector(".widget-title-text");
  const title = titleElement ? titleElement.textContent : "Untitled Widget";

  // Determine widget type from wrapper class
  let type = "kpi"; // default
  if (widget.classList.contains("chart-widget-wrapper")) {
    if (widget.querySelector(".pie-chart")) type = "pie-chart";
    else if (widget.querySelector(".bar-chart")) type = "bar-chart";
    else if (widget.querySelector(".line-chart")) type = "line-chart";
    else type = "chart";
  } else if (widget.classList.contains("table-widget-wrapper")) {
    type = "handsontable";
  } else if (widget.classList.contains("text-widget-wrapper")) {
    type = "text";
  } else if (widget.classList.contains("media-widget-wrapper")) {
    type = "image";
  } else if (widget.classList.contains("tab-container-wrapper")) {
    type = "tab-container";
  } else if (widget.classList.contains("price-chart-wrapper")) {
    type = "price-chart";
  }

  return {
    id: widgetId,
    title: title,
    type: type,
    size: "medium", // default size
    createdAt: new Date().toISOString(),
  };
}

function saveWidgetAsTemplate(widgetId) {
  const templateName = document.getElementById("templateName").value.trim();
  const templateDescription = document
    .getElementById("templateDescription")
    .value.trim();

  if (!templateName) {
    alert("Please enter a template name");
    return;
  }

  const widgetConfig = extractWidgetConfiguration(widgetId);
  if (!widgetConfig) {
    alert("Could not extract widget configuration");
    return;
  }

  const template = {
    id: `custom-${Date.now()}`,
    name: templateName,
    description: templateDescription,
    type: "custom",
    isCustom: true,
    slots: [widgetConfig],
    createdAt: new Date().toISOString(),
  };

  // Save to localStorage (prototype implementation)
  let savedTemplates = getSavedTemplates();
  savedTemplates.unshift(template); // Add to beginning of array
  localStorage.setItem(
    "smartWidgetComposer_savedTemplates",
    JSON.stringify(savedTemplates)
  );

  // Close modal
  const modal = bootstrap.Modal.getInstance(
    document.getElementById("saveTemplateModal")
  );
  modal.hide();

  // Show success notification
  showNotification(`Template "${templateName}" saved successfully!`, "success");

  console.log("Template saved:", template);
}

function getSavedTemplates() {
  try {
    const saved = localStorage.getItem("smartWidgetComposer_savedTemplates");
    return saved ? JSON.parse(saved) : [];
  } catch (error) {
    console.error("Error loading saved templates:", error);
    return [];
  }
}

function deleteSavedTemplate(templateId) {
  let savedTemplates = getSavedTemplates();
  savedTemplates = savedTemplates.filter(
    (template) => template.id !== templateId
  );
  localStorage.setItem(
    "smartWidgetComposer_savedTemplates",
    JSON.stringify(savedTemplates)
  );

  // Refresh template grid if composer is open
  updateTemplateGrid();
}

function updateTemplateGrid() {
  const templateGrid = document.querySelector(".template-grid");
  if (!templateGrid) return;

  const savedTemplates = getSavedTemplates();

  // Create saved templates HTML
  const savedTemplatesHTML = savedTemplates
    .map(
      (template) => `
    <div class="template-card custom-template" data-template="${template.id}">
      <div class="template-preview">
        <div class="preview-grid custom-preview">
          ${
            template.type === "section"
              ? // Section template preview - show multiple widgets in grid
                template.slots
                  .slice(0, 4)
                  .map(
                    (slot) => `
              <div class="preview-item ${slot.type || "kpi"}">
                <i class="las ${getWidgetIcon(slot.type || "kpi")}"></i>
              </div>
            `
                  )
                  .join("")
              : // Widget template preview - show single widget
                `<div class="preview-item ${template.slots[0]?.type || "kpi"}">
              <i class="las ${getWidgetIcon(
                template.slots[0]?.type || "kpi"
              )}"></i>
            </div>`
          }
        </div>
      </div>
      <div class="template-info">
        <h6>${template.name}</h6>
        <small class="text-muted">${
          template.description ||
          (template.type === "section" ? "Section template" : "Custom template")
        }</small>
        <div class="template-meta">
          <span class="badge bg-${
            template.type === "section" ? "primary" : "secondary"
          } me-1">
            ${template.type === "section" ? "Section" : "Widget"}
          </span>
          <span class="text-muted small">${template.slots.length} item${
        template.slots.length !== 1 ? "s" : ""
      }</span>
        </div>
        <div class="template-actions mt-2">
          <button class="btn btn-sm btn-outline-danger" onclick="deleteSavedTemplate('${
            template.id
          }')" title="Delete Template">
            <i class="las la-trash"></i>
          </button>
        </div>
      </div>
    </div>
  `
    )
    .join("");

  // Get existing default templates
  const defaultTemplatesHTML = `
    <div class="template-card" data-template="executive">
      <div class="template-preview">
        <div class="preview-grid">
          <div class="preview-item kpi"></div>
          <div class="preview-item kpi"></div>
          <div class="preview-item chart"></div>
          <div class="preview-item table"></div>
        </div>
      </div>
      <div class="template-info">
        <h6>Executive Dashboard</h6>
        <small class="text-muted">KPIs, charts, and data tables</small>
      </div>
    </div>
    
    <div class="template-card" data-template="analytics">
      <div class="template-preview">
        <div class="preview-grid analytics">
          <div class="preview-item chart wide"></div>
          <div class="preview-item chart"></div>
          <div class="preview-item chart"></div>
        </div>
      </div>
      <div class="template-info">
        <h6>Analytics View</h6>
        <small class="text-muted">Multiple chart views</small>
      </div>
    </div>
    
    <div class="template-card" data-template="comparison">
      <div class="template-preview">
        <div class="preview-grid">
          <div class="preview-item chart"></div>
          <div class="preview-item chart"></div>
          <div class="preview-item table wide"></div>
        </div>
      </div>
      <div class="template-info">
        <h6>Comparison View</h6>
        <small class="text-muted">Side-by-side comparisons</small>
      </div>
    </div>
    
    <div class="template-card" data-template="report">
      <div class="template-preview">
        <div class="preview-grid">
          <div class="preview-item text wide"></div>
          <div class="preview-item chart"></div>
          <div class="preview-item table"></div>
        </div>
      </div>
      <div class="template-info">
        <h6>Report Layout</h6>
        <small class="text-muted">Text, charts, and tables</small>
      </div>
    </div>
    
    <div class="template-card" data-template="custom">
      <div class="template-preview">
        <div class="preview-grid custom-preview">
          <div class="preview-item empty">
            <i class="las la-plus"></i>
          </div>
        </div>
      </div>
      <div class="template-info">
        <h6>Custom Layout</h6>
        <small class="text-muted">Build from scratch</small>
      </div>
    </div>
  `;

  // Update template grid with saved templates first, then default templates
  templateGrid.innerHTML = savedTemplatesHTML + defaultTemplatesHTML;

  // Re-attach event listeners
  templateGrid.querySelectorAll(".template-card").forEach((card) => {
    card.addEventListener("click", function () {
      const templateId = this.dataset.template;

      // Remove active class from all cards
      templateGrid
        .querySelectorAll(".template-card")
        .forEach((c) => c.classList.remove("active"));

      // Add active class to clicked card
      this.classList.add("active");

      // Set selected template
      selectedTemplate = templateId;

      // Handle custom templates (both widget and section templates)
      if (templateId.startsWith("custom-")) {
        const savedTemplates = getSavedTemplates();
        const customTemplate = savedTemplates.find((t) => t.id === templateId);
        if (customTemplate) {
          if (customTemplate.type === "section") {
            // For section templates, set layout slots directly from the saved section configuration
            layoutSlots = [...customTemplate.slots];

            // Show notification that this is a section template
            showNotification(
              `Section template "${customTemplate.name}" selected with ${customTemplate.slots.length} widgets`,
              "success"
            );
          } else {
            // For widget templates, set layout slots to the custom template's slots
            layoutSlots = [...customTemplate.slots];

            showNotification(
              `Widget template "${customTemplate.name}" selected`,
              "success"
            );
          }
        }
      }

      // Enable next button
      document.getElementById("nextStep").disabled = false;
    });
  });
}

function showSaveSectionTemplateModal(sectionId) {
  const sectionElement = document.querySelector(
    `[data-section-id="${sectionId}"]`
  );
  if (!sectionElement) {
    console.error("Section not found:", sectionId);
    return;
  }

  const sectionConfig = JSON.parse(
    sectionElement.getAttribute("data-section-config") || "{}"
  );
  const sectionTitle = sectionConfig.title || "Untitled Section";

  // Show modal for template naming
  const modalHTML = `
    <div class="modal fade" id="saveSectionTemplateModal" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Save Section as Template</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="sectionTemplateName" class="form-label">Template Name</label>
              <input type="text" class="form-control" id="sectionTemplateName" value="${sectionTitle} Template" placeholder="Enter template name">
            </div>
            <div class="mb-3">
              <label for="sectionTemplateDescription" class="form-label">Description (Optional)</label>
              <textarea class="form-control" id="sectionTemplateDescription" rows="3" placeholder="Describe this section template..."></textarea>
            </div>
            <div class="template-preview-card">
              <h6>Section Preview:</h6>
              <div class="template-preview-content">
                <div class="preview-item section">
                  <i class="las la-th-large"></i>
                </div>
                <div>
                  <strong>${sectionTitle}</strong><br>
                  <small class="text-muted">${
                    sectionConfig.slots?.length || 0
                  } widgets</small>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary save-template-btn" onclick="saveSectionAsTemplate('${sectionId}')">
              <i class="las la-save me-1"></i>Save Template
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Remove existing modal if any
  const existingModal = document.getElementById("saveSectionTemplateModal");
  if (existingModal) {
    existingModal.remove();
  }

  // Add modal to DOM
  document.body.insertAdjacentHTML("beforeend", modalHTML);

  // Show modal
  const modal = new bootstrap.Modal(
    document.getElementById("saveSectionTemplateModal")
  );
  modal.show();
}

function extractSectionConfiguration(sectionId) {
  const sectionElement = document.querySelector(
    `[data-section-id="${sectionId}"]`
  );
  if (!sectionElement) {
    console.error("Section not found:", sectionId);
    return null;
  }

  const sectionConfig = JSON.parse(
    sectionElement.getAttribute("data-section-config") || "{}"
  );
  const nestedGrid = sectionElement.querySelector(".nested-grid-container");

  // Extract current widget positions and configurations from the nested grid
  const widgets = [];
  if (nestedGrid) {
    const gridItems = nestedGrid.querySelectorAll(".grid-stack-item");
    gridItems.forEach((item, index) => {
      const node = item.gridstackNode;
      const widgetElement = item.querySelector(".widget-wrapper");

      if (widgetElement && node) {
        // Determine widget type from the widget element
        let widgetType = "kpi"; // default
        if (widgetElement.querySelector(".chart-container")) {
          if (widgetElement.id.includes("pie")) widgetType = "pie-chart";
          else if (widgetElement.id.includes("bar")) widgetType = "bar-chart";
          else if (widgetElement.id.includes("line")) widgetType = "line-chart";
          else if (widgetElement.id.includes("price"))
            widgetType = "price-chart";
          else widgetType = "chart";
        } else if (widgetElement.querySelector(".table-container")) {
          widgetType = "handsontable";
        } else if (widgetElement.querySelector(".tab-widget-container")) {
          widgetType = "tab-container";
        } else if (widgetElement.querySelector(".text-content")) {
          widgetType = "text";
        } else if (widgetElement.querySelector(".image-container")) {
          widgetType = "image";
        }

        const widgetTitle =
          widgetElement.querySelector(".widget-title")?.textContent ||
          widgetElement.querySelector(".card-title")?.textContent ||
          `Widget ${index + 1}`;

        widgets.push({
          type: widgetType,
          title: widgetTitle,
          size: node.w >= 8 ? "large" : node.w >= 6 ? "medium" : "small",
          position: {
            x: node.x,
            y: node.y,
            w: node.w,
            h: node.h,
          },
        });
      }
    });
  }

  return {
    title: sectionConfig.title,
    size: sectionConfig.size,
    template: sectionConfig.template,
    slots: widgets.length > 0 ? widgets : sectionConfig.slots,
    layout: {
      columns: 12,
      rows: Math.max(...widgets.map((w) => w.position.y + w.position.h), 4),
    },
  };
}

function saveSectionAsTemplate(sectionId) {
  const templateName = document
    .getElementById("sectionTemplateName")
    .value.trim();
  const templateDescription = document
    .getElementById("sectionTemplateDescription")
    .value.trim();

  if (!templateName) {
    alert("Please enter a template name.");
    return;
  }

  const sectionConfig = extractSectionConfiguration(sectionId);
  if (!sectionConfig) {
    alert("Error extracting section configuration.");
    return;
  }

  // Convert section slots to layout slots format for compatibility
  const layoutSlots = sectionConfig.slots.map((widget, index) => ({
    type: widget.type,
    title: widget.title,
    size: widget.size,
    category: getCategoryFromType(widget.type),
    index: index,
  }));

  // Create template object
  const template = {
    id: `section-${Date.now()}`,
    name: templateName,
    description: templateDescription,
    type: "section",
    isCustom: true,
    sectionConfig: sectionConfig,
    slots: layoutSlots, // Use converted format for compatibility
    originalSlots: sectionConfig.slots, // Keep original for reconstruction
    createdAt: new Date().toISOString(),
  };

  // Save to localStorage (prototype implementation)
  let savedTemplates = getSavedTemplates();
  savedTemplates.unshift(template); // Add to beginning of array
  localStorage.setItem(
    "smartWidgetComposer_savedTemplates",
    JSON.stringify(savedTemplates)
  );

  // Close modal
  const modal = bootstrap.Modal.getInstance(
    document.getElementById("saveSectionTemplateModal")
  );
  if (modal) {
    modal.hide();
  }

  // Refresh template grid immediately
  updateTemplateGrid();

  // Show success notification
  showNotification(
    `Section template "${templateName}" saved successfully!`,
    "success"
  );

  console.log("Section template saved:", template);
}

// Helper function to get category from widget type
function getCategoryFromType(type) {
  const categoryMap = {
    kpi: "metrics",
    "pie-chart": "charts",
    "bar-chart": "charts",
    "line-chart": "charts",
    "price-chart": "charts",
    chart: "charts",
    handsontable: "data",
    table: "data",
    text: "content",
    image: "content",
    "tab-container": "layout",
  };
  return categoryMap[type] || "metrics";
}
