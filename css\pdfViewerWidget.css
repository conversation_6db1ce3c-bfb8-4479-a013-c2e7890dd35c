/* PDF Viewer Widget Styles */
.pdf-viewer-widget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.pdf-container {
  flex: 1;
  min-height: 200px;
  width: 100%;
  background-color: #f8f9fa;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

/* Settings icon styles */
.widget-header .btn-link {
  padding: 0;
  color: #333;
  text-decoration: none;
}

.widget-header .btn-link:hover {
  color: #02104f;
}

.widget-header .la-cog,
.widget-header .la-times {
  font-size: 1.1rem;
}

.widget-header .la-times:hover {
  color: #dc3545;
}

/* Widget header styles */
.pdf-viewer-widget .widget-header {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 8px 12px;
  border-radius: 4px;
  margin: -8px -8px 8px -8px;
}

/* Placeholder styles */
.pdf-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
  font-size: 0.9rem;
  padding: 2rem;
}

.pdf-placeholder .fas.la-file-pdf {
  color: #dc3545;
}

/* PDF Viewer Content */
.pdf-viewer-content {
  overflow: auto;
  height: calc(100% - 46px); /* Subtract the height of controls */
  width: 100%;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background-color: #525659;
}

/* When toolbar is hidden */
.pdf-viewer-controls[style*="display: none"] + .pdf-viewer-content {
  height: 100%;
}

/* PDF Canvas */
.pdf-viewer-content canvas {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  margin: 10px auto;
}

/* PDF Controls */
.pdf-viewer-controls {
  border-bottom: 1px solid #dee2e6;
  background-color: #f8f9fa;
  padding: 8px;
  height: 46px;
}

/* Make object tag responsive */
.pdf-container object {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

/* Form text help */
.form-text.text-muted {
  font-size: 0.75rem;
}
