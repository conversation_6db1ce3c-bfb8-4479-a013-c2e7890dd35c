let horizontalBarChartCounter = 0;

/**
 * Generate horizontal bar chart widget markup
 * Shared between onclick and drag-drop functionality
 */
function getHorizontalBarChartWidgetMarkup(chartId) {
  return `
    <div class="widget p-2">
      <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
        <div class="widget-title">
          <span>Horizontal Bar Chart</span>
        </div>
        <div class="widget-actions">
          <button class="btn btn-link"
                  data-bs-toggle="offcanvas"
                  data-bs-target="#horizontalBarChartSettings"
                  aria-controls="horizontalBarChartSettings"
                  onclick="initHorizontalBarChartSettings('${chartId}')">
            <i class="las la-cog"></i>
          </button>
          <button class="btn btn-link ms-1"
                  onclick="removeWidget(this)">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
        <div id="${chartId}" class="chart-container" style="flex: 1; width: 100%; height: 100%; min-height: 400px; position: relative;"></div>
      </div>
    </div>
  `;
}

// Add a horizontal bar chart widget using amCharts v5
function addHorizontalBarChartWidget() {
  console.log("🔄 Adding horizontal bar chart widget...");

  // Check if grid is available
  if (typeof window.grid === 'undefined' && typeof grid === 'undefined') {
    console.error("❌ Grid not available. Make sure GridStack is initialized.");
    console.log("🔍 Available globals:", Object.keys(window).filter(k => k.includes('grid') || k.includes('Grid')));
    alert("Grid not available. Please refresh the page and try again.");
    return;
  }

  // Use global grid variable (try both window.grid and grid)
  const gridInstance = window.grid || grid;

  try {
    const chartId = `horizontal-bar-chart-${Date.now()}`;
    console.log("📊 Creating horizontal bar chart with ID:", chartId);

    // Add the widget to the grid using shared markup
    gridInstance.addWidget({
      x: 0,
      y: 0,
      w: 6,
      h: 4,
      content: getHorizontalBarChartWidgetMarkup(chartId)
    });

    console.log("✅ Widget added to grid successfully");

    // Initialize the chart after a short delay to ensure DOM is ready
    setTimeout(async () => {
      try {
        console.log("🔄 Initializing horizontal bar chart...");
        await waitForAmCharts();
        await initHorizontalBarChart(chartId);
        console.log("✅ Horizontal bar chart initialized successfully");
      } catch (error) {
        console.error("❌ Error initializing horizontal bar chart:", error);

        // Show error in the chart container
        const container = document.getElementById(chartId);
        if (container) {
          container.innerHTML = `
            <div class="alert alert-danger m-3">
              <h6>Chart Initialization Failed</h6>
              <p>Failed to initialize horizontal bar chart. Please try again.</p>
              <small class="text-muted">${error.message}</small>
            </div>
          `;
        }
      }
    }, 100);

  } catch (error) {
    console.error("❌ Error adding horizontal bar chart widget:", error);
    alert("Failed to add horizontal bar chart widget. Please try again.");
  }
}

/**
 * Initialize horizontal bar chart with amCharts v5
 */
async function initHorizontalBarChart(chartId) {
  console.log("Initializing horizontal bar chart:", chartId);

  const chartContainer = document.getElementById(chartId);
  if (!chartContainer) {
    throw new Error(`Chart container with ID ${chartId} not found`);
  }

  // Check if chart is already being initialized or already exists
  if (chartContainer.hasAttribute('data-initializing')) {
    console.log("⚠️ Chart is already being initialized, skipping...");
    return;
  }

  if (chartContainer.hasAttribute('data-initialized')) {
    console.log("⚠️ Chart is already initialized, skipping...");
    return;
  }

  // Mark as initializing
  chartContainer.setAttribute('data-initializing', 'true');

  // Check if there's already a chart instance and dispose it
  if (chartContainer.chart) {
    console.log("🔄 Disposing existing chart instance...");
    try {
      chartContainer.chart.dispose();
    } catch (e) {
      console.warn("Warning disposing chart:", e);
    }
    chartContainer.chart = null;
  }

  // Clear any existing content
  chartContainer.innerHTML = '';

  try {
    // Create root element
    const root = am5.Root.new(chartId);

    // Set themes with custom branding
    root.setThemes([
      am5themes_Animated.new(root)
    ]);

    // Create chart
    const chart = root.container.children.push(am5xy.XYChart.new(root, {
      panX: true,
      panY: true,
      wheelX: "panX",
      wheelY: "zoomY",
      pinchZoomX: true,
      layout: root.verticalLayout
    }));

    // Sample data - converted from timestamp to readable dates
    const data = [
      {
        "category": "Jan 2023",
        "value": 10,
        "date": new Date(2023, 0, 1)
      },
      {
        "category": "Feb 2023", 
        "value": 15,
        "date": new Date(2023, 1, 1)
      },
      {
        "category": "Mar 2023",
        "value": 12,
        "date": new Date(2023, 2, 1)
      },
      {
        "category": "Apr 2023",
        "value": 18,
        "date": new Date(2023, 3, 1)
      },
      {
        "category": "May 2023",
        "value": 20,
        "date": new Date(2023, 4, 1)
      },
      {
        "category": "Jun 2023",
        "value": 17,
        "date": new Date(2023, 5, 1)
      },
      {
        "category": "Jul 2023",
        "value": 22,
        "date": new Date(2023, 6, 1)
      }
    ];

    // Create axes
    // X-axis (horizontal) - Value axis
    const xAxis = chart.xAxes.push(am5xy.ValueAxis.new(root, {
      renderer: am5xy.AxisRendererX.new(root, {
        strokeOpacity: 0.1,
        minGridDistance: 50
      }),
      tooltip: am5.Tooltip.new(root, {})
    }));

    // Style X-axis labels
    xAxis.get("renderer").labels.template.setAll({
      fontSize: "12px",
      fontFamily: "Montserrat, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif",
      fill: am5.color("#02104f") // Brand navy color
    });

    // Y-axis (vertical) - Category axis for horizontal bars
    const yAxis = chart.yAxes.push(am5xy.CategoryAxis.new(root, {
      categoryField: "category",
      renderer: am5xy.AxisRendererY.new(root, {
        strokeOpacity: 0.1,
        cellStartLocation: 0.1,
        cellEndLocation: 0.9,
        inversed: true // This makes it display from top to bottom
      }),
      tooltip: am5.Tooltip.new(root, {})
    }));

    // Style Y-axis labels
    yAxis.get("renderer").labels.template.setAll({
      fontSize: "12px",
      fontFamily: "Montserrat, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif",
      fill: am5.color("#02104f") // Brand navy color
    });

    yAxis.data.setAll(data);

    // Create series (horizontal bars)
    const series = chart.series.push(am5xy.ColumnSeries.new(root, {
      name: "Horizontal Bars",
      xAxis: xAxis,
      yAxis: yAxis,
      valueXField: "value",
      categoryYField: "category"
    }));

    // Apply brand colors if available
    if (window.chartConfig && window.chartConfig.brandColors) {
      const brandColorSet = am5.ColorSet.new(root, {
        colors: window.chartConfig.brandColors.map(color => am5.color(color)),
        reuse: true
      });
      chart.set("colors", brandColorSet);
      console.log("Applied brand colors to horizontal bar chart");
    } else {
      // Default brand colors if not available in window.chartConfig
      const defaultBrandColors = [
        "#00b19c", "#3bcd3f", "#007365", "#8dbac4", "#02104f"
      ];
      const brandColorSet = am5.ColorSet.new(root, {
        colors: defaultBrandColors.map(color => am5.color(color)),
        reuse: true
      });
      chart.set("colors", brandColorSet);
      console.log("Applied default brand colors to horizontal bar chart");
    }

    // Set up a color adapter for the columns to use brand colors
    series.columns.template.adapters.add("fill", (fill, target) => {
      if (target.dataItem) {
        const index = target.dataItem.get("index") || 0;
        const brandColors = window.chartConfig?.brandColors || [
          "#00b19c", "#3bcd3f", "#007365", "#8dbac4", "#02104f"
        ];
        const colorIndex = index % brandColors.length;
        return am5.color(brandColors[colorIndex]);
      }
      return fill;
    });

    // Custom styling
    series.columns.template.setAll({
      stroke: am5.color("#ffffff"),
      strokeWidth: 2,
      cornerRadiusTR: 4,
      cornerRadiusBR: 4,
      tooltipText: "{categoryY}: {valueX}",
      tooltipY: am5.percent(50),
      height: am5.percent(70),
      fillOpacity: 0.9
    });

    // Hover effects
    series.columns.template.states.create("hover", {
      fillOpacity: 1,
      strokeWidth: 3
    });

    // Add data labels
    series.bullets.push(function () {
      return am5.Bullet.new(root, {
        locationX: 1,
        sprite: am5.Label.new(root, {
          text: "{valueX}",
          fill: am5.color("#ffffff"),
          centerY: am5.p50,
          centerX: am5.p50,
          populateText: true,
          fontSize: "12px",
          fontWeight: "500",
          fontFamily: "Montserrat, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif"
        })
      });
    });

    // Note: Removed vertical scrollbar as requested

    // Add cursor
    const cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
      behavior: "none"
    }));
    cursor.lineY.set("visible", false);

    // Set data
    series.data.setAll(data);

    // Animations
    series.appear(1000, 100);
    chart.appear(1000, 100);

    // Store chart reference for cleanup
    chartContainer.chart = root;

    // Mark as successfully initialized
    chartContainer.removeAttribute('data-initializing');
    chartContainer.setAttribute('data-initialized', 'true');

    console.log("✅ Horizontal bar chart initialized successfully");

  } catch (error) {
    console.error("❌ Error in initHorizontalBarChart:", error);

    // Clean up on error
    chartContainer.removeAttribute('data-initializing');

    // Show error message in container
    chartContainer.innerHTML = `
      <div class="alert alert-danger m-3">
        <h6>Chart Initialization Failed</h6>
        <p>Failed to initialize horizontal bar chart.</p>
        <small class="text-muted">${error.message}</small>
        <br>
        <button class="btn btn-sm btn-outline-danger mt-2" onclick="initHorizontalBarChart('${chartId}')">
          Retry
        </button>
      </div>
    `;

    throw error;
  }
}

/**
 * Wait for amCharts libraries to load
 */
function waitForAmCharts() {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    const maxAttempts = 50;
    
    const checkAmCharts = () => {
      attempts++;
      
      if (typeof am5 !== 'undefined' && typeof am5xy !== 'undefined' && typeof am5themes_Animated !== 'undefined') {
        resolve();
      } else if (attempts >= maxAttempts) {
        reject(new Error('amCharts libraries failed to load after maximum attempts'));
      } else {
        setTimeout(checkAmCharts, 100);
      }
    };
    
    checkAmCharts();
  });
}

/**
 * Initialize settings for horizontal bar chart (placeholder)
 */
function initHorizontalBarChartSettings(chartId) {
  console.log('Opening settings for horizontal bar chart:', chartId);
  // Settings functionality can be implemented later
}

/**
 * Setup drag-drop functionality for horizontal bar chart widgets
 * This ensures both onclick and drag-drop use the same markup and logic
 */
function setupHorizontalBarChartDragDrop() {
  console.log('📊 Setting up horizontal bar chart drag-drop functionality...');

  const horizontalBarChartSidebarContent = [
    {
      w: 6,
      h: 4,
      get content() {
        const chartId = "horizontal-bar-chart-" + Date.now() + "-" + Math.floor(Math.random() * 100000);

        // Use the same markup function as onclick
        const markup = getHorizontalBarChartWidgetMarkup(chartId);

        // Store initialization data for the grid "added" event handler to pick up
        if (!window.pendingHorizontalBarChartInits) {
          window.pendingHorizontalBarChartInits = new Map();
        }

        window.pendingHorizontalBarChartInits.set(chartId, {
          chartId,
          timestamp: Date.now()
        });

        console.log('📊 Drag-drop: Stored initialization data for', chartId);

        return markup;
      },
    },
  ];

  // Setup GridStack drag-in
  if (typeof GridStack !== 'undefined' && GridStack.setupDragIn) {
    GridStack.setupDragIn(
      '.widget-item[data-widget-type="horizontal-bar-chart"]',
      undefined,
      horizontalBarChartSidebarContent
    );
    console.log('✅ Horizontal bar chart drag-in setup complete');
  } else {
    console.warn('⚠️ GridStack not available, skipping horizontal bar chart drag-in setup');
  }
}

// Cleanup old pending initializations (prevent memory leaks)
function cleanupOldPendingHorizontalBarChartInits() {
  if (window.pendingHorizontalBarChartInits) {
    const now = Date.now();
    const maxAge = 30000; // 30 seconds

    for (const [chartId, initData] of window.pendingHorizontalBarChartInits.entries()) {
      if (now - initData.timestamp > maxAge) {
        console.warn('📊 Cleaning up old pending initialization for', chartId);
        window.pendingHorizontalBarChartInits.delete(chartId);
      }
    }
  }
}

// Run cleanup periodically
setInterval(cleanupOldPendingHorizontalBarChartInits, 60000); // Every minute

// Auto-setup drag-drop when this script loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', setupHorizontalBarChartDragDrop);
} else {
  // If DOM is already loaded, setup immediately
  setTimeout(setupHorizontalBarChartDragDrop, 100);
}

// Debug function to test if the widget is working
function testHorizontalBarChartWidget() {
  console.log("🧪 Testing Horizontal Bar Chart Widget...");
  console.log("✅ Function available:", typeof addHorizontalBarChartWidget);
  console.log("✅ Grid available:", typeof window.grid);
  console.log("✅ amCharts available:", typeof am5);

  if (typeof addHorizontalBarChartWidget === 'function') {
    console.log("🎯 Attempting to add widget...");
    addHorizontalBarChartWidget();
  } else {
    console.error("❌ addHorizontalBarChartWidget function not found!");
  }
}

// --- Global Exports ---
window.addHorizontalBarChartWidget = addHorizontalBarChartWidget;
window.getHorizontalBarChartWidgetMarkup = getHorizontalBarChartWidgetMarkup; // Export markup function
window.setupHorizontalBarChartDragDrop = setupHorizontalBarChartDragDrop; // Export drag-drop setup
window.initHorizontalBarChart = initHorizontalBarChart; // Export chart initialization
window.initHorizontalBarChartSettings = initHorizontalBarChartSettings; // Export settings function
window.testHorizontalBarChartWidget = testHorizontalBarChartWidget; // Export test function

// Log that the script has loaded
console.log("📊 Horizontal Bar Chart Widget script loaded successfully");
console.log("🔧 Available functions:", {
  addHorizontalBarChartWidget: typeof window.addHorizontalBarChartWidget,
  getHorizontalBarChartWidgetMarkup: typeof window.getHorizontalBarChartWidgetMarkup,
  setupHorizontalBarChartDragDrop: typeof window.setupHorizontalBarChartDragDrop,
  initHorizontalBarChart: typeof window.initHorizontalBarChart
});
