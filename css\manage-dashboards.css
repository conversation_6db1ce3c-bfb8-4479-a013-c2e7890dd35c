:root {
  --primary-color: #00a6a6;
  --primary-dark: #008080;
  --primary-light: #e5f5f5;
  --primary-lighter: #f0fafa;
  --secondary-color: #2c3e50;
  --accent-color: #3498db;
  --success-color: #2ecc71;
  --warning-color: #f1c40f;
  --danger-color: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-tertiary: #bdc3c7;
  --background-primary: #ffffff;
  --background-secondary: #f8fafa;
  --border-color: #e5e9f0;
  --shadow-sm: 0 1px 2px rgba(0, 166, 166, 0.05);
  --shadow-md: 0 2px 4px rgba(0, 166, 166, 0.07);
  --shadow-lg: 0 4px 6px rgba(0, 166, 166, 0.1);
  --transition: all 0.2s ease;
  --font-size-base: 12px;
  --font-size-sm: 11px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Montserrat", sans-serif;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  font-size: var(--font-size-base);
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-secondary);
}

/* Header Styles */
.app-header {
  background-color: var(--background-primary);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-sm);
  border-bottom: 3px solid #007365;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-logo {
  height: 32px;
  width: auto;
}

.divider {
  width: 1px;
  height: 24px;
  background-color: var(--border-color);
}

.header-left h1 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  text-transform: capitalize;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.notifications-btn {
  position: relative;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: var(--transition);
}

.notifications-btn:hover {
  color: var(--text-primary);
  background-color: var(--background-secondary);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--danger-color);
  color: white;
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
}

.user-menu {
  position: relative;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  transition: var(--transition);
}

.user-button:hover {
  background-color: var(--background-secondary);
}

.user-avatar {
  width: 36px;
  height: 36px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  text-transform: capitalize;
}

.user-role {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  text-transform: capitalize;
}

/* Main Content Styles */
.main-content {
  flex: 1;
  padding: 2rem;
  width: 100%;
  background: linear-gradient(
    180deg,
    var(--background-secondary) 0%,
    rgba(248, 250, 250, 0.5) 100%
  );
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-content h2 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-transform: capitalize;
}

.subtitle {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
}

.create-dashboard-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  font-size: var(--font-size-base);
  font-weight: 500;
  text-transform: capitalize;
}

.create-dashboard-btn:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

/* When used in add-card */
.add-card.create-dashboard-btn {
  background: none;
  padding: 0;
  width: 100%;
  height: 100%;
}

.add-card.create-dashboard-btn:hover {
  background: var(--primary-lighter);
  transform: none;
}

/* Filters Section */
.filters-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.filter-dropdowns {
  display: flex;
  gap: 1rem;
  flex: 2;
}

.filter-select {
  flex: 1;
  max-width: 300px;
}

.styled-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-family: "Montserrat", sans-serif;
  cursor: pointer;
  transition: var(--transition);
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
}

.styled-select:hover {
  border-color: var(--primary-color);
}

.styled-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

.styled-select:disabled {
  background-color: var(--background-secondary);
  cursor: not-allowed;
  opacity: 0.7;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--border-color);
  font-size: var(--font-size-base);
  transition: var(--transition);
  background-color: var(--background-primary);
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 166, 166, 0.1);
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-group {
  display: flex;
  gap: 0.5rem;
}

.filter-btn,
.sort-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: var(--transition);
}

.filter-btn:hover,
.sort-btn:hover {
  border-color: var(--primary-color);
  background-color: var(--primary-lighter);
}

.filter-badge {
  background-color: var(--primary-color);
  color: white;
  padding: 0.125rem 0.375rem;
  font-size: var(--font-size-sm);
}

.view-controls {
  display: flex;
  gap: 0.25rem;
}

.view-btn {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  background-color: var(--background-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
}

.view-btn:hover {
  background-color: var(--background-secondary);
  color: var(--text-primary);
}

.view-btn.active {
  background-color: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Dashboards Grid */
.dashboards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 1.5rem;
}

.dashboard-card {
  background-color: var(--background-primary);
  border: 1px solid var(--border-color);
  transition: var(--transition);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-sm);
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.card-header {
  height: 140px;
  background: var(--primary-lighter);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(0, 166, 166, 0.1);
}

.card-header i {
  font-size: 4rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  opacity: 1;
}

.card-body {
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  border-top: none;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  background-color: rgba(0, 166, 166, 0.05);
  color: var(--primary-dark);
  padding: 0.25rem 0.75rem;
  font-size: var(--font-size-sm);
  font-weight: 500;
  border: 1px solid rgba(0, 166, 166, 0.2);
}

.card-body h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
  margin: 0;
}

.last-edited {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.last-edited::before {
  content: "";
  display: inline-block;
  width: 4px;
  height: 4px;
  background-color: var(--primary-color);
  border-radius: 50%;
}

.card-stats {
  display: flex;
  gap: 1.5rem;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 166, 166, 0.1);
}

.stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.stat i {
  font-size: 1.125rem;
  color: var(--primary-color);
}

.card-footer {
  padding: 1rem 1.5rem;
  background-color: var(--primary-lighter);
  border-top: 1px solid rgba(0, 166, 166, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.collaborators {
  display: flex;
  align-items: center;
}

.avatar {
  width: 28px;
  height: 28px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: 500;
  margin-left: -0.5rem;
  border: 2px solid var(--background-primary);
}

.avatar.more {
  background: linear-gradient(
    135deg,
    var(--text-secondary),
    var(--text-tertiary)
  );
}

.action-btn {
  color: var(--text-secondary);
  padding: 0.5rem;
  cursor: pointer;
  transition: var(--transition);
  border: none;
  background: none;
}

.action-btn:hover {
  color: var(--primary-color);
}

/* Add Dashboard Card */
.add-card {
  border: 1px dashed rgba(0, 166, 166, 0.3);
  background-color: var(--background-primary);
  cursor: pointer;
}

.add-card:hover {
  background: var(--primary-lighter);
  border-color: var(--primary-color);
}

.add-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  gap: 0.75rem;
}

.add-icon {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.add-icon i {
  font-size: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  opacity: 0.9;
}

.add-content p {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

.add-content .hint {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* Add footer styles */
.app-footer {
  background-color: #02104f;
  padding: 1rem 2rem;
  color: rgba(255, 255, 255, 0.7);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 100%;
  margin: 0 auto;
}

.footer-links {
  display: flex;
  gap: 2rem;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  font-size: var(--font-size-base);
  transition: all 0.2s ease;
  text-transform: capitalize;
}

.footer-links a:hover {
  color: white;
}

.footer-copyright {
  font-size: var(--font-size-base);
  color: rgba(255, 255, 255, 0.7);
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: 1rem;
  }

  .main-content {
    padding: 1rem;
  }

  .page-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: none;
  }

  .filter-controls {
    justify-content: space-between;
  }

  .dashboards-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .footer-links {
    flex-direction: column;
    gap: 1rem;
  }
}

/* List View Styles */
.dashboards-grid.list-view {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.list-view .dashboard-card {
  display: grid;
  grid-template-columns: 80px 2fr 1fr auto;
  gap: 1.5rem;
  height: auto;
  padding: 1rem;
  align-items: center;
  transition: all 0.2s ease;
}

.list-view .dashboard-card:hover {
  transform: translateX(4px);
}

.list-view .card-header {
  height: 80px;
  width: 80px;
  margin: 0;
}

.list-view .card-header i {
  font-size: 2.5rem;
}

.list-view .card-body {
  padding: 0;
  border-top: none;
  display: grid;
  gap: 0.5rem;
}

.list-view .card-tags {
  order: 2;
  margin: 0;
}

.list-view .tag {
  font-size: var(--font-size-sm);
  padding: 0.125rem 0.5rem;
}

.list-view .card-body h3 {
  order: 1;
  font-size: 1rem;
  margin: 0;
}

.list-view .last-edited {
  order: 3;
  font-size: var(--font-size-sm);
  margin: 0;
}

.list-view .card-stats {
  justify-self: end;
  display: flex;
  gap: 2rem;
  margin: 0;
  padding: 0;
  border: none;
}

.list-view .stat {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.list-view .stat i {
  font-size: 1rem;
}

.list-view .card-footer {
  padding: 0;
  background: none;
  border: none;
  justify-self: end;
}

.list-view .collaborators {
  display: flex;
  align-items: center;
}

.list-view .avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: var(--font-size-sm);
  margin-left: -0.25rem;
}

.list-view .avatar:first-child {
  margin-left: 0;
}

.list-view .action-btn {
  padding: 0.5rem;
  color: var(--text-secondary);
}

.list-view .action-btn:hover {
  color: var(--primary-color);
  background: var(--primary-lighter);
}

/* List View - Add Card */
.list-view .add-card {
  display: grid;
  grid-template-columns: 80px 1fr auto;
  gap: 1.5rem;
  padding: 1rem;
  background: linear-gradient(to right, var(--primary-lighter), transparent);
  border: 1px dashed var(--primary-color);
  cursor: pointer;
  align-items: center;
  transition: all 0.2s ease;
}

.list-view .add-card:hover {
  transform: translateX(4px);
  background: linear-gradient(
    to right,
    var(--primary-lighter),
    var(--background-primary)
  );
  border-style: solid;
}

.list-view .add-icon {
  height: 80px;
  width: 80px;
  margin: 0;
  background: var(--background-primary);
  border: 1px dashed var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.list-view .add-card:hover .add-icon {
  background: var(--primary-lighter);
  border-style: solid;
}

.list-view .add-icon i {
  font-size: 2rem;
  color: var(--primary-color);
  transition: all 0.2s ease;
}

.list-view .add-card:hover .add-icon i {
  transform: scale(1.1);
}

.list-view .add-content {
  flex-direction: row;
  align-items: center;
  padding: 0;
  gap: 1rem;
}

.list-view .add-content p {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.list-view .add-content .hint {
  display: none;
}

/* List View Responsive */
@media (max-width: 1200px) {
  .list-view .dashboard-card {
    grid-template-columns: 60px 2fr 1fr auto;
    gap: 1rem;
  }

  .list-view .card-header {
    height: 60px;
    width: 60px;
  }

  .list-view .card-header i {
    font-size: 1.75rem;
  }

  .list-view .add-card {
    grid-template-columns: 60px 1fr auto;
    gap: 1rem;
  }

  .list-view .add-icon {
    height: 60px;
    width: 60px;
  }
}

@media (max-width: 768px) {
  .list-view .dashboard-card {
    grid-template-columns: 48px 1fr;
    gap: 1rem;
  }

  .list-view .card-header {
    height: 48px;
    width: 48px;
  }

  .list-view .card-header i {
    font-size: 1.5rem;
  }

  .list-view .card-stats {
    display: none;
  }

  .list-view .add-card {
    grid-template-columns: 48px 1fr;
    gap: 1rem;
  }

  .list-view .add-icon {
    height: 48px;
    width: 48px;
  }

  .list-view .add-icon i {
    font-size: 1.5rem;
  }

  .list-view .add-content .hint {
    display: none;
  }
}

/* Light theme overrides */
.app-container {
  background-color: var(--background-secondary);
}

.main-content {
  background: linear-gradient(
    180deg,
    var(--background-secondary) 0%,
    rgba(248, 250, 250, 0.5) 100%
  );
}

.dashboard-card,
.add-card {
  background: var(--background-primary);
}

.list-view .add-card {
  background: linear-gradient(to right, rgba(0, 166, 166, 0.05), transparent);
}

.list-view .add-card:hover {
  background: linear-gradient(
    to right,
    rgba(0, 166, 166, 0.1),
    var(--background-primary)
  );
}

.list-view .add-icon {
  background: var(--background-primary);
}

.list-view .add-card:hover .add-icon {
  background: var(--primary-lighter);
}

/* Table and Content Text */
.table-cell {
  font-size: var(--font-size-base);
}

.dashboard-name {
  font-size: var(--font-size-base);
  font-weight: 500;
}

.last-edited .date {
  font-size: var(--font-size-base);
}

.last-edited .editor {
  font-size: var(--font-size-sm);
}

.status-badge {
  font-size: var(--font-size-sm);
  font-weight: 500;
  text-transform: capitalize;
}

.version {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

/* Action Dropdown Styles */
.action-dropdown {
  position: relative;
}

.action-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.2s ease;
  z-index: 1000;
}

.action-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.action-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  color: var(--text-secondary);
  transition: var(--transition);
}

.action-btn:hover {
  color: var(--text-primary);
  background-color: var(--background-secondary);
  border-radius: 4px;
}

.action-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  width: 100%;
  border: none;
  background: none;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  text-align: left;
  cursor: pointer;
  transition: var(--transition);
}

.action-menu-item:hover {
  background-color: var(--background-secondary);
}

.action-menu-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 8px 0;
}

.action-menu-item.danger {
  color: var(--danger-color);
}

.action-menu-item.danger:hover {
  background-color: #fef2f2;
}

/* Offcanvas Styles */
.offcanvas-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(2, 16, 79, 0.35);
  backdrop-filter: blur(4px);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.offcanvas-backdrop.show {
  opacity: 1;
  visibility: visible;
}

.offcanvas {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 100%;
  max-width: 840px;
  background: var(--background-primary);
  box-shadow: -10px 0 40px -15px rgba(0, 0, 0, 0.2);
  z-index: 1001;
  transform: translateX(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  border-radius: 0;
}

.offcanvas.show {
  transform: translateX(0);
}

.offcanvas-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 2rem;
  background: linear-gradient(
    135deg,
    var(--background-primary) 0%,
    var(--primary-lighter) 100%
  );
  border-bottom: 1px solid var(--border-color);
}

.offcanvas-header-content {
  flex: 1;
}

.offcanvas-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 0.25rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.offcanvas-subtitle {
  color: var(--text-secondary);
  font-size: 12px;
  max-width: 80%;
}

.offcanvas-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 18px;
  cursor: pointer;
  padding: 0.5rem;
  margin: -0.5rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.offcanvas-close:hover {
  color: var(--text-primary);
  background: var(--primary-lighter);
}

.offcanvas-body {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem 2rem;
}

/* Creation Options in Offcanvas */
.creation-options {
  max-width: 100%;
}

.options-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.25rem;
  margin-bottom: 1.5rem;
}

.creation-option {
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  overflow: hidden;
}

.creation-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px -12px rgba(0, 166, 166, 0.15);
  border-color: var(--primary-color);
}

.option-header {
  padding: 1.25rem;
  background: linear-gradient(
    135deg,
    var(--primary-lighter),
    var(--background-primary)
  );
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.option-icon {
  width: 40px;
  height: 40px;
  background: var(--background-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 16px -4px rgba(0, 166, 166, 0.15);
}

.option-icon i {
  font-size: 16px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.option-badge {
  padding: 0.25rem 0.75rem;
  background: var(--primary-color);
  color: white;
  font-size: 11px;
  font-weight: 500;
}

.option-badge.pro {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--accent-color)
  );
}

.option-content {
  padding: 1.25rem;
}

.option-content h3 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.option-content p {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-size: 12px;
  line-height: 1.5;
}

.option-features {
  list-style: none;
  margin-bottom: 1.25rem;
  display: grid;
  gap: 0.75rem;
}

.option-features li {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.option-features li i {
  color: var(--primary-color);
  font-size: 14px;
  margin-top: 0.125rem;
}

.option-features li div {
  display: flex;
  flex-direction: column;
  gap: 0.125rem;
}

.option-features li strong {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 12px;
}

.option-features li span {
  color: var(--text-secondary);
  font-size: 11px;
}

.option-btn {
  width: 100%;
  padding: 0.75rem;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.option-btn i {
  font-size: 14px;
}

.option-btn.primary {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  color: white;
}

.option-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px -4px rgba(0, 166, 166, 0.25);
}

.option-btn.secondary {
  background: var(--primary-lighter);
  color: var(--primary-color);
}

.option-btn.secondary:hover {
  background: var(--primary-light);
  transform: translateY(-2px);
}

/* Template Gallery Styles */
.template-gallery {
  display: none;
}

.template-gallery.show {
  display: block;
  animation: slideIn 0.3s ease;
}

.gallery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.gallery-header-content h3 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.gallery-header-content p {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background: var(--primary-lighter);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.template-filters {
  margin-bottom: 1.5rem;
}

.filter-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-tag {
  padding: 0.375rem 0.75rem;
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-tag:hover {
  background: var(--primary-lighter);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.filter-tag.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.25rem;
}

.template-card {
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  overflow: hidden;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px -12px rgba(0, 166, 166, 0.15);
  border-color: var(--primary-color);
}

.template-preview {
  position: relative;
  padding-top: 56.25%; /* 16:9 aspect ratio */
  background: var(--background-secondary);
  overflow: hidden;
}

.template-preview img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.template-overlay {
  position: absolute;
  inset: 0;
  background: rgba(2, 16, 79, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.template-card:hover .template-overlay {
  opacity: 1;
}

.template-actions {
  display: flex;
  gap: 0.75rem;
}

.preview-btn,
.use-template-btn {
  padding: 0.5rem 1rem;
  font-size: var(--font-size-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.preview-btn {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  backdrop-filter: blur(4px);
}

.preview-btn:hover {
  background: rgba(255, 255, 255, 0.25);
}

.use-template-btn {
  background: var(--primary-color);
  color: white;
}

.use-template-btn:hover {
  background: var(--primary-dark);
}

.template-info {
  padding: 1rem;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.template-header h4 {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--text-primary);
}

.template-type {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: 500;
}

.template-info p {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.template-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 0.75rem;
  border-top: 1px solid var(--border-color);
}

.template-stats {
  display: flex;
  gap: 1rem;
}

.template-stats span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.template-stats i {
  color: var(--primary-color);
}

.template-rating {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: var(--warning-color);
  font-size: var(--font-size-sm);
}

.template-rating span {
  color: var(--text-secondary);
  margin-left: 0.25rem;
}

/* Responsive Template Gallery */
@media (max-width: 1200px) {
  .templates-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .gallery-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .back-btn {
    width: 100%;
    justify-content: center;
  }

  .template-filters {
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }

  .filter-tags {
    flex-wrap: nowrap;
    padding-bottom: 0.5rem;
  }

  .template-actions {
    flex-direction: column;
    width: 100%;
    padding: 1rem;
  }

  .preview-btn,
  .use-template-btn {
    width: 100%;
    justify-content: center;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.hexa-logo {
  display: flex;
  align-items: center;
  margin-left: 1.5rem;
}

.hexa-logo img {
  height: 32px;
  width: auto;
}
