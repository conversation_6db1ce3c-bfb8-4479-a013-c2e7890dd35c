<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Section Container Scroll Fix Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .scroll-test-area {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            background: white;
        }
        
        .scroll-content {
            height: 800px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Section Container Scroll Fix Test</h1>
        
        <div class="test-section">
            <h3>Test Instructions</h3>
            <p>This page tests the fix for unwanted scrolling when working with section containers in GridStack.</p>
            <ol>
                <li>Scroll down in the test area below to simulate a long page</li>
                <li>Try to drag elements or move your mouse up and down near the edges</li>
                <li>The page should NOT scroll automatically when you're working with section containers</li>
                <li>Only scroll when you're near the viewport edges and NOT over section containers</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>Scroll Test Area</h3>
            <p>Scroll down in this area to create a long page, then test the auto-scroll behavior:</p>
            <div class="scroll-test-area">
                <div class="scroll-content"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <button class="test-button" onclick="testScrollFix()">Test Scroll Fix</button>
            <button class="test-button" onclick="enableDebug()">Enable Debug Mode</button>
            <button class="test-button" onclick="disableDebug()">Disable Debug Mode</button>
            <button class="test-button" onclick="checkStatus()">Check Status</button>
        </div>
        
        <div id="status" class="status info">
            Ready to test. Scroll down in the test area above, then move your mouse near the edges to test auto-scroll behavior.
        </div>
        
        <div class="test-section">
            <h3>What Was Fixed</h3>
            <ul>
                <li><strong>Improved Section Container Detection:</strong> The auto-scroll system now better detects when you're working within section containers</li>
                <li><strong>Prevented Unwanted Scrolling:</strong> Auto-scroll is disabled when dragging or hovering over section containers</li>
                <li><strong>Better Event Handling:</strong> Mouse events are properly filtered to prevent false triggers</li>
                <li><strong>CSS Overflow Control:</strong> Added CSS rules to prevent unwanted scrolling within section containers</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>Expected Behavior</h3>
            <ul>
                <li>✅ Auto-scroll should work when dragging widgets near viewport edges</li>
                <li>✅ Auto-scroll should NOT work when dragging within section containers</li>
                <li>✅ Auto-scroll should NOT work when hovering over section containers</li>
                <li>✅ Page should remain stable when working with nested grids</li>
            </ul>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function testScrollFix() {
            updateStatus('Testing scroll fix...', 'info');
            
            // Simulate a test scenario
            setTimeout(() => {
                if (window.GridStackAutoScrollAuto) {
                    updateStatus('✅ GridStack Auto-Scroll is loaded and working!', 'success');
                } else {
                    updateStatus('❌ GridStack Auto-Scroll not found. Make sure the script is loaded.', 'error');
                }
            }, 1000);
        }
        
        function enableDebug() {
            if (window.GridStackAutoScrollAuto) {
                window.GridStackAutoScrollAuto.enableDebug();
                updateStatus('🔍 Debug mode enabled. Check console for detailed logs.', 'info');
            } else {
                updateStatus('❌ GridStack Auto-Scroll not found.', 'error');
            }
        }
        
        function disableDebug() {
            if (window.GridStackAutoScrollAuto) {
                window.GridStackAutoScrollAuto.disableDebug();
                updateStatus('🔍 Debug mode disabled.', 'info');
            } else {
                updateStatus('❌ GridStack Auto-Scroll not found.', 'error');
            }
        }
        
        function checkStatus() {
            if (window.GridStackAutoScrollAuto) {
                const config = window.GridStackAutoScrollAuto.config;
                updateStatus(`📊 Auto-Scroll Status: Active | Scroll Zone: ${config.scrollZone}px | Scroll Speed: ${config.scrollSpeed}`, 'success');
            } else {
                updateStatus('❌ GridStack Auto-Scroll not found.', 'error');
            }
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testScrollFix();
            }, 500);
        });
        
        // Add some interactive elements to test with
        document.addEventListener('DOMContentLoaded', () => {
            // Create some draggable test elements
            const testSection = document.querySelector('.test-section');
            testSection.style.cursor = 'grab';
            testSection.addEventListener('mousedown', () => {
                testSection.style.cursor = 'grabbing';
            });
            testSection.addEventListener('mouseup', () => {
                testSection.style.cursor = 'grab';
            });
        });
    </script>
</body>
</html>
