// Bar Chart JSON Search Functionality

// Sample data sources - in a real application, these would be populated from server or configuration
window.jsonDataSources = {
  local: [
    {
      id: "local1",
      name: "Monthly Revenue 2023",
      path: "assets/data/monthly_revenue_2023.json",
    },
    {
      id: "local2",
      name: "Quarterly Sales",
      path: "assets/data/quarterly_sales.json",
    },
    {
      id: "local3",
      name: "Product Categories",
      path: "assets/data/product_categories.json",
    },
  ],
  remote: [
    {
      id: "remote1",
      name: "Global Sales Data",
      url: "https://api.example.com/data/global_sales",
    },
    {
      id: "remote2",
      name: "Regional Performance",
      url: "https://api.example.com/data/regional_performance",
    },
    {
      id: "remote3",
      name: "Industry Benchmarks",
      url: "https://api.example.com/data/industry_benchmarks",
    },
  ],
};

// Function to search for JSON data
window.searchBarChartJSON = function () {
  const searchInput = document.getElementById("barChartJsonSearch");
  const dataSource = document.getElementById("barChartDataSource");
  const searchResults = document.getElementById("barChartSearchResults");
  const searchResultsBody = searchResults.querySelector(".search-results-body");

  // Get search query and selected data source
  const query = searchInput.value.trim().toLowerCase();
  const source = dataSource.value;

  // Clear previous results
  searchResultsBody.innerHTML = "";

  if (!query) {
    searchResults.style.display = "none";
    return;
  }

  // Search through the selected data source
  const dataArray = window.jsonDataSources[source] || [];
  const filteredResults = dataArray.filter((item) => {
    return (
      item.name.toLowerCase().includes(query) ||
      item.description?.toLowerCase().includes(query) ||
      item.tags?.some((tag) => tag.toLowerCase().includes(query))
    );
  });

  // Display results
  if (filteredResults.length > 0) {
    filteredResults.forEach((item) => {
      const resultItem = document.createElement("div");
      resultItem.className = "search-result-item";
      resultItem.innerHTML = `
        <div class="result-name">${item.name}</div>
        <div class="result-description">${
          item.description || "No description"
        }</div>
        <div class="result-path">${item.path}</div>
      `;

      resultItem.addEventListener("click", () => {
        // Load the selected JSON data
        loadBarChartData(item.path, item.name);
        searchResults.style.display = "none";
        searchInput.value = "";
      });

      searchResultsBody.appendChild(resultItem);
    });
    searchResults.style.display = "block";
  } else {
    searchResultsBody.innerHTML =
      '<div class="no-results">No results found</div>';
    searchResults.style.display = "block";
  }
};

// Function to toggle search results visibility
window.toggleSearchResults = function () {
  const searchResults = document.getElementById("barChartSearchResults");
  searchResults.style.display = "none";
};

// Function to get human-readable source label
function getSourceLabel(source) {
  switch (source) {
    case "local":
      return "Local Assets";
    case "remote":
      return "Remote Repository";
    default:
      return source;
  }
}

// Function to load JSON data for bar chart
window.loadBarChartJSONData = async function (dataId, sourceType) {
  // Find the selected data item
  const sourceArray = window.jsonDataSources[sourceType] || [];
  const dataItem = sourceArray.find((item) => item.id === dataId);

  if (!dataItem) {
    console.error("Data item not found:", dataId);
    return;
  }

  try {
    let chartData = [];

    // Load data based on source type
    if (sourceType === "local") {
      // Load from local file
      const response = await fetch(dataItem.path);
      if (!response.ok) {
        throw new Error(`Failed to load data: ${response.status}`);
      }
      const data = await response.json();
      chartData = transformDataToBarChartFormat(data);
    } else if (sourceType === "remote") {
      // Load from remote API
      const response = await fetch(dataItem.url);
      if (!response.ok) {
        throw new Error(`Failed to load remote data: ${response.status}`);
      }
      const data = await response.json();
      chartData = transformDataToBarChartFormat(data);
    }

    // Update the data table with the loaded data
    updateBarChartDataTable(chartData);

    // Hide search results
    toggleSearchResults();

    // Show success message
    alert(`Data "${dataItem.name}" loaded successfully`);
  } catch (error) {
    console.error("Error loading JSON data:", error);
    alert(`Error loading data: ${error.message}`);
  }
};

// Function to transform generic JSON data to bar chart format
function transformDataToBarChartFormat(data) {
  // This function would need to be customized based on the expected data format
  // For now, we'll assume a simple format with category and value properties

  if (Array.isArray(data)) {
    // If data is already an array, try to extract category and value
    return data.map((item) => {
      // Try to find category and value fields with various possible names
      const category =
        item.category || item.name || item.label || Object.keys(item)[0];
      const value =
        item.value || item.count || item.amount || Object.values(item)[0];

      return {
        category: category,
        value: parseFloat(value) || 0,
      };
    });
  } else if (typeof data === "object") {
    // If data is an object, convert keys to categories and values to values
    return Object.entries(data).map(([key, value]) => ({
      category: key,
      value: parseFloat(value) || 0,
    }));
  }

  // Default empty array if data format is not recognized
  return [];
}

// Function to update the bar chart data table with loaded data
function updateBarChartDataTable(chartData) {
  const tableBody = document.getElementById("barChartDataBody");
  if (!tableBody) return;

  // Clear existing rows
  tableBody.innerHTML = "";

  // Add rows for each data point
  chartData.forEach((item) => {
    if (typeof addBarChartDataRow === "function") {
      // Use existing function if available
      addBarChartDataRow(tableBody, item.category, item.value);
    } else {
      // Fallback implementation
      const row = document.createElement("tr");

      // Category cell
      const categoryCell = document.createElement("td");
      const categoryInput = document.createElement("input");
      categoryInput.type = "text";
      categoryInput.className = "form-control form-control-sm";
      categoryInput.value = item.category;
      categoryCell.appendChild(categoryInput);

      // Value cell
      const valueCell = document.createElement("td");
      const valueInput = document.createElement("input");
      valueInput.type = "number";
      valueInput.className = "form-control form-control-sm";
      valueInput.value = item.value;
      valueCell.appendChild(valueInput);

      // Actions cell
      const actionsCell = document.createElement("td");
      const deleteButton = document.createElement("button");
      deleteButton.className = "btn btn-sm btn-outline-danger";
      deleteButton.innerHTML = '<i class="las la-trash"></i>';
      deleteButton.onclick = function () {
        row.remove();
      };
      actionsCell.appendChild(deleteButton);

      // Add cells to row
      row.appendChild(categoryCell);
      row.appendChild(valueCell);
      row.appendChild(actionsCell);

      // Add row to table
      tableBody.appendChild(row);
    }
  });
}
