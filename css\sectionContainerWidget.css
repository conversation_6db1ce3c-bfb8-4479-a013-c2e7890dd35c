/* Section Container Widget Styles */
.section-container-widget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border-radius: 6px;
  transition: all 0.3s ease;
  z-index: 1;
  /* Prevent unwanted scrolling */
  overflow: hidden;
}

.section-content {
  flex: 1;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  position: relative;
  padding: 4px;
  overflow: hidden;
  z-index: 2;
}

/* Settings icon styles */
.widget-header .btn-link {
  padding: 0;
  color: #333;
  text-decoration: none;
}

.widget-header .btn-link:hover {
  color: #02104f;
}

.widget-header .la-cog,
.widget-header .la-times {
  font-size: 1.1rem;
}

.widget-header .la-times:hover {
  color: #dc3545;
}

/* Improved header visibility */
.section-container-widget .widget-header {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 8px 12px;
  border-radius: 4px;
  margin: -8px -8px 8px -8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-container-widget .widget-header i.las {
  margin-right: 6px;
  font-size: 18px;
  color: var(--ocean-teal);
}

/* Improve nested grid visibility and accessibility for drops */
.section-content .grid-stack {
  z-index: 3;
  border: 1px dashed rgba(0, 0, 0, 0.1);
  min-height: 100%;
}

/* Make the nested grid visually distinct when dragging over it */
.section-content .grid-stack.grid-stack-dropzone {
  transition: background-color 0.2s ease;
}

.section-content .grid-stack.grid-stack-dropzone:empty {
  background-color: rgba(0, 177, 156, 0.05);
  border: 1px dashed rgba(0, 177, 156, 0.3);
}

/* Make nested grid obvious when an item is being dragged */
.ui-draggable-dragging ~ .section-content .grid-stack {
  background-color: rgba(0, 177, 156, 0.1);
  border: 1px dashed rgba(0, 177, 156, 0.5);
}

/* Adjust placeholder style for section containers */
.section-content .grid-stack-placeholder > .placeholder-content {
  background: rgba(0, 177, 156, 0.15) !important;
  border: 2px dashed rgba(0, 177, 156, 0.4) !important;
}

/* Style for widgets inside the section container */
.section-container-widget .grid-stack-item-content {
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
  color: #333333;
}

/* Widget header specific styling for section content */
.section-content .grid-stack-item .widget-header {
  opacity: 0.3;
}

/* Prevent unwanted scrolling during drag operations */
.section-container-widget.dragging,
.section-container-widget.drag-active {
  overflow: hidden !important;
}

/* Prevent body scrolling when dragging in section containers */
body.dragging-in-section {
  overflow: hidden !important;
}

/* Nested grid container specific styles */
.nested-grid-container {
  overflow: hidden !important;
  position: relative;
}

.nested-grid-container .grid-stack {
  overflow: hidden !important;
}

/* Grid stack nested specific styles */
.grid-stack-nested {
  overflow: hidden !important;
}

/* Prevent scroll on grid items during drag */
.grid-stack-item.ui-draggable-dragging {
  overflow: hidden !important;
}

/* Section content specific overflow control */
.section-content {
  overflow: hidden !important;
}

/* Additional drag state indicators */
.section-container-widget.drag-over {
  border: 2px dashed #007bff !important;
  background-color: rgba(0, 123, 255, 0.1) !important;
}

.section-container-widget.drag-active {
  border: 2px solid #007bff !important;
  background-color: rgba(0, 123, 255, 0.05) !important;
}

/* Prevent text selection during drag */
.section-container-widget.dragging *,
.section-container-widget.drag-active * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* Ensure proper containment for nested grids */
.section-container-widget .grid-stack {
  contain: layout style paint;
}

/* Prevent scroll propagation from nested elements */
.section-container-widget * {
  scroll-behavior: auto;
}

/* Override any inherited scroll settings */
.section-container-widget,
.section-container-widget * {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.section-container-widget::-webkit-scrollbar,
.section-container-widget *::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Enhanced section container detection */
.section-container-widget,
.nested-grid-container,
.grid-stack-nested,
.section-content {
  /* Force these elements to be easily detectable */
  position: relative;
  z-index: auto;
}

/* Prevent any scroll events from bubbling up */
.section-container-widget *,
.nested-grid-container *,
.grid-stack-nested *,
.section-content * {
  scroll-behavior: auto !important;
  overflow: visible !important;
}

/* Specific override for grid-stack elements inside sections */
.section-container-widget .grid-stack,
.nested-grid-container .grid-stack,
.grid-stack-nested .grid-stack {
  overflow: hidden !important;
  scroll-behavior: auto !important;
}

/* Prevent wheel events from causing scrolling */
.section-container-widget,
.nested-grid-container,
.grid-stack-nested,
.section-content {
  overscroll-behavior: contain;
  -webkit-overflow-scrolling: auto;
}

/* Additional state classes for better detection */
.section-container-widget.in-section,
.nested-grid-container.in-section,
.grid-stack-nested.in-section,
.section-content.in-section {
  overflow: hidden !important;
  scroll-behavior: auto !important;
}

/* Force disable scrolling for all section-related elements */
.section-container-widget *,
.nested-grid-container *,
.grid-stack-nested *,
.section-content * {
  scroll-behavior: auto !important;
}

/* Prevent any touch scrolling on mobile */
@media (pointer: coarse) {
  .section-container-widget,
  .nested-grid-container,
  .grid-stack-nested,
  .section-content {
    touch-action: none;
    -webkit-overflow-scrolling: auto;
  }
}

/* Global scroll prevention when working with section containers */
body.scroll-prevented {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

body.scroll-prevented * {
  scroll-behavior: auto !important;
}

/* Enhanced scroll prevention for section containers */
.section-container-widget,
.nested-grid-container,
.grid-stack-nested,
.section-content {
  /* Prevent any form of scrolling */
  overflow: hidden !important;
  scroll-behavior: auto !important;
  overscroll-behavior: none !important;
  -webkit-overflow-scrolling: auto !important;
}

/* Prevent wheel events from causing scrolling */
.section-container-widget,
.nested-grid-container,
.grid-stack-nested,
.section-content {
  /* Block wheel events that could cause scrolling */
  pointer-events: auto;
}

/* Additional prevention for nested elements */
.section-container-widget *,
.nested-grid-container *,
.grid-stack-nested *,
.section-content * {
  /* Ensure no scrolling behavior */
  scroll-behavior: auto !important;
  overflow: visible !important;
}

/* Specific prevention for grid elements */
.section-container-widget .grid-stack,
.nested-grid-container .grid-stack,
.grid-stack-nested .grid-stack {
  overflow: hidden !important;
  scroll-behavior: auto !important;
}

/* Prevent any scroll events */
.section-container-widget,
.nested-grid-container,
.grid-stack-nested,
.section-content {
  /* Block scroll events */
  scroll-behavior: auto !important;
}

/* Force disable all scrolling mechanisms */
.section-container-widget,
.nested-grid-container,
.grid-stack-nested,
.section-content {
  /* Disable all scroll-related properties */
  scroll-behavior: auto !important;
  overflow: hidden !important;
  overscroll-behavior: none !important;
  -webkit-overflow-scrolling: auto !important;
  -ms-overflow-style: none !important;
}

/* Additional mobile prevention */
@media (pointer: coarse) {
  .section-container-widget,
  .nested-grid-container,
  .grid-stack-nested,
  .section-content {
    touch-action: none !important;
    -webkit-overflow-scrolling: auto !important;
    overscroll-behavior: none !important;
  }
}
