/**
 * Test script to verify pie chart onclick and drag-drop consistency
 * Run this in the browser console to check if both methods produce identical markup
 */

function testPieChartConsistency() {
  console.log('🧪 Testing Pie Chart Onclick vs Drag-Drop Consistency...');
  
  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };
  
  function addTest(name, condition, message) {
    const passed = condition;
    results.tests.push({ name, passed, message });
    if (passed) {
      results.passed++;
      console.log(`✅ ${name}: ${message}`);
    } else {
      results.failed++;
      console.log(`❌ ${name}: ${message}`);
    }
  }
  
  // Test 1: Check if getPieChartWidgetMarkup function exists
  addTest(
    'Shared Markup Function Available',
    typeof window.getPieChartWidgetMarkup === 'function',
    typeof window.getPieChartWidgetMarkup === 'function' ? 'getPieChartWidgetMarkup function found' : 'getPieChartWidgetMarkup function not found'
  );
  
  // Test 2: Check if addPieChartWidget function exists
  addTest(
    'Click Handler Available',
    typeof window.addPieChartWidget === 'function',
    typeof window.addPieChartWidget === 'function' ? 'addPieChartWidget function found' : 'addPieChartWidget function not found'
  );
  
  // Test 3: Check if setupPieChartDragDrop function exists
  addTest(
    'Drag-Drop Setup Available',
    typeof window.setupPieChartDragDrop === 'function',
    typeof window.setupPieChartDragDrop === 'function' ? 'setupPieChartDragDrop function found' : 'setupPieChartDragDrop function not found'
  );
  
  // Test 4: Test markup consistency
  if (typeof window.getPieChartWidgetMarkup === 'function') {
    try {
      const testChartId1 = 'test-chart-1';
      const testChartId2 = 'test-chart-2';
      
      const markup1 = window.getPieChartWidgetMarkup(testChartId1);
      const markup2 = window.getPieChartWidgetMarkup(testChartId2);
      
      // Check if markup contains expected elements
      const hasWidgetClass = markup1.includes('class="widget');
      const hasChartContainer = markup1.includes('chart-container');
      const hasSettings = markup1.includes('pieChartSettings');
      const hasCorrectId = markup1.includes(testChartId1);
      
      addTest(
        'Markup Contains Widget Class',
        hasWidgetClass,
        hasWidgetClass ? 'Widget class found in markup' : 'Widget class missing from markup'
      );
      
      addTest(
        'Markup Contains Chart Container',
        hasChartContainer,
        hasChartContainer ? 'Chart container found in markup' : 'Chart container missing from markup'
      );
      
      addTest(
        'Markup Contains Settings Button',
        hasSettings,
        hasSettings ? 'Settings button found in markup' : 'Settings button missing from markup'
      );
      
      addTest(
        'Markup Uses Correct Chart ID',
        hasCorrectId,
        hasCorrectId ? 'Chart ID correctly inserted in markup' : 'Chart ID not found in markup'
      );
      
      // Test that different IDs produce different markup
      const isDifferent = markup1 !== markup2;
      addTest(
        'Different IDs Produce Different Markup',
        isDifferent,
        isDifferent ? 'Markup correctly varies with different chart IDs' : 'Markup identical despite different chart IDs'
      );
      
    } catch (error) {
      addTest(
        'Markup Generation',
        false,
        `Error generating markup: ${error.message}`
      );
    }
  }
  
  // Test 5: Check if pie chart widget exists in gallery
  const pieChartWidget = document.querySelector('.widget-item[data-widget-type="pie-chart"]');
  addTest(
    'Pie Chart Widget in Gallery',
    pieChartWidget !== null,
    pieChartWidget ? 'Pie chart widget found in gallery' : 'Pie chart widget not found in gallery'
  );
  
  // Test 6: Check if widget has onclick handler
  if (pieChartWidget) {
    const hasOnclick = pieChartWidget.hasAttribute('onclick') || pieChartWidget.onclick;
    addTest(
      'Widget Has Click Handler',
      hasOnclick,
      hasOnclick ? 'Pie chart widget has onclick handler' : 'Pie chart widget missing onclick handler'
    );
  }
  
  // Test 7: Check if GridStack drag-in is setup
  const hasGridStackSetup = typeof GridStack !== 'undefined' && GridStack.setupDragIn;
  addTest(
    'GridStack Available',
    hasGridStackSetup,
    hasGridStackSetup ? 'GridStack and setupDragIn available' : 'GridStack or setupDragIn not available'
  );
  
  // Test 8: Check for duplicate code cleanup
  const indexHtmlDuplicates = document.documentElement.outerHTML.includes('pieChartSidebarContent');
  addTest(
    'No Duplicate Code in HTML',
    !indexHtmlDuplicates,
    !indexHtmlDuplicates ? 'No duplicate pieChartSidebarContent found in HTML' : 'Duplicate pieChartSidebarContent still exists in HTML'
  );

  // Test 9: Simulate markup generation for both methods
  if (typeof window.getPieChartWidgetMarkup === 'function') {
    try {
      // Simulate onclick markup generation
      const onclickChartId = `piechart-${Date.now()}`;
      const onclickMarkup = window.getPieChartWidgetMarkup(onclickChartId);
      
      // Simulate drag-drop markup generation
      const dragDropChartId = `piechart-${Date.now()}-${Math.floor(Math.random() * 100000)}`;
      const dragDropMarkup = window.getPieChartWidgetMarkup(dragDropChartId);
      
      // Both should have same structure, different IDs
      const onclickStructure = onclickMarkup.replace(/piechart-\d+(-\d+)?/g, 'CHART_ID');
      const dragDropStructure = dragDropMarkup.replace(/piechart-\d+(-\d+)?/g, 'CHART_ID');
      
      const structureMatches = onclickStructure === dragDropStructure;
      addTest(
        'Onclick and Drag-Drop Structure Match',
        structureMatches,
        structureMatches ? 'Both methods produce identical structure' : 'Structure differs between onclick and drag-drop'
      );
      
    } catch (error) {
      addTest(
        'Markup Consistency Test',
        false,
        `Error testing markup consistency: ${error.message}`
      );
    }
  }
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! Pie chart onclick and drag-drop are consistent.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the implementation for consistency issues.');
  }
  
  return results;
}

// Auto-run test if in development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
  // Wait for scripts to load
  setTimeout(() => {
    testPieChartConsistency();
  }, 3000);
}

// Export for manual testing
window.testPieChartConsistency = testPieChartConsistency;
