/**
 * Preview Mode Fix
 * Ensures chart heights are maintained in preview mode
 */

/* Fix for chart heights in preview mode */
.grid-stack-item.ui-draggable-disabled .chart-container,
.grid-stack-item.ui-resizable-disabled .chart-container {
  height: 100% !important;
  min-height: 250px !important;
}

.grid-stack-item.ui-draggable-disabled .grid-stack-item-content,
.grid-stack-item.ui-resizable-disabled .grid-stack-item-content {
  height: 100% !important;
}

.grid-stack-item.ui-draggable-disabled .widget-body,
.grid-stack-item.ui-resizable-disabled .widget-body {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Ensure grid stack items have proper dimensions in preview mode */
.grid-stack-item.ui-draggable-disabled,
.grid-stack-item.ui-resizable-disabled {
  min-height: 300px !important;
}

/* Enhanced Preview Eye Icon Styling - Subtle & Professional */
/* Increased specificity to override Bootstrap styles */
.widget-header .widget-eye-icon.btn,
.widget-eye-icon.btn.btn-sm {
  /* Base styling - subtle and professional */
  background: #f8f9fa !important;
  border: 1px solid #dee2e6 !important;
  border-radius: 6px !important;
  width: 28px !important;
  height: 28px !important;
  padding: 0 !important;
  margin: 0 2px !important;

  /* Icon styling */
  color: #6c757d !important;
  font-size: 12px !important;

  /* Subtle effects */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;

  /* Positioning */
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;

  /* Remove default button styles */
  text-decoration: none !important;
  outline: none !important;
}

/* Hover effect - subtle enhancement */
.widget-header .widget-eye-icon.btn:hover,
.widget-eye-icon.btn.btn-sm:hover {
  background: #e9ecef !important;
  border-color: #adb5bd !important;
  color: #495057 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15) !important;
  transform: translateY(-1px) !important;
}

/* Active/Preview mode styling - professional blue accent */
.widget-header .widget-eye-icon.btn.active,
.widget-eye-icon.btn.btn-sm.active {
  background: #007365 !important;
  border-color: #007365 !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(0, 115, 101, 0.25) !important;
}

.widget-header .widget-eye-icon.btn.active:hover,
.widget-eye-icon.btn.btn-sm.active:hover {
  background: #005a4d !important;
  border-color: #005a4d !important;
  color: white !important;
  box-shadow: 0 3px 6px rgba(0, 115, 101, 0.3) !important;
  transform: translateY(-1px) !important;
}

/* Focus state for accessibility */
.widget-header .widget-eye-icon.btn:focus,
.widget-eye-icon.btn.btn-sm:focus {
  box-shadow: 0 0 0 2px rgba(0, 115, 101, 0.25) !important;
  outline: none !important;
}

/* Icon animation - subtle */
.widget-header .widget-eye-icon.btn i,
.widget-eye-icon.btn.btn-sm i {
  transition: transform 0.15s ease !important;
}

.widget-header .widget-eye-icon.btn:hover i,
.widget-eye-icon.btn.btn-sm:hover i {
  transform: scale(1.05) !important;
}

/* Pressed state */
.widget-header .widget-eye-icon.btn:active,
.widget-eye-icon.btn.btn-sm:active {
  transform: translateY(0) scale(0.98) !important;
  transition: all 0.1s ease !important;
}

/* Ensure proper icon colors */
.widget-header .widget-eye-icon.btn i.las,
.widget-eye-icon.btn.btn-sm i.las {
  color: inherit !important;
}

/* Compact spacing in widget headers */
.widget-header .widget-eye-icon.btn {
  margin-left: 4px !important;
  margin-right: 2px !important;
}

/* Special styling for Smart Widget Composer headers - matching website theme */
.section-container-widget .widget-header .widget-eye-icon.btn {
  background: transparent !important;
  border-color: transparent !important;
  color: #6c757d !important;
  box-shadow: none !important;
}

.section-container-widget .widget-header .widget-eye-icon.btn:hover {
  background: transparent !important;
  border-color: transparent !important;
  color: #6c757d !important;
  box-shadow: none !important;
}

.section-container-widget .widget-header .widget-eye-icon.btn.active {
  background: transparent !important;
  border-color: transparent !important;
  color: #6c757d !important;
  box-shadow: none !important;
}

.section-container-widget .widget-header .widget-eye-icon.btn.active:hover {
  background: transparent !important;
  border-color: transparent !important;
  color: #6c757d !important;
  box-shadow: none !important;
}

/* Tooltip enhancement - subtle */
.widget-eye-icon.btn[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(33, 37, 41, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .widget-eye-icon.btn {
    width: 24px !important;
    height: 24px !important;
    font-size: 11px !important;
  }
}
