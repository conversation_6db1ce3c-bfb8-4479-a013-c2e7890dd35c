let dualAxisChartCounter = 0;

/**
 * Generate dual axis chart widget markup
 * Shared between onclick and drag-drop functionality
 */
function getDualAxisChartWidgetMarkup(chartId) {
  return `
    <div class="widget p-2">
      <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
        <div class="widget-title">
          <span>Dual Axis Chart</span>
        </div>
        <div class="widget-actions">
          <button class="btn btn-link"
                  data-bs-toggle="offcanvas"
                  data-bs-target="#dualAxisChartSettings"
                  aria-controls="dualAxisChartSettings"
                  onclick="initDualAxisChartSettings('${chartId}')">
            <i class="las la-cog"></i>
          </button>
          <button class="btn btn-link ms-1"
                  onclick="removeWidget(this)">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
        <div id="${chartId}" class="chart-container" style="flex: 1; width: 100%; height: 100%; min-height: 400px; position: relative;"></div>
      </div>
    </div>
  `;
}

// Add a dual axis chart widget using amCharts v5
function addDualAxisChartWidget() {
  console.log("🔄 Adding dual axis chart widget...");

  // Check if grid is available
  if (typeof window.grid === 'undefined' && typeof grid === 'undefined') {
    console.error("❌ Grid not available. Make sure GridStack is initialized.");
    console.log("🔍 Available globals:", Object.keys(window).filter(k => k.includes('grid') || k.includes('Grid')));
    alert("Grid not available. Please refresh the page and try again.");
    return;
  }

  // Use global grid variable (try both window.grid and grid)
  const gridInstance = window.grid || grid;

  try {
    const chartId = `dual-axis-chart-${Date.now()}`;
    console.log("📊 Creating dual axis chart with ID:", chartId);

    // Add the widget to the grid using shared markup
    gridInstance.addWidget({
      x: 0,
      y: 0,
      w: 8,
      h: 6,
      content: getDualAxisChartWidgetMarkup(chartId)
    });

    console.log("✅ Widget added to grid successfully");

    // Initialize the chart after a short delay to ensure DOM is ready
    setTimeout(async () => {
      try {
        console.log("🔄 Initializing dual axis chart...");
        await waitForAmCharts();
        await initDualAxisChart(chartId);
        console.log("✅ Dual axis chart initialized successfully");
      } catch (error) {
        console.error("❌ Error initializing dual axis chart:", error);
        
        // Show error in the chart container
        const container = document.getElementById(chartId);
        if (container) {
          container.innerHTML = `
            <div class="alert alert-danger m-3">
              <h6>Chart Initialization Failed</h6>
              <p>Failed to initialize dual axis chart. Please try again.</p>
              <small class="text-muted">${error.message}</small>
            </div>
          `;
        }
      }
    }, 100);

  } catch (error) {
    console.error("❌ Error adding dual axis chart widget:", error);
    alert("Failed to add dual axis chart widget. Please try again.");
  }
}

/**
 * Initialize dual axis chart with amCharts v5
 */
async function initDualAxisChart(chartId) {
  console.log("Initializing dual axis chart:", chartId);

  const chartContainer = document.getElementById(chartId);
  if (!chartContainer) {
    throw new Error(`Chart container with ID ${chartId} not found`);
  }

  // Check if chart is already being initialized or already exists
  if (chartContainer.hasAttribute('data-initializing')) {
    console.log("⚠️ Chart is already being initialized, skipping...");
    return;
  }

  if (chartContainer.hasAttribute('data-initialized')) {
    console.log("⚠️ Chart is already initialized, skipping...");
    return;
  }

  // Mark as initializing
  chartContainer.setAttribute('data-initializing', 'true');

  // Check if there's already a chart instance and dispose it
  if (chartContainer.chart) {
    console.log("🔄 Disposing existing chart instance...");
    try {
      chartContainer.chart.dispose();
    } catch (e) {
      console.warn("Warning disposing chart:", e);
    }
    chartContainer.chart = null;
  }

  // Ensure container has proper dimensions
  if (chartContainer.offsetWidth === 0 || chartContainer.offsetHeight === 0) {
    console.warn("⚠️ Chart container has zero dimensions, setting minimum size");
    chartContainer.style.minWidth = '600px';
    chartContainer.style.minHeight = '400px';
  }

  // Add temporary visual indicator
  chartContainer.style.border = '2px dashed #00b19c';
  chartContainer.style.backgroundColor = '#f8f9fa';
  
  // Add loading text
  chartContainer.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">Loading Dual Axis Chart...</div>';
  
  console.log("📊 Container prepared with dimensions:", chartContainer.offsetWidth, 'x', chartContainer.offsetHeight);

  try {
    // Clear loading indicator
    chartContainer.innerHTML = '';
    chartContainer.style.border = 'none';
    chartContainer.style.backgroundColor = 'transparent';
    
    // Create root element
    const root = am5.Root.new(chartId);

    // Set themes with custom branding
    root.setThemes([
      am5themes_Animated.new(root)
    ]);

    // Create chart
    const chart = root.container.children.push(am5xy.XYChart.new(root, {
      panX: true,
      panY: true,
      wheelX: "panX",
      wheelY: "zoomX",
      pinchZoomX: true,
      layout: root.verticalLayout
    }));

    // Sample data - using timestamps for amCharts v5 compatibility
    const data = [
      { "date": 1672511400000, "value1": 10, "value2": 100 },   // Day 1
      { "date": 1672597800000, "value1": 15, "value2": 150 },   // Day 2
      { "date": 1672684200000, "value1": 12, "value2": 120 },   // Day 3
      { "date": 1672770600000, "value1": 18, "value2": 180 },   // Day 4
      { "date": 1672857000000, "value1": 20, "value2": 200 },   // Day 5
      { "date": 1672943400000, "value1": 17, "value2": 170 },   // Day 6
      { "date": 1673029800000, "value1": 22, "value2": 220 },   // Day 7
      { "date": 1673116200000, "value1": 25, "value2": 250 },   // Day 8
      { "date": 1673202600000, "value1": 23, "value2": 230 },   // Day 9
      { "date": 1673289000000, "value1": 28, "value2": 280 },   // Day 10
      { "date": 1673375400000, "value1": 30, "value2": 300 },   // Day 11
      { "date": 1673461800000, "value1": 27, "value2": 270 },   // Day 12
      { "date": 1673548200000, "value1": 32, "value2": 320 },   // Day 13
      { "date": 1673634600000, "value1": 35, "value2": 350 }    // Day 14
    ];

    // Create X-axis (Date axis)
    const xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
      baseInterval: {
        timeUnit: "day",
        count: 1
      },
      renderer: am5xy.AxisRendererX.new(root, {
        minorGridEnabled: false,
        minGridDistance: 50,
        cellStartLocation: 0.1,
        cellEndLocation: 0.9
      }),
      tooltip: am5.Tooltip.new(root, {
        tooltipDateFormat: "yyyy-MM-dd"
      })
    }));

    // Style X-axis labels
    xAxis.get("renderer").labels.template.setAll({
      fontSize: "12px",
      fontFamily: "Montserrat, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif",
      fill: am5.color("#02104f") // Brand navy color
    });

    console.log("📊 Created X-axis (Date axis)");

    // Create Y-axis 1 (Left - for columns)
    const yAxis1 = chart.yAxes.push(am5xy.ValueAxis.new(root, {
      renderer: am5xy.AxisRendererY.new(root, {
        strokeOpacity: 0.1
      }),
      tooltip: am5.Tooltip.new(root, {})
    }));

    // Style Y-axis 1 labels
    yAxis1.get("renderer").labels.template.setAll({
      fontSize: "12px",
      fontFamily: "Montserrat, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif",
      fill: am5.color("#00b19c") // Column series color
    });

    console.log("📊 Created Y-axis 1 (Left - Columns)");

    // Create Y-axis 2 (Right - for line)
    const yAxis2 = chart.yAxes.push(am5xy.ValueAxis.new(root, {
      renderer: am5xy.AxisRendererY.new(root, {
        strokeOpacity: 0.1,
        opposite: true
      }),
      tooltip: am5.Tooltip.new(root, {})
    }));

    // Style Y-axis 2 labels
    yAxis2.get("renderer").labels.template.setAll({
      fontSize: "12px",
      fontFamily: "Montserrat, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif",
      fill: am5.color("#3bcd3f") // Line series color
    });

    console.log("📊 Created Y-axis 2 (Right - Line)");

    // Brand colors
    const brandColors = window.chartConfig?.brandColors || [
      "#00b19c", "#3bcd3f", "#007365", "#8dbac4", "#02104f"
    ];

    // Create Column Series (Left Y-axis)
    const columnSeries = chart.series.push(am5xy.ColumnSeries.new(root, {
      name: "Column Series",
      xAxis: xAxis,
      yAxis: yAxis1,
      valueYField: "value1",
      valueXField: "date",
      tooltip: am5.Tooltip.new(root, {
        labelText: "{name}: {valueY}",
        pointerOrientation: "vertical"
      })
    }));

    // Style columns
    columnSeries.columns.template.setAll({
      fill: am5.color(brandColors[0]), // Ocean Teal
      stroke: am5.color("#ffffff"),
      strokeWidth: 2,
      cornerRadiusTL: 4,
      cornerRadiusTR: 4,
      fillOpacity: 0.9,
      width: am5.percent(60)
    });

    // Column hover effects
    columnSeries.columns.template.states.create("hover", {
      fillOpacity: 1,
      strokeWidth: 3
    });

    console.log("📊 Created column series");

    // Create Line Series (Right Y-axis)
    const lineSeries = chart.series.push(am5xy.LineSeries.new(root, {
      name: "Line Series",
      xAxis: xAxis,
      yAxis: yAxis2,
      valueYField: "value2",
      valueXField: "date",
      tooltip: am5.Tooltip.new(root, {
        labelText: "{name}: {valueY}"
      })
    }));

    // Style line
    lineSeries.strokes.template.setAll({
      stroke: am5.color(brandColors[1]), // Emerald Green
      strokeWidth: 3
    });

    // Add bullets to line
    lineSeries.bullets.push(function () {
      return am5.Bullet.new(root, {
        sprite: am5.Circle.new(root, {
          radius: 4,
          fill: am5.color(brandColors[1]),
          stroke: am5.color("#ffffff"),
          strokeWidth: 2
        })
      });
    });

    console.log("📊 Created line series");

    console.log("Applied brand colors to dual axis chart:", brandColors[0], brandColors[1]);

    // Add scrollbar
    chart.set("scrollbarX", am5.Scrollbar.new(root, {
      orientation: "horizontal"
    }));

    // Add cursor with zoom behavior
    const cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
      behavior: "zoomX"
    }));
    cursor.lineY.set("visible", false);

    // Add legend
    const legend = chart.children.push(am5.Legend.new(root, {
      centerX: am5.p50,
      x: am5.p50
    }));

    // Add series to legend
    legend.data.setAll([columnSeries, lineSeries]);

    // Set data
    console.log("📊 Setting data for dual axis chart:", data.length, "data points");
    console.log("📊 Sample data point:", data[0]);

    columnSeries.data.setAll(data);
    lineSeries.data.setAll(data);
    xAxis.data.setAll(data);

    console.log("📊 Data set on all series and axis");

    // Animations
    columnSeries.appear(1000, 100);
    lineSeries.appear(1000, 100);
    chart.appear(1000, 100);

    // Store chart reference for cleanup
    chartContainer.chart = root;

    // Check container dimensions
    const containerRect = chartContainer.getBoundingClientRect();
    console.log("📊 Container dimensions:", {
      width: containerRect.width,
      height: containerRect.height,
      visible: containerRect.width > 0 && containerRect.height > 0
    });

    // Force a resize to ensure proper rendering
    setTimeout(() => {
      if (root && !root.isDisposed()) {
        root.resize();
        console.log("📊 Forced chart resize");
      }
    }, 100);

    // Mark as successfully initialized
    chartContainer.removeAttribute('data-initializing');
    chartContainer.setAttribute('data-initialized', 'true');

    console.log("✅ Dual axis chart initialized successfully");

  } catch (error) {
    console.error("❌ Error in initDualAxisChart:", error);

    // Clean up on error
    chartContainer.removeAttribute('data-initializing');

    // Show error message in container
    chartContainer.innerHTML = `
      <div class="alert alert-danger m-3">
        <h6>Chart Initialization Failed</h6>
        <p>Failed to initialize dual axis chart.</p>
        <small class="text-muted">${error.message}</small>
        <br>
        <button class="btn btn-sm btn-outline-danger mt-2" onclick="initDualAxisChart('${chartId}')">
          Retry
        </button>
      </div>
    `;

    throw error;
  }
}

/**
 * Wait for amCharts libraries to load
 */
function waitForAmCharts() {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    const maxAttempts = 50;

    const checkAmCharts = () => {
      attempts++;

      if (typeof am5 !== 'undefined' && typeof am5xy !== 'undefined' && typeof am5themes_Animated !== 'undefined') {
        resolve();
      } else if (attempts >= maxAttempts) {
        reject(new Error('amCharts libraries failed to load after maximum attempts'));
      } else {
        setTimeout(checkAmCharts, 100);
      }
    };

    checkAmCharts();
  });
}

/**
 * Initialize settings for dual axis chart (placeholder)
 */
function initDualAxisChartSettings(chartId) {
  console.log('Opening settings for dual axis chart:', chartId);
  // Settings functionality can be implemented later
}

/**
 * Setup drag-drop functionality for dual axis chart widgets
 * This ensures both onclick and drag-drop use the same markup and logic
 */
function setupDualAxisChartDragDrop() {
  console.log('📊 Setting up dual axis chart drag-drop functionality...');

  const dualAxisChartSidebarContent = [
    {
      w: 8,
      h: 6,
      get content() {
        const chartId = "dual-axis-chart-" + Date.now() + "-" + Math.floor(Math.random() * 100000);

        // Use the same markup function as onclick
        const markup = getDualAxisChartWidgetMarkup(chartId);

        // Store initialization data for the grid "added" event handler to pick up
        if (!window.pendingDualAxisChartInits) {
          window.pendingDualAxisChartInits = new Map();
        }

        window.pendingDualAxisChartInits.set(chartId, {
          chartId,
          timestamp: Date.now()
        });

        console.log('📊 Drag-drop: Stored initialization data for', chartId);

        return markup;
      },
    },
  ];

  // Setup GridStack drag-in
  if (typeof GridStack !== 'undefined' && GridStack.setupDragIn) {
    GridStack.setupDragIn(
      '.widget-item[data-widget-type="dual-axis-chart"]',
      undefined,
      dualAxisChartSidebarContent
    );
    console.log('✅ Dual axis chart drag-in setup complete');
  } else {
    console.warn('⚠️ GridStack not available, skipping dual axis chart drag-in setup');
  }
}

// Cleanup old pending initializations (prevent memory leaks)
function cleanupOldPendingDualAxisChartInits() {
  if (window.pendingDualAxisChartInits) {
    const now = Date.now();
    const maxAge = 30000; // 30 seconds

    for (const [chartId, initData] of window.pendingDualAxisChartInits.entries()) {
      if (now - initData.timestamp > maxAge) {
        console.warn('📊 Cleaning up old pending initialization for', chartId);
        window.pendingDualAxisChartInits.delete(chartId);
      }
    }
  }
}

// Run cleanup periodically
setInterval(cleanupOldPendingDualAxisChartInits, 60000); // Every minute

// Auto-setup drag-drop when this script loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', setupDualAxisChartDragDrop);
} else {
  // If DOM is already loaded, setup immediately
  setTimeout(setupDualAxisChartDragDrop, 100);
}

// Debug function to test if the widget is working
function testDualAxisChartWidget() {
  console.log("🧪 Testing Dual Axis Chart Widget...");
  console.log("✅ Function available:", typeof addDualAxisChartWidget);
  console.log("✅ Grid available:", typeof window.grid);
  console.log("✅ amCharts available:", typeof am5);

  if (typeof addDualAxisChartWidget === 'function') {
    console.log("🎯 Attempting to add widget...");
    addDualAxisChartWidget();
  } else {
    console.error("❌ addDualAxisChartWidget function not found!");
  }
}

// Debug function to test chart initialization directly
function testDualAxisChartInit() {
  console.log("🧪 Testing Dual Axis Chart Initialization...");

  // Create a test container
  const testContainer = document.createElement('div');
  testContainer.id = 'test-dual-axis-chart-' + Date.now();
  testContainer.style.width = '600px';
  testContainer.style.height = '400px';
  document.body.appendChild(testContainer);

  console.log("📊 Created test container:", testContainer.id);

  // Try to initialize the chart
  initDualAxisChart(testContainer.id).then(() => {
    console.log("✅ Chart initialization successful!");
  }).catch((error) => {
    console.error("❌ Chart initialization failed:", error);
  });
}

// --- Global Exports ---
window.addDualAxisChartWidget = addDualAxisChartWidget;
window.getDualAxisChartWidgetMarkup = getDualAxisChartWidgetMarkup; // Export markup function
window.setupDualAxisChartDragDrop = setupDualAxisChartDragDrop; // Export drag-drop setup
window.initDualAxisChart = initDualAxisChart; // Export chart initialization
window.initDualAxisChartSettings = initDualAxisChartSettings; // Export settings function
window.testDualAxisChartWidget = testDualAxisChartWidget; // Export test function
window.testDualAxisChartInit = testDualAxisChartInit; // Export direct init test function

// Log that the script has loaded
console.log("📊 Dual Axis Chart Widget script loaded successfully");
console.log("🔧 Available functions:", {
  addDualAxisChartWidget: typeof window.addDualAxisChartWidget,
  getDualAxisChartWidgetMarkup: typeof window.getDualAxisChartWidgetMarkup,
  setupDualAxisChartDragDrop: typeof window.setupDualAxisChartDragDrop,
  initDualAxisChart: typeof window.initDualAxisChart
});
