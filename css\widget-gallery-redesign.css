/**
 * Widget Gallery Redesign
 * A beautiful, professional, and compact UI for the widget gallery
 */

/* Widget Gallery Redesign */
:root {
  /* Core Dimensions */
  --gallery-height: 120px;
  --item-min-width: 76px;
  --item-height: 64px;
  --icon-size: 22px;
  --font-size: 11px;
  --section-total-height: 170px;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;

  /* Colors */
  --primary-color: #00b19c;
  --primary-light: rgba(0, 115, 101, 0.08);
  --primary-lighter: rgba(0, 115, 101, 0.04);
  --primary-darker: #005a4f;
  --border-color: #dee2e6;
  --text-color: #02104f;
  --text-light: #6c757d;
  --icon-color: #231f20;

  /* Effects */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.05);
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --border-radius-sm: 0px;
  --border-radius-md: 0px;
}

/* Widget Categories */
.widget-categories {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: linear-gradient(to bottom, #fff, #f8f9fa);
  border-bottom: 1px solid var(--border-color);
  gap: var(--spacing-xs);
  height: 32px;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: var(--shadow-sm);
  flex-shrink: 0;
}

.widget-categories-left {
  display: flex;
  flex-wrap: nowrap;
  gap: var(--spacing-xs);
  flex: 1;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-bottom: 2px;
}

.widget-categories-left::-webkit-scrollbar {
  display: none;
}

.widget-category {
  padding: calc(var(--spacing-xs) - 1px) var(--spacing-sm);
  font-size: var(--font-size);
  font-weight: 500;
  color: var(--text-color);
  background: transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  border: 1px solid transparent;
  white-space: nowrap;
  border-radius: var(--border-radius-sm);
  position: relative;
  overflow: hidden;
}

.widget-category::before {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--primary-lighter);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.widget-category:hover::before {
  opacity: 1;
}

.widget-category.active {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.widget-category i {
  font-size: calc(var(--font-size) + 2px);
  position: relative;
  z-index: 1;
}

.widget-category span {
  position: relative;
  z-index: 1;
}

/* Widget Gallery */
.widget-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(var(--item-min-width), 1fr));
  gap: var(--spacing-xs);
  padding: var(--spacing-xs);
  background: white;
  height: var(--gallery-height);
  overflow-y: auto;
  scrollbar-width: thin;
  position: relative;
  flex: 1;
  margin: 0;
}

/* Scrollbar styling */
.widget-gallery::-webkit-scrollbar {
  width: 4px;
}

.widget-gallery::-webkit-scrollbar-track {
  background: #f8f9fa;
}

.widget-gallery::-webkit-scrollbar-thumb {
  background: #dee2e6;
  border-radius: var(--border-radius-sm);
}

.widget-gallery::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* Widget Item */
.widget-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  border: 1px solid var(--border-color);
  padding: var(--spacing-xs);
  cursor: pointer;
  transition: all var(--transition-base);
  height: var(--item-height);
  gap: var(--spacing-xs);
  overflow: hidden;
}

.widget-item::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    var(--primary-lighter) 0%,
    transparent 100%
  );
  opacity: 0;
  transition: opacity var(--transition-base);
}

.widget-item:hover::after {
  opacity: 1;
}

/* .widget-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-darker));
  transform: translateY(-100%);
  transition: transform var(--transition-base);
} */

.widget-item:hover::before {
  transform: translateY(0);
}

/* Widget Icon Optimizations */
.widget-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--icon-color);
  transition: all var(--transition-base);
  position: relative;
  z-index: 1;
  width: var(--icon-size);
  height: var(--icon-size);
  margin: 0 auto;
}

.widget-item:hover .widget-icon {
  color: var(--primary-darker);
  transform: scale(1.1);
}

.widget-icon i {
  font-size: var(--icon-size);
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* Ensure consistent icon alignment across different icon fonts */
.widget-icon .las,
.widget-icon .bi,
.widget-icon .fa,
.widget-icon .fas,
.widget-icon .far {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.widget-label {
  font-size: var(--font-size);
  font-weight: 500;
  color: var(--text-color);
  text-align: center;
  transition: color var(--transition-base);
  line-height: 1.2;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
  z-index: 1;
}

.widget-item:hover .widget-label {
  color: var(--primary-darker);
}

/* Widget Section */
.widget-section {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  margin: var(--spacing-xs);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  height: var(--section-total-height);
  display: flex;
  flex-direction: column;
}

.widget-section {
  position: fixed;
  top: -9px;
  width: 100%;
  z-index: 1000;
  height: 90px !important
}

/* Dark Theme Support */
.dark-theme .widget-section {
  background: #1a1a2d;
  border-color: #2d2d3d;
}

.dark-theme .widget-categories {
  background: linear-gradient(to bottom, #1e1e2d, #1a1a2d);
  border-color: #2d2d3d;
}

.dark-theme .widget-category {
  color: #a1a5b7;
}

.dark-theme .widget-category:hover::before {
  background: rgba(77, 182, 172, 0.08);
}

.dark-theme .widget-category.active {
  background: #009e8b;
  color: #1a1a2d;
}

.dark-theme .widget-gallery {
  background: #151521;
}

.dark-theme .widget-gallery::-webkit-scrollbar-track {
  background: #1e1e2d;
}

.dark-theme .widget-gallery::-webkit-scrollbar-thumb {
  background: #2d2d3d;
}

.dark-theme .widget-gallery::-webkit-scrollbar-thumb:hover {
  background: #009e8b;
}

.dark-theme .widget-item {
  background: #1e1e2d;
  border-color: #2d2d3d;
}

.dark-theme .widget-item::after {
  background: linear-gradient(
    135deg,
    rgba(77, 182, 172, 0.08) 0%,
    transparent 100%
  );
}

.dark-theme .widget-item::before {
  background: linear-gradient(to right, #009e8b, #00b3a1);
}

.dark-theme .widget-icon {
  color: #4db6ac;
}

.dark-theme .widget-item:hover .widget-icon {
  color: #00b3a1;
}

.dark-theme .widget-label {
  color: #a1a5b7;
}

.dark-theme .widget-item:hover .widget-label {
  color: #00b3a1;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  :root {
    --gallery-height: 110px;
    --section-total-height: 148px;
    --item-min-width: 68px;
    --item-height: 58px;
    --icon-size: 20px;
    --font-size: 10px;
  }

  .widget-categories {
    height: 30px;
  }

  .widget-category {
    padding: calc(var(--spacing-xs) - 2px) var(--spacing-sm);
  }
}
