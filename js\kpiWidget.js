/**
 * KPI Widget Implementation
 * Displays key performance indicators with values, trends, and interactive features
 */

// Global KPI widget counter
let kpiWidgetCounter = 0;

/**
 * Add a KPI Widget to the grid
 */
function addKpiWidget() {
  const uniqueId = `kpi-widget-${++kpiWidgetCounter}`;

  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 3,
    h: 2,
    content: createKpiWidgetHtml(uniqueId),
  });

  initializeKpiWidget(uniqueId);
  return widget;
}

/**
 * Create KPI widget HTML structure
 */
function createKpiWidgetHtml(uniqueId) {
  return `
    <div class="widget-wrapper kpi-widget-wrapper" id="${uniqueId}">
      <div class="widget-header">
        <div class="widget-title">
          <i class="las la-chart-line"></i>
          <span class="widget-title-text">KPI Widget</span>
        </div>
        <div class="widget-actions">
          <button class="widget-action-btn" onclick="toggleKpiSettings('${uniqueId}')" title="Settings">
            <i class="las la-cog"></i>
          </button>
          <button class="widget-action-btn" onclick="refreshKpiWidget('${uniqueId}')" title="Refresh">
            <i class="las la-sync-alt"></i>
          </button>
          <button class="widget-action-btn" onclick="removeWidget(this)" title="Remove">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      
      <div class="widget-content kpi-content">
        <div class="kpi-main">
          <div class="kpi-icon">
            <i class="las la-dollar-sign"></i>
          </div>
          <div class="kpi-info">
            <div class="kpi-value">$124,856</div>
            <div class="kpi-label">Total Revenue</div>
          </div>
          <div class="kpi-trend">
            <span class="trend-indicator positive">
              <i class="las la-arrow-up"></i>
              <span class="trend-value">+12.5%</span>
            </span>
          </div>
        </div>
        
        <div class="kpi-details">
          <div class="kpi-subtitle">vs last month</div>
          <div class="kpi-progress">
            <div class="progress-bar">
              <div class="progress-fill" style="width: 75%"></div>
            </div>
            <div class="progress-text">75% of target</div>
          </div>
        </div>
      </div>
      
      <div class="kpi-settings" id="${uniqueId}-settings" style="display: none;">
        <div class="settings-section">
          <label>KPI Title:</label>
          <input type="text" id="${uniqueId}-title" value="Total Revenue" class="form-control">
        </div>
        
        <div class="settings-section">
          <label>Value:</label>
          <input type="text" id="${uniqueId}-value" value="$124,856" class="form-control">
        </div>
        
        <div class="settings-section">
          <label>Trend:</label>
          <select id="${uniqueId}-trend" class="form-control">
            <option value="positive">Positive (+)</option>
            <option value="negative">Negative (-)</option>
            <option value="neutral">Neutral (=)</option>
          </select>
        </div>
        
        <div class="settings-section">
          <label>Trend Value:</label>
          <input type="text" id="${uniqueId}-trend-value" value="12.5%" class="form-control">
        </div>
        
        <div class="settings-section">
          <label>Icon:</label>
          <select id="${uniqueId}-icon" class="form-control">
            <option value="la-dollar-sign">Dollar</option>
            <option value="la-users">Users</option>
            <option value="la-chart-line">Chart</option>
            <option value="la-shopping-cart">Sales</option>
            <option value="la-eye">Views</option>
            <option value="la-clock">Time</option>
            <option value="la-star">Rating</option>
            <option value="la-percent">Percentage</option>
          </select>
        </div>
        
        <div class="settings-section">
          <label>Color Theme:</label>
          <select id="${uniqueId}-theme" class="form-control">
            <option value="blue">Blue</option>
            <option value="green">Green</option>
            <option value="orange">Orange</option>
            <option value="red">Red</option>
            <option value="purple">Purple</option>
            <option value="teal">Teal</option>
          </select>
        </div>
        
        <div class="settings-actions">
          <button class="btn btn-primary" onclick="applyKpiSettings('${uniqueId}')">Apply</button>
          <button class="btn btn-secondary" onclick="toggleKpiSettings('${uniqueId}')">Cancel</button>
        </div>
      </div>
    </div>
  `;
}

/**
 * Initialize KPI widget functionality
 */
function initializeKpiWidget(widgetId) {
  // Add any initialization logic here
  console.log(`KPI Widget ${widgetId} initialized`);

  // Apply default theme
  applyKpiTheme(widgetId, "blue");
}

/**
 * Toggle KPI settings panel
 */
function toggleKpiSettings(widgetId) {
  const settingsPanel = document.getElementById(`${widgetId}-settings`);
  const widgetContent = document.querySelector(`#${widgetId} .kpi-content`);

  if (settingsPanel.style.display === "none") {
    settingsPanel.style.display = "block";
    widgetContent.style.display = "none";
  } else {
    settingsPanel.style.display = "none";
    widgetContent.style.display = "block";
  }
}

/**
 * Apply KPI settings
 */
function applyKpiSettings(widgetId) {
  const title = document.getElementById(`${widgetId}-title`).value;
  const value = document.getElementById(`${widgetId}-value`).value;
  const trend = document.getElementById(`${widgetId}-trend`).value;
  const trendValue = document.getElementById(`${widgetId}-trend-value`).value;
  const icon = document.getElementById(`${widgetId}-icon`).value;
  const theme = document.getElementById(`${widgetId}-theme`).value;

  // Update widget content
  const widget = document.getElementById(widgetId);
  widget.querySelector(".kpi-label").textContent = title;
  widget.querySelector(".kpi-value").textContent = value;
  widget.querySelector(".trend-value").textContent = trendValue;

  // Update icon
  widget.querySelector(".kpi-icon i").className = `las ${icon}`;

  // Update trend indicator
  const trendIndicator = widget.querySelector(".trend-indicator");
  trendIndicator.className = `trend-indicator ${trend}`;

  const trendIcon = trendIndicator.querySelector("i");
  if (trend === "positive") {
    trendIcon.className = "las la-arrow-up";
  } else if (trend === "negative") {
    trendIcon.className = "las la-arrow-down";
  } else {
    trendIcon.className = "las la-minus";
  }

  // Apply theme
  applyKpiTheme(widgetId, theme);

  // Hide settings panel
  toggleKpiSettings(widgetId);
}

/**
 * Apply color theme to KPI widget
 */
function applyKpiTheme(widgetId, theme) {
  const widget = document.getElementById(widgetId);

  // Remove existing theme classes
  widget.classList.remove(
    "kpi-blue",
    "kpi-green",
    "kpi-orange",
    "kpi-red",
    "kpi-purple",
    "kpi-teal"
  );

  // Add new theme class
  widget.classList.add(`kpi-${theme}`);
}

/**
 * Refresh KPI widget data
 */
function refreshKpiWidget(widgetId) {
  const widget = document.getElementById(widgetId);
  const refreshBtn = widget.querySelector('[title="Refresh"] i');

  // Add spinning animation
  refreshBtn.classList.add("fa-spin");

  // Simulate data refresh
  setTimeout(() => {
    // Generate random data for demo
    const randomValue = Math.floor(Math.random() * 200000) + 50000;
    const randomTrend = (Math.random() * 20 - 10).toFixed(1);

    widget.querySelector(
      ".kpi-value"
    ).textContent = `$${randomValue.toLocaleString()}`;
    widget.querySelector(".trend-value").textContent = `${
      randomTrend > 0 ? "+" : ""
    }${randomTrend}%`;

    // Update trend indicator
    const trendIndicator = widget.querySelector(".trend-indicator");
    if (randomTrend > 0) {
      trendIndicator.className = "trend-indicator positive";
      trendIndicator.querySelector("i").className = "las la-arrow-up";
    } else if (randomTrend < 0) {
      trendIndicator.className = "trend-indicator negative";
      trendIndicator.querySelector("i").className = "las la-arrow-down";
    } else {
      trendIndicator.className = "trend-indicator neutral";
      trendIndicator.querySelector("i").className = "las la-minus";
    }

    // Remove spinning animation
    refreshBtn.classList.remove("fa-spin");

    console.log(`KPI Widget ${widgetId} refreshed`);
  }, 1000);
}

/**
 * Create different types of KPI widgets
 */
function addRevenueKpiWidget() {
  addKpiWidget();
}

// Add Users KPI Widget
function addUsersKpiWidget() {
  const widgetId = "kpi-users-" + kpiWidgetCounter++;
  const widgetHtml = createKPIWidgetHTML(
    widgetId,
    "Users",
    "2,847",
    "+12.5%",
    "positive",
    "las la-users",
    "#8b5cf6"
  );

  grid.addWidget(widgetHtml, {
    w: 2,
    h: 1,
    id: widgetId,
  });

  // Initialize the widget after adding to DOM
  setTimeout(() => initializeKPIWidget(widgetId), 100);
}

// Add Sales KPI Widget
function addSalesKpiWidget() {
  const widgetId = "kpi-sales-" + kpiWidgetCounter++;
  const widgetHtml = createKPIWidgetHTML(
    widgetId,
    "Sales",
    "$47,892",
    "+8.3%",
    "positive",
    "las la-dollar-sign",
    "#06d6a0"
  );

  grid.addWidget(widgetHtml, {
    w: 2,
    h: 1,
    id: widgetId,
  });

  // Initialize the widget after adding to DOM
  setTimeout(() => initializeKPIWidget(widgetId), 100);
}
