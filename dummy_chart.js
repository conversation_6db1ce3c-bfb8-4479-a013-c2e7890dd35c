// dummy_chart.js - Chart functions extracted from index2.html
// This file contains all chart initialization functions for the dashboard

function chart1() {
  am5.ready(function () {
    // Create root element
    const root = am5.Root.new("chartdiv1");

    // Set themes
    root.setThemes([am5themes_Animated.new(root)]);

    // Create chart
    const chart = root.container.children.push(
      am5xy.XYChart.new(root, {
        panX: true,
        panY: true,
        wheelX: "panX",
        wheelY: "zoomX",
        pinchZoomX: true,
        paddingLeft: 0,
        paddingRight: 0,
        paddingTop: 50,
        paddingBottom: 60,
      })
    );

    // Add chart title
    const title = chart.children.unshift(
      am5.Label.new(root, {
        text: "Aluminium cash-settlement (LME)",
        fontSize: 12,
        fontWeight: "600",
        fontFamily: "Montserrat",
        x: am5.p50,
        centerX: am5.p50,
        y: 0,
        centerY: am5.p0,
        marginTop: 10,
        marginBottom: 10,
      })
    );

    // Create axes
    const xAxis = chart.xAxes.push(
      am5xy.DateAxis.new(root, {
        maxZoomCount: 50,
        baseInterval: {
          timeUnit: "month",
          count: 1,
        },
        renderer: am5xy.AxisRendererX.new(root, {
          minGridDistance: 80,
          cellStartLocation: 0.1,
          cellEndLocation: 0.9,
        }),
        tooltip: am5.Tooltip.new(root, {}),
      })
    );

    // Prepare data for actuals (left axis - USD per ton)
    const actualsData = [
      { date: new Date("2023-04-01").getTime(), value: 2350, volume: 1200 },
      { date: new Date("2023-05-01").getTime(), value: 2270, volume: 1350 },
      { date: new Date("2023-06-01").getTime(), value: 2190, volume: 1100 },
      { date: new Date("2023-07-01").getTime(), value: 2160, volume: 1450 },
      { date: new Date("2023-08-01").getTime(), value: 2150, volume: 1300 },
      { date: new Date("2023-09-01").getTime(), value: 2180, volume: 1250 },
      {
        date: new Date("2023-10-01").getTime(),
        value: 2192.45,
        volume: 1400,
      },
      { date: new Date("2023-11-01").getTime(), value: 2210, volume: 1550 },
      { date: new Date("2023-12-01").getTime(), value: 2160, volume: 1320 },
      { date: new Date("2024-01-01").getTime(), value: 2200, volume: 1480 },
      { date: new Date("2024-02-01").getTime(), value: 2190, volume: 1380 },
      { date: new Date("2024-03-01").getTime(), value: 2220, volume: 1420 },
      { date: new Date("2024-04-01").getTime(), value: 2500, volume: 1600 },
      { date: new Date("2024-05-01").getTime(), value: 2570, volume: 1750 },
      { date: new Date("2024-06-01").getTime(), value: 2500, volume: 1650 },
      { date: new Date("2024-07-01").getTime(), value: 2370, volume: 1520 },
      { date: new Date("2024-08-01").getTime(), value: 2340, volume: 1480 },
      { date: new Date("2024-09-01").getTime(), value: 2460, volume: 1580 },
      { date: new Date("2024-10-01").getTime(), value: 2600, volume: 1720 },
      { date: new Date("2024-11-01").getTime(), value: 2580, volume: 1680 },
      { date: new Date("2024-12-01").getTime(), value: 2540, volume: 1620 },
      { date: new Date("2025-01-01").getTime(), value: 2580, volume: 1700 },
      { date: new Date("2025-02-01").getTime(), value: 2670, volume: 1800 },
      { date: new Date("2025-03-01").getTime(), value: 2690, volume: 1850 },
    ];

    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 2100,
        max: 2700,
        renderer: am5xy.AxisRendererY.new(root, {
          strokeOpacity: 1,
          stroke: am5.color("#000000"),
        }),
      })
    );

    // Create second Y-axis (right side) for volume data
    const yAxisRight = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 1000,
        max: 2000,
        renderer: am5xy.AxisRendererY.new(root, {
          opposite: true,
          strokeOpacity: 1,
          stroke: am5.color("#000000"),
        }),
      })
    );

    // Add left axis title as separate element
    const leftAxisTitle = chart.plotContainer.children.push(
      am5.Label.new(root, {
        text: "Price (USD/ton)",
        fontFamily: "Montserrat",
        fontSize: 12,
        fontWeight: "400",
        rotation: -90,
        x: am5.p0,
        y: am5.p50,
        centerX: am5.p50,
        centerY: am5.p50,
        dx: -60,
      })
    );

    // Add right axis title as separate element
    const rightAxisTitle = chart.plotContainer.children.push(
      am5.Label.new(root, {
        text: "Volume (000 tons)",
        fontFamily: "Montserrat",
        fontSize: 12,
        fontWeight: "400",
        rotation: 90,
        x: am5.p100,
        y: am5.p50,
        centerX: am5.p50,
        centerY: am5.p50,
        dx: 60,
      })
    );

    // Set font styles
    const fontSettings = {
      fontFamily: "Montserrat",
      fontSize: 12,
      fontWeight: "400",
    };

    xAxis.get("renderer").labels.template.setAll(fontSettings);
    yAxis.get("renderer").labels.template.setAll(fontSettings);
    yAxisRight.get("renderer").labels.template.setAll(fontSettings);

    // Configure X-axis date format (MMM-YYYY like Jul-2024)
    xAxis.get("renderer").labels.template.setAll({
      ...fontSettings,
      rotation: -45,
      centerY: am5.p50,
      centerX: am5.p100,
    });

    // Set date format for X-axis labels
    xAxis.set("dateFormats", {
      month: "MMM-yyyy",
    });

    xAxis.set("periodChangeDateFormats", {
      month: "MMM-yyyy",
    });

    // Configure axis lines (correct approach for AmCharts v5)
    xAxis.get("renderer").set("strokeOpacity", 1);
    xAxis.get("renderer").set("stroke", am5.color("#000000"));

    // Create series for price (left axis - solid line)
    const priceSeries = chart.series.push(
      am5xy.LineSeries.new(root, {
        name: "Price (USD/ton)",
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "value",
        valueXField: "date",
        tooltip: am5.Tooltip.new(root, {
          labelText: "{valueY}",
        }),
        stroke: am5.color("#00b19c"),
        fill: am5.color("#00b19c"),
      })
    );

    // Create series for volume (right axis - dashed line)
    const volumeSeries = chart.series.push(
      am5xy.LineSeries.new(root, {
        name: "Volume (000 tons)",
        xAxis: xAxis,
        yAxis: yAxisRight,
        valueYField: "volume",
        valueXField: "date",
        tooltip: am5.Tooltip.new(root, {
          labelText: "{valueY}",
        }),
        stroke: am5.color("#3bcd3f"),
        fill: am5.color("#3bcd3f"),
      })
    );

    // Set line styles
    priceSeries.strokes.template.setAll({
      strokeWidth: 2,
      strokeDasharray: [],
    });

    volumeSeries.strokes.template.setAll({
      strokeWidth: 2,
      strokeDasharray: [5, 5],
    });

    // Add bullets (markers) for price series
    priceSeries.bullets.push(function () {
      const bulletCircle = am5.Circle.new(root, {
        radius: 4,
        fill: am5.color("#00b19c"),
        stroke: am5.color("#ffffff"),
        strokeWidth: 2,
      });
      return am5.Bullet.new(root, {
        sprite: bulletCircle,
      });
    });

    // Add bullets (markers) for volume series
    volumeSeries.bullets.push(function () {
      const bulletCircle = am5.Circle.new(root, {
        radius: 4,
        fill: am5.color("#3bcd3f"),
        stroke: am5.color("#ffffff"),
        strokeWidth: 2,
      });
      return am5.Bullet.new(root, {
        sprite: bulletCircle,
      });
    });

    // Add legend
    const legend = chart.children.push(
      am5.Legend.new(root, {
        centerX: am5.p50,
        x: am5.p50,
        centerY: am5.p0,
        y: am5.p100,
        marginTop: 15,
        marginBottom: 5,
      })
    );

    legend.labels.template.setAll(fontSettings);
    legend.data.setAll(chart.series.values);

    // Set data
    priceSeries.data.setAll(actualsData);
    volumeSeries.data.setAll(actualsData);

    // Add cursor
    chart.set(
      "cursor",
      am5xy.XYCursor.new(root, {
        behavior: "zoomX",
      })
    );

    // Configure grid
    xAxis.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    yAxis.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    yAxisRight.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    // Make stuff animate on load
    priceSeries.appear(1000);
    volumeSeries.appear(1000);
    chart.appear(1000, 100);
  });
}

function chart1a() {
  am5.ready(function () {
    // Create root element
    const root = am5.Root.new("chartDiv1a");

    // Set themes
    root.setThemes([am5themes_Animated.new(root)]);

    // Create chart
    const chart = root.container.children.push(
      am5xy.XYChart.new(root, {
        panX: true,
        panY: true,
        wheelX: "panX",
        wheelY: "zoomX",
        pinchZoomX: true,
        paddingLeft: 0,
        paddingRight: 0,
        paddingTop: 50,
        paddingBottom: 60,
      })
    );

    // Add chart title
    const title = chart.children.unshift(
      am5.Label.new(root, {
        text: "Aluminium cash-settlement (LME)",
        fontSize: 12,
        fontWeight: "600",
        fontFamily: "Montserrat",
        x: am5.p50,
        centerX: am5.p50,
        y: 0,
        centerY: am5.p0,
        marginTop: 10,
        marginBottom: 10,
      })
    );

    // Create axes
    const xAxis = chart.xAxes.push(
      am5xy.DateAxis.new(root, {
        maxZoomCount: 50,
        baseInterval: {
          timeUnit: "month",
          count: 1,
        },
        renderer: am5xy.AxisRendererX.new(root, {
          minGridDistance: 80,
          cellStartLocation: 0.1,
          cellEndLocation: 0.9,
        }),
        tooltip: am5.Tooltip.new(root, {}),
      })
    );

    // Prepare data for actuals (left axis - USD per ton)
    const actualsData = [
      { date: new Date("2023-04-01").getTime(), value: 2350, volume: 1200 },
      { date: new Date("2023-05-01").getTime(), value: 2270, volume: 1350 },
      { date: new Date("2023-06-01").getTime(), value: 2190, volume: 1100 },
      { date: new Date("2023-07-01").getTime(), value: 2160, volume: 1450 },
      { date: new Date("2023-08-01").getTime(), value: 2150, volume: 1300 },
      { date: new Date("2023-09-01").getTime(), value: 2180, volume: 1250 },
      {
        date: new Date("2023-10-01").getTime(),
        value: 2192.45,
        volume: 1400,
      },
      { date: new Date("2023-11-01").getTime(), value: 2210, volume: 1550 },
      { date: new Date("2023-12-01").getTime(), value: 2160, volume: 1320 },
      { date: new Date("2024-01-01").getTime(), value: 2200, volume: 1480 },
      { date: new Date("2024-02-01").getTime(), value: 2190, volume: 1380 },
      { date: new Date("2024-03-01").getTime(), value: 2220, volume: 1420 },
      { date: new Date("2024-04-01").getTime(), value: 2500, volume: 1600 },
      { date: new Date("2024-05-01").getTime(), value: 2570, volume: 1750 },
      { date: new Date("2024-06-01").getTime(), value: 2500, volume: 1650 },
      { date: new Date("2024-07-01").getTime(), value: 2370, volume: 1520 },
      { date: new Date("2024-08-01").getTime(), value: 2340, volume: 1480 },
      { date: new Date("2024-09-01").getTime(), value: 2460, volume: 1580 },
      { date: new Date("2024-10-01").getTime(), value: 2600, volume: 1720 },
      { date: new Date("2024-11-01").getTime(), value: 2580, volume: 1680 },
      { date: new Date("2024-12-01").getTime(), value: 2540, volume: 1620 },
      { date: new Date("2025-01-01").getTime(), value: 2580, volume: 1700 },
      { date: new Date("2025-02-01").getTime(), value: 2670, volume: 1800 },
      { date: new Date("2025-03-01").getTime(), value: 2690, volume: 1850 },
    ];

    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 2100,
        max: 2700,
        renderer: am5xy.AxisRendererY.new(root, {
          strokeOpacity: 1,
          stroke: am5.color("#000000"),
        }),
      })
    );

    // Create second Y-axis (right side) for volume data
    const yAxisRight = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 1000,
        max: 2000,
        renderer: am5xy.AxisRendererY.new(root, {
          opposite: true,
          strokeOpacity: 1,
          stroke: am5.color("#000000"),
        }),
      })
    );

    // Set font styles
    const fontSettings = {
      fontFamily: "Montserrat",
      fontSize: 12,
      fontWeight: "400",
    };

    xAxis.get("renderer").labels.template.setAll(fontSettings);
    yAxis.get("renderer").labels.template.setAll(fontSettings);
    yAxisRight.get("renderer").labels.template.setAll(fontSettings);

    // Configure X-axis date format (MMM-YYYY like Jul-2024)
    xAxis.get("renderer").labels.template.setAll({
      ...fontSettings,
      rotation: -45,
      centerY: am5.p50,
      centerX: am5.p100,
    });

    // Set date format for X-axis labels
    xAxis.set("dateFormats", {
      month: "MMM-yyyy",
    });

    xAxis.set("periodChangeDateFormats", {
      month: "MMM-yyyy",
    });

    // Configure axis lines
    xAxis.get("renderer").set("strokeOpacity", 1);
    xAxis.get("renderer").set("stroke", am5.color("#000000"));

    // Create series for price (left axis - solid line)
    const priceSeries = chart.series.push(
      am5xy.LineSeries.new(root, {
        name: "Price (USD/ton)",
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "value",
        valueXField: "date",
        tooltip: am5.Tooltip.new(root, {
          labelText: "{valueY}",
        }),
        stroke: am5.color("#00b19c"),
        fill: am5.color("#00b19c"),
      })
    );

    // Create series for volume (right axis - dashed line)
    const volumeSeries = chart.series.push(
      am5xy.LineSeries.new(root, {
        name: "Volume (000 tons)",
        xAxis: xAxis,
        yAxis: yAxisRight,
        valueYField: "volume",
        valueXField: "date",
        tooltip: am5.Tooltip.new(root, {
          labelText: "{valueY}",
        }),
        stroke: am5.color("#3bcd3f"),
        fill: am5.color("#3bcd3f"),
      })
    );

    // Set line styles
    priceSeries.strokes.template.setAll({
      strokeWidth: 2,
      strokeDasharray: [],
    });

    volumeSeries.strokes.template.setAll({
      strokeWidth: 2,
      strokeDasharray: [5, 5],
    });

    // Add bullets (markers) for price series
    priceSeries.bullets.push(function () {
      const bulletCircle = am5.Circle.new(root, {
        radius: 4,
        fill: am5.color("#00b19c"),
        stroke: am5.color("#ffffff"),
        strokeWidth: 2,
      });
      return am5.Bullet.new(root, {
        sprite: bulletCircle,
      });
    });

    // Add bullets (markers) for volume series
    volumeSeries.bullets.push(function () {
      const bulletCircle = am5.Circle.new(root, {
        radius: 4,
        fill: am5.color("#3bcd3f"),
        stroke: am5.color("#ffffff"),
        strokeWidth: 2,
      });
      return am5.Bullet.new(root, {
        sprite: bulletCircle,
      });
    });

    // Add legend
    const legend = chart.children.push(
      am5.Legend.new(root, {
        centerX: am5.p50,
        x: am5.p50,
        centerY: am5.p0,
        y: am5.p100,
        marginTop: 15,
        marginBottom: 5,
      })
    );

    legend.labels.template.setAll(fontSettings);
    legend.data.setAll(chart.series.values);

    // Set data
    priceSeries.data.setAll(actualsData);
    volumeSeries.data.setAll(actualsData);

    // Add cursor
    chart.set(
      "cursor",
      am5xy.XYCursor.new(root, {
        behavior: "zoomX",
      })
    );

    // Configure grid
    xAxis.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    yAxis.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    yAxisRight.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    // Make stuff animate on load
    priceSeries.appear(1000);
    volumeSeries.appear(1000);
    chart.appear(1000, 100);
  });
}

function chart2() {
  am5.ready(function () {
    // Create root element
    const root = am5.Root.new("chartDiv2");

    // Set themes
    root.setThemes([am5themes_Animated.new(root)]);

    // Create chart
    const chart = root.container.children.push(
      am5xy.XYChart.new(root, {
        panX: true,
        panY: true,
        wheelX: "panX",
        wheelY: "zoomX",
        pinchZoomX: true,
        paddingLeft: 0,
        paddingRight: 0,
        paddingTop: 50,
        paddingBottom: 60,
      })
    );

    // Add chart title
    const title = chart.children.unshift(
      am5.Label.new(root, {
        text: "Aluminium - supply and demand - Global",
        fontSize: 12,
        fontWeight: "600",
        fontFamily: "Montserrat",
        x: am5.p50,
        centerX: am5.p50,
        y: 0,
        centerY: am5.p0,
        marginTop: 10,
        marginBottom: 10,
      })
    );

    // Create axes - using DateAxis like in dual-axis-chart
    const xAxis = chart.xAxes.push(
      am5xy.DateAxis.new(root, {
        maxZoomCount: 50,
        baseInterval: {
          timeUnit: "year",
          count: 1,
        },
        renderer: am5xy.AxisRendererX.new(root, {
          minGridDistance: 80,
          cellStartLocation: 0.1,
          cellEndLocation: 0.9,
        }),
        tooltip: am5.Tooltip.new(root, {}),
      })
    );

    // Set font styles
    const fontSettings = {
      fontFamily: "Montserrat",
      fontSize: 12,
      fontWeight: "400",
    };

    // Configure X-axis date format and styling
    xAxis.get("renderer").labels.template.setAll({
      ...fontSettings,
      rotation: -45,
      centerY: am5.p50,
      centerX: am5.p100,
    });

    // Set date format for X-axis labels
    xAxis.set("dateFormats", {
      year: "yyyy",
    });

    xAxis.set("periodChangeDateFormats", {
      year: "yyyy",
    });

    // Configure axis lines
    xAxis.get("renderer").set("strokeOpacity", 1);
    xAxis.get("renderer").set("stroke", am5.color("#000000"));

    // Left Y-axis for Demand
    const yAxisLeft = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 0,
        max: 80000,
        renderer: am5xy.AxisRendererY.new(root, {
          opposite: false,
          strokeOpacity: 1,
          stroke: am5.color("#000000"),
        }),
      })
    );

    yAxisLeft.get("renderer").labels.template.setAll(fontSettings);

    // Right Y-axis for Supply
    const yAxisRight = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 0,
        max: 80000,
        renderer: am5xy.AxisRendererY.new(root, {
          opposite: true,
          strokeOpacity: 1,
          stroke: am5.color("#000000"),
        }),
      })
    );

    yAxisRight.get("renderer").labels.template.setAll(fontSettings);

    // Add Y-axis titles
    const leftTitle = chart.plotContainer.children.push(
      am5.Label.new(root, {
        text: "Thousand tonne",
        fontSize: 12,
        fontWeight: "400",
        fontFamily: "Montserrat",
        rotation: -90,
        x: am5.p0,
        centerX: am5.p50,
        y: am5.p50,
        centerY: am5.p50,
        dx: -60,
      })
    );

    const rightTitle = chart.plotContainer.children.push(
      am5.Label.new(root, {
        text: "Thousand tonne",
        fontSize: 12,
        fontWeight: "400",
        fontFamily: "Montserrat",
        rotation: 90,
        x: am5.p100,
        centerX: am5.p50,
        y: am5.p50,
        centerY: am5.p50,
        dx: 60,
      })
    );

    // Configure grid
    xAxis.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    yAxisLeft.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    yAxisRight.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    // Create Demand series (teal color, solid line)
    const demandSeries = chart.series.push(
      am5xy.LineSeries.new(root, {
        name: "Demand",
        xAxis: xAxis,
        yAxis: yAxisLeft,
        valueYField: "demand",
        valueXField: "date",
        tooltip: am5.Tooltip.new(root, {
          labelText: "{name}: {valueY}",
        }),
        stroke: am5.color("#5fb3a1"),
        fill: am5.color("#5fb3a1"),
      })
    );

    // Set line styles - solid line for demand
    demandSeries.strokes.template.setAll({
      strokeWidth: 2,
      strokeDasharray: [],
    });

    // Add bullets for demand
    demandSeries.bullets.push(function () {
      return am5.Bullet.new(root, {
        sprite: am5.Circle.new(root, {
          radius: 4,
          fill: am5.color("#5fb3a1"),
          stroke: am5.color("#ffffff"),
          strokeWidth: 2,
        }),
      });
    });

    // Create Supply series (green color, dashed line)
    const supplySeries = chart.series.push(
      am5xy.LineSeries.new(root, {
        name: "Supply",
        xAxis: xAxis,
        yAxis: yAxisRight,
        valueYField: "supply",
        valueXField: "date",
        tooltip: am5.Tooltip.new(root, {
          labelText: "{name}: {valueY}",
        }),
        stroke: am5.color("#7bc142"),
        fill: am5.color("#7bc142"),
      })
    );

    // Set line styles - dashed line for supply
    supplySeries.strokes.template.setAll({
      strokeWidth: 2,
      strokeDasharray: [5, 5],
    });

    // Add bullets for supply
    supplySeries.bullets.push(function () {
      return am5.Bullet.new(root, {
        sprite: am5.Circle.new(root, {
          radius: 4,
          fill: am5.color("#7bc142"),
          stroke: am5.color("#ffffff"),
          strokeWidth: 2,
        }),
      });
    });

    // Data based on the image - converted to dates
    const data = [
      {
        date: new Date("2017-01-01").getTime(),
        demand: 59000,
        supply: 63000,
      },
      {
        date: new Date("2018-01-01").getTime(),
        demand: 66000,
        supply: 64000,
      },
      {
        date: new Date("2019-01-01").getTime(),
        demand: 63000,
        supply: 64000,
      },
      {
        date: new Date("2020-01-01").getTime(),
        demand: 65000,
        supply: 66000,
      },
      {
        date: new Date("2021-01-01").getTime(),
        demand: 68050,
        supply: 69000,
      },
      {
        date: new Date("2022-01-01").getTime(),
        demand: 68000,
        supply: 70000,
      },
      {
        date: new Date("2023-01-01").getTime(),
        demand: 70000,
        supply: 72000,
      },
      {
        date: new Date("2024-01-01").getTime(),
        demand: 73000,
        supply: 74000,
      },
      {
        date: new Date("2025-01-01").getTime(),
        demand: 75000,
        supply: 76000,
      },
    ];

    // Set data
    demandSeries.data.setAll(data);
    supplySeries.data.setAll(data);

    // Add legend
    const legend = chart.children.push(
      am5.Legend.new(root, {
        centerX: am5.p50,
        x: am5.p50,
        centerY: am5.p0,
        y: am5.p100,
        marginTop: 15,
        marginBottom: 5,
      })
    );

    legend.labels.template.setAll(fontSettings);
    legend.data.setAll(chart.series.values);

    // Add cursor with zoomX behavior
    chart.set(
      "cursor",
      am5xy.XYCursor.new(root, {
        behavior: "zoomX",
      })
    );

    // Make stuff animate on load
    demandSeries.appear(1000);
    supplySeries.appear(1000);
    chart.appear(1000, 100);
  });
}

function chart2a() {
  am5.ready(function () {
    // Create root element
    const root = am5.Root.new("chartDiv1b");

    // Set themes
    root.setThemes([am5themes_Animated.new(root)]);

    // Create chart
    const chart = root.container.children.push(
      am5xy.XYChart.new(root, {
        panX: true,
        panY: true,
        wheelX: "panX",
        wheelY: "zoomX",
        pinchZoomX: true,
        paddingLeft: 0,
        paddingRight: 0,
        paddingTop: 50,
        paddingBottom: 60,
      })
    );

    // Add chart title
    const title = chart.children.unshift(
      am5.Label.new(root, {
        text: "Aluminium - supply and demand - Global",
        fontSize: 12,
        fontWeight: "600",
        fontFamily: "Montserrat",
        x: am5.p50,
        centerX: am5.p50,
        y: 0,
        centerY: am5.p0,
        marginTop: 10,
        marginBottom: 10,
      })
    );

    // Create axes - using DateAxis like in dual-axis-chart
    const xAxis = chart.xAxes.push(
      am5xy.DateAxis.new(root, {
        maxZoomCount: 50,
        baseInterval: {
          timeUnit: "year",
          count: 1,
        },
        renderer: am5xy.AxisRendererX.new(root, {
          minGridDistance: 80,
          cellStartLocation: 0.1,
          cellEndLocation: 0.9,
        }),
        tooltip: am5.Tooltip.new(root, {}),
      })
    );

    // Set font styles
    const fontSettings = {
      fontFamily: "Montserrat",
      fontSize: 12,
      fontWeight: "400",
    };

    // Configure X-axis date format and styling
    xAxis.get("renderer").labels.template.setAll({
      ...fontSettings,
      rotation: -45,
      centerY: am5.p50,
      centerX: am5.p100,
    });

    // Set date format for X-axis labels
    xAxis.set("dateFormats", {
      year: "yyyy",
    });

    xAxis.set("periodChangeDateFormats", {
      year: "yyyy",
    });

    // Configure axis lines
    xAxis.get("renderer").set("strokeOpacity", 1);
    xAxis.get("renderer").set("stroke", am5.color("#000000"));

    // Left Y-axis for Demand
    const yAxisLeft = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 0,
        max: 80000,
        renderer: am5xy.AxisRendererY.new(root, {
          opposite: false,
          strokeOpacity: 1,
          stroke: am5.color("#000000"),
        }),
      })
    );

    yAxisLeft.get("renderer").labels.template.setAll(fontSettings);

    // Right Y-axis for Supply
    const yAxisRight = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 0,
        max: 80000,
        renderer: am5xy.AxisRendererY.new(root, {
          opposite: true,
          strokeOpacity: 1,
          stroke: am5.color("#000000"),
        }),
      })
    );

    yAxisRight.get("renderer").labels.template.setAll(fontSettings);

    // Add Y-axis titles
    const leftTitle = chart.plotContainer.children.push(
      am5.Label.new(root, {
        text: "Thousand tonne",
        fontSize: 12,
        fontWeight: "400",
        fontFamily: "Montserrat",
        rotation: -90,
        x: am5.p0,
        centerX: am5.p50,
        y: am5.p50,
        centerY: am5.p50,
        dx: -60,
      })
    );

    const rightTitle = chart.plotContainer.children.push(
      am5.Label.new(root, {
        text: "Thousand tonne",
        fontSize: 12,
        fontWeight: "400",
        fontFamily: "Montserrat",
        rotation: 90,
        x: am5.p100,
        centerX: am5.p50,
        y: am5.p50,
        centerY: am5.p50,
        dx: 60,
      })
    );

    // Configure grid
    xAxis.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    yAxisLeft.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    yAxisRight.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    // Create Demand series (teal color, solid line)
    const demandSeries = chart.series.push(
      am5xy.LineSeries.new(root, {
        name: "Demand",
        xAxis: xAxis,
        yAxis: yAxisLeft,
        valueYField: "demand",
        valueXField: "date",
        tooltip: am5.Tooltip.new(root, {
          labelText: "{name}: {valueY}",
        }),
        stroke: am5.color("#5fb3a1"),
        fill: am5.color("#5fb3a1"),
      })
    );

    // Set line styles - solid line for demand
    demandSeries.strokes.template.setAll({
      strokeWidth: 2,
      strokeDasharray: [],
    });

    // Add bullets for demand
    demandSeries.bullets.push(function () {
      return am5.Bullet.new(root, {
        sprite: am5.Circle.new(root, {
          radius: 4,
          fill: am5.color("#5fb3a1"),
          stroke: am5.color("#ffffff"),
          strokeWidth: 2,
        }),
      });
    });

    // Create Supply series (green color, dashed line)
    const supplySeries = chart.series.push(
      am5xy.LineSeries.new(root, {
        name: "Supply",
        xAxis: xAxis,
        yAxis: yAxisRight,
        valueYField: "supply",
        valueXField: "date",
        tooltip: am5.Tooltip.new(root, {
          labelText: "{name}: {valueY}",
        }),
        stroke: am5.color("#7bc142"),
        fill: am5.color("#7bc142"),
      })
    );

    // Set line styles - dashed line for supply
    supplySeries.strokes.template.setAll({
      strokeWidth: 2,
      strokeDasharray: [5, 5],
    });

    // Add bullets for supply
    supplySeries.bullets.push(function () {
      return am5.Bullet.new(root, {
        sprite: am5.Circle.new(root, {
          radius: 4,
          fill: am5.color("#7bc142"),
          stroke: am5.color("#ffffff"),
          strokeWidth: 2,
        }),
      });
    });

    // Data based on the image - converted to dates
    const data = [
      {
        date: new Date("2017-01-01").getTime(),
        demand: 59000,
        supply: 63000,
      },
      {
        date: new Date("2018-01-01").getTime(),
        demand: 66000,
        supply: 64000,
      },
      {
        date: new Date("2019-01-01").getTime(),
        demand: 63000,
        supply: 64000,
      },
      {
        date: new Date("2020-01-01").getTime(),
        demand: 65000,
        supply: 66000,
      },
      {
        date: new Date("2021-01-01").getTime(),
        demand: 68050,
        supply: 69000,
      },
      {
        date: new Date("2022-01-01").getTime(),
        demand: 68000,
        supply: 70000,
      },
      {
        date: new Date("2023-01-01").getTime(),
        demand: 70000,
        supply: 72000,
      },
      {
        date: new Date("2024-01-01").getTime(),
        demand: 73000,
        supply: 74000,
      },
      {
        date: new Date("2025-01-01").getTime(),
        demand: 75000,
        supply: 76000,
      },
    ];

    // Set data
    demandSeries.data.setAll(data);
    supplySeries.data.setAll(data);

    // Add legend
    const legend = chart.children.push(
      am5.Legend.new(root, {
        centerX: am5.p50,
        x: am5.p50,
        centerY: am5.p0,
        y: am5.p100,
        marginTop: 15,
        marginBottom: 5,
      })
    );

    legend.labels.template.setAll(fontSettings);
    legend.data.setAll(chart.series.values);

    // Add cursor with zoomX behavior
    chart.set(
      "cursor",
      am5xy.XYCursor.new(root, {
        behavior: "zoomX",
      })
    );

    // Make stuff animate on load
    demandSeries.appear(1000);
    supplySeries.appear(1000);
    chart.appear(1000, 100);
  });
}

function chart3() {
  am5.ready(function () {
    // Create root element
    const root = am5.Root.new("chartDiv3");

    // Set themes
    root.setThemes([am5themes_Animated.new(root)]);

    // Create chart
    const chart = root.container.children.push(
      am5percent.PieChart.new(root, {
        layout: root.verticalLayout,
        paddingTop: 0,
        paddingBottom: 0,
        paddingLeft: 0,
        paddingRight: 0,
      })
    );

    // Add chart title
    const title = chart.children.unshift(
      am5.Label.new(root, {
        text: "Aluminium - Global-2023",
        fontSize: 12,
        fontWeight: "600",
        fontFamily: "Montserrat",
        x: am5.p50,
        centerX: am5.p50,
        y: 0,
        centerY: am5.p0,
        marginTop: 10,
        marginBottom: 10,
      })
    );

    // Create series
    const series = chart.series.push(
      am5percent.PieSeries.new(root, {
        valueField: "value",
        categoryField: "category",
        alignLabels: false,
        radius: am5.percent(90),
        innerRadius: am5.percent(0),
      })
    );

    // Add hidden state for animation
    series.states.create("hidden", {
      startAngle: am5.p0,
      endAngle: am5.p0,
    });

    // Set colors to match the image
    series.set(
      "colors",
      am5.ColorSet.new(root, {
        colors: [
          am5.color("#5fb3a1"), // Raw material costs - teal
          am5.color("#7bc142"), // Electricity - green
          am5.color("#2d5a4a"), // Other - dark green
          am5.color("#a8c8d1"), // Labour cost - light blue
        ],
      })
    );

    // Configure slices
    series.slices.template.setAll({
      stroke: am5.color("#ffffff"),
      strokeWidth: 2,
      cornerRadius: 0,
    });

    // Configure labels
    series.labels.template.setAll({
      textType: "circular",
      radius: 4,
      fontSize: 11,
      fontFamily: "Montserrat",
      fontWeight: "400",
    });

    // Configure ticks
    series.ticks.template.setAll({
      strokeOpacity: 1,
      length: 8,
    });

    // Set data
    series.data.setAll([
      { category: "Raw material costs", value: 45 },
      { category: "Electricity", value: 25 },
      { category: "Other", value: 20 },
      { category: "Labour cost", value: 10 },
    ]);

    // Create legend
    const legend = chart.children.push(
      am5.Legend.new(root, {
        centerX: am5.p50,
        x: am5.p50,
        centerY: am5.p0,
        y: am5.p100,
        marginTop: 15,
        marginBottom: 5,
      })
    );

    legend.labels.template.setAll({
      fontSize: 11,
      fontFamily: "Montserrat",
      fontWeight: "400",
    });

    legend.data.setAll(series.dataItems);

    // Animate chart
    series.appear(1000, 100);
    chart.appear(1000, 100);
  });
}

function chart4() {
  am5.ready(function () {
    // Create root element
    const root = am5.Root.new("chartDiv4");

    // Set themes
    root.setThemes([am5themes_Animated.new(root)]);

    // Create chart
    const chart = root.container.children.push(
      am5xy.XYChart.new(root, {
        panX: true,
        panY: true,
        wheelX: "panX",
        wheelY: "zoomX",
        pinchZoomX: true,
        paddingLeft: 0,
        paddingRight: 0,
        paddingTop: 50,
        paddingBottom: 0,
      })
    );

    // Add chart title
    const title = chart.children.unshift(
      am5.Label.new(root, {
        text: "Aluminium cash-settlement (LME)",
        fontSize: 12,
        fontWeight: "600",
        fontFamily: "Montserrat",
        x: am5.p50,
        centerX: am5.p50,
        y: 0,
        centerY: am5.p0,
        marginTop: 10,
        marginBottom: 10,
      })
    );

    // Create axes
    const xAxis = chart.xAxes.push(
      am5xy.DateAxis.new(root, {
        maxZoomCount: 50,
        baseInterval: {
          timeUnit: "month",
          count: 1,
        },
        renderer: am5xy.AxisRendererX.new(root, {
          minGridDistance: 80,
          cellStartLocation: 0.1,
          cellEndLocation: 0.9,
        }),
        tooltip: am5.Tooltip.new(root, {}),
      })
    );

    // Prepare data for actuals (left axis - USD per ton)
    const actualsData = [
      { date: new Date("2023-04-01").getTime(), value: 2350, volume: 1200 },
      { date: new Date("2023-05-01").getTime(), value: 2270, volume: 1350 },
      { date: new Date("2023-06-01").getTime(), value: 2190, volume: 1100 },
      { date: new Date("2023-07-01").getTime(), value: 2160, volume: 1450 },
      { date: new Date("2023-08-01").getTime(), value: 2150, volume: 1300 },
      { date: new Date("2023-09-01").getTime(), value: 2180, volume: 1250 },
      {
        date: new Date("2023-10-01").getTime(),
        value: 2192.45,
        volume: 1400,
      },
      { date: new Date("2023-11-01").getTime(), value: 2210, volume: 1550 },
      { date: new Date("2023-12-01").getTime(), value: 2160, volume: 1320 },
      { date: new Date("2024-01-01").getTime(), value: 2200, volume: 1480 },
      { date: new Date("2024-02-01").getTime(), value: 2190, volume: 1380 },
      { date: new Date("2024-03-01").getTime(), value: 2220, volume: 1420 },
      { date: new Date("2024-04-01").getTime(), value: 2500, volume: 1600 },
      { date: new Date("2024-05-01").getTime(), value: 2570, volume: 1750 },
      { date: new Date("2024-06-01").getTime(), value: 2500, volume: 1650 },
      { date: new Date("2024-07-01").getTime(), value: 2370, volume: 1520 },
      { date: new Date("2024-08-01").getTime(), value: 2340, volume: 1480 },
      { date: new Date("2024-09-01").getTime(), value: 2460, volume: 1580 },
      { date: new Date("2024-10-01").getTime(), value: 2600, volume: 1720 },
      { date: new Date("2024-11-01").getTime(), value: 2580, volume: 1680 },
      { date: new Date("2024-12-01").getTime(), value: 2540, volume: 1620 },
      { date: new Date("2025-01-01").getTime(), value: 2580, volume: 1700 },
      { date: new Date("2025-02-01").getTime(), value: 2670, volume: 1800 },
      { date: new Date("2025-03-01").getTime(), value: 2690, volume: 1850 },
    ];

    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 2100,
        max: 2700,
        renderer: am5xy.AxisRendererY.new(root, {
          strokeOpacity: 1,
          stroke: am5.color("#000000"),
        }),
      })
    );

    // Create second Y-axis (right side) for volume data
    const yAxisRight = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 1000,
        max: 2000,
        renderer: am5xy.AxisRendererY.new(root, {
          opposite: true,
          strokeOpacity: 1,
          stroke: am5.color("#000000"),
        }),
      })
    );

    // Set font styles
    const fontSettings = {
      fontFamily: "Montserrat",
      fontSize: 12,
      fontWeight: "400",
    };

    xAxis.get("renderer").labels.template.setAll(fontSettings);
    yAxis.get("renderer").labels.template.setAll(fontSettings);
    yAxisRight.get("renderer").labels.template.setAll(fontSettings);

    // Configure X-axis date format (MMM-YYYY like Jul-2024)
    xAxis.get("renderer").labels.template.setAll({
      ...fontSettings,
      rotation: -45,
      centerY: am5.p50,
      centerX: am5.p100,
    });

    // Set date format for X-axis labels
    xAxis.set("dateFormats", {
      month: "MMM-yyyy",
    });

    xAxis.set("periodChangeDateFormats", {
      month: "MMM-yyyy",
    });

    // Configure axis lines
    xAxis.get("renderer").set("strokeOpacity", 1);
    xAxis.get("renderer").set("stroke", am5.color("#000000"));

    // Create series for price (left axis - solid line)
    const priceSeries = chart.series.push(
      am5xy.LineSeries.new(root, {
        name: "Price (USD/ton)",
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "value",
        valueXField: "date",
        tooltip: am5.Tooltip.new(root, {
          labelText: "{valueY}",
        }),
        stroke: am5.color("#00b19c"),
        fill: am5.color("#00b19c"),
      })
    );

    // Create series for volume (right axis - dashed line)
    const volumeSeries = chart.series.push(
      am5xy.LineSeries.new(root, {
        name: "Volume (000 tons)",
        xAxis: xAxis,
        yAxis: yAxisRight,
        valueYField: "volume",
        valueXField: "date",
        tooltip: am5.Tooltip.new(root, {
          labelText: "{valueY}",
        }),
        stroke: am5.color("#3bcd3f"),
        fill: am5.color("#3bcd3f"),
      })
    );

    // Set line styles
    priceSeries.strokes.template.setAll({
      strokeWidth: 2,
      strokeDasharray: [],
    });

    volumeSeries.strokes.template.setAll({
      strokeWidth: 2,
      strokeDasharray: [5, 5],
    });

    // Add bullets (markers) for price series
    priceSeries.bullets.push(function () {
      const bulletCircle = am5.Circle.new(root, {
        radius: 4,
        fill: am5.color("#00b19c"),
        stroke: am5.color("#ffffff"),
        strokeWidth: 2,
      });
      return am5.Bullet.new(root, {
        sprite: bulletCircle,
      });
    });

    // Add bullets (markers) for volume series
    volumeSeries.bullets.push(function () {
      const bulletCircle = am5.Circle.new(root, {
        radius: 4,
        fill: am5.color("#3bcd3f"),
        stroke: am5.color("#ffffff"),
        strokeWidth: 2,
      });
      return am5.Bullet.new(root, {
        sprite: bulletCircle,
      });
    });

    // Add legend
    const legend = chart.children.push(
      am5.Legend.new(root, {
        centerX: am5.p50,
        x: am5.p50,
        centerY: am5.p0,
        y: am5.p100,
        marginTop: 15,
        marginBottom: 5,
      })
    );

    legend.labels.template.setAll(fontSettings);
    legend.data.setAll(chart.series.values);

    // Set data
    priceSeries.data.setAll(actualsData);
    volumeSeries.data.setAll(actualsData);

    // Add cursor
    chart.set(
      "cursor",
      am5xy.XYCursor.new(root, {
        behavior: "zoomX",
      })
    );

    // Configure grid
    xAxis.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    yAxis.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    yAxisRight.get("renderer").grid.template.setAll({
      stroke: am5.color("#e0e0e0"),
      strokeDasharray: [1, 3],
    });

    // Make stuff animate on load
    priceSeries.appear(1000);
    volumeSeries.appear(1000);
    chart.appear(1000, 100);
  });
}

function createSupplyDemandChart() {
  // Ensure AmCharts is available
  var container = document.getElementById("supplyDemandChart");
  if (!container) {
    console.error("supplyDemandChart container not found");
    return;
  }

  console.log(
    "Creating Supply Demand chart, container dimensions:",
    container.offsetWidth + "x" + container.offsetHeight
  );

  // Clean up any existing chart and observer
  if (container.resizeObserver) {
    console.log("Disconnecting existing ResizeObserver for supplyDemandChart");
    container.resizeObserver.disconnect();
    container.resizeObserver = null;
  }

  try {
    // Create root element
    var root = am5.Root.new("supplyDemandChart");

    // Set themes
    root.setThemes([am5themes_Animated.new(root)]);

    // Create chart
    var chart = root.container.children.push(
      am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        wheelX: "panX",
        wheelY: "zoomX",
        layout: root.verticalLayout,
      })
    );

    // Create axes
    var xAxis = chart.xAxes.push(
      am5xy.CategoryAxis.new(root, {
        categoryField: "year",
        renderer: am5xy.AxisRendererX.new(root, {}),
        tooltip: am5.Tooltip.new(root, {}),
      })
    );

    xAxis.data.setAll([
      { year: "2018" },
      { year: "2019" },
      { year: "2020" },
      { year: "2021" },
      { year: "2022" },
      { year: "2023" },
      { year: "2024E" },
      { year: "2025F" },
    ]);

    var yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 0,
        max: 300,
        numberFormat: "#'K'",
        renderer: am5xy.AxisRendererY.new(root, {}),
      })
    );

    yAxis.children.push(
      am5.Label.new(root, {
        text: "('000 tonnes)",
        rotation: -90,
        y: am5.p50,
        centerX: am5.p50,
      })
    );

    // Add supply series
    var supplySeries = chart.series.push(
      am5xy.ColumnSeries.new(root, {
        name: "Supply",
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "supply",
        categoryXField: "year",
        tooltip: am5.Tooltip.new(root, {
          labelText: "Supply: {valueY}",
        }),
      })
    );

    supplySeries.columns.template.setAll({
      width: am5.p80,
      tooltipY: 0,
      strokeOpacity: 0,
      fill: am5.color("#00B19C"),
    });

    // Add demand series
    var demandSeries = chart.series.push(
      am5xy.ColumnSeries.new(root, {
        name: "Demand",
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "demand",
        categoryXField: "year",
        tooltip: am5.Tooltip.new(root, {
          labelText: "Demand: {valueY}",
        }),
      })
    );

    demandSeries.columns.template.setAll({
      width: am5.p80,
      tooltipY: 0,
      strokeOpacity: 0,
      fill: am5.color("#8DBAC4"),
    });

    // Set data
    supplySeries.data.setAll([
      { year: "2018", supply: 125 },
      { year: "2019", supply: 140 },
      { year: "2020", supply: 145 },
      { year: "2021", supply: 160 },
      { year: "2022", supply: 185 },
      { year: "2023", supply: 220 },
      { year: "2024E", supply: 245 },
      { year: "2025F", supply: 265 },
    ]);

    demandSeries.data.setAll([
      { year: "2018", demand: 120 },
      { year: "2019", demand: 130 },
      { year: "2020", demand: 130 },
      { year: "2021", demand: 160 },
      { year: "2022", demand: 175 },
      { year: "2023", demand: 200 },
      { year: "2024E", demand: 222 },
      { year: "2025F", demand: 255 },
    ]);

    // Create legend
    var legend = chart.children.push(
      am5.Legend.new(root, {
        centerX: am5.p50,
        x: am5.p50,
        visible: false,
      })
    );

    legend.data.setAll(chart.series.values);

    // Make series and chart appear
    supplySeries.appear(1000);
    demandSeries.appear(1000);
    chart.appear(1000, 100);

    // Enhanced ResizeObserver
    const resizeObserver = new ResizeObserver((entries) => {
      console.log("ResizeObserver triggered for supplyDemandChart");
      if (root && container.offsetWidth > 0 && container.offsetHeight > 0) {
        console.log(
          "Before resize dimensions:",
          root.width(),
          "x",
          root.height()
        );
        root.resize();
        console.log(
          "After resize dimensions:",
          root.width(),
          "x",
          root.height()
        );
      } else {
        console.log(
          "ResizeObserver skipped: supplyDemandChart has zero dimensions or root is unavailable"
        );
      }
    });

    // Start observing the container
    resizeObserver.observe(container);
    container.resizeObserver = resizeObserver;

    console.log("Supply Demand chart created successfully");
  } catch (error) {
    console.error("Error creating Supply Demand chart:", error);
  }
}

// Initialize all charts when the page loads
function initializeAllCharts() {
  try {
    // Only initialize charts if their containers exist and are visible
    if (document.getElementById("chartdiv1") && document.getElementById("chartdiv1").style.display !== "none") {
      chart1();
    }
    if (document.getElementById("chartdiv1a") && document.getElementById("chartdiv1a").style.display !== "none") {
      chart1a();
    }
    if (document.getElementById("chartdiv2") && document.getElementById("chartdiv2").style.display !== "none") {
      chart2();
    }
    if (document.getElementById("chartdiv2a") && document.getElementById("chartdiv2a").style.display !== "none") {
      chart2a();
    }
    if (document.getElementById("chartdiv3") && document.getElementById("chartdiv3").style.display !== "none") {
      chart3();
    }
    if (document.getElementById("chartdiv4") && document.getElementById("chartdiv4").style.display !== "none") {
      chart4();
    }
  } catch (error) {
    console.log("Some charts could not be initialized:", error.message);
  }
}

// Call initialization when DOM is ready
document.addEventListener("DOMContentLoaded", function () {
  // Delay chart initialization to ensure DOM is fully ready
  setTimeout(initializeAllCharts, 1000);
});
