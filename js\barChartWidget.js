// Using shared brand colors from chartConfig.js

// Add a bar chart widget using amCharts v5
function addBarChartWidget() {
  console.log("Adding bar chart widget");
  const chartId = "barchart-" + Date.now();

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 8,
    content: `
      <div class="bar-chart-widget p-2">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
            Bar Chart
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#barChartSettings"
                    aria-controls="barChartSettings"
                    onclick="initBarChartSettings('${chartId}')">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div id="${chartId}" class="chart-container"></div>
      </div>
    `,
  });

  // Initialize the chart with a slight delay
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing chart");
      window.initBarChart(chartId);
    } catch (error) {
      console.error("Error initializing chart:", error);
    }
  }, 1000);

  return widget;
}

// Function to handle backdrop cleanup
function handleBackdropCleanup() {
  // Remove all backdrops
  const backdrops = document.querySelectorAll(".offcanvas-backdrop");
  backdrops.forEach((backdrop) => {
    backdrop.remove();
  });
}

// Function to initialize bar chart settings
function initBarChartSettings(chartId) {
  const settingsPanel = document.getElementById("barChartSettings");
  if (!settingsPanel) return;

  // Store the current chart ID
  settingsPanel.dataset.currentChart = chartId;

  // Find the widget element for border settings
  const chartContainer = document.getElementById(chartId);
  if (chartContainer) {
    const widgetElement = chartContainer.closest('.grid-stack-item') || chartContainer.closest('.bar-chart-widget');
    if (widgetElement && typeof window.setCurrentBarChartWidget === 'function') {
      window.setCurrentBarChartWidget(widgetElement);
      console.log('🎨 Set current widget for border settings');
    }
  }

  // Initialize offcanvas with proper options
  const bsOffcanvas = new bootstrap.Offcanvas(settingsPanel, {
    backdrop: true,
    keyboard: true,
    scroll: false,
  });

  // Remove any existing event listeners
  settingsPanel.removeEventListener(
    "hidden.bs.offcanvas",
    handleBackdropCleanup
  );
  // Add event listener for when offcanvas is hidden
  settingsPanel.addEventListener("hidden.bs.offcanvas", handleBackdropCleanup);

  // Show the offcanvas
  bsOffcanvas.show();
}

// Function to apply bar chart settings
function applyBarChartSettings() {
  const settingsPanel = document.getElementById("barChartSettings");
  const chartId = settingsPanel.dataset.currentChart;
  if (!chartId) return;

  const chart = document.getElementById(chartId)?.chart;
  if (!chart) return;

  // Get settings values
  const chartTitle = document.getElementById(
    "barChartSettings-chartTitle"
  ).value;
  const yAxisTitle = document.getElementById(
    "barChartSettings-yAxisTitle"
  ).value;
  const type = document.getElementById("barChartSettings-type").value;
  const columnWidth = document.getElementById(
    "barChartSettings-columnWidth"
  ).value;
  const cornerRadius = document.getElementById(
    "barChartSettings-cornerRadius"
  ).value;
  const showLabels = document.getElementById("barChartSettings-labels").checked;
  const showGridLines = document.getElementById(
    "barChartSettings-gridLines"
  ).checked;
  const showLegend = document.getElementById("barChartSettings-legend").checked;

  // Get the series
  const series = chart.series.getIndex(0);
  if (!series) return;

  // Update chart title
  // The title is in the chartContainer, not directly in the chart
  // First, try to find the title in the chart container
  let chartTitleLabel = null;

  // Get the chart container (parent of the chart)
  const chartContainer = chart.root.container.children.values[0];

  if (chartContainer) {
    // Look for the title in the container's children
    chartContainer.children.each(function (child) {
      if (child.isType("Label") && child.get("role") === "title") {
        chartTitleLabel = child;
        return false; // Break the loop
      }
    });

    if (chartTitleLabel) {
      // Update existing title
      chartTitleLabel.set("text", chartTitle);
    } else {
      // If no title found, add it as the first child (at the top)
      chartTitleLabel = chartContainer.children.unshift(
        am5.Label.new(chart.root, {
          text: chartTitle,
          fontSize: 12,
          fontWeight: "500",
          textAlign: "center",
          x: am5.p50,
          centerX: am5.p50,
          paddingTop: 5,
          paddingBottom: 10,
          role: "title",
        })
      );
    }
  }

  // Update Y-axis title
  let yAxis = chart.yAxes.getIndex(0);
  if (yAxis) {
    // Find the existing title label
    let yAxisTitleLabel = null;
    yAxis.children.each(function (child) {
      if (child.isType("Label")) {
        yAxisTitleLabel = child;
        return false; // Break the loop
      }
    });

    if (yAxisTitleLabel) {
      yAxisTitleLabel.set("text", yAxisTitle);
    } else {
      // Add title to y-axis if it doesn't exist
      yAxis.children.unshift(
        am5.Label.new(chart.root, {
          rotation: -90,
          text: yAxisTitle,
          y: am5.p50,
          centerX: am5.p50,
          fill: am5.color(0x000000),
          fontSize: 12,
          fontWeight: "500",
          paddingLeft: 10,
        })
      );
    }
  }

  // Apply settings
  series.set("clustered", type === "clustered");

  // Apply column template settings if it exists
  if (series.columns && series.columns.template) {
    series.columns.template.setAll({
      width: am5.percent(columnWidth),
      cornerRadiusTL: parseInt(cornerRadius),
      cornerRadiusTR: parseInt(cornerRadius),
    });
  }

  // Toggle labels (if they exist)
  if (series.labels && series.labels.template) {
    series.labels.template.set("forceHidden", !showLabels);
  }

  // Toggle grid lines
  const xAxis = chart.xAxes.getIndex(0);
  // Re-get yAxis to ensure we have the latest reference
  yAxis = chart.yAxes.getIndex(0);

  // Toggle X-axis grid lines if they exist
  if (
    xAxis &&
    xAxis.get("renderer") &&
    xAxis.get("renderer").grid &&
    xAxis.get("renderer").grid.template
  ) {
    xAxis.get("renderer").grid.template.set("forceHidden", !showGridLines);
  }

  // Toggle Y-axis grid lines if they exist
  if (
    yAxis &&
    yAxis.get("renderer") &&
    yAxis.get("renderer").grid &&
    yAxis.get("renderer").grid.template
  ) {
    yAxis.get("renderer").grid.template.set("forceHidden", !showGridLines);
  }

  // Toggle legend
  const legend =
    chart.children.indexOf(chart.legend) !== -1 ? chart.legend : null;
  if (legend) {
    legend.set("forceHidden", !showLegend);
  }

  // Update chart data from the table
  updateBarChartDataFromTable(chart);

  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(settingsPanel);
  if (offcanvas) {
    offcanvas.hide();
  }
}

// Initialize a bar chart using amCharts v5
window.initBarChart = function (containerId) {
  console.log("Starting bar chart initialization for container:", containerId);
  const container = document.getElementById(containerId);

  if (!container) {
    console.error("Chart container not found:", containerId);
    return;
  }

  // Dispose previous amCharts root if it exists
  if (container.am5root) {
    try {
      container.am5root.dispose();
    } catch (e) {
      console.warn("Error disposing previous am5root:", e);
    }
    container.am5root = null;
  }

  // Make sure the container has dimensions
  if (container.offsetWidth === 0 || container.offsetHeight === 0) {
    console.warn("Chart container has zero dimensions, setting explicit size");
    container.style.width = "100%";
    container.style.height = "300px";
  }

  console.log(
    "Container dimensions:",
    container.offsetWidth,
    "x",
    container.offsetHeight
  );

  // Create root element
  const root = am5.Root.new(containerId);
  container.am5root = root;

  // Set themes
  root.setThemes([am5themes_Animated.new(root)]);

  // Create a container for the chart and title
  const chartContainer = root.container.children.push(
    am5.Container.new(root, {
      width: am5.p100,
      height: am5.p100,
      layout: root.verticalLayout,
    })
  );

  // Add chart title at the top of the container
  chartContainer.children.push(
    am5.Label.new(root, {
      text: "Monthly Revenue",
      fontSize: 12,
      fontWeight: "500",
      textAlign: "center",
      x: am5.p50,
      centerX: am5.p50,
      paddingTop: 5,
      paddingBottom: 10,
      role: "title", // Used to identify this as the chart title
    })
  );

  // Create chart inside the container
  const chart = chartContainer.children.push(
    am5xy.XYChart.new(root, {
      panX: false,
      panY: false,
      wheelX: "panX",
      wheelY: "zoomX",
      layout: root.verticalLayout,
      paddingTop: 10,
      paddingBottom: 20,
      paddingLeft: 10,
      paddingRight: 10,
      width: am5.p100,
      height: am5.p100,
    })
  );

  // Create axes
  const xAxis = chart.xAxes.push(
    am5xy.CategoryAxis.new(root, {
      categoryField: "category",
      renderer: am5xy.AxisRendererX.new(root, {}),
      tooltip: am5.Tooltip.new(root, {}),
    })
  );

  xAxis.data.setAll([
    { category: "USA" },
    { category: "China" },
    { category: "Japan" },
    { category: "Germany" },
    { category: "UK" },
    { category: "France" },
    { category: "India" },
  ]);

  const yAxis = chart.yAxes.push(
    am5xy.ValueAxis.new(root, {
      renderer: am5xy.AxisRendererY.new(root, {}),
    })
  );

  // Add title to y-axis
  yAxis.children.unshift(
    am5.Label.new(root, {
      rotation: -90,
      text: "Revenue ($)",
      y: am5.p50,
      centerX: am5.p50,
      fill: am5.color(0x000000),
      fontSize: 12,
      fontWeight: "500",
      paddingLeft: 10,
    })
  );

  // Add series
  const series = chart.series.push(
    am5xy.ColumnSeries.new(root, {
      name: "GDP",
      xAxis: xAxis,
      yAxis: yAxis,
      valueYField: "value",
      categoryXField: "category",
      tooltip: am5.Tooltip.new(root, {
        labelText: "{name}: {valueY}",
      }),
    })
  );

  // We'll use the brand colors directly in the adapter

  // Set up a color adapter for the columns
  series.columns.template.adapters.add("fill", (fill, target) => {
    if (target.dataItem) {
      const index = target.dataItem.get("index") || 0;
      const colorIndex = index % window.chartConfig.brandColors.length;
      return am5.color(window.chartConfig.brandColors[colorIndex]);
    }
    return fill;
  });

  // Configure series columns
  series.columns.template.setAll({
    cornerRadiusTL: 5,
    cornerRadiusTR: 5,
    strokeOpacity: 0,
    fillOpacity: 0.8,
  });

  // Add hover state
  series.columns.template.states.create("hover", {
    fillOpacity: 1,
  });

  // Set data
  series.data.setAll([
    { category: "USA", value: 22.2 },
    { category: "China", value: 16.4 },
    { category: "Japan", value: 4.9 },
    { category: "Germany", value: 4.2 },
    { category: "UK", value: 3.3 },
    { category: "France", value: 2.9 },
    { category: "India", value: 3.2 },
  ]);

  // Add legend as a separate element below the chart
  const legend = chartContainer.children.push(
    am5.Legend.new(root, {
      centerX: am5.p50,
      x: am5.p50,
      layout: root.horizontalLayout,
      marginTop: 10,
      paddingTop: 10,
      paddingBottom: 10,
      background: am5.Rectangle.new(root, {
        fillOpacity: 0.03,
        fill: am5.color(0x000000),
      }),
    })
  );

  // Set legend data items
  legend.data.setAll(chart.series.values);

  // Add cursor
  chart.set(
    "cursor",
    am5xy.XYCursor.new(root, {
      behavior: "zoomX",
    })
  );

  // Add animation
  series.appear(1000, 100);

  console.log("Bar chart initialization complete for container:", containerId);

  // Store the chart instance on the container element
  container.chart = chart;

  // Return the root object so it doesn't get garbage collected
  return root;
};

// Function to load bar chart data into the data table
function loadBarChartDataToTable(chart) {
  const tableBody = document.getElementById("barChartDataBody");
  if (!tableBody) return;

  // Clear existing rows
  tableBody.innerHTML = "";

  // Get data from the series
  const series = chart.series.getIndex(0);
  if (!series || !series.dataItems) {
    // If no data, generate sample data
    generateSampleBarChartData(tableBody);
    return;
  }

  // Get data items
  const dataItems = series.dataItems;

  // Create rows for each data point
  dataItems.forEach((item) => {
    const category = item.get("categoryX");
    const value = item.get("valueY");

    // Add a row to the table
    addBarChartDataRow(tableBody, category, value);
  });
}

// Function to generate sample data if none exists
function generateSampleBarChartData(tableBody) {
  if (!tableBody) return;

  // Sample categories
  const categories = [
    "USA",
    "China",
    "Japan",
    "Germany",
    "UK",
    "France",
    "India",
  ];

  // Generate sample data
  categories.forEach((category) => {
    // Generate random values
    const revenue = Math.round(Math.random() * 1000) / 10;

    // Add a row to the table
    addBarChartDataRow(tableBody, category, revenue);
  });
}

// Helper function to add a data row with specific values
function addBarChartDataRow(tableBody, category, value) {
  if (!tableBody) return;

  // Create a new row
  const row = document.createElement("tr");

  // Category cell
  const categoryCell = document.createElement("td");
  const categoryInput = document.createElement("input");
  categoryInput.type = "text";
  categoryInput.className = "form-control form-control-sm";
  categoryInput.value = category || "";
  categoryCell.appendChild(categoryInput);

  // Value cell
  const valueCell = document.createElement("td");
  const valueInput = document.createElement("input");
  valueInput.type = "number";
  valueInput.className = "form-control form-control-sm";
  valueInput.value = value || 0;
  valueInput.step = "0.1";
  valueCell.appendChild(valueInput);

  // Actions cell
  const actionsCell = document.createElement("td");
  actionsCell.className = "text-center";
  const deleteButton = document.createElement("button");
  deleteButton.className = "btn btn-sm btn-outline-danger";
  deleteButton.style.width = "28px";
  deleteButton.style.height = "28px";
  deleteButton.style.padding = "0";
  deleteButton.innerHTML = "<i class='las la-times'></i>";
  deleteButton.title = "Remove row";
  deleteButton.addEventListener("click", () => {
    row.remove();
  });
  actionsCell.appendChild(deleteButton);

  // Add cells to row
  row.appendChild(categoryCell);
  row.appendChild(valueCell);
  row.appendChild(actionsCell);

  // Add row to table
  tableBody.appendChild(row);
}

// Function to add a new data row
function addNewBarChartDataRow() {
  const tableBody = document.getElementById("barChartDataBody");
  if (!tableBody) return;

  // Add a new row with default values
  addBarChartDataRow(tableBody, "New Category", 0);
}

// Function to update chart data from the table
function updateBarChartDataFromTable(chart) {
  const tableBody = document.getElementById("barChartDataBody");
  if (!tableBody || !chart) return;

  // Get all rows
  const rows = tableBody.querySelectorAll("tr");
  if (rows.length === 0) return;

  // Prepare data array
  const chartData = [];

  // Extract data from each row
  rows.forEach((row) => {
    const cells = row.querySelectorAll("td");
    if (cells.length >= 2) {
      const categoryInput = cells[0].querySelector("input");
      const valueInput = cells[1].querySelector("input");

      if (categoryInput && valueInput) {
        const category = categoryInput.value;
        const value = parseFloat(valueInput.value) || 0;

        if (category) {
          chartData.push({
            category: category,
            value: value,
          });
        }
      }
    }
  });

  // Update chart data
  const series = chart.series.getIndex(0);
  if (series) {
    series.data.setAll(chartData);
  }

  // Update x-axis categories
  const xAxis = chart.xAxes.getIndex(0);
  if (xAxis) {
    xAxis.data.setAll(chartData.map((item) => ({ category: item.category })));
  }
}

// Function to import CSV data
function importBarChartCSV() {
  const fileInput = document.getElementById("barChartDataFile");
  if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
    alert("Please select a CSV file to import.");
    return;
  }

  const file = fileInput.files[0];
  const reader = new FileReader();

  reader.onload = function (e) {
    const contents = e.target.result;
    const lines = contents.split("\n");

    // Clear existing data
    const tableBody = document.getElementById("barChartDataBody");
    if (!tableBody) return;

    tableBody.innerHTML = "";

    // Process each line
    lines.forEach((line, index) => {
      // Skip empty lines
      if (!line.trim()) return;

      // Skip header row if present
      if (
        index === 0 &&
        line.toLowerCase().includes("category") &&
        line.toLowerCase().includes("revenue")
      ) {
        return;
      }

      const values = line.split(",");
      if (values.length < 2) return; // Need at least category and one value

      const category = values[0].trim();
      const revenue = parseFloat(values[1]) || 0;

      // Add a row to the table
      addBarChartDataRow(tableBody, category, revenue);
    });

    alert("CSV data imported successfully!");
  };

  reader.onerror = function () {
    alert("Error reading the file. Please try again.");
  };

  reader.readAsText(file);
}

// Function to export CSV data
function exportBarChartCSV() {
  const tableBody = document.getElementById("barChartDataBody");
  if (!tableBody) return;

  const rows = tableBody.querySelectorAll("tr");
  if (rows.length === 0) {
    alert("No data to export.");
    return;
  }

  // Create CSV content
  let csvContent = "Category,Value\n";

  rows.forEach((row) => {
    const cells = row.querySelectorAll("td");
    if (cells.length >= 2) {
      const categoryInput = cells[0].querySelector("input");
      const valueInput = cells[1].querySelector("input");

      if (categoryInput && valueInput) {
        const category = categoryInput.value;
        const value = valueInput.value;

        csvContent += `${category},${value}\n`;
      }
    }
  });

  // Create a download link
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.setAttribute("href", url);
  link.setAttribute("download", "bar-chart-data.csv");
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// Export the functions
window.addBarChartWidget = addBarChartWidget;
window.applyBarChartSettings = applyBarChartSettings;
window.loadBarChartDataToTable = loadBarChartDataToTable;
window.updateBarChartDataFromTable = updateBarChartDataFromTable;
window.addNewBarChartDataRow = addNewBarChartDataRow;
window.importBarChartCSV = importBarChartCSV;
window.exportBarChartCSV = exportBarChartCSV;
