// Add a stacked column chart widget using amCharts v5

// Series configuration - use a unique name to avoid conflicts
window.stackedColumnSeriesDefaults = [
  { id: 1, name: "Electronics", field: "electronics" },
  { id: 2, name: "Apparel", field: "apparel" },
  { id: 3, name: "Home & Garden", field: "homeGarden" },
  { id: 4, name: "Sports", field: "sports" },
  { id: 5, name: "Books", field: "books" },
  { id: 6, name: "Toys", field: "toys" },
  { id: 7, name: "Beauty", field: "beauty" },
  { id: 8, name: "Food", field: "food" },
  { id: 9, name: "Health", field: "health" },
  { id: 10, name: "Auto", field: "auto" },
];

// Use a local components utility for this widget only
// This avoids conflicts with other global objects
window.stackedColumnComponents = window.stackedColumnComponents || {
  createStyles() {
    return `
      .section-content {
        margin-top: 1rem;
      }

      .floating-actions {
        position: sticky;
        bottom: 1rem;
        background: white;
        padding: 1rem;
        border-top: 1px solid #dee2e6;
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
      }
    `;
  },

  createFormInput(id, label, type = "text", options = {}) {
    const inputId = `chart-input-${id}`;
    let input = "";

    switch (type) {
      case "range":
        input = `
          <label for="${inputId}" class="form-label d-flex justify-content-between">
            ${label}
            <span class="value-display">${options.value || "0"}</span>
          </label>
          <input type="range"
                 class="form-range"
                 id="${inputId}"
                 min="${options.min || "0"}"
                 max="${options.max || "100"}"
                 step="${options.step || "1"}"
                 value="${options.value || "0"}">
        `;
        break;

      case "color":
        input = `
          <label for="${inputId}" class="form-label">${label}</label>
          <input type="color"
                 class="form-control form-control-color w-100"
                 id="${inputId}"
                 value="${options.value || "#000000"}">
        `;
        break;

      case "select":
        const optionsHtml = options.options
          .map((opt) => `<option value="${opt}">${opt}</option>`)
          .join("");
        input = `
          <label for="${inputId}" class="form-label">${label}</label>
          <select class="form-select"
                  id="${inputId}">
            ${optionsHtml}
          </select>
        `;
        break;

      default:
        input = `
          <label for="${inputId}" class="form-label">${label}</label>
          <input type="${type}"
                 class="form-control"
                 id="${inputId}"
                 value="${options.value || ""}"
                 ${Object.entries(options)
                   .map(([key, value]) => `${key}="${value}"`)
                   .join(" ")}>
        `;
    }

    return `<div class="mb-3">${input}</div>`;
  },

  createToggleSwitch(id, label, defaultChecked = false) {
    const inputId = `chart-toggle-${id}`;
    return `
      <div class="form-check form-switch mb-3">
        <input type="checkbox"
               class="form-check-input"
               id="${inputId}"
               ${defaultChecked ? "checked" : ""}>
        <label class="form-check-label" for="${inputId}">${label}</label>
      </div>
    `;
  },

  createTabsContainer(tabs) {
    const tabButtons = tabs
      .map(
        (tab, index) => `
        <button class="nav-link ${index === 0 ? "active" : ""}"
                id="tab-${tab.id}"
                data-bs-toggle="tab"
                data-bs-target="#${tab.id}-pane"
                type="button"
                role="tab"
                aria-controls="${tab.id}-pane"
                aria-selected="${index === 0}">
          <i class="las ${tab.icon}"></i>
          ${tab.label}
        </button>
      `
      )
      .join("");

    const tabPanes = tabs
      .map(
        (tab, index) => `
        <div class="tab-pane fade ${index === 0 ? "show active" : ""}"
             id="${tab.id}-pane"
             role="tabpanel"
             aria-labelledby="tab-${tab.id}"
             tabindex="0">
          ${tab.content}
        </div>
      `
      )
      .join("");

    return `
      <nav>
        <div class="nav nav-tabs" role="tablist">
          ${tabButtons}
        </div>
      </nav>
      <div class="tab-content pt-3">
        ${tabPanes}
      </div>
    `;
  },

  createFloatingActions(actions) {
    const buttons = actions
      .map(
        (action) => `
        <button class="btn ${action.class}"
                onclick="${action.onClick}"
                data-bs-toggle="tooltip"
                title="${action.tooltip}">
          <i class="las ${action.icon}"></i>
          ${action.label}
        </button>
      `
      )
      .join("");

    return `<div class="floating-actions">${buttons}</div>`;
  },

  createExportSection(formats) {
    const buttons = formats
      .map(
        (format) => `
      <button class="btn btn-outline-secondary mb-2 me-2"
              onclick="exportChart('${format}')"
              data-bs-toggle="tooltip"
              title="Export as ${format.toUpperCase()}">
        <i class="las la-file-export"></i>
        Export as ${format.toUpperCase()}
      </button>
    `
      )
      .join("");

    return `
      <div class="export-buttons">
        ${buttons}
      </div>
    `;
  },
};

// Add a stacked column chart widget using amCharts v5
window.addStackedColumnChartWidget = function () {
  console.log("Adding stacked column chart widget");
  const chartId = "stacked-column-" + Date.now();

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 8,
    content: `
      <div class="stacked-column-chart-widget p-2" style="height: 100%; display: flex; flex-direction: column;">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
             Stacked Column Chart
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#stackedColumnChartSettings"
                    aria-controls="stackedColumnChartSettings"
                    onclick="initStackedColumnChartSettings('${chartId}')">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
          <div id="${chartId}" class="chart-container" style="flex: 1; width: 100%; height: 100%; min-height: 300px; position: relative;"></div>
        </div>
      </div>
    `,
  });

  // Initialize the chart with a slight delay to ensure DOM is ready
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing chart");
      initStackedColumnChart(chartId);
    } catch (error) {
      console.error("Error initializing chart:", error);
    }
  }, 100);

  return widget;
};

// Function to initialize the stacked column chart
window.initStackedColumnChart = function (chartId) {
  console.log("Initializing stacked column chart:", chartId);

  // Get the chart container element
  const chartContainer = document.getElementById(chartId);
  if (!chartContainer) {
    console.error("Chart container not found:", chartId);
    return;
  }

  // Ensure the container and its parent have proper dimensions
  chartContainer.style.width = "100%";
  chartContainer.style.height = "100%";
  chartContainer.style.minHeight = "300px";
  chartContainer.style.position = "relative";

  // If container is inside a widget, ensure the widget has proper height
  const widgetBody = chartContainer.closest(".widget-body");
  if (widgetBody) {
    widgetBody.style.flex = "1 1 auto";
    widgetBody.style.minHeight = "0";
    widgetBody.style.display = "flex";
    widgetBody.style.height = "100%";
  }

  // Force overflow visible on parent gridstack item content
  const gridStackItemContent = chartContainer.closest(
    ".grid-stack-item-content"
  );
  if (gridStackItemContent) {
    gridStackItemContent.style.overflow = "visible";
    gridStackItemContent.style.height = "100%";
  }

  // Get the widget container
  const widgetContainer = chartContainer.closest(
    ".stacked-column-chart-widget"
  );
  if (widgetContainer) {
    widgetContainer.style.height = "100%";
    widgetContainer.style.display = "flex";
    widgetContainer.style.flexDirection = "column";
  }

  // Dispose of any existing chart instance
  if (chartContainer.chart && chartContainer.chart.root) {
    try {
      chartContainer.chart.root.dispose();
    } catch (error) {
      console.warn("Error disposing chart:", error);
    }
  }

  // Clear the container
  chartContainer.innerHTML = "";

  // Check if amCharts is loaded
  if (typeof am5 === "undefined") {
    console.error(
      "amCharts 5 is not loaded. Please check your script includes."
    );
    return;
  }

  // Check if there's already a Root instance for this DOM node
  // If so, dispose of it first to avoid the "multiple Roots" error
  if (am5.registry.rootElements) {
    const existingRoot = am5.registry.rootElements.find(
      (root) => root.dom.id === chartId
    );
    if (existingRoot) {
      console.log("Found existing Root for this DOM node, disposing it first");
      existingRoot.dispose();
    }
  }

  // Create root element
  const root = am5.Root.new(chartId);

  // Set themes
  if (typeof am5themes_Animated !== "undefined") {
    root.setThemes([am5themes_Animated.new(root)]);
  } else {
    console.warn(
      "amCharts 5 Animated theme is not loaded. Using default theme."
    );
  }

  // Create a container for the chart and title
  const chartWrapper = root.container.children.push(
    am5.Container.new(root, {
      width: am5.p100,
      height: am5.p100,
      layout: root.verticalLayout,
    })
  );

  // Add chart title at the top of the container
  chartWrapper.children.push(
    am5.Label.new(root, {
      text: "Monthly Sales by Category",
      fontSize: 14,
      fontWeight: "500",
      textAlign: "center",
      x: am5.p50,
      centerX: am5.p50,
      paddingTop: 5,
      paddingBottom: 10,
      fill: am5.color(0x000000), // Black text color
      role: "title", // Used to identify this as the chart title
    })
  );

  // Check if am5xy is loaded
  if (typeof am5xy === "undefined") {
    console.error(
      "amCharts 5 XY module is not loaded. Please check your script includes."
    );
    return;
  }

  // Create chart inside the container
  const chart = chartWrapper.children.push(
    am5xy.XYChart.new(root, {
      panX: false,
      panY: false,
      wheelX: "none",
      wheelY: "none",
      layout: root.verticalLayout,
      paddingTop: 10,
      paddingBottom: 20,
      paddingLeft: 10,
      paddingRight: 10,
      width: am5.p100,
      height: am5.p100,
    })
  );

  // Apply brand colors if available
  if (window.chartConfig && window.chartConfig.brandColors) {
    const brandColorSet = am5.ColorSet.new(root, {
      colors: window.chartConfig.brandColors.map((color) => am5.color(color)),
      reuse: true,
    });
    chart.set("colors", brandColorSet);
    console.log("Applied brand colors to stacked column chart");
  } else {
    console.warn("Brand colors not found. Using default theme colors.");
  }

  // Store both root and chart instances
  chartContainer.chart = chart;
  chartContainer.root = root;

  // Mark the chart container as initialized
  chartContainer.setAttribute("data-initialized", "true");

  // Add resize observer to handle chart resizing
  const resizeObserver = new ResizeObserver(() => {
    // Get the current dimensions of the container
    const width = chartContainer.clientWidth;
    const height = chartContainer.clientHeight;

    console.log(`Resizing stacked column chart to ${width}x${height}`);

    // In amCharts 5, we use root.resize() to handle resizing
    // No need to call setSize() as it's handled internally
    try {
      // Resize the root element
      root.resize();

      // Force chart to redraw
      chart.series.each(function (series) {
        series.appear(1000, 100);
      });
    } catch (error) {
      console.warn("Error resizing chart:", error);
    }
  });

  // Observe the chart container for size changes
  resizeObserver.observe(chartContainer);

  // Also observe the widget body for size changes
  if (widgetBody) {
    resizeObserver.observe(widgetBody);
  }

  // Observe the grid stack item content
  if (gridStackItemContent) {
    resizeObserver.observe(gridStackItemContent);
  }

  // Observe the widget container
  if (widgetContainer) {
    resizeObserver.observe(widgetContainer);
  }

  // Add legend
  const legend = chart.children.push(
    am5.Legend.new(root, {
      centerX: am5.p50,
      x: am5.p50,
      layout: root.horizontalLayout,
      marginTop: 15,
      useDefaultMarker: true,
    })
  );

  // Customize legend markers
  legend.markers.template.setAll({
    width: 12,
    height: 12,
    cornerRadius: 6,
  });

  // Customize legend labels
  legend.labels.template.setAll({
    fontSize: 12,
    fontWeight: "400",
    fill: am5.color(0x000000),
  });

  // Create axes
  const xAxis = chart.xAxes.push(
    am5xy.CategoryAxis.new(root, {
      categoryField: "date",
      renderer: am5xy.AxisRendererX.new(root, {
        cellStartLocation: 0.1,
        cellEndLocation: 0.9,
        minGridDistance: 30,
      }),
      tooltip: am5.Tooltip.new(root, {}),
    })
  );

  // Style X-axis labels
  xAxis.get("renderer").labels.template.setAll({
    fontSize: 11,
    fill: am5.color(0x666666),
  });

  const yAxis = chart.yAxes.push(
    am5xy.ValueAxis.new(root, {
      renderer: am5xy.AxisRendererY.new(root, {
        minGridDistance: 30,
      }),
      numberFormat: "#,###",
    })
  );

  // Style Y-axis labels
  yAxis.get("renderer").labels.template.setAll({
    fontSize: 11,
    fill: am5.color(0x666666),
  });

  // Style Y-axis grid
  yAxis.get("renderer").grid.template.setAll({
    stroke: am5.color(0xdddddd),
    strokeWidth: 1,
    strokeDasharray: [2, 2],
  });

  // Set data
  const data = [
    {
      date: "Jan",
      first: 1250,
      second: 1850,
      third: 950,
    },
    {
      date: "Feb",
      first: 1400,
      second: 1650,
      third: 1100,
    },
    {
      date: "Mar",
      first: 1800,
      second: 2100,
      third: 1350,
    },
    {
      date: "Apr",
      first: 2200,
      second: 1900,
      third: 1500,
    },
    {
      date: "May",
      first: 2500,
      second: 2300,
      third: 1800,
    },
    {
      date: "Jun",
      first: 2300,
      second: 2100,
      third: 1600,
    },
  ];

  xAxis.data.setAll(data);

  // Create series
  function makeSeries(name, fieldName, index) {
    const series = chart.series.push(
      am5xy.ColumnSeries.new(root, {
        name: name,
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: fieldName,
        categoryXField: "date",
        stacked: true,
        tooltip: am5.Tooltip.new(root, {
          labelText: "{name}: ${valueY}",
        }),
      })
    );

    // Get brand colors
    const brandColors = window.chartConfig?.brandColors || [
      "#00b19c",
      "#3bcd3f",
      "#007365",
      "#8dbac4",
      "#02104f",
    ];

    // Set series colors and properties
    series.columns.template.setAll({
      tooltipText: "{name}, {categoryX}: {valueY}",
      width: am5.percent(90),
      tooltipY: am5.percent(10),
      strokeOpacity: 0,
      fillOpacity: 0.8,
      fill: am5.color(brandColors[index % brandColors.length]),
    });

    // Add hover state
    series.columns.template.states.create("hover", {
      fillOpacity: 1,
    });

    series.data.setAll(data);
    series.appear();

    return series;
  }

  // Add series
  makeSeries("First", "first", 0);
  makeSeries("Second", "second", 1);
  makeSeries("Third", "third", 2);

  // Make stuff animate on load
  chart.appear(1000, 100);

  return chart;
};

// Function to initialize settings panel for the stacked column chart
window.initStackChartSettings = function (chartId) {
  console.log("Initializing settings for stacked column chart:", chartId);
  // Settings panel logic can be implemented here in the future.
};

// Also define initStackedColumnChartSettings for consistency
window.initStackedColumnChartSettings = function (chartId) {
  console.log("Initializing settings for stacked column chart:", chartId);

  // For now, just log that this function was called
  // In a future update, we'll implement the full settings panel
};

// Function to update data table columns based on number of series
function updateDataTableColumns(numSeries) {
  const table = document.getElementById("stackedColumnChartDataTable");
  if (!table) return;

  const thead = table.querySelector("thead tr");
  const tbody = document.getElementById("stackedColumnChartDataBody");
  if (!thead || !tbody) return;

  // Default series names and fields
  const seriesConfig = [
    { name: "Electronics", field: "electronics" },
    { name: "Apparel", field: "apparel" },
    { name: "Home & Garden", field: "homeGarden" },
    { name: "Sports", field: "sports" },
    { name: "Books", field: "books" },
    { name: "Toys", field: "toys" },
    { name: "Beauty", field: "beauty" },
    { name: "Food", field: "food" },
    { name: "Health", field: "health" },
    { name: "Auto", field: "auto" },
  ];

  // Update series list in the configuration section
  const seriesList = document.getElementById(
    "stackedColumnChartSettings-seriesList"
  );
  if (seriesList) {
    seriesList.innerHTML = "";
    for (let i = 0; i < numSeries; i++) {
      const series = seriesConfig[i];
      const seriesItem = document.createElement("div");
      seriesItem.className = "series-item d-flex align-items-center mb-2";
      seriesItem.innerHTML = `
        <div class="color-indicator" style="width: 16px; height: 16px; border-radius: 4px; margin-right: 8px; background-color: ${
          window.chartConfig.brandColors[
            i % window.chartConfig.brandColors.length
          ]
        }"></div>
        <span class="series-name">${series.name}</span>
      `;
      seriesList.appendChild(seriesItem);
    }
  }

  // Clear existing header cells except Date and Actions
  const headerCells = thead.querySelectorAll("th");
  for (let i = headerCells.length - 2; i > 0; i--) {
    headerCells[i].remove();
  }

  // Add new header cells for each series
  for (let i = 0; i < numSeries; i++) {
    const th = document.createElement("th");
    th.textContent = seriesConfig[i].name;
    // Insert before the Actions column
    thead.insertBefore(th, headerCells[headerCells.length - 1]);
  }

  // Update existing rows
  const rows = tbody.querySelectorAll("tr");
  rows.forEach((row) => {
    const date = row.querySelector("td:first-child").textContent;
    const cells = row.querySelectorAll("td");

    // Remove all value cells
    for (let i = cells.length - 2; i > 0; i--) {
      cells[i].remove();
    }

    // Add new value cells
    for (let i = 0; i < numSeries; i++) {
      const td = document.createElement("td");
      const input = document.createElement("input");
      input.type = "number";
      input.className = "form-control form-control-sm";
      input.value = "0";
      input.dataset.series = seriesConfig[i].field;
      input.onchange = () =>
        updateStackedColumnChartData(date, seriesConfig[i].field, input.value);
      td.appendChild(input);
      // Insert before the Actions cell
      row.insertBefore(td, cells[cells.length - 1]);
    }
  });

  return seriesConfig.slice(0, numSeries);
}

// Function to handle series count change
function handleSeriesCountChange(value) {
  const count = parseInt(value);
  const chartId = document.getElementById("stackedColumnChartSettings").dataset
    .currentChart;
  const chart = document.getElementById(chartId)?.chart;

  if (!chart) {
    console.error("Chart not found");
    return;
  }

  // Update value display
  document.querySelector('[for="seriesCount"] .value-display').textContent =
    count;

  // Get current data
  const currentData = chart.series.getIndex(0)?.data?.values || [];

  // Remove existing series
  while (chart.series.length > 0) {
    chart.series.removeIndex(0);
  }

  // Add new series
  for (let i = 0; i < count; i++) {
    const seriesConfig = window.stackedColumnSeriesDefaults[i];
    const series = chart.series.push(
      am5xy.ColumnSeries.new(chart.root, {
        name: seriesConfig.name,
        xAxis: chart.xAxes.getIndex(0),
        yAxis: chart.yAxes.getIndex(0),
        valueYField: seriesConfig.field,
        categoryXField: "date",
        stacked: true,
        tooltip: am5.Tooltip.new(chart.root, {
          labelText: "{name}: ${valueY}",
        }),
      })
    );

    // Set series colors and properties
    series.columns.template.setAll({
      width: am5.percent(80),
      tooltipY: 0,
      strokeOpacity: 0,
      fillOpacity: 0.8,
      fill: am5.color(
        window.chartConfig.brandColors[
          i % window.chartConfig.brandColors.length
        ]
      ),
    });

    // Add hover state
    series.columns.template.states.create("hover", {
      fillOpacity: 1,
    });

    // Set data
    series.data.setAll(currentData);
  }

  // Update legend
  if (chart.legend) {
    chart.legend.data.setAll(chart.series.values);
  }

  // Update series configuration UI
  updateSeriesConfigUI(count, chart);

  // Reinitialize Handsontable with new series
  initStackedColumnHandsontable(chartId);
}

// Function to update series configuration UI
function updateSeriesConfigUI(seriesCount, chart) {
  const container = document.getElementById("seriesConfigContainer");
  if (!container) return;

  container.innerHTML = "";

  // Create series configuration items
  for (let i = 0; i < seriesCount; i++) {
    const series = chart.series.getIndex(i);
    if (!series) continue;

    const seriesItem = document.createElement("div");
    seriesItem.className = "series-item d-flex align-items-center gap-3 mb-3";
    seriesItem.innerHTML = `
      <div class="color-picker" style="width: 30px;">
        <input type="color"
               class="form-control form-control-color p-0"
               value="${series.get("fill").toString()}"
                 onchange="updateSeriesColor(${i}, this.value)"
                 aria-label="Series color"/>
        </div>
      <div class="flex-grow-1">
        <input type="text"
               class="form-control"
               value="${series.get("name")}"
               onchange="updateSeriesName(${i}, this.value)"
               aria-label="Series name"/>
      </div>
        <div class="btn-group btn-group-sm">
        <button class="btn btn-outline-secondary"
                onclick="moveSeriesUp(${i})"
                ${i === 0 ? "disabled" : ""}>
            <i class="las la-arrow-up"></i>
          </button>
        <button class="btn btn-outline-secondary"
                onclick="moveSeriesDown(${i})"
                ${i === seriesCount - 1 ? "disabled" : ""}>
            <i class="las la-arrow-down"></i>
          </button>
      </div>
    `;
    container.appendChild(seriesItem);
  }
}

// Function to update series color
function updateSeriesColor(index, color) {
  const chart = document.getElementById("chart")?.chart;
  if (!chart) return;

  const series = chart.series.getIndex(index);
  if (!series) return;

  series.set("fill", am5.color(color));
  if (chart.legend) {
    chart.legend.data.setAll(chart.series.values);
  }
}

// Function to update series name
function updateSeriesName(index, name) {
  const chart = document.getElementById("chart")?.chart;
  if (!chart) return;

  const series = chart.series.getIndex(index);
  if (!series) return;

  series.set("name", name);
  if (chart.legend) {
    chart.legend.data.setAll(chart.series.values);
  }
}

// Function to move series up
function moveSeriesUp(index) {
  if (index <= 0) return;
  const chart = document.getElementById("chart")?.chart;
  if (!chart) return;

  const series1 = chart.series.getIndex(index);
  const series2 = chart.series.getIndex(index - 1);
  if (!series1 || !series2) return;

  // Swap series properties
  const tempName = series1.get("name");
  const tempColor = series1.get("fill");
  const tempData = series1.data.values;

  series1.set("name", series2.get("name"));
  series1.set("fill", series2.get("fill"));
  series1.data.setAll(series2.data.values);

  series2.set("name", tempName);
  series2.set("fill", tempColor);
  series2.data.setAll(tempData);

  // Update legend
  if (chart.legend) {
    chart.legend.data.setAll(chart.series.values);
  }

  // Update UI
  updateSeriesConfigUI(chart.series.length, chart);
}

// Function to move series down
function moveSeriesDown(index) {
  const chart = document.getElementById("chart")?.chart;
  if (!chart) return;

  if (index >= chart.series.length - 1) return;
  moveSeriesUp(index + 1);
}

// Function to update chart series
function updateChartSeries(chartId, count) {
  const chart = document.getElementById(chartId)?.chart;
  if (!chart) return;

  // Store current data
  const currentData = chart.series.getIndex(0)?.data?.values || [];

  // Remove existing series
  while (chart.series.length > 0) {
    chart.series.removeIndex(0);
  }

  // Add new series
  for (let i = 0; i < count; i++) {
    const series = chart.series.push(
      am5xy.ColumnSeries.new(chart.root, {
        name: `Series ${i + 1}`,
        xAxis: chart.xAxes.getIndex(0),
        yAxis: chart.yAxes.getIndex(0),
        valueYField: `series${i}`,
        categoryXField: "date",
        stacked: true,
        tooltip: am5.Tooltip.new(chart.root, {
          labelText: "{name}: ${valueY}",
        }),
      })
    );

    // Set series colors
    series.columns.template.setAll({
      width: am5.percent(80),
      tooltipY: 0,
      strokeOpacity: 0,
      fillOpacity: 0.8,
      fill: am5.color(
        window.chartConfig.brandColors[
          i % window.chartConfig.brandColors.length
        ]
      ),
    });

    // Add hover state
    series.columns.template.states.create("hover", {
      fillOpacity: 1,
    });

    // Set data
    series.data.setAll(currentData);
  }

  // Update legend
  if (chart.legend) {
    chart.legend.data.setAll(chart.series.values);
  }

  // Initialize data table
  initializeDataTable(chartId);
}

// Function to initialize data table
function initializeDataTable(chartId) {
  const chart = document.getElementById(chartId)?.chart;
  if (!chart) return;

  const container = document.getElementById("dataTable");
  if (!container) return;

  // Get current data
  const data = chart.series.getIndex(0)?.data?.values || [];

  // Create column headers
  const columns = [{ data: "date", title: "Date", type: "text" }];

  chart.series.each((series, index) => {
    columns.push({
      data: `series${index}`,
      title: series.get("name"),
      type: "numeric",
      numericFormat: {
        pattern: "0,0.00",
        culture: "en-US",
      },
    });
  });

  // Initialize Handsontable
  if (window.dataTable) {
    window.dataTable.destroy();
  }

  window.dataTable = new Handsontable(container, {
    data: data,
    columns: columns,
    colHeaders: true,
    rowHeaders: true,
    height: "100%",
    licenseKey: "non-commercial-and-evaluation",
    contextMenu: true,
    manualColumnResize: true,
    manualRowResize: true,
    stretchH: "all",
    autoColumnSize: true,
    wordWrap: false,
    outsideClickDeselects: false,
    className: "custom-handsontable",
    dropdownMenu: [
      "filter_by_condition",
      "filter_by_value",
      "filter_action_bar",
    ],
    filters: true,
    afterChange: (changes, source) => {
      if (!changes || source === "loadData") return;

      changes.forEach(([row, prop, oldValue, newValue]) => {
        if (prop === 0) return; // Skip date column changes

        const date = this.getDataAtCell(row, 0);
        const seriesField = seriesFields[prop - 1];
        const value = parseFloat(newValue) || 0;

        // Update chart data
        const dataPoint = chartData.find((d) => d.date === date);
        if (dataPoint) {
          dataPoint[seriesField] = value;
        }
      });

      // Update all series with new data
      chart.series.each((series) => {
        series.data.setAll(chartData);
      });
    },
    afterCreateRow: (index, amount, source) => {
      if (source !== "loadData") {
        // Get the last row's date and increment it
        const lastDate = this.getDataAtCell(index - 1, 0);
        const [year, month] = lastDate.split("-").map(Number);
        const newDate =
          month === 12
            ? `${year + 1}-01`
            : `${year}-${String(month + 1).padStart(2, "0")}`;

        // Set the new row's date
        this.setDataAtCell(index, 0, newDate);

        // Initialize other cells with 0
        for (let i = 1; i < this.countCols(); i++) {
          this.setDataAtCell(index, i, 0);
        }

        // Update chart data
        const newDataPoint = { date: newDate };
        seriesFields.forEach((field) => {
          newDataPoint[field] = 0;
        });
        chartData.splice(index, 0, newDataPoint);

        // Update chart
        chart.series.each((series) => {
          series.data.setAll(chartData);
        });
      }
    },
  });

  // Add custom buttons
  addCustomButtons(container, window.dataTable);

  return window.dataTable;
}

// Function to add custom buttons to Handsontable
function addCustomButtons(container, hot) {
  let buttonContainer = container.querySelector(".handsontable-buttons");
  if (!buttonContainer) {
    buttonContainer = document.createElement("div");
    buttonContainer.className = "handsontable-buttons d-flex gap-2 mb-3";
    container.parentElement.insertBefore(buttonContainer, container);

    // Add Row Button
    const addRowBtn = document.createElement("button");
    addRowBtn.className = "btn btn-sm btn-primary";
    addRowBtn.innerHTML = '<i class="las la-plus"></i> Add Row';
    addRowBtn.onclick = () => hot.alter("insert_row");
    buttonContainer.appendChild(addRowBtn);

    // Import/Export Buttons
    const importBtn = document.createElement("button");
    importBtn.className = "btn btn-sm btn-outline-secondary";
    importBtn.innerHTML = '<i class="las la-file-import"></i> Import';
    importBtn.onclick = () => importData();
    buttonContainer.appendChild(importBtn);

    const exportBtn = document.createElement("button");
    exportBtn.className = "btn btn-sm btn-outline-secondary";
    exportBtn.innerHTML = '<i class="las la-file-export"></i> Export';
    exportBtn.onclick = () => exportData();
    buttonContainer.appendChild(exportBtn);
  }
}

// Function to add custom styles for Handsontable
function addHandsontableStyles() {
  // Add styles if they don't exist
  if (!document.getElementById("stackedColumnChartStyles")) {
    const style = document.createElement("style");
    style.id = "stackedColumnChartStyles";
    style.textContent = `
      .handsontable-container {
        margin: 1rem 0;
        max-height: 400px;
        overflow: auto;
      }

      .handsontable-buttons {
        margin-bottom: 1rem;
      }

      .toast {
        z-index: 9999;
        min-width: 250px;
        max-width: 400px;
        background: white;
        border-radius: 4px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      .toast-success {
        border-left: 4px solid #28a745;
      }

      .toast-error {
        border-left: 4px solid #dc3545;
      }

      .toast-info {
        border-left: 4px solid #17a2b8;
      }

      .data-loading-spinner {
        z-index: 9998;
        background: rgba(255, 255, 255, 0.8);
        padding: 2rem;
        border-radius: 8px;
      }

      .filter-container {
        margin-bottom: 1rem;
      }

      .series-controls {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        margin-bottom: 0.5rem;
      }

      .series-name-input {
        flex: 1;
      }

      .series-color-input {
        width: 50px;
      }
    `;
    document.head.appendChild(style);
  }
}

// Function to import data
function importData() {
  const input = document.createElement("input");
  input.type = "file";
  input.accept = ".csv,.json";

  input.onchange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      try {
        let data;
        if (file.name.endsWith(".csv")) {
          data = parseCSV(event.target.result);
        } else {
          data = JSON.parse(event.target.result);
        }

        if (Array.isArray(data) && data.length > 0) {
          window.dataTable.loadData(data);
        }
      } catch (error) {
        console.error("Error importing data:", error);
        alert("Error importing data. Please check the file format.");
      }
    };

    reader.readAsText(file);
  };

  input.click();
}

// Function to export data
function exportData() {
  if (!window.dataTable) return;

  const data = window.dataTable.getData();
  const headers = window.dataTable.getColHeader();

  // Create CSV content
  const csvContent = [
    headers.join(","),
    ...data.map((row) => row.map((cell) => `"${cell}"`).join(",")),
  ].join("\n");

  // Create download link
  const blob = new Blob([csvContent], { type: "text/csv" });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = "chart-data.csv";
  a.click();
  window.URL.revokeObjectURL(url);
}

// Function to parse CSV data
function parseCSV(csv) {
  const lines = csv.split("\n");
  const headers = lines[0].split(",").map((h) => h.trim());
  const data = [];

  for (let i = 1; i < lines.length; i++) {
    if (!lines[i].trim()) continue;

    const values = lines[i].split(",").map((v) => v.trim());
    if (values.length === headers.length) {
      const row = [];
      headers.forEach((header, index) => {
        const value = values[index].replace(/^"(.*)"$/, "$1"); // Remove quotes
        row.push(index === 0 ? value : parseFloat(value) || 0);
      });
      data.push(row);
    }
  }

  return data;
}

// Update chart data from Handsontable
function updateChartFromHandsontable(chartId) {
  const chart = document.getElementById(chartId)?.chart;
  if (!chart || !window.dataTable) return;

  const hotData = window.dataTable.getData();
  const headers = window.dataTable.getColHeader();

  // Transform Handsontable data to chart format
  const chartData = hotData.map((row) => {
    const dataPoint = { date: row[0] };
    chart.series.each((series, index) => {
      dataPoint[series.get("valueYField")] = parseFloat(row[index + 1]) || 0;
    });
    return dataPoint;
  });

  // Update all series with new data
  chart.series.each((series) => {
    series.data.setAll(chartData);
  });
}

// Function to filter data table
function filterDataTable(value) {
  if (!window.dataTable) return;

  const search = value.toLowerCase();
  window.dataTable.search(search);
}

function initDataManagementEvents(chartId) {
  // Series management events
  document.querySelectorAll(".series-up-btn").forEach((btn, index) => {
    btn.onclick = () => moveSeriesUp(index);
  });

  document.querySelectorAll(".series-down-btn").forEach((btn, index) => {
    btn.onclick = () => moveSeriesDown(index);
  });

  document.querySelectorAll(".series-name-input").forEach((input, index) => {
    input.onchange = (e) => updateSeriesName(index, e.target.value);
  });

  document.querySelectorAll(".series-color-input").forEach((input, index) => {
    input.onchange = (e) => updateSeriesColor(index, e.target.value);
  });

  // Data table filter
  const filterInput = document.querySelector("#dataTableFilter");
  if (filterInput) {
    filterInput.oninput = (e) => filterDataTable(e.target.value);
  }

  // Export buttons
  const csvExportBtn = document.querySelector("#exportCSV");
  if (csvExportBtn) {
    csvExportBtn.onclick = () => exportToCSV(chartId);
  }

  const jsonExportBtn = document.querySelector("#exportJSON");
  if (jsonExportBtn) {
    jsonExportBtn.onclick = () => exportToJSON(chartId);
  }

  // Import handlers
  const fileInput = document.querySelector("#importFile");
  if (fileInput) {
    fileInput.onchange = (e) => {
      const file = e.target.files[0];
      if (!file) return;

      const fileType = file.name.split(".").pop().toLowerCase();
      if (fileType === "csv") {
        importFromCSV(chartId, file);
      } else if (fileType === "json") {
        importFromJSON(chartId, file);
      } else {
        console.error("Unsupported file type");
      }

      // Reset file input
      fileInput.value = "";
    };
  }
}

// Export functions
function exportToCSV(chartId) {
  const chart = document.getElementById(chartId)?.chart;
  if (!chart || !window.dataTable) return;

  const headers = window.dataTable.getColHeader();
  const data = window.dataTable.getData();

  // Create CSV content
  const csvContent = [
    headers.join(","),
    ...data.map((row) => row.map((cell) => `"${cell}"`).join(",")),
  ].join("\n");

  // Create and trigger download
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = "chart_data.csv";
  link.click();
}

function exportToJSON(chartId) {
  const chart = document.getElementById(chartId)?.chart;
  if (!chart || !window.dataTable) return;

  const headers = window.dataTable.getColHeader();
  const data = window.dataTable.getData();

  // Transform to JSON format
  const jsonData = data.map((row) => {
    const obj = {};
    headers.forEach((header, index) => {
      obj[header] = row[index];
    });
    return obj;
  });

  // Create and trigger download
  const blob = new Blob([JSON.stringify(jsonData, null, 2)], {
    type: "application/json",
  });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = "chart_data.json";
  link.click();
}

// Function to import chart data from file
function importStackedColumnChartData(chartId, file) {
  if (!file) {
    console.error("No file provided");
    return;
  }

  showLoadingSpinner(true);
  const reader = new FileReader();

  reader.onload = (e) => {
    try {
      let data;
      const fileType = file.name.split(".").pop().toLowerCase();

      // Parse file content based on type
      if (fileType === "csv") {
        const text = e.target.result;
        const rows = text
          .split("\n")
          .map((row) =>
            row.split(",").map((cell) => cell.replace(/^"(.*)"$/, "$1").trim())
          );

        const headers = rows[0];
        data = rows.slice(1).filter((row) => row.length === headers.length);

        // Transform CSV data to chart format
        data = data.map((row) => {
          const obj = { date: row[0] };
          headers.slice(1).forEach((header, index) => {
            obj[window.stackedColumnSeriesDefaults[index].field] =
              parseFloat(row[index + 1]) || 0;
          });
          return obj;
        });
      } else if (fileType === "json") {
        data = JSON.parse(e.target.result);

        // Validate JSON data structure
        if (!Array.isArray(data)) {
          throw new Error("Invalid JSON format: expected an array");
        }

        // Ensure data has required fields
        data = data.map((item) => {
          if (!item.date) {
            throw new Error("Invalid data format: missing date field");
          }
          return item;
        });
      } else {
        throw new Error("Unsupported file format");
      }

      // Get chart instance
      const chart = document.getElementById(chartId)?.chart;
      if (!chart) {
        throw new Error("Chart not found");
      }

      // Update each series with new data
      chart.series.each((series) => {
        series.data.setAll(data);
      });

      // Update data table if it exists
      if (window.dataTable) {
        window.updateHandsontableData(chart, data);
      }

      showToast("Data imported successfully", "success");
    } catch (error) {
      console.error("Import error:", error);
      showToast(`Import failed: ${error.message}`, "error");
    } finally {
      showLoadingSpinner(false);
    }
  };

  reader.onerror = () => {
    console.error("File reading error");
    showToast("Failed to read file", "error");
    showLoadingSpinner(false);
  };

  // Read file content
  reader.readAsText(file);
}

// Function to export chart data
function exportStackedColumnChartData(chartId, format = "csv") {
  const chart = document.getElementById(chartId)?.chart;
  if (!chart) {
    console.error("Chart not found");
    return;
  }

  try {
    // Get data from first series
    const data = chart.series.getIndex(0)?.data?.values || [];

    if (format === "csv") {
      // Create CSV content
      const headers = [
        "Date",
        ...chart.series.values.map((s) => s.get("name")),
      ];
      const csvContent = [
        headers.join(","),
        ...data.map((row) => {
          const values = [row.date];
          chart.series.each((series) => {
            values.push(row[series.get("valueYField")] || "0");
          });
          return values.join(",");
        }),
      ].join("\n");

      // Download CSV file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `chart_data_${new Date().toISOString().slice(0, 10)}.csv`;
      link.click();
    } else if (format === "json") {
      // Create JSON content
      const jsonData = data.map((row) => {
        const obj = { date: row.date };
        chart.series.each((series) => {
          obj[series.get("valueYField")] = row[series.get("valueYField")] || 0;
        });
        return obj;
      });

      // Download JSON file
      const blob = new Blob([JSON.stringify(jsonData, null, 2)], {
        type: "application/json",
      });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `chart_data_${new Date()
        .toISOString()
        .slice(0, 10)}.json`;
      link.click();
    } else {
      throw new Error("Unsupported export format");
    }

    showToast("Data exported successfully", "success");
  } catch (error) {
    console.error("Export error:", error);
    showToast(`Export failed: ${error.message}`, "error");
  }
}

// Function to edit series configuration
function editSeries(chartId, seriesIndex) {
  const chart = document.getElementById(chartId)?.chart;
  if (!chart) return;

  const series = chart.series.getIndex(seriesIndex);
  if (!series) return;

  // Get current series configuration
  const currentConfig = {
    name: series.get("name"),
    color: series.columns.template.get("fill").toString(),
    visible: series.get("visible"),
  };

  // Update series configuration UI
  const nameInput = document.getElementById(`series-name-${seriesIndex}`);
  const colorInput = document.getElementById(`series-color-${seriesIndex}`);
  const visibilityToggle = document.getElementById(
    `series-visibility-${seriesIndex}`
  );

  if (nameInput) nameInput.value = currentConfig.name;
  if (colorInput) colorInput.value = currentConfig.color;
  if (visibilityToggle) visibilityToggle.checked = currentConfig.visible;

  // Show series edit modal if it exists
  const editModal = document.getElementById("seriesEditModal");
  if (editModal && bootstrap.Modal) {
    const modal = new bootstrap.Modal(editModal);
    modal.show();
  }
}

// Function to initialize Handsontable for data editing
function initStackedColumnHandsontable(chartId) {
  console.log("Initializing Handsontable for chart:", chartId);

  const container = document.getElementById("dataTable");
  const settingsPanel = document.getElementById("stackedColumnChartSettings");
  const chart = document.getElementById(chartId)?.chart;

  if (!container || !settingsPanel || !chart) {
    console.warn("Required elements not found");
    return;
  }

  // Get current data from chart
  const chartData = chart.series.getIndex(0)?.data?.values || [];
  const seriesFields = [];
  const seriesNames = [];

  // Collect all series fields and names
  chart.series.each((series) => {
    seriesFields.push(series.get("valueYField"));
    seriesNames.push(series.get("name"));
  });

  // Transform chart data for Handsontable
  const hotData = chartData.map((row) => {
    const dataRow = [row.date];
    seriesFields.forEach((field) => {
      dataRow.push(row[field] || 0);
    });
    return dataRow;
  });

  // Create column definitions
  const columns = [
    {
      data: 0,
      title: "Date",
      type: "text",
      className: "htLeft date-cell",
      readOnly: true,
    },
    ...seriesFields.map((field, index) => ({
      data: index + 1,
      title: seriesNames[index],
      type: "numeric",
      numericFormat: {
        pattern: "0,0.00",
        culture: "en-US",
      },
      className: "htRight",
      validator: function (value, callback) {
        callback(value === null || value === "" || !isNaN(parseFloat(value)));
      },
    })),
  ];

  // Destroy existing instance if it exists
  if (window.dataTable) {
    window.dataTable.destroy();
  }

  // Initialize Handsontable with afterChange hook
  window.dataTable = new Handsontable(container, {
    data: hotData,
    columns: columns,
    colHeaders: true,
    rowHeaders: true,
    height: 300,
    licenseKey: "non-commercial-and-evaluation",
    contextMenu: true,
    manualColumnResize: true,
    manualRowResize: true,
    stretchH: "all",
    autoColumnSize: true,
    wordWrap: false,
    outsideClickDeselects: false,
    className: "custom-handsontable",
    dropdownMenu: [
      "filter_by_condition",
      "filter_by_value",
      "filter_action_bar",
    ],
    filters: true,
    afterChange: function (changes, source) {
      if (!changes || source === "loadData") return;

      changes.forEach(([row, prop, oldValue, newValue]) => {
        if (prop === 0) return; // Skip date column changes

        const date = this.getDataAtCell(row, 0);
        const seriesField = seriesFields[prop - 1];
        const value = parseFloat(newValue) || 0;

        // Update chart data
        const dataPoint = chartData.find((d) => d.date === date);
        if (dataPoint) {
          dataPoint[seriesField] = value;
        }
      });

      // Update all series with new data
      chart.series.each((series) => {
        series.data.setAll(chartData);
      });
    },
  });

  // Add custom buttons
  addCustomButtons(container, window.dataTable);

  // Initialize series configuration
  updateSeriesConfigUI(seriesFields.length, chart);

  return window.dataTable;
}

// Function to update Handsontable data
function updateHandsontableData(chart, data) {
  if (!window.dataTable || !chart) return;

  // Transform data for Handsontable
  const hotData = data.map((row) => {
    const dataRow = [row.date];
    chart.series.each((series) => {
      dataRow.push(row[series.get("valueYField")] || 0);
    });
    return dataRow;
  });

  // Update Handsontable
  window.dataTable.loadData(hotData);
}

// Function to export chart as image
function exportChartToImage(chartId) {
  const chart = document.getElementById(chartId)?.chart;
  if (!chart) {
    console.error("Chart not found");
    return;
  }

  try {
    // Export chart as PNG using amCharts export functionality
    chart.exporting.export("png", {
      quality: 1,
      backgroundColor: "#ffffff",
    });
    showToast("Chart exported as image successfully", "success");
  } catch (error) {
    console.error("Export error:", error);
    showToast(`Export failed: ${error.message}`, "error");
  }
}

// Function to export chart data as Excel
function exportChartToExcel(chartId) {
  const chart = document.getElementById(chartId)?.chart;
  if (!chart) {
    console.error("Chart not found");
    return;
  }

  try {
    // Export chart data as XLSX using amCharts export functionality
    chart.exporting.export("xlsx", {
      includeData: true,
      addColumnNames: true,
    });
    showToast("Chart exported as Excel successfully", "success");
  } catch (error) {
    console.error("Export error:", error);
    showToast(`Export failed: ${error.message}`, "error");
  }
}

// Function to export chart as PDF
function exportChartToPDF(chartId) {
  const chart = document.getElementById(chartId)?.chart;
  if (!chart) {
    console.error("Chart not found");
    return;
  }

  try {
    // Export chart as PDF using amCharts export functionality
    chart.exporting.export("pdf", {
      quality: 1,
      backgroundColor: "#ffffff",
      pageSize: "A4",
      pageOrientation: "landscape",
      addTitle: true,
    });
    showToast("Chart exported as PDF successfully", "success");
  } catch (error) {
    console.error("Export error:", error);
    showToast(`Export failed: ${error.message}`, "error");
  }
}

// Function to export chart data as CSV
function exportChartToCSV(chartId) {
  const chart = document.getElementById(chartId)?.chart;
  if (!chart) {
    console.error("Chart not found");
    return;
  }

  try {
    // Get data from first series
    const data = chart.series.getIndex(0)?.data?.values || [];

    // Create CSV content
    const headers = ["Date", ...chart.series.values.map((s) => s.get("name"))];
    const csvContent = [
      headers.join(","),
      ...data.map((row) => {
        const values = [row.date];
        chart.series.each((series) => {
          values.push(row[series.get("valueYField")] || "0");
        });
        return values.join(",");
      }),
    ].join("\n");

    // Create and trigger download
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = `chart_data_${new Date().toISOString().slice(0, 10)}.csv`;
    link.click();

    showToast("Chart exported as CSV successfully", "success");
  } catch (error) {
    console.error("Export error:", error);
    showToast(`Export failed: ${error.message}`, "error");
  }
}

// Function to remove widget and cleanup chart
function removeWidget(element) {
  const widget = element.closest(".grid-stack-item");
  const chartContainer = widget.querySelector(".chart-container");

  if (chartContainer && chartContainer.root) {
    chartContainer.root.dispose();
  }

  grid.removeWidget(widget);
}

// Export functions
window.addStackedColumnChartWidget = addStackedColumnChartWidget;
window.initStackedColumnChart = initStackedColumnChart;
window.initDataManagementEvents = initDataManagementEvents;
window.initStackedColumnHandsontable = initStackedColumnHandsontable;
window.updateHandsontableData = updateHandsontableData;
window.exportChartToImage = exportChartToImage;
window.exportChartToExcel = exportChartToExcel;
window.exportChartToPDF = exportChartToPDF;
window.exportChartToCSV = exportChartToCSV;
window.updateSeriesConfigUI = updateSeriesConfigUI;
window.updateSeriesColor = updateSeriesColor;
window.updateSeriesName = updateSeriesName;
window.moveSeriesUp = moveSeriesUp;
window.moveSeriesDown = moveSeriesDown;
