// Add a dropdown filter widget
function addDropdownFilterWidget() {
  console.log("Adding dropdown filter widget");
  const widgetId = "dropdown-filter-" + Date.now();
  const settingsId = "settings-" + widgetId;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 4,
    content: `
      <div class="dropdown-filter-widget p-2">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>Filter Widget
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#${settingsId}"
                    aria-controls="${settingsId}">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="filter-container mb-3">
          <div class="filter-title mb-2">Filter By:</div>
          <div class="filter-controls">
            <div class="row">
              <div class="col-md-6 mb-2">
                <select class="form-select form-select-sm" id="${widgetId}-filter1">
                  <option value="">Category (All)</option>
                  <option value="category1">Category 1</option>
                  <option value="category2">Category 2</option>
                  <option value="category3">Category 3</option>
                </select>
              </div>
              <div class="col-md-6 mb-2">
                <select class="form-select form-select-sm" id="${widgetId}-filter2">
                  <option value="">Region (All)</option>
                  <option value="region1">Region 1</option>
                  <option value="region2">Region 2</option>
                  <option value="region3">Region 3</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <div class="filtered-content-container border p-2" id="${widgetId}-content">
          <div class="alert alert-info m-0">
            Select filters above to filter dashboard content.
          </div>
        </div>
        <div class="filter-footer mt-2 d-flex justify-content-end">
          <button class="btn btn-sm btn-outline-secondary" id="${widgetId}-reset">
            <i class="las la-undo-alt"></i> Reset Filters
          </button>
        </div>
      </div>
    `,
  });

  // Create settings panel in the offcanvas container
  const offcanvasContainer = document.getElementById("offcanvasContainer");
  const settingsPanel = document.createElement("div");
  settingsPanel.className = "offcanvas offcanvas-end";
  settingsPanel.id = settingsId;
  settingsPanel.setAttribute("tabindex", "-1");
  settingsPanel.setAttribute("aria-labelledby", `${settingsId}-label`);

  settingsPanel.innerHTML = `
    <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="${settingsId}-label">Filter Widget Settings</h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- Widget Title -->
      <div class="mb-3">
        <label for="${settingsId}-title" class="form-label">Widget Title</label>
        <input type="text" class="form-control" id="${settingsId}-title" value="Filter Widget">
      </div>

      <!-- Filter Configuration -->
      <div class="mb-3">
        <label class="form-label">Number of Filters</label>
        <div class="d-flex align-items-center mb-2">
          <input type="number" class="form-control" id="${settingsId}-filterCount" min="1" max="4" value="2" style="width: 70px;">
          <label class="ms-2 form-label mb-0" for="${settingsId}-filterCount">Filters</label>
        </div>
      </div>

      <!-- Filter 1 -->
      <div class="mb-3 filter-setting" id="${settingsId}-filter1-container">
        <h6 class="mb-2">Filter 1</h6>
        <div class="mb-2">
          <label for="${settingsId}-filter1-label" class="form-label">Label</label>
          <input type="text" class="form-control" id="${settingsId}-filter1-label" value="Category">
        </div>
        <div class="mb-2">
          <label for="${settingsId}-filter1-placeholder" class="form-label">Placeholder</label>
          <input type="text" class="form-control" id="${settingsId}-filter1-placeholder" value="Category (All)">
        </div>
        <div class="mb-2">
          <label class="form-label">Options (one per line)</label>
          <textarea class="form-control" id="${settingsId}-filter1-options" rows="3">Category 1
Category 2
Category 3</textarea>
        </div>
      </div>

      <!-- Filter 2 -->
      <div class="mb-3 filter-setting" id="${settingsId}-filter2-container">
        <h6 class="mb-2">Filter 2</h6>
        <div class="mb-2">
          <label for="${settingsId}-filter2-label" class="form-label">Label</label>
          <input type="text" class="form-control" id="${settingsId}-filter2-label" value="Region">
        </div>
        <div class="mb-2">
          <label for="${settingsId}-filter2-placeholder" class="form-label">Placeholder</label>
          <input type="text" class="form-control" id="${settingsId}-filter2-placeholder" value="Region (All)">
        </div>
        <div class="mb-2">
          <label class="form-label">Options (one per line)</label>
          <textarea class="form-control" id="${settingsId}-filter2-options" rows="3">Region 1
Region 2
Region 3</textarea>
        </div>
      </div>

      <!-- Layout Options -->
      <div class="mb-3">
        <label class="form-label">Layout</label>
        <select class="form-select" id="${settingsId}-layout">
          <option value="row" selected>Row (Side by Side)</option>
          <option value="column">Column (Stacked)</option>
        </select>
      </div>

      <!-- Apply Button -->
      <button class="btn btn-primary w-100" onclick="applyDropdownFilterSettings('${widgetId}', '${settingsId}')">
        Apply Changes
      </button>
    </div>
  `;
  offcanvasContainer.appendChild(settingsPanel);

  // Add event listeners for filter change
  setTimeout(() => {
    initializeFilterWidget(widgetId);
  }, 100);

  return widget;
}

// Initialize filter change handlers
function initializeFilterWidget(widgetId) {
  const filter1 = document.getElementById(`${widgetId}-filter1`);
  const filter2 = document.getElementById(`${widgetId}-filter2`);
  const resetBtn = document.getElementById(`${widgetId}-reset`);
  
  if (filter1) {
    filter1.addEventListener('change', () => updateFilteredContent(widgetId));
  }
  
  if (filter2) {
    filter2.addEventListener('change', () => updateFilteredContent(widgetId));
  }
  
  if (resetBtn) {
    resetBtn.addEventListener('click', () => resetFilters(widgetId));
  }
}

// Update filtered content based on selected filters
function updateFilteredContent(widgetId) {
  const filter1 = document.getElementById(`${widgetId}-filter1`);
  const filter2 = document.getElementById(`${widgetId}-filter2`);
  const contentContainer = document.getElementById(`${widgetId}-content`);
  
  if (!contentContainer) return;
  
  // Get selected values
  const filter1Value = filter1 ? filter1.value : '';
  const filter2Value = filter2 ? filter2.value : '';
  
  // For demo purposes, generate a message showing the active filters
  // In a real implementation, this would update other widgets on the dashboard
  let message = '<div class="alert alert-success m-0">';
  
  if (!filter1Value && !filter2Value) {
    message = '<div class="alert alert-info m-0">No filters applied. Showing all content.</div>';
  } else {
    message += '<strong>Filters applied:</strong><ul class="mb-0 mt-2">';
    
    if (filter1Value) {
      message += `<li>${filter1.options[0].text.split(' ')[0]}: ${filter1Value}</li>`;
    }
    
    if (filter2Value) {
      message += `<li>${filter2.options[0].text.split(' ')[0]}: ${filter2Value}</li>`;
    }
    
    message += '</ul></div>';
    
    // This would trigger the actual filtering of dashboard widgets
    // For demo, we just log to console
    console.log(`Filter applied: Widget ${widgetId} filters updated:`, { 
      filter1: filter1Value, 
      filter2: filter2Value 
    });
  }
  
  contentContainer.innerHTML = message;
}

// Reset all filters
function resetFilters(widgetId) {
  const filter1 = document.getElementById(`${widgetId}-filter1`);
  const filter2 = document.getElementById(`${widgetId}-filter2`);
  
  if (filter1) filter1.value = '';
  if (filter2) filter2.value = '';
  
  updateFilteredContent(widgetId);
}

// Apply settings from the settings panel
function applyDropdownFilterSettings(widgetId, settingsId) {
  // Get widget elements
  const widget = document.querySelector(`.grid-stack-item .dropdown-filter-widget`);
  const widgetHeader = widget.querySelector('.widget-header > div:first-child');
  const filterControls = widget.querySelector('.filter-controls .row');
  
  // Get settings values
  const widgetTitle = document.getElementById(`${settingsId}-title`).value;
  const filterCount = parseInt(document.getElementById(`${settingsId}-filterCount`).value);
  const layout = document.getElementById(`${settingsId}-layout`).value;
  
  // Update widget title
  widgetHeader.innerHTML = `<i class="las la-filter"></i> ${widgetTitle}`;
  
  // Clear existing filters
  filterControls.innerHTML = '';
  
  // Set column class based on layout
  const columnClass = layout === 'column' ? 'col-12' : 'col-md-6';
  
  // Add filters based on count
  for (let i = 1; i <= filterCount; i++) {
    // Get filter configuration
    const filterLabel = document.getElementById(`${settingsId}-filter${i}-label`).value;
    const filterPlaceholder = document.getElementById(`${settingsId}-filter${i}-placeholder`).value;
    const filterOptions = document.getElementById(`${settingsId}-filter${i}-options`).value.split('\n');
    
    // Create filter DOM element
    const filterCol = document.createElement('div');
    filterCol.className = `${columnClass} mb-2`;
    
    // Create select element
    let selectHTML = `<select class="form-select form-select-sm" id="${widgetId}-filter${i}">`;
    selectHTML += `<option value="">${filterPlaceholder}</option>`;
    
    // Add options
    filterOptions.forEach(option => {
      if (option.trim()) {
        selectHTML += `<option value="${option.trim().toLowerCase().replace(/\s+/g, '')}">${option.trim()}</option>`;
      }
    });
    
    selectHTML += '</select>';
    filterCol.innerHTML = selectHTML;
    
    // Add to filter controls
    filterControls.appendChild(filterCol);
  }
  
  // Reinitialize event listeners
  initializeFilterWidget(widgetId);
  
  // Reset filters
  resetFilters(widgetId);
  
  // Create additional filter settings if needed
  for (let i = 3; i <= filterCount; i++) {
    if (!document.getElementById(`${settingsId}-filter${i}-container`)) {
      const filterContainer = document.createElement('div');
      filterContainer.className = 'mb-3 filter-setting';
      filterContainer.id = `${settingsId}-filter${i}-container`;
      
      filterContainer.innerHTML = `
        <h6 class="mb-2">Filter ${i}</h6>
        <div class="mb-2">
          <label for="${settingsId}-filter${i}-label" class="form-label">Label</label>
          <input type="text" class="form-control" id="${settingsId}-filter${i}-label" value="Filter ${i}">
        </div>
        <div class="mb-2">
          <label for="${settingsId}-filter${i}-placeholder" class="form-label">Placeholder</label>
          <input type="text" class="form-control" id="${settingsId}-filter${i}-placeholder" value="Filter ${i} (All)">
        </div>
        <div class="mb-2">
          <label class="form-label">Options (one per line)</label>
          <textarea class="form-control" id="${settingsId}-filter${i}-options" rows="3">Option 1
Option 2
Option 3</textarea>
        </div>
      `;
      
      // Insert before the Layout Options
      const layoutOptions = document.querySelector(`#${settingsId} .offcanvas-body .mb-3:last-of-type`);
      layoutOptions.parentNode.insertBefore(filterContainer, layoutOptions);
    }
  }
  
  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById(settingsId));
  if (offcanvas) {
    offcanvas.hide();
  }
}

// Add the widget to the widget gallery
document.addEventListener("DOMContentLoaded", function () {
  // Check if the Advanced category exists, if not create it
  let advancedCategory = document.querySelector('.widget-category[data-category="advanced"]');
  if (!advancedCategory) {
    console.log("Advanced category not found in DOM, cannot add widget to gallery");
    return;
  }
  
  // Add widget to the widget gallery
  const widgetGallery = document.querySelector('.widget-gallery');
  if (widgetGallery) {
    const widgetItem = document.createElement('div');
    widgetItem.className = 'widget-item widget-category-advanced';
    widgetItem.onclick = function() { addDropdownFilterWidget(); };
    
    widgetItem.innerHTML = `
      <div class="widget-icon">
        <i class="las la-filter"></i>
      </div>
      <div class="widget-label">Filter Widget</div>
    `;
    
    widgetGallery.appendChild(widgetItem);
  }
});

// Export functions
window.addDropdownFilterWidget = addDropdownFilterWidget;
window.applyDropdownFilterSettings = applyDropdownFilterSettings;
window.updateFilteredContent = updateFilteredContent;
window.resetFilters = resetFilters; 