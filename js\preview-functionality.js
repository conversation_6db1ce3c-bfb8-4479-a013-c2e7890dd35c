console.log("=== PREVIEW FUNCTIONALITY SCRIPT STARTING ===");

/**
 * Enhanced Preview Functionality for Dashboard
 * This script adds individual widget preview, dashboard preview, and template saving capabilities.
 */

// Enhanced Preview Functionality
console.log("Preview functionality script loaded");

// Preview state management
const previewState = {
  dashboardPreview: false,
  widgetPreviews: new Set(),
  previewIndicator: null,
};

// Initialize when DOM is ready
document.addEventListener("DOMContentLoaded", function () {
  console.log("DOM loaded, initializing preview functionality");
  initializePreviewMode();
  initializeModals();
  standardizeWidgetIcons();
  addPreviewButtonsToWidgets();

  // Also observe for dynamically added widgets
  observeWidgetChanges();
});

// Also initialize if DOM is already loaded
if (document.readyState === "loading") {
  console.log("DOM is still loading, waiting for DOMContentLoaded");
} else {
  console.log(
    "DOM already loaded, initializing preview functionality immediately"
  );
  initializePreviewMode();
  initializeModals();
  standardizeWidgetIcons();
  addPreviewButtonsToWidgets();
  observeWidgetChanges();
}

// Standardize widget icons to only show settings and close icons
function standardizeWidgetIcons() {
  // Find all widget-icons containers
  const widgetIconsContainers = document.querySelectorAll(".widget-icons");

  widgetIconsContainers.forEach((container) => {
    // Hide expand and download icons
    const expandIcons = container.querySelectorAll(
      ".widget-icon.la-expand, .widget-icon:has(i.las.la-expand)"
    );
    expandIcons.forEach((icon) => {
      icon.style.display = "none";
    });

    const downloadIcons = container.querySelectorAll(
      ".widget-icon.la-download, .widget-icon:has(i.las.la-download)"
    );
    downloadIcons.forEach((icon) => {
      icon.style.display = "none";
    });

    // Make sure settings and close icons are visible
    const settingsIcons = container.querySelectorAll(
      ".widget-icon.la-cog, .widget-icon:has(i.las.la-cog), .settings-trigger"
    );
    settingsIcons.forEach((icon) => {
      icon.style.display = "";
    });

    const closeIcons = container.querySelectorAll(
      ".widget-icon.la-times, .widget-icon:has(i.las.la-times)"
    );
    closeIcons.forEach((icon) => {
      icon.style.display = "";
    });
  });
}

// Initialize preview mode functionality
function initializePreviewMode() {
  console.log("Initializing preview mode functionality");
  // Event listeners for buttons
  const previewBtn = document.getElementById("preview-btn");
  const exitPreviewBtn = document.getElementById("exit-preview-btn");
  const previewOverlay = document.getElementById("preview-overlay");

  console.log("Found elements:", {
    previewBtn,
    exitPreviewBtn,
    previewOverlay,
  });

  // Toggle preview mode when clicking the preview button
  if (previewBtn) {
    previewBtn.addEventListener("click", function () {
      console.log("Preview button clicked");
      if (document.body.classList.contains("preview-mode")) {
        exitDashboardPreview();
      } else {
        enterDashboardPreview();
      }
    });
  }

  // Exit preview mode when clicking the exit button in the overlay
  if (exitPreviewBtn) {
    exitPreviewBtn.addEventListener("click", exitDashboardPreview);
  }

  // Add keyboard shortcut (Escape key) to exit preview mode
  document.addEventListener("keydown", function (event) {
    if (
      event.key === "Escape" &&
      document.body.classList.contains("preview-mode")
    ) {
      exitDashboardPreview();
    }
  });

  // Legacy button support
  const saveDraftBtn = document.getElementById("save-draft-btn");
  const submitFeedbackBtn = document.getElementById("submit-feedback-btn");
  const publishBtn = document.getElementById("publish-btn");

  if (saveDraftBtn) {
    saveDraftBtn.addEventListener("click", saveDashboard);
  }
  if (submitFeedbackBtn) {
    submitFeedbackBtn.addEventListener("click", submitFeedback);
  }
  if (publishBtn) {
    publishBtn.addEventListener("click", openPublishConfirmation);
  }

  // Add event listener for feedback button with a slight delay to ensure it works in preview mode
  setTimeout(() => {
    const feedbackBtn = document.getElementById("feedback-btn");
    if (feedbackBtn) {
      // Remove any existing event listeners to prevent duplicates
      feedbackBtn.removeEventListener("click", openFeedbackModal);
      // Add the event listener
      feedbackBtn.addEventListener("click", openFeedbackModal);
    }
  }, 500);

  // Add eye icons to widgets after initialization
  setTimeout(() => {
    console.log("Adding eye icons to widgets");
    addEyeIconsToWidgets();
  }, 1000);
}

// Initialize modals
function initializeModals() {
  // Initialize confirmation modal action
  document
    .getElementById("confirm-action-btn")
    .addEventListener("click", function () {
      // Get the current action from the button's data attribute
      const action = this.getAttribute("data-action");

      // Hide the confirmation modal
      const confirmationModal = bootstrap.Modal.getInstance(
        document.getElementById("confirmation-modal")
      );
      confirmationModal.hide();

      // Execute the appropriate action
      if (action === "publish") {
        publishDashboardConfirmed();
      }
    });
}

// Save dashboard layout
function saveDashboard() {
  // Get the grid instance
  const grid = document.querySelector(".grid-stack").gridstack;

  // Save the layout
  const layout = grid.save();

  // TODO: Implement saving to backend
  console.log("Saving dashboard layout:", layout);

  // Show the Preview button after saving as draft
  document.getElementById("preview-btn").style.display = "inline-flex";

  // Show confirmation using Bootstrap modal
  showNotification(
    "success",
    "Success",
    "Dashboard saved as draft successfully!"
  );
}

// Add preview buttons to all existing widgets
function addPreviewButtonsToWidgets() {
  // Wait a bit for widgets to be fully loaded
  setTimeout(() => {
    const widgetIconsContainers = document.querySelectorAll(".widget-icons");

    widgetIconsContainers.forEach((container) => {
      // Check if preview button already exists
      if (container.querySelector(".widget-preview-btn")) {
        return;
      }

      // Create preview button
      const previewBtn = document.createElement("button");
      previewBtn.className = "widget-action-btn widget-preview-btn";
      previewBtn.innerHTML = '<i class="las la-eye"></i>';
      previewBtn.title = "Preview Widget";
      previewBtn.setAttribute("data-bs-toggle", "tooltip");

      // Add click event
      previewBtn.addEventListener("click", function (e) {
        e.stopPropagation();
        const widgetElement = this.closest(".grid-stack-item");
        toggleWidgetPreview(widgetElement);
      });

      // Insert preview button before settings button
      const settingsBtn = container.querySelector(
        ".settings-trigger, .widget-action-btn:has(i.las.la-cog)"
      );
      if (settingsBtn) {
        container.insertBefore(previewBtn, settingsBtn);
      } else {
        container.appendChild(previewBtn);
      }
    });

    // Initialize tooltips for new buttons
    if (typeof bootstrap !== "undefined" && bootstrap.Tooltip) {
      const tooltipTriggerList = [].slice.call(
        document.querySelectorAll('[data-bs-toggle="tooltip"]')
      );
      tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
      });
    }
  }, 1000);
}

// Add eye icons to Smart Widget Composer main widgets (parent containers)
function addEyeIconsToWidgets() {
  console.log(
    "=== DEBUG: Starting to add eye icons to Smart Widget Composer main widgets ==="
  );

  // Debug: Check if grid container exists
  const gridContainer = document.getElementById("grid-container");
  console.log("DEBUG: Grid container found:", !!gridContainer);

  // Find all grid stack items that contain Smart Widget Composer sections as DIRECT children
  const allGridItems = document.querySelectorAll(".grid-stack-item");
  console.log(`DEBUG: Found ${allGridItems.length} total grid stack items`);

  // Debug each grid item structure
  allGridItems.forEach((gridItem, index) => {
    console.log(`DEBUG: Grid item ${index}:`, {
      id: gridItem.id,
      classes: gridItem.className,
      hasGridStackItemContent: !!gridItem.querySelector(
        ".grid-stack-item-content"
      ),
      hasSectionContainer: !!gridItem.querySelector(
        ".section-container-widget"
      ),
      directSectionContainer: !!gridItem.querySelector(
        ".grid-stack-item-content > .section-container-widget"
      ),
      innerHTML: gridItem.innerHTML.substring(0, 200) + "...",
    });
  });

  const smartComposerWidgets = document.querySelectorAll(".grid-stack-item");
  console.log(
    `DEBUG: Found ${smartComposerWidgets.length} grid stack items to check`
  );

  smartComposerWidgets.forEach((gridItem, index) => {
    console.log(
      `DEBUG: Checking grid item ${index + 1}/${smartComposerWidgets.length}`
    );

    // Check if this grid item contains a Smart Widget Composer section as a DIRECT child
    // Updated selector to match actual DOM structure: grid-stack-item-content > section-container-widget
    const sectionContainer = gridItem.querySelector(
      ".grid-stack-item-content > .section-container-widget"
    );

    console.log(`DEBUG: Grid item ${index} section container check:`, {
      hasSectionContainer: !!sectionContainer,
      hasGridStackItemContent: !!gridItem.querySelector(
        ".grid-stack-item-content"
      ),
      directPath: !!gridItem.querySelector(
        ".grid-stack-item-content > .section-container-widget"
      ),
    });

    if (!sectionContainer) {
      console.log(
        `DEBUG: Skipping grid item ${index} - not a Smart Widget Composer main widget`
      );
      return; // Skip if not a Smart Widget Composer main widget
    }

    console.log(
      `DEBUG: Processing Smart Widget Composer main widget ${index + 1}`
    );

    // Skip if eye icon already exists
    if (gridItem.querySelector(".widget-eye-icon")) {
      console.log(
        `DEBUG: Eye icon already exists for Smart Widget Composer ${
          gridItem.id || index
        }`
      );
      return;
    }

    // Find or create the widget icons container in the Smart Widget Composer header
    const widgetHeader = sectionContainer.querySelector(".widget-header");
    if (!widgetHeader) {
      console.log(
        `DEBUG: No widget header found for Smart Widget Composer ${
          gridItem.id || index
        }`
      );
      return;
    }

    // Look for existing button container in header, or create one
    let buttonContainer = widgetHeader.querySelector("div:last-child");
    if (!buttonContainer || !buttonContainer.querySelector("button")) {
      // Create a new button container if none exists
      buttonContainer = document.createElement("div");
      widgetHeader.appendChild(buttonContainer);
      console.log(
        `DEBUG: Created button container for Smart Widget Composer ${
          gridItem.id || index
        }`
      );
    }

    console.log(
      `DEBUG: Button container found for grid item ${index}:`,
      !!buttonContainer
    );

    // Create the eye icon button
    const eyeIcon = document.createElement("button");
    eyeIcon.className = "btn btn-sm btn-link text-dark ms-1 widget-eye-icon";
    eyeIcon.title = "Preview Smart Widget Composer";
    eyeIcon.innerHTML = '<i class="las la-eye"></i>';

    // Add click event listener
    eyeIcon.addEventListener("click", function (e) {
      e.stopPropagation();
      console.log(
        `DEBUG: Eye icon clicked for Smart Widget Composer ${
          gridItem.id || index
        }`
      );
      toggleSmartComposerPreview(gridItem);
    });

    // Insert the eye icon before the close button
    const closeButton = buttonContainer.querySelector(
      'button[onclick*="removeSectionContainer"]'
    );
    if (closeButton) {
      buttonContainer.insertBefore(eyeIcon, closeButton);
    } else {
      buttonContainer.appendChild(eyeIcon);
    }

    console.log(
      `DEBUG: Added eye icon to Smart Widget Composer main widget ${
        gridItem.id || index
      }`
    );
  });

  console.log(
    "=== DEBUG: Finished adding eye icons to Smart Widget Composer main widgets ==="
  );
}

// Toggle individual widget preview
function toggleWidgetPreview(widget) {
  const widgetId = widget.id;
  console.log(`Toggling preview for widget ${widgetId}`);

  if (previewState.widgetPreviews.has(widgetId)) {
    exitWidgetPreview(widget, widgetId);
  } else {
    enterWidgetPreview(widget, widgetId);
  }
}

// Enter individual widget preview
function enterWidgetPreview(widget, widgetId) {
  console.log(`Entering preview mode for widget ${widgetId}`);

  // Add to preview state
  previewState.widgetPreviews.add(widgetId);

  // Add preview class to widget
  widget.classList.add("widget-preview-mode");

  // Update eye icon
  const eyeIcon = widget.querySelector(".widget-eye-icon");
  if (eyeIcon) {
    eyeIcon.classList.add("active");
    eyeIcon.title = "Exit Widget Preview";
    const icon = eyeIcon.querySelector("i");
    if (icon) {
      icon.classList.remove("la-eye");
      icon.classList.add("la-eye-slash");
    }
  }

  // Hide other widget controls
  const otherActions = widget.querySelectorAll(
    ".widget-action-btn:not(.widget-eye-icon)"
  );
  otherActions.forEach((action) => {
    action.style.display = "none";
  });

  console.log(`Widget ${widgetId} entered preview mode`);
}

// Exit individual widget preview
function exitWidgetPreview(widget, widgetId) {
  console.log(`Exiting preview mode for widget ${widgetId}`);

  // Remove from preview state
  previewState.widgetPreviews.delete(widgetId);

  // Remove preview class from widget
  widget.classList.remove("widget-preview-mode");

  // Update eye icon
  const eyeIcon = widget.querySelector(".widget-eye-icon");
  if (eyeIcon) {
    eyeIcon.classList.remove("active");
    eyeIcon.title = "Preview Widget";
    const icon = eyeIcon.querySelector("i");
    if (icon) {
      icon.classList.remove("la-eye-slash");
      icon.classList.add("la-eye");
    }
  }

  // Show other widget controls
  const otherActions = widget.querySelectorAll(
    ".widget-action-btn:not(.widget-eye-icon)"
  );
  otherActions.forEach((action) => {
    action.style.display = "";
  });

  console.log(`Widget ${widgetId} exited preview mode`);
}

// Enhanced dashboard preview mode
function enterDashboardPreview() {
  // Add dashboard preview class to body
  document.body.classList.add("dashboard-preview-mode");
  document.body.classList.add("preview-mode"); // For compatibility with existing CSS

  // Set dashboard preview state
  previewState.dashboardPreview = true;

  // Clear individual widget previews
  previewState.widgetPreviews.forEach((widgetId) => {
    const widgetElement = document.getElementById(widgetId);
    if (widgetElement) {
      exitWidgetPreview(widgetElement, widgetId);
    }
  });

  // Show dashboard preview indicator
  showPreviewIndicator("Dashboard Preview Mode");

  // Call existing preview functionality
  enterPreviewMode();

  // Show the preview overlay
  const previewOverlay = document.getElementById("preview-overlay");
  if (previewOverlay) {
    previewOverlay.style.display = "block";
  }

  // Update preview button state
  const previewBtn = document.getElementById("preview-btn");
  if (previewBtn) {
    previewBtn.classList.add("active");
    const icon = previewBtn.querySelector("i");
    const span = previewBtn.querySelector("span");
    if (icon) {
      icon.classList.remove("la-eye");
      icon.classList.add("la-eye-slash");
    }
    if (span) {
      span.textContent = "Exit Preview";
    }
  }
}

// Exit dashboard preview mode
function exitDashboardPreview() {
  // Remove dashboard preview classes
  document.body.classList.remove("dashboard-preview-mode");
  document.body.classList.remove("preview-mode"); // For compatibility with existing CSS

  // Set dashboard preview state
  previewState.dashboardPreview = false;

  // Hide preview indicator
  hidePreviewIndicator();

  // Call existing exit preview functionality
  exitPreviewMode();

  // Hide the preview overlay
  const previewOverlay = document.getElementById("preview-overlay");
  if (previewOverlay) {
    previewOverlay.style.display = "none";
  }

  // Update preview button state
  const previewBtn = document.getElementById("preview-btn");
  if (previewBtn) {
    previewBtn.classList.remove("active");
    const icon = previewBtn.querySelector("i");
    const span = previewBtn.querySelector("span");
    if (icon) {
      icon.classList.remove("la-eye-slash");
      icon.classList.add("la-eye");
    }
    if (span) {
      span.textContent = "Preview";
    }
  }
}

// Show preview mode indicator
function showPreviewIndicator(text) {
  // Remove existing indicator
  hidePreviewIndicator();

  // Create new indicator
  const indicator = document.createElement("div");
  indicator.className = "preview-mode-indicator";
  indicator.innerHTML = `<i class="las la-eye me-2"></i>${text}`;

  // Add click to exit functionality
  indicator.addEventListener("click", () => {
    if (previewState.dashboardPreview) {
      exitDashboardPreview();
    } else {
      // Exit all widget previews
      previewState.widgetPreviews.forEach((widgetId) => {
        const widgetElement = document.getElementById(widgetId);
        if (widgetElement) {
          toggleWidgetPreview(widgetElement);
        }
      });
    }
  });

  document.body.appendChild(indicator);
  previewState.previewIndicator = indicator;
}

// Hide preview mode indicator
function hidePreviewIndicator() {
  if (previewState.previewIndicator) {
    previewState.previewIndicator.remove();
    previewState.previewIndicator = null;
  }
}

// Add escape key listener for widget preview
function addEscapeKeyListener(widgetElement, widgetId) {
  const escapeHandler = function (e) {
    if (e.key === "Escape") {
      toggleWidgetPreview(widgetElement);
    }
  };

  document.addEventListener("keydown", escapeHandler);

  // Store handler for removal
  widgetElement._escapeHandler = escapeHandler;
}

// Remove escape key listener
function removeEscapeKeyListener(widgetId) {
  const widgetElement = document.getElementById(widgetId);
  if (widgetElement && widgetElement._escapeHandler) {
    document.removeEventListener("keydown", widgetElement._escapeHandler);
    delete widgetElement._escapeHandler;
  }
}

// Enhanced preview dashboard function
function previewDashboard() {
  // Check if canvas operation is in progress
  if (window.canvasOperationInProgress) {
    console.log("Preview mode blocked - Canvas operation in progress");
    return;
  }
  enterDashboardPreview();
}

// Enter preview mode (original functionality)
function enterPreviewMode() {
  // Prevent preview mode during canvas operations
  if (window.canvasOperationInProgress) {
    console.log("Preview mode blocked - canvas operation in progress");
    return;
  }

  console.log("🔍 Entering preview mode");

  // Store current state
  window.previewState = {
    isPreviewMode: true,
    originalWidgetSectionDisplay: null,
  };

  // Hide editing elements
  const editingElements = document.querySelectorAll(
    ".widget-section, .grid-stack-item .widget-controls, .add-widget-btn, .widget-item .edit-btn, .widget-item .remove-btn, .widget-item .move-btn"
  );

  editingElements.forEach((element) => {
    if (element) {
      // Store original display state before hiding
      if (element.classList.contains("widget-section")) {
        window.previewState.originalWidgetSectionDisplay =
          element.style.display || "block";
      }
      element.style.display = "none";
    }
  });

  // Show preview controls
  const previewControls = document.querySelectorAll(".preview-controls");
  previewControls.forEach((control) => {
    if (control) control.style.display = "block";
  });

  // Add preview mode class to body
  document.body.classList.add("preview-mode");

  console.log("✅ Preview mode entered successfully");
}

// Open feedback modal
function openFeedbackModal() {
  console.log("Opening feedback modal");

  // Clear previous feedback
  document.getElementById("feedback-text").value = "";
  document.getElementById("feedback-type").value = "general";

  // Get the modal element
  const modalElement = document.getElementById("feedback-modal");

  // Check if there's an existing modal instance
  let feedbackModal = bootstrap.Modal.getInstance(modalElement);

  // If there's an existing instance, dispose it to prevent issues
  if (feedbackModal) {
    feedbackModal.dispose();
  }

  // Create a new modal instance
  feedbackModal = new bootstrap.Modal(modalElement, {
    backdrop: "static",
    keyboard: false,
  });

  // Show the modal
  feedbackModal.show();
}

// Submit feedback
function submitFeedback() {
  const feedbackType = document.getElementById("feedback-type").value;
  const feedbackText = document.getElementById("feedback-text").value;

  if (!feedbackText.trim()) {
    showNotification(
      "warning",
      "Warning",
      "Please enter your feedback before submitting."
    );
    return;
  }

  // TODO: Implement sending feedback to backend
  console.log("Submitting feedback:", {
    type: feedbackType,
    text: feedbackText,
  });

  // Close the modal
  const feedbackModal = bootstrap.Modal.getInstance(
    document.getElementById("feedback-modal")
  );
  feedbackModal.hide();

  // Show confirmation
  showNotification(
    "success",
    "Thank You",
    "Your feedback has been submitted successfully!"
  );
}

// Open publish confirmation modal
function openPublishConfirmation() {
  // Set up the confirmation modal
  document.getElementById("confirmation-title").textContent =
    "Publish Dashboard";
  document.getElementById("confirmation-message").textContent =
    "Are you sure you want to publish this dashboard?";
  document
    .getElementById("confirm-action-btn")
    .setAttribute("data-action", "publish");

  // Show the confirmation modal
  const confirmationModal = new bootstrap.Modal(
    document.getElementById("confirmation-modal")
  );
  confirmationModal.show();
}

// Publish dashboard after confirmation
function publishDashboardConfirmed() {
  // TODO: Implement publishing to backend
  console.log("Publishing dashboard");

  // Show success notification
  showNotification("success", "Success", "Dashboard published successfully!");

  // Exit preview mode
  exitPreviewMode();
}

// Show notification modal
function showNotification(type, title, message) {
  // Get the notification modal elements
  const modal = document.getElementById("notification-modal");
  const modalTitle = document.getElementById("notification-title");
  const modalMessage = document.getElementById("notification-message");
  const modalIcon = document.getElementById("notification-icon");

  // Remove any existing type classes
  modal.classList.remove("success", "warning", "error");

  // Set the appropriate icon and add type class
  if (type === "success") {
    modal.classList.add("success");
    modalIcon.className = "las la-check-circle";
  } else if (type === "warning") {
    modal.classList.add("warning");
    modalIcon.className = "las la-exclamation-circle";
  } else if (type === "error") {
    modal.classList.add("error");
    modalIcon.className = "las la-times-circle";
  }

  // Set the title and message
  modalTitle.textContent = title;
  modalMessage.textContent = message;

  // Show the modal
  const notificationModal = new bootstrap.Modal(modal);
  notificationModal.show();
}

// Enhanced exit preview mode function
function exitPreviewMode() {
  console.log("🚪 Exiting preview mode");

  // Remove preview mode class from body
  document.body.classList.remove("preview-mode");

  // Restore editing elements
  const editingElements = document.querySelectorAll(
    ".widget-section, .grid-stack-item .widget-controls, .add-widget-btn, .widget-item .edit-btn, .widget-item .remove-btn, .widget-item .move-btn"
  );

  editingElements.forEach((element) => {
    if (element) {
      // Restore widget section with original display state
      if (
        element.classList.contains("widget-section") &&
        window.previewState?.originalWidgetSectionDisplay
      ) {
        element.style.display =
          window.previewState.originalWidgetSectionDisplay;
      } else {
        element.style.display = "block";
      }
    }
  });

  // Hide preview controls
  const previewControls = document.querySelectorAll(".preview-controls");
  previewControls.forEach((control) => {
    if (control) control.style.display = "none";
  });

  // Clear preview state
  if (window.previewState) {
    window.previewState.isPreviewMode = false;
    window.previewState = null;
  }

  console.log("✅ Preview mode exited successfully");
}

// Observe for dynamically added Smart Widget Composer main widgets
function observeWidgetChanges() {
  console.log(
    "=== DEBUG: Setting up Smart Widget Composer main widget observer ==="
  );

  const gridContainer = document.getElementById("grid-container");
  if (!gridContainer) {
    console.log("DEBUG: Grid container not found, retrying in 1 second");
    setTimeout(observeWidgetChanges, 1000);
    return;
  }

  console.log("DEBUG: Grid container found, setting up observer");

  // Create a MutationObserver to watch for new Smart Widget Composer main widgets
  const observer = new MutationObserver(function (mutations) {
    console.log(
      `DEBUG: MutationObserver triggered with ${mutations.length} mutations`
    );

    mutations.forEach(function (mutation, mutationIndex) {
      console.log(
        `DEBUG: Processing mutation ${mutationIndex + 1}/${mutations.length}:`,
        {
          type: mutation.type,
          addedNodes: mutation.addedNodes.length,
          removedNodes: mutation.removedNodes.length,
          target:
            mutation.target.tagName +
            (mutation.target.className ? "." + mutation.target.className : ""),
        }
      );

      if (mutation.type === "childList") {
        mutation.addedNodes.forEach(function (node, nodeIndex) {
          console.log(
            `DEBUG: Checking added node ${nodeIndex + 1}/${
              mutation.addedNodes.length
            }:`,
            {
              nodeType: node.nodeType,
              tagName: node.tagName,
              className: node.className,
              isElement: node.nodeType === Node.ELEMENT_NODE,
            }
          );

          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if a Smart Widget Composer main widget was added
            // Updated selector to match actual DOM structure
            const isGridStackItem = node.classList.contains("grid-stack-item");
            const hasSectionContainer = node.querySelector(
              ".grid-stack-item-content > .section-container-widget"
            );

            console.log(`DEBUG: Node analysis:`, {
              isGridStackItem,
              hasSectionContainer: !!hasSectionContainer,
              hasGridStackItemContent: !!node.querySelector(
                ".grid-stack-item-content"
              ),
              innerHTML: node.innerHTML
                ? node.innerHTML.substring(0, 100) + "..."
                : "No innerHTML",
            });

            if (isGridStackItem && hasSectionContainer) {
              console.log(
                "DEBUG: New Smart Widget Composer main widget detected, adding eye icon"
              );
              setTimeout(() => {
                console.log(
                  "DEBUG: Executing delayed addEyeIconsToWidgets call"
                );
                addEyeIconsToWidgets();
              }, 500);
            } else {
              console.log(
                "DEBUG: Added node is not a Smart Widget Composer main widget"
              );
            }
          }
        });
      }
    });
  });

  // Start observing
  observer.observe(gridContainer, {
    childList: true,
    subtree: true,
  });

  console.log(
    "=== DEBUG: Smart Widget Composer main widget observer set up successfully ==="
  );
}

// Global function to manually add eye icons to Smart Widget Composer main widgets (for testing)
window.addEyeIconsManually = function () {
  console.log(
    "Manually adding eye icons to Smart Widget Composer main widgets..."
  );
  addEyeIconsToWidgets();
};

// Global function to test Smart Widget Composer main widget preview functionality
window.testPreviewFunctionality = function () {
  console.log(
    "Testing Smart Widget Composer main widget preview functionality..."
  );
  console.log("Grid container:", document.getElementById("grid-container"));

  const smartComposerWidgets = document.querySelectorAll(".grid-stack-item");
  let smartComposerCount = 0;

  smartComposerWidgets.forEach((gridItem, index) => {
    const sectionContainer = gridItem.querySelector(
      ".section-container-widget"
    );
    if (sectionContainer) {
      smartComposerCount++;
      const nestedWidgets =
        sectionContainer.querySelectorAll(".widget-wrapper");
      console.log(
        `Smart Widget Composer ${smartComposerCount}: ${nestedWidgets.length} nested widgets`
      );
    }
  });

  console.log(
    `Total Smart Widget Composer main widgets found: ${smartComposerCount}`
  );
  addEyeIconsToWidgets();
};

// Toggle Smart Widget Composer preview
function toggleSmartComposerPreview(gridItem) {
  const widgetId = gridItem.id || `smart-composer-${Date.now()}`;
  if (!gridItem.id) {
    gridItem.id = widgetId;
  }

  console.log(`Toggling Smart Widget Composer preview for ${widgetId}`);

  if (previewState.widgetPreviews.has(widgetId)) {
    exitSmartComposerPreview(gridItem, widgetId);
  } else {
    enterSmartComposerPreview(gridItem, widgetId);
  }
}

// Enter Smart Widget Composer preview mode
function enterSmartComposerPreview(gridItem, widgetId) {
  console.log(`Entering Smart Widget Composer preview mode for ${widgetId}`);

  // Add to preview state
  previewState.widgetPreviews.add(widgetId);

  // Add preview class to main widget
  gridItem.classList.add("smart-composer-preview-mode");

  // Update eye icon on main widget
  const eyeIcon = gridItem.querySelector(".widget-eye-icon");
  if (eyeIcon) {
    eyeIcon.classList.add("active");
    eyeIcon.title = "Exit Smart Widget Composer Preview";
    const icon = eyeIcon.querySelector("i");
    if (icon) {
      icon.classList.remove("la-eye");
      icon.classList.add("la-eye-slash");
    }
  }

  // Hide main widget controls except eye icon
  const mainWidgetIcons = gridItem.querySelectorAll(
    ".widget-icons .widget-action-btn:not(.widget-eye-icon)"
  );
  mainWidgetIcons.forEach((icon) => {
    icon.style.display = "none";
  });

  // Find the section container and hide nested widget titles and controls
  const sectionContainer = gridItem.querySelector(".section-container-widget");
  if (sectionContainer) {
    // Hide all nested widget headers (titles)
    const nestedHeaders = sectionContainer.querySelectorAll(
      ".widget-wrapper .widget-header"
    );
    nestedHeaders.forEach((header) => {
      header.style.display = "none";
    });

    // Hide all nested widget action buttons and icons
    const nestedActions = sectionContainer.querySelectorAll(
      ".widget-wrapper .widget-actions, .widget-wrapper .widget-icons"
    );
    nestedActions.forEach((actions) => {
      actions.style.display = "none";
    });

    // Hide any settings triggers in nested widgets
    const nestedSettings = sectionContainer.querySelectorAll(
      ".widget-wrapper .settings-trigger"
    );
    nestedSettings.forEach((settings) => {
      settings.style.display = "none";
    });

    // Enhanced styling for cleaner preview - remove all nested card appearance
    const nestedContents = sectionContainer.querySelectorAll(
      ".widget-wrapper .widget-content"
    );
    nestedContents.forEach((content) => {
      content.style.paddingTop = "0";
      content.style.padding = "0";
      content.style.margin = "0";
      content.style.border = "none";
      content.style.boxShadow = "none";
      content.style.borderRadius = "0";
      content.style.background = "transparent";
    });

    // Remove padding and styling from nested widget wrappers
    const nestedWrappers = sectionContainer.querySelectorAll(".widget-wrapper");
    nestedWrappers.forEach((wrapper) => {
      wrapper.style.padding = "0";
      wrapper.style.margin = "0";
      wrapper.style.border = "none";
      wrapper.style.boxShadow = "none";
      wrapper.style.borderRadius = "0";
      wrapper.style.background = "transparent";
    });

    // Remove padding from grid stack items inside the composer
    const nestedGridItems =
      sectionContainer.querySelectorAll(".grid-stack-item");
    nestedGridItems.forEach((item) => {
      item.style.padding = "0";
      item.style.margin = "0";
    });

    // Remove padding from grid stack item content
    const nestedGridContents = sectionContainer.querySelectorAll(
      ".grid-stack-item-content"
    );
    nestedGridContents.forEach((content) => {
      content.style.padding = "0";
      content.style.margin = "0";
    });

    // Remove padding from the nested grid container itself
    const nestedGridContainer = sectionContainer.querySelector(
      ".nested-grid-container"
    );
    if (nestedGridContainer) {
      nestedGridContainer.style.padding = "0";
      nestedGridContainer.style.margin = "0";
      nestedGridContainer.style.gap = "0";
      // Reduce grid margins to make it seamless
      nestedGridContainer.style.setProperty("--gs-item-margin-top", "0px");
      nestedGridContainer.style.setProperty("--gs-item-margin-bottom", "0px");
      nestedGridContainer.style.setProperty("--gs-item-margin-right", "0px");
      nestedGridContainer.style.setProperty("--gs-item-margin-left", "0px");
    }

    // Remove padding from the section container itself
    sectionContainer.style.padding = "0";
    sectionContainer.style.margin = "0";
  }

  // Add visual enhancement to the main widget
  gridItem.style.boxShadow = "0 8px 25px rgba(0, 0, 0, 0.15)";
  gridItem.style.borderRadius = "12px";
  gridItem.style.overflow = "hidden";

  console.log(`Smart Widget Composer ${widgetId} entered preview mode`);
}

// Exit Smart Widget Composer preview mode
function exitSmartComposerPreview(gridItem, widgetId) {
  console.log(`Exiting Smart Widget Composer preview mode for ${widgetId}`);

  // Remove from preview state
  previewState.widgetPreviews.delete(widgetId);

  // Remove preview class from main widget
  gridItem.classList.remove("smart-composer-preview-mode");

  // Update eye icon on main widget
  const eyeIcon = gridItem.querySelector(".widget-eye-icon");
  if (eyeIcon) {
    eyeIcon.classList.remove("active");
    eyeIcon.title = "Preview Smart Widget Composer";
    const icon = eyeIcon.querySelector("i");
    if (icon) {
      icon.classList.remove("la-eye-slash");
      icon.classList.add("la-eye");
    }
  }

  // Show main widget controls
  const mainWidgetIcons = gridItem.querySelectorAll(
    ".widget-icons .widget-action-btn:not(.widget-eye-icon)"
  );
  mainWidgetIcons.forEach((icon) => {
    icon.style.display = "";
  });

  // Find the section container and restore nested widget titles and controls
  const sectionContainer = gridItem.querySelector(".section-container-widget");
  if (sectionContainer) {
    // Show all nested widget headers (titles)
    const nestedHeaders = sectionContainer.querySelectorAll(
      ".widget-wrapper .widget-header"
    );
    nestedHeaders.forEach((header) => {
      header.style.display = "";
    });

    // Show all nested widget action buttons and icons
    const nestedActions = sectionContainer.querySelectorAll(
      ".widget-wrapper .widget-actions, .widget-wrapper .widget-icons"
    );
    nestedActions.forEach((actions) => {
      actions.style.display = "";
    });

    // Show settings triggers in nested widgets
    const nestedSettings = sectionContainer.querySelectorAll(
      ".widget-wrapper .settings-trigger"
    );
    nestedSettings.forEach((settings) => {
      settings.style.display = "";
    });

    // Restore nested widget content styling
    const nestedContents = sectionContainer.querySelectorAll(
      ".widget-wrapper .widget-content"
    );
    nestedContents.forEach((content) => {
      content.style.paddingTop = "";
      content.style.padding = "";
      content.style.margin = "";
      content.style.border = "";
      content.style.boxShadow = "";
      content.style.borderRadius = "";
      content.style.background = "";
    });

    // Restore nested widget wrapper styling
    const nestedWrappers = sectionContainer.querySelectorAll(".widget-wrapper");
    nestedWrappers.forEach((wrapper) => {
      wrapper.style.padding = "";
      wrapper.style.margin = "";
      wrapper.style.border = "";
      wrapper.style.boxShadow = "";
      wrapper.style.borderRadius = "";
      wrapper.style.background = "";
    });

    // Restore grid stack items styling
    const nestedGridItems =
      sectionContainer.querySelectorAll(".grid-stack-item");
    nestedGridItems.forEach((item) => {
      item.style.padding = "";
      item.style.margin = "";
    });

    // Restore grid stack item content styling
    const nestedGridContents = sectionContainer.querySelectorAll(
      ".grid-stack-item-content"
    );
    nestedGridContents.forEach((content) => {
      content.style.padding = "";
      content.style.margin = "";
    });

    // Restore nested grid container styling
    const nestedGridContainer = sectionContainer.querySelector(
      ".nested-grid-container"
    );
    if (nestedGridContainer) {
      nestedGridContainer.style.padding = "";
      nestedGridContainer.style.margin = "";
      nestedGridContainer.style.gap = "";
      // Restore original grid margins
      nestedGridContainer.style.setProperty("--gs-item-margin-top", "5px");
      nestedGridContainer.style.setProperty("--gs-item-margin-bottom", "5px");
      nestedGridContainer.style.setProperty("--gs-item-margin-right", "5px");
      nestedGridContainer.style.setProperty("--gs-item-margin-left", "5px");
    }

    // Restore section container styling
    sectionContainer.style.padding = "";
    sectionContainer.style.margin = "";
  }

  // Remove visual enhancements from main widget
  gridItem.style.boxShadow = "";
  gridItem.style.borderRadius = "";
  gridItem.style.overflow = "";

  console.log(`Smart Widget Composer ${widgetId} exited preview mode`);
}

// Global function to manually test DOM structure (for debugging)
window.debugDOMStructure = function () {
  console.log("=== MANUAL DEBUG: DOM Structure Analysis ===");

  // Check grid container
  const gridContainer = document.getElementById("grid-container");
  console.log("Grid container exists:", !!gridContainer);

  // Check all grid stack items
  const allGridItems = document.querySelectorAll(".grid-stack-item");
  console.log(`Total grid stack items found: ${allGridItems.length}`);

  allGridItems.forEach((item, index) => {
    console.log(`Grid item ${index}:`, {
      id: item.id,
      classes: item.className,
      hasWidgetWrapper: !!item.querySelector(".widget-wrapper"),
      hasWidgetContent: !!item.querySelector(".widget-content"),
      hasSectionContainer: !!item.querySelector(".section-container-widget"),
      directSectionContainer: !!item.querySelector(
        ":scope > .widget-wrapper > .widget-content > .section-container-widget"
      ),
      hasWidgetIcons: !!item.querySelector(".widget-icons"),
      hasEyeIcon: !!item.querySelector(".widget-eye-icon"),
      outerHTML: item.outerHTML.substring(0, 300) + "...",
    });
  });

  // Try to manually add eye icons
  console.log("Attempting to manually add eye icons...");
  addEyeIconsToWidgets();

  console.log("=== END MANUAL DEBUG ===");
};

// Call the debug function automatically after a delay
setTimeout(() => {
  console.log("Auto-calling debug function...");
  window.debugDOMStructure();
}, 2000);
