/* Line Chart Widget Styles */
.line-chart-widget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 0px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--slate-grey-light);
}

.chart-container {
  flex: 1;
  min-height: 300px;
  padding: 1.25rem;
  background-color: white;
}

/* Widget Header */
.line-chart-widget .widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  background: #ffffff;
  border-bottom: 1px solid #e5e9f0;
}

.line-chart-widget .widget-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.line-chart-widget .widget-header i {
  font-size: 16px;
  color: var(--ocean-teal);
}

/* Chart Settings Panel */
.chart-settings {
  background-color: var(--slate-grey-light);
  border-radius: 8px;
  padding: 1.25rem;
  margin-bottom: 1rem;
}

.chart-settings label {
  color: var(--wns-black);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.chart-settings .form-select {
  border-color: var(--slate-grey);
  border-radius: 6px;
  padding: 0.625rem;
}

.chart-settings .form-range {
  height: 6px;
  border-radius: 3px;
}

.chart-settings .form-range::-webkit-slider-thumb {
  background: var(--denali-blue);
  width: 16px;
  height: 16px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-settings .form-range::-moz-range-thumb {
  background: var(--denali-blue);
  width: 16px;
  height: 16px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-settings .form-check-input {
  width: 2.5rem;
  height: 1.25rem;
  border-radius: 1rem;
}

.chart-settings .form-check-input:checked {
  background-color: var(--denali-blue);
  border-color: var(--denali-blue);
}

.chart-settings .form-check-label {
  padding-left: 0.5rem;
  user-select: none;
}

/* Line Colors */
.line-primary {
  stroke: var(--ocean-teal);
}

.line-secondary {
  stroke: var(--emerald-green);
}

.line-tertiary {
  stroke: var(--forest-green);
}

.line-quaternary {
  stroke: var(--slate-grey);
}

.line-quinary {
  stroke: var(--denali-blue);
}

/* Point Colors */
.point-primary {
  fill: var(--ocean-teal);
}

.point-secondary {
  fill: var(--emerald-green);
}

.point-tertiary {
  fill: var(--forest-green);
}

.point-quaternary {
  fill: var(--slate-grey);
}

.point-quinary {
  fill: var(--denali-blue);
}

/* Risk Score Colors */
.risk-score-5 {
  stroke: var(--high-risk);
}

.risk-score-4-5 {
  stroke: var(--risk-4-5);
}

.risk-score-4 {
  stroke: var(--risk-4-0);
}

.risk-score-3-5 {
  stroke: var(--risk-3-5);
}

.risk-score-3 {
  stroke: var(--moderate-risk);
}

.risk-score-2-5 {
  stroke: var(--risk-2-5);
}

.risk-score-2 {
  stroke: var(--risk-2-0);
}

.risk-score-1-5 {
  stroke: var(--risk-1-5);
}

.risk-score-1 {
  stroke: var(--low-risk);
}

/* Chart Widget Styles */
.chart-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.chart-title i {
  font-size: var(--font-size-md);
  color: var(--primary-color);
}

.chart-title span {
  font-size: var(--font-size-lg);
}

.chart-actions {
  display: flex;
  gap: 0.5rem;
}

.chart-actions button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  font-size: var(--font-size-base);
  transition: color 0.2s ease;
}

.chart-actions button:hover {
  color: var(--primary-color);
}

.chart-body {
  flex: 1;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

.chart-legend {
  margin-top: 1rem;
  font-size: var(--font-size-base);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: var(--font-size-base);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-label {
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

/* Line Chart Specific Styles */
.line-label {
  font-size: var(--font-size-base);
  color: var(--text-primary);
}

.axis-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.point-label {
  font-size: var(--font-size-base);
  font-weight: 500;
}

/* Dark Theme Support */
body.dark-theme .chart-widget {
  background: var(--secondary-color);
}

body.dark-theme .chart-header {
  background: rgba(0, 0, 0, 0.1);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme .chart-title {
  color: white;
}

body.dark-theme .chart-actions button {
  color: rgba(255, 255, 255, 0.7);
}

body.dark-theme .chart-actions button:hover {
  color: white;
}

body.dark-theme .line-label,
body.dark-theme .point-label {
  color: rgba(255, 255, 255, 0.9);
}

body.dark-theme .axis-label {
  color: rgba(255, 255, 255, 0.6);
}

/* Responsive Design */
@media (max-width: 768px) {
  .chart-header {
    padding: 0.75rem;
  }

  .chart-title span {
    font-size: var(--font-size-base);
  }

  .chart-title i {
    font-size: var(--font-size-md);
  }

  .chart-actions button {
    font-size: var(--font-size-sm);
  }

  .legend-item,
  .legend-label {
    font-size: var(--font-size-sm);
  }

  .line-label,
  .point-label {
    font-size: var(--font-size-sm);
  }

  .axis-label {
    font-size: var(--font-size-sm);
  }
}
