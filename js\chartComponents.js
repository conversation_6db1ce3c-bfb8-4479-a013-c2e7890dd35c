// Reusable chart components
class ChartComponents {
  // Create a settings section with title and optional reset button
  static createSettingsSection(
    title,
    resetFunction = null,
    tooltipText = null
  ) {
    return `
      <div class="settings-section mb-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h6 class="section-title mb-0">${title}</h6>
          ${
            resetFunction
              ? `
            <button class="btn btn-sm btn-outline-secondary" onclick="${resetFunction}()" 
                    data-bs-toggle="tooltip" title="${
                      tooltipText || `Reset ${title.toLowerCase()}`
                    }">
              <i class="las la-undo-alt"></i>
            </button>
          `
              : ""
          }
        </div>
        <div class="section-content"></div>
      </div>
    `;
  }

  // Create a form input with label
  static createFormInput(id, label, type = "text", options = {}) {
    const {
      placeholder = "",
      value = "",
      min = "",
      max = "",
      step = "",
      onchange = "previewChanges()",
      ariaLabel = label,
    } = options;

    return `
      <div class="mb-3">
        <label for="${id}" class="form-label d-flex justify-content-between">
          ${label}
          ${
            type === "range"
              ? '<span class="value-display">${value}</span>'
              : ""
          }
        </label>
        <input type="${type}" 
               class="form-control form-control-sm${
                 type === "range" ? " form-range" : ""
               }" 
               id="${id}"
               value="${value}"
               ${placeholder ? `placeholder="${placeholder}"` : ""}
               ${min ? `min="${min}"` : ""}
               ${max ? `max="${max}"` : ""}
               ${step ? `step="${step}"` : ""}
               oninput="${onchange}"
               aria-label="${ariaLabel}"/>
      </div>
    `;
  }

  // Create a toggle switch
  static createToggleSwitch(
    id,
    label,
    checked = false,
    onChange = "previewChanges()"
  ) {
    return `
      <div class="form-check form-switch mb-2">
        <input type="checkbox" 
               class="form-check-input" 
               id="${id}" 
               ${checked ? "checked" : ""}
               onchange="${onChange}"
               aria-label="Toggle ${label.toLowerCase()}"/>
        <label class="form-check-label" for="${id}">${label}</label>
      </div>
    `;
  }

  // Create a data table container
  static createDataTable(id, options = {}) {
    const {
      height = "400px",
      searchEnabled = true,
      addButtonText = "Add Row",
      addButtonFunction = "",
      reorderEnabled = false,
    } = options;

    return `
      <div class="table-responsive">
        ${
          searchEnabled
            ? `
          <div class="search-box mb-2">
            <div class="input-group input-group-sm">
              <span class="input-group-text">
                <i class="las la-search"></i>
              </span>
              <input type="text" 
                     class="form-control" 
                     placeholder="Search data..." 
                     oninput="filterTableData(this.value)"
                     aria-label="Search data"/>
            </div>
          </div>
        `
            : ""
        }
        <div id="${id}" style="height: ${height}; overflow: auto;"></div>
        <div class="table-actions mt-2">
          ${
            addButtonFunction
              ? `
            <button class="btn btn-sm btn-primary" onclick="${addButtonFunction}">
              <i class="las la-plus"></i> ${addButtonText}
            </button>
          `
              : ""
          }
          ${
            reorderEnabled
              ? `
            <button class="btn btn-sm btn-outline-secondary ms-2" onclick="reorderTableData()">
              <i class="las la-sort"></i> Reorder
            </button>
          `
              : ""
          }
        </div>
      </div>
    `;
  }

  // Create export options section
  static createExportSection(formats = ["csv", "excel", "json"]) {
    return `
      <div class="mb-3">
        <label class="form-label">Export Format</label>
        <select class="form-select form-select-sm" id="exportFormat" aria-label="Select export format">
          ${formats
            .map(
              (format) =>
                `<option value="${format}">${format.toUpperCase()}</option>`
            )
            .join("")}
        </select>
      </div>
      <div class="mb-3">
        <button class="btn btn-primary w-100" onclick="exportChartData()" data-bs-toggle="tooltip" title="Export chart data">
          <i class="las la-download"></i> Export Data
        </button>
      </div>
      <div class="mb-3">
        <button class="btn btn-outline-secondary w-100" onclick="exportChartImage()" data-bs-toggle="tooltip" title="Export chart as image">
          <i class="las la-image"></i> Export as Image
        </button>
      </div>
    `;
  }

  // Create tabs container
  static createTabsContainer(tabs) {
    return `
      <div class="settings-tabs mb-3">
        <ul class="nav nav-tabs nav-fill" role="tablist">
          ${tabs
            .map(
              (tab, index) => `
            <li class="nav-item" role="presentation">
              <button class="nav-link${index === 0 ? " active" : ""}" 
                      data-bs-toggle="tab" 
                      data-bs-target="#${tab.id}-tab" 
                      type="button" 
                      role="tab">
                <i class="las ${tab.icon}"></i> ${tab.label}
              </button>
            </li>
          `
            )
            .join("")}
        </ul>
        <div class="tab-content pt-3">
          ${tabs
            .map(
              (tab, index) => `
            <div class="tab-pane fade${
              index === 0 ? " show active" : ""
            }" id="${tab.id}-tab">
              ${tab.content}
            </div>
          `
            )
            .join("")}
        </div>
      </div>
    `;
  }

  // Create floating action buttons
  static createFloatingActions(actions) {
    return `
      <div class="floating-actions">
        <div class="btn-group shadow">
          ${actions
            .map(
              (action) => `
            <button class="btn ${action.class || "btn-primary"}" 
                    onclick="${action.onClick}" 
                    data-bs-toggle="tooltip" 
                    title="${action.tooltip}">
              <i class="las ${action.icon}"></i> ${action.label}
            </button>
          `
            )
            .join("")}
        </div>
      </div>
    `;
  }

  // Create styles for components
  static createStyles() {
    return `
      .settings-tabs .nav-tabs {
        border-bottom-color: var(--bs-border-color);
      }
      .settings-tabs .nav-link {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        color: var(--bs-gray-600);
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
      .settings-tabs .nav-link.active {
        color: var(--bs-primary);
        font-weight: 500;
      }
      .settings-tabs .nav-link i {
        font-size: 1.1em;
      }
      .section-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--bs-gray-700);
        margin: 0;
      }
      .value-display {
        font-size: 0.75rem;
        color: var(--bs-gray-600);
      }
      .floating-actions {
        position: sticky;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1rem;
        background: linear-gradient(transparent, var(--bs-body-bg) 20%);
        text-align: center;
        z-index: 1045;
      }
      .search-box {
        position: sticky;
        top: 0;
        background: var(--bs-body-bg);
        padding: 0.5rem 0;
        z-index: 1;
      }
      .settings-section {
        background: var(--bs-body-bg);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid var(--bs-border-color);
      }
      [data-bs-theme="dark"] .settings-section {
        background: var(--bs-dark);
        border-color: var(--bs-border-color);
      }
      [data-bs-theme="dark"] .floating-actions {
        background: linear-gradient(transparent, var(--bs-dark) 20%);
      }
    `;
  }
}

// Export the components
window.ChartComponents = ChartComponents;
