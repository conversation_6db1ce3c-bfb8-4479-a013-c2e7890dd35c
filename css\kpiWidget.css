/**
 * KPI Widget Styles
 * Modern, clean design with color themes and responsive layout
 */

.kpi-widget-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.kpi-widget-wrapper:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Widget Header */
.kpi-widget-wrapper .widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  border-radius: 12px 12px 0 0;
  min-height: 60px;
}

.kpi-widget-wrapper .widget-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1e293b;
  font-weight: 600;
  font-size: 14px;
}

.kpi-widget-wrapper .widget-title i {
  font-size: 18px;
  color: #64748b;
}

.kpi-widget-wrapper .widget-actions {
  display: flex;
  gap: 8px;
}

.kpi-widget-wrapper .widget-action-btn {
  background: none;
  border: none;
  color: #64748b;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.kpi-widget-wrapper .widget-action-btn:hover {
  background: #e2e8f0;
  color: #1e293b;
}

/* KPI Content */
.kpi-content {
  flex: 1;
  padding: 24px 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.kpi-main {
  display: flex;
  align-items: center;
  gap: 16px;
}

.kpi-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  font-size: 24px;
  flex-shrink: 0;
}

.kpi-info {
  flex: 1;
  min-width: 0;
}

.kpi-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  line-height: 1.2;
}

.kpi-label {
  font-size: 14px;
  color: #64748b;
  margin-top: 4px;
  font-weight: 500;
}

.kpi-trend {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.trend-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
}

.trend-indicator.positive {
  background: #dcfce7;
  color: #166534;
}

.trend-indicator.negative {
  background: #fef2f2;
  color: #dc2626;
}

.trend-indicator.neutral {
  background: #f1f5f9;
  color: #64748b;
}

.trend-indicator i {
  font-size: 12px;
}

/* KPI Details */
.kpi-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.kpi-subtitle {
  font-size: 12px;
  color: #94a3b8;
  font-weight: 500;
}

.kpi-progress {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-bar {
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

/* KPI Settings */
.kpi-settings {
  padding: 20px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.settings-section {
  margin-bottom: 16px;
}

.settings-section label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
}

.settings-section .form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  background: white;
  transition: border-color 0.2s ease;
}

.settings-section .form-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.settings-actions {
  display: flex;
  gap: 8px;
  margin-top: 20px;
}

.settings-actions .btn {
  flex: 1;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.settings-actions .btn-primary {
  background: #3b82f6;
  color: white;
}

.settings-actions .btn-primary:hover {
  background: #2563eb;
}

.settings-actions .btn-secondary {
  background: #e5e7eb;
  color: #374151;
}

.settings-actions .btn-secondary:hover {
  background: #d1d5db;
}

/* Color Themes */
.kpi-widget-wrapper.kpi-blue .kpi-icon {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.kpi-widget-wrapper.kpi-blue .progress-fill {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.kpi-widget-wrapper.kpi-green .kpi-icon {
  background: linear-gradient(135deg, #10b981, #059669);
}

.kpi-widget-wrapper.kpi-green .progress-fill {
  background: linear-gradient(90deg, #10b981, #059669);
}

.kpi-widget-wrapper.kpi-orange .kpi-icon {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.kpi-widget-wrapper.kpi-orange .progress-fill {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.kpi-widget-wrapper.kpi-red .kpi-icon {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.kpi-widget-wrapper.kpi-red .progress-fill {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.kpi-widget-wrapper.kpi-purple .kpi-icon {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.kpi-widget-wrapper.kpi-purple .progress-fill {
  background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.kpi-widget-wrapper.kpi-teal .kpi-icon {
  background: linear-gradient(135deg, #14b8a6, #0d9488);
}

.kpi-widget-wrapper.kpi-teal .progress-fill {
  background: linear-gradient(90deg, #14b8a6, #0d9488);
}

/* Responsive Design */
@media (max-width: 768px) {
  .kpi-content {
    padding: 16px;
  }

  .kpi-main {
    gap: 12px;
  }

  .kpi-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .kpi-value {
    font-size: 20px;
  }

  .kpi-label {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .kpi-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .kpi-trend {
    align-self: flex-end;
  }

  .kpi-value {
    font-size: 18px;
  }
}

/* Animation for spinning refresh icon */
.fa-spin {
  animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
