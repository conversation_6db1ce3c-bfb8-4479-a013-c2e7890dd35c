let curvedLineMapChartCounter = 0;

// Add a Curved Line Map Chart widget using amCharts v5
function addCurvedLineMapWidget(initialConfig = {}) {
  const widgetId = `curved-line-map-${curvedLineMapChartCounter++}`;
  const chartContainerId = `curved-line-map-container-${widgetId}`;
  const settingsId = `curvedLineMapSettings-${widgetId}`;

  // Default data structure for cities and connections
  const defaultData = {
    origin: {
      id: "london",
      title: "London",
      coordinates: [-0.1262, 51.5002],
    },
    destinations: [
      {
        id: "new-york",
        title: "New York",
        coordinates: [-74, 40.43],
      },
      {
        id: "paris",
        title: "Paris",
        coordinates: [2.351, 48.8567],
      },
      {
        id: "tokyo",
        title: "Tokyo",
        coordinates: [139.6917, 35.6895],
      },
    ],
  };

  const config = {
    title: initialConfig.title || "Connection Map",
    data: initialConfig.data || defaultData,
    notes:
      initialConfig.notes ||
      "Shows connections from origin city to destinations",
    source: initialConfig.source || "Default Source",
    lastUpdate: initialConfig.lastUpdate || "N/A",
    nextUpdate: initialConfig.nextUpdate || "N/A",
  };

  const grid = window.grid;
  const newWidget = grid.addWidget({
    w: initialConfig.w || 12,
    h: initialConfig.h || 12,
    content: `
      <div class="curved-line-map-widget widget p-2" id="${widgetId}" style="height: 100%; display: flex; flex-direction: column;">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div class="widget-title editable-title" data-editable="true" title="Click to edit title">
              <span>${config.title}</span>
          </div>
          <div class="widget-actions">
            <button class="btn btn-link" data-bs-toggle="offcanvas" data-bs-target="#${settingsId}" aria-controls="${settingsId}">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-link ms-1" onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex;">
          <div id="${chartContainerId}" class="chart-container" style="width: 100%; height: 100%; position: relative; min-height: 300px;"></div>
        </div>
        <div class="widget-footer mt-2" style="padding: 0.5rem; border-top: 1px solid #e5e9f0; font-size: 10px; color: #6c757d;">
          ${
            config.notes
              ? `<div class="d-flex align-items-center"><i class="las la-clipboard me-1"></i><span>Notes: ${config.notes}</span></div>`
              : ""
          }
          ${
            config.source
              ? `<div class="d-flex align-items-center mt-1"><i class="las la-database me-1"></i><span>Source: ${config.source}</span></div>`
              : ""
          }
          ${
            config.lastUpdate || config.nextUpdate
              ? `
          <div class="d-flex mt-1">
            ${
              config.lastUpdate
                ? `<div class="d-flex align-items-center"><i class="las la-calendar-alt me-1"></i><span>Last update: ${config.lastUpdate}</span></div>`
                : ""
            }
            ${
              config.nextUpdate
                ? `<div class="d-flex align-items-center ms-3"><i class="las la-calendar-plus me-1"></i><span>Next update: ${config.nextUpdate}</span></div>`
                : ""
            }
          </div>`
              : ""
          }
        </div>
      </div>
    `,
  });

  const widgetElement = document.getElementById(widgetId);
  widgetElement.widgetConfig = config;
  widgetElement.chartContainerId = chartContainerId;

  // Initialize the chart
  setTimeout(() => {
    if (typeof am5 !== "undefined") {
      initializeCurvedLineMap(chartContainerId, config);
    } else {
      console.error("amCharts library not loaded properly");
    }
  }, 100);

  // Make title editable
  if (typeof makeTitleEditable === "function") {
    makeTitleEditable(widgetId);
  }

  // Create settings panel
  createCurvedLineMapSettingsOffcanvas(
    settingsId,
    widgetId,
    chartContainerId,
    config
  );
}

function initializeCurvedLineMap(chartContainerId, config) {
  const chartContainerElement = document.getElementById(chartContainerId);
  if (!chartContainerElement) return;

  // Dispose existing chart if any
  if (chartContainerElement.amRoot) {
    chartContainerElement.amRoot.dispose();
  }

  // Create root element
  const root = am5.Root.new(chartContainerId);
  chartContainerElement.amRoot = root;

  // Set themes
  root.setThemes([am5themes_Animated.new(root)]);

  // Create the map chart
  const chart = root.container.children.push(
    am5map.MapChart.new(root, {
      panX: "translateX",
      panY: "translateY",
      projection: am5map.geoMercator(),
    })
  );

  // Add switch control container
  const cont = chart.children.push(
    am5.Container.new(root, {
      layout: root.horizontalLayout,
      x: 20,
      y: 40,
    })
  );

  // Add labels and controls
  cont.children.push(
    am5.Label.new(root, {
      centerY: am5.p50,
      text: "Map",
    })
  );

  const switchButton = cont.children.push(
    am5.Button.new(root, {
      themeTags: ["switch"],
      centerY: am5.p50,
      icon: am5.Circle.new(root, {
        themeTags: ["icon"],
      }),
    })
  );

  cont.children.push(
    am5.Label.new(root, {
      centerY: am5.p50,
      text: "Globe",
    })
  );

  // Switch between map and globe view
  switchButton.on("active", function () {
    if (!switchButton.get("active")) {
      chart.set("projection", am5map.geoMercator());
      chart.set("panX", "translateX");
      chart.set("panY", "translateY");
    } else {
      chart.set("projection", am5map.geoOrthographic());
      chart.set("panX", "rotateX");
      chart.set("panY", "rotateY");
    }
  });

  // Create main polygon series for countries
  const polygonSeries = chart.series.push(
    am5map.MapPolygonSeries.new(root, {
      geoJSON: am5geodata_worldLow,
      fill: am5.color("#e9ecef"),
      stroke: am5.color("#ffffff"),
    })
  );

  polygonSeries.mapPolygons.template.setAll({
    fillOpacity: 0.9,
    strokeWidth: 0.5,
    interactive: true,
  });

  // Create hover state for countries
  polygonSeries.mapPolygons.template.states.create("hover", {
    fill: am5.color("#f8f9fa"),
  });

  // Add graticule series (grid lines)
  const graticuleSeries = chart.series.push(
    am5map.GraticuleSeries.new(root, {})
  );
  graticuleSeries.mapLines.template.setAll({
    stroke: root.interfaceColors.get("alternativeBackground"),
    strokeOpacity: 0.1,
  });

  // Create line series for trajectory lines
  const lineSeries = chart.series.push(am5map.MapLineSeries.new(root, {}));
  lineSeries.mapLines.template.setAll({
    stroke: am5.color("#00c8b3"),
    strokeOpacity: 0.6,
    strokeWidth: 2,
  });

  // Create point series for cities
  const citySeries = chart.series.push(am5map.MapPointSeries.new(root, {}));

  // Configure city points
  citySeries.bullets.push(function () {
    const circle = am5.Circle.new(root, {
      radius: 5,
      tooltipText: "{title}",
      tooltipY: 0,
      fill: am5.color("#00c8b3"),
      stroke: root.interfaceColors.get("background"),
      strokeWidth: 2,
    });

    return am5.Bullet.new(root, {
      sprite: circle,
    });
  });

  // Create arrow series
  const arrowSeries = chart.series.push(am5map.MapPointSeries.new(root, {}));

  // Configure arrows
  arrowSeries.bullets.push(function () {
    const arrow = am5.Graphics.new(root, {
      fill: am5.color("#00c8b3"),
      stroke: am5.color("#00c8b3"),
      draw: function (display) {
        display.moveTo(0, -3);
        display.lineTo(8, 0);
        display.lineTo(0, 3);
        display.lineTo(0, -3);
      },
    });

    return am5.Bullet.new(root, {
      sprite: arrow,
    });
  });

  // Prepare cities data
  const cities = [config.data.origin];
  config.data.destinations.forEach((dest) => cities.push(dest));

  // Format cities for the series
  const formattedCities = cities.map((city) => ({
    id: city.id,
    title: city.title,
    geometry: {
      type: "Point",
      coordinates: city.coordinates,
    },
  }));

  citySeries.data.setAll(formattedCities);

  // Create lines from origin to each destination
  config.data.destinations.forEach((destination) => {
    const lineDataItem = lineSeries.pushDataItem({
      geometry: {
        type: "LineString",
        coordinates: [config.data.origin.coordinates, destination.coordinates],
      },
    });

    arrowSeries.pushDataItem({
      lineDataItem: lineDataItem,
      positionOnLine: 0.5,
      autoRotate: true,
    });
  });

  // Set initial zoom to origin city
  polygonSeries.events.on("datavalidated", function () {
    chart.zoomToGeoPoint(
      {
        longitude: config.data.origin.coordinates[0],
        latitude: config.data.origin.coordinates[1],
      },
      2
    );
  });

  // Make stuff animate on load
  chart.appear(1000, 100);

  // Add resize observer
  const resizeObserver = new ResizeObserver(() => {
    root.resize();
  });

  resizeObserver.observe(chartContainerElement);
  chartContainerElement.resizeObserver = resizeObserver;
}

function createCurvedLineMapSettingsOffcanvas(
  settingsId,
  widgetId,
  chartContainerId,
  currentConfig
) {
  const offcanvasContainer =
    document.getElementById("offcanvasContainer") || document.body;
  const dataJson = JSON.stringify(currentConfig.data, null, 2);

  const offcanvasHtml = `
    <div class="offcanvas offcanvas-end" tabindex="-1" id="${settingsId}" aria-labelledby="${settingsId}Label">
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="${settingsId}Label">Curved Line Map Settings</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div class="offcanvas-body">
        <div class="mb-3">
          <label class="form-label">Chart Title</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-chartTitle" value="${currentConfig.title}">
        </div>

        <div class="mb-3">
          <label class="form-label">Data (JSON format)</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-data" rows="10" spellcheck="false">${dataJson}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Notes</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-notes" rows="2">${currentConfig.notes}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Source</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-source" rows="2">${currentConfig.source}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Last Update</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-lastUpdate" value="${currentConfig.lastUpdate}">
        </div>

        <div class="mb-3">
          <label class="form-label">Next Update</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-nextUpdate" value="${currentConfig.nextUpdate}">
        </div>

        <button class="btn btn-primary w-100" onclick="applyCurvedLineMapSettings('${widgetId}', '${settingsId}', '${chartContainerId}')">Apply Changes</button>
      </div>
    </div>
  `;

  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = offcanvasHtml;
  offcanvasContainer.appendChild(tempDiv.firstElementChild);
}

function applyCurvedLineMapSettings(widgetId, settingsId, chartContainerId) {
  const widgetElement = document.getElementById(widgetId);
  if (!widgetElement || !widgetElement.widgetConfig) return;

  const currentConfig = widgetElement.widgetConfig;

  try {
    currentConfig.title = document.getElementById(
      `${settingsId}-chartTitle`
    ).value;
    const dataInput = document.getElementById(`${settingsId}-data`).value;
    currentConfig.data = JSON.parse(dataInput);
    currentConfig.notes = document.getElementById(`${settingsId}-notes`).value;
    currentConfig.source = document.getElementById(
      `${settingsId}-source`
    ).value;
    currentConfig.lastUpdate = document.getElementById(
      `${settingsId}-lastUpdate`
    ).value;
    currentConfig.nextUpdate = document.getElementById(
      `${settingsId}-nextUpdate`
    ).value;

    // Update widget title
    const titleSpan = widgetElement.querySelector(".widget-title span");
    if (titleSpan) {
      titleSpan.textContent = currentConfig.title;
    }

    // Update footer
    if (typeof updateWidgetFooter === "function") {
      updateWidgetFooter(widgetElement, currentConfig);
    }

    // Re-initialize chart
    initializeCurvedLineMap(chartContainerId, currentConfig);

    // Close settings panel
    const offcanvasElement = document.getElementById(settingsId);
    if (offcanvasElement) {
      const offcanvasInstance =
        bootstrap.Offcanvas.getInstance(offcanvasElement);
      if (offcanvasInstance) {
        offcanvasInstance.hide();
      }
    }
  } catch (e) {
    console.error("Error applying settings:", e);
    alert("Error: Invalid JSON format in Data. Please correct and try again.");
  }
}

// Export functions to global scope
window.addCurvedLineMapWidget = addCurvedLineMapWidget;
window.applyCurvedLineMapSettings = applyCurvedLineMapSettings;
