/* Dropdown-Based Widget Assignment Interface */
.dropdown-widget-assignment {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* New Simplified Widget Assignment Interface */
.simple-widget-assignment {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  padding: 0;
}

.simple-widget-assignment .assignment-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  border-radius: 12px 12px 0 0;
  margin-bottom: 1.5rem;
}

.simple-widget-assignment .assignment-title h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.simple-widget-assignment .assignment-title p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

.simple-widget-assignment .assignment-stats {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.simple-widget-assignment .stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.simple-widget-assignment .stat-item i {
  font-size: 1rem;
}

/* Widget Assignment Area */
.widget-assignment-area {
  padding: 0;
  background: #f8fafc;
  border-radius: 12px;
  overflow: hidden;
}

.widget-assignment-grid {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Tab Assignment List */
.tab-assignment-list {
  space-y: 1rem;
}

.tab-assignment-item {
  background: #fff;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  margin-bottom: 1rem;
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
}

.tab-assignment-item:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.tab-assignment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.tab-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.tab-info i {
  font-size: 1.25rem;
}

.tab-details h5 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

.tab-details .widget-count {
  font-size: 0.8rem;
  color: #6b7280;
  margin-top: 0.1rem;
}

.add-widget-btn {
  background: #3b82f6;
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.add-widget-btn:hover {
  background: #2563eb;
  transform: scale(1.05);
}

/* Tab Widgets List */
.tab-widgets-list {
  padding: 0 1.25rem 1rem 1.25rem;
}

.no-widgets {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  font-size: 0.9rem;
  padding: 1rem 0;
  justify-content: center;
}

.no-widgets i {
  font-size: 1.1rem;
}

/* Assigned Widget Items */
.assigned-widget-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  transition: all 0.2s ease;
}

.assigned-widget-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.assigned-widget-item:last-child {
  margin-bottom: 0;
}

.widget-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
}

.widget-info {
  flex: 1;
}

.widget-title {
  display: block;
  font-weight: 500;
  color: #1f2937;
  font-size: 0.9rem;
  line-height: 1.3;
}

.widget-type {
  display: block;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.1rem;
}

.remove-btn {
  background: #ef4444;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.8rem;
  opacity: 0.7;
}

.remove-btn:hover {
  background: #dc2626;
  opacity: 1;
  transform: scale(1.1);
}

/* Quick Actions */
.assignment-quick-actions {
  display: flex;
  gap: 0.75rem;
  margin: 1.5rem 0;
  padding: 0 0.5rem;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: none;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn.secondary {
  background: #10b981;
  color: white;
}

.action-btn.secondary:hover {
  background: #059669;
}

.action-btn.outline {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.action-btn.outline:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

/* Unassigned Widgets */
.unassigned-widgets {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.unassigned-widgets h6 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 0.9rem;
  font-weight: 600;
}

.unassigned-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 0.75rem;
}

.unassigned-widget-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.unassigned-widget-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.quick-assign-btn {
  background: #f3f4f6;
  color: #6b7280;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.quick-assign-btn:hover {
  background: #3b82f6;
  color: white;
  transform: scale(1.1);
}

/* Tab Widget Selector Styles */
.tab-widget-selector {
  position: fixed !important;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
  z-index: 99999999 !important;
  min-width: 300px;
  max-width: 400px;
  max-height: 400px;
  overflow: hidden;
  pointer-events: auto !important;
  display: block !important;
}

/* Ensure the selector and its children are interactive */
.tab-widget-selector,
.tab-widget-selector * {
  pointer-events: auto !important;
}

/* Position the selector near the add button */
.tab-assignment-item {
  position: relative;
}

.tab-widget-selector-header {
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  position: relative;
  z-index: 99999999 !important;
}

.tab-widget-selector-header h6 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: #374151;
}

.tab-widget-selector-search {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  z-index: 99999999 !important;
}

.tab-widget-selector-search input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.85rem;
}

.tab-widget-selector-items {
  max-height: 250px;
  overflow-y: auto;
  padding: 0.5rem 0;
  position: relative;
  z-index: 99999999 !important;
}

.tab-widget-selector-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  position: relative;
  z-index: 99999999 !important;
}

.tab-widget-selector-item:hover {
  background: #f8fafc;
}

.tab-widget-selector-item.assigned {
  opacity: 0.6;
  cursor: not-allowed;
}

.tab-widget-selector-item.assigned:hover {
  background: #f9fafb;
}

/* Empty State */
.assignment-empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: #6b7280;
}

.empty-state-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.assignment-empty-state h4 {
  margin: 0 0 0.5rem 0;
  color: #374151;
}

.assignment-empty-state p {
  margin: 0 0 1.5rem 0;
  font-size: 0.9rem;
}

.assignment-empty-state .btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.assignment-empty-state .btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* Assignment Header */
.assignment-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.assignment-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.assignment-title i {
  font-size: 24px;
}

.assignment-title h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.assignment-stats {
  display: flex;
  gap: 12px;
}

.stat-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.stat-badge.total {
  background: rgba(255, 255, 255, 0.25);
}

.stat-badge.success {
  background: rgba(34, 197, 94, 0.8);
}

.stat-badge.pending {
  background: rgba(251, 191, 36, 0.8);
}

/* Assignment Actions */
.assignment-actions {
  padding: 24px 32px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  gap: 12px;
  align-items: center;
}

.action-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn.primary {
  background: #3b82f6;
  color: white;
}

.action-btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: #e2e8f0;
  color: #64748b;
}

.action-btn.secondary:hover {
  background: #cbd5e1;
}

.assignment-filter {
  margin-left: auto;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
}

/* Assignment Tips */
.assignment-tips {
  padding: 20px 32px;
  background: #f0f7ff;
  border-left: 4px solid #3b82f6;
  display: flex;
  gap: 12px;
}

.assignment-tips-icon {
  color: #3b82f6;
  font-size: 20px;
}

.assignment-tips-content h6 {
  margin: 0 0 4px 0;
  color: #1e40af;
  font-weight: 600;
}

.assignment-tips-content p {
  margin: 0;
  color: #64748b;
  font-size: 14px;
}

/* Widget Assignment Grid */
.widget-assignment-grid {
  padding: 32px;
}

.widget-category-section {
  margin-bottom: 32px;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.category-header h5 {
  margin: 0;
  color: #374151;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-count {
  background: #f3f4f6;
  color: #6b7280;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.widget-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

/* Dropdown Widget Cards */
.dropdown-widget-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
}

.dropdown-widget-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.dropdown-widget-card.assigned {
  border-color: #10b981;
  background: #f0fdf4;
}

.widget-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.dropdown-widget-card.assigned .widget-icon {
  background: #d1fae5;
  color: #10b981;
}

.widget-info {
  flex: 1;
}

.widget-info h6 {
  margin: 0 0 4px 0;
  font-weight: 600;
  color: #111827;
}

.widget-type {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
}

.assignment-status {
  font-size: 20px;
}

/* Assignment Controls */
.widget-assignment-control {
  margin-bottom: 16px;
}

.assignment-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.assignment-dropdown-container {
  display: flex;
  gap: 8px;
  align-items: center;
}

.assignment-dropdown {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.assignment-dropdown:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.unassign-btn {
  padding: 8px;
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.unassign-btn:hover {
  background: #fecaca;
  transform: scale(1.05);
}

/* Widget Preview Info */
.widget-preview-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.widget-features {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.feature-tag {
  background: #f1f5f9;
  color: #475569;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.suggestion-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #059669;
  font-size: 12px;
  font-weight: 500;
  background: #ecfdf5;
  padding: 6px 10px;
  border-radius: 6px;
}

.suggestion-hint i {
  font-size: 14px;
}

/* Assignment Preview */
.assignment-preview {
  padding: 24px 32px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.assignment-preview h6 {
  margin: 0 0 16px 0;
  color: #374151;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Dark Theme Support */
.dark-theme .dropdown-widget-assignment {
  background: #1f2937;
  color: #f9fafb;
}

.dark-theme .assignment-actions {
  background: #111827;
  border-color: #374151;
}

.dark-theme .assignment-tips {
  background: #1e3a8a;
  border-color: #3b82f6;
}

.dark-theme .dropdown-widget-card {
  background: #374151;
  border-color: #4b5563;
}

.dark-theme .dropdown-widget-card.assigned {
  background: #064e3b;
  border-color: #10b981;
}

.dark-theme .assignment-dropdown {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .assignment-header {
    padding: 16px 24px;
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .assignment-actions {
    padding: 16px 24px;
  }

  .widget-cards-grid {
    grid-template-columns: 1fr;
  }

  .assignment-dropdown-container {
    flex-direction: column;
  }

  /* Responsive styles for simplified interface */
  .simple-widget-assignment .assignment-header {
    padding: 1rem;
  }

  .simple-widget-assignment .assignment-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .assignment-quick-actions {
    flex-direction: column;
  }

  .unassigned-list {
    grid-template-columns: 1fr;
  }

  .tab-assignment-header {
    padding: 0.75rem 1rem;
  }

  .tab-widgets-list {
    padding: 0 1rem 0.75rem 1rem;
  }

  .tab-widget-selector {
    min-width: 280px;
    max-width: 90vw;
  }
}
