/**
 * Pie Chart Compact Mode
 * Provides functionality to toggle between normal and compact modes for pie charts
 */

// Initialize compact mode functionality when the document is ready
document.addEventListener('DOMContentLoaded', function() {
  initializePieChartCompactMode();
});

// Initialize compact mode for pie charts
function initializePieChartCompactMode() {
  // Add compact mode toggle button to all pie charts
  addCompactModeToggleToPieCharts();
  
  // Listen for new pie charts being added to the dashboard
  observeNewPieCharts();
}

// Add compact mode toggle button to all pie charts
function addCompactModeToggleToPieCharts() {
  // Find all pie chart widgets
  const pieChartWidgets = document.querySelectorAll('.widget:has(.widget-title i.la-chart-pie)');
  
  pieChartWidgets.forEach(widget => {
    addCompactModeToggleToWidget(widget);
  });
}

// Add compact mode toggle to a specific widget
function addCompactModeToggleToWidget(widget) {
  // Skip if this widget already has a compact mode toggle
  if (widget.querySelector('.compact-mode-toggle')) {
    return;
  }
  
  // Get widget header and actions
  const widgetHeader = widget.querySelector('.widget-header');
  const widgetActions = widget.querySelector('.widget-actions');
  const widgetTitle = widget.querySelector('.widget-title');
  
  if (!widgetHeader || !widgetActions || !widgetTitle) {
    return;
  }
  
  // Create compact mode toggle button
  const toggleButton = document.createElement('div');
  toggleButton.className = 'compact-mode-toggle';
  toggleButton.innerHTML = '<i class="las la-compress-arrows-alt"></i>';
  toggleButton.title = 'Toggle compact mode';
  
  // Add toggle button to widget
  widget.appendChild(toggleButton);
  
  // Add click event to toggle button
  toggleButton.addEventListener('click', function(e) {
    e.stopPropagation(); // Prevent event bubbling
    toggleCompactMode(widget);
  });
}

// Toggle compact mode for a widget
function toggleCompactMode(widget) {
  // Toggle compact mode class
  widget.classList.toggle('pie-chart-compact-mode');
  
  // Get toggle button
  const toggleButton = widget.querySelector('.compact-mode-toggle');
  
  if (widget.classList.contains('pie-chart-compact-mode')) {
    // Switching to compact mode
    toggleButton.innerHTML = '<i class="las la-expand-arrows-alt"></i>';
    toggleButton.title = 'Exit compact mode';
    
    // Create floating actions if they don't exist
    if (!widget.querySelector('.floating-actions')) {
      createFloatingActions(widget);
    }
    
    // Create floating title if it doesn't exist
    if (!widget.querySelector('.floating-title')) {
      createFloatingTitle(widget);
    }
  } else {
    // Switching back to normal mode
    toggleButton.innerHTML = '<i class="las la-compress-arrows-alt"></i>';
    toggleButton.title = 'Toggle compact mode';
    
    // Remove floating elements
    const floatingActions = widget.querySelector('.floating-actions');
    const floatingTitle = widget.querySelector('.floating-title');
    
    if (floatingActions) {
      floatingActions.remove();
    }
    
    if (floatingTitle) {
      floatingTitle.remove();
    }
  }
  
  // Force a resize event to ensure charts render properly
  setTimeout(() => {
    window.dispatchEvent(new Event('resize'));
  }, 300);
}

// Create floating actions for compact mode
function createFloatingActions(widget) {
  // Get widget actions
  const widgetActions = widget.querySelector('.widget-actions');
  
  if (!widgetActions) {
    return;
  }
  
  // Create floating actions container
  const floatingActions = document.createElement('div');
  floatingActions.className = 'floating-actions';
  
  // Get settings button
  const settingsButton = widgetActions.querySelector('button[data-bs-toggle="offcanvas"]');
  
  if (settingsButton) {
    // Create floating settings button
    const floatingSettingsButton = document.createElement('div');
    floatingSettingsButton.className = 'floating-action-button settings';
    floatingSettingsButton.innerHTML = '<i class="las la-cog"></i>';
    floatingSettingsButton.title = 'Settings';
    
    // Copy onclick attribute and data attributes
    floatingSettingsButton.setAttribute('data-bs-toggle', settingsButton.getAttribute('data-bs-toggle'));
    floatingSettingsButton.setAttribute('data-bs-target', settingsButton.getAttribute('data-bs-target'));
    floatingSettingsButton.setAttribute('aria-controls', settingsButton.getAttribute('aria-controls'));
    floatingSettingsButton.setAttribute('onclick', settingsButton.getAttribute('onclick'));
    
    // Add to floating actions
    floatingActions.appendChild(floatingSettingsButton);
  }
  
  // Get close button
  const closeButton = widgetActions.querySelector('button[onclick*="removeWidget"]');
  
  if (closeButton) {
    // Create floating close button
    const floatingCloseButton = document.createElement('div');
    floatingCloseButton.className = 'floating-action-button close';
    floatingCloseButton.innerHTML = '<i class="las la-times"></i>';
    floatingCloseButton.title = 'Remove widget';
    
    // Copy onclick attribute
    floatingCloseButton.setAttribute('onclick', closeButton.getAttribute('onclick'));
    
    // Add to floating actions
    floatingActions.appendChild(floatingCloseButton);
  }
  
  // Add floating actions to widget
  widget.appendChild(floatingActions);
}

// Create floating title for compact mode
function createFloatingTitle(widget) {
  // Get widget title
  const widgetTitle = widget.querySelector('.widget-title');
  
  if (!widgetTitle) {
    return;
  }
  
  // Get title text and icon
  const titleText = widgetTitle.querySelector('span')?.textContent || 'Pie Chart';
  const titleIcon = widgetTitle.querySelector('i')?.outerHTML || '<i class="las la-chart-pie"></i>';
  
  // Create floating title
  const floatingTitle = document.createElement('div');
  floatingTitle.className = 'floating-title';
  floatingTitle.innerHTML = `${titleIcon} <span>${titleText}</span>`;
  
  // Add floating title to widget
  widget.appendChild(floatingTitle);
}

// Observe for new pie charts being added to the dashboard
function observeNewPieCharts() {
  // Create a mutation observer to watch for new widgets
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      // Check if new nodes were added
      if (mutation.addedNodes.length) {
        mutation.addedNodes.forEach(function(node) {
          // Check if the added node is an element
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if it's a pie chart widget
            if (node.querySelector && node.querySelector('.widget-title i.la-chart-pie')) {
              addCompactModeToggleToWidget(node);
            }
            
            // Check if it contains pie chart widgets
            const pieChartWidgets = node.querySelectorAll && node.querySelectorAll('.widget:has(.widget-title i.la-chart-pie)');
            if (pieChartWidgets && pieChartWidgets.length) {
              pieChartWidgets.forEach(widget => {
                addCompactModeToggleToWidget(widget);
              });
            }
          }
        });
      }
    });
  });
  
  // Start observing the grid container
  const gridContainer = document.getElementById('grid-container');
  if (gridContainer) {
    observer.observe(gridContainer, { childList: true, subtree: true });
  }
}

// Function to toggle compact mode for all pie charts
function toggleAllPieChartsCompactMode(compact = true) {
  const pieChartWidgets = document.querySelectorAll('.widget:has(.widget-title i.la-chart-pie)');
  
  pieChartWidgets.forEach(widget => {
    const isCompact = widget.classList.contains('pie-chart-compact-mode');
    
    if ((compact && !isCompact) || (!compact && isCompact)) {
      toggleCompactMode(widget);
    }
  });
}
