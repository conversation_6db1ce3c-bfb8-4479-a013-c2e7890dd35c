let dynamicPieMapWidgetCount = 0;

// Wait for amCharts to be fully loaded
function waitForAmCharts() {
  return new Promise((resolve, reject) => {
    if (
      typeof am5 !== "undefined" &&
      typeof am5map !== "undefined" &&
      typeof am5themes_Animated !== "undefined" &&
      typeof am5geodata_worldLow !== "undefined" &&
      typeof am5percent !== "undefined"
    ) {
      resolve();
    } else {
      const timeout = setTimeout(() => {
        reject(new Error("amCharts failed to load within timeout"));
      }, 5000);

      const checkInterval = setInterval(() => {
        if (
          typeof am5 !== "undefined" &&
          typeof am5map !== "undefined" &&
          typeof am5themes_Animated !== "undefined" &&
          typeof am5geodata_worldLow !== "undefined" &&
          typeof am5percent !== "undefined"
        ) {
          clearInterval(checkInterval);
          clearTimeout(timeout);
          resolve();
        }
      }, 100);
    }
  });
}

async function addDynamicPieMapWidget() {
  try {
    await waitForAmCharts();

    const widgetId = `dynamic-pie-map-widget-${++dynamicPieMapWidgetCount}`;
    const chartContainerId = `dynamic-pie-map-chart-${dynamicPieMapWidgetCount}`;
    const settingsId = `dynamic-pie-map-settings-${dynamicPieMapWidgetCount}`;

    // Create widget container
    const widgetContainer = document.createElement("div");
    widgetContainer.className = "widget";
    widgetContainer.id = widgetId;
    widgetContainer.style.height = "100%";
    widgetContainer.style.display = "flex";
    widgetContainer.style.flexDirection = "column";
    widgetContainer.style.overflow = "hidden";

    // Create widget header
    const widgetHeader = document.createElement("div");
    widgetHeader.className =
      "widget-header mb-2 fw-bold d-flex justify-content-between align-items-center";

    const titleContainer = document.createElement("div");
    titleContainer.className = "widget-title editable-title";
    titleContainer.setAttribute("data-editable", "true");
    titleContainer.setAttribute("title", "Click to edit title");
    titleContainer.innerHTML =
      '<span>Dynamic Pie Map</span>';

    const actionsContainer = document.createElement("div");
    actionsContainer.className = "widget-actions";
    actionsContainer.innerHTML = `
      <button class="btn btn-link" data-bs-toggle="offcanvas" data-bs-target="#${settingsId}" aria-controls="${settingsId}">
        <i class="las la-cog"></i>
      </button>
      <button class="btn btn-link ms-1" onclick="removeWidget(this)">
        <i class="las la-times"></i>
      </button>
    `;

    widgetHeader.appendChild(titleContainer);
    widgetHeader.appendChild(actionsContainer);

    // Create widget body
    const widgetBody = document.createElement("div");
    widgetBody.className = "widget-body";
    widgetBody.style.flex = "1 1 auto";
    widgetBody.style.minHeight = "0";
    widgetBody.style.position = "relative";
    widgetBody.style.display = "flex";
    widgetBody.style.overflow = "hidden";

    const chartContainer = document.createElement("div");
    chartContainer.id = chartContainerId;
    chartContainer.className = "chart-container";
    chartContainer.style.width = "100%";
    chartContainer.style.height = "100%";
    chartContainer.style.position = "absolute";
    chartContainer.style.top = "0";
    chartContainer.style.left = "0";
    chartContainer.style.right = "0";
    chartContainer.style.bottom = "0";

    widgetBody.appendChild(chartContainer);
    widgetContainer.appendChild(widgetHeader);
    widgetContainer.appendChild(widgetBody);

    // Create settings panel
    const settingsPanel = document.createElement("div");
    settingsPanel.className = "offcanvas offcanvas-end";
    settingsPanel.id = settingsId;
    settingsPanel.setAttribute("tabindex", "-1");
    settingsPanel.setAttribute("aria-labelledby", `${settingsId}-label`);

    settingsPanel.innerHTML = `
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="${settingsId}-label">Dynamic Pie Map Settings</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div class="offcanvas-body">
        <div class="mb-3">
          <label class="form-label">Chart Title</label>
          <input type="text" class="form-control" id="${settingsId}-title" value="Dynamic Pie Map">
        </div>
        <div class="mb-3">
          <label class="form-label">Notes</label>
          <textarea class="form-control" id="${settingsId}-notes"></textarea>
        </div>
        <div class="mb-3">
          <label class="form-label">Source</label>
          <input type="text" class="form-control" id="${settingsId}-source">
        </div>
        <div class="mb-3">
          <label class="form-label">Last Update</label>
          <input type="text" class="form-control" id="${settingsId}-lastUpdate" readonly>
        </div>
        <button class="btn btn-primary" onclick="applyDynamicPieMapSettings('${widgetId}', '${settingsId}', '${chartContainerId}')">
          Apply Changes
        </button>
      </div>
    `;

    document.body.appendChild(settingsPanel);

    // Add widget to grid
    const widget = window.grid.addWidget({
      w: 12,
      h: 12,
      content: widgetContainer.outerHTML,
    });

    // Initialize chart after container is ready
    setTimeout(async () => {
      try {
        const container = document.getElementById(chartContainerId);
        if (!container) {
          throw new Error("Chart container not found after adding to grid");
        }
        await initializeDynamicPieMap(chartContainerId);
      } catch (error) {
        console.error("Failed to initialize dynamic pie map:", error);
        const container = document.getElementById(chartContainerId);
        if (container) {
          container.innerHTML =
            '<div class="alert alert-danger">Failed to load chart. Please check console for details.</div>';
        }
      }
    }, 100);
  } catch (error) {
    console.error("Failed to load amCharts dependencies:", error);
  }
}

function initializeDynamicPieMap(chartContainerId) {
  // Check for required dependencies
  if (!am5) throw new Error("amCharts (am5) is not loaded");
  if (!am5map) throw new Error("amCharts map module is not loaded");
  if (!am5themes_Animated)
    throw new Error("amCharts animated theme is not loaded");
  if (!am5geodata_worldLow)
    throw new Error("amCharts world map data is not loaded");
  if (!am5percent) throw new Error("amCharts percent module is not loaded");

  const container = document.getElementById(chartContainerId);
  if (!container) {
    throw new Error(`Chart container ${chartContainerId} not found`);
  }

  // Create root element
  const root = am5.Root.new(chartContainerId);
  if (!root) {
    throw new Error("Failed to create amCharts root element");
  }

  root.setThemes([am5themes_Animated.new(root)]);

  // Create the map chart
  const chart = root.container.children.push(
    am5map.MapChart.new(root, {
      panX: "rotateX",
      panY: "translateY",
      projection: am5map.geoMercator(),
      homeZoomLevel: 2,
      maxZoomLevel: 16,
    })
  );

  // Create polygon series for the background
  const backgroundSeries = chart.series.push(
    am5map.MapPolygonSeries.new(root, {
      geoJSON: am5geodata_worldLow,
      exclude: ["AQ"],
      fill: am5.color("#eee"),
      stroke: am5.color("#fff"),
    })
  );

  // Create main polygon series for regions
  const polygonSeries = chart.series.push(
    am5map.MapPolygonSeries.new(root, {
      geoJSON: am5geodata_worldLow,
      exclude: ["AQ"],
      fill: am5.color("#67B7DC"),
      stroke: am5.color("#fff"),
      visible: false,
    })
  );

  // Create point series for pie charts
  const pointSeries = chart.series.push(
    am5map.MapPointSeries.new(root, {
      latitudeField: "latitude",
      longitudeField: "longitude",
    })
  );

  // Sample data for regions
  const pieData = [
    {
      latitude: 40,
      longitude: -100,
      title: "North America",
      data: [
        { category: "Category 1", value: 42.9 },
        { category: "Category 2", value: 33.3 },
        { category: "Category 3", value: 23.8 },
      ],
    },
    {
      latitude: 51,
      longitude: 10,
      title: "Europe",
      data: [
        { category: "Category 1", value: 45.7 },
        { category: "Category 2", value: 30.8 },
        { category: "Category 3", value: 23.5 },
      ],
    },
    {
      latitude: 35,
      longitude: 105,
      title: "Asia",
      data: [
        { category: "Category 1", value: 42.9 },
        { category: "Category 2", value: 33.3 },
        { category: "Category 3", value: 23.8 },
      ],
    },
  ];

  // Create pie chart bullet
  pointSeries.bullets.push(function (root, series, dataItem) {
    const container = am5.Container.new(root, {
      width: 150,
      height: 150,
      centerX: am5.p50,
      centerY: am5.p50,
    });

    const pieChart = container.children.push(
      am5percent.PieChart.new(root, {
        width: am5.p100,
        height: am5.p100,
        radius: am5.percent(45),
        layer: 1,
      })
    );

    const pieSeries = pieChart.series.push(
      am5percent.PieSeries.new(root, {
        valueField: "value",
        categoryField: "category",
        alignLabels: false,
      })
    );

    // Remove labels by hiding them
    pieSeries.labels.template.set("forceHidden", true);

    // Add tooltips
    pieSeries.slices.template.setAll({
      stroke: am5.color("#fff"),
      strokeWidth: 2,
      fillOpacity: 0.9,
      tooltipText: "{category}: {valuePercentTotal.formatNumber('0.0')}%",
      layer: 2,
      templateField: "settings",
    });

    // Create hover state
    const hoverState = pieSeries.slices.template.states.create("hover", {
      scale: 1.05,
      fillOpacity: 0.95,
    });

    // Update to brand colors
    pieSeries.set(
      "colors",
      am5.ColorSet.new(root, {
        colors: [
          am5.color("#00b19c"), // Primary brand color
          am5.color("#3bcd3f"), // Secondary brand color
          am5.color("#007365"), // Tertiary brand color
        ],
        step: 1,
      })
    );

    // Add title with better positioning
    container.children.push(
      am5.Label.new(root, {
        text: dataItem.dataContext.title,
        fontSize: 14,
        fontWeight: "600",
        y: am5.p100,
        centerX: am5.p50,
        dy: 20, // Move label below the pie chart
        centerY: am5.p100,
        fill: am5.color("#000"),
        background: am5.RoundedRectangle.new(root, {
          fill: am5.color("#fff"),
          fillOpacity: 0.9,
          cornerRadius: 4,
          paddingTop: 4,
          paddingBottom: 4,
          paddingLeft: 8,
          paddingRight: 8,
        }),
      })
    );

    pieSeries.data.setAll(dataItem.dataContext.data);

    return am5.Bullet.new(root, {
      sprite: container,
    });
  });

  // Add data to series
  pointSeries.data.setAll(pieData);

  // Create and configure legend with brand colors
  const legend = chart.children.push(
    am5.Legend.new(root, {
      centerX: am5.p50,
      x: am5.p50,
      marginTop: 15,
      y: 15,
      layout: root.horizontalLayout,
      background: am5.RoundedRectangle.new(root, {
        fill: am5.color("#fff"),
        fillOpacity: 0.9,
        cornerRadius: 4,
      }),
      paddingTop: 6,
      paddingBottom: 6,
      paddingLeft: 10,
      paddingRight: 10,
    })
  );

  legend.data.setAll([
    { name: "Category 1", color: am5.color("#00b19c") },
    { name: "Category 2", color: am5.color("#3bcd3f") },
    { name: "Category 3", color: am5.color("#007365") },
  ]);

  legend.labels.template.setAll({
    fontSize: 13,
    fontWeight: "500",
    fill: am5.color("#000"),
  });

  // Set map colors and appearance
  backgroundSeries.set("fill", am5.color("#ddd"));
  backgroundSeries.set("stroke", am5.color("#ddd"));
  backgroundSeries.set("strokeWidth", 0.5);

  // Add zoom control with adjusted positioning
  const zoomControl = am5map.ZoomControl.new(root, {
    x: am5.p100,
    y: am5.p100,
    marginRight: 30, // Increased margin
    marginBottom: 30, // Increased margin
    layer: 1,
    scale: 0.7, // Slightly smaller zoom controls
    align: "right",
    valign: "bottom",
    marginTop: 0,
  });

  chart.set("zoomControl", zoomControl);

  // Ensure zoom control stays within bounds
  chart.setAll({
    maxZoomLevel: 16,
    minZoomLevel: 1,
    wheelZoomPositionX: 0.5,
    wheelZoomPositionY: 0.5,
  });

  // Add padding to chart to prevent zoom controls from being cut off
  chart.setAll({
    paddingRight: 40,
    paddingBottom: 40,
  });

  // Set up animations
  chart.appear(1000, 100);

  // Add resize observer
  const resizeObserver = new ResizeObserver(() => {
    const container = document.getElementById(chartContainerId);
    if (container && container.offsetWidth && container.offsetHeight) {
      root.resize();
    }
  });
  resizeObserver.observe(document.getElementById(chartContainerId));

  // Store root instance for cleanup
  document.getElementById(chartContainerId).root = root;
  document.getElementById(chartContainerId).resizeObserver = resizeObserver;
}

// Add settings apply function
function applyDynamicPieMapSettings(widgetId, settingsId, chartContainerId) {
  const widget = document.getElementById(widgetId);
  if (!widget) return;

  // Update title
  const titleInput = document.getElementById(`${settingsId}-title`);
  if (titleInput) {
    const titleSpan = widget.querySelector(".widget-title span");
    if (titleSpan) {
      titleSpan.textContent = titleInput.value;
    }
  }

  // Update notes and source in footer if needed
  const notes = document.getElementById(`${settingsId}-notes`).value;
  const source = document.getElementById(`${settingsId}-source`).value;

  // Close offcanvas
  const offcanvas = document.getElementById(settingsId);
  if (offcanvas) {
    const bsOffcanvas = bootstrap.Offcanvas.getInstance(offcanvas);
    if (bsOffcanvas) {
      bsOffcanvas.hide();
    }
  }
}

// Make sure functions are exported to window object
(function (window) {
  window.addDynamicPieMapWidget = addDynamicPieMapWidget;
  window.applyDynamicPieMapSettings = applyDynamicPieMapSettings;
})(window);
