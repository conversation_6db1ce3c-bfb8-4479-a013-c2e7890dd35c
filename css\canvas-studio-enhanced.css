/* Canvas Studio Enhanced - Dynamic Section Container Styles */

/* Base Container Styles */
.canvas-studio-modal .modal-dialog {
  max-width: 100%;
  margin: 0;
  height: 100vh;
}

.canvas-studio-workspace {
  display: flex;
  height: calc(100vh - 120px);
  overflow: hidden;
}

.canvas-studio-sidebar {
  width: 300px;
  background: #f8f9fa;
  border-right: 1px solid #dee2e6;
  display: flex;
  flex-direction: column;
}

.canvas-studio-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Section Container Widget Styles */
.section-container-widget {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: #fff;
  transition: all 0.3s ease;
}

.section-container-widget:hover {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.section-container-widget .widget-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border-bottom: none;
  padding: 8px 12px;
  border-radius: 6px 6px 0 0;
}

.section-container-widget .widget-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 0.9rem;
}

.section-container-widget .widget-count-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.75rem;
  margin-left: auto;
}

.section-container-widget .widget-actions {
  display: flex;
  gap: 4px;
}

.section-container-widget .widget-actions .btn {
  padding: 4px 6px;
  font-size: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.section-container-widget .widget-actions .btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Nested Container Wrapper */
.nested-container-wrapper {
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  min-height: 200px;
  position: relative;
}

.nested-grid-stack {
  min-height: 180px;
  background: white;
  border: 2px dashed #dee2e6;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.nested-grid-stack:hover {
  border-color: #007bff;
  background: #f8f9ff;
}

.nested-grid-stack.ui-droppable-hover {
  border-color: #28a745;
  background: #f8fff8;
  border-style: solid;
}

/* Nested Grid Placeholder */
.nested-grid-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #6c757d;
  pointer-events: none;
  z-index: 1;
}

.placeholder-content-mini {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.placeholder-content-mini i {
  font-size: 2rem;
  opacity: 0.6;
}

.placeholder-content-mini p {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Tab Container Widget Styles */
.tab-container-widget {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: #fff;
  transition: all 0.3s ease;
}

.tab-container-widget:hover {
  border-color: #6f42c1;
  box-shadow: 0 4px 12px rgba(111, 66, 193, 0.15);
}

.tab-container-widget .widget-header {
  background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
  color: white;
  border-bottom: none;
  padding: 8px 12px;
  border-radius: 6px 6px 0 0;
}

/* Nested Tab Container */
.nested-tab-container {
  padding: 8px;
  background: #f8f9fa;
  border-radius: 6px;
  min-height: 200px;
}

.tab-nav-mini {
  display: flex;
  gap: 4px;
  margin-bottom: 8px;
  padding: 4px;
  background: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.tab-nav-item-mini {
  padding: 4px 8px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tab-nav-item-mini.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.tab-nav-item-mini:hover:not(.active) {
  background: #e9ecef;
}

.tab-nav-item-mini.add-tab-mini {
  background: #28a745;
  color: white;
  border-color: #28a745;
  padding: 4px 6px;
}

.tab-nav-item-mini.add-tab-mini:hover {
  background: #218838;
}

.tab-badge-mini {
  background: rgba(255, 255, 255, 0.3);
  color: white;
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 0.65rem;
  min-width: 16px;
  text-align: center;
}

.tab-nav-item-mini:not(.active) .tab-badge-mini {
  background: #6c757d;
  color: white;
}

.tab-content-mini {
  background: white;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  min-height: 150px;
}

/* Widget Library Modal Styles */
.widget-library-content {
  max-height: 70vh;
  overflow-y: auto;
}

.widget-category {
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.widget-category-header {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  user-select: none;
}

.widget-category-header h6 {
  margin: 0;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
  gap: 8px;
}

.widget-category-content {
  padding: 16px;
}

.widget-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 12px;
}

.widget-library-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.widget-library-item:hover {
  border-color: #007bff;
  background: #f8f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.widget-library-item.dragging {
  opacity: 0.8;
  transform: rotate(5deg);
}

.widget-library-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.widget-library-info {
  flex: 1;
  min-width: 0;
}

.widget-library-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: #495057;
  margin-bottom: 4px;
}

.widget-library-desc {
  font-size: 0.8rem;
  color: #6c757d;
  line-height: 1.3;
}

/* Drag and Drop Visual Feedback */
.grid-stack-item.ui-draggable-dragging {
  z-index: 1000;
  opacity: 0.8;
  transform: rotate(5deg);
}

.grid-stack.ui-droppable-hover {
  background: #f8fff8 !important;
  border-color: #28a745 !important;
  border-style: solid !important;
}

.grid-stack-item.ui-draggable-dragging .widget-card {
  border-color: #007bff;
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.3);
}

/* Nested Grid Hover Effects */
.nested-grid-stack .grid-stack-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Widget Action Buttons */
.widget-add-btn {
  background: #28a745 !important;
  border-color: #28a745 !important;
  color: white !important;
}

.widget-add-btn:hover {
  background: #218838 !important;
  border-color: #218838 !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .canvas-studio-sidebar {
    width: 250px;
  }

  .nested-grid-stack {
    min-height: 150px;
  }

  .nested-container-wrapper {
    min-height: 170px;
  }
}

@media (max-width: 768px) {
  .canvas-studio-workspace {
    flex-direction: column;
    height: auto;
  }

  .canvas-studio-sidebar {
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
  }

  .widget-grid {
    grid-template-columns: 1fr;
  }

  .section-container-widget .widget-actions {
    flex-direction: column;
    gap: 2px;
  }

  .section-container-widget .widget-actions .btn {
    font-size: 0.7rem;
    padding: 3px 5px;
  }
}

/* Animation Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.section-container-widget {
  animation: fadeInUp 0.3s ease;
}

.tab-container-widget {
  animation: fadeInUp 0.3s ease;
}

.widget-library-item {
  animation: scaleIn 0.2s ease;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .nested-grid-stack {
    border-width: 3px;
  }

  .section-container-widget {
    border-width: 3px;
  }

  .tab-container-widget {
    border-width: 3px;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles for Accessibility */
.widget-library-item:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.tab-nav-item-mini:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.section-container-widget:focus-within {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* Print Styles */
@media print {
  .canvas-studio-modal {
    display: none !important;
  }
}
