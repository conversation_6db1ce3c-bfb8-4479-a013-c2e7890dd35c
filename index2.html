<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard POC</title>

    <script src="./css-reference.js"></script>
    <script src="./js-reference.js"></script>
  </head>

  <body>
    <!-- Dashboard Header -->
    <script src="./navbar.js"></script>
    <script src="./widgetsection.js"></script>

    <div id="dashboard-main" style="margin-top: 158px">
      <div class="container-fluid">
        <!-- Grid Container -->
        <div class="row">
          <div class="col-12 px-2">
            <div id="grid-container" class="grid-stack">
              <script src="cobalt.js"></script>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="./modals.js"></script>

    <!-- Footer section  -->
    <script src="./footer.js"></script>
    <!-- Required Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://gridstackjs.com/node_modules/gridstack/dist/gridstack-all.js"></script>

    <script src="js/gridstack-autoscroll-auto.js"></script>
    <!-- Widget Comments Script -->
    <script src="js/widget-comments.js"></script>

    <!-- Inline Editing Script -->
    <script src="js/inlineEdit.js"></script>

    <!-- Section Auto-Resize Script -->
    <!-- <script src="js/sectionAutoResize.js"></script> -->

    <!-- CSS for sizeToContent support -->
    <style></style>

    <script type="text/javascript">
      // Initialize widget counter
      let widgetCount = 0;

      // Console helper functions for testing auto-resize
      window.testAutoResize = {
        // Add a section container
        addSection: function () {
          return addSectionContainerWidget();
        },

        // Add test widgets to the most recent section
        addTestWidgets: function (sectionId) {
          if (!sectionId) {
            // Find the most recent section
            const sections = document.querySelectorAll(".section-content");
            if (sections.length === 0) {
              console.error(
                "No section containers found. Add one first with testAutoResize.addSection()"
              );
              return;
            }
            sectionId = sections[sections.length - 1].id;
          }

          const container = document.getElementById(sectionId);
          if (!container) {
            console.error("Section not found:", sectionId);
            return;
          }

          const gridElement = container.querySelector(".grid-stack");
          if (!gridElement || !gridElement.gridstack) {
            console.error("Could not find grid in section container");
            return;
          }

          const nestedGrid = gridElement.gridstack;

          // Clear existing widgets
          nestedGrid.removeAll();

          // Add widgets with different heights
          nestedGrid.addWidget({
            x: 0,
            y: 0,
            w: 2,
            h: 2,
            content:
              '<div class="widget p-2 h-100" style="background: #28a745; color: white;"><div class="widget-header"><strong>Small (2h)</strong></div><div class="widget-body d-flex align-items-center justify-content-center"><div>Small Content</div></div></div>',
          });

          nestedGrid.addWidget({
            x: 2,
            y: 0,
            w: 2,
            h: 4,
            content:
              '<div class="widget p-2 h-100" style="background: #ffc107; color: black;"><div class="widget-header"><strong>Medium (4h)</strong></div><div class="widget-body d-flex align-items-center justify-content-center"><div>Medium Content<br>More lines<br>Extra content</div></div></div>',
          });

          nestedGrid.addWidget({
            x: 4,
            y: 0,
            w: 2,
            h: 6,
            content:
              '<div class="widget p-2 h-100" style="background: #dc3545; color: white;"><div class="widget-header"><strong>Tall (6h)</strong></div><div class="widget-body d-flex align-items-center justify-content-center"><div>Tall Content<br>Line 1<br>Line 2<br>Line 3<br>Line 4<br>Line 5</div></div></div>',
          });

          console.log(
            `Added test widgets to section ${sectionId} - should auto-resize to accommodate tallest widget (6 units)`
          );
        },

        // Show monitored sections
        showMonitored: function () {
          if (window.sectionAutoResize) {
            const sections = window.sectionAutoResize.getMonitoredSections();
            console.log("Monitored sections:", sections);
            return sections;
          } else {
            console.error("Auto-resize system not initialized");
            return [];
          }
        },

        // Manually trigger resize for a section
        resizeSection: function (sectionId) {
          if (window.sectionAutoResize) {
            window.sectionAutoResize.resizeSection(sectionId);
            console.log(`Triggered resize for section: ${sectionId}`);
          } else {
            console.error("Auto-resize system not initialized");
          }
        },

        // Manually retry monitoring for a section
        retrySection: function (sectionId) {
          if (window.sectionAutoResize) {
            window.sectionAutoResize.retrySection(sectionId);
            console.log(`Retrying monitoring for section: ${sectionId}`);
          } else {
            console.error("Auto-resize system not initialized");
          }
        },

        // Get all section containers and their monitoring status
        getStatus: function () {
          const sections = document.querySelectorAll(
            ".section-container-widget"
          );
          const status = [];

          sections.forEach((section, index) => {
            const sectionId =
              section.querySelector(".section-content")?.id ||
              `unknown-${index}`;
            const hasNestedGrid = !!section.querySelector(".grid-stack");
            const isMonitored = window.sectionAutoResize
              ? window.sectionAutoResize
                  .getMonitoredSections()
                  .includes(sectionId)
              : false;

            status.push({
              sectionId,
              hasNestedGrid,
              isMonitored,
              element: section,
            });
          });

          console.table(status);
          return status;
        },
      };

      // Log usage instructions
      console.log(`
      🎯 Auto-Resize Test Functions:
      - testAutoResize.addSection() - Add a new section container
      - testAutoResize.addTestWidgets() - Add widgets with different heights to latest section
      - testAutoResize.showMonitored() - Show all monitored sections
      - testAutoResize.resizeSection(sectionId) - Manually trigger resize
      - testAutoResize.retrySection(sectionId) - Retry monitoring for a section
      - testAutoResize.getStatus() - Get status of all sections and their monitoring

      🎯 Chart Test Functions:
      - createTestSectionWithCharts() - Create a test section with bar chart and line chart

      Example usage:
      1. testAutoResize.addSection()
      2. testAutoResize.addTestWidgets()
      3. testAutoResize.getStatus() - Check if monitoring is working
      4. If monitoring failed, use testAutoResize.retrySection(sectionId)
      5. createTestSectionWithCharts() - Create test charts section
            `);

      function addEvents(grid, id) {
        let g = id !== undefined ? "grid" + id + " " : "";

        grid
          .on("added removed change", function (event, items) {
            let str = "";
            items.forEach(function (item) {
              str +=
                " (" +
                item.x +
                "," +
                item.y +
                " " +
                item.w +
                "x" +
                item.h +
                ")";
            });
            console.log(
              g + event.type + " " + items.length + " items (x,y w h):" + str
            );
          })
          .on("enable", function (event) {
            let grid = event.target;
            console.log(g + "enable");
          })
          .on("disable", function (event) {
            let grid = event.target;
            console.log(g + "disable");
          })
          .on("dragstart", function (event, el) {
            let n = el.gridstackNode;
            let x = el.getAttribute("gs-x"); // verify node (easiest) and attr are the same
            let y = el.getAttribute("gs-y");
            console.log(
              g +
                "dragstart " +
                (n.content || "") +
                " pos: (" +
                n.x +
                "," +
                n.y +
                ") = (" +
                x +
                "," +
                y +
                ")"
            );
          })
          .on("drag", function (event, el) {
            let n = el.gridstackNode;
            let x = el.getAttribute("gs-x"); // verify node (easiest) and attr are the same
            let y = el.getAttribute("gs-y");
            // console.log(g + 'drag ' + (n.content || '') + ' pos: (' + n.x + ',' + n.y + ') = (' + x + ',' + y + ')');
          })
          .on("dragstop", function (event, el) {
            let n = el.gridstackNode;
            let x = el.getAttribute("gs-x"); // verify node (easiest) and attr are the same
            let y = el.getAttribute("gs-y");
            console.log(
              g +
                "dragstop " +
                (n.content || "") +
                " pos: (" +
                n.x +
                "," +
                n.y +
                ") = (" +
                x +
                "," +
                y +
                ")"
            );
          })
          .on("dropped", function (event, previousNode, newNode) {
            if (previousNode) {
              console.log(
                g + "dropped - Removed widget from grid:",
                previousNode
              );
            }
            if (newNode) {
              console.log(g + "dropped - Added widget in grid:", newNode);
            }
          })
          .on("resizestart", function (event, el) {
            let n = el.gridstackNode;
            let rec = el.getBoundingClientRect();
            console.log(
              `${g} resizestart ${n.content || ""} size: (${n.w}x${
                n.h
              }) = (${Math.round(rec.width)}x${Math.round(rec.height)})px`
            );
          })
          .on("resize", function (event, el) {
            let n = el.gridstackNode;
            let rec = el.getBoundingClientRect();
            console.log(
              `${g} resize ${n.content || ""} size: (${n.w}x${
                n.h
              }) = (${Math.round(rec.width)}x${Math.round(rec.height)})px`
            );
          })
          .on("resizestop", function (event, el) {
            let n = el.gridstackNode;
            let rec = el.getBoundingClientRect();
            console.log(
              `${g} resizestop ${n.content || ""} size: (${n.w}x${
                n.h
              }) = (${Math.round(rec.width)}x${Math.round(rec.height)})px`
            );
          });
      }
      // This render callback is used to set the innerHTML when loading a widget
      GridStack.renderCB = function (el, widget) {
        if (widget.content) el.innerHTML = widget.content;
      };

      // Global variables
      let staticGrid = false;
      let count = 0;

      // MCP (Model-Controller-Presenter) Pattern Implementation

      // Model - Stores the state and data
      class GridModel {
        constructor() {
          this.selectedNode = null;
          this.grids = {};
        }

        setSelectedNode(node) {
          this.selectedNode = node;
        }

        getSelectedNode() {
          return this.selectedNode;
        }

        registerGrid(id, gridInstance) {
          this.grids[id] = gridInstance;
        }

        getGrid(id) {
          return this.grids[id];
        }

        getAllGrids() {
          return Object.values(this.grids);
        }
      }

      // Controller - Handles the business logic
      class GridController {
        constructor(model) {
          this.model = model;
        }

        selectNode(node) {
          // Deselect previously selected node if any
          const previousNode = this.model.getSelectedNode();
          if (previousNode) {
            this.deselectNode(previousNode);
          }

          // Select the new node
          this.model.setSelectedNode(node);

          // Highlight the selected node
          if (node) {
            node.classList.add("selected-node");
          }
        }

        deselectNode(node) {
          if (node) {
            node.classList.remove("selected-node");
          }
          if (this.model.getSelectedNode() === node) {
            this.model.setSelectedNode(null);
          }
        }

        getSelectedNodeInfo() {
          const selectedNode = this.model.getSelectedNode();
          if (!selectedNode) {
            return { status: "error", message: "No node selected" };
          }

          // Find which grid this node belongs to
          const gridNode = selectedNode.closest(".grid-stack-item");
          if (!gridNode || !gridNode.gridstackNode) {
            return {
              status: "error",
              message: "Selected node is not a grid item",
            };
          }

          // Get the grid instance
          const gridElement = gridNode.closest(".grid-stack");
          if (!gridElement || !gridElement.gridstack) {
            return {
              status: "error",
              message: "Cannot find grid for selected node",
            };
          }

          const grid = gridElement.gridstack;
          const gridstackNode = gridNode.gridstackNode;

          return {
            status: "success",
            node: gridstackNode,
            grid: grid,
            element: gridNode,
            content: selectedNode.innerHTML,
            position: {
              x: gridstackNode.x,
              y: gridstackNode.y,
              w: gridstackNode.w,
              h: gridstackNode.h,
            },
            gridId: grid.opts.id || "unknown",
          };
        }

        removeSelectedNode() {
          const selectedNode = this.model.getSelectedNode();
          if (!selectedNode) {
            return { status: "error", message: "No node selected" };
          }

          // Find which grid this node belongs to
          const gridNode = selectedNode.closest(".grid-stack-item");
          if (!gridNode || !gridNode.gridstackNode) {
            return {
              status: "error",
              message: "Selected node is not a grid item",
            };
          }

          // Get the grid instance
          const gridElement = gridNode.closest(".grid-stack");
          if (!gridElement || !gridElement.gridstack) {
            return {
              status: "error",
              message: "Cannot find grid for selected node",
            };
          }

          const grid = gridElement.gridstack;

          // Remove the node from the grid
          grid.removeWidget(gridNode);

          // Clear the selection
          this.model.setSelectedNode(null);

          return { status: "success", message: "Node removed successfully" };
        }
      }

      // Presenter - Handles the UI updates
      class GridPresenter {
        constructor(model, controller) {
          this.model = model;
          this.controller = controller;
          this.setupEventListeners();
        }

        setupEventListeners() {
          // Add click event listeners to all grid items
          document.addEventListener("click", (event) => {
            // Check if clicked on a grid item content
            const gridItemContent = event.target.closest(
              ".grid-stack-item-content"
            );
            if (gridItemContent) {
              this.controller.selectNode(gridItemContent);
              event.stopPropagation();
            } else if (!event.target.closest(".grid-stack-item")) {
              // Clicked outside any grid item, deselect
              const selectedNode = this.model.getSelectedNode();
              if (selectedNode) {
                this.controller.deselectNode(selectedNode);
              }
            }
          });
        }
      }

      // Nested grid child layouts
      let sub1 = [
        { x: 0, y: 0 },
        { x: 1, y: 0 },
        { x: 2, y: 0 },
        { x: 3, y: 0 },
        { x: 0, y: 1 },
        { x: 1, y: 1 },
      ];
      let sub2 = [
        { x: 0, y: 0, h: 2 },
        { x: 1, y: 1, w: 2 },
      ];
      // Assign a simple content (incrementing count) for each nested child
      [...sub1, ...sub2].forEach((d) => (d.content = String(count++)));

      // Main grid options (including nested grids)
      let options = {
        staticGrid: staticGrid,
        cellHeight: 60, // Changed from 'auto' to fixed height
        margin: 5,
        minRow: 2,
        acceptWidgets: true,
        id: "main",
        sizeToContent: false, // Disable auto-resizing for main grid
        resizable: { handles: "se,e,s,sw,w" },
        // Remove the children array to start with an empty grid
        children: [], // Empty array for clean initial state
      };

      // Create the main grid inside the dedicated grid container
      let grid = GridStack.addGrid(
        document.getElementById("grid-container"),
        options
      );

      // Optionally attach debug event handlers
      let gridEls = GridStack.getElements(".grid-stack");
      gridEls.forEach(function (gridEl) {
        let g = gridEl.gridstack;
        if (typeof addEvents === "function") {
          addEvents(g, g.opts.id);
        }
        // Attach handler for added widgets to initialize text widgets and table widgets
        g.on("added", function (event, items) {
          items.forEach(function (item) {
            // Text widget auto-init
            const textWidget = item.el.querySelector(".text-widget");
            if (textWidget) {
              const textContainer = textWidget.querySelector(".text-container");
              if (
                textContainer &&
                textContainer.id &&
                typeof window.initTextWidget === "function"
              ) {
                const textId = textContainer.id;
                const settingsId = "settings-" + textId;
                if (
                  !document.getElementById(settingsId) &&
                  typeof window.getTextWidgetMarkup === "function"
                ) {
                  const offcanvasContainer =
                    document.getElementById("offcanvasContainer");
                  if (offcanvasContainer) {
                    const settingsPanel = document.createElement("div");
                    settingsPanel.className = "offcanvas offcanvas-end";
                    settingsPanel.id = settingsId;
                    settingsPanel.setAttribute("tabindex", "-1");
                    settingsPanel.innerHTML = `
                            <div class="offcanvas-header">
                              <h5 class="offcanvas-title">Text Settings</h5>
                              <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                            </div>
                            <div class="offcanvas-body">
                              <!-- Text Content -->
                              <div class="mb-3">
                                <label for="${settingsId}-content" class="form-label">Text Content</label>
                                <textarea class="form-control" id="${settingsId}-content" rows="4" placeholder="Enter your text content here..."></textarea>
                              </div>
                              <!-- Font Family -->
                              <div class="mb-3">
                                <label class="form-label">Font Family</label>
                                <select class="form-select" id="${settingsId}-fontFamily">
                                  <option value="Arial, sans-serif">Arial</option>
                                  <option value="Georgia, serif">Georgia</option>
                                  <option value="Times New Roman, serif">Times New Roman</option>
                                  <option value="Helvetica, sans-serif">Helvetica</option>
                                  <option value="Verdana, sans-serif">Verdana</option>
                                  <option value="Courier New, monospace">Courier New</option>
                                </select>
                              </div>
                              <!-- Font Size -->
                              <div class="mb-3">
                                <label class="form-label">Font Size (px)</label>
                                <input type="range" class="form-range" min="10" max="36" value="14" id="${settingsId}-fontSize">
                                <div class="d-flex justify-content-between">
                                  <small>10px</small>
                                  <small id="${settingsId}-fontSize-display">14px</small>
                                  <small>36px</small>
                                </div>
                              </div>
                              <!-- Text Alignment -->
                              <div class="mb-3">
                                <label class="form-label">Text Alignment</label>
                                <select class="form-select" id="${settingsId}-textAlign">
                                  <option value="left">Left</option>
                                  <option value="center">Center</option>
                                  <option value="right">Right</option>
                                  <option value="justify">Justify</option>
                                </select>
                              </div>
                              <!-- Text Color -->
                              <div class="mb-3">
                                <label for="${settingsId}-textColor" class="form-label">Text Color</label>
                                <input type="color" class="form-control form-control-color" id="${settingsId}-textColor" value="#333333">
                              </div>
                              <!-- Background Color -->
                              <div class="mb-3">
                                <label for="${settingsId}-bgcolor" class="form-label">Background Color</label>
                                <input type="color" class="form-control form-control-color" id="${settingsId}-bgcolor" value="#ffffff">
                              </div>
                              <!-- Line Height -->
                              <div class="mb-3">
                                <label class="form-label">Line Height</label>
                                <input type="range" class="form-range" min="1" max="3" step="0.1" value="1.5" id="${settingsId}-lineHeight">
                                <div class="d-flex justify-content-between">
                                  <small>1.0</small>
                                  <small id="${settingsId}-lineHeight-display">1.5</small>
                                  <small>3.0</small>
                                </div>
                              </div>
                              <!-- Text Formatting -->
                              <div class="mb-3">
                                <label class="form-label">Text Formatting</label>
                                <div class="d-flex gap-2">
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="${settingsId}-bold">
                                    <label class="form-check-label" for="${settingsId}-bold">
                                      <strong>Bold</strong>
                                    </label>
                                  </div>
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="${settingsId}-italic">
                                    <label class="form-check-label" for="${settingsId}-italic">
                                      <em>Italic</em>
                                    </label>
                                  </div>
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="${settingsId}-underline">
                                    <label class="form-check-label" for="${settingsId}-underline">
                                      <u>Underline</u>
                                    </label>
                                  </div>
                                </div>
                              </div>
                              <!-- Border -->
                              <div class="mb-3">
                                <div class="form-check form-switch">
                                  <input class="form-check-input" type="checkbox" id="${settingsId}-border" checked>
                                  <label class="form-check-label" for="${settingsId}-border">Show Border</label>
                                </div>
                              </div>
                              <!-- Border Radius -->
                              <div class="mb-3">
                                <label class="form-label">Border Radius (px)</label>
                                <input type="range" class="form-range" min="0" max="20" value="4" id="${settingsId}-radius">
                                <div class="d-flex justify-content-between">
                                  <small>0px</small>
                                  <small id="${settingsId}-radius-display">4px</small>
                                  <small>20px</small>
                                </div>
                              </div>
                              <!-- Padding -->
                              <div class="mb-3">
                                <label class="form-label">Padding (px)</label>
                                <input type="range" class="form-range" min="5" max="30" value="10" id="${settingsId}-padding">
                                <div class="d-flex justify-content-between">
                                  <small>5px</small>
                                  <small id="${settingsId}-padding-display">10px</small>
                                  <small>30px</small>
                                </div>
                              </div>
                              <!-- Apply Button -->
                              <button class="btn btn-primary w-100" onclick="applyTextSettings('${textId}', '${settingsId}')">
                                Apply Changes
                              </button>
                            </div>`;
                    offcanvasContainer.appendChild(settingsPanel);
                  }
                }
                // Now initialize the widget (event listeners, etc.)
                window.initTextWidget(textId, settingsId);
              }
            }
            // Table widget auto-init
            const tableWidget = item.el.querySelector(".table-widget");
            if (tableWidget) {
              const tableContainer =
                tableWidget.querySelector(".table-container");
              if (
                tableContainer &&
                tableContainer.id &&
                typeof window.initTableWidget === "function"
              ) {
                const tableId = tableContainer.id;
                const settingsId = "settings-" + tableId;
                if (
                  !document.getElementById(settingsId) &&
                  typeof window.getTableWidgetMarkup === "function"
                ) {
                  const offcanvasContainer =
                    document.getElementById("offcanvasContainer");
                  if (offcanvasContainer) {
                    const settingsPanel = document.createElement("div");
                    settingsPanel.className = "offcanvas offcanvas-end";
                    settingsPanel.id = settingsId;
                    settingsPanel.setAttribute("tabindex", "-1");
                    settingsPanel.innerHTML = `
                            <div class="offcanvas-header">
                              <h5 class="offcanvas-title">Table Settings</h5>
                              <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                            </div>
                            <div class="offcanvas-body">
                              <!-- Table Content -->
                              <div class="mb-3">
                                <label for="${settingsId}-content" class="form-label">Table Content</label>
                                <textarea class="form-control" id="${settingsId}-content" rows="4" placeholder="Enter your table content here..."></textarea>
                              </div>
                              <!-- Font Family -->
                              <div class="mb-3">
                                <label class="form-label">Font Family</label>
                                <select class="form-select" id="${settingsId}-fontFamily">
                                  <option value="Arial, sans-serif">Arial</option>
                                  <option value="Georgia, serif">Georgia</option>
                                  <option value="Times New Roman, serif">Times New Roman</option>
                                  <option value="Helvetica, sans-serif">Helvetica</option>
                                  <option value="Verdana, sans-serif">Verdana</option>
                                  <option value="Courier New, monospace">Courier New</option>
                                </select>
                              </div>
                              <!-- Font Size -->
                              <div class="mb-3">
                                <label class="form-label">Font Size (px)</label>
                                <input type="range" class="form-range" min="10" max="36" value="14" id="${settingsId}-fontSize">
                                <div class="d-flex justify-content-between">
                                  <small>10px</small>
                                  <small id="${settingsId}-fontSize-display">14px</small>
                                  <small>36px</small>
                                </div>
                              </div>
                              <!-- Text Alignment -->
                              <div class="mb-3">
                                <label class="form-label">Text Alignment</label>
                                <select class="form-select" id="${settingsId}-textAlign">
                                  <option value="left">Left</option>
                                  <option value="center">Center</option>
                                  <option value="right">Right</option>
                                  <option value="justify">Justify</option>
                                </select>
                              </div>
                              <!-- Text Color -->
                              <div class="mb-3">
                                <label for="${settingsId}-textColor" class="form-label">Text Color</label>
                                <input type="color" class="form-control form-control-color" id="${settingsId}-textColor" value="#333333">
                              </div>
                              <!-- Background Color -->
                              <div class="mb-3">
                                <label for="${settingsId}-bgcolor" class="form-label">Background Color</label>
                                <input type="color" class="form-control form-control-color" id="${settingsId}-bgcolor" value="#ffffff">
                              </div>
                              <!-- Line Height -->
                              <div class="mb-3">
                                <label class="form-label">Line Height</label>
                                <input type="range" class="form-range" min="1" max="3" step="0.1" value="1.5" id="${settingsId}-lineHeight">
                                <div class="d-flex justify-content-between">
                                  <small>1.0</small>
                                  <small id="${settingsId}-lineHeight-display">1.5</small>
                                  <small>3.0</small>
                                </div>
                              </div>
                              <!-- Text Formatting -->
                              <div class="mb-3">
                                <label class="form-label">Text Formatting</label>
                                <div class="d-flex gap-2">
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="${settingsId}-bold">
                                    <label class="form-check-label" for="${settingsId}-bold">
                                      <strong>Bold</strong>
                                    </label>
                                  </div>
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="${settingsId}-italic">
                                    <label class="form-check-label" for="${settingsId}-italic">
                                      <em>Italic</em>
                                    </label>
                                  </div>
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="${settingsId}-underline">
                                    <label class="form-check-label" for="${settingsId}-underline">
                                      <u>Underline</u>
                                    </label>
                                  </div>
                                </div>
                              </div>
                              <!-- Border -->
                              <div class="mb-3">
                                <div class="form-check form-switch">
                                  <input class="form-check-input" type="checkbox" id="${settingsId}-border" checked>
                                  <label class="form-check-label" for="${settingsId}-border">Show Border</label>
                                </div>
                              </div>
                              <!-- Border Radius -->
                              <div class="mb-3">
                                <label class="form-label">Border Radius (px)</label>
                                <input type="range" class="form-range" min="0" max="20" value="4" id="${settingsId}-radius">
                                <div class="d-flex justify-content-between">
                                  <small>0px</small>
                                  <small id="${settingsId}-radius-display">4px</small>
                                  <small>20px</small>
                                </div>
                              </div>
                              <!-- Padding -->
                              <div class="mb-3">
                                <label class="form-label">Padding (px)</label>
                                <input type="range" class="form-range" min="5" max="30" value="10" id="${settingsId}-padding">
                                <div class="d-flex justify-content-between">
                                  <small>5px</small>
                                  <small id="${settingsId}-padding-display">10px</small>
                                  <small>30px</small>
                                </div>
                              </div>
                              <!-- Apply Button -->
                              <button class="btn btn-primary w-100" onclick="applyTableSettings('${tableId}', '${settingsId}')">
                                Apply Changes
                              </button>
                            </div>`;
                    offcanvasContainer.appendChild(settingsPanel);
                  }
                }
                // Now initialize the widget (event listeners, etc.)
                window.initTableWidget(tableId, settingsId);
              }
            }

            // Line separator widget auto-init
            const lineSeparatorWidget = item.el.querySelector(
              ".line-separator-widget"
            );
            if (lineSeparatorWidget) {
              const separatorContainer = lineSeparatorWidget.querySelector(
                ".line-separator-container"
              );
              if (
                separatorContainer &&
                separatorContainer.id &&
                typeof window.initLineSeparatorWidget === "function"
              ) {
                const separatorId = separatorContainer.id;
                const settingsId = "settings-" + separatorId;
                if (
                  !document.getElementById(settingsId) &&
                  typeof window.getLineSeparatorWidgetMarkup === "function"
                ) {
                  const offcanvasContainer =
                    document.getElementById("offcanvasContainer");
                  if (offcanvasContainer) {
                    const settingsPanel = document.createElement("div");
                    settingsPanel.className = "offcanvas offcanvas-end";
                    settingsPanel.id = settingsId;
                    settingsPanel.setAttribute("tabindex", "-1");
                    settingsPanel.innerHTML = `
                <div class="offcanvas-header">
                  <h5 class="offcanvas-title">Line Separator Settings</h5>
                  <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                </div>
                <div class="offcanvas-body">
                  <!-- Orientation -->
                  <div class="mb-3">
                    <label class="form-label">Orientation</label>
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="${settingsId}-orientation" id="${settingsId}-horizontal" value="horizontal" checked>
                      <label class="form-check-label" for="${settingsId}-horizontal">
                        <i class="las la-minus"></i> Horizontal
                      </label>
                    </div>
                    <div class="form-check">
                      <input class="form-check-input" type="radio" name="${settingsId}-orientation" id="${settingsId}-vertical" value="vertical">
                      <label class="form-check-label" for="${settingsId}-vertical">
                        <i class="las la-grip-lines-vertical"></i> Vertical
                      </label>
                    </div>
                  </div>
                  <!-- Line Style -->
                  <div class="mb-3">
                    <label class="form-label">Line Style</label>
                    <select class="form-select" id="${settingsId}-style">
                      <option value="solid">Solid</option>
                      <option value="dashed">Dashed</option>
                      <option value="dotted">Dotted</option>
                      <option value="double">Double</option>
                    </select>
                  </div>
                  <!-- Line Width -->
                  <div class="mb-3">
                    <label class="form-label">Line Width (px)</label>
                    <input type="range" class="form-range" min="1" max="10" value="2" id="${settingsId}-width">
                    <div class="d-flex justify-content-between">
                      <small>1px</small>
                      <small id="${settingsId}-width-display">2px</small>
                      <small>10px</small>
                    </div>
                  </div>
                  <!-- Line Color -->
                  <div class="mb-3">
                    <label class="form-label">Line Color</label>
                    <input type="color" class="form-control form-control-color" id="${settingsId}-color" value="#333333">
                  </div>
                  <!-- Line Length -->
                  <div class="mb-3">
                    <label class="form-label">Line Length (%)</label>
                    <input type="range" class="form-range" min="10" max="100" value="100" id="${settingsId}-length">
                    <div class="d-flex justify-content-between">
                      <small>10%</small>
                      <small id="${settingsId}-length-display">100%</small>
                      <small>100%</small>
                    </div>
                  </div>
                  <!-- Margin -->
                  <div class="mb-3">
                    <label class="form-label">Margin (px)</label>
                    <input type="range" class="form-range" min="0" max="50" value="20" id="${settingsId}-margin">
                    <div class="d-flex justify-content-between">
                      <small>0px</small>
                      <small id="${settingsId}-margin-display">20px</small>
                      <small>50px</small>
                    </div>
                  </div>
                  <!-- Alignment -->
                  <div class="mb-3">
                    <label class="form-label">Alignment</label>
                    <select class="form-select" id="${settingsId}-alignment">
                      <option value="center">Center</option>
                      <option value="start">Start</option>
                      <option value="end">End</option>
                    </select>
                  </div>
                  <!-- Apply Button -->
                  <button class="btn btn-primary w-100" onclick="applyLineSeparatorSettings('${separatorId}', '${settingsId}')">
                    Apply Changes
                  </button>
                </div>
              `;
                    offcanvasContainer.appendChild(settingsPanel);
                  }
                }
                // Now initialize the widget (event listeners, etc.)
                window.initLineSeparatorWidget(separatorId, settingsId);
              }
            }

            // Notes section widget auto-init
            const notesSectionWidget = item.el.querySelector(
              ".notes-section-widget"
            );
            if (notesSectionWidget) {
              const notesContainer = notesSectionWidget.querySelector(
                ".notes-section-container"
              );
              if (
                notesContainer &&
                notesContainer.id &&
                typeof window.initNotesSectionWidget === "function"
              ) {
                const notesId = notesContainer.id;
                const settingsId = "settings-" + notesId;
                if (
                  !document.getElementById(settingsId) &&
                  typeof window.getNotesSectionWidgetMarkup === "function"
                ) {
                  const offcanvasContainer =
                    document.getElementById("offcanvasContainer");
                  if (offcanvasContainer) {
                    const settingsPanel = document.createElement("div");
                    settingsPanel.className = "offcanvas offcanvas-end";
                    settingsPanel.id = settingsId;
                    settingsPanel.setAttribute("tabindex", "-1");
                    settingsPanel.innerHTML = `
                            <div class="offcanvas-header">
                              <h5 class="offcanvas-title">Notes Section Settings</h5>
                              <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                            </div>
                            <div class="offcanvas-body">
                              <!-- Notes Section -->
                              <div class="mb-4">
                                <div class="form-check form-switch mb-2">
                                  <input class="form-check-input" type="checkbox" id="${settingsId}-notes-toggle">
                                  <label class="form-check-label fw-bold" for="${settingsId}-notes-toggle">
                                    <i class="las la-clipboard"></i> Show Notes
                                  </label>
                                </div>
                                <div class="mb-3">
                                  <label class="form-label">Notes Content</label>
                                  <textarea class="form-control" id="${settingsId}-notes-content" rows="2" placeholder="Enter notes content">The Smart Cube Knowledge Repository</textarea>
                                </div>
                              </div>

                              <!-- Source Section -->
                              <div class="mb-4">
                                <div class="form-check form-switch mb-2">
                                  <input class="form-check-input" type="checkbox" id="${settingsId}-source-toggle" checked>
                                  <label class="form-check-label fw-bold" for="${settingsId}-source-toggle">
                                    <i class="las la-database"></i> Show Source
                                  </label>
                                </div>
                                <div class="mb-3">
                                  <label class="form-label">Source Content</label>
                                  <input type="text" class="form-control" id="${settingsId}-source-content" placeholder="Enter source" value="The Smart Cube Research and Analysis">
                                </div>
                              </div>

                              <!-- Last Update Section -->
                              <div class="mb-4">
                                <div class="form-check form-switch mb-2">
                                  <input class="form-check-input" type="checkbox" id="${settingsId}-last-update-toggle" checked>
                                  <label class="form-check-label fw-bold" for="${settingsId}-last-update-toggle">
                                    <i class="las la-calendar-minus"></i> Show Last Update
                                  </label>
                                </div>
                                <div class="mb-3">
                                  <label class="form-label">Last Update</label>
                                  <input type="text" class="form-control" id="${settingsId}-last-update-content" placeholder="Enter last update date" value="Apr-2025">
                                </div>
                              </div>

                              <!-- Next Update Section -->
                              <div class="mb-4">
                                <div class="form-check form-switch mb-2">
                                  <input class="form-check-input" type="checkbox" id="${settingsId}-next-update-toggle" checked>
                                  <label class="form-check-label fw-bold" for="${settingsId}-next-update-toggle">
                                    <i class="las la-calendar-plus"></i> Show Next Update
                                  </label>
                                </div>
                                <div class="mb-3">
                                  <label class="form-label">Next Update</label>
                                  <input type="text" class="form-control" id="${settingsId}-next-update-content" placeholder="Enter next update date" value="May-2025">
                                </div>
                              </div>

                              <!-- Apply Button -->
                              <button class="btn btn-primary w-100" onclick="applyNotesSectionSettings('${notesId}', '${settingsId}')">
                                Apply Changes
                              </button>
                            </div>`;
                    offcanvasContainer.appendChild(settingsPanel);
                  }
                }
                // Now initialize the widget (event listeners, etc.)
                window.initNotesSectionWidget(notesId, settingsId);
              }
            }

            // Image widget auto-init
            const imageWidget = item.el.querySelector(".image-widget");
            if (imageWidget) {
              const imageContainer =
                imageWidget.querySelector(".image-container");
              if (
                imageContainer &&
                imageContainer.id &&
                typeof window.initImageWidget === "function"
              ) {
                const imageId = imageContainer.id;
                const settingsId = "settings-" + imageId;
                if (
                  !document.getElementById(settingsId) &&
                  typeof window.getImageWidgetMarkup === "function"
                ) {
                  const offcanvasContainer =
                    document.getElementById("offcanvasContainer");
                  if (offcanvasContainer) {
                    const settingsPanel = document.createElement("div");
                    settingsPanel.className = "offcanvas offcanvas-end";
                    settingsPanel.id = settingsId;
                    settingsPanel.setAttribute("tabindex", "-1");
                    settingsPanel.innerHTML = `
                            <div class="offcanvas-header">
                              <h5 class="offcanvas-title">Image Settings</h5>
                              <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                            </div>
                            <div class="offcanvas-body">
                              <!-- Image Upload -->
                              <div class="mb-3">
                                <label for="${settingsId}-upload" class="form-label">Upload Image</label>
                                <input class="form-control" type="file" id="${settingsId}-upload" accept="image/*" onchange="previewImage('${imageId}', this)">
                              </div>

                              <!-- Image URL -->
                              <div class="mb-3">
                                <label for="${settingsId}-url" class="form-label">Or Enter Image URL</label>
                                <input type="text" class="form-control" id="${settingsId}-url" placeholder="https://example.com/image.jpg">
                              </div>

                              <!-- Fit Mode -->
                              <div class="mb-3">
                                <label class="form-label">Fit Mode</label>
                                <select class="form-select" id="${settingsId}-fit">
                                  <option value="contain">Contain (show all)</option>
                                  <option value="cover">Cover (fill area)</option>
                                  <option value="fill">Stretch to fill</option>
                                  <option value="none">Original size</option>
                                </select>
                              </div>

                              <!-- Position -->
                              <div class="mb-3">
                                <label class="form-label">Position</label>
                                <select class="form-select" id="${settingsId}-position">
                                  <option value="center">Center</option>
                                  <option value="top">Top</option>
                                  <option value="bottom">Bottom</option>
                                  <option value="left">Left</option>
                                  <option value="right">Right</option>
                                  <option value="top-left">Top Left</option>
                                  <option value="top-right">Top Right</option>
                                  <option value="bottom-left">Bottom Left</option>
                                  <option value="bottom-right">Bottom Right</option>
                                </select>
                              </div>

                              <!-- Background Color -->
                              <div class="mb-3">
                                <label for="${settingsId}-bgcolor" class="form-label">Background Color</label>
                                <input type="color" class="form-control form-control-color" id="${settingsId}-bgcolor" value="#ffffff">
                              </div>

                              <!-- Border -->
                              <div class="mb-3">
                                <div class="form-check form-switch">
                                  <input class="form-check-input" type="checkbox" id="${settingsId}-border">
                                  <label class="form-check-label" for="${settingsId}-border">Show Border</label>
                                </div>
                              </div>

                              <!-- Border Radius -->
                              <div class="mb-3">
                                <label class="form-label">Border Radius (px)</label>
                                <input type="range" class="form-range" min="0" max="50" value="0" id="${settingsId}-radius">
                              </div>

                              <!-- Apply Button -->
                              <button class="btn btn-primary w-100" onclick="applyImageSettings('${imageId}', '${settingsId}')">
                                Apply Changes
                              </button>
                            </div>`;
                    offcanvasContainer.appendChild(settingsPanel);
                  }
                }
                // Now initialize the widget (event listeners, etc.)
                window.initImageWidget(imageId);
              }
            }

            // Pie chart widget auto-init
            const pieChartWidgets = item.el.querySelectorAll(
              '.widget .chart-container[id^="piechart-"]'
            );
            pieChartWidgets.forEach(function (container) {
              if (
                container &&
                container.id &&
                typeof window.initPieChart === "function"
              ) {
                setTimeout(function () {
                  window.initPieChart(container.id);
                }, 0);
              }
            });

            const sectionContainer = item.el.querySelector(
              ".section-container-widget"
            );
            if (sectionContainer) {
              const nestedGridContainer = sectionContainer.querySelector(
                ".nested-grid-container"
              );
              if (
                nestedGridContainer &&
                typeof window.initSectionContainer === "function"
              ) {
                // Only initialize if not already initialized
                if (!nestedGridContainer.querySelector(".grid-stack")) {
                  window.initSectionContainer(nestedGridContainer.id);
                }
              }
            }
          });
        });
      });

      // Initialize the MCP pattern
      const gridModel = new GridModel();
      const gridController = new GridController(gridModel);
      const gridPresenter = new GridPresenter(gridModel, gridController);

      // Auto-create test section with charts on page load
      // This functionality is now handled by onload-section-container.js

      // Register the main grid with the model
      gridModel.registerGrid("main", grid);

      // Function to check the selected node
      function checkSelectedNode() {
        const info = gridController.getSelectedNodeInfo();

        if (info.status === "error") {
          alert(info.message);
          return;
        }

        // Create a formatted message with the node information
        const message =
          `Selected Node Information:\n\n` +
          `Grid ID: ${info.gridId}\n` +
          `Position: (${info.position.x}, ${info.position.y})\n` +
          `Size: ${info.position.w}x${info.position.h}\n` +
          `Content: ${info.content.substring(0, 50)}${
            info.content.length > 50 ? "..." : ""
          }`;

        alert(message);
        console.log("Selected Node Details:", info);
      }

      // Function to remove the selected node
      function removeSelectedNode() {
        const result = gridController.removeSelectedNode();
        if (result.status === "error") {
          alert(result.message);
        } else {
          console.log(result.message);
        }
      }

      // Functions to switch grid mode
      function setStatic(val) {
        staticGrid = val;
        grid.setStatic(staticGrid);
      }

      // Add a new simple widget to the main grid
      function addWidget() {
        grid.addWidget({ x: 0, y: 100, content: "new item" });
      }

      // Function to create a section container widget
      function addSectionContainerWidget() {
        const containerId =
          "section-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 6,
          h: 4,
          content: getSectionContainerWidgetMarkup(containerId),
        });
        // Initialize nested grid after the widget is added to DOM
        requestAnimationFrame(() => {
          initSectionContainer(containerId);
        });
      }

      // Function to remove a section container
      function removeSectionContainer(button) {
        // Find the section container widget
        const sectionContainer = button.closest(".section-container-widget");
        if (!sectionContainer) {
          console.error("Could not find section container");
          return;
        }

        // Find the grid-stack-item that contains this section
        const gridItem = sectionContainer.closest(".grid-stack-item");
        if (!gridItem) {
          console.error("Could not find grid item");
          return;
        }

        // Find the nested grid inside this section
        const nestedGridContainer = sectionContainer.querySelector(
          ".nested-grid-container"
        );
        if (!nestedGridContainer) {
          console.error("Could not find nested grid container");
          return;
        }

        const nestedGridElement =
          nestedGridContainer.querySelector(".grid-stack");
        if (nestedGridElement) {
          // Find and dispose all chart instances in this section
          const chartContainers =
            nestedGridElement.querySelectorAll(".chart-container");
          chartContainers.forEach((container) => {
            if (container.chart) {
              container.chart.dispose();
            }
          });

          // Find and destroy all Handsontable instances in this section
          const spreadsheetContainers = nestedGridElement.querySelectorAll(
            ".spreadsheet-container"
          );
          spreadsheetContainers.forEach((container) => {
            if (container.hotInstance) {
              container.hotInstance.destroy();
            }
          });

          // Destroy the nested grid instance if it exists
          if (nestedGridElement.gridstack) {
            nestedGridElement.gridstack.destroy(true);
          }
        }

        // Get the main grid instance and remove the section widget
        if (window.grid && window.grid.engine) {
          try {
            window.grid.removeWidget(gridItem);
          } catch (error) {
            console.error("Error removing widget:", error);
          }
        } else {
          console.error("Main grid not initialized");
          // Fallback: remove the grid item directly from DOM if grid instance is not available
          gridItem.remove();
        }
      }

      // Add a new widget to a nested grid specified by selector (e.g. '.sub1' or '.sub2')
      function addNewWidget(selector) {
        let subGrid = document.querySelector(selector).gridstack;
        let node = {
          x: Math.round(6 * Math.random()),
          y: Math.round(5 * Math.random()),
          w: Math.round(1 + Math.random()),
          h: Math.round(1 + Math.random()),
          content: String(count++),
        };
        subGrid.addWidget(node);
        return false;
      }

      // Function to remove a widget
      function removeWidget(element) {
        // Find the widget container
        const widgetElement = element.closest(".grid-stack-item");
        if (widgetElement) {
          // Find any chart containers within the widget
          const chartContainers =
            widgetElement.querySelectorAll(".chart-container");

          // Clean up any amCharts instances and observers
          chartContainers.forEach((container) => {
            // Clean up amCharts v4 instances
            if (container.chart) {
              console.log("Disposing amCharts instance");
              container.chart.dispose();
            }

            // Clean up resize observers
            if (container.resizeObserver) {
              console.log("Disconnecting resize observer");
              container.resizeObserver.disconnect();
            }
          });

          // Remove the widget from the grid
          grid.removeWidget(widgetElement);
        } else {
          console.error("Could not find widget element");
        }
      }

      // Save, Destroy, and Load functions to test serialization
      // Removed save, destroy, and load functions
    </script>

    <!-- Widget Category Switching Script -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Widget category switching
        const categories = document.querySelectorAll(".widget-category");
        const allWidgets = document.querySelectorAll(".widget-item");

        categories.forEach((category) => {
          category.addEventListener("click", function () {
            // Remove active class from all categories
            categories.forEach((c) => c.classList.remove("active"));

            // Add active class to clicked category
            this.classList.add("active");

            // Get the selected category
            const selectedCategory = this.getAttribute("data-category");

            // Show/hide widgets based on category
            allWidgets.forEach((widget) => {
              if (
                widget.classList.contains(`widget-category-${selectedCategory}`)
              ) {
                widget.style.display = "flex";
              } else {
                widget.style.display = "none";
              }
            });
          });
        });

        // Show "insert" category widgets by default
        document
          .querySelector('.widget-category[data-category="insert"]')
          .click();
      });
    </script>

    <!-- Section Gallery Offcanvas -->

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Section Gallery button click handler
        const sectionGalleryBtn = document.querySelector(
          ".section-gallery-btn"
        );
        if (sectionGalleryBtn) {
          sectionGalleryBtn.addEventListener("click", function () {
            // Check if there's an existing instance and dispose it
            const offcanvasElement = document.getElementById(
              "sectionGalleryOffcanvas"
            );
            const existingOffcanvas =
              bootstrap.Offcanvas.getInstance(offcanvasElement);

            if (existingOffcanvas) {
              existingOffcanvas.dispose();
            }

            // Create a new offcanvas instance with explicit options
            const offcanvas = new bootstrap.Offcanvas(offcanvasElement, {
              backdrop: true,
              keyboard: true,
              scroll: false,
            });

            // Show the offcanvas
            offcanvas.show();
          });
        }

        // Template search functionality
        const templateSearch = document.getElementById("templateSearch");
        const templates = document.querySelectorAll(".section-template");

        templateSearch.addEventListener("input", function (e) {
          const searchTerm = e.target.value.toLowerCase();

          templates.forEach((template) => {
            const title = template
              .querySelector(".template-title")
              .textContent.toLowerCase();
            const description = template
              .querySelector(".template-description")
              .textContent.toLowerCase();
            const widgets = Array.from(
              template.querySelectorAll(".widget-item")
            ).map((widget) => widget.textContent.toLowerCase());

            const matches =
              title.includes(searchTerm) ||
              description.includes(searchTerm) ||
              widgets.some((widget) => widget.includes(searchTerm));

            template.style.display = matches ? "block" : "none";
          });
        });

        // Tab switching functionality
        const tabs = document.querySelectorAll(".section-gallery-tab");

        tabs.forEach((tab) => {
          tab.addEventListener("click", function () {
            // Remove active class from all tabs
            tabs.forEach((t) => t.classList.remove("active"));

            // Add active class to clicked tab
            this.classList.add("active");

            const category = this.getAttribute("data-category");

            templates.forEach((template) => {
              if (category === "all") {
                template.style.display = "block";
              } else {
                template.style.display =
                  template.getAttribute("data-type") === category
                    ? "block"
                    : "none";
              }
            });
          });
        });

        // Preview button functionality
        const previewButtons = document.querySelectorAll(
          ".template-preview-btn"
        );

        previewButtons.forEach((button) => {
          button.addEventListener("click", function (e) {
            e.stopPropagation(); // Prevent template selection
            const template = this.closest(".section-template");
            const templateTitle =
              template.querySelector(".template-title").textContent;

            // Here you can implement the preview functionality
            console.log("Preview requested for:", templateTitle);
          });
        });

        // Template selection
        templates.forEach((template) => {
          template.addEventListener("click", function () {
            const templateTitle =
              this.querySelector(".template-title").textContent;
            console.log("Template selected:", templateTitle);
            // Here you can implement the template selection functionality
          });
        });
      });
    </script>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Section Gallery template click handler
        document.querySelectorAll(".section-template").forEach((template) => {
          template.addEventListener("click", function () {
            const type = this.getAttribute("data-type");

            // Call the appropriate function based on type
            switch (type) {
              case "analytics":
                createAnalyticsSection();
                return;
              case "dashboard":
                createDashboardSection();
                return;
              case "comparison":
                createComparisonSection();
                return;
              case "summary":
                createSummarySection();
                return;
              case "trend":
                createTrendSection();
                return;
            }

            const title =
              this.querySelector(".template-title").textContent.trim();
            const widgets = Array.from(
              this.querySelectorAll(".widget-item")
            ).map((widget) => ({
              type: widget.textContent
                .trim()
                .toLowerCase()
                .replace(/\s+/g, "-"),
              title: widget.textContent.trim(),
            }));

            // Create a new section container
            const sectionContainer = document.createElement("div");
            sectionContainer.className = "grid-stack-item";
            sectionContainer.setAttribute("gs-w", "12");
            sectionContainer.setAttribute("gs-h", "6");

            // Create the section content
            const sectionContent = document.createElement("div");
            sectionContent.className =
              "grid-stack-item-content section-container";

            // Create section header
            const sectionHeader = document.createElement("div");
            sectionHeader.className = "section-header";
            sectionHeader.innerHTML = `
              <h5 class="section-title">${title}</h5>
              <div class="section-actions">
                <button class="btn btn-sm btn-outline-secondary edit-section">
                  <i class="las la-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger remove-section">
                  <i class="las la-trash"></i>
                </button>
              </div>
            `;

            // Create grid container for widgets
            const gridContainer = document.createElement("div");
            gridContainer.className = "widget-grid";
            gridContainer.style.cssText =
              "display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem; padding: 1rem; height: calc(100% - 48px);";

            // Add widgets to the grid
            widgets.forEach((widget) => {
              const widgetContainer = document.createElement("div");
              widgetContainer.className = "widget-container";
              widgetContainer.style.cssText =
                "background: #fff; border: 1px solid rgba(0,0,0,0.1); border-radius: 0px; padding: 1rem; height: 100%;";

              // Add widget content based on type
              switch (widget.type) {
                case "performance-metrics-widget":
                case "distribution-chart-widget":
                case "trend-analysis-widget":
                  widgetContainer.innerHTML = `<div class="chart-widget" style="height: 100%;"></div>`;
                  break;
                case "revenue-tracker-widget":
                case "expense-monitor-widget":
                case "budget-analysis-widget":
                  widgetContainer.innerHTML = `<div class="financial-widget" style="height: 100%;"></div>`;
                  break;
                case "gallery-grid-widget":
                case "video-player-widget":
                case "audio-player-widget":
                  widgetContainer.innerHTML = `<div class="media-widget" style="height: 100%;"></div>`;
                  break;
                case "data-table-widget":
                case "pdf-report-widget":
                case "print-layout-widget":
                  widgetContainer.innerHTML = `<div class="report-widget" style="height: 100%;"></div>`;
                  break;
                default:
                  widgetContainer.innerHTML = `<div class="default-widget" style="height: 100%;"></div>`;
              }

              gridContainer.appendChild(widgetContainer);
            });

            // Assemble the section container
            sectionContent.appendChild(sectionHeader);
            sectionContent.appendChild(gridContainer);
            sectionContainer.appendChild(sectionContent);

            // Add to the grid
            const grid = document.querySelector(".grid-stack");
            if (grid && window.grid) {
              window.grid.addWidget(sectionContainer);
            }

            // Close the offcanvas
            const offcanvasElement = document.querySelector(
              ".section-gallery-offcanvas"
            );
            const offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
            offcanvas.hide();
          });
        });
      });
    </script>

    <script>
      // Function to initialize line chart
      function initializeLineChart(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) {
          console.error("Container not found:", containerId);
          return;
        }

        // Create root element
        var root = am5.Root.new(containerId);

        // Set themes
        root.setThemes([am5themes_Animated.new(root)]);

        // Create chart
        var chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "none",
            wheelY: "none",
          })
        );

        // Create axes
        var xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "category",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll(data.categories.map((category) => ({ category })));

        var yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        // Create series
        var series = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: data.series[0].name,
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "category",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        // Set data
        series.data.setAll(
          data.series[0].data.map((value, index) => ({
            category: data.categories[index],
            value: value,
          }))
        );

        // Add cursor
        chart.set("cursor", am5xy.XYCursor.new(root, {}));

        // Add legend
        var legend = chart.rightAxes.push(
          am5.Legend.new(root, {
            centerY: am5.p50,
            y: am5.p50,
            layout: root.verticalLayout,
          })
        );
        legend.data.setAll(chart.series.values);

        // Save chart instance to container for later access
        container.chart = chart;
      }

      // Function to initialize spreadsheet
      function initializeSpreadsheet(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) {
          console.error("Container not found:", containerId);
          return;
        }

        new Handsontable(container, {
          data: data,
          rowHeaders: true,
          colHeaders: true,
          height: "100%",
          width: "100%",
          licenseKey: "non-commercial-and-evaluation",
          stretchH: "all",
          autoColumnSize: true,
          contextMenu: true,
          manualRowResize: true,
          manualColumnResize: true,
        });
      }

      // Function to remove individual widget from nested grid
      function removeWidget(button) {
        const widgetItem = button.closest(".grid-stack-item");
        if (!widgetItem) return;

        // Get the nested grid instance
        const nestedGrid = widgetItem.closest(".grid-stack").gridstack;
        if (!nestedGrid) return;

        // Check if it's a chart widget and clean up
        const chartContainer = widgetItem.querySelector(".chart-container");
        if (chartContainer && chartContainer.chart) {
          chartContainer.chart.dispose();
        }

        // Check if it's a spreadsheet widget and clean up
        const spreadsheetContainer = widgetItem.querySelector(
          ".spreadsheet-container"
        );
        if (spreadsheetContainer && spreadsheetContainer.hotInstance) {
          spreadsheetContainer.hotInstance.destroy();
        }

        // Remove the widget from the nested grid
        nestedGrid.removeWidget(widgetItem);
      }

      // Function to create analytics section with spreadsheet and chart
      function createAnalyticsSection() {
        // Create a section container widget
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 12,
          h: 8,
          content: `
            <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;">
              <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                <div>
                  <i class="las la-chart-line"></i> Analytics Section
                </div>
                <div>
                  <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                    <i class="las la-times"></i>
                  </button>
                </div>
              </div>
              <div class="nested-grid-container" style="height: calc(100% - 40px); overflow: hidden;"></div>
            </div>
          `,
        });

        // Wait for the widget to be added to the DOM
        setTimeout(() => {
          const nestedGridContainer = widget.querySelector(
            ".nested-grid-container"
          );
          const nestedGridElement = document.createElement("div");
          nestedGridElement.className = "grid-stack";
          nestedGridContainer.appendChild(nestedGridElement);

          const nestedGrid = GridStack.init(
            {
              column: 12,
              cellHeight: "auto",
              margin: 5,
              float: true,
              children: [],
              removable: true,
              removeTimeout: 0,
              disableOneColumnMode: true,
              animate: false,
            },
            nestedGridElement
          );

          nestedGridElement.gridstack = nestedGrid;

          const spreadsheetId = `spreadsheet-${Date.now()}`;
          const chartId = `chart-${Date.now()}`;

          // Add spreadsheet widget to the nested grid (left side)
          nestedGrid.addWidget({
            x: 0,
            y: 0,
            w: 6,
            h: 6,
            content: `
              <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
                <div class="widget-header mb-2 mb-2">
                  <div>
                    <i class="las la-table"></i> Analytics Data
                  </div>
                  <div>
                    <button class="btn btn-sm btn-link text-dark" data-bs-toggle="offcanvas" data-bs-target="#spreadsheetSettings" aria-controls="spreadsheetSettings">
                      <i class="las la-cog"></i>
                    </button>
                  </div>
                </div>
                <div id="${spreadsheetId}" class="spreadsheet-container" style="flex: 1; overflow: hidden;"></div>
              </div>
            `,
          });

          // Add chart widget to the nested grid (right side)
          nestedGrid.addWidget({
            x: 6,
            y: 0,
            w: 6,
            h: 6,
            content: `
              <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
                <div class="widget-header mb-2 mb-2">
                  <div>
                    <i class="las la-chart-line"></i> Analytics Chart
                  </div>
                  <div>
                    <button class="btn btn-sm btn-link text-dark" data-bs-toggle="offcanvas" data-bs-target="#lineChartSettings" aria-controls="lineChartSettings">
                      <i class="las la-cog"></i>
                    </button>
                  </div>
                </div>
                <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
              </div>
            `,
          });

          // Initialize spreadsheet with sample data
          const spreadsheetData = [
            ["Metric", "Q1", "Q2", "Q3", "Q4"],
            ["Revenue", 100000, 120000, 150000, 180000],
            ["Users", 5000, 6000, 7500, 9000],
            ["Engagement", 75, 78, 82, 85],
            ["Conversion", 2.5, 2.8, 3.2, 3.5],
          ];

          // Wait a bit for the DOM to be ready
          setTimeout(() => {
            const spreadsheetContainer = document.getElementById(spreadsheetId);
            if (spreadsheetContainer) {
              const hot = new Handsontable(spreadsheetContainer, {
                data: spreadsheetData,
                rowHeaders: true,
                colHeaders: true,
                height: "100%",
                licenseKey: "non-commercial-and-evaluation",
                stretchH: "all",
              });
              spreadsheetContainer.hotInstance = hot;
            }

            const chartContainer = document.getElementById(chartId);
            if (chartContainer) {
              const root = am5.Root.new(chartId);
              root.setThemes([am5themes_Animated.new(root)]);

              const chart = root.container.children.push(
                am5xy.XYChart.new(root, {
                  panX: false,
                  panY: false,
                  wheelX: "none",
                  wheelY: "none",
                })
              );

              const cursor = chart.set(
                "cursor",
                am5xy.XYCursor.new(root, {
                  behavior: "none",
                })
              );
              cursor.lineY.set("visible", false);

              const xAxis = chart.xAxes.push(
                am5xy.CategoryAxis.new(root, {
                  categoryField: "quarter",
                  renderer: am5xy.AxisRendererX.new(root, {}),
                  tooltip: am5.Tooltip.new(root, {}),
                })
              );

              const yAxis = chart.yAxes.push(
                am5xy.ValueAxis.new(root, {
                  min: 0,
                  renderer: am5xy.AxisRendererY.new(root, {}),
                })
              );

              const series = chart.series.push(
                am5xy.LineSeries.new(root, {
                  name: "Revenue",
                  xAxis: xAxis,
                  yAxis: yAxis,
                  valueYField: "value",
                  categoryXField: "quarter",
                  tooltip: am5.Tooltip.new(root, {
                    labelText: "{valueY}",
                  }),
                })
              );

              const data = [
                { quarter: "Q1", value: 100000 },
                { quarter: "Q2", value: 120000 },
                { quarter: "Q3", value: 150000 },
                { quarter: "Q4", value: 180000 },
              ];

              xAxis.data.setAll(data);
              series.data.setAll(data);

              series.appear(1000);
              chart.appear(1000, 100);

              chartContainer.chart = root;
            }
          }, 100);

          closeGallery();
        }, 100);
      }

      // Function to remove a section container
      function removeSectionContainer(button) {
        // Find the section container widget
        const sectionContainer = button.closest(".section-container-widget");
        if (!sectionContainer) {
          console.error("Could not find section container");
          return;
        }

        // Find the grid-stack-item that contains this section
        const gridItem = sectionContainer.closest(".grid-stack-item");
        if (!gridItem) {
          console.error("Could not find grid item");
          return;
        }

        // Find the nested grid inside this section
        const nestedGridContainer = sectionContainer.querySelector(
          ".nested-grid-container"
        );
        if (!nestedGridContainer) {
          console.error("Could not find nested grid container");
          return;
        }

        const nestedGridElement =
          nestedGridContainer.querySelector(".grid-stack");
        if (nestedGridElement) {
          // Find and dispose all chart instances in this section
          const chartContainers =
            nestedGridElement.querySelectorAll(".chart-container");
          chartContainers.forEach((container) => {
            if (container.chart) {
              container.chart.dispose();
            }
          });

          // Find and destroy all Handsontable instances in this section
          const spreadsheetContainers = nestedGridElement.querySelectorAll(
            ".spreadsheet-container"
          );
          spreadsheetContainers.forEach((container) => {
            if (container.hotInstance) {
              container.hotInstance.destroy();
            }
          });

          // Destroy the nested grid instance if it exists
          if (nestedGridElement.gridstack) {
            nestedGridElement.gridstack.destroy(true);
          }
        }

        // Get the main grid instance and remove the section widget
        if (window.grid && window.grid.engine) {
          try {
            window.grid.removeWidget(gridItem);
          } catch (error) {
            console.error("Error removing widget:", error);
          }
        } else {
          console.error("Main grid not initialized");
          // Fallback: remove the grid item directly from DOM if grid instance is not available
          gridItem.remove();
        }
      }

      // Function to create dashboard section
      function createDashboardSection() {
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 12,
          h: 12,
          content: `
            <div class="section-container-widget p-2" style="height: 100%; display: flex; flex-direction: column;">
              <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                <div data-editable="true" title="Click to edit title" class="editable-title">
                  Dashboard Section
                </div>
                <div>
                  <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                    <i class="las la-times"></i>
                  </button>
                </div>
              </div>
              <div class="nested-grid-container" style="flex: 1; position: relative;">
                <div class="grid-stack" style="position: absolute; inset: 0; padding: 10px;"></div>
              </div>
            </div>
          `,
        });

        setTimeout(() => {
          const nestedGridContainer = widget.querySelector(
            ".nested-grid-container .grid-stack"
          );

          const nestedGrid = GridStack.init(
            {
              column: 12,
              cellHeight: 60,
              margin: 10,
              float: true,
              children: [],
              removable: true,
              removeTimeout: 0,
              disableOneColumnMode: true,
              animate: false,
              minHeight: 150,
              alwaysShowResizeHandle: true,
              resizable: {
                handles: "all",
              },
            },
            nestedGridContainer
          );

          // Create a unique ID for the stack chart
          const stackChartId = `stackchart-${Date.now()}`;

          // Add Stack Chart Widget (Monthly Trends)
          nestedGrid.addWidget({
            x: 0,
            y: 0,
            w: 8,
            h: 6,
            content: `
              <div class="stack-chart-widget p-2">
                <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                  <div>
                    <i class="las la-layer-group"></i> Monthly Trends
                  </div>
                  <div>
                    <button class="btn btn-sm btn-link text-dark"
                            data-bs-toggle="offcanvas"
                            data-bs-target="#stackChartSettings"
                            aria-controls="stackChartSettings"
                            onclick="initStackChartSettings('${stackChartId}')">
                      <i class="las la-cog"></i>
                    </button>
                  </div>
                </div>
                <div id="${stackChartId}" class="chart-container"></div>
              </div>
            `,
          });

          // Add KPI Cards with adjusted heights
          nestedGrid.addWidget({
            x: 8,
            y: 0,
            w: 4,
            h: 3,
            content: createKPICardWidget("Total Revenue", "$150,000", "+15%"),
            minHeight: 150,
          });

          nestedGrid.addWidget({
            x: 8,
            y: 3,
            w: 4,
            h: 3,
            content: createKPICardWidget("Active Users", "25,000", "+8%"),
            minHeight: 150,
          });

          // Add Data Table with more height
          nestedGrid.addWidget({
            x: 0,
            y: 6,
            w: 12,
            h: 6,
            content: createDataTableWidget("Performance Metrics"),
            minHeight: 300,
          });

          // Add styles to ensure proper height and scrolling
          const style = document.createElement("style");
          style.textContent = `
            .grid-stack > .grid-stack-item > .grid-stack-item-content {
              inset: 0;
              height: 100%;
              overflow: auto;
            }
            .grid-stack-item-content {
              height: 100%;
            }
            .card {
              height: 100%;
              overflow: hidden;
            }
            .card-body {
              height: 100%;
              overflow: hidden;
            }
            .table-responsive {
              overflow-y: auto;
              max-height: calc(100% - 40px);
            }
            .chart-container {
              height: calc(100% - 40px) !important;
            }
          `;
          document.head.appendChild(style);

          // Initialize widgets and update layout
          initializeWidgets();

          // Initialize the stack chart
          setTimeout(() => {
            try {
              if (window.initStackedColumnChart) {
                // Check if the chart container exists
                const chartContainer = document.getElementById(stackChartId);
                if (chartContainer) {
                  // Check if the chart has already been initialized
                  if (!chartContainer.hasAttribute("data-initialized")) {
                    window.initStackedColumnChart(stackChartId);
                    // Mark the chart as initialized
                    chartContainer.setAttribute("data-initialized", "true");
                  } else {
                    console.log("Chart already initialized, skipping");
                  }
                } else {
                  console.error("Chart container not found:", stackChartId);
                }
              } else {
                console.error("initStackedColumnChart function not found");
              }
            } catch (error) {
              console.error("Error initializing stack chart:", error);
            }
          }, 200);

          nestedGrid.compact();

          // Force a resize after a brief delay to ensure proper layout
          setTimeout(() => {
            window.dispatchEvent(new Event("resize"));
            nestedGrid.compact();
          }, 400);

          closeGallery();
        }, 100);
      }

      // Update the Line Chart Widget to ensure proper height
      function createLineChartWidget(title) {
        return `
          <div class="card">
            <div class="card-body d-flex flex-column">
              <h6 class="card-title text-muted mb-3">${title}</h6>
              <div class="chart-container" style="position: relative;"></div>
            </div>
          </div>
        `;
      }

      // Update the KPI Card Widget with minimum height
      function createKPICardWidget(title, value, change) {
        return `
          <div class="card">
            <div class="card-body d-flex flex-column justify-content-between">
              <h6 class="card-title text-muted">${title}</h6>
              <div class="d-flex align-items-center justify-content-between">
                <h3 class="mb-0">${value}</h3>
                <span class="badge ${
                  change.startsWith("+") ? "bg-success" : "bg-danger"
                }">${change}</span>
              </div>
            </div>
          </div>
        `;
      }

      // Update the Data Table Widget with minimum height
      function createDataTableWidget(title) {
        return `
          <div class="card">
            <div class="card-body d-flex flex-column">
              <h6 class="card-title text-muted mb-3">${title}</h6>
              <div class="table-responsive">
                <table class="table table-sm table-hover">
                  <thead>
                    <tr>
                      <th>Metric</th>
                      <th>Current</th>
                      <th>Previous</th>
                      <th>Change</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Revenue</td>
                      <td>$150,000</td>
                      <td>$130,000</td>
                      <td class="text-success">+15%</td>
                    </tr>
                    <tr>
                      <td>Users</td>
                      <td>25,000</td>
                      <td>23,000</td>
                      <td class="text-success">+8%</td>
                    </tr>
                    <tr>
                      <td>Conversion</td>
                      <td>3.2%</td>
                      <td>2.8%</td>
                      <td class="text-success">+14%</td>
                    </tr>
                    <tr>
                      <td>Engagement</td>
                      <td>68%</td>
                      <td>65%</td>
                      <td class="text-success">+4%</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        `;
      }

      // Function to create comparison section
      function createComparisonSection() {
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 12,
          h: 8,
          content: `
            <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;">
              <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                <div>
                  <i class="las la-balance-scale"></i> Comparison Section
                </div>
                <div>
                  <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                    <i class="las la-times"></i>
                  </button>
                </div>
              </div>
              <div class="nested-grid-container" style="height: calc(100% - 40px); overflow: hidden;"></div>
            </div>
          `,
        });

        setTimeout(() => {
          const nestedGridContainer = widget.querySelector(
            ".nested-grid-container"
          );
          const nestedGridElement = document.createElement("div");
          nestedGridElement.className = "grid-stack";
          nestedGridContainer.appendChild(nestedGridElement);

          const nestedGrid = GridStack.init(
            {
              column: 12,
              cellHeight: "auto",
              margin: 5,
              float: true,
              children: [],
              removable: true,
              removeTimeout: 0,
              disableOneColumnMode: true,
              animate: false,
            },
            nestedGridElement
          );

          nestedGridElement.gridstack = nestedGrid;

          // Add Bar Chart for Comparison
          nestedGrid.addWidget({
            x: 0,
            y: 0,
            w: 6,
            h: 6,
            content: createBarChartWidget("Product Comparison"),
          });

          // Add Percentage Comparison
          nestedGrid.addWidget({
            x: 6,
            y: 0,
            w: 6,
            h: 3,
            content: createPercentageWidget("Market Share"),
          });

          // Add Comparison Table
          nestedGrid.addWidget({
            x: 6,
            y: 3,
            w: 6,
            h: 3,
            content: createComparisonTableWidget("Metrics Comparison"),
          });

          initializeWidgets();
          closeGallery();
        }, 100);
      }

      // Function to create summary section
      function createSummarySection() {
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 12,
          h: 8,
          content: `
            <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;">
              <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                <div>
                  <i class="las la-list-alt"></i> Summary Section
                </div>
                <div>
                  <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                    <i class="las la-times"></i>
                  </button>
                </div>
              </div>
              <div class="nested-grid-container" style="height: calc(100% - 40px); overflow: hidden;"></div>
            </div>
          `,
        });

        setTimeout(() => {
          const nestedGridContainer = widget.querySelector(
            ".nested-grid-container"
          );
          const nestedGridElement = document.createElement("div");
          nestedGridElement.className = "grid-stack";
          nestedGridContainer.appendChild(nestedGridElement);

          const nestedGrid = GridStack.init(
            {
              column: 12,
              cellHeight: "auto",
              margin: 5,
              float: true,
              children: [],
              removable: true,
              removeTimeout: 0,
              disableOneColumnMode: true,
              animate: false,
            },
            nestedGridElement
          );

          nestedGridElement.gridstack = nestedGrid;

          // Add Text Summary
          nestedGrid.addWidget({
            x: 0,
            y: 0,
            w: 12,
            h: 2,
            content: createTextSummaryWidget("Executive Summary"),
          });

          // Add Distribution Chart
          nestedGrid.addWidget({
            x: 0,
            y: 2,
            w: 6,
            h: 4,
            content: createPieChartWidget("Category Distribution"),
          });

          // Add Key Points List
          nestedGrid.addWidget({
            x: 6,
            y: 2,
            w: 6,
            h: 4,
            content: createKeyPointsWidget("Key Findings"),
          });

          initializeWidgets();
          closeGallery();
        }, 100);
      }

      // Function to create trend analysis section
      function createTrendSection() {
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 12,
          h: 8,
          content: `
            <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;">
              <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
                <div>
                  <i class="las la-chart-line"></i> Trend Analysis Section
                </div>
                <div>
                  <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                    <i class="las la-times"></i>
                  </button>
                </div>
              </div>
              <div class="nested-grid-container" style="height: calc(100% - 40px); overflow: hidden;"></div>
            </div>
          `,
        });

        setTimeout(() => {
          const nestedGridContainer = widget.querySelector(
            ".nested-grid-container"
          );
          const nestedGridElement = document.createElement("div");
          nestedGridElement.className = "grid-stack";
          nestedGridContainer.appendChild(nestedGridElement);

          const nestedGrid = GridStack.init(
            {
              column: 12,
              cellHeight: "auto",
              margin: 5,
              float: true,
              children: [],
              removable: true,
              removeTimeout: 0,
              disableOneColumnMode: true,
              animate: false,
            },
            nestedGridElement
          );

          nestedGridElement.gridstack = nestedGrid;

          // Add Time Series Chart
          nestedGrid.addWidget({
            x: 0,
            y: 0,
            w: 8,
            h: 4,
            content: createTimeSeriesWidget("Historical Trends"),
          });

          // Add Area Chart
          nestedGrid.addWidget({
            x: 8,
            y: 0,
            w: 4,
            h: 4,
            content: createAreaChartWidget("Growth Pattern"),
          });

          // Add Trend Table
          nestedGrid.addWidget({
            x: 0,
            y: 4,
            w: 12,
            h: 4,
            content: createTrendTableWidget("Trend Metrics"),
          });

          initializeWidgets();
          closeGallery();
        }, 100);
      }

      // Helper functions for widget creation
      function createLineChartWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-line"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createKPICardWidget(title, value, change) {
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-square"></i> ${title}
              </div>
            </div>
            <div class="d-flex flex-column justify-content-center align-items-center h-100">
              <h3 class="mb-0">${value}</h3>
              <span class="text-success">${change}</span>
            </div>
          </div>
        `;
      }

      function createDataTableWidget(title) {
        const tableId = `table-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-table"></i> ${title}
              </div>
            </div>
            <div id="${tableId}" class="spreadsheet-container" style="flex: 1 1 auto; min-height: 200px; width: 100%; height: 100%; position: relative;"></div>
          </div>
        `;
      }

      function createBarChartWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-bar"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createPercentageWidget(title) {
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-percentage"></i> ${title}
              </div>
            </div>
            <div class="d-flex justify-content-around align-items-center h-100">
              <div class="text-center">
                <h4>Product A</h4>
                <h2>45%</h2>
              </div>
              <div class="text-center">
                <h4>Product B</h4>
                <h2>35%</h2>
              </div>
              <div class="text-center">
                <h4>Others</h4>
                <h2>20%</h2>
              </div>
            </div>
          </div>
        `;
      }

      function createComparisonTableWidget(title) {
        const tableId = `table-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-arrows-alt-h"></i> ${title}
              </div>
            </div>
            <div id="${tableId}" class="spreadsheet-container" style="flex: 1 1 auto; min-height: 200px; width: 100%; height: 100%; position: relative;"></div>
          </div>
        `;
      }

      function createTextSummaryWidget(title) {
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-file-alt"></i> ${title}
              </div>
            </div>
            <div class="p-3" style="flex: 1; overflow: auto;">
              <p>This is an executive summary of the key findings and insights from our analysis. The data shows significant trends in market performance and user engagement.</p>
              <p>Key highlights include increased revenue growth, improved user retention, and expanding market presence.</p>
            </div>
          </div>
        `;
      }

      function createPieChartWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-pie"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createKeyPointsWidget(title) {
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-list"></i> ${title}
              </div>
            </div>
            <div class="p-3" style="flex: 1; overflow: auto;">
              <ul class="list-unstyled">
                <li class="mb-2"><i class="las la-check text-success me-2"></i> Revenue increased by 25% YoY</li>
                <li class="mb-2"><i class="las la-check text-success me-2"></i> Customer satisfaction reached 92%</li>
                <li class="mb-2"><i class="las la-check text-success me-2"></i> New market penetration successful</li>
                <li class="mb-2"><i class="las la-check text-success me-2"></i> Product adoption rate up by 15%</li>
              </ul>
            </div>
          </div>
        `;
      }

      function createTimeSeriesWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-line"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createAreaChartWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-area"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createTrendTableWidget(title) {
        const tableId = `table-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-table"></i> ${title}
              </div>
            </div>
            <div id="${tableId}" class="spreadsheet-container" style="flex: 1 1 auto; min-height: 200px; width: 100%; height: 100%; position: relative;"></div>
          </div>
        `;
      }

      function initializeWidgets() {
        // Initialize all chart containers
        document.querySelectorAll(".chart-container").forEach((container) => {
          if (!container.chart) {
            const root = am5.Root.new(container.id);
            root.setThemes([am5themes_Animated.new(root)]);

            // Create chart based on container's parent widget type
            if (container.closest('[class*="line-chart"]')) {
              createLineChart(root);
            } else if (container.closest('[class*="bar-chart"]')) {
              createBarChart(root);
            } else if (container.closest('[class*="pie-chart"]')) {
              createPieChart(root);
            } else if (container.closest('[class*="area-chart"]')) {
              createAreaChart(root);
            }

            container.chart = root;
          }
        });

        // Initialize all spreadsheet containers
        document
          .querySelectorAll(".spreadsheet-container")
          .forEach((container) => {
            if (!container.hotInstance) {
              console.log(
                "Initializing Handsontable for container:",
                container.id
              );
              console.log(
                "Container dimensions:",
                container.offsetWidth,
                "x",
                container.offsetHeight
              );

              // Ensure the container has proper dimensions
              if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                console.warn(
                  "Container has zero dimensions, setting explicit size"
                );
                container.style.width = "100%";
                container.style.height = "300px";
                container.style.minHeight = "200px";
              }

              try {
                const hot = new Handsontable(container, {
                  data: generateSampleData(),
                  rowHeaders: true,
                  colHeaders: true,
                  height: "100%",
                  width: "100%",
                  licenseKey: "non-commercial-and-evaluation",
                  stretchH: "all",
                  autoColumnSize: true,
                  contextMenu: true,
                  manualColumnResize: true,
                  manualRowResize: true,
                });
                container.hotInstance = hot;
                console.log(
                  "Handsontable initialized successfully for",
                  container.id
                );
              } catch (error) {
                console.error("Error initializing Handsontable:", error);
              }
            }
          });
      }

      function generateSampleData() {
        return [
          ["Category", "Value 1", "Value 2", "Value 3"],
          ["A", 100, 120, 140],
          ["B", 200, 220, 240],
          ["C", 300, 320, 340],
          ["D", 400, 420, 440],
        ];
      }

      function createLineChart(root) {
        const chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "none",
            wheelY: "none",
          })
        );

        const data = [
          { date: "2023-01", value: 100 },
          { date: "2023-02", value: 120 },
          { date: "2023-03", value: 140 },
          { date: "2023-04", value: 130 },
          { date: "2023-05", value: 170 },
        ];

        const xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "date",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll(data);

        const yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        const series = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Series",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "date",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        series.data.setAll(data);
        chart.set("cursor", am5xy.XYCursor.new(root, {}));
      }

      function createBarChart(root) {
        const chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "none",
            wheelY: "none",
          })
        );

        const data = [
          { category: "A", value1: 100, value2: 90 },
          { category: "B", value1: 120, value2: 110 },
          { category: "C", value1: 140, value2: 130 },
          { category: "D", value1: 130, value2: 120 },
        ];

        const xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "category",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll(data);

        const yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        const series = chart.series.push(
          am5xy.ColumnSeries.new(root, {
            name: "Series",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value1",
            categoryXField: "category",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        series.data.setAll(data);
        chart.set("cursor", am5xy.XYCursor.new(root, {}));
      }

      function createPieChart(root) {
        const chart = root.container.children.push(
          am5percent.PieChart.new(root, {
            layout: root.verticalLayout,
          })
        );

        const data = [
          { category: "A", value: 30 },
          { category: "B", value: 25 },
          { category: "C", value: 20 },
          { category: "D", value: 15 },
          { category: "E", value: 10 },
        ];

        const series = chart.series.push(
          am5percent.PieSeries.new(root, {
            valueField: "value",
            categoryField: "category",
            endAngle: 270,
          })
        );

        series.data.setAll(data);
        series.appear(1000, 100);
      }

      function createAreaChart(root) {
        const chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "none",
            wheelY: "none",
          })
        );

        const data = [
          { date: "2023-01", value: 100 },
          { date: "2023-02", value: 120 },
          { date: "2023-03", value: 140 },
          { date: "2023-04", value: 130 },
          { date: "2023-05", value: 170 },
        ];

        const xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "date",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll(data);

        const yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        const series = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Series",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "date",
            fill: am5.color(0x68ad5c),
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        series.fills.template.setAll({
          fillOpacity: 0.3,
          visible: true,
        });

        series.data.setAll(data);
        chart.set("cursor", am5xy.XYCursor.new(root, {}));
      }

      function closeGallery() {
        const offcanvasElement = document.querySelector(
          ".section-gallery-offcanvas"
        );
        const offcanvas = bootstrap.Offcanvas.getInstance(offcanvasElement);
        if (offcanvas) {
          offcanvas.hide();
        }
      }
    </script>

    <!-- Add this before the closing body tag -->
    <!-- Spreadsheet Settings Offcanvas -->

    <script>
      // Function to remove a section container
      function removeSectionContainer(button) {
        // Find the section container widget
        const sectionContainer = button.closest(".section-container-widget");
        if (!sectionContainer) {
          console.error("Could not find section container");
          return;
        }

        // Find the grid-stack-item that contains this section
        const gridItem = sectionContainer.closest(".grid-stack-item");
        if (!gridItem) {
          console.error("Could not find grid item");
          return;
        }

        // Find the nested grid inside this section
        const nestedGridContainer = sectionContainer.querySelector(
          ".nested-grid-container"
        );
        if (!nestedGridContainer) {
          console.error("Could not find nested grid container");
          return;
        }

        const nestedGridElement =
          nestedGridContainer.querySelector(".grid-stack");
        if (nestedGridElement) {
          // Find and dispose all chart instances in this section
          const chartContainers =
            nestedGridElement.querySelectorAll(".chart-container");
          chartContainers.forEach((container) => {
            if (container.chart) {
              container.chart.dispose();
            }
          });

          // Find and destroy all Handsontable instances in this section
          const spreadsheetContainers = nestedGridElement.querySelectorAll(
            ".spreadsheet-container"
          );
          spreadsheetContainers.forEach((container) => {
            if (container.hotInstance) {
              container.hotInstance.destroy();
            }
          });

          // Destroy the nested grid instance if it exists
          if (nestedGridElement.gridstack) {
            nestedGridElement.gridstack.destroy(true);
          }
        }

        // Get the main grid instance and remove the section widget
        if (window.grid && window.grid.engine) {
          try {
            window.grid.removeWidget(gridItem);
          } catch (error) {
            console.error("Error removing widget:", error);
          }
        } else {
          console.error("Main grid not initialized");
          // Fallback: remove the grid item directly from DOM if grid instance is not available
          gridItem.remove();
        }
      }

      // Function to apply spreadsheet settings
      function applySpreadsheetSettings() {
        const title = document.getElementById("spreadsheetTitle").value;
        const showRowNumbers =
          document.getElementById("showRowNumbers").checked;
        const showColumnHeaders =
          document.getElementById("showColumnHeaders").checked;

        // Find the active spreadsheet container
        const activeContainer = document.querySelector(
          ".spreadsheet-container"
        );
        if (activeContainer && activeContainer.hotInstance) {
          const hot = activeContainer.hotInstance;

          // Update settings
          hot.updateSettings({
            rowHeaders: showRowNumbers,
            colHeaders: showColumnHeaders,
          });

          // Update title if provided
          if (title) {
            const headerDiv = activeContainer
              .closest(".grid-stack-item")
              .querySelector(".widget-header mb-2 div:first-child");
            if (headerDiv) {
              headerDiv.innerHTML = `<i class="las la-table"></i> ${title}`;
            }
          }
        }

        // Close the offcanvas
        const offcanvas = bootstrap.Offcanvas.getInstance(
          document.getElementById("spreadsheetSettings")
        );
        if (offcanvas) {
          offcanvas.hide();
        }
      }
    </script>

    <!-- Initialize the main grid -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        if (!window.grid) {
          window.grid = GridStack.init(
            {
              column: 12,
              cellHeight: 60, // Changed from 'auto' to fixed height
              margin: 5,
              float: true,
              animate: false,
              removable: true,
              removeTimeout: 0,
              disableOneColumnMode: true,
              sizeToContent: false, // Disable auto-resizing for main grid
            },
            ".grid-stack"
          );
        }
      });

      function removeSectionContainer(button) {
        if (!window.grid) {
          console.error("Grid not initialized");
          return;
        }

        // Find the section container widget
        const sectionContainer = button.closest(".section-container-widget");
        if (!sectionContainer) {
          console.error("Could not find section container");
          return;
        }

        // Find the grid-stack-item that contains this section
        const gridItem = sectionContainer.closest(".grid-stack-item");
        if (!gridItem) {
          console.error("Could not find grid item");
          return;
        }

        // Find the nested grid inside this section
        const nestedGridContainer = sectionContainer.querySelector(
          ".nested-grid-container"
        );
        if (!nestedGridContainer) {
          console.error("Could not find nested grid container");
          return;
        }

        const nestedGridElement =
          nestedGridContainer.querySelector(".grid-stack");
        if (nestedGridElement) {
          // Find and dispose all chart instances in this section
          const chartContainers =
            nestedGridElement.querySelectorAll(".chart-container");
          chartContainers.forEach((container) => {
            if (container.chart) {
              try {
                container.chart.dispose();
              } catch (e) {
                console.error("Error disposing chart:", e);
              }
            }
          });

          // Find and destroy all Handsontable instances in this section
          const spreadsheetContainers = nestedGridElement.querySelectorAll(
            ".spreadsheet-container"
          );
          spreadsheetContainers.forEach((container) => {
            if (container.hotInstance) {
              try {
                container.hotInstance.destroy();
              } catch (e) {
                console.error("Error destroying spreadsheet:", e);
              }
            }
          });

          // Destroy the nested grid instance if it exists
          if (nestedGridElement.gridstack) {
            try {
              nestedGridElement.gridstack.destroy(true);
            } catch (e) {
              console.error("Error destroying nested grid:", e);
            }
          }
        }

        try {
          // Remove the widget from the main grid
          window.grid.removeWidget(gridItem);
        } catch (error) {
          console.error("Error removing widget:", error);
          // Fallback: remove the grid item directly from DOM
          try {
            gridItem.remove();
          } catch (e) {
            console.error("Error removing grid item from DOM:", e);
          }
        }
      }
    </script>

    <!-- Workspace functionality -->
    <script src="js/workspace.js"></script>
    <!-- Pie Chart Compact Mode -->
    <script src="js/pie-chart-compact-mode.js"></script>
    <!-- Smart Widget Composer -->
    <!-- <script src="js/smartWidgetComposer.js"></script> -->

    <script src="./js/widget-sidebar.js"></script>

    <!-- Add this before the closing body tag -->

    <!-- Chart functions moved to external file -->
    <script src="dummy_chart.js"></script>

    <script src="onload-section-container.js"></script>

    <script>
      // Setup drag-in functionality after DOM is ready
      let textSidebarContent = [
        {
          w: 4,
          h: 4,
          // minW: 2, // to restrict the minimum width
          // minH: 2, // to restrict the minimum height
          get content() {
            // Generate unique ids for drag-in
            const textId =
              "text-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
            const settingsId = "settings-" + textId;
            if (typeof getTextWidgetMarkup === "function") {
              return getTextWidgetMarkup({ textId, settingsId });
            }
            // fallback to old markup if function not loaded
            return `
          <div class="text-widget p-2">
            <div class="widget-header mb-2 fw-bold">
              <i class="las la-font"></i> Text Widget
            </div>
            <div class="text-container">
              <div class="text-content" contenteditable="true" style="
                min-height: 100px;
                padding: 10px;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                outline: none;
                font-family: Arial, sans-serif;
                font-size: 14px;
                line-height: 1.5;
                color: #333;
                background-color: #fff;
              ">
                <p>Click here to edit text...</p>
              </div>
            </div>
          </div>
          `;
          },
        },
      ];

      let tableSidebarContent = [
        {
          w: 6,
          h: 6,
          get content() {
            const tableId =
              "table-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
            const settingsId = "settings-" + tableId;
            if (typeof getTableWidgetMarkup === "function") {
              return getTableWidgetMarkup({ tableId, settingsId });
            }
            // fallback to old markup if function not loaded
            return `
          <div class="table-widget p-2">
            <div class="widget-header mb-2 fw-bold">
              <i class="las la-table"></i> Table Widget
            </div>
            <div class="table-container">
              <div class="table-responsive">
                <table class="table table-bordered table-striped">
                  <thead>
                    <tr>
                      <th>Column 1</th>
                      <th>Column 2</th>
                      <th>Column 3</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td contenteditable="true">Row 1, Col 1</td>
                      <td contenteditable="true">Row 1, Col 2</td>
                      <td contenteditable="true">Row 1, Col 3</td>
                    </tr>
                    <tr>
                      <td contenteditable="true">Row 2, Col 1</td>
                      <td contenteditable="true">Row 2, Col 2</td>
                      <td contenteditable="true">Row 2, Col 3</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          `;
          },
        },
      ];

      let lineSeparatorSidebarContent = [
        {
          w: 2,
          h: 1,
          get content() {
            const separatorId =
              "line-separator-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            const settingsId = "settings-" + separatorId;
            if (typeof getLineSeparatorWidgetMarkup === "function") {
              return getLineSeparatorWidgetMarkup({ separatorId, settingsId });
            }
            // fallback to old markup if function not loaded
            return `
          <div class="line-separator-widget p-2">
            <div class="widget-header mb-2 fw-bold">
              <i class="las la-minus"></i> Line Separator
            </div>
            <div class="line-separator-container">
              <div class="line-separator horizontal solid" style="
                border-top: 2px solid #333;
                width: 100%;
                height: 0;
                margin: 20px 0;
              "></div>
            </div>
          </div>
          `;
          },
        },
      ];

      let imageSidebarContent = [
        {
          w: 4,
          h: 4,
          get content() {
            const imageId =
              "image-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
            const settingsId = "settings-" + imageId;
            if (typeof getImageWidgetMarkup === "function") {
              return getImageWidgetMarkup({ imageId, settingsId });
            }
            throw new Error("getImageWidgetMarkup is not loaded");
          },
        },
      ];

      let notesSectionSidebarContent = [
        {
          w: 6,
          h: 2,
          get content() {
            const notesId =
              "notes-section-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            const settingsId = "settings-" + notesId;
            if (typeof getNotesSectionWidgetMarkup === "function") {
              return getNotesSectionWidgetMarkup({ notesId, settingsId });
            }
            // fallback to old markup if function not loaded
            return `
          <div class="notes-section-widget p-2">
            <div class="widget-header mb-2 fw-bold">
              <i class="las la-sticky-note"></i> Notes Section
            </div>
            <div class="notes-section-container">
              <footer class="widget-footer">
                <div class="mb-0">
                  <span class="size10 notes-keyInsight" style="display: none">
                    <i class="las la-clipboard size14"></i> Notes :
                    <span class="notes-content">The Smart Cube Knowledge Repository</span>
                    <br>
                  </span>

                  <span class="size10 source-keyInsight" style="">
                    <i class="las la-database size14"></i> Source :
                    <span class="source-content">The Smart Cube Research and Analysis</span>
                    <br>
                  </span>
                  
                  <span class="size10 last-update-keyInsight" style="">
                    <i class="las size14 la-calendar-minus"></i> Last update :
                    <span class="last-update-content">Apr-2025</span>
                  </span>
                  
                  <span class="size10 next-update-keyInsight" style="">
                    <i class="las size14 la-calendar-plus"></i> Next update :
                    <span class="next-update-content">May-2025</span>
                  </span>
                </div>
              </footer>
            </div>
          </div>
          `;
          },
        },
      ];

      let sectionContainerSidebarContent = [
        {
          w: 6,
          h: 6,
          get content() {
            const sectionId =
              "section-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            // Always use the single source of truth for markup
            return typeof getSectionContainerWidgetMarkup === "function"
              ? getSectionContainerWidgetMarkup(sectionId)
              : "";
          },
        },
      ];

      // Setup drag-in for text widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="text"]',
        undefined,
        textSidebarContent
      );

      // Setup drag-in for table widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="table"]',
        undefined,
        tableSidebarContent
      );

      // Setup drag-in for line separator widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="line-separator"]',
        undefined,
        lineSeparatorSidebarContent
      );

      // Setup drag-in for image widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="image"]',
        undefined,
        imageSidebarContent
      );

      // Setup drag-in for notes section widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="notes-section"]',
        undefined,
        notesSectionSidebarContent
      );

      // Setup drag-in for section container widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="section-container"]',
        undefined,
        sectionContainerSidebarContent
      );

      // Patch: Enable autoscroll for sidebar drag-in (event delegation)
      setTimeout(function () {
        var sidebar = document.querySelector(".sidebar");
        if (sidebar) {
          sidebar.addEventListener("dragstart", function (e) {
            if (
              e.target.classList &&
              e.target.classList.contains("widget-item")
            ) {
              if (
                window.GridStackAutoScrollAuto &&
                typeof window.GridStackAutoScrollAuto.startExternalDrag ===
                  "function"
              ) {
                window.GridStackAutoScrollAuto.startExternalDrag();
              }
            }
          });
          sidebar.addEventListener("dragend", function (e) {
            if (
              e.target.classList &&
              e.target.classList.contains("widget-item")
            ) {
              if (
                window.GridStackAutoScrollAuto &&
                typeof window.GridStackAutoScrollAuto.stopExternalDrag ===
                  "function"
              ) {
                window.GridStackAutoScrollAuto.stopExternalDrag();
              }
            }
          });
        }
      }, 500);

      // Global dragstart debug
      document.addEventListener("dragstart", function (e) {
        console.log("Global dragstart:", e.target, e);
        alert("Global dragstart: " + e.target.className);
      });

      // Global mouse event debug
      document.addEventListener("mousedown", function (e) {
        console.log("Global mousedown:", e.target, e);
      });
      document.addEventListener("mousemove", function (e) {
        console.log("Global mousemove:", e.target, e);
      });
      document.addEventListener("mouseup", function (e) {
        console.log("Global mouseup:", e.target, e);
      });
    </script>

    <script>
      // --- Price Analysis 1 Section Builder ---
      async function createPriceAnalysis1Section() {
        // Helper to wait for a DOM element
        function waitForElement(selector, timeout = 5000) {
          return new Promise((resolve, reject) => {
            const start = Date.now();
            function check() {
              const el = document.querySelector(selector);
              if (el) return resolve(el);
              if (Date.now() - start > timeout)
                return reject("Timeout: " + selector);
              setTimeout(check, 50);
            }
            check();
          });
        }

        // 1. Create top-level section container
        const mainSectionWidget = window.addSectionContainerWidget();
        // Wait for the section-content div to be available
        const mainSection = mainSectionWidget.querySelector(".section-content");
        const mainSectionId = mainSection.id;
        // Set header title
        mainSectionWidget.querySelector(".widget-header > div").textContent =
          "Price Analysis 1";

        // 2. Initialize nested grid in main section
        await waitForElement(`#${mainSectionId} .grid-stack`);
        const mainNestedGrid =
          mainSection.querySelector(".grid-stack").gridstack;

        // --- Helper to create a section inside a gridstack ---
        async function createSectionInGrid(grid, title, w, h) {
          const sectionWidget = grid.addWidget({
            x: 0,
            y: 0,
            w,
            h,
            content: `<div class='section-container-widget p-2' style='height: 100%;'>
            <div class='widget-header mb-2 fw-bold d-flex justify-content-between align-items-center'>
              <div>${title}</div>
              <div><button class='btn btn-sm btn-link text-dark ms-1' onclick='removeSectionContainer(this)'><i class='las la-times'></i></button></div>
            </div>
            <div class='section-content' style='height: calc(100% - 40px);'></div>
          </div>`,
          });
          // Give the section-content a unique id
          const sectionContent =
            sectionWidget.querySelector(".section-content");
          sectionContent.id = `${title
            .toLowerCase()
            .replace(/\s+/g, "-")}-content-${Date.now()}`;
          // Initialize nested grid
          window.initSectionContainer(sectionContent.id);
          await waitForElement(`#${sectionContent.id} .grid-stack`);
          return sectionContent;
        }

        // --- 3. Price Forecast Section ---
        const priceForecastSection = await createSectionInGrid(
          mainNestedGrid,
          "Price Forecast",
          12,
          6
        );
        const priceForecastGrid =
          priceForecastSection.querySelector(".grid-stack").gridstack;
        // Add Price Chart Widget
        if (window.addPriceChartWidget) {
          const chartWidget = await window.addPriceChartWidget();
          priceForecastGrid.addWidget(chartWidget);
        }
        // Add two Text Widgets
        if (window.addTextWidget) {
          const textWidget1 = window.addTextWidget();
          priceForecastGrid.addWidget(textWidget1);
          const textWidget2 = window.addTextWidget();
          priceForecastGrid.addWidget(textWidget2);
        }

        // --- 4. Supply Demand Section ---
        const supplyDemandSection = await createSectionInGrid(
          mainNestedGrid,
          "Supply Demand",
          12,
          3
        );
        const supplyDemandGrid =
          supplyDemandSection.querySelector(".grid-stack").gridstack;
        if (window.addTextWidget) {
          const supplyTextWidget = window.addTextWidget();
          supplyDemandGrid.addWidget(supplyTextWidget);
        }

        // --- 5. Other Analysis Section ---
        const otherAnalysisSection = await createSectionInGrid(
          mainNestedGrid,
          "Other Analysis",
          12,
          6
        );
        const otherAnalysisGrid =
          otherAnalysisSection.querySelector(".grid-stack").gridstack;
        // Add Bar Chart Widget
        if (window.addBarChartWidget) {
          const barChartWidget = window.addBarChartWidget();
          otherAnalysisGrid.addWidget(barChartWidget);
        }
        // Add PDF Viewer Widget
        if (window.addPdfViewerWidget) {
          const pdfWidget = window.addPdfViewerWidget();
          otherAnalysisGrid.addWidget(pdfWidget);
        }
      }
      // Run the builder automatically on page load (or call manually)
      // createPriceAnalysis1Section();
      // To run manually, open console and call: createPriceAnalysis1Section();
    </script>

    <script>
      // Patch: Dynamically watch for GridStack drag helper elements and attach listeners
      (function () {
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
              if (
                node.nodeType === 1 &&
                node.classList.contains("ui-draggable-dragging")
              ) {
                // Attach listeners to the helper
                node.addEventListener("dragstart", function () {
                  if (
                    window.GridStackAutoScrollAuto &&
                    typeof window.GridStackAutoScrollAuto.startExternalDrag ===
                      "function"
                  ) {
                    window.GridStackAutoScrollAuto.startExternalDrag();
                  }
                });
                node.addEventListener("dragend", function () {
                  if (
                    window.GridStackAutoScrollAuto &&
                    typeof window.GridStackAutoScrollAuto.stopExternalDrag ===
                      "function"
                  ) {
                    window.GridStackAutoScrollAuto.stopExternalDrag();
                  }
                });
                // Also trigger startExternalDrag immediately (in case dragstart doesn't fire)
                if (
                  window.GridStackAutoScrollAuto &&
                  typeof window.GridStackAutoScrollAuto.startExternalDrag ===
                    "function"
                ) {
                  window.GridStackAutoScrollAuto.startExternalDrag();
                }
              }
            });
          });
        });
        observer.observe(document.body, { childList: true, subtree: true });
      })();
    </script>

    <script>
      // Add this after your GridStack instance is created
      grid.on("added", function (event, items) {
        // Pie chart initialization moved to main grid "added" event handler (lines 1163-1175)
      });
    </script>

    <!-- Pie chart drag-in now handled by js/pieChartWidget.js -->

    <script>
      // ... existing code ...
      let barChartSidebarContent = [
        {
          w: 6,
          h: 8,
          get content() {
            const chartId =
              "barchart-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            // Use the same markup function as click-to-add if available
            if (typeof getBarChartWidgetMarkup === "function") {
              return getBarChartWidgetMarkup({ chartId });
            }
            // fallback markup if function not loaded
            return `
              <div class="bar-chart-widget p-2">
                <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
                  <div>Bar Chart</div>
                  <div>
                    <button class="btn btn-sm btn-link text-dark"
                            data-bs-toggle="offcanvas"
                            data-bs-target="#barChartSettings"
                            aria-controls="barChartSettings"
                            onclick="initBarChartSettings('${chartId}')">
                      <i class="las la-cog"></i>
                    </button>
                    <button class="btn btn-sm btn-link text-dark ms-1"
                            onclick="removeWidget(this)">
                      <i class="las la-times"></i>
                    </button>
                  </div>
                </div>
                <div id="${chartId}" class="chart-container"></div>
              </div>
            `;
          },
        },
      ];

      // Setup drag-in for bar chart widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="bar-chart"]',
        undefined,
        barChartSidebarContent
      );
    </script>

    <script>
      // Add this after your GridStack instance is created
      grid.on("added", function (event, items) {
        items.forEach(function (item) {
          // Look for bar chart widgets in the added item
          const barCharts = item.el.querySelectorAll(".bar-chart-widget");
          barCharts.forEach(function (widget) {
            // Find the chart container inside the widget
            const chartContainer = widget.querySelector(
              '.chart-container[id^="barchart-"]'
            );
            if (chartContainer && chartContainer.id) {
              // Delay to ensure DOM is ready
              setTimeout(function () {
                if (window.initBarChart) {
                  window.initBarChart(chartContainer.id);
                }
              }, 0);
            }
          });
        });
      });
    </script>

    <script>
      // ... existing code ...
      let lineChartSidebarContent = [
        {
          w: 6,
          h: 8,
          get content() {
            const chartId =
              "linechart-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            // Use the same markup function as click-to-add if available
            if (typeof getLineChartWidgetMarkup === "function") {
              return getLineChartWidgetMarkup({ chartId });
            }
            // fallback markup if function not loaded
            return `
              <div class="line-chart-widget p-2">
                <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
                  <div>Line Chart</div>
                  <div>
                    <button class="btn btn-sm btn-link text-dark"
                            data-bs-toggle="offcanvas"
                            data-bs-target="#lineChartSettings"
                            aria-controls="lineChartSettings"
                            onclick="initLineChartSettings('${chartId}')">
                      <i class="las la-cog"></i>
                    </button>
                    <button class="btn btn-sm btn-link text-dark ms-1"
                            onclick="removeWidget(this)">
                      <i class="las la-times"></i>
                    </button>
                  </div>
                </div>
                <div id="${chartId}" class="chart-container"></div>
              </div>
            `;
          },
        },
      ];

      // Setup drag-in for line chart widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="line-chart"]',
        undefined,
        lineChartSidebarContent
      );
    </script>

    <script>
      grid.on("added", function (event, items) {
        items.forEach(function (item) {
          // Look for line chart widgets in the added item
          const lineCharts = item.el.querySelectorAll(".line-chart-widget");
          lineCharts.forEach(function (widget) {
            // Find the chart container inside the widget
            const chartContainer = widget.querySelector(
              '.chart-container[id^="linechart-"]'
            );
            if (chartContainer && chartContainer.id) {
              // Delay to ensure DOM is ready
              setTimeout(function () {
                if (window.initLineChart) {
                  window.initLineChart(chartContainer.id);
                }
              }, 0);
            }
          });
        });
      });
    </script>

    <script>
      // ... existing code ...
      let stackedColumnChartSidebarContent = [
        {
          w: 6,
          h: 8,
          get content() {
            const chartId =
              "stacked-column-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            // Use the same markup function as click-to-add if available
            if (typeof getStackedColumnChartWidgetMarkup === "function") {
              return getStackedColumnChartWidgetMarkup({ chartId });
            }
            // fallback markup if function not loaded
            return `
              <div class="stacked-column-chart-widget p-2" style="height: 100%; display: flex; flex-direction: column;">
                <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
                  <div>Stacked Column Chart</div>
                  <div>
                    <button class="btn btn-sm btn-link text-dark"
                            data-bs-toggle="offcanvas"
                            data-bs-target="#stackedColumnChartSettings"
                            aria-controls="stackedColumnChartSettings"
                            onclick="initStackedColumnChartSettings('${chartId}')">
                      <i class="las la-cog"></i>
                    </button>
                    <button class="btn btn-sm btn-link text-dark ms-1"
                            onclick="removeWidget(this)">
                      <i class="las la-times"></i>
                    </button>
                  </div>
                </div>
                <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
                  <div id="${chartId}" class="chart-container" style="flex: 1; width: 100%; height: 100%; min-height: 300px; position: relative;"></div>
                </div>
              </div>
            `;
          },
        },
      ];

      // Setup drag-in for stacked column chart widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="stacked-column-chart"]',
        undefined,
        stackedColumnChartSidebarContent
      );
    </script>

    <script>
      grid.on("added", function (event, items) {
        items.forEach(function (item) {
          // Look for stacked column chart widgets in the added item
          const stackedColumnCharts = item.el.querySelectorAll(
            ".stacked-column-chart-widget"
          );
          stackedColumnCharts.forEach(function (widget) {
            // Find the chart container inside the widget
            const chartContainer = widget.querySelector(
              '.chart-container[id^="stacked-column-"]'
            );
            if (chartContainer && chartContainer.id) {
              // Delay to ensure DOM is ready
              setTimeout(function () {
                if (window.initStackedColumnChart) {
                  window.initStackedColumnChart(chartContainer.id);
                }
              }, 0);
            }
          });
        });
      });

      // --- Add after stackedColumnChartSidebarContent and its setup ---

      function getPercentStackedColumnChartWidgetMarkup({ chartId }) {
        return `
        <div class="percent-stacked-column-chart p-2" style="height: 100%; display: flex; flex-direction: column">
          <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
            <div>100% Stacked Column Chart</div>
            <div>
              <button class="btn btn-sm btn-link text-dark"
                      data-bs-toggle="offcanvas"
                      data-bs-target="#percentStackedColumnChartSettings"
                      aria-controls="percentStackedColumnChartSettings"
                      onclick="initPercentStackedColumnChartSettings('${chartId}')">
                <i class="las la-cog"></i>
              </button>
              <button class="btn btn-sm btn-link text-dark ms-1"
                      onclick="removeWidget(this)">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
            <div id="${chartId}" class="chart-container" style="flex: 1; width: 100%; height: 100%; min-height: 300px; position: relative;"></div>
          </div>
        </div>
      `;
      }

      let percentStackedColumnChartSidebarContent = [
        {
          w: 6,
          h: 8,
          get content() {
            const chartId =
              "percent-stacked-column-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            return getPercentStackedColumnChartWidgetMarkup({ chartId });
          },
        },
      ];
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="percent-stacked-column-chart"]',
        undefined,
        percentStackedColumnChartSidebarContent
      );

      grid.on("added", function (event, items) {
        items.forEach(function (item) {
          const percentStackedCharts = item.el.querySelectorAll(
            ".percent-stacked-column-chart"
          );
          percentStackedCharts.forEach(function (widget) {
            const chartContainer = widget.querySelector(
              '.chart-container[id^="percent-stacked-column-"]'
            );
            if (chartContainer && chartContainer.id) {
              console.log(
                "[percent-stacked-column] Initializing chart for:",
                chartContainer.id,
                chartContainer
              );
              setTimeout(function () {
                if (window.initPercentStackedColumnChart) {
                  window.initPercentStackedColumnChart(chartContainer.id);
                } else {
                  console.warn("initPercentStackedColumnChart is not defined");
                }
              }, 0);
            } else {
              console.warn(
                "No chart container found in percent-stacked-column-chart widget",
                widget
              );
            }
          });
        });
      });

      // 1. Add the Area Chart markup function
      //
      function getAreaChartWidgetMarkup({ chartId }) {
        return `
        <div
          class="area-chart-widget p-2"
          style="height: 100%; display: flex; flex-direction: column"
        >
          <div
            class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center"
          >
            <div>Area Chart</div>
            <div>
              <button
                class="btn btn-sm btn-link text-dark"
                data-bs-toggle="offcanvas"
                data-bs-target="#areaChartSettings"
                aria-controls="areaChartSettings"
                onclick="initAreaChartSettings('${chartId}')"
              >
                <i class="las la-cog"></i>
              </button>
              <button
                class="btn btn-sm btn-link text-dark ms-1"
                onclick="removeWidget(this)"
              >
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div
            class="widget-body"
            style="
              flex: 1 1 auto;
              min-height: 0;
              position: relative;
              display: flex;
              flex-direction: column;
              height: 100%;
            "
          >
            <div
              id="${chartId}"
              class="chart-container"
              style="
                flex: 1;
                width: 100%;
                height: 100%;
                min-height: 300px;
                position: relative;
              "
            ></div>
          </div>
        </div>
        `;
      } // ... existing code ... //
      //
      // 2. Update areaChartSidebarContent to always
      // use the markup

      let areaChartSidebarContent = [
        {
          w: 6,
          h: 8,
          get content() {
            const chartId =
              "areachart-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            return getAreaChartWidgetMarkup({
              chartId,
            });
          },
        },
      ]; // ... existing code ... //
      //
      //
      //3. Setup drag-in for area
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="area-chart"]',
        undefined,
        areaChartSidebarContent
      ); // ... existing code ...
      //
      // // 4. Add
      grid.on("added", function (event, items) {
        items.forEach(function (item) {
          const areaCharts = item.el.querySelectorAll(".area-chart-widget");
          areaCharts.forEach(function (widget) {
            const chartContainer = widget.querySelector(
              '.chart-container[id^="areachart-"]'
            );
            if (chartContainer && chartContainer.id) {
              console.log(
                "[area-chart] Initializing chart for:",
                chartContainer.id,
                chartContainer
              );
              setTimeout(function () {
                if (window.initAreaChart) {
                  window.initAreaChart(chartContainer.id);
                } else {
                  console.warn("initAreaChart is not defined");
                }
              }, 0);
            } else {
              console.warn(
                "No chart container found in area-chart-widget",
                widget
              );
            }
          });
        });
      }); // ... existing code ...
    </script>

    <script>
      // ... existing code ...
      // 1. Add the PDF Viewer markup function
      function getPdfViewerWidgetMarkup({ pdfId }) {
        return `
          <div class="pdf-viewer-widget p-2" style="height: 100%; display: flex; flex-direction: column">
            <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
              <div>PDF Viewer</div>
              <div>
                <button class="btn btn-sm btn-link text-dark"
                        data-bs-toggle="offcanvas"
                        data-bs-target="#pdfViewerSettings"
                        aria-controls="pdfViewerSettings"
                        onclick="initPdfViewerSettings('${pdfId}')">
                  <i class="las la-cog"></i>
                </button>
                <button class="btn btn-sm btn-link text-dark ms-1"
                        onclick="removeWidget(this)">
                  <i class="las la-times"></i>
                </button>
              </div>
            </div>
            <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
              <div id="${pdfId}" class="pdf-container" style="flex: 1; width: 100%; height: 100%; min-height: 300px; position: relative; background: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                <span class="text-muted">No PDF loaded</span>
              </div>
            </div>
          </div>
        `;
      }
      // ... existing code ...
      // 2. Update pdfViewerSidebarContent to always use the markup function
      let pdfViewerSidebarContent = [
        {
          w: 6,
          h: 8,
          get content() {
            const pdfId =
              "pdfviewer-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            return getPdfViewerWidgetMarkup({ pdfId });
          },
        },
      ];
      // ... existing code ...
      // 3. Setup drag-in for PDF Viewer widgets
      GridStack.setupDragIn(
        '.widget-item[data-widget-type="pdf-viewer"]',
        undefined,
        pdfViewerSidebarContent
      );
      // ... existing code ...
      // 4. Add grid.on('added') handler for PDF Viewer widgets
      // (Place this after other grid.on('added') handlers)
      grid.on("added", function (event, items) {
        items.forEach(function (item) {
          const pdfViewers = item.el.querySelectorAll(".pdf-viewer-widget");
          pdfViewers.forEach(function (widget) {
            const pdfContainer = widget.querySelector(
              '.pdf-container[id^="pdfviewer-"]'
            );
            if (pdfContainer && pdfContainer.id) {
              console.log(
                "[pdf-viewer] Initializing PDF viewer for:",
                pdfContainer.id,
                pdfContainer
              );
              setTimeout(function () {
                if (window.initPdfViewer) {
                  window.initPdfViewer(pdfContainer.id);
                } else {
                  console.warn("initPdfViewer is not defined");
                }
              }, 0);
            } else {
              console.warn(
                "No pdf-container found in pdf-viewer-widget",
                widget
              );
            }
          });
        });
      });
      // ... existing code ...
    </script>

    <script>
      // Function to create a predefined test section with bar chart and line chart
      // This functionality is now handled by onload-section-container.js
      // The function is available globally as window.createTestSectionWithCharts
    </script>
  </body>
</html>
