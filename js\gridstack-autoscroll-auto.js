/**
 * GridStack Auto-Scroll - Auto-Initialize Version (Fixed for Section Containers)
 *
 * Simply include this script after GridStack library and it will automatically
 * initialize with optimal viewport-based settings.
 *
 * Author: Vimal Thapliyal
 * Fixed by: AI Assistant to prevent unwanted scrolling in section containers
 *
 * Features:
 * - Automatic initialization when DOM is ready
 * - Viewport-based optimization (mobile to 4K)
 * - Dynamic adjustment on window resize
 * - Zero configuration required
 * - FIXED: Prevents unwanted scrolling when dragging within section containers
 *
 * Usage:
 * <script src="path/to/gridstack-autoscroll-auto.js"></script>
 */

class GridStackAutoScrollAuto {
  constructor() {
    this.config = this.getOptimalConfig();
    this.isDragging = false;
    this.currentDragElement = null;
    this.dragStartedInSection = false;
    this.scrollDirection = null;
    this.scrollInterval = null;
    this.gridInstances = new Set();
    this.initialized = false;
    this.lastDragPosition = { x: 0, y: 0 };
    this.lastMousePosition = { x: 0, y: 0 };
    this.isInSectionContainer = false;
    this.sectionContainerCheckInterval = null;
    this.scrollPreventionActive = false;
    this.originalScrollBy = null;
    this.originalScrollTo = null;

    // Bind methods to preserve context
    this.handleDragStart = this.handleDragStart.bind(this);
    this.handleDragEnd = this.handleDragEnd.bind(this);
    this.handleMouseMove = this.handleMouseMove.bind(this);
    this.performScroll = this.performScroll.bind(this);
    this.handleResize = this.handleResize.bind(this);
    this.continuousSectionCheck = this.continuousSectionCheck.bind(this);
    this.preventScroll = this.preventScroll.bind(this);

    // Apply configuration and initialize
    this.applyOptimalConfig();
  }

  /**
   * Get optimal configuration based on current viewport size
   */
  getOptimalConfig() {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Calculate optimal scroll zone (6-7% of viewport width, clamped between 60-120px)
    const scrollZone = Math.max(60, Math.min(viewportWidth * 0.07, 120));

    // Adjust scroll speed based on viewport size
    let scrollSpeed;
    if (viewportWidth > 2560) {
      scrollSpeed = 25; // 4K and larger
    } else if (viewportWidth > 1920) {
      scrollSpeed = 20; // QHD
    } else if (viewportWidth > 1200) {
      scrollSpeed = 15; // Standard desktop
    } else {
      scrollSpeed = 12; // Tablets and smaller
    }

    return {
      scrollSpeed: scrollSpeed,
      scrollZone: scrollZone,
      scrollInterval: 16, // 60fps for smooth performance
      enableHorizontalScroll: true,
      enableVerticalScroll: true,
      smoothScrolling: true,
      debug: false,
    };
  }

  /**
   * Apply optimal configuration
   */
  applyOptimalConfig() {
    const optimalConfig = this.getOptimalConfig();
    this.config = { ...this.config, ...optimalConfig };
    this.log(
      "Applied optimal config for viewport:",
      window.innerWidth + "x" + window.innerHeight,
      this.config
    );
  }

  /**
   * Auto-initialize with optimal settings
   */
  autoInit() {
    if (this.isActive) {
      this.log("Auto-scroll already initialized");
      return;
    }

    // Apply optimal configuration for current viewport
    this.applyOptimalConfig();

    // Discover and register GridStack instances
    this.discoverGridInstances();

    // Set up event listeners
    this.setupEventListeners();

    // Set up resize handler for dynamic adjustment
    this.setupResizeHandler();

    // Start continuous section container checking
    this.startContinuousSectionCheck();

    // Set up global scroll prevention
    this.setupGlobalScrollPrevention();

    this.isActive = true;
    this.log("GridStack Auto-Scroll auto-initialized (Fixed for Section Containers)");
  }

  /**
   * Set up global scroll prevention
   */
  setupGlobalScrollPrevention() {
    // Store original scroll methods
    this.originalScrollBy = window.scrollBy;
    this.originalScrollTo = window.scrollTo;
    
    // Override scrollBy to prevent auto-scrolling over section containers
    window.scrollBy = (...args) => {
      if (this.shouldPreventScroll()) {
        this.log("Scroll prevented - over section container");
        return;
      }
      return this.originalScrollBy.apply(window, args);
    };
    
    // Override scrollTo to prevent auto-scrolling over section containers
    window.scrollTo = (...args) => {
      if (this.shouldPreventScroll()) {
        this.log("Scroll prevented - over section container");
        return;
      }
      return this.originalScrollTo.apply(window, args);
    };
    
    this.log("Global scroll prevention enabled");
  }

  /**
   * Check if scroll should be prevented
   */
  shouldPreventScroll() {
    // If we're in a section container, prevent all auto-scrolling
    if (this.isInSectionContainer) {
      return true;
    }
    
    // If we have a last mouse position, check if it's over a section container
    if (this.lastMousePosition.x && this.lastMousePosition.y) {
      return this.isOverSectionContainer(this.lastMousePosition.x, this.lastMousePosition.y);
    }
    
    return false;
  }

  /**
   * Start continuous checking for section containers
   */
  startContinuousSectionCheck() {
    if (this.sectionContainerCheckInterval) {
      clearInterval(this.sectionContainerCheckInterval);
    }
    
    this.sectionContainerCheckInterval = setInterval(this.continuousSectionCheck, 50); // Increased frequency
  }

  /**
   * Stop continuous section container checking
   */
  stopContinuousSectionCheck() {
    if (this.sectionContainerCheckInterval) {
      clearInterval(this.sectionContainerCheckInterval);
      this.sectionContainerCheckInterval = null;
    }
  }

  /**
   * Continuously check if we're in a section container
   */
  continuousSectionCheck() {
    if (!this.lastMousePosition.x || !this.lastMousePosition.y) {
      return;
    }

    const elementUnderMouse = document.elementFromPoint(
      this.lastMousePosition.x, 
      this.lastMousePosition.y
    );
    
    if (elementUnderMouse) {
      const sectionContainer = elementUnderMouse.closest(
        ".section-container-widget, .nested-grid-container, .grid-stack-nested, .section-content"
      );
      
      const wasInSection = this.isInSectionContainer;
      this.isInSectionContainer = !!sectionContainer;
      
      // If we just entered a section container, stop auto-scroll immediately
      if (!wasInSection && this.isInSectionContainer) {
        this.log("Continuous check: Entered section container, stopping auto-scroll");
        this.stopAutoScroll();
        this.preventScroll();
      }
      
      // If we're in a section container, stop auto-scroll immediately
      if (this.isInSectionContainer && this.scrollInterval) {
        this.log("Continuous check: In section container, stopping auto-scroll");
        this.stopAutoScroll();
        this.preventScroll();
      }
    }
  }

  /**
   * Prevent scrolling by any means
   */
  preventScroll() {
    if (this.scrollPreventionActive) {
      return;
    }
    
    this.scrollPreventionActive = true;
    
    // Add CSS to prevent scrolling
    const style = document.createElement('style');
    style.id = 'scroll-prevention-style';
    style.textContent = `
      body.scroll-prevented {
        overflow: hidden !important;
        position: fixed !important;
        width: 100% !important;
        height: 100% !important;
      }
      
      .section-container-widget,
      .nested-grid-container,
      .grid-stack-nested,
      .section-content {
        overflow: hidden !important;
        scroll-behavior: auto !important;
      }
    `;
    document.head.appendChild(style);
    
    // Add class to body
    document.body.classList.add('scroll-prevented');
    
    this.log("Scroll prevention activated");
  }

  /**
   * Remove scroll prevention
   */
  removeScrollPrevention() {
    if (!this.scrollPreventionActive) {
      return;
    }
    
    this.scrollPreventionActive = false;
    
    // Remove CSS
    const style = document.getElementById('scroll-prevention-style');
    if (style) {
      style.remove();
    }
    
    // Remove class from body
    document.body.classList.remove('scroll-prevented');
    
    this.log("Scroll prevention removed");
  }

  /**
   * Discover all GridStack instances on the page
   */
  discoverGridInstances() {
    // Find all grid elements
    const gridElements = document.querySelectorAll(".grid-stack");

    gridElements.forEach((element) => {
      if (element.gridstack) {
        this.registerGridInstance(element.gridstack);
      }
    });

    // Also check window.grid (common pattern)
    if (window.grid) {
      this.registerGridInstance(window.grid);
    }

    this.log(`Discovered ${this.gridInstances.size} GridStack instances`);
  }

  /**
   * Register a GridStack instance for auto-scroll
   */
  registerGridInstance(gridInstance) {
    if (!gridInstance || this.gridInstances.has(gridInstance)) {
      return;
    }

    this.gridInstances.add(gridInstance);

    // Hook into GridStack events
    gridInstance.on("dragstart", this.handleDragStart);
    gridInstance.on("dragstop", this.handleDragEnd);

    this.log("Registered GridStack instance");
  }

  /**
   * Set up global event listeners
   */
  setupEventListeners() {
    // Mouse move for tracking position during drag
    document.addEventListener("mousemove", this.handleMouseMove, {
      passive: false,
    });

    // Touch events for mobile support
    document.addEventListener("touchmove", this.handleMouseMove, {
      passive: false,
    });

    // Patch: Listen for dragover events for external drag-in autoscroll on document
    document.addEventListener(
      "dragover",
      (event) => {
        if (this.isDragging && !this.currentDragElement) {
          const clientX = event.clientX;
          const clientY = event.clientY;
          if (typeof clientX === "number" && typeof clientY === "number") {
            // Check if we're over a section container
            if (this.isOverSectionContainer(clientX, clientY)) {
              this.stopAutoScroll();
              this.preventScroll();
              return;
            }
            
            const scrollDirection = this.calculateScrollDirection(
              clientX,
              clientY
            );
            if (scrollDirection.x !== 0 || scrollDirection.y !== 0) {
              this.startAutoScroll(scrollDirection);
            } else {
              this.stopAutoScroll();
            }
          }
        }
      },
      { passive: false }
    );

    // Patch: Listen for dragover events on all .grid-stack containers
    const addGridStackDragOver = () => {
      document.querySelectorAll(".grid-stack").forEach((gridEl) => {
        gridEl.addEventListener(
          "dragover",
          (event) => {
            event.preventDefault(); // Make it a valid drop target
            if (this.isDragging && !this.currentDragElement) {
              const clientX = event.clientX;
              const clientY = event.clientY;
              if (typeof clientX === "number" && typeof clientY === "number") {
                // Check if we're over a section container
                if (this.isOverSectionContainer(clientX, clientY)) {
                  this.stopAutoScroll();
                  this.preventScroll();
                  return;
                }
                
                const scrollDirection = this.calculateScrollDirection(
                  clientX,
                  clientY
                );
                if (scrollDirection.x !== 0 || scrollDirection.y !== 0) {
                  this.startAutoScroll(scrollDirection);
                } else {
                  this.stopAutoScroll();
                }
              }
            }
          },
          { passive: false }
        );
      });
    };
    // Run now and after a short delay (in case grids are created late)
    addGridStackDragOver();
    setTimeout(addGridStackDragOver, 1000);
  }

  /**
   * Set up resize handler for dynamic adjustment
   */
  setupResizeHandler() {
    window.addEventListener("resize", this.handleResize);
  }

  /**
   * Handle window resize with debouncing
   */
  handleResize() {
    // Stop current scrolling
    if (this.isDragging) {
      this.stopAutoScroll();
    }

    // Debounce resize handling
    clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(() => {
      this.applyOptimalConfig();
      this.log("Configuration updated for new viewport size");
    }, 250);
  }

  /**
   * Handle drag start event
   */
  handleDragStart(event, ui) {
    this.isDragging = true;
    this.currentDragElement = ui.helper || event.target;
    this.log("Drag started:", this.currentDragElement);

    // Check if drag started within a section container
    if (this.currentDragElement) {
      const sectionContainer = this.currentDragElement.closest(
        ".section-container-widget, .nested-grid-container, .grid-stack-nested, .section-content"
      );
      if (sectionContainer) {
        this.log(
          "Drag started within section container - will disable auto-scroll"
        );
        this.dragStartedInSection = true;
        // Immediately stop any auto-scroll and prevent scrolling
        this.stopAutoScroll();
        this.preventScroll();
      } else {
        this.dragStartedInSection = false;
      }
    }
  }

  /**
   * Handle drag end event
   */
  handleDragEnd(event, ui) {
    this.isDragging = false;
    this.currentDragElement = null;
    this.dragStartedInSection = false;
    this.stopAutoScroll();
    
    // Check if we're still in a section container after drag ends
    if (this.lastMousePosition.x && this.lastMousePosition.y) {
      this.isInSectionContainer = this.isOverSectionContainer(
        this.lastMousePosition.x, 
        this.lastMousePosition.y
      );
      
      // If we're still in a section container, keep scroll prevention active
      if (this.isInSectionContainer) {
        this.preventScroll();
      } else {
        this.removeScrollPrevention();
      }
    }
    
    this.log("Drag ended");
  }

  /**
   * Handle mouse move during drag
   */
  handleMouseMove(event) {
    const clientX =
      event.clientX || (event.touches && event.touches[0].clientX);
    const clientY =
      event.clientY || (event.touches && event.touches[0].clientY);

    if (!clientX || !clientY) {
      return;
    }

    // Always update last mouse position for continuous checking
    this.lastMousePosition = { x: clientX, y: clientY };

    if (!this.isDragging || !this.currentDragElement) {
      return;
    }

    // If drag started in section container, always disable auto-scroll
    if (this.dragStartedInSection) {
      this.stopAutoScroll();
      this.preventScroll();
      return;
    }

    // Check if we're dragging within a section container or nested grid
    if (this.isDraggingInSectionContainer(event)) {
      // Stop auto-scroll when dragging within section containers
      this.stopAutoScroll();
      this.preventScroll();
      return;
    }

    // Store last drag position for better detection
    this.lastDragPosition = { x: clientX, y: clientY };

    // Check if we're over a section container
    if (this.isOverSectionContainer(clientX, clientY)) {
      this.stopAutoScroll();
      this.preventScroll();
      return;
    }

    // Remove scroll prevention if we're not over a section container
    this.removeScrollPrevention();

    const scrollDirection = this.calculateScrollDirection(clientX, clientY);

    if (scrollDirection.x !== 0 || scrollDirection.y !== 0) {
      this.startAutoScroll(scrollDirection);
    } else {
      this.stopAutoScroll();
    }
  }

  /**
   * Check if a position is over a section container
   */
  isOverSectionContainer(clientX, clientY) {
    const elementUnderMouse = document.elementFromPoint(clientX, clientY);
    if (!elementUnderMouse) {
      return false;
    }

    // Check if the element is within a section container
    const sectionContainer = elementUnderMouse.closest(
      ".section-container-widget, .nested-grid-container, .grid-stack-nested, .section-content"
    );
    
    if (sectionContainer) {
      this.log("Mouse over section container, disabling auto-scroll");
      return true;
    }

    return false;
  }

  /**
   * Check if dragging is happening within a section container
   */
  isDraggingInSectionContainer(event) {
    const clientX =
      event.clientX || (event.touches && event.touches[0].clientX);
    const clientY =
      event.clientY || (event.touches && event.touches[0].clientY);

    if (!clientX || !clientY) {
      return false;
    }

    // Get the element under the mouse cursor
    const elementUnderMouse = document.elementFromPoint(clientX, clientY);
    if (!elementUnderMouse) {
      return false;
    }

    // Check if the element is within a section container
    const sectionContainer = elementUnderMouse.closest(
      ".section-container-widget, .nested-grid-container, .grid-stack-nested, .section-content"
    );
    if (sectionContainer) {
      this.log("Dragging within section container, disabling auto-scroll");
      return true;
    }

    // Check if the element is within a tab container
    const tabContainer = elementUnderMouse.closest(".tab-container-widget");
    if (tabContainer) {
      this.log("Dragging within tab container, disabling auto-scroll");
      return true;
    }

    // Check for any other nested grid patterns
    const anyNestedGrid = elementUnderMouse.closest(
      '[class*="nested"], [class*="tab-"], .grid-stack-item-content'
    );
    if (anyNestedGrid) {
      this.log(
        "Dragging within nested container pattern, disabling auto-scroll"
      );
      return true;
    }

    // Check if we're dragging a grid-stack-item that's inside a nested container
    const gridStackItem = elementUnderMouse.closest(".grid-stack-item");
    if (gridStackItem) {
      const parentGrid = gridStackItem.closest(".grid-stack");
      if (
        parentGrid &&
        parentGrid.closest(".section-container-widget, .nested-grid-container, .grid-stack-nested")
      ) {
        this.log(
          "Dragging grid item within nested container, disabling auto-scroll"
        );
        return true;
      }
    }

    // Check if we're dragging over a grid-stack-item that's inside a section
    const draggedOverGridItem = elementUnderMouse.closest(".grid-stack-item");
    if (draggedOverGridItem) {
      const sectionContainer = draggedOverGridItem.closest(
        ".section-container-widget, .nested-grid-container, .grid-stack-nested"
      );
      if (sectionContainer) {
        this.log("Dragging over grid item in section container, disabling auto-scroll");
        return true;
      }
    }

    this.log("Not dragging within section container, auto-scroll allowed");
    return false;
  }

  /**
   * Calculate scroll direction based on mouse position
   */
  calculateScrollDirection(clientX, clientY) {
    // If we're in a section container, don't scroll at all
    if (this.isInSectionContainer) {
      this.log("In section container, preventing scroll calculation");
      return { x: 0, y: 0 };
    }

    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    };

    const scrollDirection = { x: 0, y: 0 };

    // Ensure minimum scroll zone for large viewports
    const effectiveScrollZone = Math.max(this.config.scrollZone, 50);

    // Check horizontal scrolling with dynamic speed
    if (this.config.enableHorizontalScroll) {
      if (clientX < effectiveScrollZone) {
        scrollDirection.x = -1; // Scroll left
        const proximity = Math.max(
          0,
          Math.min(1, (effectiveScrollZone - clientX) / effectiveScrollZone)
        );
        scrollDirection.speedMultiplierX = 1 + proximity * 2;
      } else if (clientX > viewport.width - effectiveScrollZone) {
        scrollDirection.x = 1; // Scroll right
        const proximity = Math.max(
          0,
          Math.min(
            1,
            (clientX - (viewport.width - effectiveScrollZone)) /
              effectiveScrollZone
          )
        );
        scrollDirection.speedMultiplierX = 1 + proximity * 2;
      }
    }

    // Check vertical scrolling with dynamic speed
    if (this.config.enableVerticalScroll) {
      if (clientY < effectiveScrollZone) {
        scrollDirection.y = -1; // Scroll up
        const proximity = Math.max(
          0,
          Math.min(1, (effectiveScrollZone - clientY) / effectiveScrollZone)
        );
        scrollDirection.speedMultiplierY = 1 + proximity * 2;
      } else if (clientY > viewport.height - effectiveScrollZone) {
        scrollDirection.y = 1; // Scroll down
        const proximity = Math.max(
          0,
          Math.min(
            1,
            (clientY - (viewport.height - effectiveScrollZone)) /
              effectiveScrollZone
          )
        );
        scrollDirection.speedMultiplierY = 1 + proximity * 2;
      }
    }

    return scrollDirection;
  }

  /**
   * Start auto-scroll in the specified direction
   */
  startAutoScroll(direction) {
    // Don't start auto-scroll if we're in a section container
    if (this.isInSectionContainer) {
      this.log("In section container, preventing auto-scroll start");
      return;
    }

    if (this.scrollInterval) {
      return; // Already scrolling
    }

    this.scrollDirection = direction;
    this.scrollInterval = setInterval(
      this.performScroll,
      this.config.scrollInterval
    );
    this.log("Auto-scroll started:", direction);
  }

  /**
   * Stop auto-scroll
   */
  stopAutoScroll() {
    if (this.scrollInterval) {
      clearInterval(this.scrollInterval);
      this.scrollInterval = null;
      this.scrollDirection = null;
      this.log("Auto-scroll stopped");
    }
  }

  /**
   * Perform the actual scrolling
   */
  performScroll() {
    if (!this.scrollDirection || !this.isDragging) {
      this.stopAutoScroll();
      return;
    }

    // Double-check if we're still over a section container
    if (this.isOverSectionContainer(this.lastDragPosition.x, this.lastDragPosition.y)) {
      this.stopAutoScroll();
      return;
    }

    // Additional check for section container state
    if (this.isInSectionContainer) {
      this.log("Section container detected during scroll, stopping");
      this.stopAutoScroll();
      return;
    }

    // Apply dynamic speed multipliers for more responsive scrolling
    const baseScrollX = this.scrollDirection.x * this.config.scrollSpeed;
    const baseScrollY = this.scrollDirection.y * this.config.scrollSpeed;

    // Add viewport scaling for large screens
    const viewportWidth = window.innerWidth;
    const viewportScale = Math.min(2, Math.max(1, viewportWidth / 1920));

    const scrollX =
      baseScrollX *
      (this.scrollDirection.speedMultiplierX || 1) *
      viewportScale;
    const scrollY =
      baseScrollY *
      (this.scrollDirection.speedMultiplierY || 1) *
      viewportScale;

    // Ensure minimum scroll amount for large viewports
    const minScrollX =
      this.scrollDirection.x !== 0
        ? Math.max(Math.abs(scrollX), 5) * Math.sign(scrollX)
        : 0;
    const minScrollY =
      this.scrollDirection.y !== 0
        ? Math.max(Math.abs(scrollY), 5) * Math.sign(scrollY)
        : 0;

    if (this.config.smoothScrolling) {
      window.scrollBy({
        left: minScrollX,
        top: minScrollY,
        behavior: "instant",
      });
    } else {
      window.scrollBy(minScrollX, minScrollY);
    }

    // Improved scroll bounds checking
    const scrollableWidth = Math.max(
      0,
      document.documentElement.scrollWidth - window.innerWidth
    );
    const scrollableHeight = Math.max(
      0,
      document.documentElement.scrollHeight - window.innerHeight
    );

    const canScrollLeft = window.scrollX > 0;
    const canScrollRight = window.scrollX < scrollableWidth;
    const canScrollUp = window.scrollY > 0;
    const canScrollDown = window.scrollY < scrollableHeight;

    // Stop scrolling if we can't scroll further in the current direction
    if (
      (this.scrollDirection.x < 0 && !canScrollLeft) ||
      (this.scrollDirection.x > 0 && !canScrollRight) ||
      (this.scrollDirection.y < 0 && !canScrollUp) ||
      (this.scrollDirection.y > 0 && !canScrollDown)
    ) {
      this.stopAutoScroll();
    }
  }

  /**
   * Debug logging
   */
  log(...args) {
    if (this.config.debug) {
      console.log("[GridStack Auto-Scroll Auto]", ...args);
    }
  }

  /**
   * Public method to enable debug mode
   */
  enableDebug() {
    this.config.debug = true;
    this.log("Debug mode enabled");
  }

  /**
   * Public method to disable debug mode
   */
  disableDebug() {
    this.config.debug = false;
  }

  /**
   * Public method to enable debug mode temporarily
   */
  enableDebugTemporarily() {
    this.config.debug = true;
    this.log("Debug mode enabled temporarily");
    // Auto-disable after 30 seconds
    setTimeout(() => {
      this.config.debug = false;
      console.log("[GridStack Auto-Scroll Auto] Debug mode auto-disabled");
    }, 30000);
  }

  /**
   * Public method to start autoscroll for external drags (e.g., sidebar drag-in)
   */
  startExternalDrag() {
    this.isDragging = true;
    this.currentDragElement = null;
    this.setupGridMutationObserver();
    this.log("External drag started (sidebar drag-in)");
  }

  /**
   * Public method to stop autoscroll for external drags
   */
  stopExternalDrag() {
    this.isDragging = false;
    this.currentDragElement = null;
    this.teardownGridMutationObserver();
    this.stopAutoScroll();
    this.log("External drag stopped (sidebar drag-in)");
  }

  /**
   * Set up a MutationObserver to watch the grid's size and grow it if needed during drag-in
   */
  setupGridMutationObserver() {
    if (this._gridMutationObserver) return;
    this._gridMutationObserver = [];
    document.querySelectorAll(".grid-stack").forEach((gridEl) => {
      // Store original minHeight
      if (!gridEl._originalMinHeight)
        gridEl._originalMinHeight = gridEl.style.minHeight;
      // Observer callback
      const observer = new MutationObserver(() => {
        // If the mouse is near the bottom of the grid, grow it
        if (
          this.isDragging &&
          !this.currentDragElement &&
          this._lastDragOverY
        ) {
          const rect = gridEl.getBoundingClientRect();
          // If mouse is within 100px of bottom, grow grid
          if (this._lastDragOverY > rect.bottom - 100) {
            const growBy = 200;
            gridEl.style.minHeight = rect.height + growBy + "px";
          }
        }
      });
      observer.observe(gridEl, {
        attributes: true,
        childList: true,
        subtree: true,
      });
      this._gridMutationObserver.push({ gridEl, observer });
      // Listen for dragover to track mouse position
      gridEl.addEventListener(
        "dragover",
        (this._trackDragOverY = (e) => {
          this._lastDragOverY = e.clientY;
        })
      );
    });
  }

  /**
   * Remove MutationObserver and restore grid minHeight
   */
  teardownGridMutationObserver() {
    if (this._gridMutationObserver) {
      this._gridMutationObserver.forEach(({ gridEl, observer }) => {
        observer.disconnect();
        if (gridEl._originalMinHeight !== undefined) {
          gridEl.style.minHeight = gridEl._originalMinHeight;
          delete gridEl._originalMinHeight;
        }
        if (this._trackDragOverY) {
          gridEl.removeEventListener("dragover", this._trackDragOverY);
        }
      });
      this._gridMutationObserver = null;
      this._lastDragOverY = null;
      this._trackDragOverY = null;
    }
  }

  /**
   * Show a full-page invisible overlay as a drop target for dragover events
   */
  showOverlay() {
    if (this.overlay) return;
    const overlay = document.createElement("div");
    overlay.style.position = "fixed";
    overlay.style.top = "0";
    overlay.style.left = "0";
    overlay.style.width = "100vw";
    overlay.style.height = "100vh";
    overlay.style.zIndex = "999999";
    overlay.style.pointerEvents = "auto";
    overlay.style.background = "rgba(0,0,0,0)"; // fully transparent
    overlay.style.userSelect = "none";
    overlay.setAttribute("data-autoscroll-overlay", "true");
    overlay.addEventListener(
      "dragover",
      (event) => {
        event.preventDefault();
        if (this.isDragging && !this.currentDragElement) {
          const clientX = event.clientX;
          const clientY = event.clientY;
          if (typeof clientX === "number" && typeof clientY === "number") {
            // Check if we're over a section container
            if (this.isOverSectionContainer(clientX, clientY)) {
              this.stopAutoScroll();
              this.preventScroll();
              return;
            }
            
            const scrollDirection = this.calculateScrollDirection(
              clientX,
              clientY
            );
            if (scrollDirection.x !== 0 || scrollDirection.y !== 0) {
              this.startAutoScroll(scrollDirection);
            } else {
              this.stopAutoScroll();
            }
          }
        }
      },
      { passive: false }
    );
    document.body.appendChild(overlay);
    this.overlay = overlay;
  }

  /**
   * Hide and remove the overlay
   */
  hideOverlay() {
    if (this.overlay) {
      this.overlay.remove();
      this.overlay = null;
    }
  }

  /**
   * Cleanup method
   */
  destroy() {
    this.stopAutoScroll();
    this.stopContinuousSectionCheck();
    this.teardownGridMutationObserver();
    this.hideOverlay();
    this.removeScrollPrevention();
    
    // Restore original scroll methods
    if (this.originalScrollBy) {
      window.scrollBy = this.originalScrollBy;
    }
    if (this.originalScrollTo) {
      window.scrollTo = this.originalScrollTo;
    }
    
    this.log("GridStack Auto-Scroll destroyed");
  }
}

// Create singleton instance
const gridStackAutoScrollAuto = new GridStackAutoScrollAuto();

// Auto-initialize when DOM is ready and GridStack is available
function initializeWhenReady() {
  // Check if GridStack is available
  if (typeof window !== "undefined" && window.GridStack) {
    // Wait a bit for GridStack instances to be created
    setTimeout(() => {
      gridStackAutoScrollAuto.autoInit();
    }, 100);
  } else {
    // GridStack not ready yet, try again
    setTimeout(initializeWhenReady, 100);
  }
}

// Start initialization process
if (typeof window !== "undefined") {
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeWhenReady);
  } else {
    // DOM is already ready
    initializeWhenReady();
  }
}

// Export for global access
if (typeof window !== "undefined") {
  window.GridStackAutoScrollAuto = gridStackAutoScrollAuto;
}

// Export for different module systems
if (typeof module !== "undefined" && module.exports) {
  module.exports = gridStackAutoScrollAuto;
} else if (typeof define === "function" && define.amd) {
  define([], function () {
    return gridStackAutoScrollAuto;
  });
}
