/* Scss Document */
/* Scss Document */
/* CSS Document */
/* line 3, ../scss/_font-size.scss */
.font-size11 {
  font-size: 11px;
}

/* line 3, ../scss/_font-size.scss */
.font-size12 {
  font-size: 12px;
}

/* line 3, ../scss/_font-size.scss */
.font-size14 {
  font-size: 14px;
}

/* line 3, ../scss/_font-size.scss */
.font-size16 {
  font-size: 16px;
}

/* line 3, ../scss/_font-size.scss */
.font-size18 {
  font-size: 18px;
}

/* line 3, ../scss/_font-size.scss */
.font-size24 {
  font-size: 24px;
}

/* CSS Document */
/* line 3, ../scss/_anchors.scss */
a {
  color: #00b19c;
  text-decoration: none;
}
/* line 7, ../scss/_anchors.scss */
a:hover {
  color: #231f20 ;
  text-decoration: none;
}

/* Scss Document */
/* line 5, ../scss/_buttons.scss */
.btn-primary {
  background: #00b19c;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-primary:hover {
  background: rgba(0, 174, 155, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-primary:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-primary:not(:disabled):not(.disabled).active:focus,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active,
.show > .btn-primary.dropdown-toggle {
  background: #00b19c;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-primary {
  background: transparent;
  border-radius: 0px;
  border-color: #00b19c;
  color: #00b19c;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-primary:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-primary:hover {
  background: #00b19c;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-primary:not(:disabled):not(.disabled).active,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.show > .btn-outline-primary.dropdown-toggle {
  background: #00b19c;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-primary2 {
  background: #33c1b0;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-primary2:hover {
  background: rgba(51, 190, 175, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-primary2:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-primary2:not(:disabled):not(.disabled).active:focus,
.btn-primary2:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary2.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-primary2:not(:disabled):not(.disabled).active,
.btn-primary2:not(:disabled):not(.disabled):active,
.show > .btn-primary2.dropdown-toggle {
  background: #33c1b0;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-primary2 {
  background: transparent;
  border-radius: 0px;
  border-color: #33c1b0;
  color: #33c1b0;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-primary2:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-primary2:hover {
  background: #33c1b0;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-primary2:not(:disabled):not(.disabled).active:focus,
.btn-outline-primary2:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-primary2.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-primary2:not(:disabled):not(.disabled).active,
.btn-outline-primary2:not(:disabled):not(.disabled):active,
.show > .btn-outline-primary2.dropdown-toggle {
  background: #33c1b0;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-primary3 {
  background: #66d0c4;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-primary3:hover {
  background: rgba(102, 206, 195, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-primary3:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-primary3:not(:disabled):not(.disabled).active:focus,
.btn-primary3:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary3.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-primary3:not(:disabled):not(.disabled).active,
.btn-primary3:not(:disabled):not(.disabled):active,
.show > .btn-primary3.dropdown-toggle {
  background: #66d0c4;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-primary3 {
  background: transparent;
  border-radius: 0px;
  border-color: #66d0c4;
  color: #66d0c4;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-primary3:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-primary3:hover {
  background: #66d0c4;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-primary3:not(:disabled):not(.disabled).active:focus,
.btn-outline-primary3:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-primary3.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-primary3:not(:disabled):not(.disabled).active,
.btn-outline-primary3:not(:disabled):not(.disabled):active,
.show > .btn-outline-primary3.dropdown-toggle {
  background: #66d0c4;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-primary4 {
  background: #73dfd7;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-primary4:hover {
  background: rgba(153, 223, 215, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-primary4:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-primary4:not(:disabled):not(.disabled).active:focus,
.btn-primary4:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary4.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-primary4:not(:disabled):not(.disabled).active,
.btn-primary4:not(:disabled):not(.disabled):active,
.show > .btn-primary4.dropdown-toggle {
  background: #73dfd7;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-primary4 {
  background: transparent;
  border-radius: 0px;
  border-color: #73dfd7;
  color: #73dfd7;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-primary4:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-primary4:hover {
  background: #73dfd7;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-primary4:not(:disabled):not(.disabled).active:focus,
.btn-outline-primary4:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-primary4.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-primary4:not(:disabled):not(.disabled).active,
.btn-outline-primary4:not(:disabled):not(.disabled):active,
.show > .btn-outline-primary4.dropdown-toggle {
  background: #73dfd7;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-primary5 {
  background: #bfefeb;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-primary5:hover {
  background: rgba(204, 239, 235, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-primary5:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-primary5:not(:disabled):not(.disabled).active:focus,
.btn-primary5:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary5.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-primary5:not(:disabled):not(.disabled).active,
.btn-primary5:not(:disabled):not(.disabled):active,
.show > .btn-primary5.dropdown-toggle {
  background: #bfefeb;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-primary5 {
  background: transparent;
  border-radius: 0px;
  border-color: #bfefeb;
  color: #bfefeb;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-primary5:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-primary5:hover {
  background: #bfefeb;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-primary5:not(:disabled):not(.disabled).active:focus,
.btn-outline-primary5:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-primary5.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-primary5:not(:disabled):not(.disabled).active,
.btn-outline-primary5:not(:disabled):not(.disabled):active,
.show > .btn-outline-primary5.dropdown-toggle {
  background: #bfefeb;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-secondary {
  background: #8dbac4;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-secondary:hover {
  background: rgba(162, 184, 193, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-secondary:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-secondary:not(:disabled):not(.disabled).active:focus,
.btn-secondary:not(:disabled):not(.disabled):active:focus,
.show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-secondary:not(:disabled):not(.disabled).active,
.btn-secondary:not(:disabled):not(.disabled):active,
.show > .btn-secondary.dropdown-toggle {
  background: #8dbac4;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-secondary {
  background: transparent;
  border-radius: 0px;
  border-color: #8dbac4;
  color: #8dbac4;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-secondary:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-secondary:hover {
  background: #8dbac4;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
.btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-secondary:not(:disabled):not(.disabled).active,
.btn-outline-secondary:not(:disabled):not(.disabled):active,
.show > .btn-outline-secondary.dropdown-toggle {
  background: #8dbac4;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-secondary2 {
  background: #a4c8d0;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-secondary2:hover {
  background: rgba(181, 198, 205, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-secondary2:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-secondary2:not(:disabled):not(.disabled).active:focus,
.btn-secondary2:not(:disabled):not(.disabled):active:focus,
.show > .btn-secondary2.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-secondary2:not(:disabled):not(.disabled).active,
.btn-secondary2:not(:disabled):not(.disabled):active,
.show > .btn-secondary2.dropdown-toggle {
  background: #a4c8d0;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-secondary2 {
  background: transparent;
  border-radius: 0px;
  border-color: #a4c8d0;
  color: #a4c8d0;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-secondary2:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-secondary2:hover {
  background: #a4c8d0;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-secondary2:not(:disabled):not(.disabled).active:focus,
.btn-outline-secondary2:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-secondary2.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-secondary2:not(:disabled):not(.disabled).active,
.btn-outline-secondary2:not(:disabled):not(.disabled):active,
.show > .btn-outline-secondary2.dropdown-toggle {
  background: #a4c8d0;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-secondary3 {
  background: #bbd6dc;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-secondary3:hover {
  background: rgba(199, 212, 218, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-secondary3:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-secondary3:not(:disabled):not(.disabled).active:focus,
.btn-secondary3:not(:disabled):not(.disabled):active:focus,
.show > .btn-secondary3.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-secondary3:not(:disabled):not(.disabled).active,
.btn-secondary3:not(:disabled):not(.disabled):active,
.show > .btn-secondary3.dropdown-toggle {
  background: #bbd6dc;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-secondary3 {
  background: transparent;
  border-radius: 0px;
  border-color: #bbd6dc;
  color: #bbd6dc;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-secondary3:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-secondary3:hover {
  background: #bbd6dc;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-secondary3:not(:disabled):not(.disabled).active:focus,
.btn-outline-secondary3:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-secondary3.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-secondary3:not(:disabled):not(.disabled).active,
.btn-outline-secondary3:not(:disabled):not(.disabled):active,
.show > .btn-outline-secondary3.dropdown-toggle {
  background: #bbd6dc;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-secondary4 {
  background: #d8e4e7;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-secondary4:hover {
  background: rgba(218, 227, 230, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-secondary4:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-secondary4:not(:disabled):not(.disabled).active:focus,
.btn-secondary4:not(:disabled):not(.disabled):active:focus,
.show > .btn-secondary4.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-secondary4:not(:disabled):not(.disabled).active,
.btn-secondary4:not(:disabled):not(.disabled):active,
.show > .btn-secondary4.dropdown-toggle {
  background: #d8e4e7;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-secondary4 {
  background: transparent;
  border-radius: 0px;
  border-color: #d8e4e7;
  color: #d8e4e7;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-secondary4:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-secondary4:hover {
  background: #d8e4e7;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-secondary4:not(:disabled):not(.disabled).active:focus,
.btn-outline-secondary4:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-secondary4.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-secondary4:not(:disabled):not(.disabled).active,
.btn-outline-secondary4:not(:disabled):not(.disabled):active,
.show > .btn-outline-secondary4.dropdown-toggle {
  background: #d8e4e7;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-secondary5 {
  background: #ebf1f3;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-secondary5:hover {
  background: rgba(236, 241, 243, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-secondary5:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-secondary5:not(:disabled):not(.disabled).active:focus,
.btn-secondary5:not(:disabled):not(.disabled):active:focus,
.show > .btn-secondary5.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-secondary5:not(:disabled):not(.disabled).active,
.btn-secondary5:not(:disabled):not(.disabled):active,
.show > .btn-secondary5.dropdown-toggle {
  background: #ebf1f3;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-secondary5 {
  background: transparent;
  border-radius: 0px;
  border-color: #ebf1f3;
  color: #ebf1f3;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-secondary5:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-secondary5:hover {
  background: #ebf1f3;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-secondary5:not(:disabled):not(.disabled).active:focus,
.btn-outline-secondary5:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-secondary5.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-secondary5:not(:disabled):not(.disabled).active,
.btn-outline-secondary5:not(:disabled):not(.disabled):active,
.show > .btn-outline-secondary5.dropdown-toggle {
  background: #ebf1f3;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-info {
  background: #231f20;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-info:hover {
  background: rgba(30, 42, 57, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-info:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-info:not(:disabled):not(.disabled).active:focus,
.btn-info:not(:disabled):not(.disabled):active:focus,
.show > .btn-info.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-info:not(:disabled):not(.disabled).active,
.btn-info:not(:disabled):not(.disabled):active,
.show > .btn-info.dropdown-toggle {
  background: #231f20;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-info {
  background: transparent;
  border-radius: 0px;
  border-color: #231f20;
  color: #231f20;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-info:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-info:hover {
  background: #231f20;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-info:not(:disabled):not(.disabled).active:focus,
.btn-outline-info:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-info:not(:disabled):not(.disabled).active,
.btn-outline-info:not(:disabled):not(.disabled):active,
.show > .btn-outline-info.dropdown-toggle {
  background: #231f20;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-info2 {
  background: #231f20 ;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-info2:hover {
  background: rgba(75, 85, 97, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-info2:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-info2:not(:disabled):not(.disabled).active:focus,
.btn-info2:not(:disabled):not(.disabled):active:focus,
.show > .btn-info2.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-info2:not(:disabled):not(.disabled).active,
.btn-info2:not(:disabled):not(.disabled):active,
.show > .btn-info2.dropdown-toggle {
  background: #231f20 ;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-info2 {
  background: transparent;
  border-radius: 0px;
  border-color: #231f20 ;
  color: #231f20 ;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-info2:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-info2:hover {
  background: #231f20 ;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-info2:not(:disabled):not(.disabled).active:focus,
.btn-outline-info2:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-info2.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-info2:not(:disabled):not(.disabled).active,
.btn-outline-info2:not(:disabled):not(.disabled):active,
.show > .btn-outline-info2.dropdown-toggle {
  background: #231f20 ;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-info3 {
  background: #667986;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-info3:hover {
  background: rgba(120, 127, 136, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-info3:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-info3:not(:disabled):not(.disabled).active:focus,
.btn-info3:not(:disabled):not(.disabled):active:focus,
.show > .btn-info3.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-info3:not(:disabled):not(.disabled).active,
.btn-info3:not(:disabled):not(.disabled):active,
.show > .btn-info3.dropdown-toggle {
  background: #667986;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-info3 {
  background: transparent;
  border-radius: 0px;
  border-color: #667986;
  color: #667986;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-info3:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-info3:hover {
  background: #667986;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-info3:not(:disabled):not(.disabled).active:focus,
.btn-outline-info3:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-info3.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-info3:not(:disabled):not(.disabled).active,
.btn-outline-info3:not(:disabled):not(.disabled):active,
.show > .btn-outline-info3.dropdown-toggle {
  background: #667986;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-info4 {
  background: #a5acb1;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-info4:hover {
  background: rgba(165, 170, 176, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-info4:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-info4:not(:disabled):not(.disabled).active:focus,
.btn-info4:not(:disabled):not(.disabled):active:focus,
.show > .btn-info4.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-info4:not(:disabled):not(.disabled).active,
.btn-info4:not(:disabled):not(.disabled):active,
.show > .btn-info4.dropdown-toggle {
  background: #a5acb1;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-info4 {
  background: transparent;
  border-radius: 0px;
  border-color: #a5acb1;
  color: #a5acb1;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-info4:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-info4:hover {
  background: #a5acb1;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-info4:not(:disabled):not(.disabled).active:focus,
.btn-outline-info4:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-info4.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-info4:not(:disabled):not(.disabled).active,
.btn-outline-info4:not(:disabled):not(.disabled):active,
.show > .btn-outline-info4.dropdown-toggle {
  background: #a5acb1;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-info5 {
  background: #d2d5d8;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-info5:hover {
  background: rgba(210, 212, 215, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-info5:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-info5:not(:disabled):not(.disabled).active:focus,
.btn-info5:not(:disabled):not(.disabled):active:focus,
.show > .btn-info5.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-info5:not(:disabled):not(.disabled).active,
.btn-info5:not(:disabled):not(.disabled):active,
.show > .btn-info5.dropdown-toggle {
  background: #d2d5d8;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-info5 {
  background: transparent;
  border-radius: 0px;
  border-color: #d2d5d8;
  color: #d2d5d8;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-info5:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-info5:hover {
  background: #d2d5d8;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-info5:not(:disabled):not(.disabled).active:focus,
.btn-outline-info5:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-info5.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-info5:not(:disabled):not(.disabled).active,
.btn-outline-info5:not(:disabled):not(.disabled):active,
.show > .btn-outline-info5.dropdown-toggle {
  background: #d2d5d8;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-light {
  background: #007365;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-light:hover {
  background: rgba(0, 114, 102, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-light:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-light:not(:disabled):not(.disabled).active:focus,
.btn-light:not(:disabled):not(.disabled):active:focus,
.show > .btn-light.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-light:not(:disabled):not(.disabled).active,
.btn-light:not(:disabled):not(.disabled):active,
.show > .btn-light.dropdown-toggle {
  background: #007365;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-light {
  background: transparent;
  border-radius: 0px;
  border-color: #007365;
  color: #007365;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-light:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-light:hover {
  background: #007365;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-light:not(:disabled):not(.disabled).active:focus,
.btn-outline-light:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-light:not(:disabled):not(.disabled).active,
.btn-outline-light:not(:disabled):not(.disabled):active,
.show > .btn-outline-light.dropdown-toggle {
  background: #007365;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-light2 {
  background: #338f84;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-light2:hover {
  background: rgba(51, 142, 133, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-light2:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-light2:not(:disabled):not(.disabled).active:focus,
.btn-light2:not(:disabled):not(.disabled):active:focus,
.show > .btn-light2.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-light2:not(:disabled):not(.disabled).active,
.btn-light2:not(:disabled):not(.disabled):active,
.show > .btn-light2.dropdown-toggle {
  background: #338f84;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-light2 {
  background: transparent;
  border-radius: 0px;
  border-color: #338f84;
  color: #338f84;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-light2:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-light2:hover {
  background: #338f84;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-light2:not(:disabled):not(.disabled).active:focus,
.btn-outline-light2:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-light2.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-light2:not(:disabled):not(.disabled).active,
.btn-outline-light2:not(:disabled):not(.disabled):active,
.show > .btn-outline-light2.dropdown-toggle {
  background: #338f84;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-light3 {
  background: #66aba3;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-light3:hover {
  background: rgba(102, 170, 163, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-light3:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-light3:not(:disabled):not(.disabled).active:focus,
.btn-light3:not(:disabled):not(.disabled):active:focus,
.show > .btn-light3.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-light3:not(:disabled):not(.disabled).active,
.btn-light3:not(:disabled):not(.disabled):active,
.show > .btn-light3.dropdown-toggle {
  background: #66aba3;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-light3 {
  background: transparent;
  border-radius: 0px;
  border-color: #66aba3;
  color: #66aba3;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-light3:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-light3:hover {
  background: #66aba3;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-light3:not(:disabled):not(.disabled).active:focus,
.btn-outline-light3:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-light3.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-light3:not(:disabled):not(.disabled).active,
.btn-outline-light3:not(:disabled):not(.disabled):active,
.show > .btn-outline-light3.dropdown-toggle {
  background: #66aba3;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-light4 {
  background: #84c8c3;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-light4:hover {
  background: rgba(153, 199, 194, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-light4:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-light4:not(:disabled):not(.disabled).active:focus,
.btn-light4:not(:disabled):not(.disabled):active:focus,
.show > .btn-light4.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-light4:not(:disabled):not(.disabled).active,
.btn-light4:not(:disabled):not(.disabled):active,
.show > .btn-light4.dropdown-toggle {
  background: #84c8c3;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-light4 {
  background: transparent;
  border-radius: 0px;
  border-color: #84c8c3;
  color: #84c8c3;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-light4:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-light4:hover {
  background: #84c8c3;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-light4:not(:disabled):not(.disabled).active:focus,
.btn-outline-light4:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-light4.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-light4:not(:disabled):not(.disabled).active,
.btn-outline-light4:not(:disabled):not(.disabled):active,
.show > .btn-outline-light4.dropdown-toggle {
  background: #84c8c3;
  border-color: transparent;
  color: white;
}

/* line 5, ../scss/_buttons.scss */
.btn-light5 {
  background: #c4e4e0;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 11, ../scss/_buttons.scss */
.btn-light5:hover {
  background: rgba(204, 227, 224, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 18, ../scss/_buttons.scss */
.btn-light5:focus {
  box-shadow: none;
  outline: 0;
}

/* line 23, ../scss/_buttons.scss */
.btn-light5:not(:disabled):not(.disabled).active:focus,
.btn-light5:not(:disabled):not(.disabled):active:focus,
.show > .btn-light5.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 29, ../scss/_buttons.scss */
.btn-light5:not(:disabled):not(.disabled).active,
.btn-light5:not(:disabled):not(.disabled):active,
.show > .btn-light5.dropdown-toggle {
  background: #c4e4e0;
  border-color: transparent;
  color: white;
}

/* line 37, ../scss/_buttons.scss */
.btn-outline-light5 {
  background: transparent;
  border-radius: 0px;
  border-color: #c4e4e0;
  color: #c4e4e0;
}

/* line 44, ../scss/_buttons.scss */
.btn-outline-light5:focus {
  box-shadow: none;
  outline: 0;
}

/* line 48, ../scss/_buttons.scss */
.btn-outline-light5:hover {
  background: #c4e4e0;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 54, ../scss/_buttons.scss */
.btn-outline-light5:not(:disabled):not(.disabled).active:focus,
.btn-outline-light5:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-light5.dropdown-toggle:focus {
  box-shadow: none;
}

/* line 60, ../scss/_buttons.scss */
.btn-outline-light5:not(:disabled):not(.disabled).active,
.btn-outline-light5:not(:disabled):not(.disabled):active,
.show > .btn-outline-light5.dropdown-toggle {
  background: #c4e4e0;
  border-color: transparent;
  color: white;
}

/* Scss Document */
/* line 3, ../scss/_text-colors.scss */
.text-primary {
  color: #231f20 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-primary2 {
  color: #231f20  !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-primary3 {
  color: #667986 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-primary4 {
  color: #a5acb1 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-primary5 {
  color: #d2d5d8 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-secondary {
  color: #007365 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-secondary2 {
  color: #338f84 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-secondary3 {
  color: #66aba3 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-secondary4 {
  color: #84c8c3 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-secondary5 {
  color: #c4e4e0 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-info {
  color: #00b19c !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-info2 {
  color: #33c1b0 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-info3 {
  color: #66d0c4 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-info4 {
  color: #73dfd7 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-info5 {
  color: #bfefeb !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-light {
  color: #8dbac4 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-light2 {
  color: #a4c8d0 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-light3 {
  color: #bbd6dc !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-light4 {
  color: #d8e4e7 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-light5 {
  color: #ebf1f3 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-highlight {
  color: #3bcd3f !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-highlight2 {
  color: #62d765 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-highlight3 {
  color: #89e18c !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-highlight4 {
  color: #bfebb9 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-highlight5 {
  color: #e0f5dd !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-danger {
  color: #BB3609 !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-success {
  color: #25BB3D !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-warning {
  color: #FBC20D !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-white {
  color: #fff !important;
}

/* line 3, ../scss/_text-colors.scss */
.text-transparent {
  color: transparent !important;
}

/* Scss Document */
/* line 3, ../scss/_background-colors.scss */
.bg-primary {
  background: #231f20 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-primary2 {
  background: #231f20  !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-primary3 {
  background: #667986 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-primary4 {
  background: #a5acb1 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-primary5 {
  background: #d2d5d8 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-secondary {
  background: #007365 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-secondary2 {
  background: #338f84 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-secondary3 {
  background: #66aba3 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-secondary4 {
  background: #84c8c3 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-secondary5 {
  background: #c4e4e0 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-info {
  background: #00b19c !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-info2 {
  background: #33c1b0 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-info3 {
  background: #66d0c4 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-info4 {
  background: #73dfd7 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-info5 {
  background: #bfefeb !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-light {
  background: #8dbac4 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-light2 {
  background: #a4c8d0 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-light3 {
  background: #bbd6dc !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-light4 {
  background: #d8e4e7 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-light5 {
  background: #ebf1f3 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-highlight {
  background: #3bcd3f !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-highlight2 {
  background: #62d765 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-highlight3 {
  background: #89e18c !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-highlight4 {
  background: #bfebb9 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-highlight5 {
  background: #e0f5dd !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-danger {
  background: #BB3609 !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-success {
  background: #25BB3D !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-warning {
  background: #FBC20D !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-white {
  background: #fff !important;
}

/* line 3, ../scss/_background-colors.scss */
.bg-transparent {
  background: transparent !important;
}

/* Scss Document */
/* line 4, ../scss/_link-colors.scss */
a.text-primary:hover {
  color: #231f20 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-primary2:hover {
  color: #231f20  !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-primary3:hover {
  color: #667986 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-primary4:hover {
  color: #a5acb1 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-primary5:hover {
  color: #d2d5d8 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-secondary:hover {
  color: #007365 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-secondary2:hover {
  color: #338f84 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-secondary3:hover {
  color: #66aba3 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-secondary4:hover {
  color: #84c8c3 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-secondary5:hover {
  color: #c4e4e0 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-info:hover {
  color: #00b19c !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-info2:hover {
  color: #33c1b0 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-info3:hover {
  color: #66d0c4 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-info4:hover {
  color: #73dfd7 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-info5:hover {
  color: #bfefeb !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-light:hover {
  color: #8dbac4 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-light2:hover {
  color: #a4c8d0 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-light3:hover {
  color: #bbd6dc !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-light4:hover {
  color: #d8e4e7 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-light5:hover {
  color: #ebf1f3 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-highlight:hover {
  color: #3bcd3f !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-highlight2:hover {
  color: #62d765 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-highlight3:hover {
  color: #89e18c !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-highlight4:hover {
  color: #bfebb9 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-highlight5:hover {
  color: #e0f5dd !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-danger:hover {
  color: #BB3609 !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-success:hover {
  color: #25BB3D !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-warning:hover {
  color: #FBC20D !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-white:hover {
  color: #fff !important;
}
/* line 4, ../scss/_link-colors.scss */
a.text-transparent:hover {
  color: transparent !important;
}

/* Scss Document */
/* line 4, ../scss/_alerts.scss */
.alert-primary {
  background: #231f20;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-primary2 {
  background: #231f20 ;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-primary3 {
  background: #667986;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-primary4 {
  background: #a5acb1;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-primary5 {
  background: #d2d5d8;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-secondary {
  background: #007365;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-secondary2 {
  background: #338f84;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-secondary3 {
  background: #66aba3;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-secondary4 {
  background: #84c8c3;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-secondary5 {
  background: #c4e4e0;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-info {
  background: #00b19c;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-info2 {
  background: #33c1b0;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-info3 {
  background: #66d0c4;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-info4 {
  background: #73dfd7;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-info5 {
  background: #bfefeb;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-light {
  background: #8dbac4;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-light2 {
  background: #a4c8d0;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-light3 {
  background: #bbd6dc;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-light4 {
  background: #d8e4e7;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-light5 {
  background: #ebf1f3;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-highlight {
  background: #3bcd3f;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-highlight2 {
  background: #62d765;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-highlight3 {
  background: #89e18c;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-highlight4 {
  background: #bfebb9;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-highlight5 {
  background: #e0f5dd;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-danger {
  background: #BB3609;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-success {
  background: #25BB3D;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-warning {
  background: #FBC20D;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-white {
  background: #fff;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 4, ../scss/_alerts.scss */
.alert-transparent {
  background: transparent;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 12, ../scss/_alerts.scss */
.alert-link {
  color: white !important;
  font-weight: 700;
}

/* Scss Document */
/* line 3, ../scss/_typography.scss */
ul.circle {
  list-style-type: circle;
}

/* Scss Document */
/* line 2, ../scss/_breadcrumbs.scss */
.breadcrumb {
  padding: 0.5rem 0;
  margin-bottom: 1rem;
  list-style: none;
  background-color: transparent;
}

/* line 8, ../scss/_breadcrumbs.scss */
.breadcrumb-item.active {
  color: #8dbac4;
}

/* line 13, ../scss/_breadcrumbs.scss */
.breadcrumb-item a {
  color: #00b19c;
}

/* CSS Document */
/* line 8, ../scss/_badges.scss */
.badge-primary {
  background-color: #231f20;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-primary[href]:hover {
  background-color: rgba(30, 42, 57, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-primary2 {
  background-color: #231f20 ;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-primary2[href]:hover {
  background-color: rgba(75, 85, 97, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-primary3 {
  background-color: #667986;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-primary3[href]:hover {
  background-color: rgba(120, 127, 136, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-primary4 {
  background-color: #a5acb1;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-primary4[href]:hover {
  background-color: rgba(165, 170, 176, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-primary5 {
  background-color: #d2d5d8;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-primary5[href]:hover {
  background-color: rgba(210, 212, 215, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-secondary {
  background-color: #007365;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-secondary[href]:hover {
  background-color: rgba(0, 114, 102, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-secondary2 {
  background-color: #338f84;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-secondary2[href]:hover {
  background-color: rgba(51, 142, 133, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-secondary3 {
  background-color: #66aba3;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-secondary3[href]:hover {
  background-color: rgba(102, 170, 163, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-secondary4 {
  background-color: #84c8c3;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-secondary4[href]:hover {
  background-color: rgba(153, 199, 194, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-secondary5 {
  background-color: #c4e4e0;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-secondary5[href]:hover {
  background-color: rgba(204, 227, 224, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-info {
  background-color: #00b19c;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-info[href]:hover {
  background-color: rgba(0, 174, 155, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-info2 {
  background-color: #33c1b0;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-info2[href]:hover {
  background-color: rgba(51, 190, 175, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-info3 {
  background-color: #66d0c4;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-info3[href]:hover {
  background-color: rgba(102, 206, 195, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-info4 {
  background-color: #73dfd7;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-info4[href]:hover {
  background-color: rgba(153, 223, 215, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-info5 {
  background-color: #bfefeb;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-info5[href]:hover {
  background-color: rgba(204, 239, 235, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-light {
  background-color: #8dbac4;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-light[href]:hover {
  background-color: rgba(162, 184, 193, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-light2 {
  background-color: #a4c8d0;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-light2[href]:hover {
  background-color: rgba(181, 198, 205, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-light3 {
  background-color: #bbd6dc;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-light3[href]:hover {
  background-color: rgba(199, 212, 218, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-light4 {
  background-color: #d8e4e7;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-light4[href]:hover {
  background-color: rgba(218, 227, 230, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-light5 {
  background-color: #ebf1f3;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-light5[href]:hover {
  background-color: rgba(236, 241, 243, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-highlight {
  background-color: #3bcd3f;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-highlight[href]:hover {
  background-color: rgba(124, 204, 78, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-highlight2 {
  background-color: #62d765;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-highlight2[href]:hover {
  background-color: rgba(150, 214, 113, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-highlight3 {
  background-color: #89e18c;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-highlight3[href]:hover {
  background-color: rgba(176, 224, 149, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-highlight4 {
  background-color: #bfebb9;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-highlight4[href]:hover {
  background-color: rgba(203, 235, 184, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-highlight5 {
  background-color: #e0f5dd;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-highlight5[href]:hover {
  background-color: rgba(229, 245, 220, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-danger {
  background-color: #BB3609;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-danger[href]:hover {
  background-color: rgba(187, 54, 9, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-success {
  background-color: #25BB3D;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-success[href]:hover {
  background-color: rgba(37, 187, 61, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-warning {
  background-color: #FBC20D;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-warning[href]:hover {
  background-color: rgba(251, 194, 13, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-white {
  background-color: #fff;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-white[href]:hover {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 8, ../scss/_badges.scss */
.badge-transparent {
  background-color: transparent;
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 14, ../scss/_badges.scss */
.badge-transparent[href]:hover {
  background-color: rgba(0, 0, 0, 0);
  border-radius: 0px;
  border-color: transparent;
  color: white;
}

/* line 24, ../scss/_badges.scss */
.badge {
  font-weight: 100;
}

/* CSS Document */
/* line 7, ../scss/_pagination.scss */
.page-item.active .page-link {
  z-index: 1;
  color: white;
  background-color: #231f20;
  border-color: transparent;
}
/* line 13, ../scss/_pagination.scss */
.page-item:first-child .page-link {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
/* line 17, ../scss/_pagination.scss */
.page-item:last-child .page-link {
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}

/* line 24, ../scss/_pagination.scss */
.page-link {
  color: #00b19c;
}
/* line 26, ../scss/_pagination.scss */
.page-link:hover {
  color: white;
  text-decoration: none;
  background-color: #231f20;
  border-color: #231f20 !important;
}

/* Scss Document */
/* line 2, ../scss/_tables.scss */
.table {
  color: #231f20 ;
  font-family: "Montserrat";
  font-size: 14px;
}
/* line 6, ../scss/_tables.scss */
.table td, .table th {
  padding: 0.5rem;
  border-top: 1px solid #d8e4e7;
  font-weight: 400;
}
/* line 11, ../scss/_tables.scss */
.table thead th {
  border-bottom: 1px solid #d8e4e7;
  font-weight: 700;
}

/* line 19, ../scss/_tables.scss */
.table .thead-dark th {
  color: white !important;
  background-color: #231f20;
  font-weight: 700;
}
/* line 29, ../scss/_tables.scss */
.table .thead-light th {
  color: #231f20 ;
  background-color: #bbd6dc;
  font-weight: 700;
}

/* line 36, ../scss/_tables.scss */
.table-dark {
  color: white !important;
  background-color: #231f20;
}

/* line 43, ../scss/_tables.scss */
.table-striped tbody tr:nth-of-type(odd) {
  background-color: #ebf1f3;
}

/* line 48, ../scss/_tables.scss */
.table-bordered {
  border: 1px solid #d8e4e7;
}
/* line 51, ../scss/_tables.scss */
.table-bordered th,
.table-bordered td {
  border: 1px solid #d8e4e7;
}
/* line 57, ../scss/_tables.scss */
.table-bordered thead th,
.table-bordered thead td {
  border-bottom-width: 2px;
}

/* line 65, ../scss/_tables.scss */
.table-hover tbody tr:hover {
  background-color: #d8e4e7;
}

/* CSS Document */
/* line 5, ../scss/_spinners.scss */
.loader-primary {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #231f20;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-primary2 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #231f20 ;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-primary3 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #667986;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-primary4 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #a5acb1;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-primary5 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #d2d5d8;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-secondary {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #007365;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-secondary2 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #338f84;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-secondary3 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #66aba3;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-secondary4 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #84c8c3;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-secondary5 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #c4e4e0;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
/* .loader-info {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #00b19c;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
} */

/* line 5, ../scss/_spinners.scss */
.loader-info2 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #33c1b0;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-info3 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #66d0c4;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-info4 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #73dfd7;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-info5 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #bfefeb;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}
.loader-info div {
  left: 50%;
  top: 50%;
  position: absolute;
  animation: anim linear 1s infinite;
  width: 4px;
  height: 29px;
  transform-origin: 2px 52px;
  z-index: 999;
}

.loader-info div:nth-child(1) {
    transform: rotate(0deg);
    animation-delay: -0.9166666666666666s;
    background: #00b19c;
}

.loader-info div:nth-child(2) {
    transform: rotate(30deg);
    animation-delay: -0.8333333333333334s;
    background: #3bcd3f;
}

.loader-info div:nth-child(3) {
    transform: rotate(60deg);
    animation-delay: -0.75s;
    background: #007365;
}

.loader-info div:nth-child(4) {
    transform: rotate(90deg);
    animation-delay: -0.6666666666666666s;
    background: #8dbac4;
}

.loader-info div:nth-child(5) {
    transform: rotate(120deg);
    animation-delay: -0.5833333333333334s;
    background: #231f20;
}

.loader-info div:nth-child(6) {
    transform: rotate(150deg);
    animation-delay: -0.5s;
    background: #33c1b0;
}

.loader-info div:nth-child(7) {
    transform: rotate(180deg);
    animation-delay: -0.4166666666666667s;
    background: #62d765;
}

.loader-info div:nth-child(8) {
    transform: rotate(210deg);
    animation-delay: -0.3333333333333333s;
    background: #338f84;
}

.loader-info div:nth-child(9) {
    transform: rotate(240deg);
    animation-delay: -0.25s;
    background: #a4c8d0;
}

.loader-info div:nth-child(10) {
    transform: rotate(270deg);
    animation-delay: -0.16666666666666666s;
    background: #231f20 ;
}

.loader-info div:nth-child(11) {
    transform: rotate(300deg);
    animation-delay: -0.08333333333333333s;
    background: #66d0c4;
}

.loader-info div:nth-child(12) {
    transform: rotate(330deg);
    animation-delay: 0s;
    background: #89e18c;
}
@keyframes anim {
  0%

    {
        opacity: 1;
    }

    80% {
        opacity: 0;
    }

}
/* line 5, ../scss/_spinners.scss */
.loader-light {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #8dbac4;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-light2 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #a4c8d0;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-light3 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #bbd6dc;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-light4 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #d8e4e7;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-light5 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #ebf1f3;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-highlight {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #3bcd3f;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-highlight2 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #62d765;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-highlight3 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #89e18c;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-highlight4 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #bfebb9;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-highlight5 {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #e0f5dd;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-danger {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #BB3609;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-success {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #25BB3D;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-warning {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #FBC20D;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-white {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid #fff;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

/* line 5, ../scss/_spinners.scss */
.loader-transparent {
  border: 10px solid #ebf1f3;
  border-radius: 50%;
  border-top: 10px solid transparent;
  width: 75px;
  height: 75px;
  -webkit-animation: spin 2s linear infinite;
  animation: spin 2s linear infinite;
}

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* line 1, ../scss/_tooltip.scss */
.tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: white;
  text-align: center;
  background-color: #02104f;
  border-radius: 0px;
}

/* CSS Document */
/* line 2, ../scss/_popovers.scss */
.popover {
  border-radius: 0px;
}

/* CSS Document */
/* line 3, ../scss/_inputgroup.scss */
.input-group-text {
  border: 1px solid #d8e4e7;
  border-radius: 0px;
  background-color: #ebf1f3;
  color: #231f20;
}

/* line 10, ../scss/_inputgroup.scss */
.input-group-sm > .form-control, .input-group-sm > .input-group-append > .btn, .input-group-sm > .input-group-append > .input-group-text, .input-group-sm > .input-group-prepend > .btn, .input-group-sm > .input-group-prepend > .input-group-text {
  border-radius: 0px;
}

/* line 14, ../scss/_inputgroup.scss */
.input-group-lg > .form-control, .input-group-lg > .input-group-append > .btn, .input-group-lg > .input-group-append > .input-group-text, .input-group-lg > .input-group-prepend > .btn, .input-group-lg > .input-group-prepend > .input-group-text {
  border-radius: 0px;
}

/* line 18, ../scss/_inputgroup.scss */
.form-control {
  border-color: #d8e4e7;
  border-radius: 0px;
  color: #231f20;
}

/* line 24, ../scss/_inputgroup.scss */
.form-control:focus {
  color: #231f20;
  border-color: #a4c8d0;
  outline: 0;
  box-shadow: none;
}

/* CSS Document */
/* line 3, ../scss/_tabs.scss */
.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
  color: #00b19c;
  background-color: white;
  border-color: #d8e4e7 #d8e4e7 white;
  border-top: 3px solid #00b19c;
}

/* line 10, ../scss/_tabs.scss */
.nav-tabs .nav-link {
  /* border-left: 1px solid #ebf1f3;
  border-right: 1px solid #ebf1f3; */
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  color: #231f20;
  border-top: 3px solid transparent;
      border-bottom: 1px solid #ccc;
}

/* line 19, ../scss/_tabs.scss */
.nav-tabs .nav-link.disabled {
  color: #667986;
  background-color: transparent;
  border-color: transparent;
}

/* line 25, ../scss/_tabs.scss */
.nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover {
  border-color: white #ebf1f3 white;
  border-top: 3px solid #00b19c;
}

/* line 30, ../scss/_tabs.scss */
.nav-link.disabled:hover {
  border-color: transparent transparent transparent;
  border-top: 3px solid transparent;
}

/* line 35, ../scss/_tabs.scss */
.nav-tabs {
  border-bottom: 0px solid #00b19c;
}

/* line 39, ../scss/_tabs.scss */
.nav-tabs .nav-item {
  margin-bottom: 0px;
}

/* CSS Document */
@media (min-width: 576px) and (max-width: 1050px) {
  /* line 4, ../scss/_navbar.scss */
  #tsc_nav_1 .nav-item {
    text-align: left !important;
    margin-left: 2px;
  }

  /* line 9, ../scss/_navbar.scss */
  #tsc_nav_1 .nav-item a {
    text-align: left !important;
    margin-left: 2px;
    padding-left: 5px;
  }

  /* line 14, ../scss/_navbar.scss */
  #tsc_nav_1 .search-form {
    margin-bottom: 5px;
    margin-left: 2px;
  }

  /* line 19, ../scss/_navbar.scss */
  #tsc_nav_1 .form-inline .form-control {
    display: inline-block;
    width: 100%;
    vertical-align: middle;
  }
}
/* line 25, ../scss/_navbar.scss */
#tsc_nav_1 .themeBorderBottom {
  border-bottom: 5px solid #00b19c;
}

/* line 29, ../scss/_navbar.scss */
#tsc_nav_1 .nav-link {
  font-size: 0.8rem;
}

/* line 33, ../scss/_navbar.scss */
#tsc_nav_1 .heading_style {
  color: white !important;
  font-size: 1.3rem;
  font-weight: bold;
}

/* line 39, ../scss/_navbar.scss */
#tsc_nav_1 .navbar .nav-link:focus, .navbar .nav-link:hover, .navbar .nav-item:hover {
  background: #00b19c;
  color: white !important;
}

/* line 44, ../scss/_navbar.scss */
#tsc_nav_1 .dropdown:hover > .dropdown-menu {
/*   display: block; */
  border-radius: 0px;
}

/* line 49, ../scss/_navbar.scss */
#tsc_nav_1 .dropdown-menu {
  font-size: 12px;
  box-shadow: 2px 5px 6px 0px rgba(0, 174, 155, 0.07);
  border: 1px solid #dfebf3;
  z-index: 1071;
  left: auto;
  right: 0;
  margin: 0;
  padding: 0;
}

/* line 60, ../scss/_navbar.scss */
#tsc_nav_1 .dropdown-item {
  padding: 0.4rem 0.9rem;
}

/* line 64, ../scss/_navbar.scss */
#tsc_nav_1 .dropdown-item:hover {
  background-color: #ebf1f3;
}

/* line 68, ../scss/_navbar.scss */
#tsc_nav_1 .navbar .navbar-toggler {
  border-color: white;
}

/* line 72, ../scss/_navbar.scss */
#tsc_nav_1 .nav-item {
  text-align: center;
}

/* line 3, ../scss/_borders.scss */
.border-primary {
  border-color: #231f20 !important;
}

/* line 3, ../scss/_borders.scss */
.border-primary2 {
  border-color: #231f20  !important;
}

/* line 3, ../scss/_borders.scss */
.border-primary3 {
  border-color: #667986 !important;
}

/* line 3, ../scss/_borders.scss */
.border-primary4 {
  border-color: #a5acb1 !important;
}

/* line 3, ../scss/_borders.scss */
.border-primary5 {
  border-color: #d2d5d8 !important;
}

/* line 3, ../scss/_borders.scss */
.border-secondary {
  border-color: #007365 !important;
}

/* line 3, ../scss/_borders.scss */
.border-secondary2 {
  border-color: #338f84 !important;
}

/* line 3, ../scss/_borders.scss */
.border-secondary3 {
  border-color: #66aba3 !important;
}

/* line 3, ../scss/_borders.scss */
.border-secondary4 {
  border-color: #84c8c3 !important;
}

/* line 3, ../scss/_borders.scss */
.border-secondary5 {
  border-color: #c4e4e0 !important;
}

/* line 3, ../scss/_borders.scss */
.border-info {
  border-color: #00b19c !important;
}

/* line 3, ../scss/_borders.scss */
.border-info2 {
  border-color: #33c1b0 !important;
}

/* line 3, ../scss/_borders.scss */
.border-info3 {
  border-color: #66d0c4 !important;
}

/* line 3, ../scss/_borders.scss */
.border-info4 {
  border-color: #73dfd7 !important;
}

/* line 3, ../scss/_borders.scss */
.border-info5 {
  border-color: #bfefeb !important;
}

/* line 3, ../scss/_borders.scss */
.border-light {
  border-color: #8dbac4 !important;
}

/* line 3, ../scss/_borders.scss */
.border-light2 {
  border-color: #a4c8d0 !important;
}

/* line 3, ../scss/_borders.scss */
.border-light3 {
  border-color: #bbd6dc !important;
}

/* line 3, ../scss/_borders.scss */
.border-light4 {
  border-color: #d8e4e7 !important;
}

/* line 3, ../scss/_borders.scss */
.border-light5 {
  border-color: #ebf1f3 !important;
}

/* line 3, ../scss/_borders.scss */
.border-highlight {
  border-color: #3bcd3f !important;
}

/* line 3, ../scss/_borders.scss */
.border-highlight2 {
  border-color: #62d765 !important;
}

/* line 3, ../scss/_borders.scss */
.border-highlight3 {
  border-color: #89e18c !important;
}

/* line 3, ../scss/_borders.scss */
.border-highlight4 {
  border-color: #bfebb9 !important;
}

/* line 3, ../scss/_borders.scss */
.border-highlight5 {
  border-color: #e0f5dd !important;
}

/* line 3, ../scss/_borders.scss */
.border-danger {
  border-color: #BB3609 !important;
}

/* line 3, ../scss/_borders.scss */
.border-success {
  border-color: #25BB3D !important;
}

/* line 3, ../scss/_borders.scss */
.border-warning {
  border-color: #FBC20D !important;
}

/* line 3, ../scss/_borders.scss */
.border-white {
  border-color: #fff !important;
}

/* line 3, ../scss/_borders.scss */
.border-transparent {
  border-color: transparent !important;
}

/* line 1, ../scss/_collapse.scss */
.card {
  border-radius: 0px;
  border: 1px solid #d8e4e7;
}

/* line 6, ../scss/_collapse.scss */
.card-header {
  background-color: #ebf1f3;
  border-bottom: 1px solid transparent;
}

/* line 11, ../scss/_collapse.scss */
.btn-link {
  font-weight: 400;
  color: #231f20 ;
  background-color: transparent;
}

/* line 17, ../scss/_collapse.scss */
.btn-link:hover {
  color: #231f20;
  text-decoration: underline;
  background-color: transparent;
  border-color: transparent;
}

/* CSS Document */
/* line 3, ../scss/_footer.scss */
.footer {
  font-size: 14px;
  position: relative;
  width: 100%;
  background: #02104f;
  border-top: 1px solid #02104f;
  padding: 8px 0;
  color: white;
}
/* line 13, ../scss/_footer.scss */
.footer a {
  color: white;
}
/* line 17, ../scss/_footer.scss */
.footer a:hover {
  text-decoration: underline;
}

/* CSS Document */
/* line 2, ../scss/_tags.scss */
.bootstrap-tagsinput .tag {
  margin-right: 2px;
  color: #fff;
  background-color: #00b19c;
  padding: 5px;
  font-size: 12px;
}

/* CSS Document */
/* line 2, ../scss/_select2.scss */
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #00b19c;
  color: white;
}

/* line 7, ../scss/_select2.scss */
.select2-container--default .select2-selection--single {
  border-radius: 0;
}

/* line 11, ../scss/_select2.scss */
.select2-container *:focus {
  outline: none;
}

/* line 1, ../scss/_redactor.scss */
.redactor-air a:hover, .redactor-toolbar a:hover {
  outline: 0;
  color: #fff;
  background: #00b19c;
}

/* CSS Document */
/* CSS Document */
/* line 3, ../scss/_multiselect.scss */
.multiselect-container > li > a {
  padding: 0;
  margin-left: -28px;
}

/* line 8, ../scss/_multiselect.scss */
.multiselect-container.dropdown-menu.show {
  display: block;
  width: 100%;
  min-width: 12rem;
}

/* line 19, ../scss/_multiselect.scss */
.multiselect-native-select > .btn-group > .btn-default {
  color: #888888;
  background-color: #fff;
  border-color: #bbd6dc;
  padding: 5px 12px 6px 12px;
  border-radius: 0px;
}

/* line 39, ../scss/_multiselect.scss */
.btn.focus, .btn:focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
}

/* CSS Document */
/* line 6, ../scss/_datatable.scss */
table.dataTable {
  margin-top: 0 !important;
}

/* line 7, ../scss/_datatable.scss */
.dataTables_wrapper .form-control, .dataTables_paginate li a {
  border-radius: 0px !important;
}

/* line 8, ../scss/_datatable.scss */
.dataTables_wrapper .dataTables_length, .dataTables_wrapper .dataTables_filter, .dataTables_wrapper .dataTables_info, .dataTables_wrapper .dataTables_processing, .dataTables_wrapper .dataTables_paginate {
  color: #667986 !important;
  font-size: 13px;
}

/* line 10, ../scss/_datatable.scss */
table.dataTable.table-condensed > thead > tr > th {
  border-bottom: none;
  border-top: 1px solid #ddd !important;
  text-align: center;
}

/* line 11, ../scss/_datatable.scss */
table.dataTable.table-condensed > tbody > tr td:last-child {
  text-align: center;
}

/* line 12, ../scss/_datatable.scss */
table.dataTable.no-footer {
  border-bottom: 1px solid #ddd !important;
}

/* line 13, ../scss/_datatable.scss */
table.dataTable tbody td {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}

/* line 14, ../scss/_datatable.scss */
table.dataTable thead .sorting.hidesort:after {
  opacity: 0;
  content: "";
}

/* line 18, ../scss/_datatable.scss */
.dataTables_wrapper .form-control {
  color: #888888 !important;
}

/* line 22, ../scss/_datatable.scss */
.dataTables_wrapper .dataTables_paginate .paginate_button.current, .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  background: #00b19c !important;
  color: white !important;
  border-radius: 0 !important;
  border: 1px solid #00b19c !important;
}

/* line 29, ../scss/_datatable.scss */
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled, .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover, .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
  cursor: default !important;
  color: #667986 !important;
  border: 1px solid #d8e4e7 !important;
  background: transparent !important;
  box-shadow: none;
}

/* line 37, ../scss/_datatable.scss */
.dataTables_wrapper .dataTables_paginate .paginate_button {
  border-radius: 0;
  margin-left: 0;
}

/* line 42, ../scss/_datatable.scss */
table.dataTable {
  border-collapse: collapse;
}

/* line 46, ../scss/_datatable.scss */
#projDetails_filter [type=search] {
  outline-offset: -2px;
  -webkit-appearance: button;
}

/* line 51, ../scss/_datatable.scss */
#projDetails_filter input:focus {
  outline-color: #ffffff;
}

/* line 55, ../scss/_datatable.scss */
#projDetails_length label select:focus {
  outline-color: white;
}

/* line 59, ../scss/_datatable.scss */
tfoot {
  display: table-header-group;
}

/* line 63, ../scss/_datatable.scss */
input:active, input:focus {
  outline-color: #ffffff;
}

/* line 67, ../scss/_datatable.scss */
table.dataTable thead .sorting_asc {
  background-image: url(../images/sort_asc.png);
}

/* line 71, ../scss/_datatable.scss */
table.dataTable thead .sorting {
  background-image: url(../images/sort_both.png);
}

/* CSS Document */
/* line 2, ../scss/_autosuggest.scss */
.easy-autocomplete input {
  border-radius: 0px;
}

/* CSS Document */
/* line 2, ../scss/_3leveldropdown.scss */
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: .5rem 0;
  margin: 0 0 0;
  font-size: 1rem;
  color: #231f20;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0;
}

/* line 22, ../scss/_3leveldropdown.scss */
.dropdown-submenu {
  position: relative;
}

/* line 26, ../scss/_3leveldropdown.scss */
.dropdown-submenu > .dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: -6px;
  margin-left: -1px;
}

/* line 33, ../scss/_3leveldropdown.scss */


/* line 37, ../scss/_3leveldropdown.scss */
.dropdown-submenu:hover > .dropdown-menu {
  display: block;
}

/* line 41, ../scss/_3leveldropdown.scss */
.dropdown-submenu > a:after {
  display: block;
  content: " ";
  float: right;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  border-left-color: #ccc;
  margin-top: 5px;
  margin-right: -10px;
}

/* line 55, ../scss/_3leveldropdown.scss */
.dropdown-submenu:hover > a:after {
  border-left-color: #fff;
}

/* line 59, ../scss/_3leveldropdown.scss */
.dropdown-submenu.pull-left {
  float: none;
}

/* line 63, ../scss/_3leveldropdown.scss */
.dropdown-submenu.pull-left > .dropdown-menu {
  left: -100%;
  margin-left: 10px;
}

/*# sourceMappingURL=uikit.css.map */
