// GridStack-based Canvas Composer with Real Charts
class CanvasComposer {
  constructor() {
    this.widgets = {};
    this.chartInstances = new Map();
    this.tabContainers = new Map(); // Initialize tab containers Map
    this.gridStack = null;
    this.widgetCounter = 0;
    this.paletteCollapsed = false;
    this.currentSectionId = null;
    this.creatingSection = false;

    // Initialize when DOM is ready
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => this.init());
    } else {
      this.init();
    }
  }

  init() {
    this.initGridStack();
    this.bindEvents();
    this.hideInitially(); // Hide canvas composer by default
  }

  hideInitially() {
    // Hide the canvas composer overlay by default
    const overlay = document.querySelector(".canvas-composer-overlay");
    if (overlay) {
      overlay.style.display = "none";
    }
  }

  initGridStack() {
    const grid = document.getElementById("canvasGrid");
    if (!grid) {
      console.error("Canvas grid element not found!");
      return;
    }

    // Initialize GridStack with professional settings
    this.gridStack = GridStack.init(
      {
        cellHeight: 80,
        verticalMargin: 10,
        horizontalMargin: 10,
        minRow: 8,
        float: false,
        animate: true,
        resizable: {
          handles: "se, sw, ne, nw",
        },
        draggable: {
          handle: ".widget-header",
        },
      },
      grid
    );

    console.log("GridStack initialized successfully:", this.gridStack);

    // Handle widget changes
    this.gridStack.on("change", (event, items) => {
      items.forEach((item) => {
        const widgetId = item.el.getAttribute("data-widget-id");
        const widget = this.widgets[widgetId];
        if (widget) {
          widget.x = item.x;
          widget.y = item.y;
          widget.width = item.w;
          widget.height = item.h;
        }
      });
    });

    // Handle widget removal
    this.gridStack.on("removed", (event, items) => {
      items.forEach((item) => {
        const widgetId = item.el.getAttribute("data-widget-id");
        if (widgetId) {
          this.cleanupWidget(widgetId);
        }
      });
    });
  }

  bindEvents() {
    // Remove any existing event listeners to prevent duplicates
    const existingCloseBtn = document.querySelector(".canvas-close");
    if (existingCloseBtn) {
      existingCloseBtn.replaceWith(existingCloseBtn.cloneNode(true));
    }

    // Close button
    const closeBtn = document.querySelector(".canvas-close");
    if (closeBtn) {
      closeBtn.addEventListener("click", () => this.close());
    }

    // Create section button - use more specific selector to avoid conflicts
    const createBtns = document.querySelectorAll(".canvas-btn-primary");
    createBtns.forEach((btn) => {
      // Check if this button is specifically for creating sections
      if (btn.textContent.includes("Create Section")) {
        // Remove existing listeners by cloning the button
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);

        // Add the event listener to the new button
        newBtn.addEventListener("click", (e) => {
          e.preventDefault();
          e.stopPropagation();
          this.createSection();
        });
      }
    });
  }

  // Add widget to canvas
  addWidget(type) {
    if (!this.gridStack) {
      console.error("GridStack not initialized!");
      return;
    }

    const widgetId = "widget_" + Date.now();
    const widget = this.createWidgetData(widgetId, type);

    // Create DOM element
    const gridItem = document.createElement("div");
    gridItem.className = "grid-stack-item";
    gridItem.setAttribute("gs-w", widget.width);
    gridItem.setAttribute("gs-h", widget.height);
    gridItem.setAttribute("gs-x", widget.x);
    gridItem.setAttribute("gs-y", widget.y);
    gridItem.setAttribute("data-widget-id", widgetId);

    const content = document.createElement("div");
    content.className = "grid-stack-item-content";
    content.innerHTML = `
      <div class="widget-header">
        <h6 class="widget-title">
          <i class="${widget.icon}"></i>
          ${widget.title}
        </h6>
        <div class="widget-controls">
          <button class="widget-control-btn edit" onclick="window.canvasComposer.editWidget('${widgetId}')" title="Edit">
            <i class="las la-edit"></i>
          </button>
          <button class="widget-control-btn delete" onclick="window.canvasComposer.removeWidget('${widgetId}')" title="Delete">
            <i class="las la-trash"></i>
          </button>
        </div>
      </div>
      <div class="widget-content" id="widget_content_${widgetId}">
        <div class="loading-placeholder">
          <i class="${widget.icon}" style="font-size: 1.5rem; color: #667eea; margin-bottom: 0.5rem;"></i>
          <p style="font-size: 0.8rem;">Loading ${widget.title}...</p>
        </div>
      </div>
    `;

    gridItem.appendChild(content);

    // Add to GridStack directly (GridStack will handle DOM insertion)
    this.gridStack.addWidget(gridItem, {
      x: widget.x,
      y: widget.y,
      w: widget.width,
      h: widget.height,
    });

    // Store widget data
    this.widgets[widgetId] = widget;

    console.log("Widget added successfully:", widgetId, widget);

    // Initialize widget content after a short delay
    setTimeout(() => {
      this.initializeWidgetContent(widgetId, content);
    }, 100);

    return widget;
  }

  createWidgetData(id, type) {
    const widgetTypes = {
      "pie-chart": {
        title: "Pie Chart",
        width: 4,
        height: 4,
        icon: "las la-chart-pie",
      },
      "bar-chart": {
        title: "Bar Chart",
        width: 6,
        height: 4,
        icon: "las la-chart-bar",
      },
      "line-chart": {
        title: "Line Chart",
        width: 6,
        height: 4,
        icon: "las la-chart-line",
      },
      "world-map": {
        title: "World Map",
        width: 8,
        height: 5,
        icon: "las la-globe",
      },
      kpi: {
        title: "KPI Widget",
        width: 3,
        height: 2,
        icon: "las la-tachometer-alt",
      },
      handsontable: {
        title: "Data Table",
        width: 8,
        height: 6,
        icon: "las la-table",
      },
      "price-chart": {
        title: "Price Chart",
        width: 6,
        height: 4,
        icon: "las la-chart-area",
      },
      "word-cloud": {
        title: "Word Cloud",
        width: 4,
        height: 4,
        icon: "las la-cloud",
      },
      text: { title: "Text Widget", width: 4, height: 3, icon: "las la-font" },
      image: {
        title: "Image Widget",
        width: 4,
        height: 4,
        icon: "las la-image",
      },
      video: {
        title: "Video Widget",
        width: 6,
        height: 4,
        icon: "las la-video",
      },
      "pdf-viewer": {
        title: "PDF Viewer",
        width: 6,
        height: 6,
        icon: "las la-file-pdf",
      },
      tabs: {
        title: "Tab Container",
        width: 8,
        height: 6,
        icon: "las la-folder-open",
      },
      "section-container": {
        title: "Section Container",
        width: 6,
        height: 4,
        icon: "las la-layer-group",
      },
    };

    const config = widgetTypes[type] || widgetTypes["text"];

    return {
      id,
      type,
      title: config.title,
      width: config.width,
      height: config.height,
      x: 0,
      y: 0,
      icon: config.icon,
      data: this.getDefaultWidgetData(type),
    };
  }

  getDefaultWidgetData(type) {
    // Return appropriate default data for each widget type
    switch (type) {
      case "kpi":
        return { value: "1,234", label: "Total Sales", trend: "+12%" };
      case "text":
        return { content: "Enter your text here..." };
      case "image":
        return { src: "", alt: "Image placeholder" };
      case "video":
        return { src: "", title: "Video placeholder" };
      case "tabs":
        return {
          tabs: [
            {
              id: "tab1",
              name: "Analysis",
              icon: "las la-chart-line",
              color: "#3b82f6",
              widgets: [],
            },
            {
              id: "tab2",
              name: "Reports",
              icon: "las la-file-alt",
              color: "#10b981",
              widgets: [],
            },
          ],
          activeTab: "tab1",
        };
      case "section-container":
        return {
          title: "Section Container",
          widgets: [],
        };
      default:
        return {};
    }
  }

  initializeWidgetContent(widgetId, widgetElement) {
    const container = widgetElement.querySelector(".widget-content");
    if (!container) return;

    const widgetData = this.widgets[widgetId];
    if (!widgetData) return;

    // Add drag functionality for tab assignment - but only when not in GridStack drag mode
    widgetElement.addEventListener("dragstart", (e) => {
      // Only handle custom drag if we're in tab assignment mode
      const tabAssignmentOverlay = document.querySelector(
        ".tab-assignment-overlay[style*='display: block']"
      );
      if (tabAssignmentOverlay) {
        console.log("Starting drag for tab assignment:", widgetId);
        e.dataTransfer.setData("text/plain", widgetId);
        e.dataTransfer.setData("application/x-widget-id", widgetId);
        widgetElement.style.opacity = "0.5";
        widgetElement.classList.add("dragging-for-tab-assignment");

        // Prevent GridStack from handling this drag
        e.stopPropagation();
      }
    });

    widgetElement.addEventListener("dragend", (e) => {
      widgetElement.style.opacity = "1";
      widgetElement.classList.remove("dragging-for-tab-assignment");
      console.log("Drag ended for widget:", widgetId);
    });

    // Make widget draggable for tab assignment
    widgetElement.setAttribute("draggable", "true");

    // Initialize widget content based on type
    switch (widgetData.type) {
      case "pie-chart":
        this.createPieChart(container, widgetId);
        break;
      case "bar-chart":
        this.createBarChart(container, widgetId);
        break;
      case "line-chart":
        this.createLineChart(container, widgetId);
        break;
      case "world-map":
        this.createWorldMap(container, widgetId);
        break;
      case "price-chart":
        this.createPriceChart(container, widgetId);
        break;
      case "word-cloud":
        this.createWordCloud(container, widgetId);
        break;
      case "data-table":
        this.createDataTable(container, widgetId);
        break;
      case "kpi":
        this.createKPIWidget(container, widgetData);
        break;
      case "text":
        this.createTextWidget(container, widgetData);
        break;
      case "image":
        this.createImageWidget(container, widgetData);
        break;
      case "video":
        this.createVideoWidget(container, widgetData);
        break;
      case "pdf":
        this.createPDFViewer(container, widgetData);
        break;
      case "tabs":
        this.createTabContainer(container, widgetData);
        break;
      case "section-container":
        this.createSectionContainer(container, widgetData);
        break;
      // amCharts fallback methods
      case "amcharts-pie":
        this.createPieChart(container, widgetId);
        break;
      case "amcharts-bar":
        this.createBarChart(container, widgetId);
        break;
      case "amcharts-line":
        this.createLineChart(container, widgetId);
        break;
      case "amcharts-world-map":
        this.createWorldMap(container, widgetId);
        break;
      case "amcharts-price":
        this.createPriceChart(container, widgetId);
        break;
      case "amcharts-word-cloud":
        this.createWordCloud(container, widgetId);
        break;
      case "handsontable":
        this.createDataTable(container, widgetId);
        break;
      default:
        container.innerHTML = `<p>Widget type "${widgetData.type}" not implemented yet.</p>`;
        break;
    }
  }

  // Chart creation methods using existing amCharts setup
  createPieChart(container, widgetId) {
    container.innerHTML =
      '<div class="chart-container" style="width: 100%; height: 100%;"></div>';
    const chartContainer = container.querySelector(".chart-container");

    // Create a simple placeholder pie chart
    chartContainer.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
        <div style="text-align: center; color: #6c757d;">
          <i class="las la-chart-pie" style="font-size: 3rem; margin-bottom: 1rem; color: #007bff;"></i>
          <p style="margin: 0; font-weight: 500;">Pie Chart</p>
          <small>Sample Data Visualization</small>
        </div>
      </div>
    `;
  }

  createBarChart(container, widgetId) {
    container.innerHTML =
      '<div class="chart-container" style="width: 100%; height: 100%;"></div>';
    const chartContainer = container.querySelector(".chart-container");

    // Create a simple placeholder bar chart
    chartContainer.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
        <div style="text-align: center; color: #6c757d;">
          <i class="las la-chart-bar" style="font-size: 3rem; margin-bottom: 1rem; color: #28a745;"></i>
          <p style="margin: 0; font-weight: 500;">Bar Chart</p>
          <small>Sample Data Visualization</small>
        </div>
      </div>
    `;
  }

  createLineChart(container, widgetId) {
    container.innerHTML =
      '<div class="chart-container" style="width: 100%; height: 100%;"></div>';
    const chartContainer = container.querySelector(".chart-container");

    // Create a simple placeholder line chart
    chartContainer.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
        <div style="text-align: center; color: #6c757d;">
          <i class="las la-chart-line" style="font-size: 3rem; margin-bottom: 1rem; color: #ffc107;"></i>
          <p style="margin: 0; font-weight: 500;">Line Chart</p>
          <small>Sample Data Visualization</small>
        </div>
      </div>
    `;
  }

  createWorldMap(container, widgetId) {
    container.innerHTML =
      '<div class="chart-container" style="width: 100%; height: 100%;"></div>';
    const chartContainer = container.querySelector(".chart-container");

    // Create a simple placeholder world map
    chartContainer.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
        <div style="text-align: center; color: #6c757d;">
          <i class="las la-globe" style="font-size: 3rem; margin-bottom: 1rem; color: #17a2b8;"></i>
          <p style="margin: 0; font-weight: 500;">World Map</p>
          <small>Geographic Data Visualization</small>
        </div>
      </div>
    `;
  }

  createPriceChart(container, widgetId) {
    container.innerHTML =
      '<div class="chart-container" style="width: 100%; height: 100%;"></div>';
    const chartContainer = container.querySelector(".chart-container");

    // Create a simple placeholder price chart
    chartContainer.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
        <div style="text-align: center; color: #6c757d;">
          <i class="las la-chart-area" style="font-size: 3rem; margin-bottom: 1rem; color: #dc3545;"></i>
          <p style="margin: 0; font-weight: 500;">Price Chart</p>
          <small>Financial Data Visualization</small>
        </div>
      </div>
    `;
  }

  createWordCloud(container, widgetId) {
    container.innerHTML =
      '<div class="chart-container" style="width: 100%; height: 100%;"></div>';
    const chartContainer = container.querySelector(".chart-container");

    // Create a simple placeholder word cloud
    chartContainer.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
        <div style="text-align: center; color: #6c757d;">
          <i class="las la-cloud" style="font-size: 3rem; margin-bottom: 1rem; color: #6f42c1;"></i>
          <p style="margin: 0; font-weight: 500;">Word Cloud</p>
          <small>Text Data Visualization</small>
        </div>
      </div>
    `;
  }

  createDataTable(container, widgetId) {
    container.innerHTML =
      '<div class="table-container" style="width: 100%; height: 100%;"></div>';
    const tableContainer = container.querySelector(".table-container");

    if (typeof Handsontable !== "undefined") {
      const data = [
        ["Company", "Revenue", "Profit"],
        ["Company A", "$1,234,567", "$234,567"],
        ["Company B", "$987,654", "$187,654"],
        ["Company C", "$2,345,678", "$445,678"],
        ["Company D", "$1,567,890", "$267,890"],
      ];

      const hot = new Handsontable(tableContainer, {
        data: data,
        rowHeaders: true,
        colHeaders: true,
        height: "100%",
        licenseKey: "non-commercial-and-evaluation",
        stretchH: "all",
      });

      this.chartInstances.set(widgetId, hot);
    } else {
      tableContainer.innerHTML = "<p>Handsontable not available</p>";
    }
  }

  createKPIWidget(container, data) {
    container.innerHTML = `
      <div class="kpi-widget">
        <div class="kpi-value">${data.value}</div>
        <div class="kpi-label">${data.label}</div>
        ${
          data.trend
            ? `<div class="kpi-trend" style="color: #28a745; font-size: 0.9rem; margin-top: 0.5rem;">${data.trend}</div>`
            : ""
        }
      </div>
    `;
  }

  createTextWidget(container, data) {
    container.innerHTML = `
      <div style="padding: 1rem; height: 100%; overflow-y: auto;">
        <div contenteditable="true" style="outline: none; height: 100%;">${data.content}</div>
      </div>
    `;
  }

  createImageWidget(container, data) {
    container.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; padding: 1rem;">
        ${
          data.src
            ? `<img src="${data.src}" alt="${data.alt}" style="max-width: 100%; max-height: 100%; object-fit: contain;">`
            : `<div style="text-align: center; color: #64748b;">
            <i class="las la-image" style="font-size: 3rem; margin-bottom: 1rem;"></i>
            <p>Click to add image</p>
          </div>`
        }
      </div>
    `;
  }

  createVideoWidget(container, data) {
    container.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; padding: 1rem;">
        ${
          data.src
            ? `<video controls style="max-width: 100%; max-height: 100%;">
            <source src="${data.src}" type="video/mp4">
            Your browser does not support the video tag.
          </video>`
            : `<div style="text-align: center; color: #64748b;">
            <i class="las la-video" style="font-size: 3rem; margin-bottom: 1rem;"></i>
            <p>Click to add video</p>
          </div>`
        }
      </div>
    `;
  }

  createPDFViewer(container, data) {
    container.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; padding: 1rem;">
        <div style="text-align: center; color: #64748b;">
          <i class="las la-file-pdf" style="font-size: 3rem; margin-bottom: 1rem;"></i>
          <p>PDF Viewer</p>
          <p style="font-size: 0.8rem;">Click to load PDF</p>
        </div>
      </div>
    `;
  }

  createTabContainer(container, data) {
    const tabContainerId = `tab-container-${Date.now()}`;

    container.innerHTML = `
      <div class="smart-tab-container" id="${tabContainerId}" style="height: 100%; display: flex; flex-direction: column;">
        <!-- Tab Container Header -->
        <div class="tab-container-header" style="padding: 12px 16px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px 8px 0 0;">
          <div style="display: flex; justify-content: between; align-items: center;">
            <div style="display: flex; align-items: center; gap: 8px;">
              <i class="las la-layer-group" style="font-size: 18px;"></i>
              <span style="font-weight: 600;">Tab Container</span>
              <span class="tab-count-badge" style="background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 12px; font-size: 12px;">
                ${data.tabs?.length || 0} tabs
              </span>
            </div>
            <button class="configure-tabs-btn" onclick="window.canvasComposer.configureTabContainer('${tabContainerId}')" 
                    style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 12px;">
              <i class="las la-cog"></i> Configure
            </button>
          </div>
        </div>

        <!-- Tab Navigation -->
        <div class="tab-navigation" style="display: flex; background: #f8fafc; border-bottom: 2px solid #e2e8f0; overflow-x: auto;">
          ${this.renderTabNavigation(
            data.tabs || [],
            data.activeTab || "tab1",
            tabContainerId
          )}
        </div>

        <!-- Tab Content Area -->
        <div class="tab-content-area" style="flex: 1; position: relative; background: #ffffff; border-radius: 0 0 8px 8px;">
          ${this.renderTabContent(data.tabs || [], data.activeTab || "tab1")}
        </div>

        <!-- Tab Assignment Overlay (hidden by default) -->
        <div class="tab-assignment-overlay" id="${tabContainerId}-overlay" style="display: none; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.8); z-index: 1000; border-radius: 8px;">
          <div style="padding: 20px; color: white; text-align: center;">
            <h4><i class="las la-magic"></i> Assign Widgets to Tabs</h4>
            <p>Drag widgets from the canvas to the colored zones below</p>
            <div class="tab-drop-zones" id="${tabContainerId}-zones">
              <!-- Drop zones will be generated here -->
            </div>
            <button onclick="window.canvasComposer.exitTabAssignmentMode('${tabContainerId}')" 
                    style="background: #ef4444; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; margin-top: 16px;">
              <i class="las la-times"></i> Exit Assignment Mode
            </button>
          </div>
        </div>
      </div>
    `;

    // Store tab container data
    if (!this.tabContainers) {
      this.tabContainers = new Map();
    }
    this.tabContainers.set(tabContainerId, {
      tabs: data.tabs || [
        {
          id: "tab1",
          name: "Analysis",
          icon: "las la-chart-line",
          color: "#3b82f6",
          widgets: [],
        },
        {
          id: "tab2",
          name: "Reports",
          icon: "las la-file-alt",
          color: "#10b981",
          widgets: [],
        },
      ],
      activeTab: data.activeTab || "tab1",
    });
  }

  renderTabNavigation(tabs, activeTab, containerId = null) {
    return (
      tabs
        .map(
          (tab) => `
      <div class="tab-nav-item" style="position: relative; display: inline-block;">
        <button class="tab-nav-btn ${tab.id === activeTab ? "active" : ""}" 
                onclick="window.canvasComposer.switchToTab('${tab.id}')"
                style="padding: 12px 16px; border: none; background: ${
                  tab.id === activeTab ? "#ffffff" : "transparent"
                }; 
                       color: ${
                         tab.id === activeTab ? tab.color : "#64748b"
                       }; cursor: pointer; border-radius: 6px 6px 0 0; 
                       margin-right: 4px; font-weight: ${
                         tab.id === activeTab ? "600" : "400"
                       }; transition: all 0.2s ease;
                       border-bottom: 3px solid ${
                         tab.id === activeTab ? tab.color : "transparent"
                       };">
          <i class="${tab.icon}" style="margin-right: 6px;"></i>
          ${tab.name}
          <span class="widget-count" style="background: ${
            tab.color
          }; color: white; padding: 2px 6px; border-radius: 10px; font-size: 10px; margin-left: 6px;">
            ${tab.widgets?.length || 0}
          </span>
        </button>
        ${
          containerId
            ? `
        <button class="tab-add-widget-btn" 
                onclick="window.canvasComposer.showTabWidgetSelector('${tab.id}', '${containerId}', event)"
                style="position: absolute; top: -5px; right: -5px; width: 20px; height: 20px; 
                       border-radius: 50%; border: none; background: ${tab.color}; color: white; 
                       font-size: 12px; cursor: pointer; opacity: 0; transition: opacity 0.2s ease;
                       display: flex; align-items: center; justify-content: center; z-index: 10;"
                onmouseover="this.style.opacity='1'"
                onmouseout="this.style.opacity='0.7'">
          <i class="las la-plus"></i>
        </button>
        `
            : ""
        }
      </div>
    `
        )
        .join("") +
      (containerId
        ? `
      <div class="tab-nav-item" style="display: inline-block; margin-left: 10px;">
        <button class="tab-manage-btn" 
                onclick="window.canvasComposer.showTabManager('${containerId}')"
                style="padding: 8px 12px; border: 1px dashed #cbd5e0; background: transparent; 
                       color: #64748b; cursor: pointer; border-radius: 6px; font-size: 12px;
                       transition: all 0.2s ease;"
                onmouseover="this.style.borderColor='#3b82f6'; this.style.color='#3b82f6'"
                onmouseout="this.style.borderColor='#cbd5e0'; this.style.color='#64748b'">
          <i class="las la-cog"></i> Manage
        </button>
      </div>
      `
        : "")
    );
  }

  renderTabContent(tabs, activeTab) {
    const activeTabData = tabs.find((tab) => tab.id === activeTab);
    if (!activeTabData) {
      return `
        <div class="tab-content-placeholder" style="display: flex; align-items: center; justify-content: center; height: 100%; color: #64748b;">
          <div style="text-align: center;">
            <i class="las la-plus-circle" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
            <h4>No tabs configured</h4>
            <p>Click "Configure" to add tabs and organize your widgets</p>
          </div>
        </div>
      `;
    }

    return `
      <div class="active-tab-content" style="padding: 20px; height: 100%;">
        <div class="tab-info" style="background: ${
          activeTabData.color
        }10; border-left: 4px solid ${
      activeTabData.color
    }; padding: 12px; margin-bottom: 16px; border-radius: 0 4px 4px 0;">
          <div style="display: flex; align-items: center; gap: 8px; color: ${
            activeTabData.color
          };">
            <i class="${activeTabData.icon}" style="font-size: 18px;"></i>
            <span style="font-weight: 600;">${activeTabData.name}</span>
            <span style="font-size: 12px; opacity: 0.7;">(${
              activeTabData.widgets?.length || 0
            } widgets)</span>
          </div>
        </div>
        
        <div class="tab-widget-preview" style="background: #f8fafc; border: 2px dashed #cbd5e0; border-radius: 8px; padding: 20px; text-align: center; color: #64748b;">
          ${
            activeTabData.widgets?.length > 0
              ? `<div>
              <i class="las la-eye" style="font-size: 24px; margin-bottom: 8px;"></i>
              <p><strong>${activeTabData.widgets.length} widgets</strong> assigned to this tab</p>
              <p style="font-size: 12px;">Widgets will be shown/hidden based on active tab when section is created</p>
            </div>`
              : `<div>
              <i class="las la-inbox" style="font-size: 24px; margin-bottom: 8px;"></i>
              <p>No widgets assigned to this tab yet</p>
              <p style="font-size: 12px;">Use "Configure" to assign widgets to tabs</p>
            </div>`
          }
        </div>
      </div>
    `;
  }

  configureTabContainer(containerId) {
    console.log("Configuring tab container:", containerId);
    console.log("Available tab containers:", this.tabContainers);

    const containerData = this.tabContainers.get(containerId);
    if (!containerData) {
      console.error("Container data not found for:", containerId);
      console.log(
        "Available containers:",
        Array.from(this.tabContainers.keys())
      );
      return;
    }

    console.log("Container data found:", containerData);
    // Create configuration modal
    this.showTabConfigurationModal(containerId, containerData);
  }

  showTabConfigurationModal(containerId, containerData) {
    const modalId = `tab-config-modal-${Date.now()}`;

    const modalHTML = `
      <div class="modal fade" id="${modalId}" tabindex="-1" style="z-index: 15000;">
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
              <h5 class="modal-title">
                <i class="las la-layer-group"></i> Configure Tab Container
              </h5>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <!-- Tab Management Section -->
              <div class="row">
                <div class="col-md-4">
                  <h6 class="mb-3"><i class="las la-cog"></i> Tab Management</h6>
                  <div class="tab-list" id="${modalId}-tab-list">
                    ${this.renderTabList(containerData.tabs, modalId)}
                  </div>
                  <button class="btn btn-primary btn-sm w-100 mt-2" onclick="window.canvasComposer.addNewTab('${modalId}', '${containerId}')">
                    <i class="las la-plus"></i> Add New Tab
                  </button>
                </div>
                
                <div class="col-md-8">
                  <h6 class="mb-3"><i class="las la-magic"></i> Widget Assignment</h6>
                  <div class="widget-assignment-area">
                    <div class="alert alert-info mb-3">
                      <i class="las la-info-circle"></i>
                      <strong>Simple Assignment:</strong> Check the boxes to assign widgets to tabs. Each widget can only be in one tab at a time.
                    </div>
                    
                    <div class="widget-assignment-grid" id="${modalId}-widget-grid">
                      ${this.renderWidgetAssignmentGrid(
                        containerData,
                        modalId,
                        containerId
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                <i class="las la-times"></i> Cancel
              </button>
              <button type="button" class="btn btn-success" onclick="window.canvasComposer.saveTabConfiguration('${containerId}', '${modalId}')">
                <i class="las la-save"></i> Save Configuration
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML("beforeend", modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById(modalId));
    modal.show();

    // Clean up modal when hidden
    document.getElementById(modalId).addEventListener("hidden.bs.modal", () => {
      document.getElementById(modalId).remove();
    });
  }

  renderTabList(tabs, modalId) {
    return tabs
      .map(
        (tab, index) => `
      <div class="tab-item mb-3 p-3 border rounded" style="border-color: ${tab.color};">
        <div class="d-flex align-items-center mb-2">
          <div class="tab-color-indicator" style="width: 20px; height: 20px; background: ${tab.color}; border-radius: 50%; margin-right: 10px;"></div>
          <input type="text" class="form-control form-control-sm" value="${tab.name}" 
                 onchange="window.canvasComposer.updateTabName('${modalId}', ${index}, this.value)" 
                 style="border: none; background: transparent; font-weight: 500;">
        </div>
        <div class="row">
          <div class="col-6">
            <label class="form-label small">Icon</label>
            <input type="text" class="form-control form-control-sm" value="${tab.icon}" 
                   onchange="window.canvasComposer.updateTabIcon('${modalId}', ${index}, this.value)"
                   placeholder="las la-icon">
          </div>
          <div class="col-6">
            <label class="form-label small">Color</label>
            <input type="color" class="form-control form-control-color form-control-sm" value="${tab.color}" 
                   onchange="window.canvasComposer.updateTabColor('${modalId}', ${index}, this.value)">
          </div>
        </div>
        <button class="btn btn-outline-danger btn-sm mt-2" onclick="window.canvasComposer.removeTab('${modalId}', ${index})">
          <i class="las la-trash"></i> Remove Tab
        </button>
      </div>
    `
      )
      .join("");
  }

  renderWidgetAssignmentGrid(containerData, modalId, containerId) {
    // Get all widgets from the dashboard
    const widgets = this.widgets ? Object.values(this.widgets) : [];

    if (widgets.length === 0) {
      return `
        <div class="assignment-empty-state">
          <div class="empty-state-icon">
            <i class="las la-puzzle-piece"></i>
          </div>
          <h4>No widgets available</h4>
          <p>Create some widgets first to assign them to tabs.</p>
          <button class="btn btn-primary" onclick="window.canvasComposer.close()">
            <i class="las la-plus"></i>
            Add Widgets
          </button>
        </div>
      `;
    }

    // Get assignment statistics
    const totalWidgets = widgets.length;
    const assignedCount = this.getAssignedWidgetCount(containerData);
    const unassignedCount = totalWidgets - assignedCount;

    return `
      <div class="simple-widget-assignment">
        <!-- Header with stats -->
        <div class="assignment-header">
          <div class="assignment-title">
            <i class="las la-layer-group"></i>
            <h4>Manage Tab Widgets</h4>
            <p>Organize your ${totalWidgets} widgets across tabs</p>
          </div>
          <div class="assignment-stats">
            <span class="stat-item">
              <i class="las la-check-circle"></i>
              ${assignedCount} assigned
            </span>
            <span class="stat-item">
              <i class="las la-clock"></i>
              ${unassignedCount} available
            </span>
          </div>
        </div>

        <!-- Simple tab list interface -->
        <div class="tab-assignment-list">
          ${containerData.tabs
            .map((tab) =>
              this.renderSimpleTabAssignment(
                tab,
                containerData,
                containerId,
                widgets
              )
            )
            .join("")}
        </div>

        <!-- Quick actions -->
        <div class="assignment-quick-actions">
          <button class="action-btn secondary" onclick="window.canvasComposer.autoAssignWidgets('${containerId}', '${modalId}')">
            <i class="las la-wand-magic-sparkles"></i>
            Auto-assign remaining widgets
          </button>
          ${
            assignedCount > 0
              ? `
          <button class="action-btn outline" onclick="window.canvasComposer.clearAllAssignments('${containerId}', '${modalId}')">
            <i class="las la-eraser"></i>
            Clear all assignments
          </button>
          `
              : ""
          }
        </div>

        <!-- Unassigned widgets (if any) -->
        ${
          unassignedCount > 0
            ? `
        <div class="unassigned-widgets">
          <h6><i class="las la-cubes"></i> Available Widgets (${unassignedCount})</h6>
          <div class="unassigned-list">
            ${this.getUnassignedWidgets()
              .filter((w) => widgets.find((widget) => widget.id === w.id))
              .map((widget) => this.renderUnassignedWidget(widget, containerId))
              .join("")}
          </div>
        </div>
        `
            : ""
        }
      </div>
    `;
  }

  renderSimpleTabAssignment(tab, containerData, containerId, allWidgets) {
    const assignedWidgets = tab.widgets || [];
    const widgetCount = assignedWidgets.length;

    return `
      <div class="tab-assignment-item" style="border-left: 3px solid ${
        tab.color
      }">
        <div class="tab-assignment-header">
          <div class="tab-info">
            <i class="${tab.icon}" style="color: ${tab.color}"></i>
            <div class="tab-details">
              <h5>${tab.name}</h5>
              <span class="widget-count">${widgetCount} widget${
      widgetCount !== 1 ? "s" : ""
    }</span>
            </div>
          </div>
          <div class="tab-actions">
            <button class="add-widget-btn" onclick="window.canvasComposer.showTabWidgetSelector('${
              tab.id
            }', '${containerId}', event)" title="Add widget to ${tab.name}">
              <i class="las la-plus"></i>
            </button>
          </div>
        </div>
        
        <div class="tab-widgets-list">
          ${
            assignedWidgets.length > 0
              ? assignedWidgets
                  .map((widgetId) => {
                    const widget = allWidgets.find((w) => w.id === widgetId);
                    return widget
                      ? this.renderAssignedWidget(widget, tab.id, containerId)
                      : "";
                  })
                  .join("")
              : `<div class="no-widgets">
              <i class="las la-plus-circle"></i>
              <span>Click + to add widgets</span>
            </div>`
          }
        </div>
      </div>
    `;
  }

  renderAssignedWidget(widget, tabId, containerId) {
    const widgetTypeInfo = this.getWidgetTypeInfo(widget.type);

    return `
      <div class="assigned-widget-item">
        <div class="widget-icon" style="background: ${widgetTypeInfo.gradient}">
          <i class="${widgetTypeInfo.icon}"></i>
        </div>
        <div class="widget-info">
          <span class="widget-title">${widget.title}</span>
          <span class="widget-type">${widgetTypeInfo.name}</span>
        </div>
        <div class="widget-actions">
          <button class="remove-btn" onclick="window.canvasComposer.removeWidgetFromTab('${widget.id}', '${tabId}', '${containerId}')" title="Remove from tab">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
    `;
  }

  renderUnassignedWidget(widget, containerId) {
    const widgetTypeInfo = this.getWidgetTypeInfo(widget.type);

    return `
      <div class="unassigned-widget-item">
        <div class="widget-icon" style="background: ${widgetTypeInfo.gradient}">
          <i class="${widgetTypeInfo.icon}"></i>
        </div>
        <div class="widget-info">
          <span class="widget-title">${widget.title}</span>
          <span class="widget-type">${widgetTypeInfo.name}</span>
        </div>
        <div class="widget-actions">
          <button class="quick-assign-btn" onclick="window.canvasComposer.showQuickAssignMenu(event, '${widget.id}', '${containerId}', 'quick-assign')" title="Quick assign to tab">
            <i class="las la-plus"></i>
          </button>
        </div>
      </div>
    `;
  }

  // Helper method to remove widget from specific tab
  removeWidgetFromTab(widgetId, tabId, containerId) {
    const containerElement = document.getElementById(containerId);
    if (!containerElement) return;

    let containerData = JSON.parse(
      containerElement.dataset.containerData || "{}"
    );

    // Find the tab and remove the widget
    const tab = containerData.tabs.find((t) => t.id === tabId);
    if (tab && tab.widgets) {
      tab.widgets = tab.widgets.filter((id) => id !== widgetId);
    }

    // Update the container data
    containerElement.dataset.containerData = JSON.stringify(containerData);

    // Refresh the tab container and modal
    this.refreshTabContainer(containerId);
    this.refreshWidgetAssignmentGrid(
      "tab-config-modal-" + Date.now(),
      containerId,
      containerData
    );

    this.showToast(`Widget removed from ${tab.name}`, "success");
  }

  renderWidgetPool(widgets, containerData, containerId, modalId) {
    return `
      <div class="widget-cards-pool">
        ${widgets
          .map((widget) =>
            this.renderDraggableWidgetCard(
              widget,
              containerData,
              containerId,
              modalId
            )
          )
          .join("")}
      </div>
    `;
  }

  renderDraggableWidgetCard(widget, containerData, containerId, modalId) {
    const assignedTab = this.getWidgetAssignedTab(widget.id, containerData);
    const isAssigned = assignedTab !== null;
    const widgetTypeInfo = this.getWidgetTypeInfo(widget.type);

    return `
      <div class="draggable-widget-card ${
        isAssigned ? "assigned" : "unassigned"
      }" 
           draggable="true"
           data-widget-id="${widget.id}"
           data-widget-type="${widget.type}"
           ondragstart="window.canvasComposer.handleWidgetDragStart(event, '${
             widget.id
           }')"
           oncontextmenu="window.canvasComposer.showWidgetContextMenu(event, '${
             widget.id
           }', '${containerId}', '${modalId}')">
        
        <div class="widget-card-preview">
          <div class="widget-preview-icon" style="background: ${
            widgetTypeInfo.gradient
          }">
            <i class="${widgetTypeInfo.icon}"></i>
          </div>
          ${
            isAssigned
              ? `
            <div class="assignment-indicator" style="background: ${assignedTab.color}">
              <i class="${assignedTab.icon}"></i>
            </div>
          `
              : ""
          }
        </div>
        
        <div class="widget-card-info">
          <div class="widget-card-title">
            <h5>${widget.title}</h5>
            <p>${widgetTypeInfo.category}</p>
          </div>
          
          <div class="widget-card-features">
            ${widgetTypeInfo.features
              .map((feature) => `<span class="feature-tag">${feature}</span>`)
              .join("")}
          </div>
          
          <div class="widget-card-actions">
            ${
              isAssigned
                ? `
              <button class="assign-button assigned" onclick="window.canvasComposer.unassignWidget('${containerId}', '${widget.id}', '${modalId}')">
                <i class="las la-unlink"></i>
                <span>Unassign</span>
              </button>
            `
                : `
              <button class="assign-button" onclick="window.canvasComposer.showQuickAssignMenu(event, '${widget.id}', '${containerId}', '${modalId}')">
                <i class="las la-plus"></i>
                <span>Assign</span>
              </button>
            `
            }
          </div>
        </div>
      </div>
    `;
  }

  renderTabDropZones(containerData, containerId, modalId) {
    return `
      <div class="drop-zones-grid">
        ${containerData.tabs
          .map((tab) =>
            this.renderTabDropZone(tab, containerData, containerId, modalId)
          )
          .join("")}
      </div>
    `;
  }

  renderTabDropZone(tab, containerData, containerId, modalId) {
    const assignedWidgets = tab.widgets || [];
    const widgetCount = assignedWidgets.length;

    return `
      <div class="tab-drop-zone" 
           data-tab-id="${tab.id}"
           style="border-color: ${tab.color}30; background: ${tab.color}08"
           ondragover="window.canvasComposer.handleDragOver(event)"
           ondrop="window.canvasComposer.handleWidgetDrop(event, '${
             tab.id
           }', '${containerId}', '${modalId}')"
           ondragenter="window.canvasComposer.handleDragEnter(event)"
           ondragleave="window.canvasComposer.handleDragLeave(event)">
        
        <div class="drop-zone-header" style="background: ${tab.color}15">
          <div class="tab-info">
            <i class="${tab.icon}" style="color: ${tab.color}"></i>
            <div class="tab-details">
              <h6 style="color: ${tab.color}">${tab.name}</h6>
              <span class="widget-count">${widgetCount} widget${
      widgetCount !== 1 ? "s" : ""
    }</span>
            </div>
          </div>
          <div class="drop-zone-actions">
            <button class="btn-icon" onclick="window.canvasComposer.previewTab('${
              tab.id
            }', '${containerId}')" title="Preview Tab">
              <i class="las la-eye"></i>
            </button>
          </div>
        </div>
        
        <div class="drop-zone-content">
          ${
            widgetCount > 0
              ? `
            <div class="assigned-widgets-mini">
              ${assignedWidgets
                .slice(0, 3)
                .map((widgetId) => {
                  const widget = this.widgets[widgetId];
                  if (!widget) return "";
                  const typeInfo = this.getWidgetTypeInfo(widget.type);
                  return `
                  <div class="mini-widget-card" title="${widget.title}">
                    <i class="${typeInfo.icon}"></i>
                    <span>${widget.title}</span>
                    <button class="remove-mini" onclick="window.canvasComposer.unassignWidget('${containerId}', '${widgetId}', '${modalId}')">
                      <i class="las la-times"></i>
                    </button>
                  </div>
                `;
                })
                .join("")}
              ${
                widgetCount > 3
                  ? `<div class="more-widgets">+${widgetCount - 3} more</div>`
                  : ""
              }
            </div>
          `
              : `
            <div class="drop-zone-placeholder">
              <i class="las la-hand-paper"></i>
              <p>Drop widgets here</p>
              <div class="drop-hint">Perfect for ${this.getTabSuggestion(
                tab.name
              )}</div>
            </div>
          `
          }
        </div>
      </div>
    `;
  }

  renderLiveTabPreview(containerData) {
    return `
      <div class="preview-tabs-container">
        ${containerData.tabs
          .map((tab) => {
            const widgetCount = tab.widgets ? tab.widgets.length : 0;
            return `
            <div class="preview-tab-item" style="border-left: 3px solid ${
              tab.color
            }">
              <div class="preview-tab-header">
                <i class="${tab.icon}" style="color: ${tab.color}"></i>
                <span>${tab.name}</span>
                <div class="preview-count" style="background: ${
                  tab.color
                }20; color: ${tab.color}">${widgetCount}</div>
              </div>
              <div class="preview-tab-widgets">
                ${
                  widgetCount > 0
                    ? (tab.widgets || [])
                        .map((widgetId) => {
                          const widget = this.widgets[widgetId];
                          return widget
                            ? `<span class="preview-widget">${widget.title}</span>`
                            : "";
                        })
                        .join("")
                    : '<span class="preview-empty">No widgets</span>'
                }
              </div>
            </div>
          `;
          })
          .join("")}
      </div>
    `;
  }

  getWidgetTypeInfo(type) {
    const typeMap = {
      "pie-chart": {
        icon: "las la-chart-pie",
        name: "Pie Chart",
        color: "#3b82f6",
        gradient: "linear-gradient(135deg, #3b82f6, #1d4ed8)",
        features: ["Interactive", "Data Viz"],
      },
      "bar-chart": {
        icon: "las la-chart-bar",
        name: "Bar Chart",
        color: "#10b981",
        gradient: "linear-gradient(135deg, #10b981, #047857)",
        features: ["Responsive", "Analytics"],
      },
      "line-chart": {
        icon: "las la-chart-line",
        name: "Line Chart",
        color: "#f59e0b",
        gradient: "linear-gradient(135deg, #f59e0b, #d97706)",
        features: ["Time Series", "Trends"],
      },
      kpi: {
        icon: "las la-tachometer-alt",
        name: "KPI Widget",
        color: "#ef4444",
        gradient: "linear-gradient(135deg, #ef4444, #dc2626)",
        features: ["Real-time", "Metrics"],
      },
      "data-table": {
        icon: "las la-table",
        name: "Data Table",
        color: "#8b5cf6",
        gradient: "linear-gradient(135deg, #8b5cf6, #7c3aed)",
        features: ["Sortable", "Filterable"],
      },
      text: {
        icon: "las la-font",
        name: "Text Widget",
        color: "#06b6d4",
        gradient: "linear-gradient(135deg, #06b6d4, #0891b2)",
        features: ["Rich Text", "Markdown"],
      },
      image: {
        icon: "las la-image",
        name: "Image Widget",
        color: "#84cc16",
        gradient: "linear-gradient(135deg, #84cc16, #65a30d)",
        features: ["Responsive", "Gallery"],
      },
      video: {
        icon: "las la-video",
        name: "Video Widget",
        color: "#f97316",
        gradient: "linear-gradient(135deg, #f97316, #ea580c)",
        features: ["Streaming", "Controls"],
      },
      tabs: {
        icon: "las la-folder-open",
        name: "Tab Container",
        color: "#6b7280",
        gradient: "linear-gradient(135deg, #6b7280, #4b5563)",
        features: ["Container", "Organization"],
      },
    };

    return (
      typeMap[type] || {
        icon: "las la-puzzle-piece",
        name: "Widget",
        color: "#6b7280",
        gradient: "linear-gradient(135deg, #6b7280, #4b5563)",
        features: ["Custom"],
      }
    );
  }

  getTabSuggestion(tabName) {
    const name = tabName.toLowerCase();
    if (name.includes("chart") || name.includes("analytic"))
      return "charts and analytics";
    if (name.includes("data") || name.includes("table"))
      return "data tables and lists";
    if (name.includes("kpi") || name.includes("metric"))
      return "KPIs and metrics";
    if (name.includes("media") || name.includes("content"))
      return "media and content";
    return "various widgets";
  }

  categorizeWidgetsForTabs(widgets) {
    const categories = {
      charts: {
        title: "Charts & Analytics",
        icon: "las la-chart-line",
        color: "#3b82f6",
        description: "Visual data representations",
        widgets: [],
      },
      metrics: {
        title: "KPIs & Metrics",
        icon: "las la-tachometer-alt",
        color: "#10b981",
        description: "Key performance indicators",
        widgets: [],
      },
      data: {
        title: "Data & Tables",
        icon: "las la-table",
        color: "#f59e0b",
        description: "Structured data display",
        widgets: [],
      },
      media: {
        title: "Media & Content",
        icon: "las la-photo-video",
        color: "#8b5cf6",
        description: "Images, videos, and documents",
        widgets: [],
      },
      other: {
        title: "Other Widgets",
        icon: "las la-puzzle-piece",
        color: "#6b7280",
        description: "Miscellaneous widgets",
        widgets: [],
      },
    };

    widgets.forEach((widget) => {
      const type = widget.type.toLowerCase();

      if (
        [
          "pie-chart",
          "bar-chart",
          "line-chart",
          "world-map",
          "price-chart",
          "word-cloud",
        ].includes(type)
      ) {
        categories.charts.widgets.push(widget);
      } else if (["kpi"].includes(type)) {
        categories.metrics.widgets.push(widget);
      } else if (["data-table"].includes(type)) {
        categories.data.widgets.push(widget);
      } else if (["image", "video", "pdf-viewer"].includes(type)) {
        categories.media.widgets.push(widget);
      } else {
        categories.other.widgets.push(widget);
      }
    });

    return categories;
  }

  renderWidgetCategories(categories, containerData, containerId, modalId) {
    return Object.entries(categories)
      .filter(([key, category]) => category.widgets.length > 0)
      .map(
        ([key, category]) => `
        <div class="widget-category mb-4">
          <div class="category-header">
            <h6 class="category-title">
              <i class="${category.icon}" style="color: ${category.color};"></i>
              ${category.title}
              <span class="badge bg-light text-dark ms-2">${
                category.widgets.length
              }</span>
            </h6>
            <p class="category-description">${category.description}</p>
          </div>
          
          <div class="widget-cards-grid">
            ${category.widgets
              .map((widget) =>
                this.renderWidgetCard(
                  widget,
                  containerData,
                  containerId,
                  modalId
                )
              )
              .join("")}
          </div>
        </div>
      `
      )
      .join("");
  }

  renderWidgetCard(widget, containerData, containerId, modalId) {
    const assignedTab = this.getWidgetAssignedTab(widget.id, containerData);
    const isAssigned = assignedTab !== null;

    return `
      <div class="widget-assignment-card ${
        isAssigned ? "assigned" : "unassigned"
      }">
        <div class="widget-card-header">
          <div class="widget-info">
            <i class="${widget.icon}" style="color: ${
      isAssigned ? assignedTab.color : "#6b7280"
    };"></i>
            <div class="widget-details">
              <div class="widget-name">${widget.title}</div>
              <div class="widget-type">${widget.type}</div>
            </div>
          </div>
          ${
            isAssigned
              ? `
            <div class="assignment-badge" style="background: ${assignedTab.color}15; color: ${assignedTab.color}; border: 1px solid ${assignedTab.color}30;">
              <i class="${assignedTab.icon}"></i> ${assignedTab.name}
            </div>
          `
              : `
            <div class="unassigned-badge">
              <i class="las la-clock"></i> Unassigned
            </div>
          `
          }
        </div>
        
        <div class="widget-assignment-options">
          <div class="assignment-label">Assign to tab:</div>
          <div class="tab-options">
            <div class="form-check tab-option">
              <input class="form-check-input" type="radio" 
                     name="widget-${widget.id}" 
                     value=""
                     ${!isAssigned ? "checked" : ""}
                     onchange="window.canvasComposer.unassignWidget('${containerId}', '${
      widget.id
    }', '${modalId}')">
              <label class="form-check-label unassigned-option">
                <i class="las la-ban"></i> None
              </label>
            </div>
            ${containerData.tabs
              .map(
                (tab) => `
              <div class="form-check tab-option">
                <input class="form-check-input" type="radio" 
                       name="widget-${widget.id}" 
                       value="${tab.id}"
                       ${
                         isAssigned && assignedTab.id === tab.id
                           ? "checked"
                           : ""
                       }
                       onchange="window.canvasComposer.assignWidgetToTab('${containerId}', '${
                  widget.id
                }', '${tab.id}', '${modalId}')">
                <label class="form-check-label tab-option-label" style="color: ${
                  tab.color
                };">
                  <i class="${tab.icon}"></i> ${tab.name}
                </label>
              </div>
            `
              )
              .join("")}
          </div>
        </div>
      </div>
    `;
  }

  getWidgetAssignedTab(widgetId, containerData) {
    for (const tab of containerData.tabs) {
      if (tab.widgets && tab.widgets.includes(widgetId)) {
        return tab;
      }
    }
    return null;
  }

  getAssignedWidgetCount(containerData) {
    let count = 0;
    containerData.tabs.forEach((tab) => {
      if (tab.widgets) {
        count += tab.widgets.length;
      }
    });
    return count;
  }

  getUnassignedWidgets() {
    if (!this.widgets || Object.keys(this.widgets).length === 0) {
      return [];
    }

    // Get all widget IDs
    const allWidgetIds = Object.keys(this.widgets);

    // Get all assigned widget IDs from all tab containers
    const assignedWidgetIds = new Set();

    for (let [containerId, containerData] of this.tabContainers) {
      if (containerData.tabs) {
        containerData.tabs.forEach((tab) => {
          if (tab.widgets && Array.isArray(tab.widgets)) {
            tab.widgets.forEach((widgetId) => {
              assignedWidgetIds.add(widgetId);
            });
          }
        });
      }
    }

    // Return widgets that are not assigned to any tab
    return allWidgetIds
      .filter((widgetId) => !assignedWidgetIds.has(widgetId))
      .map((widgetId) => this.widgets[widgetId])
      .filter((widget) => widget); // Remove any null/undefined widgets
  }

  renderTabPreview(containerData) {
    return `
      <div class="tab-preview-container">
        ${containerData.tabs
          .map((tab) => {
            const widgetCount = tab.widgets ? tab.widgets.length : 0;
            const widgetList = tab.widgets
              ? tab.widgets
                  .map((widgetId) => {
                    const widget = this.widgets[widgetId];
                    return widget ? widget.title : "Unknown Widget";
                  })
                  .join(", ")
              : "No widgets assigned";

            return `
            <div class="tab-preview-item">
              <div class="tab-preview-header" style="border-left: 4px solid ${
                tab.color
              };">
                <div class="tab-preview-title">
                  <i class="${tab.icon}" style="color: ${tab.color};"></i>
                  ${tab.name}
                  <span class="widget-count-badge">${widgetCount}</span>
                </div>
              </div>
              <div class="tab-preview-content">
                ${
                  widgetCount > 0
                    ? `
                  <div class="assigned-widgets">
                    <small class="text-muted">Widgets: ${widgetList}</small>
                  </div>
                `
                    : `
                  <div class="empty-tab-message">
                    <i class="las la-inbox text-muted"></i>
                    <small class="text-muted">No widgets assigned</small>
                  </div>
                `
                }
              </div>
            </div>
          `;
          })
          .join("")}
      </div>
    `;
  }

  unassignWidget(containerId, widgetId, modalId) {
    const data = this.tabContainers.get(containerId);
    if (!data) return;

    // Remove widget from all tabs
    data.tabs.forEach((tab) => {
      if (tab.widgets) {
        const index = tab.widgets.indexOf(widgetId);
        if (index > -1) {
          tab.widgets.splice(index, 1);
        }
      }
    });

    // Update the tab containers map
    this.tabContainers.set(containerId, data);

    // Refresh the assignment grid
    this.refreshWidgetAssignmentGrid(modalId, containerId, data);

    this.showToast("Widget unassigned from all tabs", "info");
  }

  switchToTab(tabId) {
    // Find the tab container that contains this tab
    for (let [containerId, data] of this.tabContainers) {
      if (data.tabs.some((tab) => tab.id === tabId)) {
        data.activeTab = tabId;
        this.refreshTabContainer(containerId);
        break;
      }
    }
  }

  refreshTabContainer(containerId) {
    const container = document.getElementById(containerId);
    const data = this.tabContainers.get(containerId);
    if (!container || !data) return;

    // Update navigation
    const navContainer = container.querySelector(".tab-navigation");
    if (navContainer) {
      navContainer.innerHTML = this.renderTabNavigation(
        data.tabs,
        data.activeTab
      );
    }

    // Update content
    const contentContainer = container.querySelector(".tab-content-area");
    if (contentContainer) {
      contentContainer.innerHTML = this.renderTabContent(
        data.tabs,
        data.activeTab
      );
    }

    // Update tab count badge
    const countBadge = container.querySelector(".tab-count-badge");
    if (countBadge) {
      countBadge.textContent = `${data.tabs.length} tabs`;
    }
  }

  enterTabAssignmentMode(containerId) {
    console.log("Entering tab assignment mode for:", containerId);

    const overlay = document.getElementById(`${containerId}-overlay`);
    if (overlay) {
      overlay.style.display = "block";

      // Generate drop zones for each tab
      this.generateTabDropZones(containerId);

      // Close any open modals
      const modals = document.querySelectorAll(".modal.show");
      modals.forEach((modal) => {
        const modalInstance = bootstrap.Modal.getInstance(modal);
        if (modalInstance) {
          modalInstance.hide();
        }
      });

      // Temporarily disable GridStack dragging to prevent conflicts
      if (this.gridStack) {
        this.gridStack.disable();
        console.log("GridStack dragging disabled for tab assignment");
      }

      // Add visual feedback to all widgets
      const widgets = document.querySelectorAll(".grid-stack-item");
      widgets.forEach((widget) => {
        widget.classList.add("tab-assignment-mode");
        widget.style.cursor = "grab";
      });

      // Show instruction toast
      this.showToast(
        "Drag widgets to the colored zones to assign them to tabs",
        "info"
      );
    }
  }

  exitTabAssignmentMode(containerId) {
    console.log("Exiting tab assignment mode for:", containerId);

    const overlay = document.getElementById(`${containerId}-overlay`);
    if (overlay) {
      overlay.style.display = "none";
    }

    // Re-enable GridStack dragging
    if (this.gridStack) {
      this.gridStack.enable();
      console.log("GridStack dragging re-enabled");
    }

    // Remove visual feedback from all widgets
    const widgets = document.querySelectorAll(".grid-stack-item");
    widgets.forEach((widget) => {
      widget.classList.remove("tab-assignment-mode");
      widget.style.cursor = "";
    });

    this.showToast("Tab assignment mode exited", "info");
  }

  generateTabDropZones(containerId) {
    const data = this.tabContainers.get(containerId);
    const zonesContainer = document.getElementById(`${containerId}-zones`);
    if (!data || !zonesContainer) return;

    zonesContainer.innerHTML = data.tabs
      .map(
        (tab) => `
      <div class="tab-drop-zone" 
           data-tab-id="${tab.id}"
           style="background: ${tab.color}20; border: 3px dashed ${tab.color}; 
                  border-radius: 8px; padding: 20px; margin: 10px; text-align: center; 
                  min-height: 100px; transition: all 0.2s ease;"
           ondragover="event.preventDefault(); this.style.background='${
             tab.color
           }40';"
           ondragleave="this.style.background='${tab.color}20';"
           ondrop="window.canvasComposer.handleWidgetDrop(event, '${containerId}', '${
          tab.id
        }')">
        <i class="${tab.icon}" style="font-size: 24px; color: ${
          tab.color
        }; margin-bottom: 8px;"></i>
        <h5 style="color: ${tab.color}; margin-bottom: 4px;">${tab.name}</h5>
        <p style="color: ${tab.color}; opacity: 0.8; font-size: 12px;">
          ${tab.widgets?.length || 0} widgets assigned
        </p>
        <p style="color: ${tab.color}; opacity: 0.6; font-size: 10px;">
          Drop widgets here
        </p>
      </div>
    `
      )
      .join("");
  }

  addNewTab(modalId, containerId) {
    const data = this.tabContainers.get(containerId);
    if (!data) return;

    const newTab = {
      id: `tab-${Date.now()}`,
      name: `Tab ${data.tabs.length + 1}`,
      icon: "las la-cube",
      color: this.getRandomTabColor(),
      widgets: [],
    };

    data.tabs.push(newTab);

    // Refresh the tab list in modal
    const tabList = document.getElementById(`${modalId}-tab-list`);
    if (tabList) {
      tabList.innerHTML = this.renderTabList(data.tabs, modalId);
    }
  }

  updateTabName(modalId, tabIndex, newName) {
    // Find the container for this modal
    for (let [containerId, data] of this.tabContainers) {
      if (data.tabs[tabIndex]) {
        data.tabs[tabIndex].name = newName;
        break;
      }
    }
  }

  updateTabIcon(modalId, tabIndex, newIcon) {
    for (let [containerId, data] of this.tabContainers) {
      if (data.tabs[tabIndex]) {
        data.tabs[tabIndex].icon = newIcon;
        break;
      }
    }
  }

  updateTabColor(modalId, tabIndex, newColor) {
    for (let [containerId, data] of this.tabContainers) {
      if (data.tabs[tabIndex]) {
        data.tabs[tabIndex].color = newColor;
        break;
      }
    }
  }

  removeTab(modalId, tabIndex) {
    for (let [containerId, data] of this.tabContainers) {
      if (data.tabs[tabIndex]) {
        if (data.tabs.length <= 1) {
          alert("Cannot remove the last tab. At least one tab is required.");
          return;
        }
        data.tabs.splice(tabIndex, 1);

        // Refresh the tab list in modal
        const tabList = document.getElementById(`${modalId}-tab-list`);
        if (tabList) {
          tabList.innerHTML = this.renderTabList(data.tabs, modalId);
        }
        break;
      }
    }
  }

  saveTabConfiguration(containerId, modalId) {
    const data = this.tabContainers.get(containerId);
    if (!data) return;

    // Refresh the tab container display
    this.refreshTabContainer(containerId);

    // Close the modal
    const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
    if (modal) {
      modal.hide();
    }

    // Show success message
    this.showToast("Tab configuration saved successfully!", "success");
  }

  assignWidgetToTab(containerId, widgetId, tabId, modalId) {
    console.log("Assigning widget to tab:", { containerId, widgetId, tabId });

    const data = this.tabContainers.get(containerId);
    if (!data) return;

    // Remove widget from all tabs first
    data.tabs.forEach((tab) => {
      if (tab.widgets) {
        const index = tab.widgets.indexOf(widgetId);
        if (index > -1) {
          tab.widgets.splice(index, 1);
        }
      }
    });

    // Add widget to the selected tab
    const targetTab = data.tabs.find((tab) => tab.id === tabId);
    if (targetTab) {
      if (!targetTab.widgets) {
        targetTab.widgets = [];
      }
      targetTab.widgets.push(widgetId);

      // Update the tab containers map
      this.tabContainers.set(containerId, data);

      // Refresh the assignment grid to show updated counts
      this.refreshWidgetAssignmentGrid(modalId, containerId, data);

      this.showToast(`Widget assigned to ${targetTab.name}`, "success");
    }
  }

  clearAllAssignments(containerId, modalId) {
    const data = this.tabContainers.get(containerId);
    if (!data) return;

    // Clear all widget assignments
    data.tabs.forEach((tab) => {
      tab.widgets = [];
    });

    // Update the tab containers map
    this.tabContainers.set(containerId, data);

    // Refresh the assignment grid
    this.refreshWidgetAssignmentGrid(modalId, containerId, data);

    // Uncheck all radio buttons
    const radioButtons = document.querySelectorAll(
      `#${modalId}-widget-grid input[type="radio"]`
    );
    radioButtons.forEach((radio) => {
      radio.checked = false;
    });

    this.showToast("All widget assignments cleared", "info");
  }

  refreshWidgetAssignmentGrid(modalId, containerId, data) {
    const gridContainer = document.getElementById(`${modalId}-widget-grid`);
    if (gridContainer) {
      gridContainer.innerHTML = this.renderWidgetAssignmentGrid(
        data,
        modalId,
        containerId
      );
    }
  }

  getRandomTabColor() {
    const colors = [
      "#3b82f6",
      "#10b981",
      "#f59e0b",
      "#ef4444",
      "#8b5cf6",
      "#06b6d4",
      "#84cc16",
      "#f97316",
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  showToast(message, type = "info") {
    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector(".toast-container");
    if (!toastContainer) {
      toastContainer = document.createElement("div");
      toastContainer.className = "toast-container";
      document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement("div");
    toast.className = `canvas-toast ${type}`;
    toast.style.cssText = `
      padding: 12px 16px;
      border-radius: 8px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      font-size: 14px;
      animation: slideInRight 0.3s ease;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    `;

    // Add icon based on type
    let icon = "las la-info-circle";
    if (type === "success") icon = "las la-check-circle";
    if (type === "error") icon = "las la-exclamation-triangle";

    toast.innerHTML = `
      <i class="${icon}"></i>
      <span>${message}</span>
    `;

    // Add to container
    toastContainer.appendChild(toast);

    // Auto remove after 4 seconds
    setTimeout(() => {
      toast.style.animation = "slideOutRight 0.3s ease";
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 4000);
  }

  // Widget management methods
  editWidget(widgetId) {
    console.log("Edit widget:", widgetId);

    const widgetData = this.widgets[widgetId];
    if (!widgetData) {
      console.error("Widget not found:", widgetId);
      return;
    }

    this.showWidgetEditModal(widgetId, widgetData);
  }

  showWidgetEditModal(widgetId, widgetData) {
    const modalId = `editModal_${widgetId}`;

    // Remove existing modal if any
    const existingModal = document.getElementById(modalId);
    if (existingModal) {
      existingModal.remove();
    }

    const modal = document.createElement("div");
    modal.id = modalId;
    modal.className = "modal fade";
    modal.style.zIndex = "15000";
    modal.innerHTML = `
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="${widgetData.icon}"></i> Edit ${widgetData.title}
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            ${this.generateEditForm(widgetId, widgetData)}
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="window.canvasComposer.saveWidgetChanges('${widgetId}', '${modalId}')">
              <i class="las la-save"></i> Save Changes
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Initialize Bootstrap modal
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Clean up modal when hidden
    modal.addEventListener("hidden.bs.modal", () => {
      modal.remove();
    });
  }

  generateEditForm(widgetId, widgetData) {
    let formHTML = `
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Widget Title</label>
            <input type="text" class="form-control" id="edit_title_${widgetId}" value="${widgetData.title}">
          </div>
        </div>
        <div class="col-md-3">
          <div class="mb-3">
            <label class="form-label">Width</label>
            <input type="number" class="form-control" id="edit_width_${widgetId}" value="${widgetData.width}" min="1" max="12">
          </div>
        </div>
        <div class="col-md-3">
          <div class="mb-3">
            <label class="form-label">Height</label>
            <input type="number" class="form-control" id="edit_height_${widgetId}" value="${widgetData.height}" min="1" max="12">
          </div>
        </div>
      </div>
    `;

    // Add widget-specific fields based on type
    switch (widgetData.type) {
      case "kpi":
        formHTML += `
          <div class="row">
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label">KPI Value</label>
                <input type="text" class="form-control" id="edit_kpi_value_${widgetId}" value="${
          widgetData.data.value || ""
        }">
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label">KPI Label</label>
                <input type="text" class="form-control" id="edit_kpi_label_${widgetId}" value="${
          widgetData.data.label || ""
        }">
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label">Trend</label>
                <input type="text" class="form-control" id="edit_kpi_trend_${widgetId}" value="${
          widgetData.data.trend || ""
        }">
              </div>
            </div>
          </div>
        `;
        break;

      case "text":
        formHTML += `
          <div class="mb-3">
            <label class="form-label">Text Content</label>
            <textarea class="form-control" id="edit_text_content_${widgetId}" rows="4">${
          widgetData.data.content || ""
        }</textarea>
          </div>
        `;
        break;

      case "image":
        formHTML += `
          <div class="row">
            <div class="col-md-8">
              <div class="mb-3">
                <label class="form-label">Image URL</label>
                <input type="url" class="form-control" id="edit_image_src_${widgetId}" value="${
          widgetData.data.src || ""
        }" placeholder="https://example.com/image.jpg">
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label">Alt Text</label>
                <input type="text" class="form-control" id="edit_image_alt_${widgetId}" value="${
          widgetData.data.alt || ""
        }">
              </div>
            </div>
          </div>
        `;
        break;

      case "video":
        formHTML += `
          <div class="row">
            <div class="col-md-8">
              <div class="mb-3">
                <label class="form-label">Video URL</label>
                <input type="url" class="form-control" id="edit_video_src_${widgetId}" value="${
          widgetData.data.src || ""
        }" placeholder="https://example.com/video.mp4">
              </div>
            </div>
            <div class="col-md-4">
              <div class="mb-3">
                <label class="form-label">Video Title</label>
                <input type="text" class="form-control" id="edit_video_title_${widgetId}" value="${
          widgetData.data.title || ""
        }">
              </div>
            </div>
          </div>
        `;
        break;

      case "tabs":
        formHTML += `
          <div class="mb-3">
            <label class="form-label">Tab Configuration</label>
            <div class="alert alert-info">
              <i class="las la-info-circle"></i>
              Use the tab configuration button in the widget to modify tabs and assign widgets.
            </div>
          </div>
        `;
        break;

      default:
        formHTML += `
          <div class="alert alert-info">
            <i class="las la-chart-bar"></i>
            This is a ${widgetData.type} widget. Chart-specific settings will be available in future updates.
          </div>
        `;
        break;
    }

    return formHTML;
  }

  saveWidgetChanges(widgetId, modalId) {
    const widgetData = this.widgets[widgetId];
    if (!widgetData) {
      console.error("Widget not found:", widgetId);
      return;
    }

    // Update basic properties
    const newTitle = document.getElementById(`edit_title_${widgetId}`).value;
    const newWidth = parseInt(
      document.getElementById(`edit_width_${widgetId}`).value
    );
    const newHeight = parseInt(
      document.getElementById(`edit_height_${widgetId}`).value
    );

    // Update widget data
    widgetData.title = newTitle;
    widgetData.width = newWidth;
    widgetData.height = newHeight;

    // Update widget-specific data
    switch (widgetData.type) {
      case "kpi":
        const kpiValue = document.getElementById(
          `edit_kpi_value_${widgetId}`
        )?.value;
        const kpiLabel = document.getElementById(
          `edit_kpi_label_${widgetId}`
        )?.value;
        const kpiTrend = document.getElementById(
          `edit_kpi_trend_${widgetId}`
        )?.value;

        widgetData.data = {
          ...widgetData.data,
          value: kpiValue,
          label: kpiLabel,
          trend: kpiTrend,
        };
        break;

      case "text":
        const textContent = document.getElementById(
          `edit_text_content_${widgetId}`
        )?.value;
        widgetData.data = {
          ...widgetData.data,
          content: textContent,
        };
        break;

      case "image":
        const imageSrc = document.getElementById(
          `edit_image_src_${widgetId}`
        )?.value;
        const imageAlt = document.getElementById(
          `edit_image_alt_${widgetId}`
        )?.value;
        widgetData.data = {
          ...widgetData.data,
          src: imageSrc,
          alt: imageAlt,
        };
        break;

      case "video":
        const videoSrc = document.getElementById(
          `edit_video_src_${widgetId}`
        )?.value;
        const videoTitle = document.getElementById(
          `edit_video_title_${widgetId}`
        )?.value;
        widgetData.data = {
          ...widgetData.data,
          src: videoSrc,
          title: videoTitle,
        };
        break;
    }

    // Update the DOM
    this.updateWidgetInDOM(widgetId, widgetData);

    // Close modal
    const modal = document.getElementById(modalId);
    const bsModal = bootstrap.Modal.getInstance(modal);
    if (bsModal) {
      bsModal.hide();
    }

    // Show success message
    this.showToast(`Widget "${newTitle}" updated successfully!`, "success");
  }

  updateWidgetInDOM(widgetId, widgetData) {
    const gridItem = document.querySelector(`[data-widget-id="${widgetId}"]`);
    if (!gridItem) {
      console.error("Grid item not found for widget:", widgetId);
      return;
    }

    // Update GridStack item size
    this.gridStack.update(gridItem, {
      w: widgetData.width,
      h: widgetData.height,
    });

    // Update widget title in header
    const titleElement = gridItem.querySelector(".widget-title");
    if (titleElement) {
      titleElement.innerHTML = `<i class="${widgetData.icon}"></i> ${widgetData.title}`;
    }

    // Update widget content based on type
    const contentContainer = gridItem.querySelector(".widget-content");
    if (contentContainer) {
      switch (widgetData.type) {
        case "kpi":
          this.createKPIWidget(contentContainer, widgetData);
          break;
        case "text":
          this.createTextWidget(contentContainer, widgetData);
          break;
        case "image":
          this.createImageWidget(contentContainer, widgetData);
          break;
        case "video":
          this.createVideoWidget(contentContainer, widgetData);
          break;
        // For chart widgets, we might need to recreate them
        default:
          // Re-initialize the widget content
          setTimeout(() => {
            this.initializeWidgetContent(
              widgetId,
              gridItem.querySelector(".grid-stack-item-content")
            );
          }, 100);
          break;
      }
    }
  }

  removeWidget(widgetId) {
    const gridItem = document.querySelector(`[data-widget-id="${widgetId}"]`);
    if (gridItem) {
      this.gridStack.removeWidget(gridItem);
      this.cleanupWidget(widgetId);
    } else {
      console.error("Widget not found for deletion:", widgetId);
    }
  }

  cleanupWidget(widgetId) {
    // Cleanup chart instances
    const chartInstance = this.chartInstances.get(widgetId);
    if (chartInstance) {
      if (typeof chartInstance.dispose === "function") {
        chartInstance.dispose();
      } else if (typeof chartInstance.destroy === "function") {
        chartInstance.destroy();
      }
      this.chartInstances.delete(widgetId);
    }

    // Remove from widgets map
    delete this.widgets[widgetId];
  }

  switchTab(tabId) {
    console.log("Switch to tab:", tabId);
    // Implement tab switching logic
  }

  // Create dashboard section from current canvas
  createSection() {
    // Prevent multiple simultaneous calls
    if (this.creatingSection) {
      console.log("Section creation already in progress...");
      return;
    }

    this.creatingSection = true;

    if (Object.keys(this.widgets).length === 0) {
      alert("Please add some widgets before creating a section.");
      this.creatingSection = false;
      return;
    }

    // Set global flag to prevent preview mode interference
    window.canvasOperationInProgress = true;

    const sectionTitle = prompt("Enter section title:") || "Canvas Section";

    // Check if main grid exists
    if (!window.grid) {
      alert(
        "Main dashboard grid not found. Please make sure you are on the main dashboard."
      );
      window.canvasOperationInProgress = false;
      this.creatingSection = false;
      return;
    }

    // Create section data
    const sectionData = {
      id: `canvas_section_${Date.now()}`,
      title: sectionTitle,
      widgets: Object.values(this.widgets),
    };

    // Create the section container on the main dashboard
    this.createSectionOnMainDashboard(sectionData);

    // Close the canvas composer
    this.close();

    // Clear the flags after a delay to ensure section creation is complete
    setTimeout(() => {
      window.canvasOperationInProgress = false;
      this.creatingSection = false;
      // Double-check widget section visibility
      this.ensureWidgetSectionVisible();
    }, 2000);
  }

  // Create section container on the main dashboard
  createSectionOnMainDashboard(sectionData) {
    if (!window.grid) {
      console.error("Main grid not available");
      return;
    }

    // Temporarily disable preview functionality to prevent interference
    const originalPreviewState = this.disablePreviewTemporarily();

    // Calculate section dimensions based on widgets
    const maxX = Math.max(...sectionData.widgets.map((w) => w.x + w.width), 12);
    const maxY = Math.max(...sectionData.widgets.map((w) => w.y + w.height), 8);

    const sectionWidth = Math.min(maxX, 12);
    const sectionHeight = Math.min(maxY + 2, 12); // +2 for header

    // Create the section container widget
    const widget = window.grid.addWidget({
      x: 0,
      y: 0,
      w: sectionWidth,
      h: sectionHeight,
      content: `
        <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;" data-section-id="${sectionData.id}">
          <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
            <div>
              ${sectionData.title}
            </div>
            <div>
              <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="nested-grid-container" style="height: calc(100% - 40px); overflow: hidden;"></div>
        </div>
      `,
    });

    // Initialize nested grid and add widgets
    setTimeout(() => {
      this.initializeNestedGridWithWidgets(widget, sectionData);

      // Restore preview functionality and ensure widget section is visible
      this.restorePreviewFunctionality(originalPreviewState);

      // Force widget section to be visible
      this.ensureWidgetSectionVisible();
    }, 100);

    // Save section data for future reference
    const sections = JSON.parse(localStorage.getItem("canvasSections") || "[]");
    sections.push(sectionData);
    localStorage.setItem("canvasSections", JSON.stringify(sections));

    alert(`Section "${sectionData.title}" created successfully!`);
  }

  // Temporarily disable preview functionality
  disablePreviewTemporarily() {
    const previewBtn = document.getElementById("preview-btn");
    const originalState = {
      previewDisabled: false,
      previewBtnDisabled: false,
    };

    if (previewBtn) {
      originalState.previewBtnDisabled = previewBtn.disabled;
      previewBtn.disabled = true;
      originalState.previewDisabled = true;
    }

    return originalState;
  }

  // Restore preview functionality
  restorePreviewFunctionality(originalState) {
    if (originalState.previewDisabled) {
      const previewBtn = document.getElementById("preview-btn");
      if (previewBtn) {
        previewBtn.disabled = originalState.previewBtnDisabled;
      }
    }
  }

  // Ensure widget section remains visible
  ensureWidgetSectionVisible() {
    const widgetSection = document.querySelector(".widget-section");
    if (widgetSection && widgetSection.style.display === "none") {
      widgetSection.style.display = "block";
      console.log("Widget section visibility restored");
    }
  }

  // Initialize nested grid and populate with widgets
  initializeNestedGridWithWidgets(sectionWidget, sectionData) {
    const nestedContainer = sectionWidget.querySelector(
      ".nested-grid-container"
    );
    if (!nestedContainer) {
      console.error("Nested grid container not found");
      return;
    }

    // Initialize nested GridStack
    const nestedGrid = GridStack.init(
      {
        column: 12,
        cellHeight: 60,
        margin: 5,
        resizable: { handles: "e, se, s, sw, w" },
        draggable: { handle: ".widget-header" },
      },
      nestedContainer
    );

    if (!nestedGrid) {
      console.error("Failed to initialize nested grid");
      return;
    }

    // Add widgets to nested grid
    sectionData.widgets.forEach((widgetData) => {
      try {
        // Check if this is a tab container widget
        if (widgetData.type === "tab-container") {
          this.createTabContainerInSection(nestedGrid, widgetData);
        } else {
          this.createRegularWidgetInSection(nestedGrid, widgetData);
        }
      } catch (error) {
        console.error(`Error adding widget ${widgetData.id}:`, error);
      }
    });

    console.log(
      `Nested grid initialized with ${sectionData.widgets.length} widgets`
    );
  }

  createTabContainerInSection(nestedGrid, widgetData) {
    // Get tab container configuration
    const tabContainerData = this.tabContainers.get(widgetData.id);

    if (!tabContainerData) {
      console.warn(`No tab container data found for widget ${widgetData.id}`);
      return;
    }

    // Create the tab container widget in the section
    const widgetElement = nestedGrid.addWidget({
      x: widgetData.x,
      y: widgetData.y,
      w: widgetData.width,
      h: widgetData.height,
      content: `
        <div class="section-tab-container" data-widget-id="${widgetData.id}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-layer-group"></i>
              <span>Tab Container</span>
            </div>
          </div>
          <div class="widget-content" style="height: calc(100% - 40px);">
            <div class="tab-navigation-section" style="display: flex; background: #f8fafc; border-bottom: 1px solid #e2e8f0; margin-bottom: 8px;">
              ${this.renderSectionTabNavigation(
                tabContainerData.tabs || [],
                tabContainerData.activeTab || "tab1",
                widgetData.id
              )}
            </div>
            <div class="tab-widgets-area" id="tab-widgets-${
              widgetData.id
            }" style="position: relative; height: calc(100% - 50px);">
              <!-- Tab-specific widgets will be rendered here -->
            </div>
          </div>
        </div>
      `,
    });

    // Create widgets for each tab - Fix: pass the complete widget data from the section
    setTimeout(() => {
      if (tabContainerData && tabContainerData.tabs) {
        // Get all widgets from the section data
        const allSectionWidgets = Object.values(this.widgets);
        const sectionDataForTabs = {
          widgets: allSectionWidgets,
        };

        console.log(
          "Creating tab-specific widgets for container:",
          widgetData.id
        );
        console.log(
          "Available widgets:",
          allSectionWidgets.map((w) => w.id)
        );
        console.log("Tab container data:", tabContainerData);

        this.createTabSpecificWidgets(
          widgetData.id,
          tabContainerData,
          sectionDataForTabs
        );
      }
    }, 100);
  }

  createRegularWidgetInSection(nestedGrid, widgetData) {
    // Create chart instance based on type
    let chartContent = "";
    switch (widgetData.type) {
      case "pie-chart":
        chartContent = `<div class="chart-container" id="chart_${widgetData.id}" style="height: 100%;"></div>`;
        break;
      case "bar-chart":
        chartContent = `<div class="chart-container" id="chart_${widgetData.id}" style="height: 100%;"></div>`;
        break;
      case "line-chart":
        chartContent = `<div class="chart-container" id="chart_${widgetData.id}" style="height: 100%;"></div>`;
        break;
      case "kpi":
        chartContent = `
          <div class="kpi-widget" style="height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center; text-align: center;">
            <div class="kpi-value" style="font-size: 2rem; font-weight: bold; color: #3b82f6;">${
              widgetData.value || "42"
            }</div>
            <div class="kpi-label" style="font-size: 0.9rem; color: #64748b;">${
              widgetData.label || "KPI Value"
            }</div>
          </div>
        `;
        break;
      case "text":
        chartContent = `
          <div class="text-widget" style="height: 100%; padding: 1rem; overflow-y: auto;">
            <p style="margin: 0; color: #374151;">${
              widgetData.content || "Sample text content"
            }</p>
          </div>
        `;
        break;
      default:
        chartContent = `<div style="height: 100%; display: flex; align-items: center; justify-content: center; color: #64748b;">
          <i class="las la-chart-bar" style="font-size: 3rem; margin-bottom: 1rem;"></i>
          <p>${widgetData.type} Widget</p>
        </div>`;
    }

    const widgetElement = nestedGrid.addWidget({
      x: widgetData.x,
      y: widgetData.y,
      w: widgetData.width,
      h: widgetData.height,
      content: `
        <div class="nested-widget" data-widget-id="${
          widgetData.id
        }" data-widget-type="${widgetData.type}">
          <div class="widget-header">
            <div class="widget-title">
              <i class="las la-chart-bar"></i>
              <span>${widgetData.type.replace("-", " ").toUpperCase()}</span>
            </div>
          </div>
          <div class="widget-content" style="height: calc(100% - 40px);">
            ${chartContent}
          </div>
        </div>
      `,
    });

    // Initialize the actual chart after DOM is ready
    setTimeout(() => {
      this.initializeWidgetContent(widgetData.id, widgetElement);
    }, 100);
  }

  renderSectionTabNavigation(tabs, activeTab, containerId) {
    return tabs
      .map(
        (tab) => `
      <button class="section-tab-btn ${tab.id === activeTab ? "active" : ""}" 
              onclick="canvasComposer.switchSectionTab('${containerId}', '${
          tab.id
        }')"
              style="padding: 8px 12px; border: none; background: ${
                tab.id === activeTab ? "#ffffff" : "transparent"
              }; 
                     color: ${
                       tab.id === activeTab ? tab.color : "#64748b"
                     }; cursor: pointer; 
                     font-size: 12px; border-radius: 4px; margin-right: 2px;">
        <i class="${tab.icon}" style="margin-right: 4px; font-size: 10px;"></i>
        ${tab.name}
      </button>
    `
      )
      .join("");
  }

  createTabSpecificWidgets(containerId, tabContainerData, sectionData) {
    const tabWidgetsArea = document.getElementById(
      `tab-widgets-${containerId}`
    );
    if (!tabWidgetsArea) return;

    // Create widget areas for each tab
    tabContainerData.tabs.forEach((tab) => {
      const tabWidgetArea = document.createElement("div");
      tabWidgetArea.className = "tab-specific-widgets";
      tabWidgetArea.id = `tab-widgets-${containerId}-${tab.id}`;
      tabWidgetArea.style.cssText = `
        position: absolute; 
        top: 0; left: 0; right: 0; bottom: 0; 
        display: ${tab.id === tabContainerData.activeTab ? "block" : "none"};
      `;

      // Add widgets assigned to this tab - Fix: properly filter assigned widgets
      const tabWidgets = sectionData.widgets.filter(
        (widget) => tab.widgets && tab.widgets.includes(widget.id)
      );

      console.log(
        `Tab ${tab.name} has ${tabWidgets.length} assigned widgets:`,
        tabWidgets.map((w) => w.id)
      );

      if (tabWidgets.length > 0) {
        tabWidgetArea.innerHTML = `
          <div class="tab-widget-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 8px; height: 100%; padding: 8px;">
            ${tabWidgets
              .map(
                (widget) => `
              <div class="tab-widget-item" style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 6px; padding: 12px;">
                <div style="font-size: 10px; color: ${
                  tab.color
                }; margin-bottom: 8px; font-weight: 600;">
                  <i class="${tab.icon}"></i> ${tab.name}
                </div>
                <div style="font-size: 14px; font-weight: 500; color: #374151; margin-bottom: 4px;">
                  <i class="${widget.icon || "las la-chart-bar"}"></i> ${
                  widget.title || widget.type.toUpperCase()
                }
                </div>
                <div style="font-size: 11px; color: #6b7280;">
                  ${widget.type.replace("-", " ")} Widget
                </div>
                <div class="mini-widget-preview" style="margin-top: 8px; height: 60px; background: #f3f4f6; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
                  <i class="${
                    widget.icon || "las la-chart-bar"
                  }" style="font-size: 24px; color: ${
                  tab.color
                }; opacity: 0.7;"></i>
                </div>
              </div>
            `
              )
              .join("")}
          </div>
        `;
      } else {
        tabWidgetArea.innerHTML = `
          <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #9ca3af; font-size: 12px;">
            <div style="text-align: center;">
              <i class="${tab.icon}" style="font-size: 32px; margin-bottom: 12px; opacity: 0.5; color: ${tab.color};"></i>
              <p style="margin: 0; font-weight: 500;">No widgets assigned to ${tab.name}</p>
              <p style="margin: 4px 0 0 0; font-size: 11px; opacity: 0.7;">Configure the tab container to assign widgets</p>
            </div>
          </div>
        `;
      }

      tabWidgetsArea.appendChild(tabWidgetArea);
    });
  }

  switchSectionTab(containerId, tabId) {
    // Update tab navigation
    const container = document.querySelector(
      `[data-widget-id="${containerId}"]`
    );
    if (!container) return;

    const tabButtons = container.querySelectorAll(".section-tab-btn");
    tabButtons.forEach((btn) => {
      if (btn.textContent.trim().includes(tabId)) {
        btn.classList.add("active");
      } else {
        btn.classList.remove("active");
      }
    });

    // Show/hide tab widget areas
    const tabWidgetAreas = container.querySelectorAll(".tab-specific-widgets");
    tabWidgetAreas.forEach((area) => {
      if (area.id.includes(tabId)) {
        area.style.display = "block";
      } else {
        area.style.display = "none";
      }
    });
  }

  // Save canvas as template
  saveAsTemplate() {
    if (Object.keys(this.widgets).length === 0) {
      alert("Please add some widgets before saving as template.");
      return;
    }

    const templateName = prompt("Enter template name:") || "New Template";
    const templateData = {
      id: `template_${Date.now()}`,
      name: templateName,
      widgets: Object.values(this.widgets),
      createdAt: new Date().toISOString(),
    };

    // Save to localStorage for now
    const templates = JSON.parse(
      localStorage.getItem("canvasTemplates") || "[]"
    );
    templates.push(templateData);
    localStorage.setItem("canvasTemplates", JSON.stringify(templates));

    alert(`Template "${templateName}" saved successfully!`);
    console.log("Template saved:", templateData);
  }

  // Close canvas composer
  close() {
    // Cleanup all chart instances
    this.chartInstances.forEach((chart) => {
      if (typeof chart.dispose === "function") {
        chart.dispose();
      } else if (typeof chart.destroy === "function") {
        chart.destroy();
      }
    });
    this.chartInstances.clear();

    // Hide overlay
    const overlay = document.querySelector(".canvas-composer-overlay");
    if (overlay) {
      overlay.style.display = "none";
    }
  }

  // Show canvas composer
  show() {
    const overlay = document.querySelector(".canvas-composer-overlay");
    if (overlay) {
      overlay.style.display = "flex";
      // Initialize GridStack after showing the overlay
      setTimeout(() => {
        this.initGridStack();
      }, 100);
    }
  }

  createSectionContainer(container, data) {
    container.innerHTML = `
      <div class="section-container" style="height: 100%; display: flex; flex-direction: column; background: #f8fafc; border-radius: 8px; padding: 16px;">
        <div class="section-header" style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px; padding-bottom: 12px; border-bottom: 2px solid #e2e8f0;">
          <i class="las la-layer-group" style="font-size: 20px; color: #667eea;"></i>
          <h5 style="margin: 0; color: #374151; font-weight: 600;">Section Container</h5>
        </div>
        <div class="section-content" style="flex: 1; background: white; border-radius: 6px; padding: 20px; border: 2px dashed #cbd5e0; display: flex; align-items: center; justify-content: center;">
          <div style="text-align: center; color: #6b7280;">
            <i class="las la-plus-circle" style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;"></i>
            <h4 style="margin-bottom: 8px;">Section Container</h4>
            <p style="margin: 0; font-size: 14px;">Drop widgets here to organize content in sections</p>
          </div>
        </div>
      </div>
    `;
  }

  // New Drag & Drop Widget Assignment Methods
  handleWidgetDragStart(event, widgetId) {
    event.dataTransfer.setData("text/plain", widgetId);
    event.dataTransfer.effectAllowed = "move";

    // Add visual feedback
    const draggedCard = event.target;
    draggedCard.classList.add("dragging");

    // Highlight compatible drop zones
    document.querySelectorAll(".tab-drop-zone").forEach((zone) => {
      zone.classList.add("drag-active");
    });
  }

  handleDragOver(event) {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }

  handleDragEnter(event) {
    event.preventDefault();
    const dropZone = event.currentTarget;
    dropZone.classList.add("drag-hover");
  }

  handleDragLeave(event) {
    const dropZone = event.currentTarget;
    // Only remove hover if we're actually leaving the drop zone
    if (!dropZone.contains(event.relatedTarget)) {
      dropZone.classList.remove("drag-hover");
    }
  }

  handleWidgetDrop(event, tabId, containerId, modalId) {
    event.preventDefault();
    const widgetId = event.dataTransfer.getData("text/plain");

    // Clean up visual feedback
    document
      .querySelectorAll(".dragging")
      .forEach((el) => el.classList.remove("dragging"));
    document
      .querySelectorAll(".drag-active")
      .forEach((el) => el.classList.remove("drag-active"));
    document
      .querySelectorAll(".drag-hover")
      .forEach((el) => el.classList.remove("drag-hover"));

    // Assign widget to tab
    this.assignWidgetToTab(containerId, widgetId, tabId, modalId);
  }

  autoAssignWidgets(containerId, modalId) {
    const data = this.tabContainers.get(containerId);
    if (!data) return;

    const widgets = Object.values(this.widgets);
    const unassignedWidgets = widgets.filter(
      (w) => !this.getWidgetAssignedTab(w.id, data)
    );

    if (unassignedWidgets.length === 0) {
      this.showToast("All widgets are already assigned!", "info");
      return;
    }

    // Smart assignment logic
    unassignedWidgets.forEach((widget) => {
      const suggestedTab = this.suggestTabForWidget(widget, data.tabs);
      if (suggestedTab) {
        if (!suggestedTab.widgets) suggestedTab.widgets = [];
        suggestedTab.widgets.push(widget.id);
      }
    });

    // Update the tab containers map
    this.tabContainers.set(containerId, data);

    // Refresh the assignment grid
    this.refreshWidgetAssignmentGrid(modalId, containerId, data);

    this.showToast(
      `Auto-assigned ${unassignedWidgets.length} widgets!`,
      "success"
    );
  }

  suggestTabForWidget(widget, tabs) {
    const type = widget.type.toLowerCase();

    // Find best matching tab based on name and widget type
    const suggestions = {
      "pie-chart": ["analytics", "chart", "report", "dashboard"],
      "bar-chart": ["analytics", "chart", "report", "dashboard"],
      "line-chart": ["analytics", "chart", "report", "dashboard"],
      kpi: ["dashboard", "overview", "metric", "kpi"],
      "data-table": ["data", "table", "detail", "list"],
      text: ["content", "info", "about", "detail"],
      image: ["media", "gallery", "content", "resource"],
      video: ["media", "gallery", "content", "resource"],
    };

    const keywords = suggestions[type] || ["general", "misc", "other"];

    // Score tabs based on name similarity
    let bestTab = null;
    let bestScore = 0;

    tabs.forEach((tab) => {
      const tabName = tab.name.toLowerCase();
      let score = 0;

      keywords.forEach((keyword) => {
        if (tabName.includes(keyword)) {
          score += 2;
        }
      });

      // Prefer tabs with fewer widgets (load balancing)
      const widgetCount = tab.widgets ? tab.widgets.length : 0;
      score += Math.max(0, 5 - widgetCount);

      if (score > bestScore) {
        bestScore = score;
        bestTab = tab;
      }
    });

    return bestTab || tabs[0]; // Fallback to first tab
  }

  filterWidgets(filterType, modalId) {
    const poolContent = document.getElementById(`widgetPool-${modalId}`);
    if (!poolContent) return;

    const allCards = poolContent.querySelectorAll(".draggable-widget-card");

    allCards.forEach((card) => {
      const isAssigned = card.classList.contains("assigned");
      let shouldShow = true;

      switch (filterType) {
        case "assigned":
          shouldShow = isAssigned;
          break;
        case "unassigned":
          shouldShow = !isAssigned;
          break;
        case "all":
        default:
          shouldShow = true;
          break;
      }

      card.style.display = shouldShow ? "block" : "none";
    });
  }

  showQuickAssignMenu(event, widgetId, containerId, modalId) {
    event.preventDefault();
    event.stopPropagation();

    const data = this.tabContainers.get(containerId);
    if (!data) return;

    // Create context menu
    const menu = document.createElement("div");
    menu.className = "quick-assign-menu";
    menu.innerHTML = `
      <div class="menu-header">
        <i class="las la-magic"></i>
        <span>Quick Assign</span>
      </div>
      ${data.tabs
        .map(
          (tab) => `
        <div class="menu-item" onclick="window.canvasComposer.assignWidgetToTab('${containerId}', '${widgetId}', '${
            tab.id
          }', '${modalId}'); window.canvasComposer.closeQuickAssignMenu();">
          <i class="${tab.icon}" style="color: ${tab.color}"></i>
          <span>${tab.name}</span>
          <div class="widget-count">${
            tab.widgets ? tab.widgets.length : 0
          }</div>
        </div>
      `
        )
        .join("")}
    `;

    // Position menu
    menu.style.position = "fixed";
    menu.style.left = event.clientX + "px";
    menu.style.top = event.clientY + "px";
    menu.style.zIndex = "10000";

    document.body.appendChild(menu);

    // Close menu on outside click
    setTimeout(() => {
      document.addEventListener("click", this.closeQuickAssignMenu.bind(this), {
        once: true,
      });
    }, 100);
  }

  closeQuickAssignMenu() {
    const menu = document.querySelector(".quick-assign-menu");
    if (menu) {
      menu.remove();
    }
  }

  showWidgetContextMenu(event, widgetId, containerId, modalId) {
    event.preventDefault();
    const widget = this.widgets[widgetId];
    if (!widget) return;

    const data = this.tabContainers.get(containerId);
    const assignedTab = this.getWidgetAssignedTab(widgetId, data);

    // Create context menu
    const menu = document.createElement("div");
    menu.className = "widget-context-menu";
    menu.innerHTML = `
      <div class="menu-header">
        <i class="${widget.icon}"></i>
        <span>${widget.title}</span>
      </div>
      <div class="menu-divider"></div>
      ${
        assignedTab
          ? `
        <div class="menu-item" onclick="window.canvasComposer.unassignWidget('${containerId}', '${widgetId}', '${modalId}'); window.canvasComposer.closeContextMenu();">
          <i class="las la-unlink"></i>
          <span>Unassign from ${assignedTab.name}</span>
        </div>
      `
          : `
        <div class="menu-item" onclick="window.canvasComposer.showQuickAssignMenu(event, '${widgetId}', '${containerId}', '${modalId}'); window.canvasComposer.closeContextMenu();">
          <i class="las la-plus"></i>
          <span>Assign to Tab</span>
        </div>
      `
      }
      <div class="menu-item" onclick="window.canvasComposer.editWidget('${widgetId}'); window.canvasComposer.closeContextMenu();">
        <i class="las la-edit"></i>
        <span>Edit Widget</span>
      </div>
      <div class="menu-divider"></div>
      <div class="menu-item danger" onclick="window.canvasComposer.removeWidget('${widgetId}'); window.canvasComposer.closeContextMenu();">
        <i class="las la-trash"></i>
        <span>Delete Widget</span>
      </div>
    `;

    // Position menu
    menu.style.position = "fixed";
    menu.style.left = event.clientX + "px";
    menu.style.top = event.clientY + "px";
    menu.style.zIndex = "10000";

    document.body.appendChild(menu);

    // Close menu on outside click
    setTimeout(() => {
      document.addEventListener("click", this.closeContextMenu.bind(this), {
        once: true,
      });
    }, 100);
  }

  closeContextMenu() {
    const menu = document.querySelector(".widget-context-menu");
    if (menu) {
      menu.remove();
    }
  }

  togglePoolView(viewType) {
    const pools = document.querySelectorAll(".widget-cards-pool");
    pools.forEach((pool) => {
      pool.className = `widget-cards-pool ${viewType}-view`;
    });
  }

  previewTab(tabId, containerId) {
    // Highlight the tab in the preview
    const previewItems = document.querySelectorAll(".preview-tab-item");
    previewItems.forEach((item) => item.classList.remove("active"));

    const targetPreview = document.querySelector(
      `[data-tab-preview="${tabId}"]`
    );
    if (targetPreview) {
      targetPreview.classList.add("active");
    }

    this.showToast(`Previewing tab content`, "info");
  }

  autoAssignAllWidgets(containerId, modalId) {
    const widgets = this.getUnassignedWidgets();
    const containerData = this.tabContainers.get(containerId);

    if (!containerData || !containerData.tabs) return;

    let assignedCount = 0;
    widgets.forEach((widget) => {
      const suggestedTab = this.suggestTabForWidget(widget, containerData.tabs);
      if (suggestedTab) {
        this.assignWidgetToTab(
          containerId,
          widget.id,
          suggestedTab.id,
          modalId
        );
        assignedCount++;
      }
    });

    this.showToast(`Auto-assigned ${assignedCount} widgets`, "success");
  }

  renderDropdownWidgetCards(widgets, containerData, containerId, modalId) {
    const categories = this.categorizeWidgetsForTabs(widgets);
    let html = "";

    Object.entries(categories).forEach(([category, categoryWidgets]) => {
      if (categoryWidgets.length > 0) {
        html += `
          <div class="widget-category-section">
            <div class="category-header">
              <h5><i class="las ${this.getCategoryIcon(
                category
              )}"></i> ${category}</h5>
              <span class="category-count">${
                categoryWidgets.length
              } widgets</span>
            </div>
            <div class="widget-cards-grid">
              ${categoryWidgets
                .map((widget) =>
                  this.renderDropdownWidgetCard(
                    widget,
                    containerData,
                    containerId,
                    modalId
                  )
                )
                .join("")}
            </div>
          </div>
        `;
      }
    });

    return html;
  }

  renderDropdownWidgetCard(widget, containerData, containerId, modalId) {
    const widgetInfo = this.getWidgetTypeInfo(widget.type);
    const assignedTab = this.getWidgetAssignedTab(widget.id, containerData);
    const tabOptions = containerData.tabs
      .map(
        (tab) =>
          `<option value="${tab.id}" ${
            assignedTab === tab.id ? "selected" : ""
          }>${tab.name}</option>`
      )
      .join("");

    return `
      <div class="dropdown-widget-card ${
        assignedTab ? "assigned" : "unassigned"
      }" data-widget-id="${widget.id}">
        <div class="widget-card-header">
          <div class="widget-icon">
            <i class="las ${widgetInfo.icon}"></i>
          </div>
          <div class="widget-info">
            <h6>${widget.title || widgetInfo.name}</h6>
            <p class="widget-type">${widgetInfo.name}</p>
          </div>
          <div class="assignment-status">
            ${
              assignedTab
                ? '<i class="las la-check-circle text-success"></i>'
                : '<i class="las la-clock text-warning"></i>'
            }
          </div>
        </div>
        
        <div class="widget-assignment-control">
          <label class="assignment-label">Assign to Tab:</label>
          <div class="assignment-dropdown-container">
            <select class="assignment-dropdown" 
                    onchange="window.canvasComposer.assignWidgetToTab('${containerId}', '${
      widget.id
    }', this.value, '${modalId}')"
                    data-widget-id="${widget.id}">
              <option value="">Select a tab...</option>
              ${tabOptions}
            </select>
            ${
              assignedTab
                ? `
              <button class="unassign-btn" 
                      onclick="window.canvasComposer.unassignWidget('${containerId}', '${widget.id}', '${modalId}')"
                      title="Remove from tab">
                <i class="las la-times"></i>
              </button>
            `
                : ""
            }
          </div>
        </div>

        <div class="widget-preview-info">
          <div class="widget-features">
            ${widgetInfo.features
              .map((feature) => `<span class="feature-tag">${feature}</span>`)
              .join("")}
          </div>
          ${
            this.getTabSuggestionForWidget(containerData.tabs, widget.type)
              ? `
            <div class="suggestion-hint">
              <i class="las la-lightbulb"></i>
              <span>Suggested: ${this.getTabSuggestionForWidget(
                containerData.tabs,
                widget.type
              )}</span>
            </div>
          `
              : ""
          }
        </div>
      </div>
    `;
  }

  getCategoryIcon(category) {
    const icons = {
      "Charts & Analytics": "la-chart-line",
      "Content & Media": "la-file-alt",
      "Interactive Elements": "la-mouse-pointer",
      "Data & Tables": "la-table",
      Other: "la-puzzle-piece",
    };
    return icons[category] || "la-puzzle-piece";
  }

  getTabSuggestionForWidget(tabs, widgetType) {
    // Simple scoring based on widget type and tab names
    return (
      tabs
        .map((tab) => ({
          tab,
          score: this.calculateTabScore(tab, widgetType),
        }))
        .sort((a, b) => b.score - a.score)[0]?.tab || tabs[0]
    );
  }

  calculateTabScore(tab, widgetType) {
    const suggestions = {
      "pie-chart": ["analytics", "charts", "data", "reports"],
      "bar-chart": ["analytics", "charts", "data", "reports"],
      "line-chart": ["analytics", "charts", "data", "reports"],
      kpi: ["dashboard", "metrics", "kpi", "overview"],
      text: ["content", "notes", "text", "info"],
      image: ["media", "images", "gallery", "content"],
      video: ["media", "videos", "content"],
      "data-table": ["data", "tables", "reports", "details"],
    };

    const keywords = suggestions[widgetType] || [];
    const tabName = tab.name.toLowerCase();

    let score = 0;
    keywords.forEach((keyword) => {
      if (tabName.includes(keyword)) {
        score += 10;
      }
    });

    return score;
  }

  // New tab-centric UX methods
  showTabWidgetSelector(tabId, containerId, event) {
    event.stopPropagation();

    // Close any existing selectors
    this.closeTabWidgetSelector();

    const containerData = this.tabContainers.get(containerId);
    if (!containerData) return;

    const widgets = this.widgets ? Object.values(this.widgets) : [];
    if (widgets.length === 0) {
      this.showToast(
        "Create some widgets first to assign them to tabs",
        "info"
      );
      return;
    }

    // Create widget selector dropdown
    const selectorId = `tab-widget-selector-${Date.now()}`;
    const buttonRect = event.target.getBoundingClientRect();

    // Calculate position to ensure selector stays within viewport
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const selectorWidth = 300;
    const selectorMaxHeight = 400;

    let left = buttonRect.left - 150;
    let top = buttonRect.bottom + 5;

    // Adjust horizontal position if selector would go off-screen
    if (left + selectorWidth > viewportWidth) {
      left = viewportWidth - selectorWidth - 10;
    }
    if (left < 10) {
      left = 10;
    }

    // Adjust vertical position if selector would go off-screen
    if (top + selectorMaxHeight > viewportHeight) {
      top = buttonRect.top - selectorMaxHeight - 5;
    }
    if (top < 10) {
      top = 10;
    }

    const selectorHTML = `
      <div class="tab-widget-selector" id="${selectorId}" 
           style="position: fixed !important; 
                  top: ${top}px; 
                  left: ${left}px; 
                  width: ${selectorWidth}px; 
                  max-height: ${selectorMaxHeight}px; 
                  background: white; 
                  border: 1px solid #e2e8f0; 
                  border-radius: 12px; 
                  box-shadow: 0 20px 40px rgba(0,0,0,0.25); 
                  z-index: 99999999 !important; 
                  overflow: hidden;
                  pointer-events: auto !important;
                  display: block !important;">
        <div class="tab-widget-selector-header" style="padding: 12px 16px; background: #f8fafc; border-bottom: 1px solid #e2e8f0;">
          <div style="display: flex; align-items: center; justify-content: space-between;">
            <div style="display: flex; align-items: center; gap: 8px;">
              <i class="las la-plus-circle" style="color: #3b82f6;"></i>
              <span style="font-weight: 600; font-size: 14px;">Add to ${
                containerData.tabs.find((t) => t.id === tabId)?.name
              }</span>
            </div>
            <button onclick="window.canvasComposer.closeTabWidgetSelector()" 
                    style="border: none; background: none; color: #64748b; cursor: pointer; padding: 4px;">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="tab-widget-selector-search" style="padding: 12px 16px; border-bottom: 1px solid #e2e8f0;">
          <input type="text" placeholder="Search widgets..." 
                 oninput="window.canvasComposer.filterTabWidgetSelector(this.value, '${selectorId}')"
                 style="width: 100%; padding: 8px 12px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 14px;">
        </div>
        <div class="tab-widget-selector-items" style="max-height: 250px; overflow-y: auto;">
          ${this.renderTabWidgetSelectorItems(
            widgets,
            tabId,
            containerId,
            containerData
          )}
        </div>
        <div class="selector-footer" style="padding: 12px 16px; background: #f8fafc; border-top: 1px solid #e2e8f0;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-size: 12px; color: #64748b;">${
              widgets.length
            } widgets available</span>
            <button onclick="window.canvasComposer.showTabManager('${containerId}')" 
                    style="background: none; border: 1px solid #e2e8f0; color: #64748b; padding: 6px 12px; 
                           border-radius: 6px; cursor: pointer; font-size: 12px;">
              <i class="las la-cog"></i> Manage All
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.insertAdjacentHTML("beforeend", selectorHTML);

    // Close selector when clicking outside
    setTimeout(() => {
      document.addEventListener("click", this.handleOutsideClick.bind(this), {
        once: true,
      });
    }, 100);
  }

  renderTabWidgetSelectorItems(widgets, tabId, containerId, containerData) {
    const assignedWidgets = new Set();
    containerData.tabs.forEach((tab) => {
      tab.widgets?.forEach((widgetId) => assignedWidgets.add(widgetId));
    });

    return widgets
      .map((widget) => {
        const isAssigned = assignedWidgets.has(widget.id);
        const typeInfo = this.getWidgetTypeInfo(widget.type);
        const clickHandler = isAssigned
          ? ""
          : `window.canvasComposer.addWidgetToTab('${widget.id}', '${tabId}', '${containerId}')`;

        return `
        <div class="selector-widget-item ${isAssigned ? "assigned" : ""}" 
             onclick="${clickHandler}"
             onmouseover="this.style.backgroundColor='#f8f9ff'"
             onmouseout="this.style.backgroundColor=''"
             style="padding: 8px; border-radius: 6px; cursor: ${
               isAssigned ? "not-allowed" : "pointer"
             }; opacity: ${isAssigned ? "0.6" : "1"};">
          <div style="display: flex; align-items: center; gap: 8px;">
            <i class="${typeInfo.icon}" style="color: ${
          typeInfo.color
        }; font-size: 16px;"></i>
            <div style="flex: 1;">
              <div style="font-weight: 500; font-size: 12px; color: #333;">${
                widget.title
              }</div>
              <div style="font-size: 10px; color: #666; text-transform: uppercase;">${
                widget.type
              }</div>
            </div>
            ${
              isAssigned
                ? '<i class="las la-check" style="color: #10b981;"></i>'
                : ""
            }
          </div>
        </div>
      `;
      })
      .join("");
  }

  addWidgetToTab(widgetId, tabId, containerId) {
    const containerData = this.tabContainers.get(containerId);
    if (!containerData) return;

    // Remove widget from all tabs first
    containerData.tabs.forEach((tab) => {
      if (tab.widgets) {
        const index = tab.widgets.indexOf(widgetId);
        if (index > -1) {
          tab.widgets.splice(index, 1);
        }
      }
    });

    // Add widget to the target tab
    const targetTab = containerData.tabs.find((tab) => tab.id === tabId);
    if (targetTab) {
      if (!targetTab.widgets) {
        targetTab.widgets = [];
      }
      targetTab.widgets.push(widgetId);

      // Update the tab container data
      this.tabContainers.set(containerId, containerData);

      // Refresh the tab container display
      this.refreshTabContainer(containerId);

      this.showToast(`Widget added to ${targetTab.name}`, "success");

      // Close the selector
      this.closeTabWidgetSelector();
    }
  }

  closeTabWidgetSelector() {
    const selector = document.querySelector(".tab-widget-selector");
    if (selector) {
      selector.remove();
    }
  }

  filterTabWidgetSelector(searchTerm, selectorId) {
    const selector = document.getElementById(selectorId);
    if (!selector) return;

    const items = selector.querySelectorAll(".selector-widget-item");
    const term = searchTerm.toLowerCase();

    items.forEach((item) => {
      const title = item
        .querySelector(".widget-info div")
        .textContent.toLowerCase();
      const type = item
        .querySelector(".widget-info div:last-child")
        .textContent.toLowerCase();

      if (title.includes(term) || type.includes(term)) {
        item.style.display = "flex";
      } else {
        item.style.display = "none";
      }
    });
  }

  handleOutsideClick(event) {
    const selector = document.querySelector(".tab-widget-selector");
    if (selector && !selector.contains(event.target)) {
      this.closeTabWidgetSelector();
    }
  }

  showTabManager(containerId) {
    this.closeTabWidgetSelector();
    this.configureTabContainer(containerId);
  }

  getWidgetTypeInfo(type) {
    const typeMap = {
      "pie-chart": {
        icon: "las la-chart-pie",
        name: "Pie Chart",
        color: "#3b82f6",
        gradient: "linear-gradient(135deg, #3b82f6, #1d4ed8)",
        features: ["Interactive", "Data Viz"],
      },
      "bar-chart": {
        icon: "las la-chart-bar",
        name: "Bar Chart",
        color: "#10b981",
        gradient: "linear-gradient(135deg, #10b981, #047857)",
        features: ["Responsive", "Analytics"],
      },
      "line-chart": {
        icon: "las la-chart-line",
        name: "Line Chart",
        color: "#f59e0b",
        gradient: "linear-gradient(135deg, #f59e0b, #d97706)",
        features: ["Time Series", "Trends"],
      },
      kpi: {
        icon: "las la-tachometer-alt",
        name: "KPI Widget",
        color: "#ef4444",
        gradient: "linear-gradient(135deg, #ef4444, #dc2626)",
        features: ["Real-time", "Metrics"],
      },
      "data-table": {
        icon: "las la-table",
        name: "Data Table",
        color: "#8b5cf6",
        gradient: "linear-gradient(135deg, #8b5cf6, #7c3aed)",
        features: ["Sortable", "Filterable"],
      },
      text: {
        icon: "las la-font",
        name: "Text Widget",
        color: "#06b6d4",
        gradient: "linear-gradient(135deg, #06b6d4, #0891b2)",
        features: ["Rich Text", "Markdown"],
      },
      image: {
        icon: "las la-image",
        name: "Image Widget",
        color: "#84cc16",
        gradient: "linear-gradient(135deg, #84cc16, #65a30d)",
        features: ["Responsive", "Gallery"],
      },
      video: {
        icon: "las la-video",
        name: "Video Widget",
        color: "#f97316",
        gradient: "linear-gradient(135deg, #f97316, #ea580c)",
        features: ["Streaming", "Controls"],
      },
      tabs: {
        icon: "las la-folder-open",
        name: "Tab Container",
        color: "#6b7280",
        gradient: "linear-gradient(135deg, #6b7280, #4b5563)",
        features: ["Container", "Organization"],
      },
    };

    return (
      typeMap[type] || {
        icon: "las la-puzzle-piece",
        name: "Widget",
        color: "#6b7280",
        gradient: "linear-gradient(135deg, #6b7280, #4b5563)",
        features: ["Custom"],
      }
    );
  }
}

// Global functions for palette interactions
function addCanvasWidget(type) {
  if (window.canvasComposer) {
    window.canvasComposer.addWidget(type);
  }
}

// Prevent preview mode during canvas operations
window.canvasOperationInProgress = false;

function togglePalette() {
  const content = document.getElementById("paletteContent");
  const toggle = document.querySelector(".palette-toggle i");

  if (content && toggle) {
    content.classList.toggle("collapsed");
    toggle.className = content.classList.contains("collapsed")
      ? "las la-chevron-right"
      : "las la-chevron-left";
  }
}

function openCanvasComposer() {
  if (window.canvasComposer) {
    // Set flag to prevent preview mode interference
    window.canvasOperationInProgress = true;

    window.canvasComposer.show();

    // Clear the flag after a delay
    setTimeout(() => {
      window.canvasOperationInProgress = false;
    }, 1000);
  } else {
    console.error("Canvas Composer not initialized");
  }
}

// Global function to save canvas template - called from HTML
function saveCanvasTemplate() {
  if (window.canvasComposer) {
    window.canvasComposer.saveAsTemplate();
  } else {
    console.error("Canvas Composer not initialized");
  }
}

// Global function to create canvas section - called from HTML
function createCanvasSection(sectionData = null) {
  if (window.canvasComposer && !sectionData) {
    window.canvasComposer.createSection();
  } else if (sectionData) {
    // Handle section creation with provided data
    console.log("Creating section with data:", sectionData);

    if (window.canvasComposer) {
      window.canvasComposer.createSectionOnMainDashboard(sectionData);
    } else {
      console.error("Canvas Composer not initialized");
    }
  } else {
    console.error("Canvas Composer not initialized");
  }
}

// Canvas toolbar functions
function setCanvasMode(mode) {
  console.log("Setting canvas mode:", mode);
  // Update toolbar button states
  document
    .querySelectorAll(".toolbar-btn")
    .forEach((btn) => btn.classList.remove("active"));
  document.getElementById(mode + "Mode").classList.add("active");

  // Apply mode-specific behavior
  if (mode === "select") {
    // Enable selection mode
    console.log("Selection mode enabled");
  } else if (mode === "drag") {
    // Enable drag mode (default)
    console.log("Drag mode enabled");
  }
}

function alignWidgets(alignment) {
  console.log("Aligning widgets:", alignment);
  if (window.canvasComposer && window.canvasComposer.gridStack) {
    // Get all widgets
    const widgets = window.canvasComposer.gridStack.getGridItems();
    // Implement alignment logic here
    console.log("Aligning", widgets.length, "widgets to", alignment);
  }
}

function distributeWidgets() {
  console.log("Distributing widgets");
  if (window.canvasComposer && window.canvasComposer.gridStack) {
    const widgets = window.canvasComposer.gridStack.getGridItems();
    console.log("Distributing", widgets.length, "widgets");
  }
}

function zoomCanvas(factor) {
  console.log("Zooming canvas by factor:", factor);
  const canvasGrid = document.getElementById("canvasGrid");
  if (canvasGrid) {
    const currentScale = canvasGrid.style.transform.match(/scale\(([^)]+)\)/);
    const scale = currentScale ? parseFloat(currentScale[1]) * factor : factor;
    canvasGrid.style.transform = `scale(${Math.max(0.5, Math.min(2, scale))})`;
    canvasGrid.style.transformOrigin = "top left";
  }
}

function resetCanvasZoom() {
  console.log("Resetting canvas zoom");
  const canvasGrid = document.getElementById("canvasGrid");
  if (canvasGrid) {
    canvasGrid.style.transform = "scale(1)";
  }
}

// Initialize global instance
// Note: Canvas Composer is now initialized in the main HTML file
