/* 
 * Grid Consistency Fix
 * This file provides standardized styling for grid-stack components
 * to resolve inconsistent margins and padding across the application
 */

/* Base grid styles */
.grid-stack {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  margin: 0;
}

/* Grid item styles */
.grid-stack-item {
  padding: 0 !important;
  margin: 0 !important;
}

/* Grid item content styles - primary target for standardization */
.grid-stack-item-content {
  position: absolute;
  inset: 0.5rem !important;
  padding: 0.75rem !important;
  margin: 0 !important;
  overflow: auto !important;
  height: auto !important;
  width: auto !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  background: #ffffff !important;
  border: 1px solid #e2e8f0 !important;
  border-radius: 6px !important;
  transition: all 0.2s ease;
}

/* Preview container specific adjustments */
#preview-container .grid-stack {
  margin: 0.75rem !important;
}

#preview-container .grid-stack-item {
  padding: 0 !important;
}

#preview-container .grid-stack-item-content {
  inset: 0.5rem !important;
  padding: 0.75rem !important;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .grid-stack {
    padding: 0.75rem;
  }

  .grid-stack-item-content {
    inset: 0.375rem !important;
    padding: 0.5rem !important;
    border-radius: 4px !important;
  }
}

/* Dark theme adjustments */
body.dark-theme .grid-stack-item-content {
  background: #1e293b !important;
  border-color: #334155 !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* Ensure nested grids maintain consistency */
.grid-stack .grid-stack {
  padding: 0.75rem;
}

/* Fix for section containers */
.section-container-widget .grid-stack {
  height: 100% !important;
  min-height: 200px !important;
}

/* Ensure proper behavior when dragging */
.grid-stack-item.ui-draggable-dragging .grid-stack-item-content {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

/* Ensure the grid-stack-item-content has proper height handling */
.grid-stack-item-content {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Enhanced scrolling behavior for grid content */
.grid-stack-item-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 177, 156, 0.3) transparent;
  scroll-behavior: smooth;
}

/* Webkit scrollbar styling for better appearance */
.grid-stack-item-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.grid-stack-item-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.grid-stack-item-content::-webkit-scrollbar-thumb {
  background: rgba(0, 177, 156, 0.3);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.grid-stack-item-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 177, 156, 0.5);
}

/* Ensure widget content fills available space */
.grid-stack-item-content > .widget,
.grid-stack-item-content > .card,
.grid-stack-item-content > div[class*="-widget"] {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Make widget bodies scrollable when content exceeds container */
.grid-stack-item-content .widget-body,
.grid-stack-item-content .card-body {
  flex: 1;
  overflow: auto;
  min-height: 0;
}
