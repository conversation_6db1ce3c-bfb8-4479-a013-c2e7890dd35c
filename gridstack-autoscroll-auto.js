/**
 * GridStack Auto-Scroll - Auto-Initialize Version
 *
 * Simply include this script after GridStack library and it will automatically
 * initialize with optimal viewport-based settings.
 *
 * Author: Vimal Thapliyal
 *
 * Features:
 * - Automatic initialization when DOM is ready
 * - Viewport-based optimization (mobile to 4K)
 * - Dynamic adjustment on window resize
 * - Zero configuration required
 *
 * Usage:
 * <script src="path/to/gridstack-autoscroll-auto.js"></script>
 */

class GridStackAutoScrollAuto {
  constructor() {
    this.isActive = false;
    this.scrollInterval = null;
    this.gridInstances = new Set();
    this.isDragging = false;
    this.currentDragElement = null;
    this.resizeTimeout = null;

    // Default configuration - will be overridden by optimal settings
    this.config = {
      scrollSpeed: 15,
      scrollZone: 80,
      scrollInterval: 16,
      enableHorizontalScroll: true,
      enableVerticalScroll: true,
      smoothScrolling: true,
      debug: false,
    };

    // Bind methods
    this.handleMouseMove = this.handleMouseMove.bind(this);
    this.handleDragStart = this.handleDragStart.bind(this);
    this.handleDragEnd = this.handleDragEnd.bind(this);
    this.startAutoScroll = this.startAutoScroll.bind(this);
    this.stopAutoScroll = this.stopAutoScroll.bind(this);
    this.performScroll = this.performScroll.bind(this);
    this.handleResize = this.handleResize.bind(this);
  }

  /**
   * Get optimal configuration based on current viewport size
   */
  getOptimalConfig() {
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Calculate optimal scroll zone (6-7% of viewport width, clamped between 60-120px)
    const scrollZone = Math.max(60, Math.min(viewportWidth * 0.07, 120));

    // Adjust scroll speed based on viewport size
    let scrollSpeed;
    if (viewportWidth > 2560) {
      scrollSpeed = 25; // 4K and larger
    } else if (viewportWidth > 1920) {
      scrollSpeed = 20; // QHD
    } else if (viewportWidth > 1200) {
      scrollSpeed = 15; // Standard desktop
    } else {
      scrollSpeed = 12; // Tablets and smaller
    }

    return {
      scrollSpeed: scrollSpeed,
      scrollZone: scrollZone,
      scrollInterval: 16, // 60fps for smooth performance
      enableHorizontalScroll: true,
      enableVerticalScroll: true,
      smoothScrolling: true,
      debug: false,
    };
  }

  /**
   * Apply optimal configuration
   */
  applyOptimalConfig() {
    const optimalConfig = this.getOptimalConfig();
    this.config = { ...this.config, ...optimalConfig };
    this.log(
      "Applied optimal config for viewport:",
      window.innerWidth + "x" + window.innerHeight,
      this.config
    );
  }

  /**
   * Auto-initialize with optimal settings
   */
  autoInit() {
    if (this.isActive) {
      this.log("Auto-scroll already initialized");
      return;
    }

    // Apply optimal configuration for current viewport
    this.applyOptimalConfig();

    // Discover and register GridStack instances
    this.discoverGridInstances();

    // Set up event listeners
    this.setupEventListeners();

    // Set up resize handler for dynamic adjustment
    this.setupResizeHandler();

    this.isActive = true;
    this.log("GridStack Auto-Scroll auto-initialized");
  }

  /**
   * Discover all GridStack instances on the page
   */
  discoverGridInstances() {
    // Find all grid elements
    const gridElements = document.querySelectorAll(".grid-stack");

    gridElements.forEach((element) => {
      if (element.gridstack) {
        this.registerGridInstance(element.gridstack);
      }
    });

    // Also check window.grid (common pattern)
    if (window.grid) {
      this.registerGridInstance(window.grid);
    }

    this.log(`Discovered ${this.gridInstances.size} GridStack instances`);
  }

  /**
   * Register a GridStack instance for auto-scroll
   */
  registerGridInstance(gridInstance) {
    if (!gridInstance || this.gridInstances.has(gridInstance)) {
      return;
    }

    this.gridInstances.add(gridInstance);

    // Hook into GridStack events
    gridInstance.on("dragstart", this.handleDragStart);
    gridInstance.on("dragstop", this.handleDragEnd);

    this.log("Registered GridStack instance");
  }

  /**
   * Set up global event listeners
   */
  setupEventListeners() {
    // Mouse move for tracking position during drag
    document.addEventListener("mousemove", this.handleMouseMove, {
      passive: false,
    });

    // Touch events for mobile support
    document.addEventListener("touchmove", this.handleMouseMove, {
      passive: false,
    });
  }

  /**
   * Set up resize handler for dynamic adjustment
   */
  setupResizeHandler() {
    window.addEventListener("resize", this.handleResize);
  }

  /**
   * Handle window resize with debouncing
   */
  handleResize() {
    // Stop current scrolling
    if (this.isDragging) {
      this.stopAutoScroll();
    }

    // Debounce resize handling
    clearTimeout(this.resizeTimeout);
    this.resizeTimeout = setTimeout(() => {
      this.applyOptimalConfig();
      this.log("Configuration updated for new viewport size");
    }, 250);
  }

  /**
   * Handle drag start event
   */
  handleDragStart(event, ui) {
    this.isDragging = true;
    this.currentDragElement = ui.helper || ui.item || event.target;
    this.log("Drag started");
  }

  /**
   * Handle drag end event
   */
  handleDragEnd(event, ui) {
    this.isDragging = false;
    this.currentDragElement = null;
    this.stopAutoScroll();
    this.log("Drag ended");
  }

  /**
   * Handle mouse move during drag
   */
  handleMouseMove(event) {
    if (!this.isDragging || !this.currentDragElement) {
      return;
    }

    const clientX =
      event.clientX || (event.touches && event.touches[0].clientX);
    const clientY =
      event.clientY || (event.touches && event.touches[0].clientY);

    if (!clientX || !clientY) {
      return;
    }

    const scrollDirection = this.calculateScrollDirection(clientX, clientY);

    if (scrollDirection.x !== 0 || scrollDirection.y !== 0) {
      this.startAutoScroll(scrollDirection);
    } else {
      this.stopAutoScroll();
    }
  }

  /**
   * Calculate scroll direction based on mouse position
   */
  calculateScrollDirection(clientX, clientY) {
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
    };

    const scrollDirection = { x: 0, y: 0 };

    // Ensure minimum scroll zone for large viewports
    const effectiveScrollZone = Math.max(this.config.scrollZone, 50);

    // Check horizontal scrolling with dynamic speed
    if (this.config.enableHorizontalScroll) {
      if (clientX < effectiveScrollZone) {
        scrollDirection.x = -1; // Scroll left
        const proximity = Math.max(
          0,
          Math.min(1, (effectiveScrollZone - clientX) / effectiveScrollZone)
        );
        scrollDirection.speedMultiplierX = 1 + proximity * 2;
      } else if (clientX > viewport.width - effectiveScrollZone) {
        scrollDirection.x = 1; // Scroll right
        const proximity = Math.max(
          0,
          Math.min(
            1,
            (clientX - (viewport.width - effectiveScrollZone)) /
              effectiveScrollZone
          )
        );
        scrollDirection.speedMultiplierX = 1 + proximity * 2;
      }
    }

    // Check vertical scrolling with dynamic speed
    if (this.config.enableVerticalScroll) {
      if (clientY < effectiveScrollZone) {
        scrollDirection.y = -1; // Scroll up
        const proximity = Math.max(
          0,
          Math.min(1, (effectiveScrollZone - clientY) / effectiveScrollZone)
        );
        scrollDirection.speedMultiplierY = 1 + proximity * 2;
      } else if (clientY > viewport.height - effectiveScrollZone) {
        scrollDirection.y = 1; // Scroll down
        const proximity = Math.max(
          0,
          Math.min(
            1,
            (clientY - (viewport.height - effectiveScrollZone)) /
              effectiveScrollZone
          )
        );
        scrollDirection.speedMultiplierY = 1 + proximity * 2;
      }
    }

    return scrollDirection;
  }

  /**
   * Start auto-scroll in the specified direction
   */
  startAutoScroll(direction) {
    if (this.scrollInterval) {
      return; // Already scrolling
    }

    this.scrollDirection = direction;
    this.scrollInterval = setInterval(
      this.performScroll,
      this.config.scrollInterval
    );
    this.log("Auto-scroll started:", direction);
  }

  /**
   * Stop auto-scroll
   */
  stopAutoScroll() {
    if (this.scrollInterval) {
      clearInterval(this.scrollInterval);
      this.scrollInterval = null;
      this.scrollDirection = null;
      this.log("Auto-scroll stopped");
    }
  }

  /**
   * Perform the actual scrolling
   */
  performScroll() {
    if (!this.scrollDirection || !this.isDragging) {
      this.stopAutoScroll();
      return;
    }

    // Apply dynamic speed multipliers for more responsive scrolling
    const baseScrollX = this.scrollDirection.x * this.config.scrollSpeed;
    const baseScrollY = this.scrollDirection.y * this.config.scrollSpeed;

    // Add viewport scaling for large screens
    const viewportWidth = window.innerWidth;
    const viewportScale = Math.min(2, Math.max(1, viewportWidth / 1920));

    const scrollX =
      baseScrollX *
      (this.scrollDirection.speedMultiplierX || 1) *
      viewportScale;
    const scrollY =
      baseScrollY *
      (this.scrollDirection.speedMultiplierY || 1) *
      viewportScale;

    // Ensure minimum scroll amount for large viewports
    const minScrollX =
      this.scrollDirection.x !== 0
        ? Math.max(Math.abs(scrollX), 5) * Math.sign(scrollX)
        : 0;
    const minScrollY =
      this.scrollDirection.y !== 0
        ? Math.max(Math.abs(scrollY), 5) * Math.sign(scrollY)
        : 0;

    if (this.config.smoothScrolling) {
      window.scrollBy({
        left: minScrollX,
        top: minScrollY,
        behavior: "instant",
      });
    } else {
      window.scrollBy(minScrollX, minScrollY);
    }

    // Improved scroll bounds checking
    const scrollableWidth = Math.max(
      0,
      document.documentElement.scrollWidth - window.innerWidth
    );
    const scrollableHeight = Math.max(
      0,
      document.documentElement.scrollHeight - window.innerHeight
    );

    const canScrollLeft = window.scrollX > 0;
    const canScrollRight = window.scrollX < scrollableWidth;
    const canScrollUp = window.scrollY > 0;
    const canScrollDown = window.scrollY < scrollableHeight;

    // Stop scrolling if we can't scroll further in the current direction
    if (
      (this.scrollDirection.x < 0 && !canScrollLeft) ||
      (this.scrollDirection.x > 0 && !canScrollRight) ||
      (this.scrollDirection.y < 0 && !canScrollUp) ||
      (this.scrollDirection.y > 0 && !canScrollDown)
    ) {
      this.stopAutoScroll();
    }
  }

  /**
   * Debug logging
   */
  log(...args) {
    if (this.config.debug) {
      console.log("[GridStack Auto-Scroll Auto]", ...args);
    }
  }

  /**
   * Public method to enable debug mode
   */
  enableDebug() {
    this.config.debug = true;
    this.log("Debug mode enabled");
  }

  /**
   * Public method to disable debug mode
   */
  disableDebug() {
    this.config.debug = false;
  }
}

// Create singleton instance
const gridStackAutoScrollAuto = new GridStackAutoScrollAuto();

// Auto-initialize when DOM is ready and GridStack is available
function initializeWhenReady() {
  // Check if GridStack is available
  if (typeof window !== "undefined" && window.GridStack) {
    // Wait a bit for GridStack instances to be created
    setTimeout(() => {
      gridStackAutoScrollAuto.autoInit();
    }, 100);
  } else {
    // GridStack not ready yet, try again
    setTimeout(initializeWhenReady, 100);
  }
}

// Start initialization process
if (typeof window !== "undefined") {
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeWhenReady);
  } else {
    // DOM is already ready
    initializeWhenReady();
  }
}

// Export for global access
if (typeof window !== "undefined") {
  window.GridStackAutoScrollAuto = gridStackAutoScrollAuto;
}

// Export for different module systems
if (typeof module !== "undefined" && module.exports) {
  module.exports = gridStackAutoScrollAuto;
} else if (typeof define === "function" && define.amd) {
  define([], function () {
    return gridStackAutoScrollAuto;
  });
}
