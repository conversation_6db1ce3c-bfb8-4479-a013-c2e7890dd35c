// Returns the HTML markup for a table widget, given tableId and settingsId
function getTableWidgetMarkup({ tableId, settingsId }) {
  return `
    <div class="table-widget p-2">
      <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
        <div>
           <i class="las la-table"></i> Table Widget
        </div>
        <div>
          <button class="btn btn-sm btn-link text-dark"
                  data-bs-toggle="offcanvas"
                  data-bs-target="#${settingsId}"
                  aria-controls="${settingsId}">
            <i class="las la-cog"></i>
          </button>
          <button class="btn btn-sm btn-link text-dark ms-1"
                  onclick="removeWidget(this)">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      <div id="${tableId}" class="table-container">
        <div class="table-responsive">
          <table class="table table-bordered table-striped">
            <thead>
              <tr>
                <th>Column 1</th>
                <th>Column 2</th>
                <th>Column 3</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td contenteditable="true">Row 1, Col 1</td>
                <td contenteditable="true">Row 1, Col 2</td>
                <td contenteditable="true">Row 1, Col 3</td>
              </tr>
              <tr>
                <td contenteditable="true">Row 2, Col 1</td>
                <td contenteditable="true">Row 2, Col 2</td>
                <td contenteditable="true">Row 2, Col 3</td>
              </tr>
              <tr>
                <td contenteditable="true">Row 3, Col 1</td>
                <td contenteditable="true">Row 3, Col 2</td>
                <td contenteditable="true">Row 3, Col 3</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  `;
}

// Add a table widget
function addTableWidget() {
  console.log("Adding table widget");
  const tableId = "table-" + Date.now();
  const settingsId = "settings-" + tableId;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 6,
    content: getTableWidgetMarkup({ tableId, settingsId }),
  });

  // Create settings panel in the offcanvas container
  const offcanvasContainer = document.getElementById("offcanvasContainer");
  const settingsPanel = document.createElement("div");
  settingsPanel.className = "offcanvas offcanvas-end";
  settingsPanel.id = settingsId;
  settingsPanel.setAttribute("tabindex", "-1");
  settingsPanel.innerHTML = `
    <div class="offcanvas-header">
      <h5 class="offcanvas-title">Table Settings</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- Table Structure -->
      <div class="mb-3">
        <label class="form-label">Table Structure</label>
        <div class="row">
          <div class="col-6">
            <label for="${settingsId}-rows" class="form-label">Rows</label>
            <input type="number" class="form-control" id="${settingsId}-rows" min="1" max="20" value="3">
          </div>
          <div class="col-6">
            <label for="${settingsId}-cols" class="form-label">Columns</label>
            <input type="number" class="form-control" id="${settingsId}-cols" min="1" max="10" value="3">
          </div>
        </div>
      </div>

      <!-- Table Style -->
      <div class="mb-3">
        <label class="form-label">Table Style</label>
        <select class="form-select" id="${settingsId}-style">
          <option value="table-bordered table-striped">Bordered & Striped</option>
          <option value="table-bordered">Bordered Only</option>
          <option value="table-striped">Striped Only</option>
          <option value="">Plain</option>
          <option value="table-hover">Hover Effect</option>
          <option value="table-dark">Dark Theme</option>
        </select>
      </div>

      <!-- Table Size -->
      <div class="mb-3">
        <label class="form-label">Table Size</label>
        <select class="form-select" id="${settingsId}-size">
          <option value="">Default</option>
          <option value="table-sm">Small</option>
          <option value="table-lg">Large</option>
        </select>
      </div>

      <!-- Header Style -->
      <div class="mb-3">
        <label class="form-label">Header Style</label>
        <select class="form-select" id="${settingsId}-headerStyle">
          <option value="">Default</option>
          <option value="table-dark">Dark Header</option>
          <option value="table-light">Light Header</option>
          <option value="table-primary">Primary Header</option>
          <option value="table-secondary">Secondary Header</option>
          <option value="table-success">Success Header</option>
          <option value="table-info">Info Header</option>
        </select>
      </div>

      <!-- Font Size -->
      <div class="mb-3">
        <label class="form-label">Font Size (px)</label>
        <input type="range" class="form-range" min="10" max="20" value="14" id="${settingsId}-fontSize">
        <div class="d-flex justify-content-between">
          <small>10px</small>
          <small id="${settingsId}-fontSize-display">14px</small>
          <small>20px</small>
        </div>
      </div>

      <!-- Text Alignment -->
      <div class="mb-3">
        <label class="form-label">Text Alignment</label>
        <select class="form-select" id="${settingsId}-textAlign">
          <option value="left">Left</option>
          <option value="center">Center</option>
          <option value="right">Right</option>
        </select>
      </div>

      <!-- Show Header -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-showHeader" checked>
          <label class="form-check-label" for="${settingsId}-showHeader">Show Header</label>
        </div>
      </div>

      <!-- Editable Cells -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-editable" checked>
          <label class="form-check-label" for="${settingsId}-editable">Editable Cells</label>
        </div>
      </div>

      <!-- Responsive -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-responsive" checked>
          <label class="form-check-label" for="${settingsId}-responsive">Responsive Table</label>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="mb-3">
        <div class="d-flex gap-2">
          <button class="btn btn-outline-secondary btn-sm" onclick="addTableRow('${tableId}')">
            <i class="las la-plus"></i> Add Row
          </button>
          <button class="btn btn-outline-secondary btn-sm" onclick="addTableColumn('${tableId}')">
            <i class="las la-plus"></i> Add Column
          </button>
        </div>
      </div>

      <div class="mb-3">
        <div class="d-flex gap-2">
          <button class="btn btn-outline-danger btn-sm" onclick="removeTableRow('${tableId}')">
            <i class="las la-minus"></i> Remove Row
          </button>
          <button class="btn btn-outline-danger btn-sm" onclick="removeTableColumn('${tableId}')">
            <i class="las la-minus"></i> Remove Column
          </button>
        </div>
      </div>

      <!-- Import/Export -->
      <div class="mb-3">
        <label class="form-label">Import/Export</label>
        <div class="d-flex gap-2">
          <button class="btn btn-outline-primary btn-sm" onclick="exportTableData('${tableId}')">
            <i class="las la-download"></i> Export CSV
          </button>
          <label class="btn btn-outline-primary btn-sm mb-0">
            <i class="las la-upload"></i> Import CSV
            <input type="file" id="${settingsId}-import" style="display: none;" accept=".csv" onchange="importTableData('${tableId}', this)">
          </label>
        </div>
      </div>

      <!-- Apply Button -->
      <button class="btn btn-primary w-100" onclick="applyTableSettings('${tableId}', '${settingsId}')">
        Apply Changes
      </button>
    </div>
  `;
  offcanvasContainer.appendChild(settingsPanel);

  // Initialize the table widget
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing table widget");
      initTableWidget(tableId, settingsId);
    } catch (error) {
      console.error("Error initializing table widget:", error);
    }
  }, 100);

  return widget;
}

// Function to initialize the table widget
function initTableWidget(tableId, settingsId) {
  console.log("Initializing table widget:", tableId);
  const container = document.getElementById(tableId);
  const table = container.querySelector("table");

  if (!container || !table) {
    console.error("Table container not found:", tableId);
    return;
  }

  // Set up real-time range slider updates
  const fontSize = document.getElementById(`${settingsId}-fontSize`);
  const fontSizeDisplay = document.getElementById(
    `${settingsId}-fontSize-display`
  );

  if (fontSize && fontSizeDisplay) {
    fontSize.addEventListener("input", function () {
      fontSizeDisplay.textContent = this.value + "px";
    });
  }

  // Add event listeners for cell editing
  const cells = table.querySelectorAll(
    "td[contenteditable], th[contenteditable]"
  );
  cells.forEach((cell) => {
    cell.addEventListener("focus", function () {
      this.classList.add("editing");
    });

    cell.addEventListener("blur", function () {
      this.classList.remove("editing");
    });

    cell.addEventListener("keydown", function (e) {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        this.blur();
      }
    });
  });

  console.log("Table widget initialized:", tableId);
}

// Function to apply table settings
function applyTableSettings(tableId, settingsId) {
  const container = document.getElementById(tableId);
  const table = container.querySelector("table");
  if (!container || !table) return;

  // Get all settings values
  const rows = parseInt(document.getElementById(`${settingsId}-rows`).value);
  const cols = parseInt(document.getElementById(`${settingsId}-cols`).value);
  const style = document.getElementById(`${settingsId}-style`).value;
  const size = document.getElementById(`${settingsId}-size`).value;
  const headerStyle = document.getElementById(
    `${settingsId}-headerStyle`
  ).value;
  const fontSize = document.getElementById(`${settingsId}-fontSize`).value;
  const textAlign = document.getElementById(`${settingsId}-textAlign`).value;
  const showHeader = document.getElementById(
    `${settingsId}-showHeader`
  ).checked;
  const editable = document.getElementById(`${settingsId}-editable`).checked;
  const responsive = document.getElementById(
    `${settingsId}-responsive`
  ).checked;

  // Apply table structure changes
  updateTableStructure(tableId, rows, cols);

  // Apply table classes
  table.className = `table ${style} ${size}`.trim();

  // Apply header style
  const thead = table.querySelector("thead");
  if (thead) {
    thead.className = headerStyle;
    thead.style.display = showHeader ? "" : "none";
  }

  // Apply font size and text alignment
  table.style.fontSize = fontSize + "px";
  table.style.textAlign = textAlign;

  // Apply editable settings
  const cells = table.querySelectorAll("td, th");
  cells.forEach((cell) => {
    if (cell.tagName === "TD") {
      cell.contentEditable = editable;
    }
  });

  // Apply responsive wrapper
  const tableResponsive = container.querySelector(".table-responsive");
  if (responsive) {
    if (!tableResponsive) {
      const wrapper = document.createElement("div");
      wrapper.className = "table-responsive";
      table.parentNode.insertBefore(wrapper, table);
      wrapper.appendChild(table);
    }
  } else {
    if (tableResponsive) {
      tableResponsive.parentNode.insertBefore(table, tableResponsive);
      tableResponsive.remove();
    }
  }

  // Store the settings in the container dataset for persistence
  container.dataset.tableSettings = JSON.stringify({
    rows,
    cols,
    style,
    size,
    headerStyle,
    fontSize,
    textAlign,
    showHeader,
    editable,
    responsive,
  });

  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(
    document.getElementById(settingsId)
  );
  if (offcanvas) {
    offcanvas.hide();
  }
}

// Function to update table structure
function updateTableStructure(tableId, rows, cols) {
  const container = document.getElementById(tableId);
  const table = container.querySelector("table");
  if (!table) return;

  let thead = table.querySelector("thead");
  let tbody = table.querySelector("tbody");

  // Create thead if it doesn't exist
  if (!thead) {
    thead = document.createElement("thead");
    table.appendChild(thead);
  }

  // Create tbody if it doesn't exist
  if (!tbody) {
    tbody = document.createElement("tbody");
    table.appendChild(tbody);
  }

  // Update header row
  let headerRow = thead.querySelector("tr");
  if (!headerRow) {
    headerRow = document.createElement("tr");
    thead.appendChild(headerRow);
  }

  // Clear existing header cells
  headerRow.innerHTML = "";

  // Add header cells
  for (let i = 0; i < cols; i++) {
    const th = document.createElement("th");
    th.textContent = `Column ${i + 1}`;
    th.contentEditable = true;
    headerRow.appendChild(th);
  }

  // Update body rows
  tbody.innerHTML = "";
  for (let i = 0; i < rows; i++) {
    const row = document.createElement("tr");
    for (let j = 0; j < cols; j++) {
      const td = document.createElement("td");
      td.textContent = `Row ${i + 1}, Col ${j + 1}`;
      td.contentEditable = true;
      row.appendChild(td);
    }
    tbody.appendChild(row);
  }

  // Re-initialize event listeners
  initTableWidget(tableId, `settings-${tableId}`);
}

// Function to add a table row
function addTableRow(tableId) {
  const container = document.getElementById(tableId);
  const table = container.querySelector("table");
  const tbody = table.querySelector("tbody");

  if (!tbody) return;

  const cols = table.querySelector("thead tr").children.length;
  const row = document.createElement("tr");

  for (let i = 0; i < cols; i++) {
    const td = document.createElement("td");
    td.textContent = `New Row, Col ${i + 1}`;
    td.contentEditable = true;
    row.appendChild(td);
  }

  tbody.appendChild(row);
}

// Function to add a table column
function addTableColumn(tableId) {
  const container = document.getElementById(tableId);
  const table = container.querySelector("table");
  const thead = table.querySelector("thead");
  const tbody = table.querySelector("tbody");

  if (!thead || !tbody) return;

  // Add header cell
  const headerRow = thead.querySelector("tr");
  const th = document.createElement("th");
  th.textContent = `Column ${headerRow.children.length + 1}`;
  th.contentEditable = true;
  headerRow.appendChild(th);

  // Add cells to existing rows
  const rows = tbody.querySelectorAll("tr");
  rows.forEach((row, index) => {
    const td = document.createElement("td");
    td.textContent = `Row ${index + 1}, New Col`;
    td.contentEditable = true;
    row.appendChild(td);
  });
}

// Function to remove a table row
function removeTableRow(tableId) {
  const container = document.getElementById(tableId);
  const table = container.querySelector("table");
  const tbody = table.querySelector("tbody");

  if (!tbody || tbody.children.length <= 1) return;

  tbody.removeChild(tbody.lastElementChild);
}

// Function to remove a table column
function removeTableColumn(tableId) {
  const container = document.getElementById(tableId);
  const table = container.querySelector("table");
  const thead = table.querySelector("thead");
  const tbody = table.querySelector("tbody");

  if (!thead || !tbody) return;

  const headerRow = thead.querySelector("tr");
  if (headerRow.children.length <= 1) return;

  // Remove header cell
  headerRow.removeChild(headerRow.lastElementChild);

  // Remove cells from existing rows
  const rows = tbody.querySelectorAll("tr");
  rows.forEach((row) => {
    if (row.children.length > 0) {
      row.removeChild(row.lastElementChild);
    }
  });
}

// Function to export table data as CSV
function exportTableData(tableId) {
  const container = document.getElementById(tableId);
  const table = container.querySelector("table");
  if (!table) return;

  let csv = "";
  const rows = table.querySelectorAll("tr");

  rows.forEach((row) => {
    const cells = row.querySelectorAll("th, td");
    const rowData = Array.from(cells).map((cell) => {
      const text = cell.textContent.trim();
      return text.includes(",") ? `"${text}"` : text;
    });
    csv += rowData.join(",") + "\n";
  });

  // Create download link
  const blob = new Blob([csv], { type: "text/csv" });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = `table-data-${Date.now()}.csv`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
}

// Function to import table data from CSV
function importTableData(tableId, fileInput) {
  const container = document.getElementById(tableId);
  const table = container.querySelector("table");
  if (!table || !fileInput.files.length) return;

  const file = fileInput.files[0];
  const reader = new FileReader();

  reader.onload = function (e) {
    const csv = e.target.result;
    const lines = csv.split("\n").filter((line) => line.trim());

    if (lines.length === 0) return;

    // Parse CSV data
    const data = lines.map((line) => {
      const cells = [];
      let current = "";
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === "," && !inQuotes) {
          cells.push(current.trim());
          current = "";
        } else {
          current += char;
        }
      }
      cells.push(current.trim());
      return cells;
    });

    // Update table structure
    const rows = data.length - 1; // Subtract header row
    const cols = data[0].length;
    updateTableStructure(tableId, rows, cols);

    // Populate table with data
    const thead = table.querySelector("thead tr");
    const tbody = table.querySelector("tbody");

    // Set header data
    data[0].forEach((cellData, index) => {
      if (thead.children[index]) {
        thead.children[index].textContent = cellData;
      }
    });

    // Set body data
    for (let i = 1; i < data.length; i++) {
      const row = tbody.children[i - 1];
      if (row) {
        data[i].forEach((cellData, index) => {
          if (row.children[index]) {
            row.children[index].textContent = cellData;
          }
        });
      }
    }

    fileInput.value = "";
  };

  reader.readAsText(file);
}

// Function to load table settings from stored data
function loadTableSettings(tableId, settingsId) {
  const container = document.getElementById(tableId);
  if (!container || !container.dataset.tableSettings) return;

  try {
    const settings = JSON.parse(container.dataset.tableSettings);

    // Load settings into form elements
    document.getElementById(`${settingsId}-rows`).value = settings.rows || 3;
    document.getElementById(`${settingsId}-cols`).value = settings.cols || 3;
    document.getElementById(`${settingsId}-style`).value =
      settings.style || "table-bordered table-striped";
    document.getElementById(`${settingsId}-size`).value = settings.size || "";
    document.getElementById(`${settingsId}-headerStyle`).value =
      settings.headerStyle || "";
    document.getElementById(`${settingsId}-fontSize`).value =
      settings.fontSize || 14;
    document.getElementById(`${settingsId}-textAlign`).value =
      settings.textAlign || "left";
    document.getElementById(`${settingsId}-showHeader`).checked =
      settings.showHeader !== false;
    document.getElementById(`${settingsId}-editable`).checked =
      settings.editable !== false;
    document.getElementById(`${settingsId}-responsive`).checked =
      settings.responsive !== false;

    // Update display values
    document.getElementById(`${settingsId}-fontSize-display`).textContent =
      (settings.fontSize || 14) + "px";
  } catch (error) {
    console.error("Error loading table settings:", error);
  }
}

// Export the functions to global scope
window.addTableWidget = addTableWidget;
window.initTableWidget = initTableWidget;
window.applyTableSettings = applyTableSettings;
window.updateTableStructure = updateTableStructure;
window.addTableRow = addTableRow;
window.addTableColumn = addTableColumn;
window.removeTableRow = removeTableRow;
window.removeTableColumn = removeTableColumn;
window.exportTableData = exportTableData;
window.importTableData = importTableData;
window.loadTableSettings = loadTableSettings;
