/**
 * Tab Container Widget Styles
 * Modern tabbed interface with drag-and-drop functionality
 */

.tab-container-widget {
  position: relative;
  height: 500px; /* Increased height for nested grids */
  background: var(--widget-bg, #ffffff);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tab-container-widget .widget-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--header-bg, #f8f9fa);
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 60px;
  z-index: 1;
}

.tab-container-widget .widget-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
}

.tab-container-widget .widget-title i {
  font-size: 18px;
  color: var(--primary-color, #3b82f6);
}

.tab-container-widget .widget-actions {
  display: flex;
  gap: 8px;
}

.tab-container-widget .widget-action-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  transition: all 0.2s ease;
}

.tab-container-widget .widget-action-btn:hover {
  background: var(--hover-bg, #e5e7eb);
  color: var(--primary-color, #3b82f6);
}

/* Tab Container Body */
.tab-container-body {
  position: relative;
  flex: 1;
  overflow: hidden;
  padding-bottom: 60px; /* Space for tab navigation */
}

/* Default Content */
.tab-container-default-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 60px; /* Leave space for bottom tabs */
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--widget-bg, #ffffff);
  transition: opacity 0.3s ease;
}

.default-content-placeholder {
  text-align: center;
  color: var(--text-secondary, #6b7280);
}

.default-content-placeholder i {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--primary-color, #3b82f6);
  opacity: 0.6;
}

.default-content-placeholder h3 {
  margin-bottom: 8px;
  color: var(--text-primary, #1f2937);
  font-weight: 600;
}

.default-content-placeholder p {
  margin: 0 0 16px 0;
  font-size: 14px;
}

.grid-info {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.grid-info .badge {
  font-size: 11px;
  padding: 4px 8px;
}

/* Slideout Panels */
.tab-slideout-panel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 60px; /* Leave space for bottom tabs */
  background: var(--widget-bg, #ffffff);
  transform: translateY(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  z-index: 10;
  display: none;
  overflow: hidden;
}

.tab-slideout-panel.active {
  transform: translateY(0%);
  opacity: 1;
  visibility: visible;
}

.tab-slideout-panel .panel-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tab-slideout-panel .content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--panel-header-bg, #f8f9fa);
  flex-shrink: 0;
}

.tab-slideout-panel .content-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-slideout-panel .content-header i {
  color: var(--primary-color, #3b82f6);
}

.panel-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-panel-btn {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  color: var(--text-secondary, #6b7280);
  cursor: pointer;
  transition: all 0.2s ease;
}

.close-panel-btn:hover {
  background: var(--danger-color, #ef4444);
  color: white;
}

.tab-slideout-panel .content-body {
  flex: 1;
  position: relative;
  overflow: hidden;
  padding: 0;
}

/* Nested Grid Container */
.nested-grid-container {
  position: relative;
  height: 100%;
  width: 100%;
}

.nested-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 12px;
  overflow-y: auto;
  overflow-x: hidden;
}

/* GridStack styles for nested grids */
.nested-grid .grid-stack {
  background: transparent;
}

.nested-grid .grid-stack-item {
  background: var(--widget-bg, #ffffff);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color, #e5e7eb);
}

.nested-grid .grid-stack-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nested-grid .grid-stack-item.ui-draggable-dragging {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  transform: rotate(2deg);
  z-index: 1000;
}

/* Nested widget styling */
.nested-widget {
  font-size: 13px;
}

.nested-widget .widget-header {
  padding: 8px 12px;
  min-height: 36px;
  font-size: 13px;
}

.nested-widget .widget-title {
  font-size: 13px;
}

.nested-widget .widget-action-btn {
  width: 24px;
  height: 24px;
  padding: 4px;
  font-size: 12px;
}

/* Grid Placeholder */
.grid-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--placeholder-bg, #f8f9fa);
  border: 2px dashed var(--border-color, #e5e7eb);
  border-radius: 8px;
  margin: 12px;
  transition: all 0.3s ease;
}

.placeholder-content {
  text-align: center;
  color: var(--text-secondary, #6b7280);
  padding: 20px;
}

.placeholder-content i {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--primary-color, #3b82f6);
  opacity: 0.4;
}

.placeholder-content h5 {
  margin-bottom: 8px;
  color: var(--text-primary, #1f2937);
  font-weight: 600;
}

.placeholder-content p {
  margin-bottom: 16px;
  font-size: 14px;
}

.drop-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  background: var(--primary-color, #3b82f6);
  color: white;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.drop-hint i {
  font-size: 14px !important;
  margin: 0 !important;
  opacity: 1 !important;
  color: white !important;
}

/* Drag Over States */
.nested-grid.drag-over {
  background: rgba(59, 130, 246, 0.05);
  border-radius: 8px;
}

.nested-grid.drag-over .grid-placeholder {
  border-color: var(--primary-color, #3b82f6);
  background: rgba(59, 130, 246, 0.1);
  border-style: solid;
}

.nested-grid.drag-over .drop-hint {
  background: var(--success-color, #10b981);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Bottom Tab Navigation */
.tab-container-tabs {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  background: white;
  border-top: 1px solid #dee2e6;
  height: 60px;
  z-index: 20;
  border-radius: 0;
}

.tab-nav-item {
  flex: 1;
  display: flex;
  margin: 0;
  padding: 0;
}

.tab-nav-link {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  background: #dfebed;
  border: none;
  border-right: 2px solid #fff;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #231f20d6;
  font-size: 12px;
  font-weight: bold;
  gap: 4px;
  position: relative;
  text-align: center;
  width: 100%;
  min-height: 60px;
}

.tab-nav-item:last-child .tab-nav-link {
  border-right: none;
}

.tab-nav-link:hover {
  background: #c8d9dc;
  color: #231f20;
}

.tab-nav-link.active {
  background: #bbd6dc !important;
  color: #231f20 !important;
  border-bottom: none;
}

.tab-nav-link.active::before {
  display: none;
}

.tab-nav-link i {
  font-size: 18px;
  margin-bottom: 4px;
  display: block;
}

.tab-nav-link span {
  font-weight: bold;
  font-size: 10px;
  line-height: 1.2;
  display: block;
}

/* Dark Mode Support - Updated for new tab styling */
.tab-container-widget[data-theme="dark"] {
  --widget-bg: #1f2937;
  --header-bg: #111827;
  --panel-header-bg: #111827;
  --placeholder-bg: #111827;
  --text-primary: #f9fafb;
  --text-secondary: #9ca3af;
  --border-color: #374151;
}

.tab-container-widget[data-theme="dark"] .tab-container-tabs {
  background: #111827;
  border-top: 1px solid #374151;
}

.tab-container-widget[data-theme="dark"] .tab-nav-link {
  background: #2d3748;
  color: #e2e8f0;
  border-right: 2px solid #111827;
}

.tab-container-widget[data-theme="dark"] .tab-nav-link:hover {
  background: #4a5568;
  color: #f7fafc;
}

.tab-container-widget[data-theme="dark"] .tab-nav-link.active {
  background: #4a5568 !important;
  color: #f7fafc !important;
}

.tab-container-widget[data-theme="dark"]
  .tab-nav-item:last-child
  .tab-nav-link {
  border-right: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tab-container-widget {
    height: 400px;
  }

  .tab-nav-link {
    font-size: 11px;
    padding: 6px 8px;
  }

  .tab-nav-link i {
    font-size: 16px;
  }

  .nested-grid {
    padding: 8px;
  }

  .grid-placeholder {
    margin: 8px;
  }

  .placeholder-content {
    padding: 16px;
  }

  .placeholder-content i {
    font-size: 36px;
  }
}

@media (max-width: 480px) {
  .tab-container-widget {
    height: 350px;
  }

  .tab-nav-link span {
    display: none;
  }

  .tab-nav-link {
    padding: 8px 6px;
  }

  .drop-hint {
    flex-direction: column;
    gap: 4px;
  }
}

/* GridStack override for nested grids */
.nested-grid .grid-stack-item-content {
  left: 0;
  right: 0;
}

/* Animation for widget drops */
.nested-grid .grid-stack-item.grid-stack-item-new {
  animation: widgetDrop 0.3s ease-out;
}

@keyframes widgetDrop {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Cross-Grid Drag and Drop Feedback */
.main-grid-dragging {
  /* Ensure body can overflow during dragging for better UX */
  overflow: auto !important;
}

.main-grid-dragging .nested-grid.potential-drop-target {
  background: rgba(59, 130, 246, 0.1);
  border: 2px dashed var(--primary-color, #3b82f6);
  border-radius: 8px;
  animation: dropTargetPulse 2s infinite;
}

.main-grid-dragging .nested-grid.potential-drop-target .grid-placeholder {
  display: none;
}

.main-grid-dragging .nested-grid.potential-drop-target::after {
  content: "Drop widget here";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--primary-color, #3b82f6);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  pointer-events: none;
  z-index: 1000;
}

@keyframes dropTargetPulse {
  0%,
  100% {
    background: rgba(59, 130, 246, 0.1);
  }
  50% {
    background: rgba(59, 130, 246, 0.2);
  }
}

/* Nested grid dragging feedback */
.nested-grid-dragging .grid-stack:not(.source-grid) {
  background: rgba(16, 185, 129, 0.1);
  border: 2px dashed var(--success-color, #10b981);
}

.nested-grid-dragging .grid-stack:not(.source-grid)::before {
  content: "Drop area";
  position: absolute;
  top: 10px;
  left: 10px;
  background: var(--success-color, #10b981);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  z-index: 100;
}

/* Enhanced drag states */
.grid-stack-item.ui-draggable-dragging {
  z-index: 2000 !important;
  transform: rotate(3deg) scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3) !important;
  border: 2px solid var(--primary-color, #3b82f6);
}

.grid-stack-item.ui-draggable-dragging .nested-widget {
  pointer-events: none;
}

/* Cross-grid compatibility styles */
.cross-grid-helper {
  background: var(--widget-bg, #ffffff) !important;
  border: 2px solid var(--primary-color, #3b82f6) !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
  border-radius: 8px !important;
  opacity: 0.9 !important;
}

/* Tab panel specific drag states */
.tab-slideout-panel.active .nested-grid.drag-over {
  background: rgba(59, 130, 246, 0.15);
  border: 3px solid var(--primary-color, #3b82f6);
}

.tab-slideout-panel.active .nested-grid.drag-over .grid-placeholder {
  border-color: var(--primary-color, #3b82f6);
  background: rgba(59, 130, 246, 0.2);
  border-width: 3px;
}

/* Enhanced visual feedback for active drop zones */
.drop-zone-active {
  animation: dropZoneActive 1s infinite alternate;
}

@keyframes dropZoneActive {
  0% {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
  }
  100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

/* Improved grid compatibility */
.nested-grid .grid-stack-item {
  transition: all 0.2s ease;
}

.nested-grid .grid-stack-item:hover:not(.ui-draggable-dragging) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced Tab Management Styles */

/* Tab Navigation */
.tab-navigation {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--widget-bg, #ffffff);
  border-top: 1px solid var(--border-color, #e5e7eb);
  z-index: 20;
}

.tab-nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  overflow-x: auto;
  scrollbar-width: none;
}

.tab-nav-list::-webkit-scrollbar {
  display: none;
}

/* Tab Management List */
.tab-management-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  background: var(--widget-bg, #ffffff);
}

.tab-management-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  transition: all 0.2s ease;
}

.tab-management-item:last-child {
  border-bottom: none;
}

.tab-management-item:hover {
  background: var(--hover-bg, #f8f9fa);
}

.tab-management-item.disabled {
  opacity: 0.6;
}

.tab-item-drag-handle {
  color: var(--text-secondary, #6b7280);
  cursor: grab;
  margin-right: 12px;
  padding: 4px;
}

.tab-item-drag-handle:active {
  cursor: grabbing;
}

.tab-item-info {
  flex: 1;
  min-width: 0;
}

.tab-item-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.tab-item-header i {
  color: var(--primary-color, #3b82f6);
  width: 16px;
}

.tab-item-name {
  font-weight: 500;
  color: var(--text-primary, #1f2937);
}

.badge-sm {
  font-size: 10px;
  padding: 2px 6px;
}

.tab-item-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
}

.tab-item-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tab-item-actions {
  display: flex;
  gap: 4px;
}

.tab-item-actions .btn {
  padding: 6px 8px;
  font-size: 12px;
}

/* Tab Configuration Modal */
.modal-body .input-group-text {
  background: var(--header-bg, #f8f9fa);
  border-color: var(--border-color, #e5e7eb);
}

.modal-body .input-group-text i {
  width: 16px;
  text-align: center;
}

/* Simple Content Container */
.simple-content-container {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
}

.simple-content-container .content-placeholder {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary, #6b7280);
}

.simple-content-container .content-placeholder i {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--primary-color, #3b82f6);
  opacity: 0.6;
}

.simple-content-container .content-placeholder h5 {
  margin-bottom: 8px;
  color: var(--text-primary, #1f2937);
  font-weight: 600;
}

.simple-content-container .content-placeholder p {
  margin: 0 0 20px 0;
  font-size: 14px;
}

.content-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Enhanced Panel Actions */
.panel-actions .btn {
  font-size: 12px;
  padding: 4px 8px;
}

/* Improved Tab Navigation Positioning */
.tab-container-default-content,
.tab-slideout-panel {
  bottom: 60px; /* Adjust for new navigation position */
}

/* Tab Management Drag and Drop */
.tab-management-item.sortable-ghost {
  opacity: 0.4;
}

.tab-management-item.sortable-chosen {
  background: var(--primary-color, #3b82f6);
  color: white;
}

.tab-management-item.sortable-chosen .tab-item-name,
.tab-management-item.sortable-chosen .tab-item-meta {
  color: white;
}

/* Enhanced Form Styles */
.form-text {
  font-size: 11px;
  margin-top: 4px;
}

/* Loading States */
.tab-management-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--text-secondary, #6b7280);
}

.tab-management-loading i {
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Tab Management */
@media (max-width: 768px) {
  .tab-item-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .tab-item-actions {
    flex-direction: column;
  }

  .tab-management-item {
    padding: 16px 12px;
  }
}

@media (max-width: 480px) {
  .tab-item-header {
    flex-wrap: wrap;
  }

  .tab-item-actions .btn {
    padding: 8px;
    min-width: 36px;
  }

  .tab-management-list {
    max-height: 250px;
  }
}
