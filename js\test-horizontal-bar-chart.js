/**
 * Test script to verify horizontal bar chart onclick and drag-drop consistency
 * Run this in the browser console to check if both methods work properly
 */

function testHorizontalBarChart() {
  console.log('🧪 Testing Horizontal Bar Chart Functionality...');
  
  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };
  
  function addTest(name, condition, message) {
    const passed = condition;
    results.tests.push({ name, passed, message });
    if (passed) {
      results.passed++;
      console.log(`✅ ${name}: ${message}`);
    } else {
      results.failed++;
      console.log(`❌ ${name}: ${message}`);
    }
  }
  
  // Test 1: Check if shared markup function exists
  addTest(
    'Shared Markup Function Available',
    typeof window.getHorizontalBarChartWidgetMarkup === 'function',
    typeof window.getHorizontalBarChartWidgetMarkup === 'function' ? 'getHorizontalBarChartWidgetMarkup function found' : 'getHorizontalBarChartWidgetMarkup function not found'
  );
  
  // Test 2: Check if click handler exists
  addTest(
    'Click Handler Available',
    typeof window.addHorizontalBarChartWidget === 'function',
    typeof window.addHorizontalBarChartWidget === 'function' ? 'addHorizontalBarChartWidget function found' : 'addHorizontalBarChartWidget function not found'
  );
  
  // Test 3: Check if drag-drop setup exists
  addTest(
    'Drag-Drop Setup Available',
    typeof window.setupHorizontalBarChartDragDrop === 'function',
    typeof window.setupHorizontalBarChartDragDrop === 'function' ? 'setupHorizontalBarChartDragDrop function found' : 'setupHorizontalBarChartDragDrop function not found'
  );
  
  // Test 4: Check if chart initialization function exists
  addTest(
    'Chart Initialization Available',
    typeof window.initHorizontalBarChart === 'function',
    typeof window.initHorizontalBarChart === 'function' ? 'initHorizontalBarChart function found' : 'initHorizontalBarChart function not found'
  );
  
  // Test 5: Test markup generation
  if (typeof window.getHorizontalBarChartWidgetMarkup === 'function') {
    try {
      const testChartId = 'test-horizontal-bar-chart-1';
      const markup = window.getHorizontalBarChartWidgetMarkup(testChartId);
      
      // Check if markup contains expected elements
      const hasWidgetClass = markup.includes('class="widget');
      const hasChartContainer = markup.includes('chart-container');
      const hasSettings = markup.includes('horizontalBarChartSettings');
      const hasCorrectId = markup.includes(testChartId);
      const hasTitle = markup.includes('Horizontal Bar Chart');
      
      addTest(
        'Markup Contains Widget Class',
        hasWidgetClass,
        hasWidgetClass ? 'Widget class found in markup' : 'Widget class missing from markup'
      );
      
      addTest(
        'Markup Contains Chart Container',
        hasChartContainer,
        hasChartContainer ? 'Chart container found in markup' : 'Chart container missing from markup'
      );
      
      addTest(
        'Markup Contains Settings Button',
        hasSettings,
        hasSettings ? 'Settings button found in markup' : 'Settings button missing from markup'
      );
      
      addTest(
        'Markup Uses Correct Chart ID',
        hasCorrectId,
        hasCorrectId ? 'Chart ID correctly inserted in markup' : 'Chart ID not found in markup'
      );
      
      addTest(
        'Markup Contains Title',
        hasTitle,
        hasTitle ? 'Chart title found in markup' : 'Chart title missing from markup'
      );
      
    } catch (error) {
      addTest(
        'Markup Generation',
        false,
        `Error generating markup: ${error.message}`
      );
    }
  }
  
  // Test 6: Check if widget exists in gallery
  const horizontalBarChartWidget = document.querySelector('.widget-item[data-widget-type="horizontal-bar-chart"]');
  addTest(
    'Horizontal Bar Chart Widget in Gallery',
    horizontalBarChartWidget !== null,
    horizontalBarChartWidget ? 'Horizontal bar chart widget found in gallery' : 'Horizontal bar chart widget not found in gallery'
  );
  
  // Test 7: Check if widget has onclick handler
  if (horizontalBarChartWidget) {
    const hasOnclick = horizontalBarChartWidget.hasAttribute('onclick') || horizontalBarChartWidget.onclick;
    addTest(
      'Widget Has Click Handler',
      hasOnclick,
      hasOnclick ? 'Horizontal bar chart widget has onclick handler' : 'Horizontal bar chart widget missing onclick handler'
    );
    
    // Test 8: Check if widget has data-widget-type for drag-drop
    const hasDataWidgetType = horizontalBarChartWidget.hasAttribute('data-widget-type');
    const correctDataWidgetType = horizontalBarChartWidget.getAttribute('data-widget-type') === 'horizontal-bar-chart';
    addTest(
      'Widget Has Correct Data Widget Type',
      hasDataWidgetType && correctDataWidgetType,
      hasDataWidgetType && correctDataWidgetType ? 'Widget has correct data-widget-type for drag-drop' : 'Widget missing or incorrect data-widget-type'
    );
  }
  
  // Test 9: Check if GridStack is available
  const hasGridStackSetup = typeof GridStack !== 'undefined' && GridStack.setupDragIn;
  addTest(
    'GridStack Available',
    hasGridStackSetup,
    hasGridStackSetup ? 'GridStack and setupDragIn available' : 'GridStack or setupDragIn not available'
  );
  
  // Test 10: Check if amCharts is available
  const hasAmCharts = typeof am5 !== 'undefined' && typeof am5xy !== 'undefined' && typeof am5themes_Animated !== 'undefined';
  addTest(
    'amCharts Libraries Available',
    hasAmCharts,
    hasAmCharts ? 'amCharts libraries loaded' : 'amCharts libraries not loaded'
  );
  
  // Test 11: Check pending initialization system
  addTest(
    'Pending Initialization System Ready',
    typeof window.pendingHorizontalBarChartInits !== 'undefined' || true, // Will be created on first drag
    'Pending initialization system ready'
  );

  // Test 12: Test onclick functionality
  if (horizontalBarChartWidget) {
    try {
      // Check if the onclick function can be called
      const onclickAttr = horizontalBarChartWidget.getAttribute('onclick');
      const functionName = onclickAttr ? onclickAttr.replace('()', '') : null;

      if (functionName && typeof window[functionName] === 'function') {
        addTest(
          'Onclick Function Callable',
          true,
          `${functionName} function is available and callable`
        );
      } else {
        addTest(
          'Onclick Function Callable',
          false,
          `Onclick function ${functionName} not found or not callable`
        );
      }
    } catch (error) {
      addTest(
        'Onclick Function Callable',
        false,
        `Error testing onclick: ${error.message}`
      );
    }
  }
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! Horizontal Bar Chart is ready for use.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the implementation.');
  }
  
  return results;
}

// Auto-run test if in development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
  // Wait for scripts to load
  setTimeout(() => {
    testHorizontalBarChart();
  }, 3000);
}

// Export for manual testing
window.testHorizontalBarChart = testHorizontalBarChart;
