// Configure PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc =
  "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js";

// Add a PDF Viewer widget
function addPdfViewerWidget() {
  console.log("Adding PDF viewer widget");
  const pdfId = "pdf-" + Date.now();
  const settingsId = "settings-" + pdfId;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 8,
    content: `
      <div class="pdf-viewer-widget p-2">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
            PDF Viewer
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#${settingsId}"
                    aria-controls="${settingsId}">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div id="${pdfId}" class="pdf-container d-flex flex-column">
          <div class="text-center text-muted flex-grow-1 d-flex align-items-center justify-content-center">
            <div>
              <i class="las la-file-upload la-3x mb-3"></i>
              <p>No PDF selected.<br>Use settings to upload a PDF file.</p>
            </div>
          </div>
          <div class="pdf-controls d-none">
            <div class="d-flex justify-content-between align-items-center mt-2">
              <button class="btn btn-sm btn-outline-primary" onclick="previousPage('${pdfId}')">
                <i class="las la-chevron-left"></i> Previous
              </button>
              <span class="page-info">Page <span id="${pdfId}-page-num"></span> of <span id="${pdfId}-page-count"></span></span>
              <button class="btn btn-sm btn-outline-primary" onclick="nextPage('${pdfId}')">
                Next <i class="las la-chevron-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    `,
  });

  // Create settings panel in the offcanvas container
  const offcanvasContainer = document.getElementById("offcanvasContainer");
  const settingsPanel = document.createElement("div");
  settingsPanel.className = "offcanvas offcanvas-end";
  settingsPanel.id = settingsId;
  settingsPanel.setAttribute("tabindex", "-1");
  settingsPanel.innerHTML = `
    <div class="offcanvas-header">
      <h5 class="offcanvas-title">PDF Viewer Settings</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- PDF Upload -->
      <div class="mb-3">
        <label for="${settingsId}-upload" class="form-label">Upload PDF</label>
        <input class="form-control" type="file" id="${settingsId}-upload" accept=".pdf" onchange="loadPDF('${pdfId}', this)">
      </div>

      <!-- PDF URL -->
      <div class="mb-3">
        <label for="${settingsId}-url" class="form-label">Or Enter PDF URL</label>
        <input type="text" class="form-control" id="${settingsId}-url" placeholder="https://example.com/document.pdf">
      </div>

      <!-- Zoom Level -->
      <div class="mb-3">
        <label class="form-label">Zoom Level</label>
        <select class="form-select" id="${settingsId}-zoom">
          <option value="0.5">50%</option>
          <option value="0.75">75%</option>
          <option value="1" selected>100%</option>
          <option value="1.25">125%</option>
          <option value="1.5">150%</option>
          <option value="2">200%</option>
        </select>
      </div>

      <!-- Page Fit -->
      <div class="mb-3">
        <label class="form-label">Page Fit</label>
        <select class="form-select" id="${settingsId}-fit">
          <option value="width">Fit to Width</option>
          <option value="height">Fit to Height</option>
          <option value="page">Fit to Page</option>
        </select>
      </div>

      <!-- Background Color -->
      <div class="mb-3">
        <label for="${settingsId}-bgcolor" class="form-label">Background Color</label>
        <input type="color" class="form-control form-control-color" id="${settingsId}-bgcolor" value="#ffffff">
      </div>

      <!-- Border -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-border">
          <label class="form-check-label" for="${settingsId}-border">Show Border</label>
        </div>
      </div>

      <!-- Border Radius -->
      <div class="mb-3">
        <label class="form-label">Border Radius (px)</label>
        <input type="range" class="form-range" min="0" max="50" value="0" id="${settingsId}-radius">
      </div>

      <!-- Apply Button -->
      <button class="btn btn-primary w-100" onclick="applyPdfSettings('${pdfId}', '${settingsId}')">
        Apply Changes
      </button>
    </div>
  `;
  offcanvasContainer.appendChild(settingsPanel);

  // Initialize the PDF viewer
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing PDF viewer");
      initPdfViewer(pdfId);
    } catch (error) {
      console.error("Error initializing PDF viewer:", error);
    }
  }, 100);

  return widget;
}

// Function to initialize the PDF viewer
function initPdfViewer(pdfId) {
  console.log("Initializing PDF viewer:", pdfId);
  const container = document.getElementById(pdfId);

  if (!container) {
    console.error("PDF container not found:", pdfId);
    return;
  }

  // Initialize PDF.js if not already done
  if (!pdfjsLib.getDocument) {
    pdfjsLib.GlobalWorkerOptions.workerSrc =
      "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js";
  }

  // Store viewer state in the container
  container.pdfState = {
    pdfDoc: null,
    pageNum: 1,
    pageRendering: false,
    pageNumPending: null,
    scale: 1.0,
  };

  console.log("PDF viewer initialized:", pdfId);
}

// Function to load a PDF from file input or URL
async function loadPDF(pdfId, source) {
  const container = document.getElementById(pdfId);
  if (!container) return;

  let pdfUrl;
  if (source instanceof HTMLInputElement && source.files.length > 0) {
    pdfUrl = URL.createObjectURL(source.files[0]);
  } else if (typeof source === "string") {
    pdfUrl = source;
  } else {
    console.error("Invalid PDF source");
    return;
  }

  try {
    const pdfDoc = await pdfjsLib.getDocument(pdfUrl).promise;
    container.pdfState.pdfDoc = pdfDoc;
    container.pdfState.pageNum = 1;

    // Update page count display
    document.getElementById(`${pdfId}-page-count`).textContent =
      pdfDoc.numPages;

    // Show PDF controls
    container.querySelector(".pdf-controls").classList.remove("d-none");

    // Render first page
    renderPage(pdfId);
  } catch (error) {
    console.error("Error loading PDF:", error);
    container.innerHTML = `
      <div class="text-center text-danger">
        <i class="las la-exclamation-circle la-3x mb-3"></i>
        <p>Error loading PDF.<br>Please try again.</p>
      </div>
    `;
  }
}

// Function to render a PDF page
async function renderPage(pdfId) {
  const container = document.getElementById(pdfId);
  if (!container || !container.pdfState) return;

  const state = container.pdfState;
  if (state.pageRendering) {
    state.pageNumPending = state.pageNum;
    return;
  }

  state.pageRendering = true;

  try {
    // Get the page
    const page = await state.pdfDoc.getPage(state.pageNum);

    // Create canvas if it doesn't exist
    let canvas = container.querySelector("canvas");
    if (!canvas) {
      canvas = document.createElement("canvas");
      container.insertBefore(canvas, container.firstChild);
    }

    // Prepare canvas using PDF page dimensions
    const viewport = page.getViewport({ scale: state.scale });
    canvas.height = viewport.height;
    canvas.width = viewport.width;

    // Render PDF page into canvas context
    const renderContext = {
      canvasContext: canvas.getContext("2d"),
      viewport: viewport,
    };

    const renderTask = page.render(renderContext);
    await renderTask.promise;

    state.pageRendering = false;
    document.getElementById(`${pdfId}-page-num`).textContent = state.pageNum;

    if (state.pageNumPending !== null) {
      state.pageNum = state.pageNumPending;
      state.pageNumPending = null;
      renderPage(pdfId);
    }
  } catch (error) {
    console.error("Error rendering PDF page:", error);
    state.pageRendering = false;
  }
}

// Functions to navigate PDF pages
function previousPage(pdfId) {
  const container = document.getElementById(pdfId);
  if (!container || !container.pdfState) return;

  if (container.pdfState.pageNum <= 1) return;
  container.pdfState.pageNum--;
  renderPage(pdfId);
}

function nextPage(pdfId) {
  const container = document.getElementById(pdfId);
  if (!container || !container.pdfState) return;

  if (container.pdfState.pageNum >= container.pdfState.pdfDoc.numPages) return;
  container.pdfState.pageNum++;
  renderPage(pdfId);
}

// Function to apply PDF viewer settings
function applyPdfSettings(pdfId, settingsId) {
  const container = document.getElementById(pdfId);
  if (!container) return;

  const urlInput = document.getElementById(`${settingsId}-url`);
  const zoomLevel = parseFloat(
    document.getElementById(`${settingsId}-zoom`).value
  );
  const fitMode = document.getElementById(`${settingsId}-fit`).value;
  const bgcolor = document.getElementById(`${settingsId}-bgcolor`).value;
  const showBorder = document.getElementById(`${settingsId}-border`).checked;
  const borderRadius = document.getElementById(`${settingsId}-radius`).value;

  // Apply zoom level
  if (container.pdfState) {
    container.pdfState.scale = zoomLevel;
    renderPage(pdfId);
  }

  // Apply background color
  container.style.backgroundColor = bgcolor;

  // Apply border
  if (showBorder) {
    container.style.border = "1px solid #dee2e6";
  } else {
    container.style.border = "none";
  }

  // Apply border radius
  container.style.borderRadius = `${borderRadius}px`;

  // Load PDF from URL if provided
  if (urlInput.value.trim()) {
    loadPDF(pdfId, urlInput.value.trim());
  }

  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(
    document.getElementById(settingsId)
  );
  offcanvas.hide();
}

// Export the functions
window.addPdfViewerWidget = addPdfViewerWidget;
window.initPdfViewer = initPdfViewer;
window.loadPDF = loadPDF;
window.renderPage = renderPage;
window.previousPage = previousPage;
window.nextPage = nextPage;
window.applyPdfSettings = applyPdfSettings;
