let bubbleChartCounter = 0;

// Add a bubble chart widget using amCharts v5
function addBubbleChartWidget(initialConfig = {}) {
  const widgetId = `bubble-chart-${bubbleChartCounter++}`;
  const chartId = `bubble-chart-container-${widgetId}`;
  const settingsId = `bubbleChartSettings-${widgetId}`;

  const config = {
    title: initialConfig.title || "Bubble Chart",
    data: initialConfig.data || generateInitialBubbleData(),
    minBubbleSize: initialConfig.minBubbleSize || 2,
    maxBubbleSize: initialConfig.maxBubbleSize || 20,
    showGrid:
      initialConfig.showGrid !== undefined ? initialConfig.showGrid : true,
    showLegend:
      initialConfig.showLegend !== undefined ? initialConfig.showLegend : false,
    xAxisTitle: initialConfig.xAxisTitle || "X Axis",
    yAxisTitle: initialConfig.yAxisTitle || "Y Axis",
    valueField: initialConfig.valueField || "value",
    categoryXField: initialConfig.categoryXField || "x",
    categoryYField: initialConfig.categoryYField || "y",
    seriesName: initialConfig.seriesName || "Series",
    // Metadata fields with default placeholders
    notes:
      initialConfig.notes ||
      "Default Notes: Please provide specific notes when creating the widget.",
    source:
      initialConfig.source ||
      "Default Source: Please provide a specific source.",
    lastUpdate: initialConfig.lastUpdate || "N/A",
    nextUpdate: initialConfig.nextUpdate || "N/A",
  };

  // DEBUG: Log the config object right before generating HTML
  console.log(
    "DEBUG: Config object before HTML generation:",
    JSON.stringify(config, null, 2)
  );

  const grid = window.grid;
  const newWidget = grid.addWidget({
    w: initialConfig.w || 6,
    h: initialConfig.h || 8,
    content: `
      <div class=" bubble-chart-widget widget p-2" id="${widgetId}">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div class="widget-title editable-title" data-editable="true" title="Click to edit title">
              
              <span>${config.title}</span>
          </div>
          <div class="widget-actions">
            <button class="btn btn-link" data-bs-toggle="offcanvas" data-bs-target="#${settingsId}" aria-controls="${settingsId}">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-link ms-1" onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body" style="height: 100%;">
          <div id="${chartId}" class="chart-container" style="width: 100%; height: 100%;"></div>
        </div>
        <!-- Widget Footer for Metadata (Using config object with defaults) -->
        <div class="widget-footer" style="padding: 0.5rem 1rem; border-top: 1px solid #e5e9f0; font-size: 10px; color: #6c757d; text-align: left;">
          ${
            config.notes
              ? `<div><i class="las la-clipboard"></i> Notes : ${config.notes}</div>`
              : ""
          }
          ${
            config.source
              ? `<div><i class="las la-database"></i> Source : ${config.source}</div>`
              : ""
          }
          ${
            config.lastUpdate || config.nextUpdate
              ? `
          <div class="d-flex mt-1">
            ${
              config.lastUpdate
                ? `<span><i class="las la-calendar-alt"></i> Last update : ${config.lastUpdate}</span>`
                : "<span></span>"
            }
            ${
              config.nextUpdate
                ? `<span class="ms-3"><i class="las la-calendar-plus"></i> Next update : ${config.nextUpdate}</span>`
                : ""
            }
          </div>
          `
              : ""
          }
        </div>
      </div>
    `,
  });

  // Create and append settings offcanvas
  createBubbleChartSettingsOffcanvas(settingsId, widgetId, chartId, config);

  // Store config on the widget element
  const widgetElement = document.getElementById(widgetId);
  widgetElement.widgetConfig = config;
  widgetElement.chartId = chartId;

  // Initialize the chart after a short delay to ensure DOM is ready and amCharts is loaded
  setTimeout(() => {
    if (typeof am5 !== "undefined") {
      initializeBubbleChart(chartId, config);
    } else {
      console.error("amCharts is not loaded properly");
    }
  }, 100);

  // Make title editable
  makeTitleEditable(widgetId);
}

function initializeBubbleChart(chartId, config) {
  const chartContainer = document.getElementById(chartId);
  if (!chartContainer || typeof am5 === "undefined") {
    console.error(
      `Chart container not found or amCharts not loaded: ${chartId}`
    );
    return;
  }

  // DEBUG: Log configuration
  console.log("Initializing bubble chart with ID:", chartId);
  console.log("Config data:", JSON.stringify(config.data)); // Log data
  console.log(
    `Bubble sizes: min=${config.minBubbleSize}, max=${config.maxBubbleSize}`
  ); // Log sizes

  // Dispose existing chart if any
  if (chartContainer.chart) {
    chartContainer.chart.dispose();
  }

  try {
    // Create root element
    const root = am5.Root.new(chartId);
    chartContainer.chart = root; // Store root instance

    // Set themes
    root.setThemes([am5themes_Animated.new(root)]);

    // Create chart
    const chart = root.container.children.push(
      am5xy.XYChart.new(root, {
        panX: true,
        panY: true,
        wheelX: "none",
        wheelY: "none",
        maxTooltipDistance: 0,
      })
    );

    // Apply brand colors if available, directly to the chart object
    if (window.chartConfig && window.chartConfig.brandColors) {
      const am5BrandColors = window.chartConfig.brandColors.map((hex) =>
        am5.color(hex)
      );
      // Set the color sequence directly on the chart's colors property
      chart.get("colors").set("colors", am5BrandColors);
      console.log(
        "Applied brand colors to chart:",
        window.chartConfig.brandColors
      );
    } else {
      console.warn(
        "Brand colors (window.chartConfig.brandColors) not found. Using default theme colors."
      );
    }

    // Create axes
    const xAxis = chart.xAxes.push(
      am5xy.ValueAxis.new(root, {
        renderer: am5xy.AxisRendererX.new(root, {
          minGridDistance: 50,
          strokeOpacity: config.showGrid ? 0.1 : 0,
          grid: {
            template: {
              strokeOpacity: config.showGrid ? 0.1 : 0,
            },
          },
        }),
        tooltip: am5.Tooltip.new(root, {}),
        title: am5.Label.new(root, {
          text: config.xAxisTitle,
          fontSize: 12,
          fontWeight: "500",
          x: am5.p50,
          centerX: am5.p50,
        }),
      })
    );

    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        renderer: am5xy.AxisRendererY.new(root, {
          strokeOpacity: config.showGrid ? 0.1 : 0,
          grid: {
            template: {
              strokeOpacity: config.showGrid ? 0.1 : 0,
            },
          },
        }),
        tooltip: am5.Tooltip.new(root, {}),
        title: am5.Label.new(root, {
          text: config.yAxisTitle,
          fontSize: 12,
          fontWeight: "500",
          rotation: -90,
          y: am5.p50,
          centerX: am5.p50,
        }),
      })
    );

    // Create series
    const series = chart.series.push(
      am5xy.LineSeries.new(root, {
        calculateAggregates: true,
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: config.categoryYField,
        valueXField: config.categoryXField,
        valueField: config.valueField,
        seriesTooltipTarget: "bullet",
        name: config.seriesName,
        tooltip: am5.Tooltip.new(root, {
          labelText: "[{name}]\nX:{valueX}, Y:{valueY}\nValue:{value}",
        }),
      })
    );

    series.strokes.template.set("visible", false); // Hide the line

    // Create a template for the circle graphic
    const circleTemplate = am5.Template.new({});

    // Add bullets using a function that creates the circle
    series.bullets.push(function () {
      // Determine the fill color
      let bubbleFillColor;
      if (
        window.chartConfig &&
        window.chartConfig.brandColors &&
        window.chartConfig.brandColors.length > 0
      ) {
        bubbleFillColor = am5.color(window.chartConfig.brandColors[0]); // Use first brand color
      } else {
        bubbleFillColor = series.get("fill"); // Fallback to series color
      }

      const graphics = am5.Circle.new(
        root,
        {
          // Set fill color directly and ensure opacity
          fill: bubbleFillColor,
          fillOpacity: 1, // Ensure bubbles are not transparent
          strokeOpacity: 0,
          templateField: "bulletSettings",
        },
        circleTemplate
      );
      return am5.Bullet.new(root, {
        sprite: graphics,
      });
    });

    // DEBUG: Log the series object before setting heat rules
    console.log("Attempting to set heatRules on series:", series);
    if (series && typeof series.set === "function") {
      console.log("Series object appears valid and has a 'set' method.");
    } else {
      console.error(
        "Series object is invalid or missing the 'set' method before setting heatRules.",
        series
      );
    }

    // Set heat rules on the series, targeting the circle template
    series.set("heatRules", [
      {
        target: circleTemplate, // Target the graphic template
        min: config.minBubbleSize,
        max: config.maxBubbleSize,
        dataField: "value",
        key: "radius",
      },
    ]);

    // Set data
    series.data.setAll(config.data);

    // Add cursor
    chart.set(
      "cursor",
      am5xy.XYCursor.new(root, {
        xAxis: xAxis,
        yAxis: yAxis,
        snapToSeries: [series],
      })
    );

    // Add legend if enabled
    if (config.showLegend) {
      const legend = chart.children.push(
        am5.Legend.new(root, {
          centerX: am5.p50,
          x: am5.p50,
        })
      );
      legend.data.setAll([series]);
    }

    // Make stuff animate on load
    series.appear(1000);
    chart.appear(1000, 100);

    // Add resize observer for the chart container
    const chartContainerElement = document.getElementById(chartId);
    if (chartContainerElement) {
      const resizeObserver = new ResizeObserver(() => {
        root.resize();
      });
      resizeObserver.observe(chartContainerElement);
      chartContainerElement.resizeObserver = resizeObserver; // Store observer for cleanup
    }
  } catch (error) {
    console.error("Error initializing chart:", error);
  }
}

function createBubbleChartSettingsOffcanvas(
  settingsId,
  widgetId,
  chartId,
  currentConfig
) {
  const offcanvasContainer =
    document.getElementById("offcanvasContainer") || document.body;

  const offcanvasHtml = `
        <div class="offcanvas offcanvas-end" tabindex="-1" id="${settingsId}" aria-labelledby="${settingsId}Label">
            <div class="offcanvas-header">
                <h5 class="offcanvas-title" id="${settingsId}Label">Bubble Chart Settings</h5>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body">
                <!-- Chart Appearance -->
                <h6 class="mb-3">Chart Appearance</h6>

                <!-- Chart Title -->
                <div class="mb-3">
                    <label class="form-label">Chart Title</label>
                    <input type="text" class="form-control form-control-sm" id="${settingsId}-chartTitle" value="${
    currentConfig.title
  }">
                </div>

                <!-- Axis Titles -->
                 <div class="mb-3">
                    <label class="form-label">X-Axis Title</label>
                    <input type="text" class="form-control form-control-sm" id="${settingsId}-xAxisTitle" value="${
    currentConfig.xAxisTitle
  }">
                </div>
                 <div class="mb-3">
                    <label class="form-label">Y-Axis Title</label>
                    <input type="text" class="form-control form-control-sm" id="${settingsId}-yAxisTitle" value="${
    currentConfig.yAxisTitle
  }">
                </div>

                 <!-- Bubble Size -->
                <div class="mb-3">
                    <label class="form-label">Min Bubble Size (%)</label>
                    <input type="range" class="form-range" min="1" max="10" value="${
                      currentConfig.minBubbleSize
                    }" id="${settingsId}-minBubbleSize">
                </div>
                 <div class="mb-3">
                    <label class="form-label">Max Bubble Size (%)</label>
                    <input type="range" class="form-range" min="10" max="50" value="${
                      currentConfig.maxBubbleSize
                    }" id="${settingsId}-maxBubbleSize">
                </div>

                 <!-- Toggles -->
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="${settingsId}-gridLines" ${
    currentConfig.showGrid ? "checked" : ""
  }>
                        <label class="form-check-label" for="${settingsId}-gridLines">Show Grid Lines</label>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="${settingsId}-legend" ${
    currentConfig.showLegend ? "checked" : ""
  }>
                        <label class="form-check-label" for="${settingsId}-legend">Show Legend</label>
                    </div>
                </div>

                <!-- Data Management -->
                <div class="mb-4">
                    <h6 class="mb-3">Data Management</h6>
                    <!-- Data Table Section -->
                    <div class="data-editor-section">
                      <div class="d-flex justify-content-between align-items-center mb-2">
                        <div class="section-title">Chart Data</div>
                        <button class="btn btn-sm btn-outline-primary" id="addBubbleChartDataRow-${widgetId}" onclick="addNewBubbleChartDataRow('${settingsId}')">
                          <i class="las la-plus"></i> Add Row
                        </button>
                      </div>
                      <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                        <table class="table table-sm" id="${settingsId}-dataTable">
                          <thead>
                            <tr>
                              <th style="width: 25%">X Value</th>
                              <th style="width: 25%">Y Value</th>
                              <th style="width: 25%">Bubble Size</th>
                              <th style="width: 25%">Actions</th>
                            </tr>
                          </thead>
                          <tbody id="${settingsId}-dataBody">
                            <!-- Data rows will be added dynamically -->
                          </tbody>
                        </table>
                      </div>
                    </div>
                </div>

                <!-- Metadata -->
                <div class="mb-4">
                    <h6 class="mb-3">Metadata</h6>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea class="form-control form-control-sm" id="${settingsId}-notes" rows="2">${
    currentConfig.notes
  }</textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Source</label>
                        <textarea class="form-control form-control-sm" id="${settingsId}-source" rows="2">${
    currentConfig.source
  }</textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Last Update</label>
                        <input type="text" class="form-control form-control-sm" id="${settingsId}-lastUpdate" value="${
    currentConfig.lastUpdate
  }">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Next Update</label>
                        <input type="text" class="form-control form-control-sm" id="${settingsId}-nextUpdate" value="${
    currentConfig.nextUpdate
  }">
                    </div>
                </div>

                <!-- Apply Button -->
                <button class="btn btn-primary w-100" onclick="applyBubbleChartSettings('${widgetId}', '${settingsId}', '${chartId}')">Apply Changes</button>
            </div>
        </div>
    `;

  // Append the offcanvas HTML to the container
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = offcanvasHtml;
  offcanvasContainer.appendChild(tempDiv.firstElementChild);

  // Populate data table
  populateBubbleChartDataTable(settingsId, currentConfig.data);
}

function populateBubbleChartDataTable(settingsId, data) {
  const tableBody = document.getElementById(`${settingsId}-dataBody`);
  if (!tableBody) return;
  tableBody.innerHTML = ""; // Clear existing rows

  data.forEach((item, index) => {
    const row = document.createElement("tr");
    row.innerHTML = `
            <td><input type="number" class="form-control form-control-sm" value="${item.x}"></td>
            <td><input type="number" class="form-control form-control-sm" value="${item.y}"></td>
            <td><input type="number" class="form-control form-control-sm" value="${item.value}"></td>
            <td>
                <button class="btn btn-sm btn-outline-danger" onclick="removeBubbleChartDataRow(this, '${settingsId}')">
                    <i class="las la-trash"></i>
                </button>
            </td>
        `;
    tableBody.appendChild(row);
  });
}

function addNewBubbleChartDataRow(settingsId) {
  const tableBody = document.getElementById(`${settingsId}-dataBody`);
  if (!tableBody) return;

  const row = document.createElement("tr");
  row.innerHTML = `
        <td><input type="number" class="form-control form-control-sm" value="0"></td>
        <td><input type="number" class="form-control form-control-sm" value="0"></td>
        <td><input type="number" class="form-control form-control-sm" value="10"></td>
        <td>
            <button class="btn btn-sm btn-outline-danger" onclick="removeBubbleChartDataRow(this, '${settingsId}')">
                <i class="las la-trash"></i>
            </button>
        </td>
    `;
  tableBody.appendChild(row);
}

function removeBubbleChartDataRow(button, settingsId) {
  button.closest("tr").remove();
}

// Helper function to update the widget footer content
function updateWidgetFooter(widgetElement, config) {
  const footerElement = widgetElement.querySelector(".widget-footer");
  if (!footerElement) {
    console.warn("Widget footer element not found for update.");
    return;
  }

  // Clear existing footer content
  footerElement.innerHTML = "";

  // Build new footer content based on config
  let footerHtml = "";
  if (config.notes) {
    footerHtml += `<div><i class="las la-clipboard"></i> Notes : ${config.notes}</div>`;
  }
  if (config.source) {
    footerHtml += `<div><i class="las la-database"></i> Source : ${config.source}</div>`;
  }
  if (config.lastUpdate || config.nextUpdate) {
    footerHtml += '<div class="d-flex mt-1">'; // Removed justify-content-between
    if (config.lastUpdate) {
      footerHtml += `<span><i class="las la-calendar-alt"></i> Last update : ${config.lastUpdate}</span>`;
    } else {
      footerHtml += "<span></span>";
    }
    if (config.nextUpdate) {
      footerHtml += `<span class="ms-3"><i class="las la-calendar-plus"></i> Next update : ${config.nextUpdate}</span>`; // Added ms-3 for spacing
    }
    footerHtml += "</div>";
  }

  // Set the new HTML
  footerElement.innerHTML = footerHtml;
}

function applyBubbleChartSettings(widgetId, settingsId, chartId) {
  const widgetElement = document.getElementById(widgetId);
  if (!widgetElement || !widgetElement.widgetConfig) {
    console.error("Widget element or config not found for ID:", widgetId);
    return;
  }

  const currentConfig = widgetElement.widgetConfig;

  // Get new settings from the offcanvas form
  currentConfig.title = document.getElementById(
    `${settingsId}-chartTitle`
  ).value;
  currentConfig.xAxisTitle = document.getElementById(
    `${settingsId}-xAxisTitle`
  ).value;
  currentConfig.yAxisTitle = document.getElementById(
    `${settingsId}-yAxisTitle`
  ).value;
  currentConfig.minBubbleSize = parseFloat(
    document.getElementById(`${settingsId}-minBubbleSize`).value
  );
  currentConfig.maxBubbleSize = parseFloat(
    document.getElementById(`${settingsId}-maxBubbleSize`).value
  );
  currentConfig.showGrid = document.getElementById(
    `${settingsId}-gridLines`
  ).checked;
  currentConfig.showLegend = document.getElementById(
    `${settingsId}-legend`
  ).checked;

  // Read metadata from settings
  currentConfig.notes = document.getElementById(`${settingsId}-notes`).value;
  currentConfig.source = document.getElementById(`${settingsId}-source`).value;
  currentConfig.lastUpdate = document.getElementById(
    `${settingsId}-lastUpdate`
  ).value;
  currentConfig.nextUpdate = document.getElementById(
    `${settingsId}-nextUpdate`
  ).value;

  // Read data from table
  const tableBody = document.getElementById(`${settingsId}-dataBody`);
  const newData = [];
  if (tableBody) {
    tableBody.querySelectorAll("tr").forEach((row) => {
      const inputs = row.querySelectorAll("input");
      if (inputs.length === 3) {
        newData.push({
          x: parseFloat(inputs[0].value) || 0,
          y: parseFloat(inputs[1].value) || 0,
          value: parseFloat(inputs[2].value) || 1,
        });
      }
    });
  }
  currentConfig.data = newData;

  // Update widget title in the header
  const titleSpan = widgetElement.querySelector(".widget-title span");
  if (titleSpan) {
    titleSpan.textContent = currentConfig.title;
  }

  // Update the widget footer
  updateWidgetFooter(widgetElement, currentConfig);

  // Re-initialize the chart with new settings
  initializeBubbleChart(chartId, currentConfig);

  // Close the offcanvas
  const offcanvasElement = document.getElementById(settingsId);
  const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvasElement);
  if (offcanvasInstance) {
    offcanvasInstance.hide();
  }

  console.log("Applied bubble chart settings:", currentConfig);
}

// Helper function to generate initial random data
function generateInitialBubbleData(count = 15) {
  const data = [];
  for (let i = 0; i < count; i++) {
    data.push({
      x: Math.round(Math.random() * 100),
      y: Math.round(Math.random() * 100),
      value: Math.round(Math.random() * 50) + 5, // Ensure value > 0
    });
  }
  return data;
}

// Helper function to make widget titles editable
function makeTitleEditable(widgetId) {
  const widgetElement = document.getElementById(widgetId);
  if (!widgetElement) return;

  const titleSpan = widgetElement.querySelector(".widget-title span");
  const titleContainer = widgetElement.querySelector(".widget-title");

  if (titleSpan && titleContainer) {
    titleContainer.addEventListener("click", function () {
      const currentText = titleSpan.textContent.trim();
      titleSpan.contentEditable = true;
      titleSpan.focus();

      // Create a range and selection
      const range = document.createRange();
      range.selectNodeContents(titleSpan);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);

      // Handle focus out
      function onFocusOut() {
        titleSpan.contentEditable = false;
        titleSpan.removeEventListener("focusout", onFocusOut);
        titleSpan.removeEventListener("keydown", onKeyDown);

        // Update the widget config with new title
        if (widgetElement.widgetConfig) {
          widgetElement.widgetConfig.title = titleSpan.textContent.trim();
        }
      }

      // Handle enter key
      function onKeyDown(e) {
        if (e.key === "Enter") {
          e.preventDefault();
          titleSpan.blur();
        }
      }

      titleSpan.addEventListener("focusout", onFocusOut);
      titleSpan.addEventListener("keydown", onKeyDown);
    });
  }
}

// Export the functions for global use
window.addBubbleChartWidget = addBubbleChartWidget;
window.applyBubbleChartSettings = applyBubbleChartSettings;
window.addNewBubbleChartDataRow = addNewBubbleChartDataRow;
