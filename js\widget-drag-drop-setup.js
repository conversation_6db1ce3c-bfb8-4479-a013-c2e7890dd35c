/**
 * Widget Drag and Drop Setup
 * Centralized configuration for GridStack drag-in functionality
 * Include this script to enable drag and drop for all widgets
 */

// Widget drag and drop configuration
const WidgetDragDropSetup = {
  
  /**
   * Initialize all widget drag and drop functionality
   */
  init() {
    console.log('🚀 Initializing Widget Drag & Drop Setup...');
    
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupAllWidgets());
    } else {
      this.setupAllWidgets();
    }
  },

  /**
   * Setup drag-in for all widget types
   */
  setupAllWidgets() {
    console.log('📦 Setting up drag-in for all widgets...');
    
    // Setup each widget type
    this.setupTextWidget();
    this.setupTableWidget();
    this.setupSectionContainerWidget();
    // Pie chart setup moved to js/pieChartWidget.js for consistency
    this.setupBarChartWidget();
    this.setupLineChartWidget();
    this.setupStackedColumnChartWidget();
    this.setupPercentStackedColumnChartWidget();
    this.setupAreaChartWidget();
    this.setupPdfViewerWidget();
    
    // Hidden widgets (commented out but available)
    // this.setupLineSeparatorWidget();
    // this.setupNotesSectionWidget();
    // this.setupImageWidget();
    
    console.log('✅ All widget drag-in setup complete!');
  },

  /**
   * Text Widget Drag Setup
   */
  setupTextWidget() {
    const textSidebarContent = [
      {
        w: 4,
        h: 4,
        get content() {
          const textId = "text-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
          const settingsId = "settings-" + textId;
          
          if (typeof getTextWidgetMarkup === "function") {
            return getTextWidgetMarkup({ textId, settingsId });
          }
          
          return `
            <div class="text-widget p-2">
              <div class="widget-header mb-2 fw-bold">
                <i class="las la-font"></i> Text Widget
              </div>
              <div class="text-container">
                <div class="text-content" contenteditable="true" style="
                  min-height: 100px;
                  padding: 10px;
                  border: 1px solid #e0e0e0;
                  border-radius: 4px;
                  outline: none;
                  font-family: Arial, sans-serif;
                  font-size: 14px;
                  line-height: 1.5;
                  color: #333;
                  background-color: #fff;
                ">
                  <p>Click here to edit text...</p>
                </div>
              </div>
            </div>
          `;
        },
      },
    ];

    GridStack.setupDragIn(
      '.widget-item[data-widget-type="text"]',
      undefined,
      textSidebarContent
    );
    console.log('📝 Text widget drag-in setup complete');
  },

  /**
   * Table Widget Drag Setup
   */
  setupTableWidget() {
    const tableSidebarContent = [
      {
        w: 6,
        h: 6,
        get content() {
          const tableId = "table-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
          const settingsId = "settings-" + tableId;
          
          if (typeof getTableWidgetMarkup === "function") {
            return getTableWidgetMarkup({ tableId, settingsId });
          }
          
          return `
            <div class="table-widget p-2">
              <div class="widget-header mb-2 fw-bold">
                <i class="las la-table"></i> Table Widget
              </div>
              <div class="table-container">
                <div class="table-responsive">
                  <table class="table table-bordered table-striped">
                    <thead>
                      <tr>
                        <th>Column 1</th>
                        <th>Column 2</th>
                        <th>Column 3</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td contenteditable="true">Row 1, Col 1</td>
                        <td contenteditable="true">Row 1, Col 2</td>
                        <td contenteditable="true">Row 1, Col 3</td>
                      </tr>
                      <tr>
                        <td contenteditable="true">Row 2, Col 1</td>
                        <td contenteditable="true">Row 2, Col 2</td>
                        <td contenteditable="true">Row 2, Col 3</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          `;
        },
      },
    ];

    GridStack.setupDragIn(
      '.widget-item[data-widget-type="table"]',
      undefined,
      tableSidebarContent
    );
    console.log('📊 Table widget drag-in setup complete');
  },

  /**
   * Section Container Widget Drag Setup
   */
  setupSectionContainerWidget() {
    const sectionContainerSidebarContent = [
      {
        w: 6,
        h: 4,
        get content() {
          const sectionId = "section-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
          
          if (typeof getSectionContainerWidgetMarkup === "function") {
            return getSectionContainerWidgetMarkup(sectionId);
          }
          
          return `
            <div class="section-container-widget">
              <div class="section-content" id="${sectionId}">
                <div class="section-header">
                  <h5>Section Container</h5>
                </div>
                <div class="nested-grid-container">
                  <div class="grid-stack nested-grid"></div>
                </div>
              </div>
            </div>
          `;
        },
      },
    ];

    GridStack.setupDragIn(
      '.widget-item[data-widget-type="section-container"]',
      undefined,
      sectionContainerSidebarContent
    );
    console.log('📦 Section container widget drag-in setup complete');
  },

  // Pie Chart Widget setup moved to js/pieChartWidget.js for consistency

  /**
   * Bar Chart Widget Drag Setup
   */
  setupBarChartWidget() {
    const barChartSidebarContent = [
      {
        w: 6,
        h: 6,
        get content() {
          const chartId = "barchart-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
          
          if (typeof getBarChartWidgetMarkup === "function") {
            return getBarChartWidgetMarkup(chartId);
          }
          
          return `
            <div class="bar-chart-widget">
              <div class="widget-header">
                <i class="las la-chart-bar"></i> Bar Chart
              </div>
              <div class="widget-body">
                <div id="${chartId}" class="chart-container"></div>
              </div>
            </div>
          `;
        },
      },
    ];

    GridStack.setupDragIn(
      '.widget-item[data-widget-type="bar-chart"]',
      undefined,
      barChartSidebarContent
    );
    console.log('📊 Bar chart widget drag-in setup complete');
  },

  /**
   * Line Chart Widget Drag Setup
   */
  setupLineChartWidget() {
    const lineChartSidebarContent = [
      {
        w: 6,
        h: 6,
        get content() {
          const chartId = "linechart-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
          
          if (typeof getLineChartWidgetMarkup === "function") {
            return getLineChartWidgetMarkup(chartId);
          }
          
          return `
            <div class="line-chart-widget">
              <div class="widget-header">
                <i class="las la-chart-line"></i> Line Chart
              </div>
              <div class="widget-body">
                <div id="${chartId}" class="chart-container"></div>
              </div>
            </div>
          `;
        },
      },
    ];

    GridStack.setupDragIn(
      '.widget-item[data-widget-type="line-chart"]',
      undefined,
      lineChartSidebarContent
    );
    console.log('📈 Line chart widget drag-in setup complete');
  },

  /**
   * Stacked Column Chart Widget Drag Setup
   */
  setupStackedColumnChartWidget() {
    const stackedColumnChartSidebarContent = [
      {
        w: 6,
        h: 6,
        get content() {
          const chartId = "stackedcolumnchart-" + Date.now() + "-" + Math.floor(Math.random() * 100000);

          if (typeof getStackedColumnChartWidgetMarkup === "function") {
            return getStackedColumnChartWidgetMarkup(chartId);
          }

          return `
            <div class="stacked-column-chart-widget">
              <div class="widget-header">
                <i class="las la-layer-group"></i> Stacked Column Chart
              </div>
              <div class="widget-body">
                <div id="${chartId}" class="chart-container"></div>
              </div>
            </div>
          `;
        },
      },
    ];

    GridStack.setupDragIn(
      '.widget-item[data-widget-type="stacked-column-chart"]',
      undefined,
      stackedColumnChartSidebarContent
    );
    console.log('📊 Stacked column chart widget drag-in setup complete');
  },

  /**
   * Percent Stacked Column Chart Widget Drag Setup
   */
  setupPercentStackedColumnChartWidget() {
    const percentStackedColumnChartSidebarContent = [
      {
        w: 6,
        h: 6,
        get content() {
          const chartId = "percentstackedcolumnchart-" + Date.now() + "-" + Math.floor(Math.random() * 100000);

          if (typeof getPercentStackedColumnChartWidgetMarkup === "function") {
            return getPercentStackedColumnChartWidgetMarkup(chartId);
          }

          return `
            <div class="percent-stacked-column-chart-widget">
              <div class="widget-header">
                <i class="las la-percentage"></i> 100% Stacked Column Chart
              </div>
              <div class="widget-body">
                <div id="${chartId}" class="chart-container"></div>
              </div>
            </div>
          `;
        },
      },
    ];

    GridStack.setupDragIn(
      '.widget-item[data-widget-type="percent-stacked-column-chart"]',
      undefined,
      percentStackedColumnChartSidebarContent
    );
    console.log('📊 Percent stacked column chart widget drag-in setup complete');
  },

  /**
   * Area Chart Widget Drag Setup
   */
  setupAreaChartWidget() {
    const areaChartSidebarContent = [
      {
        w: 6,
        h: 6,
        get content() {
          const chartId = "areachart-" + Date.now() + "-" + Math.floor(Math.random() * 100000);

          if (typeof getAreaChartWidgetMarkup === "function") {
            return getAreaChartWidgetMarkup(chartId);
          }

          return `
            <div class="area-chart-widget">
              <div class="widget-header">
                <i class="las la-chart-area"></i> Area Chart
              </div>
              <div class="widget-body">
                <div id="${chartId}" class="chart-container"></div>
              </div>
            </div>
          `;
        },
      },
    ];

    GridStack.setupDragIn(
      '.widget-item[data-widget-type="area-chart"]',
      undefined,
      areaChartSidebarContent
    );
    console.log('📈 Area chart widget drag-in setup complete');
  },

  /**
   * PDF Viewer Widget Drag Setup
   */
  setupPdfViewerWidget() {
    const pdfViewerSidebarContent = [
      {
        w: 8,
        h: 8,
        get content() {
          const pdfId = "pdf-viewer-" + Date.now() + "-" + Math.floor(Math.random() * 100000);

          if (typeof getPdfViewerWidgetMarkup === "function") {
            return getPdfViewerWidgetMarkup(pdfId);
          }

          return `
            <div class="pdf-viewer-widget">
              <div class="widget-header">
                <i class="las la-file-pdf"></i> PDF Viewer
              </div>
              <div class="widget-body">
                <div id="${pdfId}" class="pdf-container">
                  <p>PDF Viewer - Drop a PDF file to view</p>
                </div>
              </div>
            </div>
          `;
        },
      },
    ];

    GridStack.setupDragIn(
      '.widget-item[data-widget-type="pdf-viewer"]',
      undefined,
      pdfViewerSidebarContent
    );
    console.log('📄 PDF viewer widget drag-in setup complete');
  },

  /**
   * Line Separator Widget Drag Setup (Hidden by default)
   */
  setupLineSeparatorWidget() {
    const lineSeparatorSidebarContent = [
      {
        w: 2,
        h: 1,
        get content() {
          const separatorId = "line-separator-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
          const settingsId = "settings-" + separatorId;

          if (typeof getLineSeparatorWidgetMarkup === "function") {
            return getLineSeparatorWidgetMarkup({ separatorId, settingsId });
          }

          return `
            <div class="line-separator-widget p-2">
              <div class="widget-header mb-2 fw-bold">
                <i class="las la-minus"></i> Line Separator
              </div>
              <div class="line-separator-container">
                <div class="line-separator horizontal solid" style="
                  border-top: 2px solid #333;
                  width: 100%;
                  height: 0;
                  margin: 20px 0;
                "></div>
              </div>
            </div>
          `;
        },
      },
    ];

    GridStack.setupDragIn(
      '.widget-item[data-widget-type="line-separator"]',
      undefined,
      lineSeparatorSidebarContent
    );
    console.log('➖ Line separator widget drag-in setup complete');
  },

  /**
   * Notes Section Widget Drag Setup (Hidden by default)
   */
  setupNotesSectionWidget() {
    const notesSectionSidebarContent = [
      {
        w: 6,
        h: 2,
        get content() {
          const notesId = "notes-section-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
          const settingsId = "settings-" + notesId;

          if (typeof getNotesSectionWidgetMarkup === "function") {
            return getNotesSectionWidgetMarkup({ notesId, settingsId });
          }

          return `
            <div class="notes-section-widget p-2">
              <div class="widget-header mb-2 fw-bold">
                <i class="las la-sticky-note"></i> Notes Section
              </div>
              <div class="notes-section-container">
                <div class="notes-content">
                  <p>Notes section content...</p>
                </div>
              </div>
            </div>
          `;
        },
      },
    ];

    GridStack.setupDragIn(
      '.widget-item[data-widget-type="notes-section"]',
      undefined,
      notesSectionSidebarContent
    );
    console.log('📝 Notes section widget drag-in setup complete');
  },

  /**
   * Image Widget Drag Setup (Hidden by default)
   */
  setupImageWidget() {
    const imageSidebarContent = [
      {
        w: 4,
        h: 4,
        get content() {
          const imageId = "image-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
          const settingsId = "settings-" + imageId;

          if (typeof getImageWidgetMarkup === "function") {
            return getImageWidgetMarkup({ imageId, settingsId });
          }

          return `
            <div class="image-widget p-2">
              <div class="widget-header mb-2 fw-bold">
                <i class="las la-image"></i> Image Widget
              </div>
              <div class="image-container">
                <div class="image-placeholder" style="
                  width: 100%;
                  height: 200px;
                  background: #f8f9fa;
                  border: 2px dashed #dee2e6;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: #6c757d;
                ">
                  <i class="las la-image" style="font-size: 48px;"></i>
                </div>
              </div>
            </div>
          `;
        },
      },
    ];

    GridStack.setupDragIn(
      '.widget-item[data-widget-type="image"]',
      undefined,
      imageSidebarContent
    );
    console.log('🖼️ Image widget drag-in setup complete');
  }
};

// Auto-initialize when script loads
WidgetDragDropSetup.init();

// Export for manual initialization if needed
window.WidgetDragDropSetup = WidgetDragDropSetup;
