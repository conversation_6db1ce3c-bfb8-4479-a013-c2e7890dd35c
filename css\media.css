@media (max-width: 767.98px) and (min-width: 576px) {
    /* .slide-box div {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 17%;
} */
}

@media (max-width: 991.98px) and (min-width: 768px) {
    /* .slide-box div {
    -ms-flex: 0 0 33.3333%;
    flex: 0 0 33.3333%;
    max-width: 17.3333%;
} */
}

@media (min-width: 992px) {
    /* .slide-box div {
    -ms-flex: 0 0 15%;
    flex: 0 0 25%;
    max-width: 17%;
  } */
    .desktop {
        display: flex;
    }
    .mobile {
        display: none;
    }
}

@media (min-width: 990px) and (max-width: 1160px) {
    .themeBorderBottom {
        max-height: 90px;
    }
    .navbar-brand {
        display: flex;
        flex-direction: column;
    }
    nav.desktop a.navbar-brand span.tierName {
        margin-left: 0px;
        color: #00b19c;
        font-size: 16px;
        position: relative;
        bottom: 0px;
        left: 0;
    }
}

@media screen and (max-width: 1088px) {
    #tsc_nav_1 .nav-link {
        display: block;
        padding: 0.4rem 0.9rem;
    }
    .catimageSection {
        overflow: hidden;
        height: auto;
    }
    .catimageSection img {
        max-width: 100%;
        height: auto;
        width: 100%;
        margin-left: 0;
    }
    .newsDesc {
        display: block;
    }
    .newsGroup {
        margin-bottom: 60px;
        float: none;
        clear: both;
    }
    .desktop.navbar .input-group {
        width: 750px;
    }
}

@media (min-width: 320px) and (max-width: 1024px) {
    .porterTableHarvery .d-flex.align-items-center.justify-content-center div {
        flex-direction: column !important;
    }
    div.pie {
        margin: auto !important;
        margin-bottom: 10px !important;
    }
    .recBtn {
        background: #fff !important;
        border: none;
        font-size: 18px;
        position: absolute;
        top: 1px !important;
        right: 27px !important;
        cursor: pointer;
        color: #6c757d;
        z-index: 999;
        height: 28px !important;
        padding-top: 0px;
    }
    .slideControls .slidePrev {
        left: -5px !important;
    }
    .slideControls .slideNext {
        right: -5px !important;
    }
    .catLand .col-sm-3 {
        padding-right: 10px;
    }
    .lock .marginTop86:after {
        position: absolute;
        content: "";
        width: 100%;
        height: 100%;
        background: #f9f9f9;
        overflow: hidden;
        top: 0;
        left: 0;
        z-index: 9;
    }
    /* .mysol.dropdown:hover > .dropdown-menu {
  display: block;
} */
    .navbar .input-group {
        background: #fff;
    }
    .lock {
        overflow: hidden !important;
        position: relative;
    }
    .lock .navbar-toggler-icon {
        background-image: url(../images/close.png);
    }

    .lock .themeBorderBottom {
        background: #f9f9f9;
        border-color: #f9f9f9;
    }
    .marginTop86 {
        margin-top: 61px;
    }
    #tsc_nav_1 .odi .nav-link {
        padding: 3px 8px;
    }
    .mob {
        flex-direction: row;
    }
    #tsc_nav_1 .nav-item {
        text-align: left !important;
        margin-left: 2px;
    }
    #loadTopCategory .col-sm-4 {
        max-width: 100%;
        flex: auto;
        padding-right: 0;
    }
    #tsc_nav_1 .nav-item a {
        text-align: left !important;
        margin-left: 0px;
        padding-left: 2px;
        white-space: normal;
    }
    .navbar-brand img {
        width: 70px;
    }

    #tsc_nav_1 .navbar-nav {
        align-items: normal;
        padding: 10px 3px;
    }
    .out100 {
        display: none;
    }
    .mobile {
        display: flex;
    }
    .desktop {
        display: none;
    }
    .themeBorderBottom {
        max-height: 61px;
    }
    .blogdesc {
        top: 50%;
        min-height: 131px;
        /* transform: translate(-50%, -50%); */
    }

    .blogHeading a {
        color: #fff;
        font-size: 17px;
    }
    .blogHeading {
        align-items: flex-start;
    }
    .blogContainer {
        margin-bottom: 10px;
    }

    /* Category Monitor Responsive */
    .tab-cards-active span {
        top: 5px !important;
    }
    #craftTable2 th {
        width: auto !important;
    }
    .ciKeyInsightScroll-news {
        margin-bottom: 15px;
        float: left;
    }
    .centerMeFix {
        position: unset !important;
        top: unset !important;
        width: 100%;
        transform: unset !important;
    }
    #craftTable th {
        width: auto !important;
    }
    .row.sd-wrapper.craft-row {
        max-height: none !important;
    }
    .trendMScroll {
        min-height: unset !important;
    }
    .trendLoader {
        display: none !important;
    }
    .trendMScroll img {
        width: 120px !important;
        text-align: center;
        position: initial !important;
        text-align: center !important;
        margin: 0 auto !important;
        display: block !important;
    }

    /* End Category Monitor Responsive */
    /* Category Intelligence */
    .skew-div-section {
        width: 100% !important;
        position: unset !important;
        right: 14px;
        bottom: 0;
        background-color: #dae3e6;
        color: #231f20;
        padding: 5px 10px;
        clip-path: unset !important;
    }
    /* ENd Category Intelligence */

    /* Category Insight Dashboard */
    .gridMAdjust100 .col-sm-3 {
        max-width: 100% !important;
    }
    /* End Category Insight Dashboard */

    .btn.dropdown-toggle.btn-light {
        margin-bottom: 0 !important;
    }

    .tab-content .col-sm-3 {
        max-width: 100% !important;
    }
    #example_wrapper .dt-buttons button {
        padding-top: 6px !important;
    }

    .mOptionsCIB {
        display: flex !important;
        flex-direction: column !important;
        height: 100vh !important;
    }
    .reqstForcast a {
        margin-right: 0px !important;
    }
}

@media (max-width: 320px) {
    .navbar-light .navbar-brand {
        margin-right: 0 !important;
    }

    .catwrap {
        margin: 0 auto;
        display: inherit;
    }
    .navbar .input-group {
        height: auto;
    }
    .input-group input,
    .input-group span {
        margin-top: 5px;
    }
    .navbar .input-group {
        background: transparent;
    }
    .input-group input {
        height: 28px;
        border-left-width: 0;
    }
}
@media all and (min-width: 1366px) {
    .container-fluid {
        /* width: 1310px; */
    } /* .footer{width:97.7%} */
    /* .marginTop86 {
        min-height: 796px;
    } */
    .marginTop86 {
        min-height: calc(100vh - 142px);
    }

    .modal-lg {
        max-width: 1160px;
    }
}

@media screen and (min-width: 320px) and (max-width: 699px) {
    .row {
        position: relative !important;
    }
    .carousel-control-prev {
        margin-left: -24px;
    }
    .carousel-control-next {
        margin-right: -24px;
    }
    .arrowicon {
        font-size: 40px;
        color: #98bac3;
    }
    .resizing_select {
        width: 70px !important;
    }
    .navbar-brand {
        position: relative;
    }
    .navbar-brand span {
        position: absolute;
        bottom: 5px;
        margin-left: 5px;
    }
    div.slider-image {
        margin-left: 0px;
    }
    .slick-slider:before {
        box-shadow: 13px 12px 29px 64px rgb(255, 255, 255);
    }
    .arrowicon {
        font-size: 30px;
        color: #98bac3;
    }
    .slideControls i {
        font-size: 30px;
    }
    section#loadTopCategoryMobile {
        overflow-y: auto;
        height: 370px;
    }
    .secondaryOption .col-sm-5.pl-1 {
        padding-left: 15px !important;
    }
    .catimageSection {
        height: 131px !important;
    }
    .Rheading .icons {
        background: #fff;
    }
    div#grid-viewinsightsReports {
        margin-top: 10px;
    }
    .socialMediaShare .col-sm-4 {
        width: 25% !important;
        /* display: flex; */
        max-width: 30% !important;
    }
    input#myInput {
        width: 96%;
    }
    #cpyLink {
        padding-left: 10px;
        padding-top: 10px;
        text-align: center;
        display: block !important;
        margin: 0 auto !important;
    }
    .blogHeading a {
        color: #fff;
        font-size: 13px;
        line-height: 18px;
    }
    #remove-row #LoadMoreButton {
        padding-left: 30px;
    }
    .catimageSection::after {
        box-shadow: -15px -7px 20px 16px rgba(255, 255, 255, 1);
    }
    .d-flex.iconDiv::after {
        content: " ";
        width: 100%;
        position: absolute;
        height: 1px;
        background: #ddd;
        bottom: 10px;
    }
    #loadSourcingConutriesData .col-sm-12 {
        padding: 15px !important;
    }
    div#loadTariffData .col-sm-12 {
        padding: 15px !important;
    }
    /* .row {
        overflow-x: hidden;
    } */
    #tsc_nav_1 .nav-item a {
        font-size: 12px;
    }
    .freqDiv {
        margin-bottom: 5px;
        flex-direction: column !important;
    }
    .vImageMob {
        height: 100% !important;
        width: 100% !important;
    }
    .guageChartKPI .progressbar-text {
        top: 40% !important;
    }
    .top-five-data {
        margin-bottom: 20px !important;
    }
    #riskWidget
        > div
        > div
        > div
        > div.riskMobileLe.pt-2.homeRiskLeg.d-flex.align-items-center.justify-content-end
        > span
        > img {
        width: 206px !important;
    }
    #home
        > div
        > div.col-sm-10.pr-md-0
        > div.row.wrapper.cpXYchart.cd-chart-wrapper
        > div
        > div
        > div
        > section.noteSection.cdInfoSection.lineChartLoaderNotes.showdowNote {
        position: relative !important;
    }
    #home > div > div.col-sm-2.wrapper {
        padding-left: 0 !important;
    }
    .com-right-section {
        height: auto !important;
    }
    #ci_import_section
        > div.row.pl-0.pb-2.imp-notes-sec.importExportDive1
        > div {
        position: relative !important;
    }
    .importStackIcon1 {
        top: -4px !important;
    }
    #ci_export_section
        > div.row.pl-0.pb-2.exp-notes-sec.importExportDive2
        > div,
    #loadSourcingConutriesData > div.row.pl-0.pb-2.exp-notes-sec > div {
        position: relative !important;
    }
}

@media screen and (min-width: 700px) and (max-width: 1260px) {
    .arrowicon {
        font-size: 40px;
        color: #98bac3;
    }
    .carousel-control-prev {
        margin-left: -42px;
    }
    .carousel-control-next {
        margin-right: -42px;
    }
    .back .descriptionSection {
        font-size: 12px;
    }
}
/* @media screen and (max-width:1024px) {
    .col-sm-3{
        -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
    }
    .catLand .col-sm-3{
        -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
    }
} */
@media screen and (min-width: 380px) {
    #tsc_nav_1 .dropdown:hover > .dropdown-menu {
        display: block;
    }
    #tsc_nav_1 .dropdown:hover > .dropdown-menu {
        display: block;
        border-radius: 0px;
    }
}

@media screen and (min-width: 991px) and (max-width: 1024px) {
    .descriptionSection a {
        font-weight: normal;
        font-size: 12px !important;
    }
}

@media screen and (min-width: 577px) and (max-width: 1023px) {
    /* .slide-box div {
    -ms-flex: 0 0 15%;
    flex: 0 0 25%;
    max-width: 25%;
} */
    .carouselSection .carousel-inner {
        overflow: hidden !important;
    }
    #carousel0 .card-style:hover,
    .card-style:hover {
        z-index: 2;
        -webkit-transition: none;
        -webkit-transform: none;
        -ms-transition: none;
        -ms-transform: none;
        -moz-transition: none;
        -moz-transform: none;
        transition: none;
        transform: none;
        background-color: #fff !important;
    }

    .slideTextSection p {
        font-size: 12px;
    }
    .carouselSection .box {
        margin-left: 0px;
    }
    .lslide {
        margin-right: -1px !important;
    }

    [class^="col-sm-"],
    [class^=" col-sm-"],
    [class^="col-4"],
    [class^=" col-4-"] {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
    }
    .catwrap {
        height: auto;
        width: 100%;
        display: block;
        margin-bottom: 20px;
    }
    .catContainer {
        float: none;
    }
    .catwrap:hover .catContainer {
        z-index: 2;
        -webkit-transition: none;
        -webkit-transform: none;
        -ms-transition: none;
        -ms-transform: none;
        -moz-transition: none;
        -moz-transform: none;
        transition: none;
        transform: none;
        background-color: #fff !important;
        position: relative;
        height: auto;
    }
    .catContainer:hover .catdesc {
        transform: none;
        padding-bottom: inherit;
        padding-top: inherit;
        padding: inherit;
    }
    #displayReportData .col-sm-3 {
        width: 50% !important;
        max-width: 50% !important;
    }
    .seemore {
        display: none;
    }
    .catimageSection img {
        height: auto !important;
    }
    .back .descriptionSection {
        margin-top: 0px;
    }
}

@media screen and (min-width: 320px) and (max-width: 576px) {
    /* .slide-box div {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
} */
    .mobile.navbar ul.navbar-nav .show .dropdown-menu {
        display: block;
    }
    .catwrap {
        height: auto;
        width: 100%;
        display: block;
        margin-bottom: 20px;
    }
    .catContainer {
        float: none;
    }
    .catwrap:hover .catContainer {
        z-index: 2;
        -webkit-transition: none;
        -webkit-transform: none;
        -ms-transition: none;
        -ms-transform: none;
        -moz-transition: none;
        -moz-transform: none;
        transition: none;
        transform: none;
        background-color: #fff !important;
        position: relative;
        height: auto;
    }
    .catContainer:hover .catdesc {
        transform: none;
        padding-bottom: inherit;
        padding-top: inherit;
        padding: inherit;
    }
    .seemore {
        display: none;
    }
    .catimageSection img {
        height: auto !important;
    }
}
@media screen and (max-width: 568px) and (orientation: landscape) {
    .carouselSection .boxContainer {
        height: 280px !important;
    }
    .carouselSection .box {
        height: 285px;
    }
    .descriptionSection a {
        font-size: 12px !important;
    }
    /* h1 + select + span.select2-container{
    width: auto
} */
}

@media (min-width: 992px) {
    .header.widget-header {
        flex-direction: column;
    }
}
@media (max-width: 768px) {
    [class^="col-sm-"],
    [class^=" col-sm-"],
    [class^="col-4"],
    [class^=" col-4-"] {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
    }
    .newsDesc {
        display: block !important;
    }
    .focus-section-pro,
    .focus-section {
        display: none !important;
    }
    .newsGroup {
        margin-bottom: auto !important;
    }

    /* CI Page */
    /* .resComIns #newThemesTabs div {
        display: flex;
        flex-direction: column;
        align-items: center !important;
        justify-content: center !important;
        flex: 1;
    }
    .resComIns #newThemesTabs div button {
        width: 100%;
        display: block !important;
    }
    .resComIns div#newThemesTabs {
        justify-content: flex-start !important;
    } */
    .resComIns #focusNavigator {
        display: none !important;
    }
    .resComIns #v-pills-home > div.row > div.col-sm-12 > div {
        flex-direction: column;
    }
    .resComIns #v-pills-home > div.row > div.col-sm-12 > div .radiobuttons {
        margin-top: 10px;
    }
    .resComIns .priceAnalysisScroll ul {
        padding-left: 15px;
        font-size: 12px;
    }
    .resComIns .ciKeyInsightScroll-supply ul {
        padding-left: 15px;
        font-size: 12px;
    }
    .resComIns #priceAnalysis {
        display: grid;
        grid-template-columns: 1fr;
    }

    .resComIns div#newThemesTabs {
        justify-content: flex-start !important;
        display: grid !important;
        grid-template-columns: 4fr 1fr;
        align-items: start !important;
    }

    .resComIns .vim a {
        position: relative;
        padding: 6px 10px;
        color: #231f20;
        text-decoration: none;
        overflow: hidden;
        font-size: 12px !important;
    }
    .saf div#newThemesTabs {
        justify-content: flex-start !important;
        display: grid !important;
        grid-template-columns: 1fr;
        align-items: start !important;
    }
    .saf .vim a {
        position: relative;
        padding: 6px 10px;
        color: #231f20;
        text-decoration: none;
        overflow: hidden;
        font-size: 12px;
    }

    .saf .guageChartKPI {
        display: grid;
        grid-template-columns: 1fr 1fr;
    }
    .saf .navbar-brand span {
        position: absolute;
        bottom: 2px;
        margin-left: 5px;
        font-size: 12px;
    }

    .saf .regulatoryScroll {
        width: 100%;
        overflow-x: scroll;
    }

    .nlqSearch .col-sm-8 {
        order: 2;
        margin-bottom: 20px;
    }
    .nlqSearch #frame {
        margin-bottom: 20px;
    }

    /* .fixedcontactUs {
        display: none !important;
    } */
    .resComIns .btn-group-horizontal.btn-group-fill {
        display: none !important;
    }
    .resComIns .btn-group-section {
        z-index: 9999 !important;
        background: #fff !important;
    }
    .resComIns .row {
        overflow: visible !important;
    }

    #example_wrapper .dt-buttons,
    #example1_wrapper .dt-buttons {
        display: flex !important;
        width: 100% !important;
        text-align: right;
        float: none !important;
        position: relative !important;
        bottom: 0 !important;
        left: auto !important;
    }

    #example_wrapper > div:nth-child(1) {
        flex-direction: column;
    }
    #example1_wrapper > div:nth-child(1) {
        flex-direction: column;
    }
    #example_wrapper .iconSearchData,
    #example1_wrapper .iconSearchData {
        left: 90%;
        top: auto !important;
    }

    .dd-block {
        /* display: none !important; */
        margin-top: 10px;
        margin-bottom: 30px;
        width: 100% !important;
    }

    /* .searchTextCommodities {
        display: none !important;
    }
    .custom-btn-class {
        display: none !important;
    }
    .dataTables_info {
        display: none !important;
    } */

    .resComIns #supplyDemandContent.slideout-panel.open {
        transform: translateY(0) !important;
    }
    .resComIns #priceAnalysisPanel.slideout-panel.open {
        transform: translateY(0px) !important;
    }
    .resComIns #priceAnalysisPanel.slideout-panel.open {
        transform: translateY(0px) !important;
    }
    .resComIns #priceAnalysisPanel.slideout-panel.open {
        transform: translateY(0px) !important;
    }
    .resComIns #priceAnalysisPanel.slideout-panel.open {
        transform: translateY(0px) !important;
    }

    .resComIns .skeltonWrapper.kpi-loader-chart.no-animation {
        min-height: 435px;
    }
    .resComIns .lockMessage.vimalthapliyal {
        padding-top: 0 !important;
    }

    .slideout-panel {
        position: relative !important;
        bottom: 0 !important;
        left: 0;
        width: 100%;
        background-color: #fff;
        padding: 0px !important;
        transition: all 0.5s ease-in-out;
        z-index: 9;
        padding-top: 0px;
        box-shadow: none;
        height: auto !important;
        transform: translateY(0) !important;
        z-index: 9999;
        animation-timing-function: linear;
        padding-left: 10px !important;
        padding-right: 10px !important;
    }
    .loader-info {
        display: none !important;
    }

    .resComIns .icons a:last-child {
        display: none !important;
    }

    .resComIns .table-loader {
        display: none !important;
    }

    .resComIns .table-responsive1 {
        display: block !important;
        width: 100% !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        -ms-overflow-style: -ms-autohiding-scrollbar !important;
    }

    .resComIns .showTable.animatedTable {
        height: auto !important;
    }
    .resComIns .customSaving {
        height: auto !important;
    }

    .resComIns .scroll2 {
        display: none !important;
    }
    .resComIns .cloudy::after {
        display: none !important;
    }
    .resComIns .answerData .mr1 {
        margin-right: 1px;
        display: flex;
        flex-direction: column !important;
    }
    .resComIns .answerData .col {
        flex: 1;
    }
    .resComIns .answerData .card {
        border: none !important;
    }
    .resComIns #ci_supplier_intelligence_wrap .craftScroll.table-responsive {
        display: block !important;
        width: 100% !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        -ms-overflow-style: -ms-autohiding-scrollbar !important;
        height: auto !important;
    }
    .resComIns #ci_supplier_intelligence_wrap .slimScrollDiv,
    .resComIns #supplier-craft {
        overflow: visible !important;
        height: auto !important;
    }
    .dd-block {
        display: none !important;
    }
    #catIntelligence li.lslide,
    #comIntelligence li.lslide {
        width: 230.4px !important;
    }

    #loadRecentView li.lslide {
        width: 225px !important;
    }

    .resComIn #ci_supplier_intelligence_wrap .slimScrollBar,
    .resComIn #ci_supplier_intelligence_wrap .slimScrollRail {
        display: none !important;
        opacity: 0 !important;
    }

    #supplier-craft > div > div > div.slimScrollBar,
    #supplier-craft > div > div > div.slimScrollRail {
        display: none !important;
        opacity: 0 !important;
    }

    /* Event Page */

    #eventTable_wrapper {
        display: block !important;
        width: 100% !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        -ms-overflow-style: -ms-autohiding-scrollbar !important;
    }

    #firstSec .col {
        flex: 1 !important;
    }

    #firstSec .chartDiv {
        display: flex;
        flex-direction: column;
    }
    .chartGhostView {
        display: flex;
        flex-direction: column;
    }
    .chartGhostView .col {
        flex: 1 !important;
    }

   #example_wrapper,
    #example_wrapper1 {
        display: block !important;
        width: 100% !important;
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch !important;
        -ms-overflow-style: -ms-autohiding-scrollbar !important;
    }

    #commodityFilterModal
        #alphaSection
        .mt-2.d-flex.align-items-center.justify-content-between {
        flex-direction: column !important;
    }

    .eventImpactModalCol {
        flex: 1 !important;
    }
    .noteSection.CPInfoSection.dnone.priceOutlookDivNotes1.pt-0 {
        display: flex !important;
    }

    .selectordiv {
        margin-top: 20px !important;
    }

    .dd-block {
        /* display: none !important; */
        margin-top: 10px;
        margin-bottom: 50px;
        width: 100% !important;
        display: block !important;
    }
    .dd-block img {
        width: 251px !important;
    }
}

@media (min-width: 375px) and (max-width: 1024px) {
    .newsDesc {
        display: block;
    }

    body {
        font-size: 14px !important;
    }
    .themeBorderBottom {
        position: absolute;
    }
    .fixedcontactUs {
        bottom: 10px;
    }
    .fixedContactForm {
        bottom: 80px;
    }
    .marginTop86 {
        margin-top: 5px;
        padding-top: 50px;
        min-height: calc(100vh - 78px);
    }
    .resizing_select {
        width: 70px !important;
    }
    .blogContainer {
        width: 100%;
        /* height: auto; */
    }
    .blogdesc {
        top: inherit;
        min-height: auto;
    }
    .blogdesc {
        padding-left: 0px;
        transform: translate(0%, 14%);
    }
    .blogHeading {
        display: block;
        justify-content: center;
        align-items: center;
        height: auto;
        max-width: auto;
        padding: 10px;
    }

    #loadBlockcovidlatestTopics {
        padding-left: 10px;
    }
    .blogdesc {
        top: 50% !important;
    }
    .slimScrollDiv,
    .scrollNews {
        height: auto !important;
    }
    .footer {
        text-align: center;
    }
    .footer .col-sm-6 {
        text-align: center !important;
    }
    .blogHeading a {
        color: #fff;
        font-size: 12px;
    }
    .serbox {
        min-height: auto;
    }

    .back .descriptionSection a {
        font-weight: normal;
        font-size: 14px;
        padding-bottom: 5px;
    }
    .carouselSection .box {
        height: 250px;
    }
    #carousel0 .box {
        height: 250px;
    }
    .autoHideClick li.nav-item {
        display: block !important;
        width: 100% !important;
    }
    /* #lightSlider .slide-box {
    display: flex;
    justify-content: flex-end !important;
} */
    .catContainer .icons {
        padding-top: 20px;
    }
    .catdesc p {
        font-size: 14px;
    }
    #collapseFilter .mb-4,
    #collapseFilter .mb-3 {
        margin-bottom: 20px !important;
    }
    #collapseFilter .assetFilter {
        margin-bottom: 0;
    }
    #collapseFilter .col-sm-3.mb-3.pl-0 {
        padding-left: 15px !important;
    }
    .borderBottomDark .col-sm-12.mb-4.pr-0 {
        padding-right: 15px !important;
    }
    .chartArea .noteSection .btn.float-right {
        display: block !important;
        width: 100%;
        margin-top: 10px;
    }
    .Rheading .icons {
        margin-left: 0px !important;
        display: block !important;
        background: #eee;
        margin-top: 5px;
        margin-bottom: 5px;
    }
    .Rheading .icons i {
        position: relative !important;
        left: 0 !important;
    }
    .mobileNum {
        display: block;
        background: #eee;
        padding: 10px;
        line-height: 19px;
        width: 100%;
    }
    .mobileNum.float-right {
        float: none !important;
    }
    .mobileNum.float-right .float-right {
        float: none !important;
    }
    .bkbtn {
        top: 0px;

        background: #eee;
        width: 100%;
        padding-top: 10px;
        padding-bottom: 10px;
        font-size: 14px;
    }
    .la-search {
        -ms-transform: rotate(269deg);
        -webkit-transform: rotate(269deg);
        transform: rotate(269deg);
        font-size: 16px !important;
    }
    .serbox p {
        font-weight: normal;
        font-size: 14px;
    }
    .favtabCATSection .carouselSection .box {
        margin-left: 0px !important;
    }
    .repoAction.inline-block {
        display: block !important;
    }
    .Rheading .icons {
        text-align: left !important;
    }
    .favtabCATSection .repoAction .icons {
        margin-left: 0px !important;
        display: block !important;
        background: #eee;
        margin-top: 5px;
        margin-bottom: 5px;
        width: 100%;
        text-align: left !important;
    }
    .Rheading .icons {
        margin-left: 0px !important;
        display: block !important;
        background: #eee;
        margin-top: 5px;
        margin-bottom: 5px;
        padding: 5px 0px;
    }
    .mom {
        padding: 20px;
    }
    .serbox {
        min-height: auto;
    }
    .serbox p {
        min-height: auto !important;
    }
    #videoIframeId {
        height: auto !important;
    }
}
@media screen and (min-width: 320px) and (max-width: 569px) {
    body {
        font-size: 14px !important;
    }
    .back .descriptionSection a {
        font-weight: normal;
        font-size: 14px;
        padding-bottom: 5px;
    }
    .carouselSection .box {
        height: 280px;
        background: #fff;
        width: 280px;
        margin-left: -1px;
    }
    .lSSlideWrapper {
        padding-left: 0px;
    }
    .lSSlideWrapper {
        padding-left: 0px;
        /* margin-left: -8px; */
    }
    #carousel0 .box {
        background: #fff !important;
        height: 280px;
        border: 1px solid #d2e2e6 !important;
    }
    #carousel0 .box {
        width: 280px !important;
    }
    #carousel0 .lslide {
        margin-right: -1.2px !important;
    }
    #carousel0 .lSSlideWrapper {
        margin-left: 0px !important;
    }
    /* #lightSlider .slide-box {
        display: flex;
        justify-content: flex-end !important;
    } */
    .catContainer .icons {
        padding-top: 20px;
    }
    .catdesc p {
        font-size: 14px;
    }
    #collapseFilter .mb-4,
    #collapseFilter .mb-3 {
        margin-bottom: 20px !important;
    }
    #collapseFilter .assetFilter {
        margin-bottom: 20px;
    }
    #collapseFilter .col-sm-3.mb-3.pl-0 {
        padding-left: 15px !important;
    }
    .borderBottomDark .col-sm-12.mb-4.pr-0 {
        padding-right: 15px !important;
    }
    .chartArea .noteSection .btn.float-right {
        display: block !important;
        width: 100%;
        margin-top: 10px;
    }
    .Rheading .icons {
        margin-left: 0px !important;
        display: block !important;
        background: #eee;
        margin-top: 5px;
        margin-bottom: 5px;
    }
    .Rheading .icons i {
        position: relative !important;
        left: 0 !important;
    }
    .mobileNum {
        display: block;
        background: #eee;
        padding: 10px;
        line-height: 19px;
        width: 100%;
    }
    .mobileNum.float-right {
        float: none !important;
    }
    .mobileNum.float-right .float-right {
        float: none !important;
    }
    .bkbtn {
        top: 0px;

        background: #eee;
        width: 100%;
        padding-top: 10px;
        padding-bottom: 10px;
        font-size: 14px;
    }
    .la-search {
        -ms-transform: rotate(269deg);
        -webkit-transform: rotate(269deg);
        transform: rotate(269deg);
        font-size: 16px !important;
    }
    .serbox p {
        font-weight: normal;
        font-size: 14px;
    }
    .favtabCATSection .carouselSection .box {
        margin-left: 0px !important;
    }
    .repoAction.inline-block {
        display: block !important;
    }
    .Rheading .icons {
        text-align: left !important;
    }
    .favtabCATSection .repoAction .icons {
        margin-left: 0px !important;
        display: block !important;
        background: #eee;
        margin-top: 5px;
        margin-bottom: 5px;
        width: 100%;
        text-align: left !important;
    }
    .Rheading .icons {
        margin-left: 0px !important;
        display: block !important;
        background: #eee;
        margin-top: 5px;
        margin-bottom: 5px;
        padding: 5px 0px;
    }
    .mom {
        padding: 20px;
    }
    #remove-row #LoadMoreButton {
        padding-left: 10px;
    }
    .serbox {
        min-height: auto;
    }
    .serbox p {
        min-height: auto !important;
    }
    #videoIframeId {
        height: auto !important;
    }

    .footer {
        text-align: center !important;
    }
    .footer .text-right {
        text-align: center !important;
    }
    .blogdesc {
        top: 140px !important;
        min-height: 100px;
    }
    .blogHeading {
        height: auto;
    }

    .carouselSection .carousel-inner {
        overflow: hidden !important;
    }
    .carouselSection .carousel-inner {
        width: 100% !important;
    }

    .EventRiskImpact__no-record {
        font-size: 16px;
        display: block;
        width: 100%;
    }

    .card-style:hover {
        z-index: 2;
        -webkit-transition: none;
        -webkit-transform: none;
        -ms-transition: none;
        -ms-transform: none;
        -moz-transition: none;
        -moz-transform: none;
        transition: none;
        transform: none;
        background-color: #fff !important;
    }
    .slideTextSection p {
        font-size: 12px;
    }
    .slideTextSection h2 {
        font-size: 22px;
    }
    /* div.slider-image{
    min-height: 150px;
}
.slick-slide img{
    min-height: 150px;
} */
    .slick-current:after {
        content: "";
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
        width: 100%;
        height: 98%;
        top: 0;
        position: absolute;
    }
    .shapeSection {
        top: 65px !important;
    }
    #rectangle {
        width: 78px;
        height: 30px;
        background: #3bcd3f;
        display: inline-block;
        margin-left: -4px;
    }
    .shapeText {
        position: absolute;
        top: 6px;
        color: #fff;
        font-size: 12px;
        left: 11px;
        width: 100%;
        display: block;
    }
    .carouselSection .box {
        margin-left: 0px !important;
    }
    .fixedContactForm {
        width: 310px !important;
    }
    /* h1 + select + span.select2-container {
    width: auto !important;
    padding-right: 20px;
    width: 100% !important;
} */
    .covidPageM .tab-content {
        padding-left: 0px !important;
        padding-right: 0px !important;
    }
    .covidPageM .blogContainer,
    .blogContainer {
        padding-right: 15px !important;
    }
    .bannerSection,
    .footerBanner {
        font-size: 1.4em !important;
    }
    .covidPageM .blogContainer,
    .blogContainer {
        width: 100%;
        height: 250px;
    }
    .blogdesc {
        top: 50% !important;
        padding-left: 10px;
        padding-right: 10px;
    }
    .blogdesc {
        min-height: 50px !important;
        z-index: 99;
    }

    .blogHeading a {
        color: #fff;
        font-size: 12px;
        line-height: 18px;
    }
    .catLand #news.col-sm-3 {
        padding-right: 15px;
        padding-left: 15px;
    }
    #loadReports1 .loadMoreData {
        margin-left: -15px;
    }
    h1 {
        font-size: 16px !important;
        font-weight: bold;
    }
    .catheading a {
        font-size: 16px;
    }
    /* .catLand .col-sm-3 {
        padding-right: 10px;
        padding-left: 0px;
        padding-right: 0px;
    } */
    .viewReport {
        height: auto !important;
        width: 100% !important;
        min-height: 320px;
    }
    .viewReport iframe {
        height: auto;
        width: 100% !important;
        min-height: 320px;
    }
    #remove-row #LoadMoreButton {
        padding-left: 0px;
    }
    .serbox .descriptionSection a {
        min-height: 40px;
        display: block;
        font-size: 16px;
    }
    tspan {
        font-size: 12px !important;
    }

    div.slider-image {
        margin-left: 0px;
    }
    .slick-slider:before {
        box-shadow: 13px 12px 29px 64px rgb(255, 255, 255);
    }
    .arrowicon {
        font-size: 30px;
        color: #98bac3;
    }
    .slideControls i {
        font-size: 30px;
    }
    section#loadTopCategoryMobile {
        overflow-y: auto;
        height: 370px;
    }
    #tsc_nav_1 .dropdown-menu {
        overflow-y: auto;
        height: auto;
    }
    .secondaryOption .col-sm-5.pl-1 {
        padding-left: 15px !important;
    }
    .catimageSection {
        height: 131px !important;
    }
    .Rheading .icons {
        background: #fff;
    }
    div#grid-viewinsightsReports {
        margin-top: 10px;
    }
    .socialMediaShare .col-sm-4 {
        width: 25% !important;
        /* display: flex; */
        max-width: 30% !important;
    }
    input#myInput {
        width: 96%;
    }
    #cpyLink {
        padding-left: 10px;
        padding-top: 10px;
        text-align: center;
        display: block !important;
        margin: 0 auto !important;
    }
    .blogHeading a {
        color: #fff;
        font-size: 13px;
        line-height: 18px;
    }
    #remove-row #LoadMoreButton {
        padding-left: 30px;
    }
    .catimageSection::after {
        box-shadow: -15px -7px 20px 16px rgba(255, 255, 255, 1);
    }
    .d-flex.iconDiv::after {
        content: " ";
        width: 100%;
        position: absolute;
        height: 1px;
        background: #ddd;
        bottom: 10px;
    }
    #loadSourcingConutriesData .col-sm-12 {
        padding: 15px !important;
    }
    div#loadTariffData .col-sm-12 {
        padding: 15px !important;
    }
    #tsc_nav_1 .nav-item a {
        font-size: 12px;
    }
    .blogimageSection {
        background-size: cover !important;
    }
    /* div#copyDropdown {
        top: -140px !important;
    } */

    .sourcePage .chartArea,
    .sourcePage #stackChart2 {
        margin-top: 20px;
    }
    .sourcePage div#copyDropdown {
        top: 0 !important;
    }
    .sourcePage .pr-0 {
        padding-right: 15px !important;
    }
    .sourcePage .pl-0 {
        padding-left: 15px !important;
    }
    #tsc_nav_1 .dropdown-menu {
        overflow-y: auto;
    }
    .mobileScroll {
        overflow-y: scroll;
        height: 220px;
    }
    p.mLegend {
        flex-direction: column;
        align-items: flex-start !important;
        justify-content: end;
    }
    .mLegend button {
        margin-top: 20px !important;
    }
    .mMobileHam {
        justify-content: flex-start !important;
        align-items: center !important;
    }

    .mMobileHam p {
        color: #00b19c;
        position: initial !important;
        top: initial !important;
        font-size: 11px;
        left: initial !important;
        margin-bottom: 5px !important;
    }
    #catTab #navbarSupportedContent .hidethisbtnonMobile {
        display: none !important;
    }
    #newHamexpMenu {
        position: relative !important;
    }
    .newSearchSecPage .navbar-expand-lg .navbar-nav {
        padding-top: 20px !important;
    }
    .float-right.mb-1 {
        display: none !important;
    }
    .newSearchSecPage #catTab .navbar-collapse.show {
        flex-basis: content !important;
    }
    .multiselect-native-select > .btn-group {
        height: auto;
    }
    .la-info-circle {
        display: none !important;
    }
    .cdTypeView-Section #collapseFilter div.mb-3 {
        width: 100% !important;
        margin-bottom: 10px !important;
    }
    .cdTypeView-Section #collapseFilter .btn-group {
        width: 94% !important;
    }

    .cdTypeView-Section #collapseFilter #applyFilter {
        display: block;
        /* margin-left: 17px; */
        margin-top: -30px;
    }
    .multiselect-container > li > a > label {
        font-size: 12px !important;
    }
    .commodity-menu-main {
        position: inherit !important;
        top: inherit !important;
        left: inherit !important;
        transform: inherit !important;
        width: inherit !important;
    }
    .commodity-menu-main .commodityInsightIcon span,
    .commodity-menu-main .commodityPricesIcon span {
        font-size: 5em !important;
        position: absolute !important;
        color: #007365 !important;
        top: 87px !important;
        left: 50% !important;
        font-weight: bold !important;
        transform: translateX(-50%);
    }
    .commodity-menu-main .commodityInsightDesc {
        margin-top: -27px;
    }
    .footer {
        position: relative !important;
        bottom: 0;
    }
    .commodity-menu-main .col {
        margin-top: 30px;
        margin-bottom: 30px;
    }
    .border-right.cp-inner-chart {
        border: none !important;
    }
    .cpKpiSection .col {
        padding: 21px !important;
    }
    .cpKpiSection .col.border-right {
        border-right: none !important;
    }
    .large_banner .footerBanner .col {
        width: 100% !important;
        flex-basis: auto !important;
    }
    .large_banner .footerBanner .col:nth-child(1) {
        padding-bottom: 20px !important;
        border-bottom: 2px solid #fff !important;
    }
    .large_banner .footerBanner .col:nth-child(2) {
        padding-top: 20px !important;
    }
    .mCommRow {
        display: flex;
        flex-direction: column !important;
    }
    #collapseFilter div.mb-3 {
        width: 100% !important;
        margin-bottom: 10px !important;
    }
    #collapseFilter .filterBig .btn-group {
        width: 94% !important;
    }

    #collapseFilter #applyCommodity {
        /* display: block; */
        margin-left: 0px;
        /* margin-top: -30px; */
    }
    .left-pad-filers .dropdown {
        position: relative;
        display: block !important;
    }
    #sectorDownList label span,
    #locationDownList label span {
        width: auto !important;
    }
    #initialListing #mobShift.row.left-pad-filers.asd {
        margin-left: -30px !important;
        margin-right: 0px !important;
    }
    .mFixImage {
        width: 100% !important;
        height: auto !important;
    }
    .News {
        display: inline-block;
        margin-bottom: 20px;
    }
    .col-8.pr-3 {
        width: 100%;
        max-width: 100%;
        flex: 0 0 100%;
    }
    div#aboutScroll {
        height: auto !important;
    }

    #collapseFilter .assetFilter {
        margin-bottom: 0px;
        margin-left: 0;
    }
    .emptyLabel {
        display: none !important;
    }
    .mComSec {
        align-items: center;
        justify-content: space-around;
    }
    .mComSec .mComSecDiv {
        align-self: normal;
        margin-top: 40px;
    }

    .mobMLR0 {
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    .imp-notes-sec .row {
        position: relative !important;
    }
    .fixedcontactUs {
        z-index: 999;
    }
    .import-chart-actions {
        top: 0 !important;
    }
    .headersIcon {
        display: none !important;
    }
    .tier-plans .scrollDiv {
        min-width: 1024px !important;
    }
    .mobile.navbar .navbar-nav.light-bg-1 {
        height: 610px;
        overflow-y: scroll;
    }

    /* Media Fix */

    #catIntelligence .tab-pane.show {
        display: grid;
        grid-template-columns: 1fr 1fr;
        max-width: 100%;
        width: 400px;
    }
    .slidingSection .slideControls {
        display: none !important;
    }
    #catIntelligence .carouselSectionSlide,
    #comIntelligence .carouselSectionSlide,
    #loadRecentView .carouselSectionSlide {
        padding-right: 0 !important;
    }
     ul#lightSlide0,
    #lightSlide1,
    #lightSlide2,
    #lightSlide3,
    #lightSlide4,
    #lightSlide5,
    #lightSlide6,
    #lightSlide7,
    #lightSlide8,
    #lightSlide9,
    #lightSlide10,
    #lightSlide11,
    #lightSlide12,
    #lightSlide13,
    #lightSlide14,
    #lightSlide15,
    #lightSlide16,
    #lightSlide17,
    #lightSlide18,
    #lightSlide19,
    #lightSlide20 {
        width: 100% !important;
    }

    #loadRecentView li.lslide {
        width: 100% !important;
    }
    #catIntelligence .carouselSectionSlide,
    #comIntelligence .carouselSectionSlide,
    #loadRecentView .carouselSectionSlide {
        padding-right: 0 !important;
        display: grid;
        grid-template-columns: 1fr;
        align-items: center;
    }
    .colContainer .title-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: column;
        align-items: start !important;
    }
    div.title-header > span:nth-child(2) {
        display: flex;
        flex-direction: column;
    }
    .resComIns div#newThemesTabs {
        display: grid !important;
        grid-template-columns: 1fr;
        align-items: start !important;
    }
    .priceAnalysisScroll {
        height: auto !important;
    }
    #eventTable_wrapper.dataTables_wrapper.dataTables_filter {
        margin-top: 0 !important;
    }
    div#eventTable_length {
        float: left !important;
    }
    div#eventTable_filter {
        float: right !important;
        margin-top: 0px !important;
    }
    .dataTables_info {
        display: none !important;
    }
    .dataTables_wrapper .dataTables_paginate {
        margin-top: 0.5em;
        float: right !important;
    }

    div#collapseFilter.commodityFilterBtn {
        display: grid;
        grid-template-columns: 1fr;
    }
    .filterBig {
        padding: 0 !important;
    }
    .filterBig:not(:last-child) {
        padding: 0px !important;
        padding-left: 10px !important;
        padding-right: 10px !important;
        font-size: 12px !important;
        max-width: 100% !important;
    }
    .mb-2.filterBig {
        width: 100% !important;
        padding-right: 0 !important;
    }
    table#lower-table td:nth-child(1) {
        width: 140px !important;
    }
    .guageChartKPI {
        display: grid;
        grid-template-columns: 1fr 1fr;
    }
    .regulatoryScroll {
        width: 100%;
        overflow-x: scroll;
    }
    #subscribed-news .form-group.text-right.mb-0 {
        text-align: left !important;
    }
    div#inputtableL div {
        display: inline;
    }

    button#calCostData {
        display: inline !important;
        width: 100px !important;
        float: left;
    }
    div#example_length,
    div#example1_length {
        display: none !important;
    }
    #example_wrapper .iconSearchData,#example2_wrapper .iconSearchData,#example4_wrapper .iconSearchData
    #example1_wrapper .iconSearchData {
        left: 70%;
        top: 35px;
    }
}
@media (min-width: 990px) and (max-width: 1200px) {
    .firstlistsection {
        height: 330px;
        min-width: auto !important;
    }
    #sectorDownList label span,
    #locationDownList label span {
        width: 75px !important;
    }
    .firstlistsection .firstlistbutton {
        position: absolute;
        bottom: 10px;
        left: 50%;
        text-align: center;
        transform: translateX(-50%);
    }
    #catTab .navbar-collapse.show ul{
        border: none;
    }
    #catTab .navbar-collapse.show{
        background-color: transparent;
    }
}

.animation-none {
    animation: none !important;
}



/* <===== for mobile responsive css =====>  */

@media (max-width: 576px) {
    .risk-profile-widgets .border-color-graph {
        border-color: #bbd6dc !important;
        flex-wrap: wrap;
        justify-content: center !important;
        gap: 20px;
    }
    .toggleSectionGuage .w-10{
        width: 45% !important;
    }
    div#collapseFilter.commodityFilterBtn {
        display: block;
    }
    #example_wrapper .iconSearchData, #example1_wrapper .iconSearchData {
        /* left: 74% !important; */
        top: 0 !important;
    }
    div#example_wrapper div#example_filter label{
        position: relative;
    }
    div#example2_wrapper div#example2_filter label{
        position: relative;
    }
    div#example4_wrapper div#example4_filter label{
        position: relative;
    }
    
    div.dataTables_wrapper div.dataTables_filter input{
        padding-right: 30px;
    }
    #example_wrapper .dt-buttons button, #example1_wrapper .dt-buttons button{
        line-height: 15px;

    }
    .dd-block img {
        width: 190px !important;
    }
    #collapseFilter .filterBig .btn-group {
        width: 100% !important;
    }
    #collapseFilter div.mb-3 {
        width: 100% !important;
        margin-bottom: 10px !important;
        padding-left: 0 !important;
        padding-right: 0 !IMPORTANT;
    }
    .mb-2.filterBig {
        width: 100% !important;
        padding-right: 0 !important;
        padding-left: 0 !important;
    }
    div#collapseFilter .mb-3.filterBig .select2-container{
        padding: 0 !important;
    }
    div#collapseFilter .mb-3.filterBig:nth-last-child(2){
        margin-bottom: 15px;
    }
    .container-fluid.marginTop86 div#collapseFilter.commodityFilterBtn{
        margin: 0 !important;
        margin-left: 0 !important;
        padding-left: 0;
    }
    .cdTypeView-Section #collapseFilter .btn-group {
        width: 100% !important;
    }
    .sector--search input.search-text.mobile_show_input{
        width: 156px !important;
        border: 1px solid #a4c8d0 !important;
        margin-right: 10px !important;
        top: -3px;
        right: -7px;
    }
    .row.mobile_overflow_show{
        overflow-x: inherit !important;
    }
    .sector--search span.serbtn{
        z-index: 9 !important;
    }
    div#appendedDivs .row.mb-2 .col {
        -ms-flex: 0 0 50%;
        flex: 0 0 50%;
        max-width: 50%;
        margin-bottom: 10px;
    }
    div#appendedDivs .row.mb-2 .col .custom-boxes{
        align-items: flex-start !important;
    }
 
    .leftArrow {
        left: -6px;
    }
    .rightArrow{
        right: -6px;
    }
}
/* <===== for Desktop only css =====>  */
@media (min-width: 1366px) {
    form#search-form{
        margin: 0;
    }
  
    button#start-record-btn {
        top: 50% !important;
        right: 56px !important;
        transform: translateY(-50%);
        line-height: 18px;
    }
    .custom-select.modified-new-select-modified span:first-child {
        bottom: 50% !important;
        transform: translateY(50%) !important;
    }
}
/* <===== for Ipad and mobile responsive css=====>  */
@media (max-width: 1366px) {
    .table-responsive-custom-width{
        display: block;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }
}

/* <===== Only for Ipad responsive css=====>  */
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    
    .desktop.navbar .input-group {
        width: 675px !important;
    }
    .navbar-brand {
        flex-direction: row;
        align-items: baseline;
    }
    .guageChartKPI .progressbar-text{
        top: 20px !important;
    }
    #tsc_nav_1 .nav-item a{
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    #collapseFilter .btn-group {
        width: 295px !important;
    }
    .skew-div-section{
        width: 200px !important;
        padding: 0px 10px !important;
        font-size: 10px !important;
    }
    .filter_search{
        left: 50px !important;
    }
    .newsGroup{
        margin-bottom: 10px !important;
    }
    .newsGroup .News{
        flex: 1;
    }
    #collapseFilter .cost_structures .btn-group{
        width: 235px !important;
    }
    .marginTop86 {
        /* margin-top: 86px; */
        min-height: calc(100vh - 141px) !important;
    }
    div#loadToolsData, div#grid-data{
        margin-right: 0;
    }
    div#loadReports37 {
        padding-right: 0;
    }
    .guageChartKPI {
        display: grid;
        grid-template-columns: 1fr 1fr;
    }
    div#outer-divs {
        padding-right: 15px;
    }
    .col-md-2.wrapper {
        padding-top: 10px;
        width: 100%;
        padding-right: 0;
    }
    div#intelligence_tabs1 .intelligence_tabs .autoHideClick, div#ci-commodity-list .intelligence_tabs .autoHideClick{
        display: block;
    }
    div#intelligence_tabs1 .intelligence_tabs .autoHideClick .nav-tabs .nav-link, div#ci-commodity-list .intelligence_tabs .autoHideClick{
        padding: 0.5rem .8rem !important;
        font-size: 12px;
    }
    .row.chartDivAllEvent.eventTable-visible {
        display: block;
    }
    .autoHideClick li.nav-item {
        display: inline-block !important;
        width: auto !important;
    }
    div#intelligence_tabs1 div#example1_wrapper .table_top_search{
        position: static;
        display: flex !important;
            flex-direction: row;
    }
    div#intelligence_tabs .intelligence_tabs{
        width: 100%;
    }
    .row.mb-3.pt-2.outer-divs.me-md-0, .row.mb-3.outer-divs.me-md-0, .row.mb-3.case-no-access-divs.EventCINewsMainDIv{
        row-gap: 10px;
    }
}
/* <===== Only for Ipad mini responsive css=====>  */
@media only screen and (min-device-width: 568px) and (max-device-width: 768px) {
    .row.py-3.mb-4.guageChartKPI .col-sm-2 {
        width: 50% !important;
        max-width: 50% !important;
        flex: 0 0 50% !important;
    }
    .card {
        margin-bottom: 10px;
    }
    .genAiModule .btn-demo.UserActivityLogger {
        min-width: 120px;
        font-size: 12px;
        padding: 6px 5px;
    }
    .themes_cost_structure.themes_cost_structure_true, .negotiateMainDiv.themes_negotiation_levers.themes_negotiation_levers_true{
        padding-bottom: 10px;
    }
    .row.cost_structures .col-sm-3 {
        width: 33%;
        max-width: 33%;
        flex: 0 0 33% !important;
    }
    .multiselect.dropdown-toggle.btn.btn-default{
        min-height: 25px;
    }
    div#outer-divs {
        gap: 10px;
    }
    div#inputtableR {
        width: 100%;
    }
    .row.chartDivAllEvent.eventTable-visible {
        display: block;
    }
    div#intelligence_tabs1 .intelligence_tabs .autoHideClick, div#ci-commodity-list .intelligence_tabs .autoHideClick{
        display: block;
    }
    div#intelligence_tabs1 .intelligence_tabs .autoHideClick .nav-tabs .nav-link, div#ci-commodity-list .intelligence_tabs .autoHideClick{
        padding: 0.5rem .8rem !important;
        font-size: 12px;
    }
    .row.chartDivAllEvent.eventTable-visible {
        display: block;
    }
    .row.mb-3.pt-2.outer-divs.me-md-0, .row.mb-3.outer-divs.me-md-0, .row.mb-3.case-no-access-divs.EventCINewsMainDIv{
        row-gap: 10px;
    }
}


/* @media screen and (min-width: 1900px) {
    .container-fluid {
        max-width:1600px;
    }
    
} */
@media (min-width: 1668px) {
    .grid-adjust .col-sm-3 {
        max-width: 100% !important;
    }
}
@media (max-width: 576px) {
    div#newThemesTabs {
        flex-direction: column;
    }
    div#newThemesTabs .genAiModule{
        width: 100%;
        justify-content: end;
    }
    .card{
        margin-bottom: 10px;
        height: auto !important;
    }
    .themes_cost_structure.themes_cost_structure_true, .negotiateMainDiv.themes_negotiation_levers.themes_negotiation_levers_true{
        padding-bottom: 10px;
    }
    .card_crousal.cc_crousal {
        margin-top: 15px;
    }
    nav.desktop a.navbar-brand span.tierName{
        left: 80px;
    }
    .saf .navbar-brand span{
        bottom: 4px;
    }
    .lSSlideWrapper{
        padding-top: 0 !important;
    }
    ul#lightSlide0, #lightSlide1, #lightSlide2, #lightSlide3, #lightSlide4, #lightSlide5, #lightSlide6, #lightSlide7, #lightSlide8, #lightSlide9, #lightSlide10, #lightSlide11, #lightSlide12, #lightSlide13, #lightSlide14, #lightSlide15, #lightSlide16, #lightSlide17, #lightSlide18, #lightSlide19, #lightSlide20{
        transform: none !important;
        width: 100% !important;
    }
    .lSSlideWrapper.usingCss .slide-box{
        display: block;
    }
    .lSSlideWrapper.usingCss .carouselSection .box{
        width: 100%;
        margin-bottom: 10px;
    }
    #catIntelligence li.lslide, #comIntelligence li.lslide{
        width: 100% !important;
    }
    #catIntelligence .tab-pane.show{
        display: block;
    }
    div#loadCategoryDashboardGroup > .col-sm-12, div#loadComodityGroup > .col-sm-12{
        padding-left: 0;
        padding-right: 0;
    }
    div#loadRecentView h4.heading {
        margin-bottom: 15px !important;
    }
    #carousel0 .box {
        width: 100% !important;
    }
    #tsc_nav_1 .navbar .nav-link:focus, .navbar .nav-link:hover, .navbar .nav-item:hover{
        background: transparent;
    }
    #tsc_nav_1 .nav-item a{
        color: #231f20 !important;
    }
    div.slider-image{
        height: 108px;
    }
    .slider-image img {
        height: 100% !important;
        object-fit: fill;
    }
    .slick-dots {
        bottom: -20px !important;
    }
    .pagesGhostCss .col-sm-3{
        flex: 1 !important;
    }
    div#intelligence_tabs div#example_wrapper .table_top_search{
        position: static;
    }
    div#nav-tabContent {
        padding-left: 0 !important;
        padding-right: 0 !important;
    }
    #frame .content .message-input .wrap button{
        margin: 0;
    }
    .tdcs{
        width: 60%;
    }
    .FirstTable2Td {
        width: 20%;
    }
    .row.wrapper.cpXYchart.cd-chart-wrapper {
        margin-bottom: 10px;
    }
    .multiselect.dropdown-toggle.btn.btn-default{
        min-height: 25px;
    }
    .supplier-details-tabel-section .col-8, .supplier-details-tabel-section .col-4{
        width: 100%;
        margin-bottom: 10px;
    }
    .catLand .catDashboardGhostCss .col-sm-3 {
        flex: 100% !important;
    }
    div#cscd .col-sm-10.pe-md-0 {
        padding-right: 0;
        margin-bottom: 10px;
    }
    div#cscd .col-sm-2.pe-md-0{
        padding-right: 0; 
    }
    div#intelligence_tabs1 .table-responsive{
        overflow: scroll;
    }
    div#intelligence_tabs1 div#example1_wrapper .table_top_search{
        position: static;
    }
    div#eventTable_wrapper {
        display: none !important;
    }
    div#allEventTable_wrapper, div#eventTable_wrapper {
        overflow: scroll;
    }
    div#parentClientFilter {
        margin: 0 !important;
    }
    div#allEventTable_wrapper .dt-layout-row {
        display: flex !important;
        justify-content: space-between;
    }
    div#intelligence_tabs1 .intelligence_tabs .autoHideClick{
        display: block;
    }
    div#intelligence_tabs1 .intelligence_tabs .autoHideClick .nav-tabs .nav-link{
        padding: 0.5rem .8rem !important;
        font-size: 12px;
    }
    .row.chartDivAllEvent.eventTable-visible {
        display: block;
    }
    div#allEventTable_wrapper, div#eventTable_wrapper{
        top: -50px;
    }
    .card-body div#intelligence_tabs .intelligence_tabs div#navbarSupportedContent ul {
        display: flex;
    }
    .card-body div#intelligence_tabs .intelligence_tabs div#navbarSupportedContent {
        display: block;
    }
    .card-body div#intelligence_tabs .intelligence_tabs div#navbarSupportedContent ul li{
       width: 50% !important;
    }
    .card-body div#intelligence_tabs .intelligence_tabs div#navbarSupportedContent ul li  .nav-link{
        padding: 0.5rem 1rem !important;
        font-size: 12px;
    }
    div#intelligence_tabs .intelligence_tabs{
        width: 100%;
    }
    .firstlistsection .firstlistbutton{
        position: relative !important;
        bottom: -5px !important;
    }
    div#filterWrapper {
    overflow: hidden;
    margin-bottom: 15px;
    }
    .d-contents { display: contents; }
    .slideout-panel-askpia{
        height: fit-content !important;
    }
    div#swot_petsal_main_div div#swotResponsGhostDiv .card {
        height: fit-content !important;
    }

    div#swot_petsal_main_div div#swotResponsGhostDiv {
        height: fit-content !important;
    }
    div#swot_petsal_main_div {
    overflow: visible !important;
    }
    div#swot_petsal_main_div div#swotResponsGhostDiv, div#swot_petsal_main_div .slideout-panel-askpia {
        top: -7% !important;
    }
    .risk_level_graph{
        float: none !important;
    }
    #CDChartDiv{
        height: 375px;
    }
}

