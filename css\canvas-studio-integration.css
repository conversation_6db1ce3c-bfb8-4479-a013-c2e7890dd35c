/**
 * Canvas Studio Integration Styles
 * Styles for the tab configuration modal and widget assignment interface
 */

/* ===== MODAL STYLES ===== */
.canvas-studio-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.canvas-studio-modal.show {
  opacity: 1;
  visibility: visible;
}

.canvas-studio-modal .modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
}

.canvas-studio-modal .modal-content {
  position: relative;
  background: white;
  border-radius: 16px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  max-width: 95vw;
  max-height: 95vh;
  overflow: hidden;
  transform: scale(0.9) translateY(20px);
  transition: transform 0.3s ease;
}

.canvas-studio-modal.show .modal-content {
  transform: scale(1) translateY(0);
}

.canvas-studio-modal.medium .modal-content {
  width: 800px;
}

.canvas-studio-modal.large .modal-content {
  width: 1200px;
  height: 800px;
}

.canvas-studio-modal .modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.canvas-studio-modal .modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.canvas-studio-modal .modal-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.canvas-studio-modal .modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.canvas-studio-modal .modal-body {
  padding: 0;
  overflow: hidden;
  height: calc(100% - 80px);
}

/* ===== TAB CONFIGURATION MODAL ===== */
.tab-configuration-modal {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.config-title h3 {
  margin: 0 0 4px 0;
  font-size: 1.25rem;
  color: #1e293b;
}

.config-subtitle {
  color: #64748b;
  font-size: 0.875rem;
}

.config-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #3b82f6;
}

.stat-label {
  font-size: 0.75rem;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.config-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.config-panel {
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e2e8f0;
}

.config-tabs-panel {
  width: 400px;
}

.config-preview-panel {
  flex: 1;
  min-width: 400px;
}

.config-widgets-panel {
  width: 350px;
  border-right: none;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.panel-header h4 {
  margin: 0;
  font-size: 1rem;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-add-tab {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.btn-add-tab:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
}

/* ===== TABS LIST ===== */
.tabs-list {
  margin-bottom: 24px;
}

.tab-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.tab-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tab-item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8fafc;
}

.tab-info {
  flex: 1;
}

.tab-icon-name {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.tab-name-input {
  border: none;
  background: transparent;
  font-weight: 600;
  font-size: 0.875rem;
  color: #374151;
  padding: 2px 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.tab-name-input:focus {
  outline: none;
  background: white;
  box-shadow: 0 0 0 2px #3b82f6;
}

.tab-meta {
  font-size: 0.75rem;
  color: #64748b;
}

.tab-actions {
  display: flex;
  gap: 4px;
}

.btn-icon {
  background: none;
  border: none;
  color: #64748b;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.btn-icon:hover {
  background: #e2e8f0;
  color: #374151;
}

.btn-icon.btn-danger:hover {
  background: #fee2e2;
  color: #dc2626;
}

.tab-item-content {
  padding: 12px 16px;
}

.assigned-widgets {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.assigned-widget {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #f1f5f9;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 0.75rem;
  color: #475569;
}

.assigned-widget i {
  font-size: 0.875rem;
}

.btn-remove {
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.btn-remove:hover {
  background: #ef4444;
  color: white;
}

.no-widgets {
  color: #94a3b8;
  font-size: 0.75rem;
  font-style: italic;
  text-align: center;
  padding: 8px;
}

/* ===== TAB SETTINGS ===== */
.tab-settings {
  border-top: 1px solid #e2e8f0;
  padding-top: 16px;
}

.tab-settings h5 {
  margin: 0 0 12px 0;
  font-size: 0.875rem;
  color: #374151;
}

.setting-group {
  margin-bottom: 12px;
}

.setting-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #475569;
  cursor: pointer;
}

.setting-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.setting-group select {
  margin-left: 8px;
  padding: 4px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
}

/* ===== WIDGET ASSIGNMENT ===== */
.widget-filter {
  display: flex;
  align-items: center;
}

.widget-search {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
}

.widget-search:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.tab-selector {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.tab-selector label {
  display: block;
  font-size: 0.875rem;
  color: #374151;
  margin-bottom: 6px;
}

.tab-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  background: white;
}

.widgets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 12px;
}

.widget-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.widget-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.widget-icon {
  width: 32px;
  height: 32px;
  background: #f1f5f9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #475569;
}

.widget-info {
  flex: 1;
}

.widget-title {
  font-weight: 500;
  font-size: 0.875rem;
  color: #374151;
  margin-bottom: 2px;
}

.widget-type {
  font-size: 0.75rem;
  color: #64748b;
  text-transform: capitalize;
}

.widget-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.widget-item:hover .widget-actions {
  opacity: 1;
}

.btn-assign {
  background: #3b82f6;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.btn-assign:hover {
  background: #2563eb;
  transform: scale(1.1);
}

/* ===== PREVIEW SECTION ===== */
.config-preview {
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.preview-header h4 {
  margin: 0;
  font-size: 1rem;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-preview-mode {
  background: #6366f1;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.btn-preview-mode:hover {
  background: #5b21b6;
}

.preview-content {
  padding: 16px 24px;
  height: 200px;
  overflow-y: auto;
}

.tab-preview-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.preview-tabs {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.preview-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 0.875rem;
  color: #64748b;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.preview-tab.active {
  background: white;
  color: #374151;
  font-weight: 500;
}

.preview-tab:hover {
  background: #f1f5f9;
}

.preview-tab-count {
  background: #e2e8f0;
  color: #64748b;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 500;
}

.preview-tab.active .preview-tab-count {
  background: currentColor;
  color: white;
}

.preview-content {
  padding: 16px;
  min-height: 120px;
}

.empty-tab-preview {
  text-align: center;
  color: #94a3b8;
  padding: 24px;
}

.empty-tab-preview i {
  font-size: 2rem;
  margin-bottom: 8px;
  display: block;
}

.empty-tab-preview h4 {
  margin: 0 0 4px 0;
  font-size: 1rem;
}

.empty-tab-preview p {
  margin: 0;
  font-size: 0.875rem;
}

.tab-widgets-preview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.preview-widget {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
}

.preview-widget-header {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 10px;
  background: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.75rem;
  font-weight: 500;
  color: #475569;
}

.preview-widget-content {
  padding: 8px 10px;
}

.widget-placeholder {
  background: #e2e8f0;
  height: 40px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  color: #64748b;
}

/* ===== ACTION BUTTONS ===== */
.config-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.btn-primary {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background: #f8fafc;
  color: #475569;
  border: 1px solid #d1d5db;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #f1f5f9;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

/* ===== TOAST NOTIFICATIONS ===== */
.canvas-studio-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  border-left: 4px solid #3b82f6;
}

.canvas-studio-toast.show {
  transform: translateX(0);
}

.canvas-studio-toast.success {
  border-left-color: #10b981;
}

.canvas-studio-toast.error {
  border-left-color: #ef4444;
}

.canvas-studio-toast.warning {
  border-left-color: #f59e0b;
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  font-size: 0.875rem;
  color: #374151;
}

.toast-content i {
  font-size: 1.125rem;
}

.canvas-studio-toast.success .toast-content i {
  color: #10b981;
}

.canvas-studio-toast.error .toast-content i {
  color: #ef4444;
}

.canvas-studio-toast.warning .toast-content i {
  color: #f59e0b;
}

.canvas-studio-toast.info .toast-content i {
  color: #3b82f6;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .canvas-studio-modal.large .modal-content {
    width: 95vw;
    height: 95vh;
  }

  .config-main {
    flex-direction: column;
  }

  .config-tabs-panel,
  .config-widgets-panel {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e2e8f0;
  }

  .config-tabs-panel {
    max-height: 300px;
  }

  .config-widgets-panel {
    max-height: 250px;
  }
}

@media (max-width: 768px) {
  .canvas-studio-modal .modal-content {
    margin: 10px;
    width: calc(100vw - 20px);
    height: calc(100vh - 20px);
  }

  .config-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .config-stats {
    align-self: stretch;
    justify-content: space-around;
  }

  .widgets-grid {
    grid-template-columns: 1fr;
  }

  .tab-widgets-preview {
    grid-template-columns: 1fr;
  }
}

/* ===== SCROLLBAR STYLES ===== */
.panel-content::-webkit-scrollbar,
.preview-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track,
.preview-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.panel-content::-webkit-scrollbar-thumb,
.preview-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.preview-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* ===== DRAG AND DROP STYLES ===== */
.widget-item {
  cursor: grab;
  transition: all 0.2s ease;
}

.widget-item:active {
  cursor: grabbing;
}

.widget-item.dragging {
  opacity: 0.6;
  transform: scale(0.95);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.drag-over {
  background-color: rgba(59, 130, 246, 0.1) !important;
  border: 2px dashed #3b82f6 !important;
  transform: scale(1.02);
  transition: all 0.2s ease;
}

.preview-tab.drag-over {
  background-color: rgba(59, 130, 246, 0.15) !important;
  border-bottom: 3px solid #3b82f6 !important;
}

.empty-tab-preview.drag-over {
  background-color: rgba(59, 130, 246, 0.05);
  border: 2px dashed #3b82f6;
  border-radius: 8px;
}

.tab-widgets-preview.drag-over {
  background-color: rgba(59, 130, 246, 0.05);
  border: 2px dashed #3b82f6;
  border-radius: 8px;
}

/* Remove widget button styles */
.remove-widget-btn {
  background: rgba(239, 68, 68, 0.1);
  border: none;
  color: #ef4444;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.2s ease;
  font-size: 12px;
}

.preview-widget:hover .remove-widget-btn {
  opacity: 1;
}

.remove-widget-btn:hover {
  background: #ef4444;
  color: white;
  transform: scale(1.1);
}

.preview-widget-header {
  position: relative;
}

/* Enhanced widget item hover effects */
.widget-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Visual feedback for successful drops */
@keyframes dropSuccess {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.drop-success {
  animation: dropSuccess 0.3s ease;
}
