// Console Test Script for Section Container Scroll Fix
// Run this in your browser console on index2.html

console.log('🔧 Testing Section Container Scroll Fix...');

// Test function to check if the fix is working
function testScrollFix() {
  console.log('🧪 Running scroll fix tests...');
  
  // Check if GridStack Auto-Scroll is loaded
  if (!window.GridStackAutoScrollAuto) {
    console.error('❌ GridStack Auto-Scroll not found!');
    return false;
  }
  
  console.log('✅ GridStack Auto-Scroll is loaded');
  
  // Enable debug mode to see what's happening
  window.GridStackAutoScrollAuto.enableDebug();
  console.log('🔍 Debug mode enabled');
  
  // Check current configuration
  const config = window.GridStackAutoScrollAuto.config;
  console.log('📊 Current config:', config);
  
  // Check if we're currently in a section container
  const isInSection = window.GridStackAutoScrollAuto.isInSectionContainer;
  console.log('📍 Currently in section container:', isInSection);
  
  // Test section container detection
  const testElement = document.querySelector('.section-container-widget');
  if (testElement) {
    console.log('✅ Found section container widget');
    
    // Test if the element is properly detected
    const rect = testElement.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    const isOverSection = window.GridStackAutoScrollAuto.isOverSectionContainer(centerX, centerY);
    console.log('🎯 Section container detection test:', isOverSection);
    
    if (isOverSection) {
      console.log('✅ Section container detection is working!');
    } else {
      console.log('❌ Section container detection failed!');
    }
  } else {
    console.log('⚠️ No section container found. Create one first using the section gallery.');
  }
  
  return true;
}

// Function to simulate mouse movement and test scroll prevention
function testMouseMovement() {
  console.log('🖱️ Testing mouse movement scroll prevention...');
  
  if (!window.GridStackAutoScrollAuto) {
    console.error('❌ GridStack Auto-Scroll not found!');
    return;
  }
  
  // Find a section container
  const sectionContainer = document.querySelector('.section-container-widget');
  if (!sectionContainer) {
    console.log('⚠️ No section container found. Create one first.');
    return;
  }
  
  // Get the center of the section container
  const rect = sectionContainer.getBoundingClientRect();
  const centerX = rect.left + rect.width / 2;
  const centerY = rect.top + rect.height / 2;
  
  console.log('📍 Section container center:', { x: centerX, y: centerY });
  
  // Test if auto-scroll is prevented when over section container
  const isOverSection = window.GridStackAutoScrollAuto.isOverSectionContainer(centerX, centerY);
  console.log('🎯 Is over section container:', isOverSection);
  
  if (isOverSection) {
    console.log('✅ Auto-scroll should be prevented when over section container');
    
    // Check if auto-scroll is currently active
    const hasScrollInterval = !!window.GridStackAutoScrollAuto.scrollInterval;
    console.log('📜 Auto-scroll currently active:', hasScrollInterval);
    
    if (!hasScrollInterval) {
      console.log('✅ Auto-scroll is properly disabled over section container');
    } else {
      console.log('❌ Auto-scroll is still active over section container!');
    }
  }
}

// Function to create a test section container if none exists
function createTestSection() {
  console.log('🏗️ Creating test section container...');
  
  // Check if we have the function available
  if (typeof addSectionContainerWidget === 'function') {
    const widget = addSectionContainerWidget();
    console.log('✅ Test section container created:', widget);
    return widget;
  } else if (typeof createDashboardSection === 'function') {
    createDashboardSection();
    console.log('✅ Test dashboard section created');
  } else {
    console.log('⚠️ No section creation function found. You may need to create one manually.');
  }
}

// Function to run all tests
function runAllTests() {
  console.log('🚀 Running all scroll fix tests...');
  console.log('=====================================');
  
  // Test 1: Basic functionality
  const basicTest = testScrollFix();
  if (!basicTest) {
    console.log('❌ Basic test failed. Cannot continue.');
    return;
  }
  
  console.log('=====================================');
  
  // Test 2: Mouse movement
  testMouseMovement();
  
  console.log('=====================================');
  
  // Test 3: Create test section if needed
  if (!document.querySelector('.section-container-widget')) {
    createTestSection();
  }
  
  console.log('=====================================');
  console.log('🎯 Test Summary:');
  console.log('- Check the console for any error messages');
  console.log('- Try moving your mouse over the section container');
  console.log('- The page should NOT scroll when over section containers');
  console.log('- Auto-scroll should only work near viewport edges (outside sections)');
}

// Function to monitor scroll events
function monitorScrollEvents() {
  console.log('👀 Monitoring scroll events...');
  
  let scrollCount = 0;
  const originalScrollBy = window.scrollBy;
  
  // Override scrollBy to monitor calls
  window.scrollBy = function(...args) {
    scrollCount++;
    console.log(`📜 Scroll event #${scrollCount}:`, args);
    
    // Check if we're over a section container
    if (window.GridStackAutoScrollAuto && window.GridStackAutoScrollAuto.lastMousePosition) {
      const { x, y } = window.GridStackAutoScrollAuto.lastMousePosition;
      const isOverSection = window.GridStackAutoScrollAuto.isOverSectionContainer(x, y);
      
      if (isOverSection) {
        console.warn('⚠️ WARNING: Scroll attempted while over section container!');
        console.warn('📍 Mouse position:', { x, y });
        console.warn('🎯 This should NOT happen with the fix!');
      }
    }
    
    // Call original function
    return originalScrollBy.apply(this, args);
  };
  
  console.log('✅ Scroll monitoring enabled. Check console for scroll events.');
  console.log('⚠️ WARNING: This will show ALL scroll events, including manual scrolling.');
}

// Auto-run tests when script is loaded
console.log('🔄 Auto-running tests in 2 seconds...');
setTimeout(() => {
  runAllTests();
}, 2000);

// Export functions for manual testing
window.testScrollFix = testScrollFix;
window.testMouseMovement = testMouseMovement;
window.createTestSection = createTestSection;
window.runAllTests = runAllTests;
window.monitorScrollEvents = monitorScrollEvents;

console.log('📚 Available test functions:');
console.log('- testScrollFix() - Basic functionality test');
console.log('- testMouseMovement() - Mouse movement test');
console.log('- createTestSection() - Create test section container');
console.log('- runAllTests() - Run all tests');
console.log('- monitorScrollEvents() - Monitor scroll events (use with caution)');
