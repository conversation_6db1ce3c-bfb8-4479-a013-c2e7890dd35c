/**
 * Test script to verify widget drag & drop setup is working
 * Run this in the browser console to check if drag-in is properly configured
 */

function testWidgetDragDropSetup() {
  console.log('🧪 Testing Widget Drag & Drop Setup...');
  
  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };
  
  function addTest(name, condition, message) {
    const passed = condition;
    results.tests.push({ name, passed, message });
    if (passed) {
      results.passed++;
      console.log(`✅ ${name}: ${message}`);
    } else {
      results.failed++;
      console.log(`❌ ${name}: ${message}`);
    }
  }
  
  // Test 1: Check if WidgetDragDropSetup is available
  addTest(
    'WidgetDragDropSetup Available',
    typeof window.WidgetDragDropSetup === 'object',
    typeof window.WidgetDragDropSetup === 'object' ? 'WidgetDragDropSetup object found' : 'WidgetDragDropSetup not found'
  );
  
  // Test 2: Check if GridStack is available
  addTest(
    'GridStack Available',
    typeof GridStack !== 'undefined',
    typeof GridStack !== 'undefined' ? 'GridStack library loaded' : 'GridStack library not found'
  );
  
  // Test 3: Check if widget gallery exists
  const widgetGallery = document.querySelector('.widget-gallery');
  addTest(
    'Widget Gallery Exists',
    widgetGallery !== null,
    widgetGallery ? 'Widget gallery found in DOM' : 'Widget gallery not found'
  );
  
  // Test 4: Check for text widget in gallery
  const textWidget = document.querySelector('.widget-item[data-widget-type="text"]');
  addTest(
    'Text Widget in Gallery',
    textWidget !== null,
    textWidget ? 'Text widget found with correct data-widget-type' : 'Text widget not found'
  );
  
  // Test 5: Check for table widget in gallery
  const tableWidget = document.querySelector('.widget-item[data-widget-type="table"]');
  addTest(
    'Table Widget in Gallery',
    tableWidget !== null,
    tableWidget ? 'Table widget found with correct data-widget-type' : 'Table widget not found'
  );
  
  // Test 6: Check for section container widget in gallery
  const sectionWidget = document.querySelector('.widget-item[data-widget-type="section-container"]');
  addTest(
    'Section Container Widget in Gallery',
    sectionWidget !== null,
    sectionWidget ? 'Section container widget found with correct data-widget-type' : 'Section container widget not found'
  );
  
  // Test 7: Check for pie chart widget in gallery
  const pieChartWidget = document.querySelector('.widget-item[data-widget-type="pie-chart"]');
  addTest(
    'Pie Chart Widget in Gallery',
    pieChartWidget !== null,
    pieChartWidget ? 'Pie chart widget found with correct data-widget-type' : 'Pie chart widget not found'
  );
  
  // Test 8: Check if hidden widgets are actually hidden
  const lineSeparatorWidget = document.querySelector('.widget-item[data-widget-type="line-separator"]');
  const isLineSeparatorHidden = lineSeparatorWidget && lineSeparatorWidget.classList.contains('d-none');
  addTest(
    'Line Separator Hidden',
    isLineSeparatorHidden,
    isLineSeparatorHidden ? 'Line separator widget is properly hidden' : 'Line separator widget not hidden or not found'
  );
  
  const imageWidget = document.querySelector('.widget-item[data-widget-type="image"]');
  const isImageWidgetHidden = imageWidget && imageWidget.classList.contains('d-none');
  addTest(
    'Image Widget Hidden',
    isImageWidgetHidden,
    isImageWidgetHidden ? 'Image widget is properly hidden' : 'Image widget not hidden or not found'
  );
  
  const notesWidget = document.querySelector('.widget-item[data-widget-type="notes-section"]');
  const isNotesWidgetHidden = notesWidget && notesWidget.classList.contains('d-none');
  addTest(
    'Notes Section Hidden',
    isNotesWidgetHidden,
    isNotesWidgetHidden ? 'Notes section widget is properly hidden' : 'Notes section widget not hidden or not found'
  );
  
  // Test 9: Check if main grid exists and accepts widgets
  const mainGrid = document.querySelector('#grid-container .grid-stack');
  addTest(
    'Main Grid Exists',
    mainGrid !== null,
    mainGrid ? 'Main grid container found' : 'Main grid container not found'
  );
  
  // Test 10: Check if grid has acceptWidgets enabled
  const gridInstance = mainGrid && mainGrid.gridstack;
  const acceptsWidgets = gridInstance && gridInstance.opts && gridInstance.opts.acceptWidgets;
  addTest(
    'Grid Accepts Widgets',
    acceptsWidgets === true,
    acceptsWidgets ? 'Main grid configured to accept widgets' : 'Main grid not configured to accept widgets'
  );

  // Test 11: Check if widget functions are available
  addTest(
    'addTextWidget Function Available',
    typeof window.addTextWidget === 'function',
    typeof window.addTextWidget === 'function' ? 'addTextWidget function found' : 'addTextWidget function not found'
  );

  addTest(
    'addTableWidget Function Available',
    typeof window.addTableWidget === 'function',
    typeof window.addTableWidget === 'function' ? 'addTableWidget function found' : 'addTableWidget function not found'
  );
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! Widget drag & drop setup is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the setup and configuration.');
  }
  
  return results;
}

// Auto-run test if in development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
  // Wait for DOM and scripts to load
  setTimeout(() => {
    testWidgetDragDropSetup();
  }, 2000);
}

// Export for manual testing
window.testWidgetDragDropSetup = testWidgetDragDropSetup;
