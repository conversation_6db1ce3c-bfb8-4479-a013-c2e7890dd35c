/* Section Widget Styling - Remove card styling from widgets inside section containers */

/* Target widgets inside section containers (nested grids) */
.grid-stack-nested
  .grid-stack-item
  .grid-stack-item-content
  > div[class*="-widget"] {
  /* Remove card styling */
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  margin: 0 !important;
}

/* Remove padding from widget containers inside sections */
.grid-stack-nested
  .grid-stack-item
  .grid-stack-item-content
  > div[class*="-widget"] {
  padding: 0 !important;
}

/* OVERRIDE inline-raita.css conflicting rules for widget headers inside sections */
.grid-stack-nested .grid-stack-item .widget-header {
  /* Override inline-raita.css positioning rules */
  display: flex !important;
  justify-content: space-between !important;
  position: static !important;
  width: 100% !important;
  opacity: 1 !important;

  /* Our minimal styling */
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  padding: 4px 8px !important;
  margin-bottom: 8px !important;
  font-size: 11px !important;
  color: #666 !important;
  border-bottom: 1px solid #e9ecef !important;

  /* Reset any absolute positioning */
  top: auto !important;
  right: auto !important;
  z-index: auto !important;
}

/* OVERRIDE inline-raita.css rule that hides the title */
.grid-stack-nested .grid-stack-item .widget-header > div:first-child {
  display: block !important;
}

/* OVERRIDE inline-raita.css rule for widget actions */
.grid-stack-nested .grid-stack-item .widget-header > div:last-child,
.grid-stack-nested .grid-stack-item .widget-icons {
  display: flex !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Preserve editable title functionality inside sections */
.grid-stack-nested .grid-stack-item .widget-header .editable-title {
  cursor: text !important;
  position: relative !important;
}

.grid-stack-nested
  .grid-stack-item
  .widget-header
  .editable-title:hover::after {
  content: "" !important;
  position: absolute !important;
  bottom: -2px !important;
  left: 0 !important;
  width: 100% !important;
  height: 1px !important;
  background-color: var(--ocean-teal, #00b19c) !important;
}

.grid-stack-nested .grid-stack-item .widget-header .title-edit-input {
  background: transparent !important;
  border: none !important;
  border-bottom: 1px solid var(--ocean-teal, #00b19c) !important;
  outline: none !important;
  padding: 0 !important;
  margin: 0 !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  color: inherit !important;
  font-family: inherit !important;
  width: calc(100% - 24px) !important;
}

.grid-stack-nested .grid-stack-item .widget-header .title-edit-input:focus {
  border-bottom: 2px solid var(--ocean-teal, #00b19c) !important;
}

/* Keep widget header buttons functional but minimal */
.grid-stack-nested .grid-stack-item .widget-header button {
  background: transparent !important;
  border: none !important;
  color: #666 !important;
  padding: 2px 4px !important;
  font-size: 12px !important;
}

.grid-stack-nested .grid-stack-item .widget-header button:hover {
  background: rgba(0, 0, 0, 0.1) !important;
  color: #333 !important;
}

/* Specific widget types inside sections */

/* Text Widget inside sections */
.grid-stack-nested .text-widget {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

.grid-stack-nested .text-widget .text-container {
  margin-top: 0 !important;
}

.grid-stack-nested .text-widget .text-content {
  border: none !important;
  background: #fff !important;
  border-radius: 4px !important;
}

/* Table Widget inside sections */
.grid-stack-nested .table-widget {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

.grid-stack-nested .table-widget .table-container {
  border: 1px solid #e9ecef !important;
  border-radius: 4px !important;
  background: #fff !important;
}

/* Image Widget inside sections */
.grid-stack-nested .image-widget {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

.grid-stack-nested .image-widget .image-container {
  border: 1px solid #e9ecef !important;
  border-radius: 4px !important;
  background: #fff !important;
}

/* Notes Section Widget inside sections */
.grid-stack-nested .notes-section-widget {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

.grid-stack-nested .notes-section-widget .notes-section-container {
  background: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  border-radius: 4px !important;
  padding: 8px !important;
}

/* Line Separator Widget inside sections */
.grid-stack-nested .line-separator-widget {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

/* Chart Widgets inside sections */
.grid-stack-nested .pie-chart-widget,
.grid-stack-nested .bar-chart-widget,
.grid-stack-nested .line-chart-widget,
.grid-stack-nested .area-chart-widget,
.grid-stack-nested .stack-chart-widget {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

.grid-stack-nested .chart-container {
  border: 1px solid #e9ecef !important;
  border-radius: 4px !important;
  background: #fff !important;
}

/* PDF Viewer Widget inside sections */
.grid-stack-nested .pdf-viewer-widget {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

/* Video Widget inside sections */
.grid-stack-nested .video-widget {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

/* Handsontable Widget inside sections */
.grid-stack-nested .handsontable-widget {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

.grid-stack-nested .spreadsheet-container {
  border: 1px solid #e9ecef !important;
  border-radius: 4px !important;
  background: #fff !important;
}

/* KPI Widget inside sections */
.grid-stack-nested .kpi-widget {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

/* Generic widget content inside sections */
.grid-stack-nested .widget-body,
.grid-stack-nested .widget-content {
  background: #fff !important;
  border: 1px solid #e9ecef !important;
  border-radius: 4px !important;
  margin-top: 0 !important;
}

/* Ensure content areas have proper styling */
.grid-stack-nested .grid-stack-item-content {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* When grid is empty, show placeholder */
.grid-stack-nested:empty::before {
  content: "Drop widgets here" !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: rgba(0, 177, 156, 0.6) !important;
  font-style: italic !important;
  height: 100% !important;
  min-height: 80px !important;
}

/* Hover effects for widgets inside sections */
.grid-stack-nested
  .grid-stack-item:hover
  .grid-stack-item-content
  > div[class*="-widget"] {
  background: rgba(0, 0, 0, 0.02) !important;
  border-radius: 4px !important;
}

/* Focus effects for accessibility */
.grid-stack-nested
  .grid-stack-item:focus-within
  .grid-stack-item-content
  > div[class*="-widget"] {
  outline: 2px solid rgba(0, 177, 156, 0.5) !important;
  outline-offset: 2px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .grid-stack-nested .grid-stack-item .widget-header {
    padding: 2px 6px !important;
    font-size: 10px !important;
  }

  .grid-stack-nested .grid-stack-item .widget-header button {
    padding: 1px 3px !important;
    font-size: 11px !important;
  }
}

/* Print styles */
@media print {
  .grid-stack-nested .grid-stack-item .widget-header button {
    display: none !important;
  }

  .grid-stack-nested
    .grid-stack-item
    .grid-stack-item-content
    > div[class*="-widget"] {
    border: 1px solid #ccc !important;
  }
}
