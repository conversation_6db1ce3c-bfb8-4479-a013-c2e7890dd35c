document.write(`
     <div class="modal fade" id="chartSettingsModal" tabindex="-1" aria-labelledby="chartSettingsModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="chartSettingsModalLabel">
            Chart Settings
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <!-- Modal content will be dynamically inserted here -->
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            Cancel
          </button>
          <button type="button" class="btn btn-primary" onclick="applyChartSettings()">
            Apply
          </button>
        </div>
      </div>
    </div>
  </div>
  
  
  <div
      class="offcanvas offcanvas-end"
      tabindex="-1"
      id="widget-comment-panel"
      aria-labelledby="widget-comment-title"
      data-bs-backdrop="true"
      data-bs-scroll="false"
    >
      <div class="comment-panel-wrapper">
        <!-- Comment Panel Header -->
        <div class="comment-panel-header">
          <div class="comment-panel-title">
            <div class="comment-panel-icon">
              <i class="las la-comment-dots"></i>
            </div>
            <h5 id="widget-comment-title">Comments</h5>
          </div>
          <div class="comment-panel-actions">
            <div class="comment-panel-status">
              <span class="status-indicator"></span>
              <span class="status-text">Preview Mode</span>
            </div>
            <button
              type="button"
              class="comment-panel-close"
              data-bs-dismiss="offcanvas"
              aria-label="Close"
            >
              <i class="las la-times"></i>
            </button>
          </div>
        </div>

        <!-- Comment Panel Content -->
        <div class="comment-panel-content">
          <!-- Widget Info -->
          <div class="widget-info">
            <div class="widget-info-icon">
              <i class="las la-chart-bar"></i>
            </div>
            <div class="widget-info-details">
              <h6 class="widget-info-title" id="comment-widget-title">
                Widget Title
              </h6>
              <p class="widget-info-type" id="comment-widget-type">
                Chart Widget
              </p>
            </div>
          </div>

          <!-- Comment Thread -->
          <div class="comment-thread" id="comment-list">
            <!-- Comments will be added here dynamically -->
            <div class="no-comments-message">
              <div class="no-comments-icon">
                <i class="las la-comments"></i>
              </div>
              <h6>No comments yet</h6>
              <p>Be the first to add a comment to this widget</p>
            </div>
          </div>
        </div>

        <!-- Comment Panel Footer -->
        <div class="comment-panel-footer">
          <div class="comment-form">
            <div class="comment-form-avatar">
              <div class="user-avatar">VT</div>
            </div>
            <div class="comment-form-input">
              <textarea
                id="comment-text"
                placeholder="Add a comment..."
              ></textarea>
              <div class="comment-form-actions">
                <button
                  type="button"
                  id="submit-comment-btn"
                  class="comment-submit"
                >
                  <span>Send</span>
                </button>
              </div>
            </div>
        
            </div>
        </div>
      </div>
    </div>


    <div
      class="modal fade"
      id="feedback-modal"
      tabindex="-1"
      aria-hidden="true"
      data-bs-backdrop="static"
    >
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Dashboard Feedback</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="feedback-type" class="form-label"
                >Feedback Type</label
              >
              <select class="form-select" id="feedback-type">
                <option value="general">General Feedback</option>
                <option value="design">Design Feedback</option>
                <option value="content">Content Feedback</option>
                <option value="functionality">Functionality Feedback</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="feedback-text" class="form-label"
                >Your Feedback</label
              >
              <textarea
                class="form-control"
                id="feedback-text"
                rows="5"
                placeholder="Enter your feedback here..."
              ></textarea>
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-primary"
              id="submit-feedback-btn"
            >
              Submit Feedback
            </button>
          </div>
        </div>
      </div>
    </div>


  <div
      class="modal fade"
      id="notification-modal"
      tabindex="-1"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="notification-title">Notification</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div class="notification-content">
              <div class="notification-icon mb-3 text-center">
                <i
                  id="notification-icon"
                  class="las la-check-circle"
                  style="font-size: 3rem"
                ></i>
              </div>
              <p id="notification-message" class="text-center fs-5">
                Operation completed successfully.
              </p>
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-primary"
              data-bs-dismiss="modal"
            >
              OK
            </button>
          </div>
        </div>
      </div>
    </div>
<div
      class="modal fade"
      id="confirmation-modal"
      tabindex="-1"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="confirmation-title">Confirmation</h5>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <div class="confirmation-content">
              <div class="confirmation-icon mb-3 text-center">
                <i
                  id="confirmation-icon"
                  class="las la-question-circle"
                  style="font-size: 3rem"
                ></i>
              </div>
              <p id="confirmation-message" class="text-center fs-5">
                Are you sure you want to proceed?
              </p>
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button
              type="button"
              class="btn btn-primary"
              id="confirm-action-btn"
            >
              Confirm
            </button>
          </div>
        </div>
      </div>
    </div>


    <div id="offcanvasContainer">
      <!-- Workspace Panel -->
      <div
        class="offcanvas offcanvas-end workspace-panel"
        tabindex="-1"
        id="workspace-panel"
        aria-labelledby="workspace-panel-label"
        style="width: 520px; font-size: 12px"
      >
        <div
          class="offcanvas-header"
          style="border-bottom: 1px solid #e5e7eb; padding: 16px"
        >
          <h5
            class="offcanvas-title"
            id="workspace-panel-label"
            style="font-size: 18px; font-weight: 500"
          >
            Workspace Management
          </h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="offcanvas"
            aria-label="Close"
          ></button>
        </div>
        <div class="offcanvas-body p-0">
          <!-- Navigation Tabs -->
          <div
            class="nav nav-tabs"
            style="background: #f8fafc; border-bottom: 1px solid #e5e7eb"
          >
            <button
              class="nav-link active px-4 py-3 border-0"
              id="my-workspace-tab"
              data-bs-toggle="tab"
              data-bs-target="#my-workspace"
              type="button"
              style="font-size: 13px"
            >
              <i class="las la-layout-grid"></i> My Workspace
            </button>
            <button
              class="nav-link px-4 py-3 border-0"
              id="assignments-tab"
              data-bs-toggle="tab"
              data-bs-target="#assignments"
              type="button"
              style="font-size: 13px"
            >
              <i class="las la-users-cog"></i> Assignments
            </button>
            <button
              class="nav-link px-4 py-3 border-0"
              id="sections-tab"
              data-bs-toggle="tab"
              data-bs-target="#sections"
              type="button"
              style="font-size: 13px"
            >
              <i class="las la-shapes"></i> Sections
            </button>
          </div>

          <!-- Tab Content -->
          <div class="tab-content">
            <!-- My Workspace Tab -->
            <div class="tab-pane fade show active p-4" id="my-workspace">
              <div
                class="d-flex justify-content-between align-items-center mb-4"
              >
                <h6 style="font-size: 14px; font-weight: 500">My Analytics</h6>
                <div class="input-group" style="width: 240px">
                  <span
                    class="input-group-text border-0"
                    style="background: #f8fafc"
                  >
                    <i class="las la-search"></i>
                  </span>
                  <input
                    type="text"
                    class="form-control border-0"
                    style="background: #f8fafc"
                    placeholder="Search..."
                  />
                </div>
              </div>

              <div class="workspace-items">
                <!-- Items will be populated by JS -->
              </div>
            </div>

            <!-- Assignments Tab -->
            <div class="tab-pane fade p-4" id="assignments">
              <div
                class="d-flex justify-content-between align-items-center mb-4"
              >
                <h6 style="font-size: 14px; font-weight: 500">
                  Manage Assignments
                </h6>
                <button
                  class="btn btn-sm btn-primary"
                  style="font-size: 12px"
                  onclick="createNewAssignment()"
                >
                  <i class="las la-plus"></i> New Assignment
                </button>
              </div>

              <!-- Assignment Form -->
              <div
                class="assignment-form mb-4"
                style="background: #f8fafc; padding: 16px"
              >
                <div class="mb-3">
                  <label class="form-label" style="font-size: 12px"
                    >Select Item Type</label
                  >
                  <select
                    class="form-select form-select-sm"
                    id="itemTypeSelect"
                  >
                    <option value="section">Section</option>
                    <option value="widget">Widget</option>
                  </select>
                </div>

                <div class="mb-3">
                  <label class="form-label" style="font-size: 12px"
                    >Select Item</label
                  >
                  <select class="form-select form-select-sm" id="itemSelect">
                    <option value="" disabled selected>Choose item...</option>
                    <!-- Sections -->
                    <optgroup label="Sections">
                      <option value="analytics">Analytics Dashboards</option>
                      <option value="reports">Generated Reports</option>
                      <option value="custom">Custom Dashboards</option>
                    </optgroup>
                    <!-- Widgets -->
                    <optgroup label="Widgets">
                      <option value="revenue">Revenue Analysis</option>
                      <option value="users">User Engagement</option>
                      <option value="conversion">Conversion Rates</option>
                      <option value="performance">Team Performance</option>
                    </optgroup>
                  </select>
                </div>

                <div class="mb-3">
                  <label class="form-label" style="font-size: 12px"
                    >Assign To</label
                  >
                  <select
                    class="form-select form-select-sm"
                    id="assigneeSelect"
                  >
                    <option value="" disabled selected>Select user...</option>
                    <option value="1">Deepak Singh</option>
                    <option value="2">Sneha Issar</option>
                    <option value="3">Piyush Kumar</option>
                    <option value="4">Vimal Thapliyal</option>
                    <option value="5">Reyansh Sharma</option>
                  </select>
                </div>

                <div class="d-flex justify-content-end gap-2">
                  <button
                    class="btn btn-sm btn-light"
                    style="font-size: 12px"
                    onclick="cancelAssignment()"
                  >
                    <i class="las la-times"></i> Cancel
                  </button>
                  <button
                    class="btn btn-sm btn-primary"
                    style="font-size: 12px"
                    onclick="saveAssignment()"
                  >
                    <i class="las la-check"></i> Assign
                  </button>
                </div>
              </div>

              <!-- Current Assignments List -->
              <div class="current-assignments">
                <h6 class="mb-3" style="font-size: 13px">
                  Current Assignments
                </h6>
                <div class="list-group">
                  <div
                    class="list-group-item border-0 mb-2"
                    style="background: #f8fafc"
                  >
                    <div
                      class="d-flex justify-content-between align-items-center"
                    >
                      <div>
                        <div style="font-size: 13px">Analytics Dashboards</div>
                        <small class="text-muted" style="font-size: 11px"
                          >Section • Deepak Singh</small
                        >
                      </div>
                      <button
                        class="btn btn-sm btn-light"
                        onclick="removeAssignment(1)"
                      >
                        <i class="las la-times"></i>
                      </button>
                    </div>
                  </div>
                  <div
                    class="list-group-item border-0 mb-2"
                    style="background: #f8fafc"
                  >
                    <div
                      class="d-flex justify-content-between align-items-center"
                    >
                      <div>
                        <div style="font-size: 13px">Revenue Analysis</div>
                        <small class="text-muted" style="font-size: 11px"
                          >Widget • Sneha Issar</small
                        >
                      </div>
                      <button
                        class="btn btn-sm btn-light"
                        onclick="removeAssignment(2)"
                      >
                        <i class="las la-times"></i>
                      </button>
                    </div>
                  </div>
                  <div
                    class="list-group-item border-0 mb-2"
                    style="background: #f8fafc"
                  >
                    <div
                      class="d-flex justify-content-between align-items-center"
                    >
                      <div>
                        <div style="font-size: 13px">Custom Dashboards</div>
                        <small class="text-muted" style="font-size: 11px"
                          >Section • Piyush Kumar</small
                        >
                      </div>
                      <button
                        class="btn btn-sm btn-light"
                        onclick="removeAssignment(3)"
                      >
                        <i class="las la-times"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Sections Tab -->
            <div class="tab-pane fade p-4" id="sections">
              <div
                class="d-flex justify-content-between align-items-center mb-4"
              >
                <h6 style="font-size: 14px; font-weight: 500">
                  Manage Sections
                </h6>
                <button class="btn btn-sm btn-primary" style="font-size: 12px">
                  <i class="las la-plus"></i> New Section
                </button>
              </div>

              <div class="sections-list">
                <!-- Analytics Section -->
                <div
                  class="section-item mb-3"
                  style="border: 1px solid #e5e7eb"
                >
                  <div
                    class="section-header d-flex justify-content-between align-items-center p-3"
                    style="background: #f8fafc"
                  >
                    <div>
                      <div style="font-size: 13px; font-weight: 500">
                        Analytics Dashboards
                      </div>
                      <small class="text-muted" style="font-size: 11px"
                        >3 Widgets • Assigned to Deepak Singh</small
                      >
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-light" title="Edit">
                        <i class="las la-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-light" title="Delete">
                        <i class="las la-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Reports Section -->
                <div
                  class="section-item mb-3"
                  style="border: 1px solid #e5e7eb"
                >
                  <div
                    class="section-header d-flex justify-content-between align-items-center p-3"
                    style="background: #f8fafc"
                  >
                    <div>
                      <div style="font-size: 13px; font-weight: 500">
                        Generated Reports
                      </div>
                      <small class="text-muted" style="font-size: 11px"
                        >2 Widgets • Assigned to Sneha Issar</small
                      >
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-light" title="Edit">
                        <i class="las la-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-light" title="Delete">
                        <i class="las la-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Custom Dashboards Section -->
                <div
                  class="section-item mb-3"
                  style="border: 1px solid #e5e7eb"
                >
                  <div
                    class="section-header d-flex justify-content-between align-items-center p-3"
                    style="background: #f8fafc"
                  >
                    <div>
                      <div style="font-size: 13px; font-weight: 500">
                        Custom Dashboards
                      </div>
                      <small class="text-muted" style="font-size: 11px"
                        >4 Widgets • Assigned to Piyush Kumar</small
                      >
                    </div>
                    <div class="btn-group">
                      <button class="btn btn-sm btn-light" title="Edit">
                        <i class="las la-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-light" title="Delete">
                        <i class="las la-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Bar Chart Settings -->
      <div
        class="offcanvas offcanvas-end"
        id="barChartSettings"
        tabindex="-1"
        style="width: 420px"
      >
        <div class="offcanvas-header">
          <h5 class="offcanvas-title">Chart Configuration</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="offcanvas"
          ></button>
        </div>
        <div class="offcanvas-body">
          <!-- Chart Appearance -->
          <h6 class="mb-3"><i class="las la-palette"></i> Visual Settings</h6>

          <!-- Chart Title -->
          <div class="mb-4">
            <label class="form-label">Chart Title</label>
            <input
              type="text"
              class="form-control"
              id="barChartSettings-chartTitle"
              value="Q4 2023 Revenue Analysis"
            />
          </div>

          <!-- Y-Axis Title -->
          <div class="mb-4">
            <label class="form-label">Y-Axis Label</label>
            <input
              type="text"
              class="form-control"
              id="barChartSettings-yAxisTitle"
              value="Revenue (USD)"
            />
          </div>

          <!-- Chart Type -->
          <div class="mb-3">
            <label class="form-label">Chart Type</label>
            <select class="form-select" id="barChartSettings-type">
              <option value="clustered">Clustered</option>
              <option value="stacked">Stacked</option>
            </select>
          </div>

          <!-- Column Width -->
          <div class="mb-3">
            <label class="form-label">Column Width (%)</label>
            <input
              type="range"
              class="form-range"
              min="30"
              max="100"
              value="80"
              id="barChartSettings-columnWidth"
            />
          </div>

          <!-- Corner Radius -->
          <div class="mb-3">
            <label class="form-label">Corner Radius (px)</label>
            <input
              type="range"
              class="form-range"
              min="0"
              max="10"
              value="0"
              id="barChartSettings-cornerRadius"
            />
          </div>

          <!-- Labels -->
          <div class="mb-3">
            <div class="form-check form-switch">
              <input
                class="form-check-input"
                type="checkbox"
                id="barChartSettings-labels"
                checked
              />
              <label class="form-check-label" for="barChartSettings-labels"
                >Show Labels</label
              >
            </div>
          </div>

          <!-- Grid Lines -->
          <div class="mb-3">
            <div class="form-check form-switch">
              <input
                class="form-check-input"
                type="checkbox"
                id="barChartSettings-gridLines"
                checked
              />
              <label class="form-check-label" for="barChartSettings-gridLines"
                >Show Grid Lines</label
              >
            </div>
          </div>

          <!-- Legend -->
          <div class="mb-3">
            <div class="form-check form-switch">
              <input
                class="form-check-input"
                type="checkbox"
                id="barChartSettings-legend"
                checked
              />
              <label class="form-check-label" for="barChartSettings-legend"
                >Show Legend</label
              >
            </div>
          </div>

          <!-- Widget Border Settings -->
          <div class="mb-4">
            <h6 class="mb-3 d-flex align-items-center">
              <i class="las la-border-style me-2 text-primary"></i>
              <span>Widget Borders</span>
            </h6>

            <!-- Border Preview -->
            <div class="border-preview-container mb-3">
              <div class="border-preview" id="barChartBorderPreview">
                <div class="preview-content">
                  <i class="las la-chart-bar text-muted"></i>
                  <small class="text-muted d-block mt-1">Preview</small>
                </div>
              </div>
            </div>

            <!-- Border Controls -->
            <div class="border-controls">
              <!-- Border Toggle Buttons -->
              <div class="border-toggles mb-3">
                <div class="row g-2">
                  <div class="col-6">
                    <button type="button" class="btn btn-outline-primary btn-sm w-100 border-toggle"
                            data-border="top" id="barChart-border-top">
                      <i class="las la-minus"></i> Top
                    </button>
                  </div>
                  <div class="col-6">
                    <button type="button" class="btn btn-outline-primary btn-sm w-100 border-toggle"
                            data-border="right" id="barChart-border-right">
                      <i class="las la-grip-lines-vertical"></i> Right
                    </button>
                  </div>
                  <div class="col-6">
                    <button type="button" class="btn btn-outline-primary btn-sm w-100 border-toggle"
                            data-border="bottom" id="barChart-border-bottom">
                      <i class="las la-minus"></i> Bottom
                    </button>
                  </div>
                  <div class="col-6">
                    <button type="button" class="btn btn-outline-primary btn-sm w-100 border-toggle"
                            data-border="left" id="barChart-border-left">
                      <i class="las la-grip-lines-vertical"></i> Left
                    </button>
                  </div>
                </div>
              </div>

              <!-- Border Style -->
              <div class="mb-3">
                <label class="form-label small fw-semibold">Border Style</label>
                <select class="form-select form-select-sm" id="barChart-border-style">
                  <option value="solid">Solid</option>
                  <option value="dashed">Dashed</option>
                  <option value="dotted">Dotted</option>
                  <option value="double">Double</option>
                </select>
              </div>

              <!-- Border Width -->
              <div class="mb-3">
                <label class="form-label small fw-semibold">Border Width</label>
                <div class="d-flex align-items-center">
                  <input type="range" class="form-range me-3" min="1" max="5" value="1"
                         id="barChart-border-width">
                  <span class="badge bg-light text-dark" id="barChart-border-width-value">1px</span>
                </div>
              </div>

              <!-- Border Color -->
              <div class="mb-3">
                <label class="form-label small fw-semibold">Border Color</label>
                <div class="border-color-options">
                  <div class="row g-2">
                    <div class="col-4">
                      <button type="button" class="btn btn-sm w-100 color-option active"
                              data-color="#e5e9f0" style="background-color: #e5e9f0; border: 2px solid #dee2e6;">
                        <small>Light</small>
                      </button>
                    </div>
                    <div class="col-4">
                      <button type="button" class="btn btn-sm w-100 color-option"
                              data-color="#8dbac4" style="background-color: #8dbac4; color: white;">
                        <small>Slate</small>
                      </button>
                    </div>
                    <div class="col-4">
                      <button type="button" class="btn btn-sm w-100 color-option"
                              data-color="#02104f" style="background-color: #02104f; color: white;">
                        <small>Navy</small>
                      </button>
                    </div>
                    <div class="col-4">
                      <button type="button" class="btn btn-sm w-100 color-option"
                              data-color="#00b19c" style="background-color: #00b19c; color: white;">
                        <small>Teal</small>
                      </button>
                    </div>
                    <div class="col-4">
                      <button type="button" class="btn btn-sm w-100 color-option"
                              data-color="#3bcd3f" style="background-color: #3bcd3f; color: white;">
                        <small>Green</small>
                      </button>
                    </div>
                    <div class="col-4">
                      <button type="button" class="btn btn-sm w-100 color-option"
                              data-color="#007365" style="background-color: #007365; color: white;">
                        <small>Forest</small>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Quick Presets -->
              <div class="mb-3">
                <label class="form-label small fw-semibold">Quick Presets</label>
                <div class="btn-group w-100" role="group">
                  <button type="button" class="btn btn-outline-secondary btn-sm border-preset" data-preset="none">
                    <i class="las la-times"></i> None
                  </button>
                  <button type="button" class="btn btn-outline-secondary btn-sm border-preset" data-preset="all">
                    <i class="las la-border-all"></i> All
                  </button>
                  <button type="button" class="btn btn-outline-secondary btn-sm border-preset" data-preset="card">
                    <i class="las la-square"></i> Card
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Data Management -->
          <div class="mb-4">
            <h6 class="mb-3">Data Management</h6>

            <!-- JSON Search Section -->
            <div class="data-editor-section mb-3">
              <div class="section-title">Search JSON Data</div>
              <div class="row g-2 mb-2">
                <div class="col-8">
                  <input
                    type="text"
                    class="form-control form-control-sm"
                    id="barChartJsonSearch"
                    placeholder="Search for JSON data..."
                  />
                </div>
                <div class="col-4">
                  <button
                    class="btn btn-sm btn-primary w-100"
                    id="barChartSearchBtn"
                    onclick="searchBarChartJSON()"
                  >
                    <i class="las la-search"></i> Search
                  </button>
                </div>
              </div>
              <div class="row g-2 mb-2">
                <div class="col-12">
                  <select
                    class="form-select form-select-sm"
                    id="barChartDataSource"
                  >
                    <option value="local">Local Assets</option>
                    <option value="tsc20">TSC 2.0 Data</option>
                    <option value="remote">Remote Repository</option>
                  </select>
                </div>
              </div>
              <div
                id="barChartSearchResults"
                class="search-results mt-2"
                style="display: none"
              >
                <div
                  class="search-results-header d-flex justify-content-between align-items-center"
                >
                  <span class="small fw-bold">Search Results</span>
                  <button
                    class="btn btn-sm btn-link p-0"
                    onclick="toggleSearchResults()"
                  >
                    <i class="las la-times"></i>
                  </button>
                </div>
                <div class="search-results-body mt-2">
                  <!-- Results will be populated dynamically -->
                </div>
              </div>
            </div>

            <!-- Data Import/Export Section -->
            <div class="data-editor-section">
              <div class="section-title">Import/Export Data</div>
              <div class="row g-2 mb-2">
                <div class="col-8">
                  <input
                    type="file"
                    class="form-control form-control-sm"
                    id="barChartDataFile"
                    accept=".csv"
                  />
                </div>
                <div class="col-4">
                  <button
                    class="btn btn-sm btn-outline-secondary w-100"
                    id="barChartDataImport"
                    onclick="importBarChartCSV()"
                  >
                    <i class="las la-upload"></i> Import CSV
                  </button>
                </div>
              </div>
              <div class="row g-2">
                <div class="col-12">
                  <button
                    class="btn btn-sm btn-outline-secondary w-100"
                    id="barChartDataExport"
                    onclick="exportBarChartCSV()"
                  >
                    <i class="las la-download"></i> Export to CSV
                  </button>
                </div>
              </div>
              <div class="form-text mt-2 small">CSV format: Category,Value</div>
            </div>

            <!-- Data Table Section -->
            <div class="data-editor-section">
              <div
                class="d-flex justify-content-between align-items-center mb-2"
              >
                <div class="section-title">Chart Data</div>
                <button
                  class="btn btn-sm btn-outline-primary"
                  id="addBarChartDataRow"
                  onclick="addNewBarChartDataRow()"
                >
                  <i class="las la-plus"></i> Add Row
                </button>
              </div>
              <div class="table-responsive">
                <table class="table table-sm" id="barChartDataTable">
                  <thead>
                    <tr>
                      <th style="width: 40%">Category</th>
                      <th style="width: 45%">Value</th>
                      <th style="width: 15%">Actions</th>
                    </tr>
                  </thead>
                  <tbody id="barChartDataBody">
                    <!-- Data rows will be added dynamically -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Apply Button -->
          <button
            class="btn btn-primary w-100"
            onclick="applyBarChartSettings()"
          >
            Apply Changes
          </button>
        </div>
      </div>

      <!-- Pie Chart Settings -->
      <div class="offcanvas" id="pieChartSettings" tabindex="-1">
        <div class="offcanvas-header">
          <h5>Pie Chart Settings</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="offcanvas"
          ></button>
        </div>
        <div class="offcanvas-body">
          <!-- Chart Appearance -->
          <h6 class="mb-3">Chart Appearance</h6>

          <!-- Chart Title -->
          <div class="mb-3">
            <label class="form-label">Chart Title</label>
            <input
              type="text"
              class="form-control form-control-sm"
              id="pieChartSettings-chartTitle"
              value="Distribution"
            />
          </div>

          <!-- Chart Type -->
          <div class="mb-3">
            <label class="form-label">Chart Type</label>
            <select class="form-select" id="pieChartSettings-type">
              <option value="pie">Pie</option>
              <option value="donut">Donut</option>
            </select>
          </div>

          <!-- Inner Radius (for donut) -->
          <div class="mb-3">
            <label class="form-label">Inner Radius (%)</label>
            <input
              type="range"
              class="form-range"
              min="0"
              max="80"
              value="40"
              id="pieChartSettings-innerRadius"
            />
          </div>

          <!-- Radius -->
          <div class="mb-3">
            <label class="form-label">Chart Radius (%)</label>
            <input
              type="range"
              class="form-range"
              min="50"
              max="100"
              value="90"
              id="pieChartSettings-radius"
            />
          </div>

          <!-- Labels -->
          <div class="mb-3">
            <div class="form-check form-switch">
              <input
                class="form-check-input"
                type="checkbox"
                id="pieChartSettings-labels"
                checked
              />
              <label class="form-check-label" for="pieChartSettings-labels"
                >Show Labels</label
              >
            </div>
          </div>

          <!-- Legend -->
          <div class="mb-3">
            <div class="form-check form-switch">
              <input
                class="form-check-input"
                type="checkbox"
                id="pieChartSettings-legend"
                checked
              />
              <label class="form-check-label" for="pieChartSettings-legend"
                >Show Legend</label
              >
            </div>
          </div>

          <!-- Data Management -->
          <div class="mb-4">
            <h6 class="mb-3">Data Management</h6>

            <!-- Data Import/Export Section -->
            <div class="data-editor-section">
              <div class="section-title">Import/Export Data</div>
              <div class="row g-2 mb-2">
                <div class="col-8">
                  <input
                    type="file"
                    class="form-control form-control-sm"
                    id="pieChartDataFile"
                    accept=".csv"
                  />
                </div>
                <div class="col-4">
                  <button
                    class="btn btn-sm btn-outline-secondary w-100"
                    id="pieChartDataImport"
                    onclick="importPieChartCSV()"
                  >
                    <i class="las la-upload"></i> Import CSV
                  </button>
                </div>
              </div>
              <div class="row g-2">
                <div class="col-12">
                  <button
                    class="btn btn-sm btn-outline-secondary w-100"
                    id="pieChartDataExport"
                    onclick="exportPieChartCSV()"
                  >
                    <i class="las la-download"></i> Export to CSV
                  </button>
                </div>
              </div>
              <div class="form-text mt-2 small">CSV format: Category,Value</div>
            </div>

            <!-- Data Table Section -->
            <div class="data-editor-section">
              <div
                class="d-flex justify-content-between align-items-center mb-2"
              >
                <div class="section-title">Chart Data</div>
                <button
                  class="btn btn-sm btn-outline-primary"
                  id="addPieChartDataRow"
                  onclick="addNewPieChartDataRow()"
                >
                  <i class="las la-plus"></i> Add Row
                </button>
              </div>
              <div class="table-responsive">
                <table class="table table-sm" id="pieChartDataTable">
                  <thead>
                    <tr>
                      <th style="width: 40%">Category</th>
                      <th style="width: 45%">Value</th>
                      <th style="width: 15%">Actions</th>
                    </tr>
                  </thead>
                  <tbody id="pieChartDataBody">
                    <!-- Data rows will be added dynamically -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Apply Button -->
          <button
            class="btn btn-primary w-100"
            onclick="applyPieChartSettings()"
          >
            Apply Changes
          </button>
        </div>
      </div>

      <!-- Line Chart Settings -->
      <div class="offcanvas" id="lineChartSettings" tabindex="-1">
        <div class="offcanvas-header">
          <h5>Line Chart Settings</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="offcanvas"
          ></button>
        </div>
        <div class="offcanvas-body">
          <!-- Chart Title -->
          <div class="mb-3">
            <label class="form-label">Chart Title</label>
            <input
              type="text"
              class="form-control"
              id="lineChartSettings-chartTitle"
              value="Financial Performance"
            />
          </div>

          <!-- Y-Axis Title -->
          <div class="mb-3">
            <label class="form-label">Y-Axis Title</label>
            <input
              type="text"
              class="form-control"
              id="lineChartSettings-yAxisTitle"
              value="Revenue ($)"
            />
          </div>

          <!-- Legend Toggle -->
          <div class="mb-3">
            <div class="form-check form-switch">
              <input
                class="form-check-input"
                type="checkbox"
                id="lineChartSettings-legend"
                checked
              />
              <label class="form-check-label" for="lineChartSettings-legend"
                >Show Legend</label
              >
            </div>
          </div>

          <hr />

          <!-- Data Management -->
          <h6 class="mb-3">Data Management</h6>

          <!-- Series Selection -->
          <div class="mb-3">
            <label class="form-label">Select Series to Display</label>
            <select class="form-select" id="lineChartSettings-activeSeries">
              <option value="0">Revenue</option>
              <option value="1">Expenses</option>
              <option value="2">Profit</option>
            </select>
          </div>

          <!-- Data Editor -->
          <div class="mb-4">
            <h6 class="mb-3">Data Management</h6>

            <!-- Data Import/Export Section -->
            <div class="data-editor-section">
              <div class="section-title">Import/Export Data</div>
              <div class="row g-2 mb-2">
                <div class="col-8">
                  <input
                    type="file"
                    class="form-control form-control-sm"
                    id="lineChartDataFile"
                    accept=".csv"
                  />
                </div>
                <div class="col-4">
                  <button
                    class="btn btn-sm btn-outline-secondary w-100"
                    id="lineChartDataImport"
                    onclick="importLineChartCSV()"
                  >
                    <i class="las la-upload"></i> Import CSV
                  </button>
                </div>
              </div>
              <div class="row g-2">
                <div class="col-12">
                  <button
                    class="btn btn-sm btn-outline-secondary w-100"
                    id="lineChartDataExport"
                    onclick="exportLineChartCSV()"
                  >
                    <i class="las la-download"></i> Export to CSV
                  </button>
                </div>
              </div>
              <div class="form-text mt-2 small">
                CSV format: Date,Revenue,Expenses,Profit
              </div>
            </div>

            <!-- Data Table Section -->
            <div class="data-editor-section">
              <div
                class="d-flex justify-content-between align-items-center mb-2"
              >
                <div class="section-title">Chart Data</div>
                <button
                  class="btn btn-sm btn-outline-primary"
                  id="addLineChartDataRow"
                  onclick="addNewLineChartDataRow()"
                >
                  <i class="las la-plus"></i> Add Row
                </button>
              </div>
              <div class="table-responsive">
                <table class="table table-sm" id="lineChartDataTable">
                  <thead>
                    <tr>
                      <th style="width: 25%">Date</th>
                      <th style="width: 20%">Revenue</th>
                      <th style="width: 20%">Expenses</th>
                      <th style="width: 20%">Profit</th>
                      <th style="width: 15%">Actions</th>
                    </tr>
                  </thead>
                  <tbody id="lineChartDataBody">
                    <!-- Data rows will be added dynamically -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Apply Button -->
          <button
            class="btn btn-primary w-100"
            onclick="applyLineChartSettings()"
          >
            Apply Changes
          </button>
        </div>
      </div>

      <!-- PDF Viewer Settings -->
      <div class="offcanvas" id="pdfViewerSettings" tabindex="-1">
        <div class="offcanvas-header">
          <h5>PDF Viewer Settings</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="offcanvas"
          ></button>
        </div>
        <div class="offcanvas-body">
          <!-- PDF viewer settings content -->
        </div>
      </div>

      <!-- Word Cloud Settings -->
      <div class="offcanvas" id="wordCloudSettings" tabindex="-1">
        <div class="offcanvas-header">
          <h5>Word Cloud Settings</h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="offcanvas"
          ></button>
        </div>
        <div class="offcanvas-body">
          <!-- Chart Appearance -->
          <h6 class="mb-3">Chart Appearance</h6>

          <!-- Chart Title -->
          <div class="mb-3">
            <label class="form-label">Chart Title</label>
            <input
              type="text"
              class="form-control form-control-sm"
              id="wordCloudSettings-chartTitle"
              value="Word Cloud"
            />
          </div>

          <!-- Min Font Size -->
          <div class="mb-3">
            <label class="form-label">Min Font Size (%)</label>
            <input
              type="range"
              class="form-range"
              min="1"
              max="10"
              value="2"
              id="wordCloudSettings-minFontSize"
            />
          </div>

          <!-- Max Font Size -->
          <div class="mb-3">
            <label class="form-label">Max Font Size (%)</label>
            <input
              type="range"
              class="form-range"
              min="10"
              max="50"
              value="20"
              id="wordCloudSettings-maxFontSize"
            />
          </div>

          <!-- Randomness -->
          <div class="mb-3">
            <label class="form-label">Randomness</label>
            <input
              type="range"
              class="form-range"
              min="0"
              max="1"
              step="0.1"
              value="0.2"
              id="wordCloudSettings-randomness"
            />
          </div>

          <!-- Word Rotation -->
          <div class="mb-3">
            <label class="form-label">Word Rotation</label>
            <select class="form-select" id="wordCloudSettings-rotation">
              <option value="horizontal">Horizontal Only</option>
              <option value="mixed" selected>
                Mixed (Horizontal & Vertical)
              </option>
              <option value="vertical">Vertical Only</option>
            </select>
          </div>

          <!-- Data Management -->
          <div class="mb-4">
            <h6 class="mb-3">Data Management</h6>

            <!-- Data Import/Export Section -->
            <div class="data-editor-section">
              <div class="section-title">Import/Export Data</div>
              <div class="row g-2 mb-2">
                <div class="col-8">
                  <input
                    type="file"
                    class="form-control form-control-sm"
                    id="wordCloudDataFile"
                    accept=".csv, .txt"
                  />
                </div>
                <div class="col-4">
                  <button
                    class="btn btn-sm btn-outline-secondary w-100"
                    id="wordCloudDataImport"
                    onclick="importWordCloudData()"
                  >
                    <i class="las la-upload"></i> Import
                  </button>
                </div>
              </div>
              <div class="row g-2">
                <div class="col-12">
                  <button
                    class="btn btn-sm btn-outline-secondary w-100"
                    id="wordCloudDataExport"
                    onclick="exportWordCloudData()"
                  >
                    <i class="las la-download"></i> Export to CSV
                  </button>
                </div>
              </div>
              <div class="form-text mt-2 small">CSV format: Word,Weight</div>
            </div>

            <!-- Text Input Section -->
            <div class="data-editor-section">
              <div class="section-title">Text Input</div>
              <div class="mb-3">
                <textarea
                  class="form-control"
                  id="wordCloudSettings-text"
                  rows="5"
                  placeholder="Enter text to generate word cloud..."
                ></textarea>
              </div>
              <div class="form-text mb-3 small">
                Enter text to analyze, or use the data table below to specify
                exact words and weights.
              </div>
            </div>

            <!-- Data Table Section -->
            <div class="data-editor-section">
              <div
                class="d-flex justify-content-between align-items-center mb-2"
              >
                <div class="section-title">Word Data</div>
                <button
                  class="btn btn-sm btn-outline-primary"
                  id="addWordCloudDataRow"
                  onclick="addNewWordCloudDataRow()"
                >
                  <i class="las la-plus"></i> Add Word
                </button>
              </div>
              <div class="table-responsive">
                <table class="table table-sm" id="wordCloudDataTable">
                  <thead>
                    <tr>
                      <th style="width: 60%">Word</th>
                      <th style="width: 25%">Weight</th>
                      <th style="width: 15%">Actions</th>
                    </tr>
                  </thead>
                  <tbody id="wordCloudDataBody">
                    <!-- Data rows will be added dynamically -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Apply Button -->
          <button
            class="btn btn-primary w-100"
            onclick="applyWordCloudSettings()"
          >
            Apply Changes
          </button>
        </div>
      </div>
    </div>


    <div class="offcanvas" id="stackedColumnChartSettings" tabindex="-1">
      <div class="offcanvas-header">
        <h5>Stacked Column Chart Settings</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="offcanvas"
        ></button>
      </div>
      <div class="offcanvas-body">
        <!-- Chart Appearance -->
        <h6 class="mb-3">Chart Appearance</h6>

        <!-- Chart Title -->
        <div class="mb-3">
          <label class="form-label">Chart Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="stackedColumnChartSettings-chartTitle"
            value="Monthly Sales by Category"
          />
        </div>

        <!-- Y-Axis Title -->
        <div class="mb-3">
          <label class="form-label">Y-Axis Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="stackedColumnChartSettings-yAxisTitle"
            value="Sales ($)"
          />
        </div>

        <!-- X-Axis Title -->
        <div class="mb-3">
          <label class="form-label">X-Axis Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="stackedColumnChartSettings-xAxisTitle"
            value="Month"
          />
        </div>

        <!-- Legend -->
        <div class="mb-3">
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              id="stackedColumnChartSettings-legend"
              checked
            />
            <label
              class="form-check-label"
              for="stackedColumnChartSettings-legend"
              >Show Legend</label
            >
          </div>
        </div>

        <!-- Series Count -->
        <div class="mb-3">
          <label
            for="seriesCount"
            class="form-label d-flex justify-content-between"
          >
            Number of Series
            <span class="value-display">3</span>
          </label>
          <input
            type="range"
            class="form-range"
            id="seriesCount"
            min="1"
            max="10"
            value="3"
            onchange="handleSeriesCountChange(this.value)"
          />
        </div>

        <!-- Series Configuration -->
        <div class="mb-4">
          <h6 class="mb-3">Series Configuration</h6>
          <div id="seriesConfigContainer" class="mb-3">
            <!-- Series configuration items will be added dynamically -->
          </div>
        </div>

        <!-- Series List -->
        <div class="mb-4">
          <h6 class="mb-2">Series</h6>
          <div id="stackedColumnChartSettings-seriesList" class="series-list">
            <!-- Series items will be added dynamically -->
          </div>
        </div>

        <!-- Data Management -->
        <div class="mb-4">
          <h6 class="mb-3">Data Management</h6>

          <!-- Data Import/Export Section -->
          <div class="data-editor-section">
            <div class="section-title">Import/Export Data</div>
            <div class="row g-2 mb-2">
              <div class="col-8">
                <input
                  type="file"
                  class="form-control form-control-sm"
                  id="stackedColumnChartDataFile"
                  accept=".csv, .json"
                />
              </div>
              <div class="col-4">
                <button
                  class="btn btn-sm btn-outline-secondary w-100"
                  id="stackedColumnChartDataImport"
                  onclick="importStackedColumnChartData(document.getElementById('stackedColumnChartSettings').dataset.currentChart, document.getElementById('stackedColumnChartDataFile').files[0])"
                >
                  <i class="las la-upload"></i> Import
                </button>
              </div>
            </div>
            <div class="row g-2">
              <div class="col-6">
                <button
                  class="btn btn-sm btn-outline-secondary w-100"
                  id="stackedColumnChartDataExportCSV"
                  onclick="exportStackedColumnChartData(document.getElementById('stackedColumnChartSettings').dataset.currentChart, 'csv')"
                >
                  <i class="las la-download"></i> Export CSV
                </button>
              </div>
              <div class="col-6">
                <button
                  class="btn btn-sm btn-outline-secondary w-100"
                  id="stackedColumnChartDataExportJSON"
                  onclick="exportStackedColumnChartData(document.getElementById('stackedColumnChartSettings').dataset.currentChart, 'json')"
                >
                  <i class="las la-download"></i> Export JSON
                </button>
              </div>
            </div>
          </div>

          <!-- Data Table Section -->
          <div class="data-editor-section mt-3">
            <div class="section-title mb-2">Chart Data</div>
            <div id="dataTable" class="handsontable-container"></div>
          </div>
        </div>

        <!-- Apply Button -->
        <button
          class="btn btn-primary w-100"
          onclick="applyStackedColumnChartSettings()"
        >
          Apply Changes
        </button>
      </div>
    </div>


    <div class="offcanvas" id="percentStackedColumnChartSettings" tabindex="-1">
      <div class="offcanvas-header">
        <h5>100% Stacked Column Chart Settings</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="offcanvas"
        ></button>
      </div>
      <div class="offcanvas-body">
        <!-- Chart Appearance -->
        <h6 class="mb-3">Chart Appearance</h6>

        <!-- Chart Title -->
        <div class="mb-3">
          <label class="form-label">Chart Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="percentStackedColumnChartSettings-chartTitle"
            value="Sales Distribution by Category"
          />
        </div>

        <!-- Y-Axis Title -->
        <div class="mb-3">
          <label class="form-label">Y-Axis Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="percentStackedColumnChartSettings-yAxisTitle"
            value="Percentage (%)"
          />
        </div>

        <!-- X-Axis Title -->
        <div class="mb-3">
          <label class="form-label">X-Axis Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="percentStackedColumnChartSettings-xAxisTitle"
            value="Month"
          />
        </div>

        <!-- Legend -->
        <div class="mb-3">
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              id="percentStackedColumnChartSettings-legend"
              checked
            />
            <label
              class="form-check-label"
              for="percentStackedColumnChartSettings-legend"
              >Show Legend</label
            >
          </div>
        </div>

        <!-- Series Count -->
        <div class="mb-3">
          <label
            for="percentSeriesCount"
            class="form-label d-flex justify-content-between"
          >
            Number of Series
            <span class="value-display">3</span>
          </label>
          <input
            type="range"
            class="form-range"
            id="percentSeriesCount"
            min="1"
            max="10"
            value="3"
            onchange="handlePercentSeriesCountChange(this.value)"
          />
        </div>

        <!-- Series Configuration -->
        <div class="mb-4">
          <h6 class="mb-3">Series Configuration</h6>
          <div id="percentSeriesConfigContainer" class="mb-3">
            <!-- Series configuration items will be added dynamically -->
          </div>
        </div>

        <!-- Series List -->
        <div class="mb-4">
          <h6 class="mb-2">Series</h6>
          <div
            id="percentStackedColumnChartSettings-seriesList"
            class="series-list"
          >
            <!-- Series items will be added dynamically -->
          </div>
        </div>

        <!-- Data Management -->
        <div class="mb-4">
          <h6 class="mb-3">Data Management</h6>

          <!-- Data Import/Export Section -->
          <div class="data-editor-section">
            <div class="section-title">Import/Export Data</div>
            <div class="row g-2 mb-2">
              <div class="col-8">
                <input
                  type="file"
                  class="form-control form-control-sm"
                  id="percentStackedColumnChartDataFile"
                  accept=".csv, .json"
                />
              </div>
              <div class="col-4">
                <button
                  class="btn btn-sm btn-outline-secondary w-100"
                  id="percentStackedColumnChartDataImport"
                  onclick="importPercentStackedColumnChartData(document.getElementById('percentStackedColumnChartSettings').dataset.currentChart, document.getElementById('percentStackedColumnChartDataFile').files[0])"
                >
                  <i class="las la-upload"></i> Import
                </button>
              </div>
            </div>
            <div class="row g-2">
              <div class="col-6">
                <button
                  class="btn btn-sm btn-outline-secondary w-100"
                  id="percentStackedColumnChartDataExportCSV"
                  onclick="exportPercentStackedColumnChartData(document.getElementById('percentStackedColumnChartSettings').dataset.currentChart, 'csv')"
                >
                  <i class="las la-download"></i> Export CSV
                </button>
              </div>
              <div class="col-6">
                <button
                  class="btn btn-sm btn-outline-secondary w-100"
                  id="percentStackedColumnChartDataExportJSON"
                  onclick="exportPercentStackedColumnChartData(document.getElementById('percentStackedColumnChartSettings').dataset.currentChart, 'json')"
                >
                  <i class="las la-download"></i> Export JSON
                </button>
              </div>
            </div>
          </div>

          <!-- Data Table Section -->
          <div class="data-editor-section mt-3">
            <div class="section-title mb-2">Chart Data</div>
            <div id="percentDataTable" class="handsontable-container"></div>
          </div>
        </div>

        <!-- Apply Button -->
        <button
          class="btn btn-primary w-100"
          onclick="applyPercentStackedColumnChartSettings()"
        >
          Apply Changes
        </button>
      </div>
    </div>


    <div
      class="offcanvas offcanvas-end"
      id="percentStackedColumnChartSettings"
      tabindex="-1"
      aria-labelledby="percentStackedColumnChartSettingsLabel"
    >
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="percentStackedColumnChartSettingsLabel">
          100% Stacked Column Chart Settings
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="offcanvas"
          aria-label="Close"
        ></button>
      </div>
      <div class="offcanvas-body">
        <!-- Chart Appearance -->
        <h6 class="mb-3">Chart Appearance</h6>

        <!-- Chart Title -->
        <div class="mb-3">
          <label
            for="percentStackedColumnChartSettings-chartTitle"
            class="form-label"
            >Chart Title</label
          >
          <input
            type="text"
            class="form-control"
            id="percentStackedColumnChartSettings-chartTitle"
            value="Sales Distribution by Category"
          />
        </div>

        <!-- Y-Axis Title -->
        <div class="mb-3">
          <label
            for="percentStackedColumnChartSettings-yAxisTitle"
            class="form-label"
            >Y-Axis Title</label
          >
          <input
            type="text"
            class="form-control"
            id="percentStackedColumnChartSettings-yAxisTitle"
            value="Percentage (%)"
          />
        </div>

        <!-- X-Axis Title -->
        <div class="mb-3">
          <label
            for="percentStackedColumnChartSettings-xAxisTitle"
            class="form-label"
            >X-Axis Title</label
          >
          <input
            type="text"
            class="form-control"
            id="percentStackedColumnChartSettings-xAxisTitle"
            value="Month"
          />
        </div>

        <!-- Legend -->
        <div class="mb-3">
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              id="percentStackedColumnChartSettings-legend"
              checked
            />
            <label
              class="form-check-label"
              for="percentStackedColumnChartSettings-legend"
              >Show Legend</label
            >
          </div>
        </div>

        <!-- Apply Button -->
        <button
          class="btn btn-primary w-100"
          onclick="applyPercentStackedColumnChartSettings()"
        >
          Apply Changes
        </button>
      </div>
    </div>


    <div
      class="offcanvas offcanvas-end"
      id="stockChartSettings"
      tabindex="-1"
      aria-labelledby="stockChartSettingsLabel"
    >
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="stockChartSettingsLabel">
          Stock Chart Settings
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="offcanvas"
          aria-label="Close"
        ></button>
      </div>
      <div class="offcanvas-body">
        <!-- Chart Appearance -->
        <h6 class="mb-3">Chart Appearance</h6>

        <!-- Chart Title -->
        <div class="mb-3">
          <label for="stockChartSettings-chartTitle" class="form-label"
            >Chart Title</label
          >
          <input
            type="text"
            class="form-control"
            id="stockChartSettings-chartTitle"
            value="Stock Price"
          />
        </div>

        <!-- Chart Type -->
        <div class="mb-3">
          <label for="stockChartSettings-chartType" class="form-label"
            >Chart Type</label
          >
          <select class="form-select" id="stockChartSettings-chartType">
            <option value="candlestick">Candlestick</option>
            <option value="ohlc">OHLC</option>
            <option value="line">Line</option>
          </select>
        </div>

        <!-- Show Volume -->
        <div class="mb-3">
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              id="stockChartSettings-showVolume"
              checked
            />
            <label class="form-check-label" for="stockChartSettings-showVolume"
              >Show Volume Panel</label
            >
          </div>
        </div>

        <!-- Apply Button -->
        <button
          class="btn btn-primary w-100"
          onclick="applyStockChartSettings()"
        >
          Apply Changes
        </button>
      </div>
    </div>


    <div
      class="offcanvas offcanvas-end"
      id="areaChartSettings"
      tabindex="-1"
      aria-labelledby="areaChartSettingsLabel"
    >
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="areaChartSettingsLabel">
          Area Chart Settings
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="offcanvas"
          aria-label="Close"
        ></button>
      </div>
      <div class="offcanvas-body">
        <!-- Chart Appearance -->
        <h6 class="mb-3">Chart Appearance</h6>

        <!-- Chart Title -->
        <div class="mb-3">
          <label for="areaChartSettings-chartTitle" class="form-label"
            >Chart Title</label
          >
          <input
            type="text"
            class="form-control"
            id="areaChartSettings-chartTitle"
            value="Website Traffic Over Time"
          />
        </div>

        <!-- Fill Opacity -->
        <div class="mb-3">
          <label
            for="areaChartSettings-fillOpacity"
            class="form-label d-flex justify-content-between"
          >
            Fill Opacity
            <span class="value-display">50%</span>
          </label>
          <input
            type="range"
            class="form-range"
            id="areaChartSettings-fillOpacity"
            min="0"
            max="100"
            value="50"
            onchange="handleFillOpacityChange(this.value)"
          />
        </div>

        <!-- Apply Button -->
        <button
          class="btn btn-primary w-100"
          onclick="applyAreaChartSettings()"
        >
          Apply Changes
        </button>
      </div>
    </div>

    <div class="offcanvas" id="stackChartSettings" tabindex="-1">
      <div class="offcanvas-header">
        <h5>Stack Chart Settings</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="offcanvas"
        ></button>
      </div>
      <div class="offcanvas-body">
        <!-- Chart Appearance -->
        <h6 class="mb-3">Chart Appearance</h6>

        <!-- Chart Title -->
        <div class="mb-3">
          <label class="form-label">Chart Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="stackChartSettings-chartTitle"
            value="Total Cost of Ownership ($, 2011–2018)"
          />
        </div>

        <!-- Y-Axis Title -->
        <div class="mb-3">
          <label class="form-label">Y-Axis Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="stackChartSettings-yAxisTitle"
            value="TCO ($)"
          />
        </div>

        <!-- X-Axis Title -->
        <div class="mb-3">
          <label class="form-label">X-Axis Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="stackChartSettings-xAxisTitle"
            value="Period"
          />
        </div>

        <!-- Legend -->
        <div class="mb-3">
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              id="stackChartSettings-legend"
              checked
            />
            <label class="form-check-label" for="stackChartSettings-legend"
              >Show Legend</label
            >
          </div>
        </div>

        <!-- Data Management -->
        <div class="mb-4">
          <h6 class="mb-3">Data Management</h6>

          <!-- Data Table Section -->
          <div class="data-editor-section">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <div class="section-title">Chart Data</div>
              <button
                class="btn btn-sm btn-outline-primary"
                id="addStackChartDataRow"
                onclick="addNewStackChartDataRow()"
              >
                <i class="las la-plus"></i> Add Row
              </button>
            </div>
            <div class="table-responsive">
              <table class="table table-sm" id="stackChartDataTable">
                <thead>
                  <tr>
                    <th style="width: 20%">Category</th>
                    <th style="width: 15%">Fuel Costs</th>
                    <th style="width: 15%">Maintenance</th>
                    <th style="width: 15%">Insurance</th>
                    <th style="width: 15%">Depreciation</th>
                    <th style="width: 10%">Actions</th>
                  </tr>
                </thead>
                <tbody id="stackChartDataBody">
                  <!-- Data rows will be added dynamically -->
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Apply Button -->
        <button
          class="btn btn-primary w-100"
          onclick="applyStackChartSettings()"
        >
          Apply Changes
        </button>
      </div>
    </div>


    <div class="offcanvas" id="wordMapSettings" tabindex="-1">
      <div class="offcanvas-header">
        <h5>Word Map Settings</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="offcanvas"
        ></button>
      </div>
      <div class="offcanvas-body">
        <!-- Chart Appearance -->
        <h6 class="mb-3">Chart Appearance</h6>

        <!-- Chart Title -->
        <div class="mb-3">
          <label class="form-label">Chart Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="wordMapSettings-chartTitle"
            value="Global Distribution"
          />
        </div>

        <!-- Data Management -->
        <div class="mb-4">
          <h6 class="mb-3">Data Management</h6>

          <!-- Data Table Section -->
          <div class="data-editor-section">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <div class="section-title">Country Data</div>
              <button
                class="btn btn-sm btn-outline-primary"
                id="addWordMapDataRow"
                onclick="addNewWordMapDataRow()"
              >
                <i class="las la-plus"></i> Add Country
              </button>
            </div>
            <div class="table-responsive">
              <table class="table table-sm" id="wordMapDataTable">
                <thead>
                  <tr>
                    <th style="width: 40%">Country Code</th>
                    <th style="width: 40%">Value</th>
                    <th style="width: 20%">Actions</th>
                  </tr>
                </thead>
                <tbody id="wordMapDataBody">
                  <!-- Data rows will be added dynamically -->
                </tbody>
              </table>
            </div>
            <div class="form-text mt-2 small">
              Use ISO country codes (e.g., US, GB, IN, CN)
            </div>
          </div>
        </div>

        <!-- Apply Button -->
        <button class="btn btn-primary w-100" onclick="applyWordMapSettings()">
          Apply Changes
        </button>
      </div>
    </div>
    
    <div class="offcanvas" id="dualAxisSettings" tabindex="-1">
      <div class="offcanvas-header">
        <h5>Dual Axis Chart Settings</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="offcanvas"
        ></button>
      </div>
      <div class="offcanvas-body">
        <!-- Chart Appearance -->
        <h6 class="mb-3">Chart Appearance</h6>

        <!-- Chart Title -->
        <div class="mb-3">
          <label class="form-label">Chart Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="dualAxisSettings-chartTitle"
            value="Global Crude Oil Prices ($/barrel, 2011–2018)"
          />
        </div>

        <!-- Left Y-Axis Title -->
        <div class="mb-3">
          <label class="form-label">Left Y-Axis Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="dualAxisSettings-yAxis1Title"
            value="Brent Price ($/b)"
          />
        </div>

        <!-- Right Y-Axis Title -->
        <div class="mb-3">
          <label class="form-label">Right Y-Axis Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="dualAxisSettings-yAxis2Title"
            value="WTI Price (€/b)"
          />
        </div>

        <!-- X-Axis Title -->
        <div class="mb-3">
          <label class="form-label">X-Axis Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="dualAxisSettings-xAxisTitle"
            value="Period"
          />
        </div>

        <!-- Legend -->
        <div class="mb-3">
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              id="dualAxisSettings-legend"
              checked
            />
            <label class="form-check-label" for="dualAxisSettings-legend"
              >Show Legend</label
            >
          </div>
        </div>

        <!-- Data Management -->
        <div class="mb-4">
          <h6 class="mb-3">Left Axis Data (Brent)</h6>

          <!-- Left Axis Data Table Section -->
          <div class="data-editor-section">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <div class="section-title">Brent Data</div>
              <button
                class="btn btn-sm btn-outline-primary"
                id="addDualAxisLeftDataRow"
                onclick="addNewDualAxisLeftDataRow()"
              >
                <i class="las la-plus"></i> Add Row
              </button>
            </div>
            <div class="table-responsive">
              <table class="table table-sm" id="dualAxisLeftDataTable">
                <thead>
                  <tr>
                    <th style="width: 40%">Category</th>
                    <th style="width: 40%">Value</th>
                    <th style="width: 20%">Actions</th>
                  </tr>
                </thead>
                <tbody id="dualAxisLeftDataBody">
                  <!-- Data rows will be added dynamically -->
                </tbody>
              </table>
            </div>
          </div>

          <h6 class="mb-3 mt-4">Right Axis Data (WTI)</h6>

          <!-- Right Axis Data Table Section -->
          <div class="data-editor-section">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <div class="section-title">WTI Data</div>
              <button
                class="btn btn-sm btn-outline-primary"
                id="addDualAxisRightDataRow"
                onclick="addNewDualAxisRightDataRow()"
              >
                <i class="las la-plus"></i> Add Row
              </button>
            </div>
            <div class="table-responsive">
              <table class="table table-sm" id="dualAxisRightDataTable">
                <thead>
                  <tr>
                    <th style="width: 40%">Category</th>
                    <th style="width: 40%">Value</th>
                    <th style="width: 20%">Actions</th>
                  </tr>
                </thead>
                <tbody id="dualAxisRightDataBody">
                  <!-- Data rows will be added dynamically -->
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Apply Button -->
        <button class="btn btn-primary w-100" onclick="applyDualAxisSettings()">
          Apply Changes
        </button>
      </div>
    </div>


    <div class="offcanvas" id="lineThresholdSettings" tabindex="-1">
      <div class="offcanvas-header">
        <h5>Line Chart with Threshold Settings</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="offcanvas"
        ></button>
      </div>
      <div class="offcanvas-body">
        <!-- Chart Appearance -->
        <h6 class="mb-3">Chart Appearance</h6>

        <!-- Chart Title -->
        <div class="mb-3">
          <label class="form-label">Chart Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="lineThresholdSettings-chartTitle"
            value="Global Crude Oil Prices ($/barrel, 2011–2018)"
          />
        </div>

        <!-- Y-Axis Title -->
        <div class="mb-3">
          <label class="form-label">Y-Axis Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="lineThresholdSettings-yAxisTitle"
            value="Price ($/b)"
          />
        </div>

        <!-- X-Axis Title -->
        <div class="mb-3">
          <label class="form-label">X-Axis Title</label>
          <input
            type="text"
            class="form-control form-control-sm"
            id="lineThresholdSettings-xAxisTitle"
            value="Period"
          />
        </div>

        <!-- Threshold Values -->
        <div class="mb-3">
          <label class="form-label">Green Threshold</label>
          <input
            type="number"
            class="form-control form-control-sm"
            id="lineThresholdSettings-greenThreshold"
            value="3"
            step="0.1"
          />
        </div>

        <div class="mb-3">
          <label class="form-label">Red Threshold</label>
          <input
            type="number"
            class="form-control form-control-sm"
            id="lineThresholdSettings-redThreshold"
            value="3.5"
            step="0.1"
          />
        </div>

        <!-- Legend -->
        <div class="mb-3">
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              id="lineThresholdSettings-legend"
              checked
            />
            <label class="form-check-label" for="lineThresholdSettings-legend"
              >Show Legend</label
            >
          </div>
        </div>

        <!-- Data Management -->
        <div class="mb-4">
          <h6 class="mb-3">Data Management</h6>

          <!-- Data Table Section -->
          <div class="data-editor-section">
            <div class="d-flex justify-content-between align-items-center mb-2">
              <div class="section-title">Chart Data</div>
              <button
                class="btn btn-sm btn-outline-primary"
                id="addLineThresholdDataRow"
                onclick="addNewLineThresholdDataRow()"
              >
                <i class="las la-plus"></i> Add Row
              </button>
            </div>
            <div class="table-responsive">
              <table class="table table-sm" id="lineThresholdDataTable">
                <thead>
                  <tr>
                    <th style="width: 30%">Category</th>
                    <th style="width: 25%">US</th>
                    <th style="width: 25%">UK</th>
                    <th style="width: 20%">Actions</th>
                  </tr>
                </thead>
                <tbody id="lineThresholdDataBody">
                  <!-- Data rows will be added dynamically -->
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Apply Button -->
        <button
          class="btn btn-primary w-100"
          onclick="applyLineThresholdSettings()"
        >
          Apply Changes
        </button>
      </div>
    </div>


    <div
      class="offcanvas offcanvas-end section-gallery-offcanvas"
      id="sectionGalleryOffcanvas"
      tabindex="-1"
    >
      <div class="section-gallery-header">
        <h5 class="section-gallery-title">
          <i class="las la-th-large"></i>
          Pre-configured section templates
        </h5>
        <div class="section-gallery-search">
          <i class="las la-search"></i>
          <input
            type="text"
            placeholder="Search templates..."
            id="templateSearch"
          />
        </div>
      </div>

      <div class="section-gallery-tabs">
        <div class="section-gallery-tab active" data-category="all">
          <i class="las la-border-all"></i>
          All Templates
        </div>
        <div class="section-gallery-tab" data-category="analytics">
          <i class="las la-chart-line"></i>
          Analytics
        </div>
        <div class="section-gallery-tab" data-category="financial">
          <i class="las la-dollar-sign"></i>
          Financial
        </div>
        <div class="section-gallery-tab" data-category="media">
          <i class="las la-photo-video"></i>
          Media
        </div>
      </div>

      <div class="section-gallery-body">
        <div class="section-templates-grid">
          <!-- Analytics Section -->
          <div class="section-template" data-type="analytics">
            <div class="template-badge">Analytics</div>
            <div class="template-header">
              <h5 class="template-title">
                <i class="las la-chart-line"></i>
                Analytics Section
              </h5>
            </div>
            <p class="template-description">
              A comprehensive analytics dashboard with key metrics and
              performance indicators.
            </p>
            <div class="widget-list">
              <div class="widget-item">
                <i class="las la-chart-bar"></i>
                Performance Metrics Widget
              </div>
              <div class="widget-item">
                <i class="las la-chart-pie"></i>
                Distribution Chart Widget
              </div>
              <div class="widget-item">
                <i class="las la-chart-area"></i>
                Trend Analysis Widget
              </div>
            </div>
          </div>

          <!-- Dashboard Section -->
          <div class="section-template" data-type="dashboard">
            <div class="template-badge">Dashboard</div>
            <div class="template-header">
              <h5 class="template-title">
                <i class="las la-tachometer-alt"></i>
                Dashboard Section
              </h5>
            </div>
            <p class="template-description">
              A dynamic dashboard layout combining charts, tables, and KPI cards
              for comprehensive data visualization.
            </p>
            <div class="widget-list">
              <div class="widget-item">
                <i class="las la-chart-line"></i>
                Line Chart Widget
              </div>
              <div class="widget-item">
                <i class="las la-table"></i>
                Data Table Widget
              </div>
              <div class="widget-item">
                <i class="las la-square"></i>
                KPI Card Widget
              </div>
            </div>
          </div>

          <!-- Comparison Section -->
          <div class="section-template" data-type="comparison">
            <div class="template-badge">Comparison</div>
            <div class="template-header">
              <h5 class="template-title">
                <i class="las la-balance-scale"></i>
                Comparison Section
              </h5>
            </div>
            <p class="template-description">
              Compare data side by side using various visualization methods and
              metrics.
            </p>
            <div class="widget-list">
              <div class="widget-item">
                <i class="las la-chart-bar"></i>
                Bar Chart Widget
              </div>
              <div class="widget-item">
                <i class="las la-percentage"></i>
                Percentage Widget
              </div>
              <div class="widget-item">
                <i class="las la-arrows-alt-h"></i>
                Comparison Table Widget
              </div>
            </div>
          </div>

          <!-- Summary Section -->
          <div class="section-template" data-type="summary">
            <div class="template-badge">Summary</div>
            <div class="template-header">
              <h5 class="template-title">
                <i class="las la-list-alt"></i>
                Summary Section
              </h5>
            </div>
            <p class="template-description">
              Present key findings and insights with a combination of text,
              charts, and metrics.
            </p>
            <div class="widget-list">
              <div class="widget-item">
                <i class="las la-file-alt"></i>
                Text Summary Widget
              </div>
              <div class="widget-item">
                <i class="las la-chart-pie"></i>
                Distribution Widget
              </div>
              <div class="widget-item">
                <i class="las la-list"></i>
                Key Points Widget
              </div>
            </div>
          </div>

          <!-- Trend Analysis Section -->
          <div class="section-template" data-type="trend">
            <div class="template-badge">Trend Analysis</div>
            <div class="template-header">
              <h5 class="template-title">
                <i class="las la-chart-line"></i>
                Trend Analysis Section
              </h5>
            </div>
            <p class="template-description">
              Analyze and visualize trends over time with multiple chart types
              and metrics.
            </p>
            <div class="widget-list">
              <div class="widget-item">
                <i class="las la-chart-line"></i>
                Time Series Widget
              </div>
              <div class="widget-item">
                <i class="las la-chart-area"></i>
                Area Chart Widget
              </div>
              <div class="widget-item">
                <i class="las la-table"></i>
                Trend Table Widget
              </div>
            </div>
          </div>

          <!-- Financial Section -->
          <div class="section-template" data-type="financial">
            <div class="template-badge">Financial</div>
            <div class="template-header">
              <h5 class="template-title">
                <i class="las la-dollar-sign"></i>
                Financial Section
              </h5>
            </div>
            <p class="template-description">
              Track financial metrics and monitor business performance.
            </p>
            <div class="widget-list">
              <div class="widget-item">
                <i class="las la-money-bill-wave"></i>
                Revenue Tracker Widget
              </div>
              <div class="widget-item">
                <i class="las la-file-invoice-dollar"></i>
                Expense Monitor Widget
              </div>
              <div class="widget-item">
                <i class="las la-balance-scale"></i>
                Budget Analysis Widget
              </div>
            </div>
          </div>

          <!-- Media Section -->
          <div class="section-template" data-type="media">
            <div class="template-badge">Media</div>
            <div class="template-header">
              <h5 class="template-title">
                <i class="las la-photo-video"></i>
                Media Section
              </h5>
            </div>
            <p class="template-description">
              Organize and display media content effectively.
            </p>
            <div class="widget-list">
              <div class="widget-item">
                <i class="las la-images"></i>
                Gallery Grid Widget
              </div>
              <div class="widget-item">
                <i class="las la-play-circle"></i>
                Video Player Widget
              </div>
              <div class="widget-item">
                <i class="las la-file-audio"></i>
                Audio Player Widget
              </div>
            </div>
          </div>

          <!-- Report Section -->
          <div class="section-template" data-type="report">
            <div class="template-badge">Report</div>
            <div class="template-header">
              <h5 class="template-title">
                <i class="las la-file-alt"></i>
                Report Section
              </h5>
            </div>
            <p class="template-description">
              Generate and display detailed reports and summaries.
            </p>
            <div class="widget-list">
              <div class="widget-item">
                <i class="las la-table"></i>
                Data Table Widget
              </div>
              <div class="widget-item">
                <i class="las la-file-pdf"></i>
                PDF Report Widget
              </div>
              <div class="widget-item">
                <i class="las la-print"></i>
                Print Layout Widget
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


    <div
      class="offcanvas offcanvas-end"
      tabindex="-1"
      id="spreadsheetSettings"
      aria-labelledby="spreadsheetSettingsLabel"
    >
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="spreadsheetSettingsLabel">
          Spreadsheet Settings
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="offcanvas"
          aria-label="Close"
        ></button>
      </div>
      <div class="offcanvas-body">
        <div class="mb-3">
          <label class="form-label">Table Title</label>
          <input
            type="text"
            class="form-control"
            id="spreadsheetTitle"
            placeholder="Enter table title"
          />
        </div>
        <div class="mb-3">
          <label class="form-label">Show Row Numbers</label>
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              id="showRowNumbers"
              checked
            />
          </div>
        </div>
        <div class="mb-3">
          <label class="form-label">Show Column Headers</label>
          <div class="form-check form-switch">
            <input
              class="form-check-input"
              type="checkbox"
              id="showColumnHeaders"
              checked
            />
          </div>
        </div>
        <button class="btn btn-primary" onclick="applySpreadsheetSettings()">
          Apply Settings
        </button>
      </div>
    </div>
    
    
    `);
