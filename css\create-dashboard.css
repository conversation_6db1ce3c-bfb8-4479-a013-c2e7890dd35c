:root {
  --primary-color: #00a6a6;
  --primary-dark: #008080;
  --primary-light: #e5f5f5;
  --primary-lighter: #f0fafa;
  --secondary-color: #2c3e50;
  --accent-color: #3498db;
  --success-color: #2ecc71;
  --warning-color: #f1c40f;
  --danger-color: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-tertiary: #bdc3c7;
  --background-primary: #ffffff;
  --background-secondary: #f8fafa;
  --border-color: #e5e9f0;
  --shadow-sm: 0 1px 2px rgba(0, 166, 166, 0.05);
  --shadow-md: 0 2px 4px rgba(0, 166, 166, 0.07);
  --shadow-lg: 0 4px 6px rgba(0, 166, 166, 0.1);
  --transition: all 0.2s ease;
  --font-size-base: 12px;
  --font-size-sm: 11px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Montserrat", sans-serif;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  font-size: var(--font-size-base);
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-secondary);
}

/* Header Styles */
.app-header {
  background-color: var(--background-primary);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--shadow-sm);
  border-bottom: 3px solid #007365;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-logo {
  height: 32px;
  width: auto;
}

.divider {
  width: 1px;
  height: 24px;
  background-color: var(--border-color);
}

.header-left h1 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.notifications-btn {
  position: relative;
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  transition: var(--transition);
}

.notifications-btn:hover {
  color: var(--text-primary);
  background-color: var(--background-secondary);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--danger-color);
  color: white;
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 1rem;
}

.user-menu {
  position: relative;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  transition: var(--transition);
}

.user-button:hover {
  background-color: var(--background-secondary);
}

.user-avatar {
  width: 36px;
  height: 36px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

.user-role {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.hexa-logo img {
  height: 32px;
  width: auto;
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-content h2 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.subtitle {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--background-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  font-size: var(--font-size-base);
}

.back-btn:hover {
  background-color: var(--background-secondary);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

/* Main Content Styles */
.main-content {
  flex: 1;
  padding: 3rem 2rem;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(
    180deg,
    var(--background-secondary) 0%,
    rgba(248, 250, 250, 0.5) 100%
  );
}

/* Creation Options */
.creation-options {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.options-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  margin-bottom: 1rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.options-subtitle {
  text-align: center;
  color: var(--text-secondary);
  font-size: 1.125rem;
  margin-bottom: 3rem;
}

.options-container {
  display: flex;
  gap: 2rem;
  justify-content: center;
  margin-top: 2rem;
}

/* Creation Option Cards */
.creation-option {
  flex: 1;
  max-width: 400px;
  background: var(--background-primary);
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  border: 1px solid var(--border-color);
}

.creation-option:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.creation-option::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 16px;
  padding: 2px;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.creation-option:hover::before {
  opacity: 1;
}

.option-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1.5rem;
  background: linear-gradient(
    135deg,
    var(--primary-lighter),
    var(--primary-light)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.creation-option:hover .option-icon {
  transform: scale(1.1);
}

.option-icon i {
  font-size: 2rem;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.creation-option h3 {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-weight: 600;
}

.creation-option p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  font-size: 1rem;
  line-height: 1.6;
}

.option-features {
  list-style: none;
  text-align: left;
  margin-bottom: 2rem;
}

.option-features li {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
}

.option-features li i {
  color: var(--primary-color);
  font-size: 1rem;
}

.option-btn {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  font-size: 1rem;
  position: relative;
  overflow: hidden;
}

.option-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s;
}

.option-btn:hover::before {
  left: 100%;
}

/* Template Gallery */
.template-gallery {
  margin-top: 2rem;
}

.gallery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.gallery-header h3 {
  font-size: 1.75rem;
  color: var(--text-primary);
  font-weight: 600;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.2s ease;
}

.back-btn:hover {
  color: var(--primary-color);
}

.template-filters {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.filter-tags {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.filter-tag {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 20px;
  background: var(--background-primary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.filter-tag:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.filter-tag.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.template-card {
  background: var(--background-primary);
  border-radius: 0px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.template-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.template-preview {
  position: relative;
  padding-top: 66.67%; /* 3:2 Aspect Ratio */
  background: var(--primary-lighter);
  overflow: hidden;
}

.template-preview img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.template-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  opacity: 0;
  transition: all 0.3s ease;
}

.template-card:hover .template-overlay {
  opacity: 1;
}

.preview-btn,
.use-template-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preview-btn {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.use-template-btn {
  background: var(--primary-color);
  color: white;
  border: none;
}

.preview-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.use-template-btn:hover {
  background: var(--primary-dark);
}

.template-info {
  padding: 1.5rem;
}

.template-info h4 {
  font-size: 1.125rem;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.template-info p {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.template-tags {
  display: flex;
  gap: 0.5rem;
}

.template-tags .tag {
  font-size: 0.8rem;
  padding: 0.25rem 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .options-container {
    flex-direction: column;
    align-items: center;
  }

  .creation-option {
    width: 100%;
  }

  .template-filters {
    flex-direction: column;
  }

  .gallery-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .options-title {
    font-size: 2rem;
  }

  .options-subtitle {
    font-size: 1rem;
  }

  .template-overlay {
    flex-direction: column;
  }
}

/* Override any dark background colors */
.app-container {
  background-color: var(--background-secondary);
}

.main-content {
  background: linear-gradient(
    180deg,
    var(--background-secondary) 0%,
    rgba(248, 250, 250, 0.5) 100%
  );
}

.option-card,
.template-card {
  background: var(--background-primary);
}

.option-divider span {
  background: var(--background-secondary);
}
