/* Word Cloud Widget Styles */
.word-cloud-widget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  width: 100%;
  background-color: #fff;
  border-radius: 0px;
}

/* Settings Panel Styles */
.chart-settings .form-range::-webkit-slider-thumb {
  background: var(--ocean-teal);
  width: 14px;
  height: 14px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-settings .form-range::-moz-range-thumb {
  background: var(--ocean-teal);
  width: 14px;
  height: 14px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-settings .form-check-input {
  width: 2rem;
  height: 1rem;
  border-radius: 0.5rem;
}

.chart-settings .form-check-input:checked {
  background-color: var(--ocean-teal);
  border-color: var(--ocean-teal);
}

.chart-settings .form-check-label {
  padding-left: 0.5rem;
  user-select: none;
  font-size: 12px;
}

/* Dark theme adjustments */
.dark-theme .word-cloud-widget .chart-container {
  background-color: #1a1a1a;
}

.dark-theme .chart-settings .form-range::-webkit-slider-thumb {
  background: var(--ocean-teal);
}

.dark-theme .chart-settings .form-range::-moz-range-thumb {
  background: var(--ocean-teal);
}

.dark-theme .chart-settings .form-check-input:checked {
  background-color: var(--ocean-teal);
  border-color: var(--ocean-teal);
}
