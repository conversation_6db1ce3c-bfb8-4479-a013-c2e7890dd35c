/* Dropdown Filter Widget Styles */
.dropdown-filter-widget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 0px;
  overflow: hidden;
  position: relative;
}

/* Widget Header */
.dropdown-filter-widget .widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 16px;
  background: white;
  border-bottom: 1px solid #e5e9f0;
  margin: -8px -8px 8px -8px;
}

.dropdown-filter-widget .widget-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.dropdown-filter-widget .widget-header i {
  font-size: 16px;
  color: #007365;
}

/* Filter Container */
.dropdown-filter-widget .filter-container {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
  border: 1px solid #e9ecef;
}

.dropdown-filter-widget .filter-title {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
}

.dropdown-filter-widget .filter-controls {
  margin-top: 8px;
}

.dropdown-filter-widget .form-select {
  border: 1px solid #ced4da;
  padding: 6px 12px;
  font-size: 13px;
  border-radius: 4px;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.dropdown-filter-widget .form-select:focus {
  border-color: #007365;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 115, 101, 0.25);
}

/* Filtered Content Container */
.dropdown-filter-widget .filtered-content-container {
  flex: 1;
  overflow: auto;
  background-color: #fff;
  border-radius: 4px;
  padding: 12px;
  min-height: 100px;
}

.dropdown-filter-widget .alert {
  font-size: 13px;
  border-radius: 4px;
}

.dropdown-filter-widget .alert-info {
  background-color: #e3f2fd;
  border-color: #c6e2ff;
  color: #0c5460;
}

.dropdown-filter-widget .alert-success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.dropdown-filter-widget .alert ul {
  padding-left: 20px;
}

/* Filter Footer */
.dropdown-filter-widget .filter-footer {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #e9ecef;
}

.dropdown-filter-widget .btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.15s ease-in-out;
}

.dropdown-filter-widget .btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.dropdown-filter-widget .btn-outline-secondary i {
  margin-right: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dropdown-filter-widget .filter-container {
    padding: 10px;
  }
  
  .dropdown-filter-widget .filter-title {
    font-size: 12px;
  }
  
  .dropdown-filter-widget .form-select {
    font-size: 12px;
  }
  
  .dropdown-filter-widget .alert {
    font-size: 12px;
  }
  
  .dropdown-filter-widget .btn-outline-secondary {
    font-size: 11px;
  }
}

/* Dark Theme Support */
.dark-theme .dropdown-filter-widget {
  background-color: #1a1a2d;
  border-color: #2d2d3d;
}

.dark-theme .dropdown-filter-widget .widget-header {
  background: #1e1e2d;
  border-color: #2d2d3d;
}

.dark-theme .dropdown-filter-widget .widget-header > div:first-child {
  color: #a1a5b7;
}

.dark-theme .dropdown-filter-widget .widget-header i {
  color: #009e8b;
}

.dark-theme .dropdown-filter-widget .filter-container {
  background-color: #151521;
  border-color: #2d2d3d;
}

.dark-theme .dropdown-filter-widget .filter-title {
  color: #a1a5b7;
}

.dark-theme .dropdown-filter-widget .form-select {
  background-color: #1e1e2d;
  border-color: #2d2d3d;
  color: #a1a5b7;
}

.dark-theme .dropdown-filter-widget .form-select:focus {
  border-color: #009e8b;
  box-shadow: 0 0 0 0.2rem rgba(0, 158, 139, 0.25);
}

.dark-theme .dropdown-filter-widget .filtered-content-container {
  background-color: #1e1e2d;
  border-color: #2d2d3d;
}

.dark-theme .dropdown-filter-widget .alert-info {
  background-color: #1a2841;
  border-color: #0d3a6a;
  color: #a1a5b7;
}

.dark-theme .dropdown-filter-widget .alert-success {
  background-color: #1a342c;
  border-color: #0f4437;
  color: #a1a5b7;
}

.dark-theme .dropdown-filter-widget .filter-footer {
  border-color: #2d2d3d;
}

.dark-theme .dropdown-filter-widget .btn-outline-secondary {
  color: #a1a5b7;
  border-color: #2d2d3d;
}

.dark-theme .dropdown-filter-widget .btn-outline-secondary:hover {
  color: #1a1a2d;
  background-color: #a1a5b7;
  border-color: #a1a5b7;
} 