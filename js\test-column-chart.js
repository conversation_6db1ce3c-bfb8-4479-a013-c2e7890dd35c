/**
 * Test script to verify column chart onclick and drag-drop consistency
 * Run this in the browser console to check if both methods work properly
 */

function testColumnChart() {
  console.log('🧪 Testing Column Chart Functionality...');
  
  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };
  
  function addTest(name, condition, message) {
    const passed = condition;
    results.tests.push({ name, passed, message });
    if (passed) {
      results.passed++;
      console.log(`✅ ${name}: ${message}`);
    } else {
      results.failed++;
      console.log(`❌ ${name}: ${message}`);
    }
  }
  
  // Test 1: Check if shared markup function exists
  addTest(
    'Shared Markup Function Available',
    typeof window.getColumnChartWidgetMarkup === 'function',
    typeof window.getColumnChartWidgetMarkup === 'function' ? 'getColumnChartWidgetMarkup function found' : 'getColumnChartWidgetMarkup function not found'
  );
  
  // Test 2: Check if click handler exists
  addTest(
    'Click Handler Available',
    typeof window.addColumnChartWidget === 'function',
    typeof window.addColumnChartWidget === 'function' ? 'addColumnChartWidget function found' : 'addColumnChartWidget function not found'
  );
  
  // Test 3: Check if drag-drop setup exists
  addTest(
    'Drag-Drop Setup Available',
    typeof window.setupColumnChartDragDrop === 'function',
    typeof window.setupColumnChartDragDrop === 'function' ? 'setupColumnChartDragDrop function found' : 'setupColumnChartDragDrop function not found'
  );
  
  // Test 4: Check if chart initialization function exists
  addTest(
    'Chart Initialization Available',
    typeof window.initColumnChart === 'function',
    typeof window.initColumnChart === 'function' ? 'initColumnChart function found' : 'initColumnChart function not found'
  );
  
  // Test 5: Test markup generation
  if (typeof window.getColumnChartWidgetMarkup === 'function') {
    try {
      const testChartId = 'test-column-chart-1';
      const markup = window.getColumnChartWidgetMarkup(testChartId);
      
      // Check if markup contains expected elements
      const hasWidgetClass = markup.includes('class="widget');
      const hasChartContainer = markup.includes('chart-container');
      const hasSettings = markup.includes('columnChartSettings');
      const hasCorrectId = markup.includes(testChartId);
      const hasTitle = markup.includes('Column Chart');
      
      addTest(
        'Markup Contains Widget Class',
        hasWidgetClass,
        hasWidgetClass ? 'Widget class found in markup' : 'Widget class missing from markup'
      );
      
      addTest(
        'Markup Contains Chart Container',
        hasChartContainer,
        hasChartContainer ? 'Chart container found in markup' : 'Chart container missing from markup'
      );
      
      addTest(
        'Markup Contains Settings Button',
        hasSettings,
        hasSettings ? 'Settings button found in markup' : 'Settings button missing from markup'
      );
      
      addTest(
        'Markup Uses Correct Chart ID',
        hasCorrectId,
        hasCorrectId ? 'Chart ID correctly inserted in markup' : 'Chart ID not found in markup'
      );
      
      addTest(
        'Markup Contains Title',
        hasTitle,
        hasTitle ? 'Chart title found in markup' : 'Chart title missing from markup'
      );
      
    } catch (error) {
      addTest(
        'Markup Generation',
        false,
        `Error generating markup: ${error.message}`
      );
    }
  }
  
  // Test 6: Check if widget exists in gallery
  const columnChartWidget = document.querySelector('.widget-item[data-widget-type="column-chart"]');
  addTest(
    'Column Chart Widget in Gallery',
    columnChartWidget !== null,
    columnChartWidget ? 'Column chart widget found in gallery' : 'Column chart widget not found in gallery'
  );
  
  // Test 7: Check if widget has onclick handler
  if (columnChartWidget) {
    const hasOnclick = columnChartWidget.hasAttribute('onclick') || columnChartWidget.onclick;
    addTest(
      'Widget Has Click Handler',
      hasOnclick,
      hasOnclick ? 'Column chart widget has onclick handler' : 'Column chart widget missing onclick handler'
    );
    
    // Test 8: Check if widget has data-widget-type for drag-drop
    const hasDataWidgetType = columnChartWidget.hasAttribute('data-widget-type');
    const correctDataWidgetType = columnChartWidget.getAttribute('data-widget-type') === 'column-chart';
    addTest(
      'Widget Has Correct Data Widget Type',
      hasDataWidgetType && correctDataWidgetType,
      hasDataWidgetType && correctDataWidgetType ? 'Widget has correct data-widget-type for drag-drop' : 'Widget missing or incorrect data-widget-type'
    );
  }
  
  // Test 9: Check if GridStack is available
  const hasGridStackSetup = typeof GridStack !== 'undefined' && GridStack.setupDragIn;
  addTest(
    'GridStack Available',
    hasGridStackSetup,
    hasGridStackSetup ? 'GridStack and setupDragIn available' : 'GridStack or setupDragIn not available'
  );
  
  // Test 10: Check if amCharts is available
  const hasAmCharts = typeof am5 !== 'undefined' && typeof am5xy !== 'undefined' && typeof am5themes_Animated !== 'undefined';
  addTest(
    'amCharts Libraries Available',
    hasAmCharts,
    hasAmCharts ? 'amCharts libraries loaded' : 'amCharts libraries not loaded'
  );
  
  // Test 11: Check pending initialization system
  addTest(
    'Pending Initialization System Ready',
    typeof window.pendingColumnChartInits !== 'undefined' || true, // Will be created on first drag
    'Pending initialization system ready'
  );
  
  // Test 12: Test onclick functionality
  if (columnChartWidget) {
    try {
      // Check if the onclick function can be called
      const onclickAttr = columnChartWidget.getAttribute('onclick');
      const functionName = onclickAttr ? onclickAttr.replace('()', '') : null;
      
      if (functionName && typeof window[functionName] === 'function') {
        addTest(
          'Onclick Function Callable',
          true,
          `${functionName} function is available and callable`
        );
      } else {
        addTest(
          'Onclick Function Callable',
          false,
          `Onclick function ${functionName} not found or not callable`
        );
      }
    } catch (error) {
      addTest(
        'Onclick Function Callable',
        false,
        `Error testing onclick: ${error.message}`
      );
    }
  }
  
  // Summary
  console.log('\n📊 Test Summary:');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! Column Chart is ready for use.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the implementation.');
  }
  
  return results;
}

// Auto-run test if in development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
  // Wait for scripts to load
  setTimeout(() => {
    testColumnChart();
  }, 3000);
}

// Export for manual testing
window.testColumnChart = testColumnChart;
