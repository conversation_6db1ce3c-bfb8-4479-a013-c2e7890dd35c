// Dual Axis Widget using amCharts v4
function addDualAxisWidget() {
  console.log("Adding Dual Axis widget");
  const chartId = "dualaxis-" + Date.now();

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 8,
    content: `
      <div class="dual-axis-widget p-2">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>Dual Axis Chart
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#dualAxisSettings"
                    aria-controls="dualAxisSettings"
                    onclick="initDualAxisSettings('${chartId}')">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div id="${chartId}" class="chart-container"></div>
      </div>
    `,
  });

  // Initialize the chart with a slight delay
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing dual axis chart");
      window.initDualAxis(chartId);
    } catch (error) {
      console.error("Error initializing dual axis chart:", error);
    }
  }, 1000);

  return widget;
}

// Initialize a dual axis chart using amCharts v4
window.initDualAxis = async function (containerId) {
  console.log("Starting dual axis chart initialization for container:", containerId);
  const container = document.getElementById(containerId);

  if (!container) {
    console.error("Chart container not found:", containerId);
    return;
  }

  // Make sure the container fills the available space
  container.style.width = "100%";
  container.style.height = "100%";
  container.style.overflow = "hidden";

  console.log(
    "Container dimensions:",
    container.offsetWidth,
    "x",
    container.offsetHeight
  );

  // Create chart instance
  let chart = am4core.create(containerId, am4charts.XYChart);

  // Load data from tsc2.0.json
  const chartData = await getDualAxisData();

  // Default data in case loading fails
  let defaultData = [
    {
      "category": "2011",
      "brent": 121.16,
      "wti": 83.23
    },
    {
      "category": "2012",
      "brent": 111.27,
      "wti": 87.45
    },
    {
      "category": "2013",
      "brent": 112.72,
      "wti": 79.21
    },
    {
      "category": "2014",
      "brent": 110.25,
      "wti": 76.88
    },
    {
      "category": "2015",
      "brent": 58.92,
      "wti": 55.54
    },
    {
      "category": "2016",
      "brent": 37.26,
      "wti": 41.28
    },
    {
      "category": "2017",
      "brent": 55.26,
      "wti": 51.32
    },
    {
      "category": "2018",
      "brent": 69.62,
      "wti": 60.24
    }
  ];

  // If we successfully loaded data from the file, use it
  if (chartData && chartData.data && chartData.data.length > 0) {
    chart.data = chartData.data;
  } else {
    chart.data = defaultData;
  }

  // Create axes
  let categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
  categoryAxis.dataFields.category = "category";
  categoryAxis.title.text = chartData?.xAxisTitle || "Period";
  categoryAxis.renderer.grid.template.location = 0;
  categoryAxis.renderer.minGridDistance = 30;

  // Create first value axis
  let valueAxis1 = chart.yAxes.push(new am4charts.ValueAxis());
  valueAxis1.title.text = chartData?.y1AxisTitle || "Brent Price ($/b)";
  valueAxis1.renderer.line.strokeOpacity = 1;
  valueAxis1.renderer.line.stroke = am4core.color(window.chartConfig.brandColors[0]);
  valueAxis1.renderer.labels.template.fill = am4core.color(window.chartConfig.brandColors[0]);
  valueAxis1.renderer.grid.template.stroke = am4core.color(window.chartConfig.brandColors[0]);
  valueAxis1.renderer.grid.template.strokeOpacity = 0.1;

  // Create second value axis
  let valueAxis2 = chart.yAxes.push(new am4charts.ValueAxis());
  valueAxis2.title.text = chartData?.y2AxisTitle || "WTI Price (€/b)";
  valueAxis2.renderer.opposite = true;
  valueAxis2.renderer.line.strokeOpacity = 1;
  valueAxis2.renderer.line.stroke = am4core.color(window.chartConfig.brandColors[1]);
  valueAxis2.renderer.labels.template.fill = am4core.color(window.chartConfig.brandColors[1]);
  valueAxis2.renderer.grid.template.disabled = true;

  // Create series for first axis
  let series1 = chart.series.push(new am4charts.LineSeries());
  series1.name = "Brent";
  series1.dataFields.valueY = "brent";
  series1.dataFields.categoryX = "category";
  series1.yAxis = valueAxis1;
  series1.stroke = am4core.color(window.chartConfig.brandColors[0]);
  series1.strokeWidth = 3;
  series1.tooltipText = "Brent: [bold]{valueY}[/]";

  // Add bullets to first series
  let bullet1 = series1.bullets.push(new am4charts.CircleBullet());
  bullet1.circle.fill = am4core.color(window.chartConfig.brandColors[0]);
  bullet1.circle.strokeWidth = 2;

  // Create series for second axis
  let series2 = chart.series.push(new am4charts.LineSeries());
  series2.name = "WTI";
  series2.dataFields.valueY = "wti";
  series2.dataFields.categoryX = "category";
  series2.yAxis = valueAxis2;
  series2.stroke = am4core.color(window.chartConfig.brandColors[1]);
  series2.strokeWidth = 3;
  series2.tooltipText = "WTI: [bold]{valueY}[/]";

  // Add bullets to second series
  let bullet2 = series2.bullets.push(new am4charts.CircleBullet());
  bullet2.circle.fill = am4core.color(window.chartConfig.brandColors[1]);
  bullet2.circle.strokeWidth = 2;

  // Add chart title
  let title = chart.titles.create();
  title.text = chartData?.title || "Global Crude Oil Prices ($/barrel, 2011–2018)";
  title.fontSize = 16;
  title.marginBottom = 15;

  // Add legend
  chart.legend = new am4charts.Legend();
  chart.legend.position = "bottom";
  chart.legend.contentAlign = "center";

  // Add cursor
  chart.cursor = new am4charts.XYCursor();
  chart.cursor.behavior = "zoomY";

  // Store chart instance in the container for later access
  container.chart = chart;

  // Add resize event listener to handle container resizing
  const resizeObserver = new ResizeObserver(() => {
    chart.invalidateSize();
  });

  resizeObserver.observe(container);

  // Store the observer in the container for cleanup
  container.resizeObserver = resizeObserver;
};

// Initialize dual axis settings panel
window.initDualAxisSettings = function (chartId) {
  console.log("Initializing dual axis settings for:", chartId);

  // Store the current chart ID for the settings panel
  document.getElementById("dualAxisSettings").dataset.chartId = chartId;

  const container = document.getElementById(chartId);
  if (!container || !container.chart) {
    console.error("Chart not found or not initialized");
    return;
  }

  const chart = container.chart;

  // Load chart title
  const chartTitle = chart.titles.getIndex(0);
  if (chartTitle) {
    document.getElementById("dualAxisSettings-chartTitle").value = chartTitle.text;
  }

  // Load Y-axis titles
  const yAxis1 = chart.yAxes.getIndex(0);
  if (yAxis1) {
    document.getElementById("dualAxisSettings-yAxis1Title").value = yAxis1.title.text;
  }

  const yAxis2 = chart.yAxes.getIndex(1);
  if (yAxis2) {
    document.getElementById("dualAxisSettings-yAxis2Title").value = yAxis2.title.text;
  }

  // Load X-axis title
  const xAxis = chart.xAxes.getIndex(0);
  if (xAxis) {
    document.getElementById("dualAxisSettings-xAxisTitle").value = xAxis.title.text;
  }

  // Load legend visibility
  document.getElementById("dualAxisSettings-legend").checked = chart.legend !== undefined;

  // Load data into the tables
  const leftAxisDataBody = document.getElementById("dualAxisLeftDataBody");
  leftAxisDataBody.innerHTML = ""; // Clear existing rows

  const rightAxisDataBody = document.getElementById("dualAxisRightDataBody");
  rightAxisDataBody.innerHTML = ""; // Clear existing rows

  chart.data.forEach((item) => {
    addDualAxisDataRow(leftAxisDataBody, item.category, item.brent, "left");
    addDualAxisDataRow(rightAxisDataBody, item.category, item.wti, "right");
  });
};

// Apply dual axis settings
window.applyDualAxisSettings = function () {
  const settingsPanel = document.getElementById("dualAxisSettings");
  const chartId = settingsPanel.dataset.chartId;

  const container = document.getElementById(chartId);
  if (!container || !container.chart) {
    console.error("Chart not found or not initialized");
    return;
  }

  const chart = container.chart;

  // Update chart title
  const chartTitle = chart.titles.getIndex(0);
  if (chartTitle) {
    chartTitle.text = document.getElementById("dualAxisSettings-chartTitle").value;
  }

  // Update Y-axis titles
  const yAxis1 = chart.yAxes.getIndex(0);
  if (yAxis1) {
    yAxis1.title.text = document.getElementById("dualAxisSettings-yAxis1Title").value;
  }

  const yAxis2 = chart.yAxes.getIndex(1);
  if (yAxis2) {
    yAxis2.title.text = document.getElementById("dualAxisSettings-yAxis2Title").value;
  }

  // Update X-axis title
  const xAxis = chart.xAxes.getIndex(0);
  if (xAxis) {
    xAxis.title.text = document.getElementById("dualAxisSettings-xAxisTitle").value;
  }

  // Update legend visibility
  const showLegend = document.getElementById("dualAxisSettings-legend").checked;
  if (showLegend && !chart.legend) {
    chart.legend = new am4charts.Legend();
    chart.legend.position = "bottom";
    chart.legend.contentAlign = "center";
  } else if (!showLegend && chart.legend) {
    chart.legend.dispose();
    chart.legend = undefined;
  }

  // Collect data from the tables
  const leftAxisDataRows = document.querySelectorAll("#dualAxisLeftDataBody tr");
  const rightAxisDataRows = document.querySelectorAll("#dualAxisRightDataBody tr");

  // Create a map to merge data from both tables
  const dataMap = new Map();

  // Process left axis data
  leftAxisDataRows.forEach(row => {
    const categoryInput = row.querySelector(".category-input");
    const valueInput = row.querySelector(".value-input");

    if (categoryInput && categoryInput.value.trim()) {
      const category = categoryInput.value.trim();
      const value = parseFloat(valueInput.value) || 0;

      if (!dataMap.has(category)) {
        dataMap.set(category, { category, brent: value, wti: 0 });
      } else {
        const item = dataMap.get(category);
        item.brent = value;
      }
    }
  });

  // Process right axis data
  rightAxisDataRows.forEach(row => {
    const categoryInput = row.querySelector(".category-input");
    const valueInput = row.querySelector(".value-input");

    if (categoryInput && categoryInput.value.trim()) {
      const category = categoryInput.value.trim();
      const value = parseFloat(valueInput.value) || 0;

      if (!dataMap.has(category)) {
        dataMap.set(category, { category, brent: 0, wti: value });
      } else {
        const item = dataMap.get(category);
        item.wti = value;
      }
    }
  });

  // Convert map to array
  const newData = Array.from(dataMap.values());

  // Update chart data if we have rows
  if (newData.length > 0) {
    chart.data = newData;
  }

  // Close the settings panel
  const offcanvasElement = bootstrap.Offcanvas.getInstance(settingsPanel);
  if (offcanvasElement) {
    offcanvasElement.hide();
  }
};

// Helper function to add a data row with specified values
function addDualAxisDataRow(tableBody, category, value, axis) {
  const rowId = "dualaxis-" + axis + "-row-" + Date.now() + "-" + Math.floor(Math.random() * 1000);

  const row = document.createElement("tr");
  row.id = rowId;
  row.innerHTML = `
    <td>
      <input type="text" class="form-control form-control-sm category-input" value="${category || ''}">
    </td>
    <td>
      <input type="number" class="form-control form-control-sm value-input" value="${value || 0}">
    </td>
    <td>
      <button class="btn btn-sm btn-outline-danger" onclick="removeDualAxisDataRow('${rowId}')">
        <i class="las la-trash"></i>
      </button>
    </td>
  `;

  tableBody.appendChild(row);
}

// Function to add a new empty data row
function addNewDualAxisLeftDataRow() {
  const tableBody = document.getElementById("dualAxisLeftDataBody");
  addDualAxisDataRow(tableBody, "", 0, "left");
}

function addNewDualAxisRightDataRow() {
  const tableBody = document.getElementById("dualAxisRightDataBody");
  addDualAxisDataRow(tableBody, "", 0, "right");
}

// Function to remove a data row
function removeDualAxisDataRow(rowId) {
  const row = document.getElementById(rowId);
  if (row) {
    row.remove();
  }
}
