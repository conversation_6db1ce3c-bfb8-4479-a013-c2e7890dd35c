/* Line Separator Widget Styles */
.line-separator-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.line-separator-widget .widget-header {
  padding: 8px 12px;
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  border-radius: 6px 6px 0 0;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.line-separator-widget .widget-header button {
  background: none;
  border: none;
  color: white;
  font-size: 14px;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.line-separator-widget .widget-header button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.line-separator-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  overflow: hidden;
}

/* Line Separator Base Styles */
.line-separator {
  display: block;
  border: none;
  background: none;
  transition: all 0.2s ease;
}

/* Horizontal Line Separator */
.line-separator.horizontal {
  width: 100%;
  height: 0;
  border-top: 2px solid #333;
  margin: 20px 0;
}

/* Vertical Line Separator */
.line-separator.vertical {
  width: 0;
  height: 100%;
  border-left: 2px solid #333;
  margin: 0 20px;
}

/* Line Styles */
.line-separator.solid {
  /* Default solid style - no additional changes needed */
}

.line-separator.dashed.horizontal {
  border-top-style: dashed;
}

.line-separator.dashed.vertical {
  border-left-style: dashed;
}

.line-separator.dotted.horizontal {
  border-top-style: dotted;
}

.line-separator.dotted.vertical {
  border-left-style: dotted;
}

.line-separator.double.horizontal {
  border-top-style: double;
}

.line-separator.double.vertical {
  border-left-style: double;
}

/* Container adjustments for different orientations */
.line-separator-container:has(.line-separator.horizontal) {
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.line-separator-container:has(.line-separator.vertical) {
  align-items: center;
  justify-content: center;
  flex-direction: row;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .line-separator-widget .widget-header {
    padding: 6px 8px;
    font-size: 11px;
  }

  .line-separator-container {
    padding: 6px;
  }

  .line-separator.horizontal {
    margin: 10px 0;
  }

  .line-separator.vertical {
    margin: 0 10px;
  }
}

/* Settings panel styles */
.line-separator-widget .offcanvas-body {
  padding: 20px;
}

.line-separator-widget .form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.line-separator-widget .form-control,
.line-separator-widget .form-select {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.line-separator-widget .form-control:focus,
.line-separator-widget .form-select:focus {
  border-color: #6c757d;
  box-shadow: 0 0 0 2px rgba(108, 117, 125, 0.2);
  outline: none;
}

.line-separator-widget .form-control-color {
  width: 100%;
  height: 40px;
  border-radius: 4px;
  cursor: pointer;
}

.line-separator-widget .form-range {
  margin: 8px 0;
}

.line-separator-widget .form-check-input:checked {
  background-color: #6c757d;
  border-color: #6c757d;
}

.line-separator-widget .form-check-label {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.line-separator-widget .form-check-label i {
  font-size: 16px;
}

.line-separator-widget .btn-primary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-weight: 600;
  transition: transform 0.2s, box-shadow 0.2s;
}

.line-separator-widget .btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

/* Prevent text selection on widget header */
.line-separator-widget .widget-header {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Ensure proper spacing in grid */
.grid-stack-item .line-separator-widget {
  margin: 0;
  height: 100%;
}

/* Animation for smooth transitions */
.line-separator-widget * {
  transition: all 0.2s ease;
}

/* Print styles */
@media print {
  .line-separator-widget .widget-header button {
    display: none;
  }

  .line-separator-widget {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .line-separator {
    border-color: #000 !important;
  }
}

/* Special styling for very small widgets */
.grid-stack-item[gs-h="1"] .line-separator-widget .widget-header {
  padding: 4px 8px;
  font-size: 10px;
}

.grid-stack-item[gs-w="1"] .line-separator-widget .widget-header {
  padding: 4px 6px;
  font-size: 10px;
}

.grid-stack-item[gs-h="1"] .line-separator-container {
  padding: 4px;
}

.grid-stack-item[gs-w="1"] .line-separator-container {
  padding: 4px;
}

/* Hover effects for better user experience */
.line-separator-widget:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.line-separator-widget:hover .line-separator {
  opacity: 0.8;
}

/* Focus styles for accessibility */
.line-separator-widget:focus-within {
  outline: 2px solid #6c757d;
  outline-offset: 2px;
}

/* Ensure line separator is visible in all themes */
.line-separator.horizontal {
  min-height: 1px;
}

.line-separator.vertical {
  min-width: 1px;
}
