// Workspace Feature JavaScript

// Available users for assignment
const workspaceUsers = [
  { id: 1, name: "<PERSON><PERSON>", avatar: "<PERSON>" },
  { id: 2, name: "<PERSON><PERSON><PERSON>", avatar: "<PERSON><PERSON>" },
  { id: 3, name: "<PERSON><PERSON><PERSON>", avatar: "<PERSON><PERSON>" },
  { id: 4, name: "Vima<PERSON> Thapliyal", avatar: "VT" },
  { id: 5, name: "<PERSON><PERSON><PERSON>", avatar: "<PERSON>" },
  { id: 6, name: "Priya Verma", avatar: "PV" },
];

// Sample workspace data
const workspaceData = {
  sections: [
    {
      id: "analytics",
      title: "Analytics Dashboards",
      widgets: [
        {
          id: "widget1",
          title: "Monthly Revenue",
          type: "Chart",
          user: 1,
          icon: "las la-chart-line",
        },
        {
          id: "widget2",
          title: "User Engagement",
          type: "Dashboard",
          user: 2,
          icon: "las la-users",
        },
        {
          id: "widget3",
          title: "Conversion Rates",
          type: "Report",
          user: 1,
          icon: "las la-percentage",
        },
      ],
    },
    {
      id: "dashboards",
      title: "Custom Dashboards",
      widgets: [
        {
          id: "widget4",
          title: "Project Timeline",
          type: "Gantt",
          user: 3,
          icon: "las la-calendar-alt",
        },
        {
          id: "widget5",
          title: "Team Performance",
          type: "Dashboard",
          user: 4,
          icon: "las la-user-chart",
        },
        {
          id: "widget6",
          title: "Resource Allocation",
          type: "Report",
          user: 2,
          icon: "las la-sitemap",
        },
      ],
    },
    {
      id: "reports",
      title: "Generated Reports",
      widgets: [
        {
          id: "widget7",
          title: "Monthly KPI Summary",
          type: "Report",
          user: 1,
          icon: "las la-file-alt",
        },
        {
          id: "widget8",
          title: "Market Analysis",
          type: "Chart",
          user: 5,
          icon: "las la-chart-pie",
        },
      ],
    },
  ],
};

// Initialize workspace feature
function initWorkspace() {
  setupWorkspacePanel();
  setupEventListeners();
  populateUserFilters();

  // Set default tab and user filter
  setActiveTab("my-tasks");
  filterWorkspaceByUser("all");
}

// Setup the workspace panel
function setupWorkspacePanel() {
  const workspaceBtn = document.getElementById("workspaceBtn");
  const workspacePanel = document.getElementById("workspacePanel");

  if (!workspaceBtn || !workspacePanel) return;

  // Add class for styling
  workspacePanel.classList.add("workspace-panel");
}

// Toggle workspace sidebar
function toggleWorkspace() {
  const workspacePanel = new bootstrap.Offcanvas(
    document.getElementById("workspacePanel")
  );
  workspacePanel.toggle();
}

// Setup all event listeners
function setupEventListeners() {
  // Workspace toggle button
  const workspaceBtn = document.getElementById("workspaceBtn");
  if (workspaceBtn) {
    workspaceBtn.addEventListener("click", toggleWorkspace);
  }

  // Navigation tabs
  const navItems = document.querySelectorAll(".workspace-nav-item");
  navItems.forEach((item) => {
    item.addEventListener("click", function () {
      const tabId = this.getAttribute("data-tab");
      setActiveTab(tabId);
    });
  });

  // Search input
  const searchInput = document.querySelector(".workspace-search input");
  if (searchInput) {
    searchInput.addEventListener("input", function () {
      filterWorkspaceItems(this.value);
    });
  }
}

// Set active navigation tab
function setActiveTab(tabId) {
  // Update navigation highlighting
  const navItems = document.querySelectorAll(".workspace-nav-item");
  navItems.forEach((item) => {
    if (item.getAttribute("data-tab") === tabId) {
      item.classList.add("active");
    } else {
      item.classList.remove("active");
    }
  });

  // Show/hide content tabs
  const contentTabs = document.querySelectorAll(".workspace-content-tab");
  contentTabs.forEach((tab) => {
    if (tab.getAttribute("id") === `${tabId}-content`) {
      tab.style.display = "block";
    } else {
      tab.style.display = "none";
    }
  });
}

// Populate user filters
function populateUserFilters() {
  const userFilterContainer = document.getElementById("workspaceUsers");
  if (!userFilterContainer) return;

  // Clear existing content
  userFilterContainer.innerHTML = "";

  // Add "All Users" option
  const allUsersElement = document.createElement("div");
  allUsersElement.className = "workspace-user active";
  allUsersElement.setAttribute("data-user", "all");
  allUsersElement.innerHTML = `
    <div class="workspace-user-avatar" style="background-color: #3498db;">
      <i class="las la-users"></i>
    </div>
    <div class="workspace-user-name">All</div>
  `;
  userFilterContainer.appendChild(allUsersElement);

  // Add individual users
  workspaceUsers.forEach((user) => {
    const userElement = document.createElement("div");
    userElement.className = "workspace-user";
    userElement.setAttribute("data-user", user.id);
    userElement.innerHTML = `
      <div class="workspace-user-avatar">${user.avatar}</div>
      <div class="workspace-user-name">${user.name}</div>
    `;
    userFilterContainer.appendChild(userElement);
  });

  // Add click event for user filtering
  const userElements = document.querySelectorAll(".workspace-user");
  userElements.forEach((element) => {
    element.addEventListener("click", function () {
      const userId = this.getAttribute("data-user");

      // Update active state
      userElements.forEach((el) => el.classList.remove("active"));
      this.classList.add("active");

      // Filter workspace items
      filterWorkspaceByUser(userId);
    });
  });
}

// Filter workspace by user
function filterWorkspaceByUser(userId) {
  renderWorkspaceItems(userId);
}

// Filter workspace items by search term
function filterWorkspaceItems(searchTerm) {
  const items = document.querySelectorAll(".workspace-item");
  const normalizedSearch = searchTerm.toLowerCase().trim();

  items.forEach((item) => {
    const title = item
      .querySelector(".workspace-item-title")
      .textContent.toLowerCase();
    const type = item
      .querySelector(".workspace-item-type")
      .textContent.toLowerCase();

    if (title.includes(normalizedSearch) || type.includes(normalizedSearch)) {
      item.style.display = "flex";
    } else {
      item.style.display = "none";
    }
  });

  // Show/hide empty state
  updateEmptyStates();
}

// Render workspace items based on user filter
function renderWorkspaceItems(userId) {
  const myTasksContainer = document.getElementById("my-tasks-items");
  const allItemsContainer = document.getElementById("all-items-items");

  if (!myTasksContainer || !allItemsContainer) return;

  // Clear containers
  myTasksContainer.innerHTML = "";
  allItemsContainer.innerHTML = "";

  let myTasksCount = 0;
  let allItemsCount = 0;

  // Process each section and its widgets
  workspaceData.sections.forEach((section) => {
    section.widgets.forEach((widget) => {
      // For My Tasks, only show items assigned to selected user
      if (
        (userId === "all" || widget.user.toString() === userId) &&
        userId !== "all"
      ) {
        myTasksContainer.appendChild(
          createWorkspaceItemElement(widget, section.title)
        );
        myTasksCount++;
      }

      // For All Items, show everything or filter by user
      if (userId === "all" || widget.user.toString() === userId) {
        allItemsContainer.appendChild(
          createWorkspaceItemElement(widget, section.title)
        );
        allItemsCount++;
      }
    });
  });

  // Show empty state if needed
  if (myTasksCount === 0) {
    myTasksContainer.innerHTML = generateEmptyState(
      "No assigned tasks",
      "When tasks are assigned to you, they will appear here."
    );
  }

  if (allItemsCount === 0) {
    allItemsContainer.innerHTML = generateEmptyState(
      "No items found",
      "Try selecting a different user filter."
    );
  }
}

// Create HTML for workspace item
function createWorkspaceItemElement(widget, sectionTitle) {
  const itemElement = document.createElement("div");
  itemElement.className = "workspace-item";
  itemElement.setAttribute("data-id", widget.id);

  // Find user details
  const user = workspaceUsers.find((u) => u.id === widget.user) || {
    name: "Unassigned",
    avatar: "?",
  };

  itemElement.innerHTML = `
    <div class="workspace-item-icon">
      <i class="${widget.icon}"></i>
    </div>
    <div class="workspace-item-details">
      <div class="workspace-item-title">${widget.title}</div>
      <div class="workspace-item-type">
        <span>${widget.type}</span> · <span>${sectionTitle}</span>
      </div>
    </div>
    <div class="workspace-item-user">
      <div class="workspace-item-user-avatar" title="${user.name}">${user.avatar}</div>
    </div>
    <div class="workspace-item-actions">
      <button class="btn btn-outline-primary btn-icon" title="View Details">
        <i class="las la-eye"></i>
      </button>
    </div>
  `;

  // Add click event
  itemElement
    .querySelector(".btn-icon")
    .addEventListener("click", function (e) {
      e.stopPropagation();
      viewWorkspaceItem(widget.id);
    });

  return itemElement;
}

// Generate empty state HTML
function generateEmptyState(title, message) {
  return `
    <div class="workspace-alert workspace-alert-info">
      <i class="las la-info-circle"></i>
      <div>
        <strong>${title}</strong>
        <div>${message}</div>
      </div>
    </div>
  `;
}

// Update empty states based on visible items
function updateEmptyStates() {
  const containers = ["my-tasks-items", "all-items-items"];

  containers.forEach((containerId) => {
    const container = document.getElementById(containerId);
    if (!container) return;

    const visibleItems = container.querySelectorAll(
      '.workspace-item[style="display: flex;"], .workspace-item:not([style*="display"])'
    );
    const emptyState = container.querySelector(".workspace-alert");

    if (visibleItems.length === 0 && !emptyState) {
      // All items are hidden - show empty state
      container.innerHTML = generateEmptyState(
        "No matching items",
        "Try adjusting your search query."
      );
    } else if (visibleItems.length > 0 && emptyState) {
      // We have visible items but empty state exists - remove it
      emptyState.remove();
    }
  });
}

// View workspace item details
function viewWorkspaceItem(itemId) {
  console.log(`Viewing item: ${itemId}`);
  // This would typically open a modal or navigate to a details page
  alert(`Viewing details for item: ${itemId}`);
}

// Assignment Management Functions
let isAssignmentFormVisible = false;
let currentAssignments = [
  {
    id: 1,
    type: "section",
    name: "Analytics Dashboards",
    assignedTo: "Deepak Singh",
  },
  {
    id: 2,
    type: "widget",
    name: "Revenue Analysis",
    assignedTo: "Sneha Issar",
  },
  {
    id: 3,
    type: "section",
    name: "Custom Dashboards",
    assignedTo: "Piyush Kumar",
  },
];

// Show/hide assignment form
function createNewAssignment() {
  const form = document.querySelector(".assignment-form");
  if (form) {
    form.style.display = isAssignmentFormVisible ? "none" : "block";
    isAssignmentFormVisible = !isAssignmentFormVisible;
  }
}

// Cancel assignment creation
function cancelAssignment() {
  const form = document.querySelector(".assignment-form");
  if (form) {
    form.style.display = "none";
    isAssignmentFormVisible = false;

    // Reset form fields
    document.getElementById("itemTypeSelect").selectedIndex = 0;
    document.getElementById("itemSelect").selectedIndex = 0;
    document.getElementById("assigneeSelect").selectedIndex = 0;
  }
}

// Save new assignment
function saveAssignment() {
  const itemType = document.getElementById("itemTypeSelect").value;
  const item = document.getElementById("itemSelect");
  const assignee = document.getElementById("assigneeSelect");

  if (!item.value || !assignee.value) {
    alert("Please select both an item and an assignee.");
    return;
  }

  const newAssignment = {
    id: currentAssignments.length + 1,
    type: itemType,
    name: item.options[item.selectedIndex].text,
    assignedTo: assignee.options[assignee.selectedIndex].text,
  };

  currentAssignments.push(newAssignment);
  refreshAssignmentsList();
  cancelAssignment(); // Hide and reset form
}

// Remove an assignment
function removeAssignment(assignmentId) {
  if (confirm("Are you sure you want to remove this assignment?")) {
    currentAssignments = currentAssignments.filter(
      (a) => a.id !== assignmentId
    );
    refreshAssignmentsList();
  }
}

// Refresh the assignments list in the UI
function refreshAssignmentsList() {
  const listContainer = document.querySelector(
    ".current-assignments .list-group"
  );
  if (!listContainer) return;

  listContainer.innerHTML = currentAssignments
    .map(
      (assignment) => `
    <div class="list-group-item border-0 mb-2" style="background: #f8fafc;">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <div style="font-size: 13px;">${assignment.name}</div>
          <small class="text-muted" style="font-size: 11px;">${assignment.type} • ${assignment.assignedTo}</small>
        </div>
        <button class="btn btn-sm btn-light" onclick="removeAssignment(${assignment.id})">
          <i class="las la-times"></i>
        </button>
      </div>
    </div>
  `
    )
    .join("");
}

// Handle item type change
function handleItemTypeChange() {
  const itemType = document.getElementById("itemTypeSelect").value;
  const itemSelect = document.getElementById("itemSelect");

  // Clear existing options
  while (itemSelect.firstChild) {
    itemSelect.removeChild(itemSelect.firstChild);
  }

  // Add default option
  const defaultOption = document.createElement("option");
  defaultOption.value = "";
  defaultOption.disabled = true;
  defaultOption.selected = true;
  defaultOption.textContent = "Choose item...";
  itemSelect.appendChild(defaultOption);

  // Add appropriate options based on type
  if (itemType === "section") {
    const sections = [
      { value: "analytics", text: "Analytics Dashboards" },
      { value: "reports", text: "Generated Reports" },
      { value: "custom", text: "Custom Dashboards" },
    ];

    const group = document.createElement("optgroup");
    group.label = "Sections";
    sections.forEach((section) => {
      const option = document.createElement("option");
      option.value = section.value;
      option.textContent = section.text;
      group.appendChild(option);
    });
    itemSelect.appendChild(group);
  } else {
    const widgets = [
      { value: "revenue", text: "Revenue Analysis" },
      { value: "users", text: "User Engagement" },
      { value: "conversion", text: "Conversion Rates" },
      { value: "performance", text: "Team Performance" },
    ];

    const group = document.createElement("optgroup");
    group.label = "Widgets";
    widgets.forEach((widget) => {
      const option = document.createElement("option");
      option.value = widget.value;
      option.textContent = widget.text;
      group.appendChild(option);
    });
    itemSelect.appendChild(group);
  }
}

// Initialize assignment management
function initAssignmentManagement() {
  // Hide assignment form initially
  const form = document.querySelector(".assignment-form");
  if (form) {
    form.style.display = "none";
  }

  // Setup item type change handler
  const itemTypeSelect = document.getElementById("itemTypeSelect");
  if (itemTypeSelect) {
    itemTypeSelect.addEventListener("change", handleItemTypeChange);
  }

  // Initial assignments list render
  refreshAssignmentsList();
}

// Populate workspace items with realistic data
function populateWorkspaceItems() {
  const container = document.querySelector(".workspace-items");
  if (!container) return;

  const items = [
    {
      title: "Q4 2023 Revenue Dashboard",
      type: "Interactive Analytics",
      status: "Live",
      icon: "las la-chart-line",
      lastUpdate: "2h ago",
      user: { initials: "DS", name: "Deepak Singh" },
      metrics: "Revenue +12.5% MoM",
    },
    {
      title: "Customer Acquisition Funnel",
      type: "Real-time Dashboard",
      status: "Live",
      icon: "las la-filter",
      lastUpdate: "1h ago",
      user: { initials: "SI", name: "Sneha Issar" },
      metrics: "Conv. Rate 3.8%",
    },
    {
      title: "Marketing Campaign Performance",
      type: "Analytics Dashboard",
      status: "Updating",
      icon: "las la-bullhorn",
      lastUpdate: "30m ago",
      user: { initials: "PK", name: "Piyush Kumar" },
      metrics: "ROAS 2.4x",
    },
    {
      title: "Product Usage Analytics",
      type: "Interactive Report",
      status: "Live",
      icon: "las la-gauge-high",
      lastUpdate: "45m ago",
      user: { initials: "VT", name: "Vimal Thapliyal" },
      metrics: "MAU 125.3k",
    },
    {
      title: "Sales Pipeline Analysis",
      type: "Predictive Analytics",
      status: "Processing",
      icon: "las la-money-bill-trend-up",
      lastUpdate: "15m ago",
      user: { initials: "RS", name: "Reyansh Sharma" },
      metrics: "Pipeline $2.4M",
    },
  ];

  container.innerHTML = items
    .map(
      (item) => `
    <div class="workspace-item mb-3" style="background: #fff; border: 1px solid #e5e7eb;">
      <div class="p-4">
        <div class="d-flex justify-content-between align-items-start mb-3">
          <div class="d-flex align-items-center">
            <div class="me-3" style="width: 40px; height: 40px; background: #f3f4f6; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
              <i class="${item.icon} text-primary"></i>
            </div>
            <div>
              <h6 class="mb-1" style="font-size: 14px; font-weight: 500;">${
                item.title
              }</h6>
              <div class="d-flex align-items-center" style="font-size: 12px; color: #6b7280;">
                <span class="me-2">${item.type}</span>
                <span class="badge ${
                  item.status === "Live"
                    ? "bg-success"
                    : item.status === "Updating"
                    ? "bg-warning"
                    : "bg-info"
                }" 
                      style="font-size: 10px; font-weight: 500;">
                  ${item.status}
                </span>
              </div>
            </div>
          </div>
          <div class="d-flex align-items-center">
            <div class="me-3 text-end">
              <div style="font-size: 12px; font-weight: 500; color: #1f2937;">${
                item.metrics
              }</div>
              <div style="font-size: 11px; color: #6b7280;">Updated ${
                item.lastUpdate
              }</div>
            </div>
            <div class="dropdown">
              <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown">
                <i class="las la-ellipsis"></i>
              </button>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#"><i class="las la-edit me-2"></i>Edit</a></li>
                <li><a class="dropdown-item" href="#"><i class="las la-share me-2"></i>Share</a></li>
                <li><a class="dropdown-item" href="#"><i class="las la-download me-2"></i>Export</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="#"><i class="las la-trash me-2"></i>Delete</a></li>
              </ul>
            </div>
          </div>
        </div>
        <div class="d-flex align-items-center justify-content-between pt-3" style="border-top: 1px solid #e5e7eb;">
          <div class="d-flex align-items-center">
            <div class="me-2" style="width: 28px; height: 28px; background: linear-gradient(135deg, #4F46E5, #7C3AED); color: white; border-radius: 6px; display: flex; align-items: center; justify-content: center; font-size: 11px;">
              ${item.user.initials}
            </div>
            <div style="font-size: 12px; color: #4b5563;">${
              item.user.name
            }</div>
          </div>
          <div class="d-flex gap-2">
            <button class="btn btn-sm btn-light" title="Quick View">
              <i class="las la-eye"></i>
            </button>
            <button class="btn btn-sm btn-light" title="Refresh Data">
              <i class="las la-sync-alt"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  `
    )
    .join("");

  // Add event listeners for actions
  container.querySelectorAll(".workspace-item").forEach((item) => {
    // Quick View button
    item.querySelector('[title="Quick View"]').addEventListener("click", () => {
      // Implement quick view functionality
      console.log("Quick view clicked");
    });

    // Refresh Data button
    item
      .querySelector('[title="Refresh Data"]')
      .addEventListener("click", function () {
        const icon = this.querySelector("i");
        icon.classList.add("la-spin");
        setTimeout(() => {
          icon.classList.remove("la-spin");
        }, 1000);
      });
  });
}

// Add to initialization
document.addEventListener("DOMContentLoaded", function () {
  initWorkspace();
  initAssignmentManagement();
  populateWorkspaceItems();
});
