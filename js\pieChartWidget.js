// Wait for amCharts to be fully loaded
function waitForAmCharts() {
  return new Promise((resolve, reject) => {
    const maxWaitTime = 5000; // Maximum wait time in milliseconds
    let waitTimeout;

    function checkOtherDependencies() {
      const dependencies = {
        am5percent: typeof window.am5percent !== "undefined",
        am5themes_Animated: typeof window.am5themes_Animated !== "undefined",
      };
      console.log("Checking other amCharts dependencies:", dependencies);
      return Object.entries(dependencies).every(([key, loaded]) => {
        if (!loaded) {
          console.log(`Waiting for ${key} to load...`);
        }
        return loaded;
      });
    }

    function attemptResolve() {
      if (checkOtherDependencies()) {
        clearTimeout(waitTimeout);
        console.log("All amCharts dependencies are loaded.");
        resolve();
      } else {
        console.log("Still waiting for other amCharts dependencies...");
        // Keep waiting, timeout will handle failure
      }
    }

    function setupReadyCheck() {
      if (typeof window.am5 === "undefined") {
        console.log("Waiting for am5 core...");
        // Check again shortly if am5 isn't even defined yet
        setTimeout(setupReady<PERSON>heck, 100);
        return;
      }

      console.log("am5 core found, setting up am5.ready() callback.");

      // Use am5.ready() for core library initialization
      window.am5.ready(() => {
        console.log("am5.ready() callback executed.");
        // Now check the other dependencies periodically
        const checkInterval = setInterval(() => {
          if (checkOtherDependencies()) {
            clearInterval(checkInterval);
            clearTimeout(waitTimeout); // Clear the overall timeout
            console.log("All amCharts dependencies are now confirmed loaded.");
            resolve();
          }
        }, 100);
      });

      // Set a timeout for the whole process
      waitTimeout = setTimeout(() => {
        const error = new Error("amCharts failed to load within timeout");
        console.error(error);
        // Log which dependencies might be missing
        const coreLoaded =
          typeof window.am5 !== "undefined" &&
          typeof window.am5.ready === "function";
        const percentLoaded = typeof window.am5percent !== "undefined";
        const animatedLoaded = typeof window.am5themes_Animated !== "undefined";
        console.error(
          `Status: Core: ${coreLoaded}, Percent: ${percentLoaded}, Animated: ${animatedLoaded}`
        );
        reject(error);
      }, maxWaitTime);
    }

    // Start the check
    setupReadyCheck();
  });
}

/**
 * Generate pie chart widget markup
 * Shared between onclick and drag-drop functionality
 */
function getPieChartWidgetMarkup(chartId) {
  return `
    <div class="widget p-2">
      <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
        <div class="widget-title">
          <span>Pie Chart</span>
        </div>
        <div class="widget-actions">
          <button class="btn btn-link"
                  data-bs-toggle="offcanvas"
                  data-bs-target="#pieChartSettings"
                  aria-controls="pieChartSettings"
                  onclick="initPieChartSettings('${chartId}')">
            <i class="las la-cog"></i>
          </button>
          <button class="btn btn-link ms-1"
                  onclick="removeWidget(this)">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      <div class="widget-body">
        <div id="${chartId}" class="chart-container"></div>
      </div>
    </div>
  `;
}

// Add a pie chart widget using amCharts v5
async function addPieChartWidget() {
  console.log("Adding pie chart widget");

  try {
    // Wait for amCharts to load
    console.log("Waiting for amCharts dependencies...");
    await waitForAmCharts();
    console.log("amCharts dependencies loaded successfully");

    const chartId = `piechart-${Date.now()}`;

    // Add the widget to the grid using shared markup
    const widget = grid.addWidget({
      x: 0,
      y: 0,
      w: 6,
      h: 4,
      content: getPieChartWidgetMarkup(chartId)
    });

    // Initialize the chart immediately after widget is added
    try {
      console.log("Widget added to grid, now initializing chart");
      await window.initPieChart(chartId);
      console.log("Chart initialized successfully");
    } catch (error) {
      console.error("Error initializing chart:", error);
      // Show error in the widget
      const container = document.getElementById(chartId);
      if (container) {
        container.innerHTML = `
          <div class="alert alert-danger">
            Failed to initialize chart. Please try again.
            <br>
            <small class="text-muted">${error.message}</small>
          </div>
        `;
      }
    }

    return widget;
  } catch (error) {
    console.error("Failed to load amCharts dependencies:", error);
    // Show error widget
    return grid.addWidget({
      x: 0,
      y: 0,
      w: 6,
      h: 3,
      content: `
        <div class="widget p-2">
          <div class="alert alert-danger">
            Failed to load chart dependencies. Please try again later.
            <br>
            <small class="text-muted">${error.message}</small>
          </div>
        </div>
      `,
    });
  }
}

// Function to initialize settings for a specific chart
function initPieChartSettings(chartId) {
  // Store the current chart ID in the settings panel
  const settingsPanel = document.getElementById("pieChartSettings");
  settingsPanel.dataset.currentChart = chartId;

  // Get the current chart settings
  const chart = document.getElementById(chartId)?.chart;
  if (!chart) return;

  // Load chart data into the data table
  loadPieChartDataToTable(chart);

  const series = chart.series.getIndex(0);
  if (!series) return;

  // Find chart title
  let chartTitle = "Distribution";

  // Get the chart container (parent of the chart)
  const chartContainer = chart.root.container.children.values[0];

  if (chartContainer) {
    // Look for the title in the container's children
    chartContainer.children.each(function (child) {
      if (child.isType("Label") && child.get("role") === "title") {
        chartTitle = child.get("text");
        return false; // Break the loop
      }
    });
  }
  document.getElementById("pieChartSettings-chartTitle").value = chartTitle;

  // Update settings panel with current values
  document.getElementById("pieChartSettings-type").value = series.get(
    "innerRadius"
  )
    ? "donut"
    : "pie";

  document.getElementById("pieChartSettings-innerRadius").value = Math.round(
    (series.get("innerRadius") || 0) * 100
  );

  document.getElementById("pieChartSettings-radius").value = Math.round(
    (series.get("radius") || 0.9) * 100
  );

  // Check if labels exist before accessing template
  let labelsVisible = true;
  if (series.labels && series.labels.template) {
    labelsVisible = !series.labels.template.get("forceHidden");
  }
  document.getElementById("pieChartSettings-labels").checked = labelsVisible;

  // Check if legend exists
  let legendVisible = true;
  if (chart.legend) {
    legendVisible = !chart.legend.get("forceHidden");
  }
  document.getElementById("pieChartSettings-legend").checked = legendVisible;
}

// Function to apply pie chart settings
function applyPieChartSettings() {
  const settingsPanel = document.getElementById("pieChartSettings");
  const chartId = settingsPanel.dataset.currentChart;
  if (!chartId) return;

  const chart = document.getElementById(chartId)?.chart;
  if (!chart) return;

  // Get settings values
  const chartTitle = document.getElementById(
    "pieChartSettings-chartTitle"
  ).value;
  const type = document.getElementById("pieChartSettings-type").value;
  const innerRadius =
    document.getElementById("pieChartSettings-innerRadius").value / 100;
  const radius = document.getElementById("pieChartSettings-radius").value / 100;
  const showLabels = document.getElementById("pieChartSettings-labels").checked;
  const showLegend = document.getElementById("pieChartSettings-legend").checked;

  // Get the series
  const series = chart.series.getIndex(0);
  if (!series) return;

  // Update chart title
  // The title is in the chartContainer, not directly in the chart
  let chartTitleLabel = null;

  // Get the chart container (parent of the chart)
  const chartContainer = chart.root.container.children.values[0];

  if (chartContainer) {
    // Look for the title in the container's children
    chartContainer.children.each(function (child) {
      if (child.isType("Label") && child.get("role") === "title") {
        chartTitleLabel = child;
        return false; // Break the loop
      }
    });

    if (chartTitleLabel) {
      // Update existing title
      chartTitleLabel.set("text", chartTitle);
    } else {
      // If no title found, add it as the first child (at the top)
      chartTitleLabel = chartContainer.children.unshift(
        window.am5.Label.new(chart.root, {
          text: chartTitle,
          fontSize: 12,
          fontWeight: "500",
          textAlign: "center",
          x: window.am5.p50,
          centerX: window.am5.p50,
          paddingTop: 5,
          paddingBottom: 10,
          role: "title",
        })
      );
    }
  }

  // Apply settings
  series.set("radius", window.am5.percent(radius * 100));
  series.set(
    "innerRadius",
    type === "donut" ? window.am5.percent(innerRadius * 100) : 0
  );

  // Toggle labels (if they exist)
  if (series.labels && series.labels.template) {
    series.labels.template.set("forceHidden", !showLabels);
  }

  // Toggle legend
  const legend =
    chart.children.indexOf(chart.legend) !== -1 ? chart.legend : null;
  if (legend) {
    legend.set("forceHidden", !showLegend);
  }

  // Update chart data from the table
  updatePieChartDataFromTable(chart);

  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(settingsPanel);
  offcanvas.hide();
}

// Initialize a pie chart using amCharts v5
window.initPieChart = async function (containerId) {
  console.log("Starting pie chart initialization for container:", containerId);

  try {
    const container = document.getElementById(containerId);
    if (!container || !(container instanceof Element)) {
      console.error(
        "Chart container not found or not an Element:",
        containerId
      );
      return;
    }

    // Dispose previous amCharts Root/chart instance if it exists
    if (container.chart && typeof container.chart.dispose === "function") {
      try {
        container.chart.dispose();
      } catch (e) {
        console.warn("Error disposing previous chart:", e);
      }
      container.chart = null;
    }
    if (container.am5root && typeof container.am5root.dispose === "function") {
      try {
        container.am5root.dispose();
      } catch (e) {
        console.warn("Error disposing previous am5root:", e);
      }
      container.am5root = null;
    }

    // Ensure the container and its parent have proper dimensions
    container.style.width = "100%";
    container.style.height = "100%";
    container.style.position = "relative";

    // If container is inside a widget, ensure the widget has proper height
    const widgetBody = container.closest(".widget-body");
    if (widgetBody) {
      widgetBody.style.height = "100%";
    }

    // Check if amCharts is properly loaded
    if (
      typeof window.am5 === "undefined" ||
      typeof window.am5.Root === "undefined"
    ) {
      console.error(
        "amCharts library (am5) is not properly loaded. Waiting for it to load..."
      );

      // Show loading message in the container
      container.innerHTML = `
        <div class="alert alert-warning">
          Loading chart library...
        </div>
      `;

      // Wait for amCharts to load (with timeout)
      await new Promise((resolve, reject) => {
        let attempts = 0;
        const maxAttempts = 20; // 10 seconds max wait time

        const checkInterval = setInterval(() => {
          attempts++;
          if (
            typeof window.am5 !== "undefined" &&
            typeof window.am5.Root !== "undefined"
          ) {
            clearInterval(checkInterval);
            resolve();
          } else if (attempts >= maxAttempts) {
            clearInterval(checkInterval);
            reject(new Error("amCharts library failed to load within timeout"));
          }
        }, 500);
      });
    }

    // Create root element
    console.log("Attempting to create am5.Root...");
    console.log("window.am5:", typeof window.am5, window.am5);
    console.log("window.am5.Root:", typeof window.am5?.Root, window.am5?.Root);
    const root = window.am5.Root.new(containerId);
    console.log("am5.Root created successfully:", root);

    // Set themes
    root.setThemes([window.am5themes_Animated.new(root)]);

    // Create a container for the chart and title
    const chartContainer = root.container.children.push(
      window.am5.Container.new(root, {
        width: window.am5.p100,
        height: window.am5.p100,
        layout: root.verticalLayout,
      })
    );

    // Add chart title at the top of the container
    chartContainer.children.push(
      window.am5.Label.new(root, {
        text: "Distribution",
        fontSize: 12,
        fontWeight: "500",
        textAlign: "center",
        x: window.am5.p50,
        centerX: window.am5.p50,
        paddingTop: 5,
        paddingBottom: 10,
        role: "title", // Used to identify this as the chart title
      })
    );

    // Create chart inside the container
    const chart = chartContainer.children.push(
      window.am5percent.PieChart.new(root, {
        layout: root.verticalLayout,
        paddingRight: 40, // Add padding for zoom controls
        endAngle: 270, // Add partial pie effect
      })
    );

    // Add drag handling to prevent GridStack interference
    const chartDiv = document.getElementById(containerId);
    if (chartDiv) {
      // Find the closest grid-stack-item
      const gridItem = chartDiv.closest(".grid-stack-item");
      if (gridItem) {
        // Add mousedown handler to the chart container
        chartDiv.addEventListener("mousedown", function (e) {
          // Check if the click is on a chart element (not widget header/controls)
          if (e.target.closest(".chart-container")) {
            // Temporarily disable GridStack dragging
            const grid = gridItem.gridstackNode?.grid;
            if (grid) {
              grid.movable(gridItem, false);

              // Re-enable dragging on mouseup (anywhere on document)
              const enableDrag = function () {
                grid.movable(gridItem, true);
                document.removeEventListener("mouseup", enableDrag);
              };
              document.addEventListener("mouseup", enableDrag);
            }
          }
        });
      }
    }

    // Skip zoom control for now as it might be causing issues
    console.log("Skipping zoom control to avoid potential issues");

    // Create series
    const series = chart.series.push(
      window.am5percent.PieSeries.new(root, {
        name: "Series",
        valueField: "value",
        categoryField: "category",
        alignLabels: false,
        legendValueText: "{value}",
        legendLabelText: "{category}",
        endAngle: 270, // Match chart's end angle
      })
    );

    // Add hidden state for animations
    series.states.create("hidden", {
      endAngle: -90,
      opacity: 0,
    });

    // Configure series colors and appearance
    series.slices.template.setAll({
      strokeWidth: 2,
      stroke: window.am5.color(0xffffff),
      cornerRadius: 4,
      fillOpacity: 0.9,
      tooltipText: "{category}: {value}",
    });

    // Set up hover state
    series.slices.template.states.create("hover", {
      scale: 1.05,
      fillOpacity: 1,
    });

    // Add legend with custom settings
    const legend = chart.children.push(
      window.am5.Legend.new(root, {
        centerX: window.am5.percent(50),
        x: window.am5.percent(50),
        layout: root.horizontalLayout,
        height: window.am5.percent(15),
        marginTop: 10,
        paddingTop: 0,
        paddingBottom: 0,
      })
    );

    legend.data.setAll(series.dataItems);

    // Set data
    const data = [
      { category: "Technology", value: 30 },
      { category: "Healthcare", value: 25 },
      { category: "Finance", value: 20 },
      { category: "Consumer", value: 15 },
      { category: "Energy", value: 10 },
    ];

    // Set the data
    series.data.setAll(data);

    // Manually set colors for each slice
    series.slices.each((slice, i) => {
      if (slice) {
        const colorIndex = i % window.chartConfig.brandColors.length;
        slice.set(
          "fill",
          window.am5.color(window.chartConfig.brandColors[colorIndex])
        );
      }
    });

    // Add animation with specific timing from reference
    series.appear(1000, 100);
    chart.appear(1000, 100);

    console.log(
      "Pie chart initialization complete for container:",
      containerId
    );

    // Store the chart instance on the container element
    container.chart = chart;
    container.am5root = root; // Store the root as well

    // Return the root object so it doesn't get garbage collected
    return root;
  } catch (error) {
    console.error("Error initializing pie chart:", error);
    return;
  }
};

// Function to load pie chart data into the data table
function loadPieChartDataToTable(chart) {
  const tableBody = document.getElementById("pieChartDataBody");
  if (!tableBody) return;

  // Clear existing rows
  tableBody.innerHTML = "";

  // Get data from the series
  const series = chart.series.getIndex(0);
  if (!series || !series.dataItems) {
    // If no data, generate sample data
    generateSamplePieChartData(tableBody);
    return;
  }

  // Get data items
  const dataItems = series.dataItems;

  // Create rows for each data point
  dataItems.forEach((item) => {
    const category = item.get("category");
    const value = item.get("value");

    // Add a row to the table
    addPieChartDataRow(tableBody, category, value);
  });
}

// Function to generate sample data if none exists
function generateSamplePieChartData(tableBody) {
  if (!tableBody) return;

  // Sample categories
  const categories = [
    "Technology",
    "Healthcare",
    "Finance",
    "Consumer",
    "Energy",
  ];
  const values = [30, 25, 20, 15, 10];

  // Generate sample data
  categories.forEach((category, index) => {
    // Add a row to the table
    addPieChartDataRow(tableBody, category, values[index] || 0);
  });
}

// Helper function to add a data row with specific values
function addPieChartDataRow(tableBody, category, value) {
  if (!tableBody) return;

  // Create a new row
  const row = document.createElement("tr");

  // Category cell
  const categoryCell = document.createElement("td");
  const categoryInput = document.createElement("input");
  categoryInput.type = "text";
  categoryInput.className = "form-control form-control-sm";
  categoryInput.value = category || "";
  categoryCell.appendChild(categoryInput);

  // Value cell
  const valueCell = document.createElement("td");
  const valueInput = document.createElement("input");
  valueInput.type = "number";
  valueInput.className = "form-control form-control-sm";
  valueInput.value = value || 0;
  valueInput.step = "0.1";
  valueCell.appendChild(valueInput);

  // Actions cell
  const actionsCell = document.createElement("td");
  actionsCell.className = "text-center";
  const deleteButton = document.createElement("button");
  deleteButton.className = "btn btn-sm btn-outline-danger";
  deleteButton.style.width = "28px";
  deleteButton.style.height = "28px";
  deleteButton.style.padding = "0";
  deleteButton.innerHTML = "<i class='las la-times'></i>";
  deleteButton.title = "Remove row";
  deleteButton.addEventListener("click", () => {
    row.remove();
  });
  actionsCell.appendChild(deleteButton);

  // Add cells to row
  row.appendChild(categoryCell);
  row.appendChild(valueCell);
  row.appendChild(actionsCell);

  // Add row to table
  tableBody.appendChild(row);
}

// Function to add a new data row
function addNewPieChartDataRow() {
  const tableBody = document.getElementById("pieChartDataBody");
  if (!tableBody) return;

  // Add a new row with default values
  addPieChartDataRow(tableBody, "New Category", 0);
}

// Function to update chart data from the table
function updatePieChartDataFromTable(chart) {
  const tableBody = document.getElementById("pieChartDataBody");
  if (!tableBody || !chart) return;

  // Get all rows
  const rows = tableBody.querySelectorAll("tr");
  if (rows.length === 0) return;

  // Prepare data array
  const chartData = [];

  // Extract data from each row
  rows.forEach((row) => {
    const cells = row.querySelectorAll("td");
    if (cells.length >= 2) {
      const categoryInput = cells[0].querySelector("input");
      const valueInput = cells[1].querySelector("input");

      if (categoryInput && valueInput) {
        const category = categoryInput.value;
        const value = parseFloat(valueInput.value) || 0;

        if (category) {
          chartData.push({
            category: category,
            value: value,
          });
        }
      }
    }
  });

  // Update chart data
  const series = chart.series.getIndex(0);
  if (series) {
    series.data.setAll(chartData);

    // Update colors for each slice
    series.slices.each((slice, i) => {
      if (slice) {
        const colorIndex = i % window.chartConfig.brandColors.length;
        slice.set(
          "fill",
          window.am5.color(window.chartConfig.brandColors[colorIndex])
        );
      }
    });
  }
}

// Function to import CSV data
function importPieChartCSV() {
  const fileInput = document.getElementById("pieChartDataFile");
  if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
    alert("Please select a CSV file to import.");
    return;
  }

  const file = fileInput.files[0];
  const reader = new FileReader();

  reader.onload = function (e) {
    const contents = e.target.result;
    const lines = contents.split("\n");

    // Clear existing data
    const tableBody = document.getElementById("pieChartDataBody");
    if (!tableBody) return;

    tableBody.innerHTML = "";

    // Process each line
    lines.forEach((line, index) => {
      // Skip empty lines
      if (!line.trim()) return;

      // Skip header row if present
      if (
        index === 0 &&
        line.toLowerCase().includes("category") &&
        line.toLowerCase().includes("value")
      ) {
        return;
      }

      const values = line.split(",");
      if (values.length < 2) return; // Need at least category and one value

      const category = values[0].trim();
      const value = parseFloat(values[1]) || 0;

      // Add a row to the table
      addPieChartDataRow(tableBody, category, value);
    });

    alert("CSV data imported successfully!");
  };

  reader.onerror = function () {
    alert("Error reading the file. Please try again.");
  };

  reader.readAsText(file);
}

// Function to export CSV data
function exportPieChartCSV() {
  const tableBody = document.getElementById("pieChartDataBody");
  if (!tableBody) return;

  const rows = tableBody.querySelectorAll("tr");
  if (rows.length === 0) {
    alert("No data to export.");
    return;
  }

  // Create CSV content
  let csvContent = "Category,Value\n";

  rows.forEach((row) => {
    const cells = row.querySelectorAll("td");
    if (cells.length >= 2) {
      const categoryInput = cells[0].querySelector("input");
      const valueInput = cells[1].querySelector("input");

      if (categoryInput && valueInput) {
        const category = categoryInput.value;
        const value = valueInput.value;

        csvContent += `${category},${value}\n`;
      }
    }
  });

  // Create a download link
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.setAttribute("href", url);
  link.setAttribute("download", "pie-chart-data.csv");
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Setup drag-drop functionality for pie chart widgets
 * This ensures both onclick and drag-drop use the same markup and logic
 */
function setupPieChartDragDrop() {
  console.log('🥧 Setting up pie chart drag-drop functionality...');

  const pieChartSidebarContent = [
    {
      w: 6,
      h: 4,
      get content() {
        const chartId = "piechart-" + Date.now() + "-" + Math.floor(Math.random() * 100000);

        // Use the same markup function as onclick
        return getPieChartWidgetMarkup(chartId);
      },
    },
  ];

  // Setup GridStack drag-in
  if (typeof GridStack !== 'undefined' && GridStack.setupDragIn) {
    GridStack.setupDragIn(
      '.widget-item[data-widget-type="pie-chart"]',
      undefined,
      pieChartSidebarContent
    );
    console.log('✅ Pie chart drag-in setup complete');
  } else {
    console.warn('⚠️ GridStack not available, skipping pie chart drag-in setup');
  }
}

// Auto-setup drag-drop when this script loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', setupPieChartDragDrop);
} else {
  // If DOM is already loaded, setup immediately
  setTimeout(setupPieChartDragDrop, 100);
}

// Export the functions
window.addPieChartWidget = addPieChartWidget;
window.getPieChartWidgetMarkup = getPieChartWidgetMarkup; // Export markup function
window.setupPieChartDragDrop = setupPieChartDragDrop; // Export drag-drop setup
window.applyPieChartSettings = applyPieChartSettings;
window.loadPieChartDataToTable = loadPieChartDataToTable;
window.updatePieChartDataFromTable = updatePieChartDataFromTable;
window.addNewPieChartDataRow = addNewPieChartDataRow;
window.importPieChartCSV = importPieChartCSV;
window.exportPieChartCSV = exportPieChartCSV;
