// Simplified section container widget implementation
function addSectionContainerWidget() {
  console.log("Adding section container widget");
  const containerId = "section-" + Date.now();

  // Add the widget to the grid with consistent settings
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 3, // Smaller initial height
    content: getSectionContainerWidgetMarkup(containerId),
    minH: 2, // Minimum height in grid units
    autoPosition: true,
  });

  // Initialize the section container
  requestAnimationFrame(() => {
    try {
      initSectionContainer(containerId);
    } catch (error) {
      console.error("Error initializing section container:", error);
    }
  });

  return widget;
}

// Returns the full markup for a section container widget, including unique IDs
function getSectionContainerWidgetMarkup(sectionId) {
  return `
    <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;">
      <div class="widget-header mb-2 mb-2 fw-bold d-flex justify-content-between align-items-center">
        <div>
         Section Container
        </div>
        <div>
          <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      <div class="nested-grid-container" id="${sectionId}" style="height: calc(100% - 40px); overflow: hidden;"></div>
    </div>
  `;
}

// Simplified initialization without complex mode switching
function initSectionContainer(containerId) {
  console.log("Initializing section container:", containerId);
  const container = document.getElementById(containerId);

  if (!container) {
    console.error("Section container not found:", containerId);
    return;
  }

  // Create a nested grid with consistent settings
  container.innerHTML = '<div class="grid-stack grid-stack-nested"></div>';
  const gridElement = container.querySelector(".grid-stack");

  // Simple styling without excessive min-heights
  gridElement.style.height = "100%";
  gridElement.style.border = "1px dashed rgba(0, 177, 156, 0.3)";
  gridElement.style.backgroundColor = "rgba(0, 177, 156, 0.05)";
  gridElement.style.borderRadius = "4px";

  // Initialize with consistent settings - no dynamic switching
  const nestedGrid = GridStack.addGrid(gridElement, {
    column: 6,
    margin: 3,
    cellHeight: 40, // Smaller cell height
    acceptWidgets: true,
    disableOneColumnMode: true,
    float: true,
    staticGrid: false,
    animate: false,
    draggable: {
      scroll: false,
      handle: ".widget-header",
    },
    resizable: {
      handles: "se,e,s,sw,w",
      autoHide: false,
    },
    removeTimeout: 100,
    itemClass: "grid-stack-item-nested",
    children: [],
  });

  // Store grid reference
  gridElement.gridstack = nestedGrid;
  container.classList.add("has-grid");

  // Simple event handlers without complex mode switching
  nestedGrid.on("added", function (event, items) {
    console.log("Widget added to section:", containerId);
    // Simple compact after adding
    setTimeout(() => nestedGrid.compact(), 50);
  });

  nestedGrid.on("change", function (event, items) {
    console.log("Change detected in section:", containerId);
    setTimeout(() => nestedGrid.compact(), 50);
  });

  // Add drag and drop event handlers for better visual feedback
  nestedGrid.on("dragstart", function (event, el) {
    console.log("Drag started in section:", containerId);
    // Add visual feedback to the nested grid
    gridElement.classList.add("drag-active");
    // Pause auto-resize for this section during drag
    if (window.sectionAutoResize) {
      window.sectionAutoResize.pausedSections.add(containerId);
    }
  });

  nestedGrid.on("dragstop", function (event, el) {
    console.log("Drag stopped in section:", containerId);
    // Remove visual feedback
    gridElement.classList.remove("drag-active");
    // Resume auto-resize after a delay
    setTimeout(() => {
      if (window.sectionAutoResize) {
        window.sectionAutoResize.pausedSections.delete(containerId);
        window.sectionAutoResize.calculateAndSetHeight(containerId);
      }
    }, 200);
  });

  nestedGrid.on("dropped", function (event, previousWidget, newWidget) {
    console.log("Widget dropped in section:", containerId);
    // Remove visual feedback
    gridElement.classList.remove("drag-active");
    // Resume auto-resize after a longer delay to ensure drop is complete
    setTimeout(() => {
      if (window.sectionAutoResize) {
        window.sectionAutoResize.pausedSections.delete(containerId);
        window.sectionAutoResize.calculateAndSetHeight(containerId);
      }
    }, 300);
  });

  // Add drag over/leave events for better visual feedback
  gridElement.addEventListener("dragover", function (e) {
    e.preventDefault();
    gridElement.classList.add("drag-over");
  });

  gridElement.addEventListener("dragleave", function (e) {
    // Only remove if we're truly leaving the grid
    if (!gridElement.contains(e.relatedTarget)) {
      gridElement.classList.remove("drag-over");
    }
  });

  gridElement.addEventListener("drop", function (e) {
    gridElement.classList.remove("drag-over");
  });

  console.log("Section container initialized:", containerId);
  return nestedGrid;
}

// Simplified resize function
function triggerSectionResize(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  const gridElement = container.querySelector(".grid-stack");
  if (!gridElement || !gridElement.gridstack) return;

  const nestedGrid = gridElement.gridstack;

  // Simple compact operation
  nestedGrid.compact();
}

// Function to add a widget to a section
function addWidgetToSection(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  const gridElement = container.querySelector(".grid-stack");
  if (!gridElement || !gridElement.gridstack) {
    console.error("Could not find grid in section container");
    return;
  }

  const nestedGrid = gridElement.gridstack;
  const addedWidget = nestedGrid.addWidget({
    x: 0,
    y: 0,
    w: 3,
    h: 2,
    content: `
      <div class="widget p-2 h-100">
        <div class="widget-header">
          <div class="widget-title">
            <i class="las la-chart-bar"></i>
            Nested Widget
          </div>
          <div class="widget-icons">
            <div class="widget-icon" title="Settings">
              <i class="las la-cog"></i>
            </div>
            <div class="widget-icon" title="Close">
              <i class="las la-times"></i>
            </div>
          </div>
        </div>
        <div class="widget-body d-flex align-items-center justify-content-center">
          <div>Nested Content</div>
        </div>
      </div>
    `,
  });

  return addedWidget;
}

// Test function for large widget
function addLargeHeightWidget(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  const gridElement = container.querySelector(".grid-stack");
  if (!gridElement || !gridElement.gridstack) {
    console.error("Could not find grid in section container");
    return;
  }

  const nestedGrid = gridElement.gridstack;
  const addedWidget = nestedGrid.addWidget({
    x: 0,
    y: 0,
    w: 3,
    h: 4, // Smaller height to prevent excessive space
    content: `
      <div class="widget p-2 h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
        <div class="widget-header">
          <div class="widget-title">
            <i class="las la-expand-arrows-alt"></i>
            Large Widget
          </div>
          <div class="widget-icons">
            <div class="widget-icon" title="Settings">
              <i class="las la-cog"></i>
            </div>
            <div class="widget-icon" title="Close">
              <i class="las la-times"></i>
            </div>
          </div>
        </div>
        <div class="widget-body d-flex flex-column justify-content-center align-items-center">
          <div class="text-center">
            <h5>Large Content</h5>
            <p>This widget has more content</p>
            <div class="mt-2">
              <div class="mb-1">Line 1</div>
              <div class="mb-1">Line 2</div>
              <div class="mb-1">Line 3</div>
              <div class="mb-1">Line 4</div>
            </div>
          </div>
        </div>
      </div>
    `,
  });

  return addedWidget;
}

// Test function to add widgets with different heights
function addTestWidgets(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  const gridElement = container.querySelector(".grid-stack");
  if (!gridElement || !gridElement.gridstack) {
    console.error("Could not find grid in section container");
    return;
  }

  const nestedGrid = gridElement.gridstack;

  // Add small widget
  nestedGrid.addWidget({
    x: 0,
    y: 0,
    w: 2,
    h: 2,
    content: `
      <div class="widget p-2 h-100" style="background: #28a745; color: white;">
        <div class="widget-header">
          <div class="widget-title">
            <i class="las la-cube"></i>
            Small Widget
          </div>
        </div>
        <div class="widget-body d-flex align-items-center justify-content-center">
          <div>Small Content</div>
        </div>
      </div>
    `,
  });

  // Add medium widget
  nestedGrid.addWidget({
    x: 2,
    y: 0,
    w: 2,
    h: 3,
    content: `
      <div class="widget p-2 h-100" style="background: #ffc107; color: black;">
        <div class="widget-header">
          <div class="widget-title">
            <i class="las la-chart-bar"></i>
            Medium Widget
          </div>
        </div>
        <div class="widget-body d-flex flex-column justify-content-center align-items-center">
          <div class="text-center">
            <h6>Medium Content</h6>
            <p>More content here</p>
            <div>Extra line</div>
          </div>
        </div>
      </div>
    `,
  });

  // Add tall widget
  nestedGrid.addWidget({
    x: 4,
    y: 0,
    w: 2,
    h: 5,
    content: `
      <div class="widget p-2 h-100" style="background: #dc3545; color: white;">
        <div class="widget-header">
          <div class="widget-title">
            <i class="las la-chart-line"></i>
            Tall Widget
          </div>
        </div>
        <div class="widget-body d-flex flex-column justify-content-center align-items-center">
          <div class="text-center">
            <h6>Tall Content</h6>
            <p>This widget is taller</p>
            <div class="mt-2">
              <div class="mb-1">Line 1</div>
              <div class="mb-1">Line 2</div>
              <div class="mb-1">Line 3</div>
              <div class="mb-1">Line 4</div>
              <div class="mb-1">Line 5</div>
              <div class="mb-1">Line 6</div>
            </div>
          </div>
        </div>
      </div>
    `,
  });

  console.log(
    "Added test widgets with different heights - section should auto-resize"
  );
}

// Export the functions
window.addSectionContainerWidget = addSectionContainerWidget;
window.addWidgetToSection = addWidgetToSection;
window.initSectionContainer = initSectionContainer;
window.triggerSectionResize = triggerSectionResize;
window.addLargeHeightWidget = addLargeHeightWidget;
window.addTestWidgets = addTestWidgets;
// Export the markup function for sidebar use
window.getSectionContainerWidgetMarkup = getSectionContainerWidgetMarkup;
