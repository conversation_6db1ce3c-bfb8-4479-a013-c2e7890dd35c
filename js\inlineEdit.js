/**
 * Inline Editing Functionality for Widget Titles
 * This script adds the ability to edit widget titles directly by clicking on them.
 */

// Initialize inline editing for all widget titles when the document is ready
document.addEventListener('DOMContentLoaded', function() {
  initInlineEditForWidgetTitles();
});

// Function to initialize inline editing for all widget titles
function initInlineEditForWidgetTitles() {
  // Find all widget headers with titles
  const widgetTitles = document.querySelectorAll('.widget-header > div:first-child');
  
  widgetTitles.forEach(titleElement => {
    makeWidgetTitleEditable(titleElement);
  });
  
  // Set up a mutation observer to detect new widgets added to the DOM
  setupWidgetObserver();
}

// Function to make a specific widget title editable
function makeWidgetTitleEditable(titleElement) {
  // Skip if already initialized or if it's not a title element
  if (titleElement.dataset.editable === 'true' || !titleElement.textContent.trim()) {
    return;
  }
  
  // Mark as editable
  titleElement.dataset.editable = 'true';
  titleElement.title = 'Click to edit title';
  titleElement.classList.add('editable-title');
  
  // Store the icon if present
  const iconElement = titleElement.querySelector('i');
  
  // Add click event to start editing
  titleElement.addEventListener('click', function(e) {
    // Don't trigger edit mode if clicking on the icon
    if (e.target.tagName === 'I') {
      return;
    }
    
    startEditingTitle(titleElement);
  });
}

// Function to start editing a title
function startEditingTitle(titleElement) {
  // Get the current title text (excluding the icon)
  const iconElement = titleElement.querySelector('i');
  const iconHTML = iconElement ? iconElement.outerHTML : '';
  
  // Get the text content, removing the icon's text if present
  let titleText = titleElement.textContent.trim();
  if (iconElement) {
    titleText = titleText.replace(iconElement.textContent.trim(), '').trim();
  }
  
  // Save original content for cancel operation
  titleElement.dataset.originalContent = titleElement.innerHTML;
  
  // Create input field
  const inputField = document.createElement('input');
  inputField.type = 'text';
  inputField.value = titleText;
  inputField.className = 'title-edit-input';
  
  // Replace content with input field, preserving the icon
  titleElement.innerHTML = iconHTML;
  titleElement.appendChild(inputField);
  
  // Focus the input field and select all text
  inputField.focus();
  inputField.select();
  
  // Handle keyboard events
  inputField.addEventListener('keydown', function(e) {
    if (e.key === 'Enter') {
      // Save changes
      saveEditedTitle(titleElement, inputField.value);
      e.preventDefault();
    } else if (e.key === 'Escape') {
      // Cancel editing
      cancelEditingTitle(titleElement);
      e.preventDefault();
    }
  });
  
  // Handle blur event to save changes
  inputField.addEventListener('blur', function() {
    saveEditedTitle(titleElement, inputField.value);
  });
}

// Function to save the edited title
function saveEditedTitle(titleElement, newTitle) {
  // Get the icon if present
  const iconElement = titleElement.querySelector('i');
  const iconHTML = iconElement ? iconElement.outerHTML : '';
  
  // Update the title with the new text, preserving the icon
  if (newTitle.trim()) {
    titleElement.innerHTML = iconHTML + ' ' + newTitle.trim();
  } else {
    // If empty, restore original content
    cancelEditingTitle(titleElement);
  }
}

// Function to cancel editing and restore original content
function cancelEditingTitle(titleElement) {
  if (titleElement.dataset.originalContent) {
    titleElement.innerHTML = titleElement.dataset.originalContent;
    delete titleElement.dataset.originalContent;
  }
}

// Set up a mutation observer to detect new widgets added to the DOM
function setupWidgetObserver() {
  // Create an observer instance
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      // Check if nodes were added
      if (mutation.addedNodes.length) {
        mutation.addedNodes.forEach(function(node) {
          // Check if the added node is an element
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Look for widget headers within the added node
            const widgetTitles = node.querySelectorAll ? 
              node.querySelectorAll('.widget-header > div:first-child') : [];
            
            widgetTitles.forEach(function(titleElement) {
              makeWidgetTitleEditable(titleElement);
            });
          }
        });
      }
    });
  });
  
  // Start observing the document with the configured parameters
  observer.observe(document.body, { childList: true, subtree: true });
}

// Make the functions available globally
window.initInlineEditForWidgetTitles = initInlineEditForWidgetTitles;
window.makeWidgetTitleEditable = makeWidgetTitleEditable;
