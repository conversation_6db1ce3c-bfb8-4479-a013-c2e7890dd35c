/**
 * Tab System for Section Containers
 * 
 * This file implements the Horizontal Tab Strip with Icon Indicators
 * for managing tabs within section containers in the dashboard.
 * 
 * Features:
 * - Horizontal tab strip with icons and labels
 * - Tab switching and management
 * - Drag and drop tab reordering
 * - Tab CRUD operations
 * - Responsive design
 * - Accessibility support
 * 
 * <AUTHOR> Assistant
 * @version 1.0
 * @date August 18, 2025
 */

// ============================================================================
// TAB SYSTEM CLASS
// ============================================================================

class TabSystem {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            defaultTabName: 'New Tab',
            maxTabs: 10,
            allowReorder: true,
            showIcons: true,
            tabHeight: 40,
            ...options
        };
        
        this.tabs = [];
        this.activeTabId = null;
        this.tabCounter = 0;
        this.isInitialized = false;
        
        this.init();
    }
    
    /**
     * Initialize the tab system
     */
    init() {
        if (this.isInitialized) return;
        
        this.createTabStrip();
        this.createDefaultTab();
        this.bindEvents();
        this.isInitialized = true;
        
        console.log('Tab system initialized for container:', this.container);
    }
    
    /**
     * Create the tab strip container
     */
    createTabStrip() {
        // Create tab strip wrapper
        this.tabStrip = document.createElement('div');
        this.tabStrip.className = 'tab-strip';
        this.tabStrip.setAttribute('role', 'tablist');
        this.tabStrip.setAttribute('aria-label', 'Section tabs');
        
        // Create tab list container
        this.tabList = document.createElement('div');
        this.tabList.className = 'tab-list';
        
        // Create add tab button
        this.addTabBtn = document.createElement('button');
        this.addTabBtn.className = 'add-tab-btn';
        this.addTabBtn.innerHTML = '<i class="las la-plus"></i>';
        this.addTabBtn.setAttribute('title', 'Add new tab');
        this.addTabBtn.setAttribute('aria-label', 'Add new tab');
        
        // Create tab content container
        this.tabContent = document.createElement('div');
        this.tabContent.className = 'tab-content';
        this.tabContent.setAttribute('role', 'tabpanel');
        
        // Assemble the structure
        this.tabStrip.appendChild(this.tabList);
        this.tabStrip.appendChild(this.addTabBtn);
        
        // Insert tab strip before the existing content
        this.container.insertBefore(this.tabStrip, this.container.firstChild);
        this.container.appendChild(this.tabContent);
        
        // Add CSS classes to container
        this.container.classList.add('has-tabs');
    }
    
    /**
     * Create the default tab
     */
    createDefaultTab() {
        this.addTab('Main', 'las la-home');
    }
    
    /**
     * Add a new tab
     * @param {string} name - Tab name
     * @param {string} icon - Line Awesome icon class
     * @param {string} content - Tab content HTML
     * @returns {string} - Tab ID
     */
    addTab(name = null, icon = 'las la-file', content = null) {
        if (this.tabs.length >= this.options.maxTabs) {
            this.showNotification('Maximum number of tabs reached', 'warning');
            return null;
        }
        
        const tabId = `tab_${++this.tabCounter}_${Date.now()}`;
        const tabName = name || `${this.options.defaultTabName} ${this.tabCounter}`;
        
        // Create tab object
        const tab = {
            id: tabId,
            name: tabName,
            icon: icon,
            content: content || this.createDefaultTabContent(tabName),
            isActive: false,
            order: this.tabs.length
        };
        
        // Add to tabs array
        this.tabs.push(tab);
        
        // Create tab element
        const tabElement = this.createTabElement(tab);
        this.tabList.appendChild(tabElement);
        
        // Create tab content element
        const contentElement = this.createTabContentElement(tab);
        this.tabContent.appendChild(contentElement);
        
        // If this is the first tab, make it active
        if (this.tabs.length === 1) {
            this.activateTab(tabId);
        }
        
        // Update tab strip state
        this.updateTabStripState();
        
        console.log('Tab added:', tab);
        return tabId;
    }
    
    /**
     * Create a tab element
     * @param {Object} tab - Tab object
     * @returns {HTMLElement} - Tab element
     */
    createTabElement(tab) {
        const tabElement = document.createElement('div');
        tabElement.className = 'tab-item';
        tabElement.setAttribute('data-tab-id', tab.id);
        tabElement.setAttribute('role', 'tab');
        tabElement.setAttribute('aria-selected', 'false');
        tabElement.setAttribute('aria-controls', `content_${tab.id}`);
        
        // Tab content
        tabElement.innerHTML = `
            ${this.options.showIcons ? `<i class="${tab.icon}"></i>` : ''}
            <span class="tab-label">${tab.name}</span>
            <button class="remove-tab-btn" title="Remove tab" aria-label="Remove tab">
                <i class="las la-times"></i>
            </button>
        `;
        
        // Add event listeners
        this.bindTabEvents(tabElement, tab);
        
        return tabElement;
    }
    
    /**
     * Create tab content element
     * @param {Object} tab - Tab object
     * @returns {HTMLElement} - Content element
     */
    createTabContentElement(tab) {
        const contentElement = document.createElement('div');
        contentElement.className = 'tab-panel';
        contentElement.id = `content_${tab.id}`;
        contentElement.setAttribute('data-tab-id', tab.id);
        contentElement.setAttribute('role', 'tabpanel');
        contentElement.setAttribute('aria-labelledby', tab.id);
        contentElement.style.display = 'none';
        
        // Set content
        if (typeof tab.content === 'string') {
            contentElement.innerHTML = tab.content;
        } else if (tab.content instanceof HTMLElement) {
            contentElement.appendChild(tab.content);
        } else {
            contentElement.innerHTML = this.createDefaultTabContent(tab.name);
        }
        
        return contentElement;
    }
    
    /**
     * Create default tab content
     * @param {string} tabName - Tab name
     * @returns {string} - Default HTML content
     */
    createDefaultTabContent(tabName) {
        return `
            <div class="default-tab-content">
                <div class="content-header">
                    <h3>${tabName}</h3>
                    <p>This is a new tab. Add widgets and content here.</p>
                </div>
                <div class="content-body">
                    <div class="empty-state">
                        <i class="las la-plus-circle"></i>
                        <p>Click the + button to add widgets to this tab</p>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Bind events to a tab element
     * @param {HTMLElement} tabElement - Tab element
     * @param {Object} tab - Tab object
     */
    bindTabEvents(tabElement, tab) {
        // Click to activate tab
        tabElement.addEventListener('click', (e) => {
            if (e.target.closest('.remove-tab-btn')) return;
            this.activateTab(tab.id);
        });
        
        // Remove tab button
        const removeBtn = tabElement.querySelector('.remove-tab-btn');
        removeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.removeTab(tab.id);
        });
        
        // Double click to rename
        tabElement.addEventListener('dblclick', (e) => {
            if (e.target.closest('.remove-tab-btn')) return;
            this.startTabRename(tab.id);
        });
        
        // Keyboard navigation
        tabElement.addEventListener('keydown', (e) => {
            this.handleTabKeyboard(e, tab.id);
        });
    }
    
    /**
     * Activate a tab
     * @param {string} tabId - Tab ID to activate
     */
    activateTab(tabId) {
        const tab = this.tabs.find(t => t.id === tabId);
        if (!tab) return;
        
        // Deactivate current active tab
        if (this.activeTabId) {
            const currentTab = this.tabs.find(t => t.id === this.activeTabId);
            if (currentTab) {
                currentTab.isActive = false;
                this.updateTabElement(currentTab.id, { isActive: false });
                this.hideTabContent(currentTab.id);
            }
        }
        
        // Activate new tab
        tab.isActive = true;
        this.activeTabId = tabId;
        this.updateTabElement(tabId, { isActive: true });
        this.showTabContent(tabId);
        
        // Update ARIA attributes
        this.updateAriaAttributes();
        
        // Trigger custom event
        this.container.dispatchEvent(new CustomEvent('tabChanged', {
            detail: { tabId, tab }
        }));
        
        console.log('Tab activated:', tabId);
    }
    
    /**
     * Remove a tab
     * @param {string} tabId - Tab ID to remove
     */
    removeTab(tabId) {
        const tabIndex = this.tabs.findIndex(t => t.id === tabId);
        if (tabIndex === -1) return;
        
        // Don't remove the last tab
        if (this.tabs.length === 1) {
            this.showNotification('Cannot remove the last tab', 'warning');
            return;
        }
        
        // Remove tab from array
        const removedTab = this.tabs.splice(tabIndex, 1)[0];
        
        // Remove tab element
        const tabElement = this.tabList.querySelector(`[data-tab-id="${tabId}"]`);
        if (tabElement) tabElement.remove();
        
        // Remove content element
        const contentElement = this.tabContent.querySelector(`[data-tab-id="${tabId}"]`);
        if (contentElement) contentElement.remove();
        
        // If removed tab was active, activate another tab
        if (removedTab.isActive) {
            const nextTab = this.tabs[Math.min(tabIndex, this.tabs.length - 1)];
            if (nextTab) {
                this.activateTab(nextTab.id);
            }
        }
        
        // Update tab strip state
        this.updateTabStripState();
        
        // Trigger custom event
        this.container.dispatchEvent(new CustomEvent('tabRemoved', {
            detail: { tabId, removedTab }
        }));
        
        console.log('Tab removed:', tabId);
    }
    
    /**
     * Start tab rename process
     * @param {string} tabId - Tab ID to rename
     */
    startTabRename(tabId) {
        const tab = this.tabs.find(t => t.id === tabId);
        if (!tab) return;
        
        const tabElement = this.tabList.querySelector(`[data-tab-id="${tabId}"]`);
        const labelElement = tabElement.querySelector('.tab-label');
        
        // Create input element
        const input = document.createElement('input');
        input.type = 'text';
        input.className = 'tab-rename-input';
        input.value = tab.name;
        input.setAttribute('aria-label', 'Edit tab name');
        
        // Replace label with input
        labelElement.style.display = 'none';
        labelElement.parentNode.insertBefore(input, labelElement);
        
        // Focus and select input
        input.focus();
        input.select();
        
        // Handle input events
        const finishRename = () => {
            const newName = input.value.trim();
            if (newName && newName !== tab.name) {
                tab.name = newName;
                labelElement.textContent = newName;
                
                // Trigger custom event
                this.container.dispatchEvent(new CustomEvent('tabRenamed', {
                    detail: { tabId, oldName: tab.name, newName }
                }));
            }
            
            // Restore label
            labelElement.style.display = 'inline';
            input.remove();
        };
        
        input.addEventListener('blur', finishRename);
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                finishRename();
            } else if (e.key === 'Escape') {
                labelElement.style.display = 'inline';
                input.remove();
            }
        });
    }
    
    /**
     * Update tab element appearance
     * @param {string} tabId - Tab ID
     * @param {Object} updates - Updates to apply
     */
    updateTabElement(tabId, updates) {
        const tabElement = this.tabList.querySelector(`[data-tab-id="${tabId}"]`);
        if (!tabElement) return;
        
        if (updates.isActive !== undefined) {
            tabElement.classList.toggle('active', updates.isActive);
            tabElement.setAttribute('aria-selected', updates.isActive);
        }
    }
    
    /**
     * Show tab content
     * @param {string} tabId - Tab ID
     */
    showTabContent(tabId) {
        const contentElement = this.tabContent.querySelector(`[data-tab-id="${tabId}"]`);
        if (contentElement) {
            contentElement.style.display = 'block';
        }
    }
    
    /**
     * Hide tab content
     * @param {string} tabId - Tab ID
     */
    hideTabContent(tabId) {
        const contentElement = this.tabContent.querySelector(`[data-tab-id="${tabId}"]`);
        if (contentElement) {
            contentElement.style.display = 'none';
        }
    }
    
    /**
     * Update ARIA attributes
     */
    updateAriaAttributes() {
        this.tabs.forEach(tab => {
            const tabElement = this.tabList.querySelector(`[data-tab-id="${tab.id}"]`);
            const contentElement = this.tabContent.querySelector(`[data-tab-id="${tab.id}"]`);
            
            if (tabElement && contentElement) {
                tabElement.setAttribute('aria-selected', tab.isActive);
                contentElement.setAttribute('aria-hidden', !tab.isActive);
            }
        });
    }
    
    /**
     * Handle tab keyboard navigation
     * @param {KeyboardEvent} e - Keyboard event
     * @param {string} tabId - Tab ID
     */
    handleTabKeyboard(e, tabId) {
        const tabIndex = this.tabs.findIndex(t => t.id === tabId);
        
        switch (e.key) {
            case 'Enter':
            case ' ':
                e.preventDefault();
                this.activateTab(tabId);
                break;
            case 'ArrowLeft':
                e.preventDefault();
                const prevTab = this.tabs[tabIndex - 1] || this.tabs[this.tabs.length - 1];
                if (prevTab) this.activateTab(prevTab.id);
                break;
            case 'ArrowRight':
                e.preventDefault();
                const nextTab = this.tabs[tabIndex + 1] || this.tabs[0];
                if (nextTab) this.activateTab(nextTab.id);
                break;
            case 'Home':
                e.preventDefault();
                if (this.tabs[0]) this.activateTab(this.tabs[0].id);
                break;
            case 'End':
                e.preventDefault();
                if (this.tabs[this.tabs.length - 1]) this.activateTab(this.tabs[this.tabs.length - 1].id);
                break;
        }
    }
    
    /**
     * Update tab strip state
     */
    updateTabStripState() {
        // Update add button state
        this.addTabBtn.disabled = this.tabs.length >= this.options.maxTabs;
        
        // Update tab count display
        const tabCount = this.tabs.length;
        this.container.setAttribute('data-tab-count', tabCount);
        
        // Show/hide tab strip based on tab count
        this.tabStrip.style.display = tabCount > 1 ? 'flex' : 'none';
    }
    
    /**
     * Bind global events
     */
    bindEvents() {
        // Add tab button
        this.addTabBtn.addEventListener('click', () => {
            this.addTab();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 't' && this.container.contains(document.activeElement)) {
                e.preventDefault();
                this.addTab();
            }
        });
    }
    
    /**
     * Show notification
     * @param {string} message - Notification message
     * @param {string} type - Notification type (info, warning, error)
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `tab-notification tab-notification-${type}`;
        notification.textContent = message;
        
        // Add to container
        this.container.appendChild(notification);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }
    
    /**
     * Get tab information
     * @param {string} tabId - Tab ID
     * @returns {Object|null} - Tab object or null
     */
    getTab(tabId) {
        return this.tabs.find(t => t.id === tabId) || null;
    }
    
    /**
     * Get all tabs
     * @returns {Array} - Array of tab objects
     */
    getAllTabs() {
        return [...this.tabs];
    }
    
    /**
     * Get active tab
     * @returns {Object|null} - Active tab object or null
     */
    getActiveTab() {
        return this.tabs.find(t => t.isActive) || null;
    }
    
    /**
     * Set tab content
     * @param {string} tabId - Tab ID
     * @param {string|HTMLElement} content - New content
     */
    setTabContent(tabId, content) {
        const tab = this.tabs.find(t => t.id === tabId);
        if (!tab) return;
        
        tab.content = content;
        
        const contentElement = this.tabContent.querySelector(`[data-tab-id="${tabId}"]`);
        if (contentElement) {
            if (typeof content === 'string') {
                contentElement.innerHTML = content;
            } else if (content instanceof HTMLElement) {
                contentElement.innerHTML = '';
                contentElement.appendChild(content);
            }
        }
    }
    
    /**
     * Destroy the tab system
     */
    destroy() {
        // Remove event listeners
        this.addTabBtn.removeEventListener('click', this.addTab);
        
        // Remove DOM elements
        if (this.tabStrip && this.tabStrip.parentNode) {
            this.tabStrip.remove();
        }
        if (this.tabContent && this.tabContent.parentNode) {
            this.tabContent.remove();
        }
        
        // Clear arrays
        this.tabs = [];
        this.activeTabId = null;
        this.isInitialized = false;
        
        // Remove container classes
        this.container.classList.remove('has-tabs');
        
        console.log('Tab system destroyed for container:', this.container);
    }
}

// ============================================================================
// GLOBAL FUNCTIONS
// ============================================================================

/**
 * Initialize tab system for a section container
 * @param {HTMLElement} container - Section container element
 * @param {Object} options - Tab system options
 * @returns {TabSystem} - Tab system instance
 */
function initTabSystem(container, options = {}) {
    if (!container) {
        console.error('Container element is required');
        return null;
    }
    
    // Check if tab system already exists
    if (container.tabSystem) {
        console.warn('Tab system already exists for this container');
        return container.tabSystem;
    }
    
    // Create and store tab system instance
    container.tabSystem = new TabSystem(container, options);
    return container.tabSystem;
}

/**
 * Get tab system instance for a container
 * @param {HTMLElement} container - Section container element
 * @returns {TabSystem|null} - Tab system instance or null
 */
function getTabSystem(container) {
    return container.tabSystem || null;
}

/**
 * Destroy tab system for a container
 * @param {HTMLElement} container - Section container element
 */
function destroyTabSystem(container) {
    if (container.tabSystem) {
        container.tabSystem.destroy();
        container.tabSystem = null;
    }
}

// ============================================================================
// EXPORT FOR MODULE SYSTEMS
// ============================================================================

if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TabSystem, initTabSystem, getTabSystem, destroyTabSystem };
}

// ============================================================================
// AUTO-INITIALIZATION
// ============================================================================

// Auto-initialize tab systems for existing section containers
document.addEventListener('DOMContentLoaded', () => {
    // Find all section containers that don't have tab systems
    const sectionContainers = document.querySelectorAll('.section-container-widget:not(.has-tabs)');
    
    sectionContainers.forEach(container => {
        // Only initialize if container has nested grid
        const nestedGrid = container.querySelector('.nested-grid-container');
        if (nestedGrid) {
            initTabSystem(container, {
                defaultTabName: 'Tab',
                maxTabs: 8,
                allowReorder: true,
                showIcons: true
            });
        }
    });
    
    console.log('Auto-initialized tab systems for', sectionContainers.length, 'section containers');
});

// ============================================================================
// CSS STYLES (will be injected)
// ============================================================================

const tabSystemStyles = `
<style id="tab-system-styles">
.tab-strip {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    padding: 8px 12px 0;
    margin-bottom: 0;
    min-height: 48px;
    position: relative;
    z-index: 10;
}

.tab-list {
    display: flex;
    flex: 1;
    gap: 4px;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: #adb5bd #f8f9fa;
}

.tab-list::-webkit-scrollbar {
    height: 4px;
}

.tab-list::-webkit-scrollbar-track {
    background: #f8f9fa;
}

.tab-list::-webkit-scrollbar-thumb {
    background: #adb5bd;
    border-radius: 2px;
}

.tab-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #e9ecef;
    border: 1px solid #dee2e6;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
    max-width: 200px;
    position: relative;
    user-select: none;
}

.tab-item:hover {
    background: #dee2e6;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tab-item.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
    z-index: 2;
}

.tab-item.active:hover {
    background: #0056b3;
}

.tab-item i {
    font-size: 14px;
    flex-shrink: 0;
}

.tab-label {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    font-weight: 500;
}

.remove-tab-btn {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 2px;
    border-radius: 4px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
    flex-shrink: 0;
}

.remove-tab-btn:hover {
    opacity: 1;
    background: rgba(255,255,255,0.2);
}

.add-tab-btn {
    background: #28a745;
    border: 1px solid #28a745;
    color: white;
    border-radius: 6px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
    margin-left: 8px;
}

.add-tab-btn:hover {
    background: #218838;
    border-color: #1e7e34;
    transform: translateY(-1px);
}

.add-tab-btn:disabled {
    background: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.tab-content {
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 8px 8px;
    background: white;
    min-height: 200px;
    position: relative;
}

.tab-panel {
    padding: 20px;
    display: none;
}

.tab-panel:first-child {
    display: block;
}

.default-tab-content {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.default-tab-content h3 {
    margin-bottom: 16px;
    color: #495057;
}

.default-tab-content .empty-state {
    margin-top: 20px;
}

.default-tab-content .empty-state i {
    font-size: 48px;
    color: #adb5bd;
    margin-bottom: 16px;
}

.tab-rename-input {
    border: 2px solid #007bff;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 14px;
    font-weight: 500;
    background: white;
    color: #495057;
    width: 100%;
    outline: none;
}

.tab-notification {
    position: absolute;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.tab-notification-info {
    background: #17a2b8;
}

.tab-notification-warning {
    background: #ffc107;
    color: #212529;
}

.tab-notification-error {
    background: #dc3545;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .tab-strip {
        padding: 6px 8px 0;
        min-height: 40px;
    }
    
    .tab-item {
        padding: 6px 12px;
        min-width: 100px;
        max-width: 150px;
    }
    
    .tab-item i {
        font-size: 12px;
    }
    
    .tab-label {
        font-size: 12px;
    }
    
    .add-tab-btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .tab-content {
        min-height: 150px;
    }
    
    .tab-panel {
        padding: 16px;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .tab-item {
        border-width: 2px;
    }
    
    .tab-item.active {
        border-width: 3px;
    }
    
    .add-tab-btn {
        border-width: 2px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .tab-item,
    .add-tab-btn,
    .tab-notification {
        transition: none;
        animation: none;
    }
    
    .tab-item:hover {
        transform: none;
    }
    
    .add-tab-btn:hover {
        transform: none;
    }
}
</style>
`;

// Inject styles when script loads
if (document.head) {
    document.head.insertAdjacentHTML('beforeend', tabSystemStyles);
} else {
    document.addEventListener('DOMContentLoaded', () => {
        document.head.insertAdjacentHTML('beforeend', tabSystemStyles);
    });
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Create a section container with tabs
 * @param {Object} options - Section options
 * @returns {HTMLElement} - Section container element
 */
function createSectionWithTabs(options = {}) {
    const {
        title = 'New Section',
        tabCount = 3,
        tabNames = ['Main', 'Data', 'Charts'],
        tabIcons = ['las la-home', 'las la-table', 'las la-chart-line']
    } = options;
    
    // Create section container
    const sectionContainer = document.createElement('div');
    sectionContainer.className = 'section-container-widget p-2';
    sectionContainer.style.cssText = 'height: 100%; overflow: hidden;';
    
    // Create section header
    const header = document.createElement('div');
    header.className = 'widget-header mb-2 fw-bold d-flex justify-content-between align-items-center';
    header.innerHTML = `
        <div>
            <i class="las la-layer-group"></i> ${title}
        </div>
        <div>
            <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                <i class="las la-times"></i>
            </button>
        </div>
    `;
    
    // Create nested grid container
    const nestedGridContainer = document.createElement('div');
    nestedGridContainer.className = 'nested-grid-container';
    nestedGridContainer.style.cssText = 'height: calc(100% - 40px); overflow: hidden;';
    
    // Assemble section
    sectionContainer.appendChild(header);
    sectionContainer.appendChild(nestedGridContainer);
    
    // Initialize tab system
    const tabSystem = initTabSystem(sectionContainer, {
        defaultTabName: 'Tab',
        maxTabs: 8,
        allowReorder: true,
        showIcons: true
    });
    
    // Add tabs
    for (let i = 0; i < tabCount; i++) {
        const tabName = tabNames[i] || `Tab ${i + 1}`;
        const tabIcon = tabIcons[i] || 'las la-file';
        tabSystem.addTab(tabName, tabIcon);
    }
    
    return sectionContainer;
}

/**
 * Add tab system to existing section container
 * @param {HTMLElement} sectionContainer - Existing section container
 * @param {Object} options - Tab system options
 * @returns {TabSystem} - Tab system instance
 */
function addTabSystemToSection(sectionContainer, options = {}) {
    if (!sectionContainer || sectionContainer.tabSystem) {
        console.error('Invalid section container or tab system already exists');
        return null;
    }
    
    return initTabSystem(sectionContainer, options);
}

// ============================================================================
// GLOBAL EXPOSURE
// ============================================================================

// Make functions globally available
window.TabSystem = TabSystem;
window.initTabSystem = initTabSystem;
window.getTabSystem = getTabSystem;
window.destroyTabSystem = destroyTabSystem;
window.createSectionWithTabs = createSectionWithTabs;
window.addTabSystemToSection = addTabSystemToSection;

console.log('Tab System loaded successfully! Available functions:');
console.log('- initTabSystem(container, options)');
console.log('- createSectionWithTabs(options)');
console.log('- addTabSystemToSection(container, options)');
console.log('- getTabSystem(container)');
console.log('- destroyTabSystem(container)');
