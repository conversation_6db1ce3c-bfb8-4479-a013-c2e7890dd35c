@charset "utf-8"; /* CSS Document */

body {
    /*  height:100%;*/
    font-family: "Montserrat", serif !important;
    font-size: 12px;
    height: 100%;
    color: #231f20 !important;
}
.navbar-light .navbar-nav .nav-link {
    color: #231f20;
}
.form-control {
    height: auto;
}
#tsc_nav_1 .navbar .odi .nav-link:focus,
.navbar .odi .nav-link:hover,
.navbar .odi:hover {
    background: none;
}

/* .navbar .nav-item:hover .nav-link{color:#fff!important} */
#tsc_nav_1 .dropdown-item {
    border-left: 3px solid transparent;
    border-bottom: 1px solid #f9f9f9;
}
#tsc_nav_1 .nav-link {
    padding: 0.7rem;
    font-size: 0.75rem;
}
#tsc_nav_1 .dropdown-item:hover {
    color: #231f20;
    border-left: 3px solid #3bcd3f;
}
.themeBorderBottom {
    position: fixed; /* Fixed position - sit on top of the page */
    top: 0;
    height: auto;
    background: #fff;
    z-index: 999999;
    width: 100%;
    max-height: 86px;
}
.marginTop86 {
    margin-top: 86px; /* min-height:484px */
}
.navbar-toggler {
    border-radius: 0;

    cursor: pointer;
    padding: 0.2rem 0.2rem 0.2rem;
    font-size: 1.25rem;
    line-height: 1;
    background-color: transparent;
    border: 1px solid transparent;
}

.dropdown-menu,
.form-control {
    font-size: inherit;
}

.color-preview {
    height: 50px;
    font-size: 14px;
}

.navbar-brand img {
    width: 90px;
}

.colorWhite {
    color: #fff;
}

.color-preview-active,
.color-preview-default {
    height: 50px;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.bd-sidebar {
    overflow-y: auto;
    position: sticky;
    z-index: 1000;
    top: 5rem;
    height: calc(100vh - 5rem);
    border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.panel-heading-a {
    text-decoration: none;
    color: #231f20;
    content: "\f0d7";
}

.panel-heading-a.collapsed {
    text-decoration: none;
    color: #00b19c;
}

.panel-heading-a:hover {
    text-decoration: none;
    color: #231f20;
}

.listStyle {
    list-style: none;
    border-left: 1px dotted grey;
    margin-top: 11px;
    margin-left: 20px;
    padding-left: 14px;
    color: #00b19c;
    font-size: 15px;
}

.listStyle:hover {
    list-style: none;
    margin-top: 11px;
    margin-left: 20px;
    padding-left: 14px;
    color: #00b19c;
}

.navdp {
    width: 110px;
    height: 50px;
}

.panel-heading-a:before {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    content: "\f107";
    color: #abaeb3;
    padding-left: 5px;
    padding-right: 3px;
}

.panel-heading-a.collapsed:before {
    content: "\f106";
}

/*buttons*/
.bd-example {
    position: relative;
    margin: 1rem -15px 0;
    border: solid #f7f7f9;
    border-width: 0.2rem;
    padding: 1.5rem;
    margin-right: 0;
    margin-left: 0;
    /*  width: 1000px;*/
}

.bd-clipboard {
    position: relative;
    display: none;
    float: right;
}

.btn-clipboard {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    z-index: 10;
    display: block;
    padding: 0.25rem 0.5rem;
    padding: 0.25rem 0.5rem;
    font-size: 75%;
    color: #818a91;
    cursor: pointer;
    background-color: transparent;
    border: 0;
    border-radius: 0.25rem;
}

.highlight {
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f7f7f9;
    /*  width: 1000px;*/
}

.nt {
    color: #2f6f9f;
}

.na {
    color: #4f9fcf;
}

.s {
    color: #d44950;
}

/*typography*/
table {
    width: 100%;
    max-width: 100%;
}

.marginRight100 {
    margin-right: 100px;
}

.themeBorderBottom {
    border-bottom: 3px solid #007365;
    background: white;
}

.s1 {
    color: #c30;
}

.kd {
    color: #069;
}

.style_nav {
    color: #231f20 !important;
    text-decoration: underline;
}

.bd-example-border-utils [class^="border"] {
    display: inline-block;
    width: 5rem;
    height: 5rem;
    margin: 0.25rem;
    background-color: #f5f5f5;
}

.nc {
    color: #0a8;
}

.nl {
    color: #99f;
}

.m {
    color: #f60;
}

.w3-table-all {
    border: 1px solid #ccc;
}

.w3-table-all tr:nth-child(even) {
    background-color: #f1f1f1;
}

.w3-bordered tr,
.w3-table-all tr {
    border-bottom: 1px solid #ddd;
}

.w3-table-all tr:nth-child(odd) {
    background-color: #fff;
}
#tsc_nav_1 .nav-link {
    display: block;
    padding: 0.4rem 1rem;
}
/* .navbar-collapse.show ul.navbar-nav{text-align:right} */

#tsc_nav_1 .navbar-nav {
    align-items: center;
}
#tsc_nav_1 {
    padding: 0 1rem;
    /* margin-bottom: 3px; */
}

#tsc_nav_1 .navbar-collapse.show .inp {
    display: block !important ;
}

#tsc_nav_1 .navbar-collapse.show .input-group {
    border: 1px solid #dee2e6 !important;
}
.navbar .nav-item:hover .nav-link {
    color: white;
}
.navbar ul.nohover .nav-item:focus,
.navbar ul.nohover .nav-item:hover,
.navbar ul.nohover .nav-link:focus,
.navbar ul.nohover .nav-link:hover,
.navbar ul.nohover:hover,
.navbar ul.nohover .nav-item:hover .nav-link {
    background: none !important;
    color: #231f20 !important;
}

/*multiselect*/
::-webkit-input-placeholder {
    color: #bbd6dc !important;
    font-weight: 300;
    font-family: "Montserrat", serif;
}

/* .navbar-nav .input-group{    justify-content: flex-end;} */
.footer {
    font-size: 12px;
    margin-top: 20px;
    background: #02104f;
    border-color: #02104f;
}
.navbar-nav.nohover {
    height: 30px;
}
#tsc_nav_1 ul.nohover a.nav-link {
    padding: 0.3rem 0.3rem;
}
#tsc_nav_1 ul.nohover li {
    padding: 0.3rem 0.3rem;
}
.sergroup .la-search,
.serbtn .la-search {
    -ms-transform: rotate(269deg);
    /* IE 9 */
    -webkit-transform: rotate(269deg);
    /* Safari 3-8 */
    transform: rotate(269deg);
    font-size: 16px !important;
}

.b-0 {
    border: none !important;
}

.comm a:after {
    border: none;
}

.nav-tabs .nav-link {
    padding: 0.5rem 1.62rem !important;
}

.usernav {
    margin-top: -2px;
}

.nav-tabs .nav-item.active {
    margin-bottom: -1px;
    background: #fff;
}

.nav-tabs .nav-link {
    border-bottom: 0px solid #dee2e6;
}

h1 {
    font-size: 18px;
}

.pageheading .select2-container--default .select2-selection--single {
    border: none;
}

.pageheading
    .select2-container
    .select2-selection--single
    .select2-selection__rendered {
    display: block;
    padding-left: 5px;
    padding-right: 5px;
}

.catContainer {
    /* margin: 10px 10px 0px 0; */
    transition: all 200ms ease-in;
    transform: scale(1);
    float: left;
    background-color: #fff;
}
.catwrap {
    height: 306px;
    width: 100%;
    display: inline-block;
    top: 0;
}
.catwrap {
    position: relative;
    min-height: 235px;
}
.catLand .col-sm-3 {
    padding-right: 0;
}
.catwrap:hover .catContainer {
    z-index: 2;
    -webkit-transition: all 200ms ease-in;
    -webkit-transform: scale(1.1);
    -ms-transition: all 200ms ease-in;
    -ms-transform: scale(1.1);
    -moz-transition: all 200ms ease-in;
    -moz-transform: scale(1.1);
    transition: all 200ms ease-in;
    transform: scale(1.1);
    background-color: #fff !important;
    position: absolute;
    height: auto;
    z-index: 99;
}
/* .catContainer:hover {
    z-index: 2;
    -webkit-transition: all 200ms ease-in;
    -webkit-transform: scale(1.20);
    -ms-transition: all 200ms ease-in;
    -ms-transform: scale(1.20);
    -moz-transition: all 200ms ease-in;
    -moz-transform: scale(1.20);
    transition: all 200ms ease-in;
    transform: scale(1.20);
    background-color: #fff !important;
    position: absolute;
    height: auto;
}
 */
.seemore {
    cursor: pointer;
    display: block;
    z-index: 9;
    position: relative;
    padding: 4px 0;
}
.icons a.text-primary4:hover {
    color: #00b19c !important;
}
.catContainer:hover .icons {
    display: block !important;
}

.catContainer:hover .catdesc {
    transform: scale(0.92);
    padding-bottom: 0;
    padding-top: 0;
    padding: 0;
}

.catContainer .icons .las {
    font-size: 20px;
}

.catimageSection img {
    max-width: 100%;
    height: 128px;
    width: 100%;
    /* margin-left: -48px; */
}

.catdesc {
    padding: 10px;
    position: relative;
    padding-top: 0px;
}

.catdesc p {
    display: block;
    /* Fallback for non-webkit */
    display: -webkit-box;
    max-width: 400px;
    /* Fallback for non-webkit */
    margin: 0 auto;
    font-size: 12px;
    /* line-height: 19px; */
    text-align: left;
    overflow: visible;
}

.catdesc p.ellipsis {
    height: 78px !important;
    -webkit-line-clamp: 5;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 200ms ease-in;
    -webkit-transition: all 200ms ease-in;
}

.catheading {
    /* font-weight: 500; */
    font-size: 14px;
    min-height: 50px;
}

.size20 {
    font-size: 20px;
}

.calendernews {
    width: 48px;
    text-align: center;
    border-color: #3bcd3f !important;
    display: inline-block;
    float: left;
    margin: 0 4px;
}

.date {
    color: #3bcd3f;
    font-size: 18px;
    line-height: 28px;
}

.year {
    color: #fff;
    background: #00b19c;
    display: block;
    padding: 4px 0 0 0;
    font-size: 10px;
}
.News {
    width: calc(100% - 60px);
    display: inline-block;
    float: left;
}
.newsDesc {
    margin-left: 20px;

    position: relative;

    min-height: 42px;

    display: inline-block;
    padding-bottom: 15px;
}

.newsDesc:before {
    content: "";
    position: absolute;
    background: #a5acb1;
    width: 1px;

    left: -12px;
    top: 12px;
    z-index: 3;
    height: -webkit-fill-available;
    height: -moz-available;
    height: stretch;
}

.newsDesc:after {
    content: "";
    position: absolute;
    background: #a5acb1;
    width: 9px;
    height: 9px;
    left: -16px;
    top: 4px;
}
.newsGroup {
    margin-bottom: 15px;
    float: left;
}
.News {
    display: inline-block;
}

.select2-dropdown {
    min-width: 140px;
}

.newsGroup .newsDesc:last-child:before {
    background: none;
}

.card {
    width: 100%;
    box-shadow: 2px 2px 3px 0 rgba(130, 130, 130, 0.15),
        0 0px 14px 0 rgba(0, 0, 0, 0.05);
    /* box-shadow: 0 2px 3px 0 rgba(130, 130, 130, 0.05),
        0 0px 14px 0 rgba(0, 0, 0, 0.05); */
}
.card_crousal {
    width: 100%;
    box-shadow: 0 2px 3px 0 rgba(130, 130, 130, 0.1),
        0 0px 14px 0 rgba(0, 0, 0, 0.1);
}

.breadcrumb {
    padding-bottom: 10px;
    margin-bottom: 0;
}

.size10 {
    font-size: 10px;
}

.size11 {
    font-size: 11px !important;
}

.size12 {
    font-size: 12px;
}

.size14 {
    font-size: 14px !important;
}

.size16 {
    font-size: 16px;
}

.size18 {
    font-size: 18px;
}

.size20 {
    font-size: 20px;
}

.size22 {
    font-size: 22px;
}

.size24 {
    font-size: 24px;
}
.size44 {
    font-size: 44px;
}
#catTab .navbar-collapse.show ul {
    border: 1px solid #ccc;
}
#catTab .navbar-collapse.show {
    background: #f7f7f7;
    width: 50%;
}

#catTab .navbar-collapse ul {
    align-items: stretch;
}
#the-canvas {
    border: 0px solid #231f20;
    direction: ltr;
}
#pdfIcons {
    padding: 5px;
    user-select: none;
    min-height: 32px;
}
.ovhide {
    overflow: hidden;
}
.expand-btnClass {
    z-index: 9999;
    width: 100%;
    height: 100%;
    position: fixed;
    background: #fff;
    top: 0;
    left: 0;
    padding: 0px !important;
}
#pdfIcons .la,
#pdfIcons .las {
    font-size: 21px;
    cursor: pointer;
    color: #00b19c;
    padding: 0 4px;
}
.repoImage {
    display: inline-block;
    float: left;
    width: 45px;
}
.repoImage img {
    width: 35px;
}
.repoDesc {
    display: inline-block;
    float: left;
    width: calc(100% - 55px);
    margin-left: 10px;
}
.serDesc {
    display: inline-block;
    float: left;
    width: calc(100% - 55px);
    margin-left: 5px;
}
.repoAction {
    display: inline-block;
    margin-left: 5px;
}
:focus {
    outline: none !important;
}
.asset .icons .las {
    font-size: 19px;
}
.bs-tooltip-auto[x-placement^="top"] .arrow::before,
.bs-tooltip-top .arrow::before {
    top: 0;
    border-width: 0.3rem 0.3rem 0;
    border-top-color: #02104f;
}
.tooltip {
    font-size: 11px;
    font-family: "Montserrat", serif !important;
}
.tooltip-inner {
    font-family: "Montserrat", serif !important;
    border: 1px solid #02104f;
    background-color: white;
    color: #231f20;
}
.asset:hover {
    background-color: #fbfbfb;
}
.listing .res:hover {
    background-color: #fbfbfb;
    cursor: pointer;
}
.Repo {
    margin-bottom: 5px;
    display: inline-block;
    border-bottom: 1px solid #f9f9f9;
    padding-bottom: 5px;
}
span.Rheading {
    display: block;
    /* font-weight: 700; */
}
/* .Rdetail{ color: #c1c1c1;} */

.res {
    margin-bottom: 0;
    display: block;
    border-bottom: 1px solid #f9f9f9;
    padding-bottom: 8px;
    float: left;
    width: 100%;
    padding-top: 8px;
}
.repoAction .las {
    font-size: 18px;
}
.res span.Rheading {
    display: block;
    font-weight: 700;
}
.res .Rdetail {
    color: #c1c1c1;
}
span.Rtag {
    padding-top: 5px;
    font-size: 10px;
    display: block;
    /* color: #c1c1c1; */
}
.navbar.nav-tabs .nav-link:focus,
.navbar.nav-tabs .nav-link:hover {
    border-color: transparent !important;
}
#tsc_nav_1 .dchild {
    top: 0;
    left: 100%;
}
#tsc_nav_1 .dparent {
    width: 100%;
    text-align: left;
    position: relative;
}
.navbar .nav-item .nav-item:hover > a {
    color: #fff;
}
.dropdown-item:active {
    color: inherit;
}
.blogimageSection img {
    width: 100%;
}
.blogContainer {
    /* width: 100%; */

    margin-bottom: 10px;

    padding-right: 5px;
}
/* .blogdesc:hover {
    background-color: #fbfbfb;

} */
/*mob*/
/* .mobile#tsc_nav_1 .navbar-collapse.show {
    justify-content: flex-end!important;
    padding-top: 0;
    position: fixed;
    top: 50px;
    z-index: 9;
    width: 100%;
     right: 15px;
    padding: 0px;
     max-width: 90%;
    height: 90vh;
    overflow: auto;
    max-height: 90vh;
} */
.blogdesc {
    /* float: left; */
    font-size: 14px;
    padding: 7px 5px;
    text-align: center;
    line-height: 20px;
    font-weight: 600;
}
.btn-sm {
    font-size: 12px;
}
.blogimageSection {
    /* float: left; */
}
a.text-primary:focus,
a.text-primary:hover {
    color: #231f20 !important;
}
/*Home Page*/
.slide-box {
    display: flex;
    justify-content: space-evenly;
    text-align: center;
    z-index: 99;
    position: relative;
}

.card-style {
    display: inline-block;
    border: 0;
    /* width:211px;
    height:211px; */
    position: relative;
    -webkit-transition: all 200ms ease-in;
    -webkit-transform: scale(1);
    -ms-transition: all 200ms ease-in;
    -ms-transform: scale(1);
    -moz-transition: all 200ms ease-in;
    -moz-transform: scale(1);
    transition: all 200ms ease-in;
    transform: scale(1);
}

#carousel0 .card-style {
    display: block;
    width: 200px;
    height: 220px;
    position: relative;
    -webkit-transition: all 200ms ease-in;
    -webkit-transform: scale(1);
    -ms-transition: all 200ms ease-in;
    -ms-transform: scale(1);
    -moz-transition: all 200ms ease-in;
    -moz-transform: scale(1);
    transition: all 200ms ease-in;
    transform: scale(1);
}

#carousel0 .card-style:hover {
    z-index: 2;
    -webkit-transition: all 200ms ease-in;
    -webkit-transform: scale(1);
    -ms-transition: all 200ms ease-in;
    -ms-transform: scale(1.1);
    -moz-transition: all 200ms ease-in;
    -moz-transform: scale(1.1);
    transition: all 200ms ease-in;
    transform: scale(1.1);
}

#carousel0 .front {
    display: block !important;
}

#carousel0 .box {
    background: #fff !important;
    /* width: 220px; */
    height: 220px;
    border: 1px solid #d2e2e6 !important;
}

#carousel0 .imageSection {
    padding-top: 0px !important;
}

#carousel0 .imageSection:hover {
    padding-top: 0px !important;
}

#carousel0 .imageSection {
    min-height: 70px !important;
}

#carousel0 .descriptionSection p {
    font-size: 12px;
    font-weight: normal;
}

.card-style:hover {
    z-index: 2;
    -webkit-transition: all 200ms ease-in;
    -webkit-transform: scale(1.1);
    -ms-transition: all 200ms ease-in;
    -ms-transform: scale(1.1);
    -moz-transition: all 200ms ease-in;
    -moz-transform: scale(1.1);
    transition: all 200ms ease-in;
    transform: scale(1.1);
    background-color: #fff !important;
}

.card-style .imageSection img {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden !important;
    -ms-transform: translateZ(0);
    /* IE 9 */
    -webkit-transform: translateZ(0);
    /* Chrome, Safari, Opera */
    transform: translateZ(0);
}

.slider1 .carousel-inner {
    overflow: visible !important;
}

.arrowicon {
    font-size: 52px;
    color: #98bac3;
}

.carousel-control-prev {
    margin-left: -50px;
}

.carousel-control-next {
    margin-right: -50px;
}

.box {
    border: 1px solid #b2cbd2;
}

.carousel-control-next,
.carousel-control-prev {
    width: 4%;
}

.main {
    display: block;
    margin: 0 auto;
    overflow: hidden;
}

div.slider-image {
    background: #fff;
    margin: 0px;
    position: relative;
    text-align: center;
    /* height: 250px; */
    padding: 0px;
    /* margin-left: 10px; */
}

.slick-next:before,
.slick-prev:before {
    font-size: 20px;
    line-height: 1;
    opacity: 0.75;
    color: #231f20;
}

.sliderBtn {
    background: transparent;
}

.leftArrow {
    position: absolute;
    top: 42%;
    left: 0px;
    z-index: 98;
    cursor: pointer;
}
.rightArrow {
    z-index: 98 !important;
}

.rightArrow {
    position: absolute;
    top: 42%;
    right: 0px;
    cursor: pointer;
}
.rightArrow,
.leftArrow {
    opacity: 0.5;
}
.leftArrow:hover,
.rightArrow:hover {
    opacity: 1;
    transition: 1s all;
}

h4.heading {
    color: #00b19c;
    font-size: 18px;
    font-weight: 600;
    padding-left: 7px;
}

.carouselSection .carousel-inner {
    overflow: visible !important;
}

.carouselSection .box {
    height: 240px;
    background: #fff;
    width: 200px;
    margin-left: -1px;
}

.carouselSection .box .boxContainer {
    position: relative;
    max-width: 100%;
    padding: 10px;
    height: 100%;
}

.carouselSection .box .boxContainer .front,
.carouselSection .box .boxContainer .back {
    max-width: 100%;
    height: 100%;
}

.carouselSection .box .boxContainer .back {
    /* position: absolute; */
    width: 100%;
}

.carousel-control-next,
.carousel-control-prev {
    z-index: 0;
}

.btnSend {
    background-color: #02104f;
    border-color: #02104f;
    border-radius: 0;
}

.btnSend:hover,
.btnSend:active,
.btnSend:focus {
    color: #fff;
    background-color: #02104f !important;
    border-color: #02104f !important;
}

/* .slick-dots {
    bottom: 10px;
    width: 100%;
    list-style: none;
    text-align: right;
    right: 20%;
} */

.slick-dots li button:before {
    position: absolute;
    top: 0;
    left: 0;
    width: 10px;
    height: 10px;
    content: " ";
    text-align: center;
    opacity: 1;
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid slategray;
}

.slick-dots li.slick-active button:before {
    opacity: 1;
    background: #3bcd3f;
}

.slick-dots li {
    margin: 0;
}

.slick-slide img {
    filter: opacity(0.4);
    opacity: 0.9;
    border: 1px solid;
}

.slick-active img{
    opacity: 1 !important;
    filter: unset !important;
    /* border: 1px solid #ccc; */
    border: 1px solid #999;
    box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.5);
}

.imageSection {
    min-height: 100px;
    width: 100%;
    max-width: 100% !important;
    text-align: center;
    padding-top: 20px;
    position: relative;
}

.descriptionSection {
    width: 100%;
    max-width: 100% !important;
    font-size: 14px;
    font-weight: 600;
}

.slick-slider {
    margin-bottom: 5px;
}

.back .imageSection {
    min-height: 50px;
    width: 100%;
    max-width: 100% !important;
    text-align: center;
    padding-top: 0px;
    margin-bottom: 5px;
}

.back .descriptionSection {
    width: 100%;
    max-width: 100% !important;
    font-size: 12px;
    font-weight: 600;
    text-align: left;
    margin-top: 10px;
}

.back .addIcon {
    position: absolute;
    top: 5px;
    right: 5px;
    font-weight: bold;
    font-size: 21px;
    cursor: pointer;
    /* color: #3bcd3f; */
    backface-visibility: hidden;
    cursor: pointer;
    z-index: 9;
}
.back .addIcon:hover {
    color: #3bcd3f;
}
.favoriteColor {
    color: #3bcd3f !important;
}

/* .slidingSection h4.heading {
    margin-left: -10px;
} */

#carousel0 .tileHeader {
    height: 25px;
    background: #8dbac4;
    width: 100%;
    max-width: 100%;
    color: #fff;
    padding-top: 2px;
    font-size: 13px;
}

#carousel0 .boxContainer {
    cursor: pointer;
    height: 196px;
    overflow: hidden;
}

.carouselSectionSlide {
    overflow: hidden;
    padding-left: 40px;
    padding-right: 40px;
    padding-top: 10px;
    padding-bottom: 20px;
}

#rectangle {
    width: 150px;
    height: 30px;
    background: #3bcd3f;
    display: inline-block;
    margin-left: -4px;
}

#triangle-bottomright {
    width: 0;
    height: 0;
    border-bottom: 30px solid #3bcd3f;
    border-left: 30px solid transparent;
    display: inline-block;
}

div.slider-image {
    background: #fff;
    margin: 0px;
    position: relative;
    text-align: center;
    height: auto;
    padding: 0px;
    /* margin-left: 10px; */
    overflow: hidden;
}

.shapeSection {
    position: absolute;
    right: 0px;
    top: 15px;
    box-shadow: 1px 4px 1px #231f20;
    /* border-bottom: 3px solid #102638; */
    display: block;
    height: 30px;
    overflow: hidden;
}

.shapeText {
    position: absolute;
    top: 4px;
    color: #fff;
    font-size: 16px;
    left: 0px;
    width: 100%;
    display: block;
}

.slideTextSection {
    position: absolute;
    top: 11px;
    left: 20px;
    text-align: left;
    transition: 1s all;
    z-index: 999;
}

.slideTextSection p {
    font-size: 18px;
    transition: 1s all;
}

.borderRightDark {
    border-right: 1px solid #e8eaef;
}
.borderBottomDark {
    border-bottom: 1px solid #e8eaef;
}
.border-head-green {
    border-color: #62d765 !important;
    border-bottom: 2px solid;
}
.font-size16 {
    font-size: 16px;
}
.border-bottom {
    border-bottom: 1px solid #dee2e6 !important;
}
section.noteSection {
    min-height: 20px;
    font-size: 12px;
    /* border-top: 1px solid #e8eaef; */
    padding: 10px;
    clear: both;
}
.chartNotes {
    padding-left: 10px;
}
header.widget-header {
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e8eaef;
}
section.chartArea {
    padding-right: 10px;
}
section.chartArea {
    padding-right: 10px;
    min-height: 300px;
    position: relative;
}
.popover,
.popover-header {
    font-size: 12px;
}
.popover {
    border: 1px solid #02104f;
}
.popover-header {
    padding: 0.3rem 0.75rem;
    background-color: #fff;
    border-bottom: none;
}
.bs-popover-auto[x-placement^="right"] .arrow::before,
.bs-popover-right .arrow::before {
    left: 0;
    border-right-color: #02104f;
}
.bs-popover-auto[x-placement^="right"] .arrow::after,
.bs-popover-right .arrow::after {
    left: 1px;
    border-right-color: none;
}
.popover-body {
    padding-top: 0px;
}
.progress {
    width: 130px;
    height: 130px;
    background: none;
    position: relative;
}

.progress::after {
    content: "";
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 6px solid #02104f;
    position: absolute;
    top: 0;
    left: 0;
}

.progress > span {
    width: 50%;
    height: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    z-index: 1;
}

.progress .progress-left {
    left: 0;
}

.progress .progress-bar {
    width: 100%;
    height: 100%;
    background: none;
    border-width: 6px;
    border-style: solid;
    position: absolute;
    top: 0;
    border-color: #00b19c !important;
}

.progress .progress-left .progress-bar {
    left: 100%;
    border-top-right-radius: 80px;
    border-bottom-right-radius: 80px;
    border-left: 0;
    -webkit-transform-origin: center left;
    transform-origin: center left;
}

.progress .progress-right {
    right: 0;
}

.progress .progress-right .progress-bar {
    left: -100%;
    border-top-left-radius: 80px;
    border-bottom-left-radius: 80px;
    border-right: 0;
    -webkit-transform-origin: center right;
    transform-origin: center right;
}

.progress .progress-value {
    position: absolute;
    top: 0;
    left: 0;
}
.out100 {
    position: absolute;
    top: 30px;
    left: 69%;
    font-size: 40px;
    font-weight: bold;
}
.changeBorderRight {
    border-right: 1px solid #ddd;
}
span.changeText {
    font-size: 32px;
    display: block;
    color: #00b19c;
}
.changeText + small {
    font-size: 12px;
    text-align: center;
}
#tsc_nav_1 .odi .nav-link {
    padding: 3px 15px;
}
.desktop.navbar .input-group {
    width: 450px;
    justify-content: flex-end;
    margin-right: 0px;
    align-items: center;
}
ul.nohover {
    margin-top: 3px;
}
.navbar .input-group {
    height: 30px;
}
.input-group-btn select {
}
.input-group input {
    height: 28px;
}
/* .navbar .nav-item:hover .nav-item:hover > a{color:#fff} */
#lineChartdbl
    li.amcharts-amexport-item.amcharts-amexport-item-level-0.amcharts-amexport-item-csv {
    opacity: 1;
    background: #fff;
    border: 1px solid #d6d9d8 !important;
    border-radius: 0px;
    font-size: 16px;
    padding-top: 2px;
    position: absolute;
    right: -16px;
    background: #d6d9d8;
}

#lineChartdbl
    li.amcharts-amexport-item.amcharts-amexport-item-level-0.amcharts-amexport-item-csv
    a
    i {
    color: #231f20;
}
li.amcharts-amexport-item.amcharts-amexport-item-level-0.amcharts-amexport-item-csv {
    opacity: 1;
    background: #fff;
    border: 1px solid #d6d9d8 !important;
    border-radius: 0px;
    font-size: 16px;
    padding-top: 2px;
    background: #d6d9d8;
}

li.amcharts-amexport-item.amcharts-amexport-item-level-0.amcharts-amexport-item-csv
    a
    i {
    color: #231f20;
}

.select2-container--open .select2-dropdown--below {
    border-top: 1px solid #aaa !important;
}

/* header aside i {
    color: #00b19c;
} */
.serbox {
    width: 100%;
    text-align: center;
    font-size: 12px;
    float: none;
    height: 100%;
    min-height: 317px;
}
.grid {
    width: 100%;
}
.grid .row,
.listing .row {
    width: 100%;
}
.serbox .imageSection {
    padding: 15px 0;
    min-height: 89px;
}
.serbox .imageSection img {
}
.serbox .tileHeader {
    color: #fff;
    background: rgb(141, 186, 196);
    padding: 2px;
}
.listing .row:hover {
    background: #fbfbfb;
}
.serbox p {
    font-weight: normal;
    font-size: 12px;
}
.serbox .boxContainer .descriptionSection {
    padding: 0 8px;
    font-size: 13px;
    padding-bottom: 20px;
}
.tooltip.show {
    opacity: 1;
}
.serPage.nav-tabs a.nav-link {
    font-size: 20px !important;
    padding: 0 5px !important;
}
.bs-tooltip-auto[x-placement^="bottom"] .arrow::before,
.bs-tooltip-bottom .arrow::before {
    bottom: 0;
    border-width: 0 0.4rem 0.4rem;
    border-bottom-color: #02104f;
}
.navbar-nav .dropdown-menu .dropdown-toggle::after {
    transform: rotate(270deg) !important;
}

.modal {
    z-index: 99999999;
}
.modal-backdrop {
    z-index: 999999;
}
.filterSection .select2-container {
    display: block;
}
#tsc_nav_1 .MegaMenu > .dropdown-menu {
    width: 100%;
}
.MegaMenu {
    position: static;
}
.slick-current:after {
    content: "";
    -webkit-box-shadow: -7px 7px 8px -6px rgb(154 154 154 / 75%);
    -moz-box-shadow: -7px 7px 8px -6px rgb(154 154 154 / 75%);
    box-shadow: -7px 7px 8px -6px rgb(154 154 154 / 75%);
    width: 100%;
    height: 98%;
    top: 0;
    position: absolute;
}
.slick-slide.slick-current {
    position: relative;
}
.slick-slide:not(.slick-current) .slider-image:after {
    /* background: rgba(226,226,226,1);
background: -moz-linear-gradient(left, rgba(226,226,226,1) 0%, rgba(219,219,219,1) 0%, rgba(209,209,209,1) 2%, rgba(254,254,254,1) 100%);
background: -webkit-gradient(left top, right top, color-stop(0%, rgba(226,226,226,1)), color-stop(0%, rgba(219,219,219,1)), color-stop(2%, rgba(209,209,209,1)), color-stop(100%, rgba(254,254,254,1)));
background: -webkit-linear-gradient(left, rgba(226,226,226,1) 0%, rgba(219,219,219,1) 0%, rgba(209,209,209,1) 2%, rgba(254,254,254,1) 100%);
background: -o-linear-gradient(left, rgba(226,226,226,1) 0%, rgba(219,219,219,1) 0%, rgba(209,209,209,1) 2%, rgba(254,254,254,1) 100%);
background: -ms-linear-gradient(left, rgba(226,226,226,1) 0%, rgba(219,219,219,1) 0%, rgba(209,209,209,1) 2%, rgba(254,254,254,1) 100%);
background: linear-gradient(to right, rgba(226,226,226,1) 0%, rgba(219,219,219,1) 0%, rgba(209,209,209,1) 2%, rgba(254,254,254,1) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#e2e2e2', endColorstr='#fefefe', GradientType=1 ); */
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 99999;
    top: 0;
}
.change {
    display: flex;
    justify-content: center;
}
.mom {
    padding: 0 10%;
}
#loadTopCategory .col-sm-4 {
    padding: 0;
    font-size: 12px;
    padding-right: 10px;
}
#loadTopCategory .col-sm-4 a {
    min-height: 30px;

    line-height: 22px;
    border-right: 0px solid #f9f9f9;
}
.slick-slider:after {
    content: "";
    position: absolute;
    width: 0;
    height: 100%;
    z-index: 9;
    left: -2px;
    top: 0;
    box-shadow: 10px 7px 24px 66px rgb(255, 255, 255);
}
.slick-slider:before {
    content: "";
    position: absolute;
    width: 0;
    height: 100%;
    z-index: 9;
    right: -2px;
    top: 0;
    box-shadow: -26px 7px 24px 66px rgb(255, 255, 255);
}
.bkbtn {
    text-align: right;
    background: #02104f;
    color: #fff;
    padding-right: 10px;
    width: auto;
    float: right;
    position: absolute;
    right: 0px;
    top: 10px;
    cursor: pointer;
    padding-top: 3px;
    padding-bottom: 3px;
    font-size: 12px;
    /*
border: 1px solid #231f20; */
    background: #fff;
    color: #00b19c;
    padding-left: 10px;
}

.filterIcon {
    width: 30px;
    height: 30px;
    display: block;
    float: right;
    text-align: center;
    cursor: pointer;
    font-size: 18px;
    padding-top: 1px;
    font-weight: bold;
    color: #00b19c;
}
.icons a {
    position: relative;
    padding: 5px 6px;
    color: #667986 !important;
}
.icons a:hover,
.fav:hover {
    color: #00b19c !important;
}

.icons a:not(:last-child):after {
    content: "";
    position: absolute;
    width: 1px;
    height: 14px;
    background: #ccc;
    right: 0;
    top: 8px;
}

/* .icons a.lastElementBorderRemove:not(:last-child):after{
        display: none;
    }   */

.fav {
    padding: 5px;
    color: #667986;
    font-size: 14px;
}
/* .listing .repoAction{float:right} */

.mom {
    padding: 0 30px !important;
}
.filterSection .select2-container {
    display: block;
}

.noCardBorder {
    border: none;
    width: 70%;
    margin: 0 auto;
    box-shadow: none;
}
.borderLeft {
    border-left: 1px solid #e8eaef !important;
}
.slidingSection {
    margin-top: -10px;
}

/* .recentlyViewed .boxContainer .front p {
          text-align: left;
      }
      #carousel0 .descriptionSection p{
          line-height: 16px;;
      }

     .descriptionSection{
          line-height: 16px;;
      }
      .back .descriptionSection {
        margin-top: 10px;
    }
    .back .imageSection {
        margin-bottom: 0px;
    }
    .back p{
        margin-bottom: 0px;
    font-weight: bold;
    width: 85%;
    margin: 0 auto;
    line-height: 16px;
    min-height: 36px;
    }
    .back .imageSection img {
        width: 30%;
        margin-top: 5px;
        margin-bottom: 5px;
    }
     */
.back .addIcon {
    position: absolute;
    top: 10px;
    right: 16px;
}
.descriptionSection a {
    font-weight: normal;
}
.recentlyViewed p {
    text-align: left;
}
.select2-container--open .select2-dropdown--below {
    border-radius: 0px !important;
}

.year {
    margin-bottom: -1px;
    margin-left: -1px;
    margin-right: -1px;
}
.serbox .tileHeader {
    color: #fff;
    background: rgb(141, 186, 196);
    padding: 2px;
}

p {
    line-height: 16px;
}
.back .descriptionSection {
    width: 100%;
    max-width: 100% !important;
    font-size: 11px;
    text-align: left;
    margin-top: 10px;
}
.back p {
    margin-bottom: 0px;
    color: #231f20;
    font-weight: bold;
}
.recentlyViewed .front .imageSection img {
    opacity: 1 !important;
}
.recentlyViewed .front .descriptionSection {
    opacity: 1 !important;
}
.filterSection .select2-container {
    padding-right: 0px !important;
}

.switchToggle.switch input[type="checkbox"] {
    height: 0;
    width: 0;
    visibility: hidden;
}

.switchToggle.switch label {
    cursor: pointer;
    width: 45px;
    height: 22px;
    background: #00b19c;
    display: block;
    border-radius: 56px;
    position: relative;
    margin-left: -40px;
    margin-top: 9px;
}

.switchToggle.switch label:before {
    content: attr(data-off);
    position: absolute;
    top: 1.4px;
    right: 0;
    font-size: 8.4px;
    padding: 7px 7px;
    color: white;
}

.switchToggle.switch input:checked + label:before {
    content: attr(data-on);
    position: absolute;
    left: 0;
    font-size: 8.4px;
    padding-left: 7px;
    color: white;
}

.switchToggle.switch label:after {
    content: "";
    position: absolute;
    top: 2.4px;
    left: 2.4px;
    width: 17.2px;
    height: 17.2px;
    background: #fff;
    border-radius: 5.6px;
}

.switchToggle.switch input:checked + label {
    background: #00b19c;
}

.switchToggle.switch input:checked + label:after {
    -webkit-transform: translateX(22px);
    transform: translateX(22px);
}
.switchToggle.switch input:checked + label:after {
    animation: none;
    -webkit-transform: translateX(22px);
    transform: translateX(22px);
}
.switchToggle.switch label:after {
    content: "";
    position: absolute;
    top: 2.4px;
    left: 2.4px;
    width: 17.2px;
    height: 17.2px;
    background: #fff;
    border-radius: 17.6px;
}
.catimageSection {
    position: relative;
    overflow: hidden;
}
/* .catimageSection::after {
    content: ' ';
    position: absolute;
    bottom: -4px;
    background: linear-gradient(0deg, rgba(255,255,255,1) 1%, rgba(255,255,255,0.3718662464985995) 31%);
    width: 100%;
    height: 100px;
    display: block;
} */
.switch.switchToggle.d-flex {
    justify-content: flex-start !important;
}
.switch.switchToggle.d-flex span:first-child {
    margin-right: 50px !important;
}
.switch.switchToggle.d-flex span:last-child {
    margin-left: 10px;
}

.catimageSection::after {
    content: " ";
    position: absolute;
    bottom: -22px;
    background: linear-gradient(
        0deg,
        rgba(255, 255, 255, 1) 9%,
        rgba(255, 255, 255, 0.9718662464985995) 11%
    );
    width: 100%;
    height: 17px;
    display: block;
    box-shadow: 0px -20px 20px 16px rgba(255, 255, 255, 1);
}
.navbar-nav .btn {
    border-radius: 0px !important;
}

a {
    line-height: 16px;
}
li.nav-item.odi {
    margin-left: 5px;
}
/* .requestCustom{
    background: #3bcd3f !important;
    -webkit-transition: all .7s;
    transition: all .7s;

} */
/* #requestCustom.requestCustom:hover{
    background: 0 0 !important;
    color: #3bcd3f !important;
    border: 0;
    border-top: 1px solid #3bcd3f;
    border-bottom: 1px solid #3bcd3f;
}

#requestCustom.requestCustom:before {
    background: #3bcd3f;
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 1px;
    transition: all .4s ease;
}
#requestCustom.requestCustom:after{
    top: inherit;
    left: inherit;
    bottom: 0;
    right: 0;
} */

.blogHeading {
    display: flex;
    justify-content: center;
    align-items: center;
}
/* .blogdesc {
    font-size: 14px;
    padding: 7px 5px;
    text-align: center;
    line-height: 20px;
    font-weight: 600;
    background: rgba(21,161,137,.85);
    position: absolute;
    bottom: 10%;
    left: 50%;
    width: 80%;
    display: block;
    margin: 0 auto;
    transform: translate(-50%, 0%);
} */
.blogdesc {
    font-size: 14px;
    padding: 7px 5px;
    text-align: center;
    line-height: 20px;
    font-weight: 600;
    background: rgba(21, 161, 137, 0.85);
    /* position: absolute; */
    /* bottom: 10%; */
    /* left: 50%; */
    width: 80%;
    /* display: block; */
    margin: 0 auto;
    /* transform: translate(-50%, 0%); */
    align-self: flex-end;
    margin-bottom: 20px !important;
}

.dropdown-item.active,
.dropdown-item:active {
    color: rgb(175, 166, 166);
    text-decoration: none;
    background-color: #007bff;
}

.bootstrap-autocomplete .dropdown-item.active,
.bootstrap-autocomplete .dropdown-item:active {
    /* color: #FFF; */
    text-decoration: none;
    color: #231f20;
    border-left: 3px solid #3bcd3f !important;
    background-color: #ebf1f3 !important;
}
.blogHeading a:hover {
    text-decoration: underline;
}
.blogHeading a {
    color: #fff;
    font-size: 17px;
    display: flex;
    line-height: 21px;
}

.hello {
    color: rgb(255, 255, 255);
    font-weight: 300;
    margin-top: -1px;
    position: relative;
    background: #3bcd3f;
    border-width: 0px;
    border-style: initial;
    border-color: initial;
    border-image: initial;
    padding: 3.5px 15px;
    transition: all 0.7s ease 0s;
    margin-left: 10px;
    display: block;
}

.hello:hover:before {
    -webkit-transition: all 0.7s ease;
    transition: all 0.7s ease;
    width: 100%;
}
.hello:after,
.hello:before {
    background: #3bcd3f;
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 1px;
    transition: all 0.4s ease;
}
.hello:hover {
    background: 0 0;
    color: #3bcd3f;
}
.hello:after {
    top: inherit;
    left: inherit;
    bottom: 0;
    right: 0;
}
.hello:hover:after {
    -webkit-transition: all 0.7s ease;
    transition: all 0.7s ease;
    width: 100%;
}
.dropdown-menu.dchild {
    min-width: 220px;
}

.resizing_select {
    width: 70px;
    min-width: 65px;
}

#width_tmp_select {
    display: none;
}

.topics {
    display: inline;
    width: 100%;
}
.blogContainer .share {
    position: relative;
    float: right;
}
.blogContainer .share .dropdown-trigger {
    padding: 0 5px;
    font-size: 16px;
}
.blogContainer .share ul {
    position: absolute;
    background-color: #171f2b;
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: -webkit-flex !important;
    display: flex !important;
    flex-wrap: wrap;
    align-items: center;
    text-align: center;
    justify-content: center;
    padding: 10px;
    right: 35px;
    top: -10px;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
    list-style: none;
}
.blogContainer .share ul:before {
    position: absolute;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid #171f2b;
    content: "";
    right: -8px;
    top: 8px;
}
.blogContainer .share ul li {
    float: left;
    margin: 5px 2px;
}
.blogContainer .topics ul li a {
    font-size: 10px;
    line-height: 8px;
    text-align: left;
    color: #171f2b;
    text-decoration: none;
}
.blogContainer .share ul li a {
    color: #fff;
    font-size: 15px;
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: -webkit-flex !important;
    display: flex !important;
    flex-wrap: wrap;
    align-items: center;
    text-align: center;
    justify-content: center;
}
.blogContainer {
    position: relative;
}
.blogHeading {
    display: flex;
    justify-content: center;
    align-items: center;
    /* max-width: 300px; */
}
.blogHeading a {
    color: #fff;
    font-size: 16px;
    display: block;
    line-height: 21px;
    font-weight: bold !important;
}

.blogdesc {
    text-align: left;
    /* padding-left: 20px; */
}
.borderBottomDarktoo {
    border-bottom: 1px solid #d2d2d2;
}

.catList {
    position: static;
    margin-bottom: 10px;
}

.catList .box {
    height: 206px;
    background: #fff;
    width: 206px;
    margin-left: -1px;
    display: inline-block;
    float: left;
    margin-bottom: 10px;
}
.catList .box .boxContainer {
    position: relative;
    max-width: 100%;
    padding: 10px;
}
iframe {
    border: 1px solid #f7f7f7;
    width: 100%;
    position: relative;
    z-index: 1;
}
#tsc_nav_1 .demo .dchild {
    top: 0;
    right: 100%;
    left: auto;
}
.navbar .nav-item .nav-item.demo:hover > a {
    color: #fff;
    background: #00b19c;
}

/*Sliding Updated code*/
#lightSlider li {
    margin-left: -2px;
}

.lightSlider,
.lSSlideWrapper1,
.lSSlideOuter {
    overflow: visible !important;
}
.lSSlideWrapper {
    max-width: 100%;
    overflow: hidden;
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 13px;
}

.lSAction > a {
    background-image: none;
}

.lSPager {
    display: none;
}

.slide-box {
    position: static;
}
h4.heading {
    margin-bottom: -10px;
}
.slideControls {
    position: absolute;
    width: 100%;
    top: 126px;
    left: 0;
}
.slideControls i {
    font-size: 50px;
}
.slideControls .slideNext {
    right: 0;
    position: absolute;
    cursor: pointer;
}
.slideControls .slidePrev {
    left: 0;
    position: absolute;
    cursor: pointer;
}
.carouselSectionSlide {
    position: relative;
}
/*End sliding updated code*/
#catTab #reportTabsId .nav-link.active {
    pointer-events: none;
    color: rgba(0, 0, 0, 0.9) !important;
}
#catTab #reportTabsId .nav-link.active:hover {
    color: rgba(0, 0, 0, 0.9) !important;
}

#catTab #reportTabsId .nav-item:hover,
#catTab #reportTabsId .nav-link:hover {
    /*background: #00b19c !important;*/
    color: #fff !important;
}

.fixedContactForm {
    box-shadow: 4px 4px 13px 1px rgba(0, 0, 0, 0.2);
    padding: 10px;
    background: #fff;
    /* height: 243px; */
    width: 340px;
    position: fixed;
    bottom: 270px;
    right: 0px;
    z-index: 99999;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    border-left: 7px solid #3bcd3f;
}

.fixedContactForm h2 {
    font-size: 14px;
    /* text-transform: uppercase; */
    color: #00b19c;
}

.fixedContactForm h2 + small {
    color: #999;
}

.fixedContactForm .form-group {
    padding-top: 5px;
}

.fixedCircle {
    width: 60px;
    height: 60px;
    border-radius: 60px;
    background: #00b19c;
    color: #fff;
    text-align: center;
    font-size: 40px;
    line-height: 60px;
    margin: 0 auto;
    display: block;
    position: relative;
    bottom: 200px;
    right: 10px;
    cursor: pointer;
}
.closeBox {
    width: 60px;
    height: 60px;
    border-radius: 60px;
    background: #00b19c;
    color: #fff;
}
.fixedcontactUs {
    position: fixed;
    bottom: 200px;
    right: 10px;
    z-index: 9999;
}
.lSAction {
    display: none !important;
}
.front img {
    width: 120px;
    height: 50px;
}
.back img {
    width: 120px;
    height: 50px;
}
h1 + select + span.select2-container {
    width: auto !important;
    padding-right: 20px;
}
.footer a:hover {
    text-decoration: none;
}

.redactor-air a.re-button-icon,
.redactor-toolbar a.re-button-icon {
    font-size: 14px !important;
    padding: 5px 10px 5px 10px !important;
}
.redactor-air a:hover,
.redactor-toolbar a:hover {
    outline: 0;
    color: #fff;
    background: #00b19c !important;
}
.catContainer {
    min-height: 271px;
}
.icons a:hover {
    color: #3bcd3f !important;
}
.icons i {
    font-size: 19px;
}
.lslide {
    margin-right: -2.2px !important;
}
.catTab .navbar-toggler {
    font-size: 12px;
}
.slimScrollBar,
.slimScrollRail {
    border-radius: 0px !important;
    opacity: 0.2 !important;
}

/* bootstrep multiselect */
.filterSection .select2-container {
    display: block;
}
.multiselect-container > li > a > label {
    padding: 0px 0px 0px 10px;
    outline: none;
}
.multiselect-container.with-inputType-none input[type="radio"] {
    display: none;
}
.multiselect.dropdown-toggle.btn.btn-default {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 0;
}
.multiselect-item .input-group-btn {
    display: none;
}
.filterSection label {
    width: 100%;
}
.multiselect.dropdown-toggle.btn.btn-default {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 0;
}
.filterSection {
    border: 1px solid #ddd;
    background-color: #fff;
    min-width: 220px;
    padding: 10px;
    position: absolute;
    right: 16px;
    z-index: 99;
    width: 260px;
}
span.multiselect-selected-text {
    font-size: 12px;
}
.multiselect.btn {
    text-align: left;
}
.multiselect-native-select > .btn-group > .btn-default {
    color: #231f20;
}

.multiselect-container > li {
    padding: 2px 0;
}
.multiselect-container > li > a {
    padding: 0 0;
    margin-left: 0;
    color: #231f20;
}
.multiselect-container > li > a > label {
    margin: 0;
    height: 100%;
    cursor: pointer;
    font-weight: 400;
    padding: 5px 20px 10px 5px !important;
}
.multiselect-container > li > a > label:hover {
    background: #dae3e6;
}
.absolute-center {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 100%;
    flex-direction: column;
    min-height: 285px;
}
.absolute-center i {
    font-size: 50px;

    color: #6c757d;
    display: block;
}
.absolute-center span.image {
    background-image: url(../images/no-data.svg?v=1.0.4);
    max-width: 100px;
    width: 100px;
    height: 58px;
    max-width: 100px;
}

.select2-container {
    width: auto !important;
    padding-right: 15px;
}
/* .multiselect-container li.active{background: #5ccbbf;} */
.slimScrollBar {
    z-index: 9 !important;
}

.catimageSection {
    overflow: hidden;
    height: 133px;
}
#chatter ul.discussions li a.discussion_list .chatter_right {
    display: flex;
}
.lead {
    font-size: 16px;
    font-weight: 300;
    line-height: 21px;
}
.modal-footer button {
    font-size: 12px;
}
.modal-body p {
    margin-bottom: 0px;
}
.modal-header,
.modal-body,
.modal-footer {
    padding: 10px;
}
.modal-content {
    border-radius: 0px;
}
.modal-title {
    font-size: 16px;
}
span.Rdetail {
    font-size: 12px;
    color: #676767;
}
span.Rtag {
    padding-top: 5px;
    font-size: 10px;
    display: block;
    color: #676767 !important;
}
#cs_country_filter + .btn-group,
#csFilter + .btn-group {
    width: 100% !important;
}

#cs_country_filter + .btn-group .multiselect-container > li > a > label,
#csFilter + .btn-group .multiselect-container > li > a > label {
    display: block;
}
/* #cs_country_filter + .btn-group button,
#csFilter + .btn-group button {
    position: relative;
    height: 32px;
    font-size: 12px;
} */

/* #cs_country_filter + .btn-group .dropdown-toggle::after,
#csFilter + .btn-group .dropdown-toggle::after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
    position: absolute;
    right: 8px;
    top: 15px;
} */
/* .loader-info,
.loader-info-iframe { */
/* border: 5px solid #ebf1f3;
    border-radius: 50%; */
/* border-top: 5px solid #00b19c;
    width: 45px;
    height: 45px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
    transform: translate(0, -50%);
}*/
.loader-info,
.loader-info-iframe {
    /*left: calc(100% / 2 - 37.5px);
    top: calc(100% / 2 - 37.5px);*/
    left: calc(100% / 2 - -3.5px);
    top: calc(100% / 2 - 65.5px);
    position: absolute;
    z-index: 99;
}
.loader-info-iframe {
    left: calc(100% / 2 - -3.5px);
    top: calc(100% / 2 - 65.5px);
    /*left: calc(100% / 2 - 37.5px);
    top: calc(100% / 2 - 37.5px);*/
    position: absolute;
    z-index: 33 !important;
}
@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.loader-info div {
    left: 50%;
    top: 50%;
    position: absolute;
    animation: anim linear 1s infinite;
    width: 4px;
    height: 29px;
    transform-origin: 2px 52px;
}

.loader-info div:nth-child(1) {
    transform: rotate(0deg);
    animation-delay: -0.9166666666666666s;
    background: #00b19c;
}

.loader-info div:nth-child(2) {
    transform: rotate(30deg);
    animation-delay: -0.8333333333333334s;
    background: #3bcd3f;
}

.loader-info div:nth-child(3) {
    transform: rotate(60deg);
    animation-delay: -0.75s;
    background: #007365;
}

.loader-info div:nth-child(4) {
    transform: rotate(90deg);
    animation-delay: -0.6666666666666666s;
    background: #8dbac4;
}

.loader-info div:nth-child(5) {
    transform: rotate(120deg);
    animation-delay: -0.5833333333333334s;
    background: #02104f;
}

.loader-info div:nth-child(6) {
    transform: rotate(150deg);
    animation-delay: -0.5s;
    background: #33c1b0;
}

.loader-info div:nth-child(7) {
    transform: rotate(180deg);
    animation-delay: -0.4166666666666667s;
    background: #62d765;
}

.loader-info div:nth-child(8) {
    transform: rotate(210deg);
    animation-delay: -0.3333333333333333s;
    background: #338f84;
}

.loader-info div:nth-child(9) {
    transform: rotate(240deg);
    animation-delay: -0.25s;
    background: #a4c8d0;
}

.loader-info div:nth-child(10) {
    transform: rotate(270deg);
    animation-delay: -0.16666666666666666s;
    background: #02104f;
}

.loader-info div:nth-child(11) {
    transform: rotate(300deg);
    animation-delay: -0.08333333333333333s;
    background: #66d0c4;
}

.loader-info div:nth-child(12) {
    transform: rotate(330deg);
    animation-delay: 0s;
    background: #89e18c;
}
@keyframes anim {
    0% {
        opacity: 1;
    }

    80% {
        opacity: 0;
    }
}
.select2-dropdown {
    z-index: 999 !important;
}
.icons a {
    position: relative;
}
.la-bolt1 {
    background: url(../images/strategy.svg) no-repeat;
    width: 19px;
    height: 19px;
    display: inline-block;
    position: absolute;
    left: -15px;
    top: 1px;
}

.la-bolt1:hover {
    background: url(../images/strategy-hover.svg) no-repeat;
    width: 19px;
    height: 19px;
    display: inline-block;
    position: absolute;
    left: -15px;
    top: 1px;
}
.la-question1 {
    background: url(../images/question.svg) no-repeat;
    width: 19px;
    height: 19px;
    display: inline-block;
    position: absolute;
    left: 2px;
    top: 1px;
}
.la-question1:hover {
    background: url(../images/question-hover.svg) no-repeat;
    width: 19px;
    height: 19px;
    display: inline-block;
    position: absolute;
    left: 2px;
    top: 1px;
}
.catContainer .icons {
    padding-right: 10px;
}
/* .Rheading .icons {
    padding-left: 15px;
} */
.Rheading .icons {
    margin-left: 15px;
}

.Rheading .icons .la-bolt1 {
    background: url(../images/strategy.svg) no-repeat;
    width: 17px;
    height: 17px;
    display: inline-block;
    position: absolute;
    left: -10px;
    top: 3px;
}

.Rheading .icons .la-bolt1:hover {
    background: url(../images/strategy-hover.svg) no-repeat;
    width: 17px;
    height: 17px;
    display: inline-block;
    position: absolute;
    left: -10px;
    top: 3px;
}
.Rheading .icons .la-question1 {
    background: url(../images/question.svg) no-repeat;
    width: 17px;
    height: 17px;
    display: inline-block;
    position: absolute;
    left: 2px;
    top: 3px;
}
.Rheading .icons .la-question1:hover {
    background: url(../images/question-hover.svg) no-repeat;
    width: 17px;
    height: 17px;
    display: inline-block;
    position: absolute;
    left: 2px;
    top: 3px;
}
.Rheading .icons .las.la-plus {
    font-size: 17px;
}
div.absolute-center p {
    text-transform: lowercase;
    font-size: 1.1em;
}
.carouselSection .carousel-inner {
    width: 99%;
}
.blogContainer {
    /* width: 420px; */
    height: 280px;
}
.blogimageSection {
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-size: cover !important;
}
.blogimageSection img {
    width: 100%;
    height: 100%;
}
.page-link:focus {
    z-index: 2;
    outline: 0;
    box-shadow: none !important;
}
.page-link {
    color: #231f20;
}

.desktop.navbar .nav-item:hover {
    background: #02104f !important;
    color: white !important;
}
.desktop.navbar .nav-link:hover {
    background: #02104f !important;
    color: white !important;
}

#tsc_nav_1 .dropdown-item:hover #tsc_nav_1 .dchild .dropdown-item,
.dparent .dropdown-item.dropdown-toggle:hover {
    background-color: #ebf1f3 !important;
    color: #231f20 !important;
}
.desktop.navbar .dparent.nav-item:hover {
    background-color: #ebf1f3 !important;
    color: #231f20 !important;
}
.desktop.navbar .dparent.nav-item:hover > a {
    color: #231f20 !important;
}
.bs-tooltip-auto[x-placement^="top"] .arrow::before,
.bs-tooltip-top .arrow::before {
    top: 0;
    border-width: 0.3rem 0.3rem 0;
    border-top-color: #02104f;
}
.bs-tooltip-auto[x-placement^="bottom"] .arrow::before,
.bs-tooltip-bottom .arrow::before {
    bottom: 0;
    border-width: 0 0.4rem 0.4rem;
    border-bottom-color: #02104f;
}
.bs-tooltip-auto[x-placement^="left"] .arrow::before,
.bs-tooltip-bottom .arrow::before {
    bottom: 0;
    border-width: 0 0.4rem 0.4rem;
    border-bottom-color: #02104f;
}

.bs-tooltip-auto[x-placement^="right"] .arrow::before,
.bs-tooltip-bottom .arrow::before {
    bottom: 0;
    border-width: 0 0.4rem 0.4rem;
    border-bottom-color: #02104f;
}
.tooltip-inner {
    border: 1px solid #02104f;
    background-color: white;
    color: #231f20;
}

button.btn.whiteBG.btn-primary.btn-primary.btn-sm:hover {
    border-color: #00b19c;
    background: transparent;
    color: #231f20;
}
button.btn.whiteBG.btn-secondary.btn-sm:hover {
    border-color: #8dbac4;
    background: transparent;
    color: #8dbac4;
}

.multiselect-container > li > a > label {
    width: 100%;
}
.commodityFilterBtn .multiselect.dropdown-toggle::after {
    vertical-align: 0.255em !important;
    float: right;
    margin-top: 10px;
}
.bannerWarning {
    color: red;
    font-size: 10px;
}
#tsc_nav_1 {
    justify-content: space-between;
}
.flexEnd {
    justify-content: flex-end;
}
.deskFlex {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}
.posRel {
    position: relative;
}
.adjustSideBar {
    position: absolute;
    right: 81px;
    top: -4px;
}
li.nav-item.serli {
    margin-right: 4px;
    margin-top: 2px;
}
.nav-item .la-share-alt {
    font-size: 1.7em;
    color: #6c757d;
}
.noClick {
    pointer-events: none;
}
.cursorPointer {
    cursor: pointer;
}
.filterA {
    border: 1px solid #ddd;
    background-color: #fff;
    min-width: 220px;
    padding: 10px;
    position: absolute;
    right: 16px;
    z-index: 99;
}
.adjustSpan {
    display: block;
    margin-bottom: 5px;
}
.filterB {
    border: 1px solid #ddd;
    background-color: #fff;
    min-width: 220px;
    padding: 10px;
    position: absolute;
    right: 16px;
    z-index: 99;
}
.text-c {
    color: #3bcd3f;
}
div.slider-image {
    z-index: 999;
    cursor: pointer;
}
.desktop.navbar .dparent.nav-item:hover > a {
    background: #ebf1f3 !important;
}

.light-bg-1 {
    background: #f9f9f9;
}
.h28 {
    height: 28px;
}
.comIcon {
    font-size: 1.7em;
    color: #6c757d;
}
.pdfDim {
    width: 100%;
    height: 520px;
    overflow: auto;
    text-align: center;
}
.pta-3 {
    padding-top: 3px;
}
.lightBG-pad {
    background: #f4f4f4;
    padding: 5px;
}
.alignSub {
    vertical-align: sub;
}
.mr10 {
    margin-right: 10px;
}
.red {
    color: red;
}

a.disabledAnchor {
    pointer-events: none;
    cursor: default;
}
.serbox .descriptionSection p {
    text-align: left;
}
.nav-tabs .nav-item a.nav-link {
    height: 100%;
}
.openPopUp {
    cursor: pointer;
}
.aligntopQ.la-question1 {
    top: -3px !important;
}
.aligntopQ.la-question1:hover {
    top: -3px !important;
}
.checkClass {
    margin-top: 2px;
}
.scrollText {
    overflow-y: auto;
    height: 300px;
}
button#btnGdprAccept.disabled {
    background: #ccc;
    border-color: #cccc;
    color: #231f20;
}
.doc-viewer {
    position: relative;
}
#reportDownload.la.la-download {
    position: absolute;
    top: 7px;
    right: 30px;
    font-size: 21px;
    z-index: 91;
}

#ContactForm p {
    margin-bottom: 0px;
}
#ContactForm .alert {
    padding: 0px;
}
#reportDownload.la.la-download.adjustDownload {
    top: 12px;
    z-index: 9999;
    color: #fff;
}
ul#lightSlider2a {
    list-style: none;
    display: inline-block;
    padding-left: 5px;
}
ul#lightSlider2a li {
    float: left;
    margin-right: 20px;
    margin-bottom: 20px;
    margin-left: 0px;
}
ul#lightSlider2a .box.card-style {
    background: #fff !important;
}
.scrollTextViewer {
    overflow-y: auto;
    height: 458px;
}
#gdbrPopUp .modal-footer {
    border-top: 1px solid #ddd;
}
.back .favIcon {
    position: absolute;
    top: 10px;
    right: 16px;
}
.modal .modal-content {
    background-clip: border-box;
}
/* .alert {
    padding: 0px;
} */
.slimScrollDiv {
    padding-right: 15px;
    z-index: 0;
}
#QuestionForm .form-group {
    margin-bottom: 0px;
}
.serbox .descriptionSection a {
    min-height: 40px;
    display: block;
    word-break: break-word;
}
.modal-footer,
.modal-header {
    padding-top: 5px;
    padding-bottom: 5px;
}
.navbar-nav .disabledAnchor {
    background: #e8e8e8;
    color: #ccc;
}
.reportViewerIcons a {
    display: inline-block;
    width: 24px;
    height: 20px;
}

.reportViewerIcons a.shareIcon i:hover {
    color: #3bcd3f !important;
}
/* .reportViewerIcons a.createStrategyA {
    left: -5px;
}
.reportViewerIcons a.gotQuestionA {
    left: -9px;
}
.reportViewerIcons a.gotQuestionA .la-question1 {
    left: 2px;
    top: 0px;
}
.reportViewerIcons a.createStrategyA .la-bolt1 {
    left: 3px;
    top: 1px;
} */
/* .reportViewerIcons a.createStrategyA {
    left: -20px;
}
.reportViewerIcons a.gotQuestionA
{
    left: -9px;
}
.reportViewerIcons a.gotQuestionA .la-question1 {
    left: -14px;
    top: 3px;
} */

/* #tsc_nav_1 .dchild {
    top: 0;
    left: auto;
    right: 100%;
}

@media (min-width: 1366px){
    #tsc_nav_1 .dropdown:hover > .dropdown-menu {
        display: block;
        border-radius: 0px;
    }
}

#tsc_nav_1 .dropdown:hover > .dropdown-menu:hover > .dropdown-menu {
    display: block;
    border-radius: 0px;
} */
.catLand .catContainer {
    min-height: 299px;
    width: 100%;
}
nav#catTab .fav-tab-list li a {
    padding: 10px 9px !important;
}
a.filterAlpha.disabled {
    color: #b7b6b6;
    pointer-events: none;
}
a.disabled {
    pointer-events: none;
}
a.filterAlpha.fw-bold {
    color: #231f20 !important;
}
#loadTopCategory {
    min-height: 120px;
}
.slider {
    opacity: 0;
    visibility: hidden;
    transition: opacity 1s ease;
    -webkit-transition: opacity 1s ease;
}
.slider.slick-initialized {
    visibility: visible;
    opacity: 1;
}

/* .desktop #loadSolutions {
    height: 350px;
    overflow-y: auto;
}*/
.favtabCATSection .carouselSection .box {
    height: 240px !important;
}
.favTabSection .boxContainer .icons,
.searchGrid .boxContainer .icons {
    margin-right: 15px;
    position: absolute;
    bottom: 0px;
    right: 10px;
}
.hexaIcon {
    width: 17px;
}
.serbox .imageSection img {
    width: 120px;
    height: 50px;
}
.searchGrid .col-sm-2,
.fav-rows .col-sm-2 {
    margin-bottom: 20px;
}
.instructionsShare {
    display: block;
    font-size: 10px;
    padding: 8px;
    margin-bottom: 0px;
    padding-left: 0px;
}

.viewReport .access-limit.alert-info a {
    color: #fff;
    text-decoration: underline;
}

.noClickBreadcrumb {
    color: #8dbac4 !important;
    pointer-events: none;
}
.front .imageSection {
    display: flex;
    justify-content: center;
    align-items: center;
}

#strategyModal .modal-dialog {
    max-width: 100%;
    width: 90%;
}

.sortFilterPage {
    border-radius: 0px;
    border: 1px solid #bfbfbf;
    color: #676767;
}
.pubDateList {
    color: #667986 !important;
    font-size: 10px;
}
.pubDateList i {
    font-size: 14px;
    vertical-align: inherit;
}
#shareTools.fa.fa-share-alt {
    color: #fff;
}

#grid-data .col-sm-3 {
    max-width: 100%;
}
.grid-adjust .col-sm-3 {
    max-width: 298px !important;
}
/* #LoadMoreButton, */
/* .loadMore, */
.loadMoreData a {
    position: relative;
    padding-left: 30px;
    display: inline-block;
    height: 23px;
    line-height: 31px;
    margin-top: 0px;
    color: #231f20;
}
.loadMoreDownArrow {
    position: absolute;
    width: 1px;
    height: 0;
    margin: 0 10px;
    transition: all ease-out 0.2s;
    left: 0;
    background-color: #00b19c;
    height: 22px;
    top: 0px;
}

.loadMoreDownArrow::before,
.loadMoreDownArrow::after {
    width: 13px !important;
}

.loadMoreDownArrow::before {
    content: "";
    position: absolute;
    width: 0px;
    height: 1px;
    transform: rotate(45deg);
    right: -1px;
    bottom: 5px;
    transition: all ease-out 0.2s;
    transition-delay: 0.1s;
    background: #00b19c;
}
.loadMoreDownArrow::after {
    content: "";
    position: absolute;
    width: 0px;
    height: 1px;
    transform: rotate(-45deg);
    left: -1px;
    bottom: 5px;
    transition: all ease-out 0.2s;
    transition-delay: 0.1s;
}
.loadMoreDownArrow::before,
.loadMoreDownArrow::after,
.loadMoreDownArrow {
    background: #00b19c !important;
}
/* .searchTabbing .nav-link{
    padding-right: 1.2rem !important;
    padding-left: 1.2rem !important;
 } */
@media screen and (max-width: 1250px) {
    .slider-image img {
        width: 100% !important;
    }
    div.slider-image {
        margin-left: 16px;
    }
    .slick-slide {
        margin-left: 10px;
    }
    .slick-current:after {
        height: 97%;
    }
}
.dropdown-toggle::after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 4px solid;
    border-right: 4px solid transparent;
    border-bottom: 0;
    border-left: 4px solid transparent;
}
article#compareSection {
    width: 100%;
    margin: 0 auto;
    position: relative;
}
#compareSection ul {
    display: flex;
    top: 0px;
    z-index: 10;
    padding-bottom: 14px;
}
#compareSection li {
    list-style: none;
    flex: 1;
}
#compareSection li:last-child {
    border-right: 1px solid #ddd;
}
#compareSection button.resBtn {
    width: 100%;
    border: 1px solid #ddd;
    border-right: 0;
    border-top: 0;
    padding: 10px;
    background: #fff;
    font-size: 14px;
    font-weight: bold;
    height: 60px;
    color: #999;
}
#compareSection li.active button.resBtn {
    background: #f5f5f5;
    color: #231f20;
}
#compareSection table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
}
#compareSection th {
    background: #f5f5f5;
    display: none;
}
#compareSection td,
#compareSection th {
    height: 53px;
}
#compareSection td,
#compareSection th {
    border: 1px solid #ddd;
    padding: 10px;
    empty-cells: show;
    font-size: 12px !important;
}
#compareSection td,
#compareSection th {
    text-align: left;
}
#compareSection td + td,
#compareSection th + th {
    text-align: center;
    display: none;
}
#compareSection td.default {
    display: table-cell;
}
#compareSection .bg-purple {
    border-top: 3px solid #007365;
}
#compareSection .bg-blue {
    border-top: 3px solid rgb(67, 203, 73);
}
#compareSection .sep {
    background: #f5f5f5;
    font-weight: bold;
    height: 5px;
    padding: 0px;
}
#compareSection .txt-l {
    font-size: 28px;
    font-weight: bold;
}
#compareSection .txt-top {
    position: relative;
    top: -9px;
    left: -2px;
}
#compareSection .tick {
    font-size: 18px;
    color: #007365;
    font-family: arial, Segoe UI Symbol;
}
#compareSection .tickNum {
    font-size: 16px;
    color: #007365;
}
#compareSection .hide {
    border: 0;
    background: none;
}
#compareSection td:nth-child(2),
#compareSection td:nth-child(3),
#compareSection td:nth-child(4),
#compareSection td:nth-child(8),
#compareSection td:nth-child(9),
#compareSection td:nth-child(10) {
    background-color: #e9f5e9;
}
#compareSection th {
    position: relative;
    overflow: hidden;
}
#compareSection .selectedPlan {
    font-size: 10px;
    font-weight: normal;
    background: #007365;
    color: #fff;
    position: absolute;
    top: 6px;
    left: -17px;
    transform: rotate(-47deg);
    padding: 5px 15px;
    text-align: center;
}

#compareSection ul {
    display: none;
}
#compareSection td,
#compareSection th {
    display: table-cell !important;
}
#compareSection td,
#compareSection th {
    width: 20%;
}
#compareSection td + td,
#compareSection th + th {
    width: auto;
}

#compareSection tbody > tr:last-child > td {
    border-bottom: 0;
    border-left: 0;
    border-right: 0;
    background-color: #fff !important;
}
#profileSection .avatar {
    border: 0.3rem solid rgba(#fff, 0.3);
    margin-bottom: 1rem;
    max-width: 9rem;
}
#profileSection article {
    width: 100%;
    margin: 0 auto;
    position: relative;
}

#profileSection ul {
    display: flex;
    top: 0px;
    z-index: 10;
    padding-bottom: 14px;
}
#profileSection li {
    list-style: none;
    flex: 1;
}
#profileSection li:last-child {
    border-right: 1px solid #ddd;
}
#profileSection button {
    width: 100%;
    border: 1px solid #ddd;
    border-right: 0;
    border-top: 0;
    padding: 10px;
    background: #fff;
    font-size: 14px;
    font-weight: bold;
    height: 60px;
    color: #999;
}
#profileSection li.active button {
    background: #f5f5f5;
    color: #231f20;
}
#profileSection table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
}
#profileSection th {
    background: #f5f5f5;
    display: none;
}
#profileSection td,
#profileSection th {
    height: 53px;
}
#profileSection td,
#profileSection th {
    border: 1px solid #ddd;
    padding: 10px;
    empty-cells: show;
    font-size: 12px !important;
}
#profileSection td,
#profileSection th {
    text-align: left;
}
#profileSection td + td,
#profileSection th + th {
    text-align: center;
    display: none;
}
#profileSection td.default {
    display: table-cell;
}
#profileSection .bg-purple {
    border-top: 3px solid #007365;
}
#profileSection .bg-blue {
    border-top: 3px solid rgb(67, 203, 73);
}
#profileSection .sep {
    background: #f5f5f5;
    font-weight: bold;
}
#profileSection .txt-l {
    font-size: 28px;
    font-weight: bold;
}
#profileSection .txt-top {
    position: relative;
    top: -9px;
    left: -2px;
}
/*IE HACK FONT ARIAL*/
#profileSection .tick {
    font-size: 18px;
    color: #007365;
    font-family: arial, Segoe UI Symbol;
}

#profileSection ul {
    display: none;
}
#profileSection td,
#profileSection th {
    display: table-cell !important;
}
#profileSection td,
#profileSection th {
    width: 20%;
}
#profileSection td + td,
#profileSection th + th {
    width: auto;
}
#profileSection .sep {
    height: 5px;
    padding: 0px;
}
#profileSection .tickNumber {
    font-size: 16px;
    color: #007365;
}

#profileSection .faqSection a {
    text-decoration: underline;
    position: relative;
}
#profileSection .faqSection i {
    font-size: 17px;
    position: absolute;
    left: -20px;
}
#profileSection td:nth-child(2),
#profileSection td:nth-child(4) {
    background-color: #e9f5e9;
}
span.multiselect-native-select {
    position: relative;
}
span.multiselect-native-select select {
    border: 0 !important;
    clip: rect(0 0 0 0) !important;
    height: 1px !important;
    margin: -1px -1px -1px -3px !important;
    overflow: hidden !important;
    padding: 0 !important;
    position: absolute !important;
    width: 1px !important;
    left: 50%;
    top: 30px;
}
.multiselect-container {
    position: absolute;
    list-style-type: none;
    margin: 0;
    padding: 0;
}
.multiselect-container .input-group {
    margin: 5px;
}
.multiselect-container .multiselect-reset .input-group {
    width: 93%;
}
.multiselect-container > li {
    padding: 0;
}
.multiselect-container > li > a.multiselect-all label {
    font-weight: 700;
}
.multiselect-container > li.multiselect-group label {
    margin: 0;
    padding: 3px 20px;
    height: 100%;
    font-weight: 700;
}
.multiselect-container > li.multiselect-group-clickable label {
    cursor: pointer;
}
.multiselect-container > li > a {
    padding: 0;
}
.multiselect-container > li > a > label {
    margin: 0;
    height: 100%;
    cursor: pointer;
    font-weight: 400;
    padding: 3px 20px 3px 40px;
    padding-top: 10px !important;
}
.multiselect-container > li > a > label.checkbox,
.multiselect-container > li > a > label.radio {
    margin: 0;
}
.multiselect-container > li > a > label > input[type="checkbox"] {
    margin-bottom: 5px;
}
.btn-group > .btn-group:nth-child(2) > .multiselect.btn {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}
.form-inline .multiselect-container label.checkbox,
.form-inline .multiselect-container label.radio {
    padding: 3px 20px 3px 40px;
}
.form-inline .multiselect-container li a label.checkbox input[type="checkbox"],
.form-inline .multiselect-container li a label.radio input[type="radio"] {
    margin-left: -20px;
    margin-right: 0;
}

#optionBy {
    border-radius: 0px;
    border: 1px solid #bfbfbf;
    color: #676767;
}
.sortFilterPage,
#optionBy {
    height: 26px;
}
.optionSort .multiselect-native-select > .btn-group > .btn-default {
    color: #676767;
    background-color: #fff;
    border-color: #bfbfbf;
    padding: 0px 5px 0px 5px;
    border-radius: 0px;
}
.optionSort .multiselect.dropdown-toggle.btn.btn-default {
    background: transparent;
    border: 1px solid #bfbfbf;
    border-radius: 0;
    color: #676767;
}
.optionSort .multiselect-native-select .btn-group {
    margin-top: -2px;
}
.secondaryOption .serbtn {
    min-width: 24px;
}
.secondaryOption .arrowIcon {
    position: absolute;
    top: 6px;
    right: 7px;
}

.secondaryOption .serbtn {
    min-width: 24px;
    position: absolute;
    top: 5px;
    right: 1px;
    background: #ffff;
    display: inline-block;
    height: 16px;
}

.secondaryOption input {
    height: 28px;
    border-color: #bfbfbf;
}
.secondaryOption .arrowIcon {
    position: absolute;
    top: 0px;
    right: 2px;
}

.optionSort .multiselect.dropdown-toggle.btn.btn-default {
    width: 120px;
    text-overflow: ellipsis;
    overflow: hidden;
}
.border-right-middle::after {
    content: " ";
    width: 1px;
    height: 200px;
    background: #eee;
    position: absolute;
    top: 50%;
    right: 0px;
    transform: translateY(-50%);
}

#shareContentSection .card {
    min-height: 300px;
    display: flex;
}
#shareContentSection .centerTop {
    margin-top: 30px;
}
#shareContentSection .txtHeading {
    font-size: 32px;
    margin-bottom: 29px;
}
#shareContentSection p {
    width: 90%;
    margin: 0 auto;
}
#shareContentSection button {
    position: relative;
    padding-left: 30px;
}
#shareContentSection i {
    font-size: 24px;
    position: absolute;
    left: 4px;
}
#shareContentSection img {
    height: 300px;
}

.insideSocial {
    text-align: center;
    color: #231f20;
    min-height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    flex-direction: column;
    padding: 10px;
    border-right: 1px solid #ddd;
    transition: 0.5s all;
}
.insideSocial i {
    font-size: 2.4em;
    padding-bottom: 4px;
}
.insideSocial:hover {
    background-color: rgb(214, 227, 231) !important;
}
.insideSocial p {
    font-size: 10px;
}
.insideSocial:last-of-type {
    border-right: none;
}
#cpyLink {
    vertical-align: middle;
    padding-left: 10px;
}
.instructionsShare {
    display: none;
}
i.las.la-dollar-sign.costCal {
    border-radius: 20px;
    border: 1px solid #667986;
    text-align: center;
    height: 20px;
    width: 20px;
    line-height: 18px;
    font-size: 14px;
}
i.las.la-dollar-sign.costCal:hover {
    border-color: #3bcd3f !important;
}
/*Jan Release code*/
.helloGreen {
    color: #3bcd3f !important;
}
.desktop.navbar .input-group {
    width: 674px;
    justify-content: flex-end;
    margin-right: 0px;
    align-items: center;
}
.navbar-brand img {
    width: 90px;
    margin-top: 11px;
}
.plans-dialog .card {
    width: 100%;
    box-shadow: 1px 0px 2px 0px rgb(130 130 130 / 28%),
        0 0px 14px 0 rgba(0, 0, 0, 0.05);
}
.plans-dialog .row {
    flex-wrap: nowrap !important;
}

.plans-dialog .card-body {
    padding: 15px;
    padding-top: 5px;
    padding-right: 5px;
    padding-left: 5px;
    padding-bottom: 5px;
}

.plans-dialog div#header {
    min-height: 90px;
}

.plans-dialog .table {
    color: #231f20;
    font-family: "Montserrat", serif;
    font-size: 14px;
    margin-bottom: 0px;
}

.plans-dialog .table th img {
    width: 97px;
}

.plans-dialog .table td,
.plans-dialog .table th {
    padding: 5px;
    border-top: 1px solid #d8e4e7;
    font-weight: 400;
    min-height: 100px !important;
    height: 45px !important;
    text-align: center;
    border: none;
    font-size: 15px;
    vertical-align: middle;
    color: #231f20;
}

.plans-dialog .card.defaultPlan {
    background: #ebf0f2;
    opacity: 0.8;
}
.plans-dialog .card.activePlan {
    background: #beefeb !important;
}

.plans-dialog .card.activePlan span.textSec {
    background: #02104f;
    color: #fff;
    font-size: 10px;
    padding: 5px 10px;
    vertical-align: middle;
    text-align: right;
    float: right;
    /* position: absolute;
    top:15px;
    right:5px; */
}
.plans-dialog td i.las.la-check,
.plans-dialog td i.las.la-times {
    color: #231f20;
    font-size: 1.8em;
}
.plans-dialog th i {
    font-size: 18px;
}
.plans-dialog .table th {
    height: auto !important;
    vertical-align: middle;
    font-size: 16px;
}

.plans-dialog .buySec a {
    font-size: 16px;
    text-align: center;
    display: block;
    position: relative;
}
.plans-dialog .buySec i.la-question-circle {
    font-size: 20px;
    vertical-align: baseline;
}
.plans-dialog button.btn.btn-block.btn-primary.secondary {
    background: #007365;
}
.plans-dialog .table td,
.plans-dialog .table th {
    padding: 4px;
}
.plans-dialog .modal-body {
    padding-top: 5px !important;
}
.plans-dialog .modal-header {
    border-bottom: none !important;
}
.plans-dialog h5.modal-title {
    padding-left: 0.5rem !important;
}
nav.desktop a.navbar-brand {
    position: relative;
}
nav.desktop a.navbar-brand span.tierName {
    margin-left: 4px;
    color: #00b19c;
    font-size: 16px;
    position: absolute;
    bottom: 0px;
    left: 90px;
}

.plans-dialog .modal-dialog {
    max-width: 87% !important;
    margin: 1.75rem auto;
}
.plans-dialog .modal-body {
    max-width: 100%;
    overflow-x: auto;
}
/* .plans-dialog .scrollDiv {
    width: 1124px;
} */
/* .plans-dialog .modal-body{
    width: 1160px !important;
    overflow-x:auto;
}*/
.plans-dialog h4 {
    font-size: 21px;
}
.plans-dialog .modal-body .col-sm-3 {
    -ms-flex: 0 0 25% !important;
    flex: 0 0 25% !important;
}
.plans-dialog .modal-body .col-sm-4 {
    -ms-flex: 0 0 33.333333% !important;
    flex: 0 0 33.333333% !important;
}
.plans-dialog .modal-body .col-sm-8 {
    -ms-flex: 0 0 66.666667% !important;
    flex: 0 0 66.666667% !important;
}
.posRel {
    position: relative;
}
.plans-dialog span.tierText {
    position: absolute;
    bottom: 4px;
    left: 112px;
    color: #8dbac4;
    font-size: 16px;
}
.alignCheck .las.la-check,
.alignCheck .las.la-times,
.alignCheck .alignBtn {
    margin-left: -180px;
}

.plans-dialog.profileSection span.tierText.profileText {
    position: absolute;
    bottom: 2px;
    left: 118px;
    color: #00b19c;
    font-size: 16px;
}
.plans-dialog .header h4 {
    color: #231f20 !important;
}
.essentialColor {
    color: #8dbac4 !important;
}
.professionalColor {
    color: #00b19c !important;
}
.ultimateColor {
    color: #007365 !important;
}
.plans-dialog.profileSection .table td,
.plans-dialog .table th {
    padding: 9px;
}
.plans-dialog.profileSection .las.la-eye,
.plans-dialog.profileSection .las.la-cloud-download-alt {
    font-size: 24px;
    vertical-align: bottom;
}
#traiffTable th {
    border-top: none;
}
.infoDetail h1 {
    font-size: 16px;
    margin-bottom: 0px;
}
.infoDetail .iconDiv {
    align-items: center;
    justify-content: start;
    position: relative;
}
.d-flex.iconDiv::after {
    content: " ";
    width: 200px;
    position: absolute;
    height: 1px;
    background: #ddd;
    bottom: 10px;
}
.infoDetail .iconDiv i {
    font-size: 2.2em;
    text-align: center;
    display: block;
    color: #007365;
}
.infoDetail .iconDiv p {
    font-size: 2em;
    color: #00b19c;
}
.halfBorder::after {
    content: " ";
    width: 1px;
    height: 140px;
    background: #ececec;
    position: absolute;
    top: 23%;
    right: 10px;
}

.footerBanner.dashBanner {
    background-image: url(../images/bg2.jpg);
    height: auto;
    width: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    color: #fff;
    padding: 10px;
    font-size: 2.5em;
    padding-left: 20px;
    padding-right: 80px;
    line-height: auto;
    font-weight: normal;
    padding-top: 15px;
    padding-bottom: 15px;
    text-align: center;
}

#allPlans.plans-dialog span.tierText {
    position: absolute;
    bottom: 4px;
    left: 109px !important;
    color: #00b19c;
    font-size: 16px;
}
#allPlans.plans-dialog .modal-dialog {
    max-width: 95% !important;
    margin: 1.75rem auto;
}

.customToggle.btn-toggle {
    margin: 0 4rem;
    padding: 0;
    position: relative;
    border: none;
    height: 1.5rem;
    width: 3rem;
    border-radius: 1.5rem;
    color: #6b7381;
    background: #8dbac4;
}
.customToggle.btn-toggle:focus,
.customToggle.btn-toggle.focus,
.customToggle.btn-toggle:focus.active,
.customToggle.btn-toggle.focus.active {
    outline: none;
}
.customToggle.btn-toggle:before,
.customToggle.btn-toggle:after {
    line-height: 1.5rem;
    width: 4rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: absolute;
    bottom: 0;
    transition: opacity 0.25s;
}
.customToggle.btn-toggle:before {
    content: "\f00c";
    font-family: "Line Awesome Free";
    font-weight: 900;
    left: -4rem;
}
.customToggle.btn-toggle:after {
    content: "On";
    right: -4rem;
    opacity: 0.5;
}
.customToggle.btn-toggle > .handle {
    position: absolute;
    top: 0.1875rem;
    left: 0.1875rem;
    width: 1.125rem;
    height: 1.125rem;
    border-radius: 1.125rem;
    background: #fff;
    transition: left 0.25s;
}
.customToggle.btn-toggle.active {
    transition: background-color 0.25s;
}
.customToggle.btn-toggle.active > .handle {
    left: 1.6875rem;
    transition: left 0.25s;
}
.customToggle.btn-toggle.active:before {
    opacity: 0.5;
}
.customToggle.btn-toggle.active:after {
    opacity: 1;
}
.customToggle.btn-toggle.btn-sm:before,
.customToggle.btn-toggle.btn-sm:after {
    line-height: -0.5rem;
    color: #fff;
    letter-spacing: 0.75px;
    left: 0.4125rem;
    width: 2.325rem;
}
.customToggle.btn-toggle.btn-sm:before {
    text-align: right;
}
.customToggle.btn-toggle.btn-sm:after {
    text-align: left;
    opacity: 0;
}
.customToggle.btn-toggle.btn-sm.active:before {
    opacity: 0;
}
.customToggle.btn-toggle.btn-sm.active:after {
    opacity: 1;
}
.customToggle.btn-toggle.btn-xs:before,
.customToggle.btn-toggle.btn-xs:after {
    display: none;
}
.customToggle.btn-toggle:before,
.customToggle.btn-toggle:after {
    color: #6b7381;
}
.customToggle.btn-toggle.active {
    background-color: #3bcd3f;
}
.customToggle.btn-toggle.btn-lg {
    margin: 0 5rem;
    padding: 0;
    position: relative;
    border: none;
    height: 2.5rem;
    width: 5rem;
    border-radius: 2.5rem;
}
.customToggle.btn-toggle.btn-lg:focus,
.customToggle.btn-toggle.btn-lg.focus,
.customToggle.btn-toggle.btn-lg:focus.active,
.customToggle.btn-toggle.btn-lg.focus.active {
    outline: none;
}
.customToggle.btn-toggle.btn-lg:before,
.customToggle.btn-toggle.btn-lg:after {
    line-height: 2.5rem;
    width: 5rem;
    text-align: center;
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: absolute;
    bottom: 0;
    transition: opacity 0.25s;
}
.customToggle.btn-toggle.btn-lg:before {
    content: "\f00c";
    font-family: "Line Awesome Free";
    font-weight: 900;
    left: -5rem;
}
.customToggle.btn-toggle.btn-lg:after {
    content: "On";
    right: -5rem;
    opacity: 0.5;
}
.customToggle.btn-toggle.btn-lg > .handle {
    position: absolute;
    top: 0.3125rem;
    left: 0.3125rem;
    width: 1.875rem;
    height: 1.875rem;
    border-radius: 1.875rem;
    background: #fff;
    transition: left 0.25s;
}
.customToggle.btn-toggle.btn-lg.active {
    transition: background-color 0.25s;
}
.customToggle.btn-toggle.btn-lg.active > .handle {
    left: 2.8125rem;
    transition: left 0.25s;
}
.customToggle.btn-toggle.btn-lg.active:before {
    opacity: 0.5;
}
.customToggle.btn-toggle.btn-lg.active:after {
    opacity: 1;
}
.customToggle.btn-toggle.btn-lg.btn-sm:before,
.customToggle.btn-toggle.btn-lg.btn-sm:after {
    line-height: 0.5rem;
    color: #fff;
    letter-spacing: 0.75px;
    left: 0.6875rem;
    width: 3.875rem;
}
.customToggle.btn-toggle.btn-lg.btn-sm:before {
    text-align: right;
}
.customToggle.btn-toggle.btn-lg.btn-sm:after {
    text-align: left;
    opacity: 0;
}
.customToggle.btn-toggle.btn-lg.btn-sm.active:before {
    opacity: 0;
}
.customToggle.btn-toggle.btn-lg.btn-sm.active:after {
    opacity: 1;
}
.customToggle.btn-toggle.btn-lg.btn-xs:before,
.customToggle.btn-toggle.btn-lg.btn-xs:after {
    display: none;
}
.customToggle.btn-toggle.btn-sm {
    margin: 0 0.5rem;
    padding: 0;
    position: relative;
    border: none;
    height: 1.5rem;
    width: 3rem;
    border-radius: 1.5rem;
}
.customToggle.btn-toggle.btn-sm:focus,
.customToggle.btn-toggle.btn-sm.focus,
.customToggle.btn-toggle.btn-sm:focus.active,
.customToggle.btn-toggle.btn-sm.focus.active {
    outline: none;
}
.customToggle.btn-toggle.btn-sm:before,
.customToggle.btn-toggle.btn-sm:after {
    line-height: 1.5rem;
    width: 0.5rem;
    text-align: center;
    font-weight: 600;
    font-size: 0.55rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: absolute;
    bottom: 0;
    transition: opacity 0.25s;
}
.customToggle.btn-toggle.btn-sm:before {
    content: "\f00d";
    font-family: "Line Awesome Free";
    font-weight: 900;
    left: 0.3rem !important;
    font-size: 17px;
}
.customToggle.btn-toggle.btn-sm:after {
    content: "\f00c";
    font-family: "Line Awesome Free";
    font-weight: 900;
    right: -0.5rem;
    opacity: 0.5;
    font-size: 17px;
}
.customToggle.btn-toggle.btn-sm > .handle {
    position: absolute;
    top: 0.1875rem;
    left: 0.1875rem;
    width: 1.125rem;
    height: 1.125rem;
    border-radius: 1.125rem;
    background: #fff;
    transition: left 0.25s;
}
.customToggle.btn-toggle.btn-sm.active {
    transition: background-color 0.25s;
}
.customToggle.btn-toggle.btn-sm.active > .handle {
    left: 1.6875rem;
    transition: left 0.25s;
}
.customToggle.btn-toggle.btn-sm.active:before {
    opacity: 0.5;
}
.customToggle.btn-toggle.btn-sm.active:after {
    opacity: 1;
}
.customToggle.btn-toggle.btn-sm.btn-sm:before,
.customToggle.btn-toggle.btn-sm.btn-sm:after {
    line-height: -0.5rem;
    color: #fff;
    letter-spacing: 0.75px;
    left: 0.4125rem;
    width: 2.325rem;
}
.customToggle.btn-toggle.btn-sm.btn-sm:before {
    text-align: right;
}
.customToggle.btn-toggle.btn-sm.btn-sm:after {
    text-align: left;
    opacity: 0;
}
.customToggle.btn-toggle.btn-sm.btn-sm.active:before {
    opacity: 0;
}
.customToggle.btn-toggle.btn-sm.btn-sm.active:after {
    opacity: 1;
}
.customToggle.btn-toggle.btn-sm.btn-xs:before,
.customToggle.btn-toggle.btn-sm.btn-xs:after {
    display: none;
}
.customToggle.btn-toggle.btn-xs {
    margin: 0 0;
    padding: 0;
    position: relative;
    border: none;
    height: 1rem;
    width: 2rem;
    border-radius: 1rem;
}
.customToggle.btn-toggle.btn-xs:focus,
.customToggle.btn-toggle.btn-xs.focus,
.customToggle.btn-toggle.btn-xs:focus.active,
.customToggle.btn-toggle.btn-xs.focus.active {
    outline: none;
}
.customToggle.btn-toggle.btn-xs:before,
.customToggle.btn-toggle.btn-xs:after {
    line-height: 1rem;
    width: 0;
    text-align: center;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: absolute;
    bottom: 0;
    transition: opacity 0.25s;
}
.customToggle.btn-toggle.btn-xs:before {
    content: "\f00c";
    font-family: "Line Awesome Free";
    font-weight: 900;
    left: 0;
}
.customToggle.btn-toggle.btn-xs:after {
    content: "\f00c";
    font-family: "Line Awesome Free";
    font-weight: 900;
    right: 0;
    opacity: 0.5;
}
.customToggle.btn-toggle.btn-xs > .handle {
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 0.75rem;
    background: #fff;
    transition: left 0.25s;
}
.customToggle.btn-toggle.btn-xs.active {
    transition: background-color 0.25s;
}
.customToggle.btn-toggle.btn-xs.active > .handle {
    left: 1.125rem;
    transition: left 0.25s;
}
.customToggle.btn-toggle.btn-xs.active:before {
    opacity: 0.5;
}
.customToggle.btn-toggle.btn-xs.active:after {
    opacity: 1;
}
.customToggle.btn-toggle.btn-xs.btn-sm:before,
.customToggle.btn-toggle.btn-xs.btn-sm:after {
    line-height: -1rem;
    color: #fff;
    letter-spacing: 0.75px;
    left: 0.275rem;
    width: 1.55rem;
}
.customToggle.btn-toggle.btn-xs.btn-sm:before {
    text-align: right;
}
.customToggle.btn-toggle.btn-xs.btn-sm:after {
    text-align: left;
    opacity: 0;
}
.customToggle.btn-toggle.btn-xs.btn-sm.active:before {
    opacity: 0;
}
.customToggle.btn-toggle.btn-xs.btn-sm.active:after {
    opacity: 1;
}
.customToggle.btn-toggle.btn-xs.btn-xs:before,
.customToggle.btn-toggle.btn-xs.btn-xs:after {
    display: none;
}
.customToggle.btn-toggle.btn-secondary {
    color: #6b7381;
    background: #8dbac4;
}
.customToggle.btn-toggle.btn-secondary:before,
.customToggle.btn-toggle.btn-secondary:after {
    color: #6b7381;
}
.customToggle.btn-toggle.btn-secondary.active {
    background-color: #ff8300;
}
.popover {
    padding: 10px;
    width: 500px;
}
.popover {
    border: 1px solid #02104f;
}
.btn-primary.disabled,
.btn-primary:disabled {
    color: #fff;
    background-color: #8dbac4;
    border-color: #8dbac4;
}
.popover-header {
    padding: 0.3rem 0.15rem;
    background-color: #fff;
    border-bottom: none;
}
.popover-body {
    padding: 0.5rem 0.15rem;
    color: #231f20;
}
.customToggle.btn-toggle.btn-sm {
    margin-left: 0px;
}
.bs-popover-auto[x-placement^="bottom"] .arrow::after,
.bs-popover-bottom .arrow::after {
    display: none;
}
.bs-popover-auto[x-placement^="bottom"] .arrow::before,
.bs-popover-bottom .arrow::before {
    display: none;
}
.closePopOver {
    position: absolute;
    top: 5px;
    right: 10px;
}
.profileHeader {
    background: #fff;
    padding-top: 0px;
    padding-bottom: 15px;
}
.profileHeader .userIcon {
    font-size: 24px;
    vertical-align: top;
    display: inline-block;
    margin-top: -5px;
    color: #667986;
}

/*Range Slider*/

.irs--round .irs-grid-text {
    color: #d6e3e7;
    font-size: 12px;
}
.irs--round .irs-line {
    top: 36px;
    height: 4px;
    background-color: #d6e3e7;
    border-radius: 0;
}
.irs--round .irs-min,
.irs--round .irs-max {
    display: none;
}
.irs--round .irs-handle {
    top: 31px;
    width: 15px;
    height: 15px;
    border: 5px solid #007365;
    background-color: #007365;
    border-radius: 15px;
    box-shadow: none;
}
.irs--round .irs-bar {
    top: 36px;
    height: 10px;
    background-color: #007365;
}
.irs--round .irs-handle.state_hover,
.irs--round .irs-handle:hover {
    background-color: #007365;
    cursor: pointer;
}
.irs--round .irs-from,
.irs--round .irs-to,
.irs--round .irs-single {
    font-size: 12px;
    line-height: 1;
    text-shadow: none;
    padding: 10px 5px;
    background-color: transparent;
    color: #007365;
    border-radius: none;
    font-size: 14px;
    font-weight: bold;
}
.irs--round .irs-from:before,
.irs--round .irs-to:before,
.irs--round .irs-single:before {
    display: none;
}
.irs--round.irs-with-grid {
    height: 63px;
}

span.irs-line::before {
    content: " ";
    width: 10px;
    height: 10px;
    background: #fff;
    position: absolute;
    top: 0px;
    left: -4px;
    z-index: 9;
}
span.irs-line::after {
    content: " ";
    width: 10px;
    height: 10px;
    background: #fff;
    position: absolute;
    top: 0px;
    right: -2px;
    z-index: 9;
}
.irs--round .irs-handle {
    z-index: 99;
}
div.weightModal h2 {
    font-size: 14px;
    margin-bottom: 0px;
    color: #007365;
}
div.weightModal .totalSec {
    justify-content: flex-start;
    align-items: center;
}
div.weightModal .totalSec label {
    margin-right: 10px;
    margin-bottom: 0px;
}
div.weightModal .totalSec h4 {
    font-size: 20px;
}
div.weightModal .totalSec input {
    border-color: #007365;
    color: #007365;
    font-size: 21px;
    width: 80px;
    height: 37px;
    text-align: right;
    font-weight: bold;
}
.weightTable th {
    vertical-align: middle;
    padding: 5px !important;
    font-weight: normal !important;
    border-bottom-width: 1px !important;
}
.cellHighlighter {
    background: #dff6f5 !important;
    color: #00b0a0 !important;
    min-width: 70px !important;
}
.table {
    font-size: 12px !important;
}
.weightModal .form-control[readonly] {
    background-color: #fff !important;
    opacity: 1;
}
.btn-outline-secondaryW {
    background: transparent;
    border-radius: 0px;
    border-color: #fff;
    color: #fff;
}
.btn-outline-secondaryW:hover {
    background: #fff;
    border-radius: 0px;
    border-color: transparent;
    color: #00b19c;
}
.keySupSection {
    background-image: url(../images/bg4.jpg);
    background-size: contain;
    background-repeat: no-repeat;
    height: 250px;
    width: 310px;
    text-align: center;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px;
    margin-left: 12px;
    padding: 15px;
    padding-right: 30px;
    flex-direction: column;
    margin-top: -10px !important;
}
.keySupSection h2 {
    font-size: 26px;
    text-align: center;
    display: block;
    margin-bottom: 15px;
}
#snackbar {
    visibility: hidden; /* Hidden by default. Visible on click */
    min-width: 250px; /* Set a default minimum width */
    margin-left: -125px; /* Divide value of min-width by 2 */
    background-color: #007365;
    color: #fff; /* White text color */
    text-align: center; /* Centered text */
    border-radius: 0px; /* Rounded borders */
    padding: 16px; /* Padding */
    position: fixed; /* Sit on top of the screen */
    z-index: 1; /* Add a z-index if needed */
    left: 50%; /* Center the snackbar */
    bottom: 30px; /* 30px from the bottom */
}
.size14 {
    font-size: 14px;
}
.showToast {
    visibility: visible !important; /* Show the snackbar */
}

span.Rheading {
    display: flex !important;
    justify-content: flex-start;
    align-items: center;
}
.Rheading .icons {
    margin-left: 15px;
    align-items: center;
    display: flex !important;
}
.icons a {
    min-height: 33px;
}
.icons a {
    position: relative;
    padding: 5px 4px;
    color: #667986 !important;
}

/*ICONS*/
.Rheading .icons .la-bolt1 {
    background: url(../images/strategy.svg) no-repeat;
    width: 17px;
    height: 17px;
    display: inline-block;
    position: static;
    left: auto;
    top: auto;
}

.Rheading .icons .la-question1 {
    background: url(../images/question.svg) no-repeat;
    width: 17px;
    height: 17px;
    display: inline-block;
    position: static;
    left: auto;
    top: auto;
}

.Rheading .icons .la-question1:hover {
    background: url(../images/question-hover.svg) no-repeat;
    width: 17px;
    height: 17px;
    display: inline-block;
    position: static;
    left: auto;
    top: auto;
}
.Rheading .icons .la-bolt1:hover {
    background: url(../images/strategy-hover.svg) no-repeat;
    width: 17px;
    height: 17px;
    display: inline-block;
    position: static;
    left: auto;
    top: auto;
}

.la-bolt1 {
    background: url(../images/strategy.svg) no-repeat;
    width: 19px;
    height: 19px;
    display: inline-block;
    position: static;
    left: auto;
    top: auto;
}
.la-bolt1:hover {
    background: url(../images/strategy-hover.svg) no-repeat;
    width: 19px;
    height: 19px;
    display: inline-block;
    position: static;
    left: auto;
    top: auto;
}
.la-question1 {
    background: url(../images/question.svg) no-repeat;
    width: 19px;
    height: 19px;
    display: inline-block;
    position: static;
    left: auto;
    top: auto;
}
.la-question1:hover {
    background: url(../images/question-hover.svg) no-repeat;
    width: 19px;
    height: 19px;
    display: inline-block;
    position: static;
    left: auto;
    top: auto;
}
.catContainer:hover .icons {
    display: flex !important;
    justify-content: flex-end;
}
.icons a:not(:last-child):after {
    content: "";
    position: absolute;
    width: 1px;
    height: 14px;
    background: #ccc;
    right: 0;
    top: 8px;
}

.favTabSection .boxContainer .icons,
.searchGrid .boxContainer .icons {
    margin-right: 0px;
    position: static;
    bottom: 0px;
    right: 0px;
    display: flex;
    justify-content: flex-end;
}

/* .reportViewerIcons a.createStrategyA .la-bolt1 {
    left: 3px;
    top: 1px;
    position: absolute;
} */

/* .reportViewerIcons a.gotQuestionA .la-question1 {
    left: 2px;
    top: 2px;
    position: absolute;
} */

.reportViewerIcons.icons a:not(:last-child):after {
    content: "";
    position: absolute;
    width: 1px;
    height: 14px;
    background: #ccc;
    right: 0;
    top: 8px;
}

i.las.la-dollar-sign.costCal {
    border-radius: 20px;
    border: 1px solid #667986;
    text-align: center;
    height: 20px;
    width: 20px;
    line-height: 18px;
    font-size: 14px;
    margin-left: 5px;
    margin-right: 5px;
}
.favTabSection .boxContainer .icons,
.searchGrid .boxContainer .icons {
    margin-right: 0px;
    position: absolute;
    bottom: -5px;
    right: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.favTabSection .icons a,
.searchGrid .icons a {
    min-height: auto;
}
.irs--round .irs-line {
    top: 36px;
    height: 10px;
    background-color: #d6e3e7;
    border-radius: 0;
}
.irs--round .irs-handle {
    top: 31px;
    width: 20px;
    height: 20px;
    border: 5px solid #007365;
    background-color: #007365;
    border-radius: 15px;
    box-shadow: none;
}
.irs-grid-pol.small {
    height: 5px !important;
}
.irs-grid-pol {
    position: absolute;
    top: 0;
    width: 1px;
    height: 10px;
    background: #02104f;
}

.irs-grid-text {
    position: absolute;
    bottom: -5px;
    left: 0;
    white-space: nowrap;
    text-align: center;
    font-size: 9px;
    line-height: 9px;
    padding: 0 3px;
    color: #231f20;
}
span.irs-line::after {
    content: " ";
    width: 15px;
    height: 10px;
    background: #fff;
    position: absolute;
    top: 0px;
    right: -5px;
    z-index: 9;
}
span.irs-line::before {
    content: " ";
    width: 10px;
    height: 10px;
    background: #fff;
    position: absolute;
    top: 0px;
    left: -1px;
    z-index: 9;
}
.irs-grid {
    bottom: -7px;
}
.irs--round .irs-grid {
    height: 25px;
    font-family: "Montserrat", serif;
}
.bs-tooltip-auto[x-placement^="top"] .arrow::before,
.bs-tooltip-top .arrow::before {
    border-width: 0.3rem 0.35rem 0;
}
.plans-dialog.profileSection .col-sm-3 {
    -ms-flex: 0 0 25% !important;
    flex: 0 0 25% !important;
}
.plans-dialog.profileSection .col-sm-4 {
    -ms-flex: 0 0 33.333333% !important;
    flex: 0 0 33.333333% !important;
}
.plans-dialog.profileSection .col-sm-8 {
    -ms-flex: 0 0 66.666667% !important;
    flex: 0 0 66.666667% !important;
}

.plans-dialog .card.activePlan h4 {
    color: #231f20 !important;
}
.plans-dialog h4 {
    font-size: 21px;
    color: #231f20 !important;
}

.plans-dialog.profileSection span.tierText.profileText {
    position: absolute;
    bottom: 2px;
    left: 118px;
    color: #8dbac4 !important;
    font-size: 16px;
}

.plans-dialog.profileSection #header h1 {
    color: #231f20 !important;
}
.plans-dialog.profileSection #header th {
    color: #231f20 !important;
}
/* .navbar .navbar-brand span.tierName {
    display: none;
} */
.plans-dialog.profileSection .card {
    box-shadow: none;
}
.plans-dialog.profileSection .table td,
.plans-dialog.profileSection .table th {
    padding: 4px;
}
.plans-dialog.profileSection .table tr.heightSection td {
    height: 10px !important;
}
.plans-dialog.profileSection .table td,
.plans-dialog.profileSection .table th {
    padding: 5px;
    border-top: 1px solid #d8e4e7;
    font-weight: 400;
    min-height: auto !important;
    height: 45px !important;
    text-align: center;
    border: none;
    font-size: 15px;
    vertical-align: middle;
    color: #231f20;
}

.breadcrumb-item.active,
.breadcrumb-item.active a {
    color: #8dbac4 !important;
    pointer-events: none;
}
.plans-dialog.profileSection .table td,
.plans-dialog.profileSection .table th {
    min-width: 173px;
}
.plans-dialog.profileSection #header th {
    min-width: 173px;
}
.tooltip.large-tooltip .tooltip-inner {
    max-width: 320px !important;
    text-align: left !important;
}
i.size16 {
    font-size: 16px;
    vertical-align: baseline;
}
.reportViewerIcons.icons {
    margin-top: -4px;
    z-index: 99;
    position: relative;
}
.reportViewerIcons.icons a {
    height: auto !important;
    min-width: 30px;
    text-align: center;
}
.reportViewerIcons.icons a .la-plus {
    position: static !important;
}
.reportViewerIcons.icons a i.las.la-dollar-sign.costCal {
    margin-left: 0px;
}
.reportViewerIcons.icons a:not(:last-child):after {
    height: 14px;
    top: 8px;
}

#tabFix .nav-tabs .nav-link {
    padding: 0.5rem 0.99rem !important;
}
.howUse {
    font-size: 14px;
}
.searchTabbing .nav-link {
    padding-right: 0.8rem !important;
    padding-left: 0.8rem !important;
}

.a,
.a:hover {
    background: #00b19c;
}
.b,
.b:hover {
    background: #3b5998;
}
.c,
.c:hover {
    background: #55acee;
}
.d,
.d:hover {
    background: #0077b7;
}
/* Sneha */
.a,
.b,
.c,
.d {
    font-size: 12px !important;
    display: block !important;
    text-align: left !important;
    color: #fff !important;
    width: 150px !important;
    margin: 0 8px;
    font-size: 14px !important;
    padding-left: 10px !important;
    padding-top: 5px;
    padding-bottom: 5px;
}
.cSlide .carousel-indicators {
    position: absolute;
    right: 0;
    bottom: 10px;
    left: 0;
    z-index: 15;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: center;
    justify-content: flex-start;
    padding-left: 0;
    /* margin-right: 11%; */
    /* margin-left: 5%; */
    list-style: none;
}
.cSlide .carousel-indicators .active {
    background-color: #3bcd3f;
}

.cSlide .carousel-indicators li {
    position: relative;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto;
    width: 10px;
    height: 10px;
    margin-right: 3px;
    margin-left: 3px;
    text-indent: -999px;
    cursor: pointer;
    background: #8dbac4;
    border-radius: 10px;
}

.serbox .boxContainer .descriptionSection {
    min-height: 184px;
}

.iconCat {
    height: 35px;
    display: block;
    cursor: pointer;
    background-repeat: no-repeat;
    transition: 0.3s transform ease-in;
    width: 210px;
}
.Repo.repo-list {
    margin-bottom: 10px;
    display: inline-block;
    border-bottom: 1px solid #8dbac4;
    padding-bottom: 10px;
    transition: 0.3s transform ease-in;
}
.categoryBriefing {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Category-Briefing.svg);
    height: 55px !important;
}
.logoSection .categoryBriefing {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Category-Briefing.svg);
    height: 35px !important;
}

.Repo.repo-list:hover .categoryBriefing {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Category-Briefing-Hover.svg);
    transform: translateX(10px);
}
.commodityMapping {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Commodity-Price.svg);
    height: 55px !important;
}
.logoSection .commodityMapping {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Commodity-Price.svg);
    height: 35px !important;
}

.Repo.repo-list:hover .commodityMapping {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Commodity-Price-Hover.svg);
    transform: translateX(10px);
}
.ciMapping {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Commodity-Insights.svg);
    height: 55px !important;
}
.logoSection .ciMapping {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Commodity-Insights.svg);
    height: 35px !important;
}

.Repo.repo-list:hover .ciMapping {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Commodity-Insights-Hover.svg);
    transform: translateX(10px);
}

.categoryDeepDiv {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Category-Deep-Dive.svg);
}
.Repo.repo-list:hover .categoryDeepDiv {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Category-Deep-Dive-Hover.svg);
    transform: translateX(10px);
}
.procurementKpiBenchmarking {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Procurement-KPIs-additional-intelligence.svg);
}
.Repo.repo-list:hover .procurementKpiBenchmarking {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Procurement-KPIs-additional-intelligence-Hover.svg);
    transform: translateX(10px);
}
.innovationTrends {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Innovation-Trends.svg);
}
.Repo.repo-list:hover .innovationTrends {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Innovation-Trends-Hover.svg);
    transform: translateX(10px);
}
.casemeInsights {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/CASME-Insights.svg);
}
.Repo.repo-list:hover .casemeInsights {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/CASME-Insights-Hover.svg);
    transform: translateX(10px);
}
.sustainabilityTrends {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Sustainability-Trends.svg);
}
.Repo.repo-list:hover .sustainabilityTrends {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Sustainability-Trends-Hover.svg);
    transform: translateX(10px);
}
.insightReports {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Insights-Report.svg);
}
.commodityInsights {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/commodityinsightidentity.svg);
}

.Repo.repo-list:hover .commodityInsights {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/commodityinsightidentity-Hover.svg);
    transform: translateX(10px);
}
.Repo.repo-list:hover .insightReports {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Insights-Report-Hover.svg);
    transform: translateX(10px);
}

.topicalInsights {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Topical-Insights.svg);
}
.Repo.repo-list:hover .topicalInsights {
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Topical-Insights-Hover.svg);
    transform: translateX(10px);
}
/* .commodityInsights{
    background-image: url(https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/Web/V2/Resource/Commodity-Insights.svg);
} */

.Repo.repo-list .catIcon-a {
    background: #8dbac4;
    padding: 1px 4px;
    color: #fff;
    margin-top: 2px;
    display: inline-block;
    cursor: pointer;
    transition: 0.3s transform ease-in;
}
.Repo.repo-list:hover .catIcon-a {
    background: #00b19c;
    transform: translateX(10px);
}

.Repo.fvt {
    display: inline-block !important;
}
.Repo.fvt a {
    /*margin-right: 10px; */
    position: relative;
    /*display: inline-block; */
}
.Repo.fvt a::after {
    content: "|";
    position: relative;
    top: 0px;
    /*right: -8px; */
    color: #231f20;
}

.Repo.fvt a:last-child::after {
    display: none !important;
}
.custom-slide .indicators {
    position: absolute;
    top: 0px;
    right: 0px;
    z-index: 99;
}
.custom-slide .indicators a i.las {
    color: #231f20;
    opacity: 1;
    font-size: 24px;
}
.cardA {
    border-bottom: 1px solid #8dbac4;
}
.noBottomBorder {
    border-bottom: none !important;
}

.reportViewerIcons.icons .dropdown-menu.show a:not(:last-child):after {
    content: "";
    display: none !important;
}

.logoSection .Repo.repo-list {
    margin-bottom: 10px;
    display: inline-block;
    border-bottom: 1px solid #8dbac4;
    padding-bottom: 10px;
    transition: 0.3s transform ease-in;
    padding-right: 20px;
}

.logoSection .iconCat {
    height: 35px;
    display: block;
    cursor: pointer;
    background-repeat: no-repeat;
    transition: 0.3s transform ease-in;
    width: 135px;
}
.icons a.last-element::after {
    display: none;
}

#reportTabsId .nav-link.active {
    pointer-events: none;
    color: rgba(0, 0, 0, 0.9) !important;
}
#reportTabsId .nav-link.active:hover {
    color: rgba(0, 0, 0, 0.9) !important;
}

#reportTabsId .nav-item:hover,
#reportTabsId .nav-link:hover {
    /*background: #00b19c !important;*/
    color: #fff !important;
}

#catTab .nav-link.active {
    pointer-events: none;
    color: rgba(0, 0, 0, 0.9) !important;
}
#catTab .nav-link.active:hover {
    color: rgba(0, 0, 0, 0.9) !important;
}

#catTab .nav-item:hover,
#catTab .nav-link:hover {
    /*background: #00b19c !important;*/
    color: #fff !important;
}

.nav-tabs .nav-item {
    margin-bottom: -1px;
}
a.las.la-paperclip {
    position: absolute;
    top: 7px;
    right: 7px;
    font-size: 21px;
    z-index: 91;
}
.iconCat.insightReports,
.iconCat.sustainabilityTrends,
.iconCat.innovationTrends {
    height: 30px !important;
}
.Repo.repo-list:last-child {
    border-bottom: none;
}
.logoSection .Repo.repo-list {
    border-bottom: 1px solid #8dbac4;
}
#copyDropdown .dropdown-item.active,
#copyDropdown .dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #00b19c;
}
#copyDropdown .dropdown-item:focus,
#copyDropdown .dropdown-item:hover {
    color: #fff;
    text-decoration: none;
    background-color: none !important;
}
.com-right-section {
    height: 100% !important;
}
.com-right-section h4 {
    font-size: 12px;
    margin-bottom: 0px;
}
.com-right-section h2 {
    color: #00b19c;
    font-size: 44px;
    margin-bottom: 0px;
}

.com-right-section .list-group-flush .list-group-item::after {
    content: " ";
    width: 75%;
    height: 1px;
    background: #8dbac4;
    display: block;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 94%;
}

.com-right-section .list-group-flush .list-group-item {
    border: none !important;
}
.com-right-section
    .list-group-flush:last-child
    .list-group-item:last-child::after {
    display: none;
}
.icons #copyDropdown a:not(:last-child):after {
    display: none !important;
}
.icons .no-last-border a:not(:last-child):after {
    display: none !important;
}
.logoSection .absolute-center {
    min-height: 135px !important;
}
a#sampleDownload.adjustSampleFile {
    color: #fff;
    top: 11px;
}
.xyz iframe {
    border: none !important;
    width: 102.3% !important;
}
.carousel-item {
    -webkit-backface-visibility: inherit !important;
    backface-visibility: inherit !important;
}
.qa .text-secondary {
    color: #00b19c !important;
}
#searchText {
    height: auto !important;
}
.carousel-inner .cardA {
    min-height: 420px !important;
}
.exhaust-plan {
    background: #e1eaed !important;
    padding-left: 2px !important;
    padding-right: 2px !important;
    line-height: 22px;
    /*color: #98b9c3 !important;*/
    bottom: 5px !important;
}
.desktop.navbar .input-group {
    width: 674px;
    justify-content: flex-end;
    margin-right: 0px;
    align-items: center;
    height: auto !important;
}
.logoSection .Repo.repo-list {
    border: none;
}
div#overAllSummary {
    min-height: 231px;
}
.paddingTop0 {
    padding-top: 0 !important;
}
.mobile .bootstrap-autocomplete.dropdown-menu {
    position: absolute !important;
}
#search_wrapper .bootstrap-autocomplete {
    width: auto !important;
}
.selectordiv {
    width: 100%;
    height: 30px;
}
button.amcharts-range-selector-period-button {
    background: white;
    border: 1px solid #667986;
    cursor: pointer;
    padding: 3px 10px;
}
button.amcharts-range-selector-period-button.active {
    background: #00b19c;
    color: #fff;
    border: 1px solid #00b19c;
}
.amcharts-range-selector-range-wrapper {
    display: none;
}
.slide-box1 {
    display: flex;
    justify-content: space-evenly;
    text-align: center;
    z-index: 99;
    position: relative;
    margin-left: -1px;
}
.slide-box1 .tileHeader {
    height: 25px;
    background: #8dbac4;
    width: 100%;
    max-width: 100%;
    color: #fff;
    padding-top: 2px;
    font-size: 14px;
}
.slide-box1 .box {
    background: #fff !important;
    /* width: 220px; */
    height: 160px;
    border: 1px solid #d2e2e6 !important;
}
.slide-box1 .card-style {
    display: block;
    position: relative;
    -webkit-transition: all 200ms ease-in;
    -webkit-transform: scale(1);
    -ms-transition: all 200ms ease-in;
    -ms-transform: scale(1);
    -moz-transition: all 200ms ease-in;
    -moz-transform: scale(1);
    transition: all 200ms ease-in;
    transform: scale(1);
    width: 100%;
}
.slimScrollBar,
.slimScrollRail {
    opacity: 1 !important;
    color: #8dbac4 !important;
}
.titleLogo {
    width: 60px;
    height: 60px;
    object-fit: contain;
    background: #fff;
}
.amcharts-range-selector-wrapper {
    padding: 0.2em 0em 0px 0.4em !important;
    margin-bottom: 2em !important;
}

.multiselect-native-select > .btn-group {
    height: 27.6px;
}
.multiselect-native-select > .btn-group > .btn-default {
    color: #231f20;
}
.multiselect-native-select > .btn-group > .btn-default {
    color: #231f20;
    background-color: #fff;
    border-color: #bbd6dc;
    padding: 0px 12px 6px 12px;
    border-radius: 0px;
}
label {
    display: inline-block;
    margin-bottom: 0.3rem;
}
.recBtn {
    background: #fff;
    border: none;
    font-size: 18px;
    position: absolute;
    top: 6px;
    right: 30px;
    cursor: pointer;
    color: #6c757d;
    z-index: 999;
    height: 23px;
    padding-top: 0px;
}
/* Class added by venkat as part of UAT feedback sprint4 */
.amcharts-range-selector-period-title {
    display: none;
}
.loadMore,
.loadMoreTools,
.loadMoreData,
#LoadMoreButton {
    background: #fff !important;
    color: #231f20 !important;
    border: 1px solid #231f20 !important;
}
.loadMore:hover,
.loadMoreTools:hover,
.loadMoreData:hover,
#LoadMoreButton:hover {
    background: #02104f !important;
    color: #fff !important;
    border: 1px solid #02104f !important;
}
.multiselect-native-select .dropdown-toggle::after {
    position: absolute !important;
    right: 3px !important;
    top: 10px;
}
.cdTypeView-Section .multiselect-native-select .dropdown-toggle::after {
    position: relative !important;
    left: 0 !important;
}
.commodityFilterBtn .multiselect-native-select .dropdown-toggle::after {
    position: relative !important;
    left: 0 !important;
}
.navbar-nav .dropdown-menu .dropdown-toggle::after {
    vertical-align: middle !important;
}
.commodityFilterBtn .multiselect-native-select .dropdown-toggle::after {
    position: absolute !important;
    right: 5px !important;
    left: unset !important;
    margin-top: 0 !important;
}
.fixedContactForm .loader-info {
    top: 14% !important;
    left: 50% !important;
}
.loader-info-iframe .loader-info {
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%);
}
.tooltip {
    z-index: 999999 !important;
}
.carousel-item .icons a:not(:last-child):after {
    content: "";
    position: absolute;
    width: 1px;
    height: 14px;
    background: #ccc;
    right: 0;
    top: 4px !important;
}

.cscdChart .icons a:not(:last-child):after {
    top: 4px;
}

.zIndexHigh {
    z-index: 99999999 !important;
}
.fy-text {
    font-weight: normal;
    font-size: 12px !important;
}
.hypen-text {
    color: #00b19c !important;
}
.noteSection {
    position: relative !important;
    min-height: 70px !important;
}
.noteSection .row {
    position: absolute !important;
    bottom: 0 !important;
}
@media screen and (max-width: 768px) {
    .noteSection .row {
        position: relative !important;
        bottom: 0 !important;
    }
}
#cscd .icons a:not(:last-child):after {
    content: "";
    position: absolute;
    width: 1px;
    height: 14px;
    background: #ccc;
    right: 0;
    top: 5px !important;
}
.multiselect-container > li > a input[type="checkbox"] {
    float: left;
    width: 12px;
    height: 12px;
    margin-top: 0.25rem;
    background: -webkit-gradient(
            linear,
            left top,
            left bottom,
            from(#fff),
            to(#f8f8f9)
        ),
        #fff;
    background: -o-linear-gradient(top, #fff, #f8f8f9), #fff;
    background: linear-gradient(180deg, #fff, #f8f8f9), #fff;
    border: 1px solid #cacace;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin-top: 1px;
}
.multiselect-container > li > a input[type="checkbox"] {
    border-radius: 0;
    margin-right: 5px;
    flex: none;
}
.multiselect-container > li > a input[type="checkbox"]:checked {
    background-color: #3bcd3f;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 0.5rem;
    border-color: #3bcd3f;
}
.multiselect-container
    > li
    > a
    input[type="checkbox"]:checked[type="checkbox"] {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='12' height='10' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1 4.654l3.42 3.244L11.164 1.5' stroke='%23fff' stroke-width='2'/%3E%3C/svg%3E");
}
.ci-table th {
    text-align: center !important;
    border-top: 1px solid #d8e4e7 !important;
    border-left: 1px solid #d8e4e7 !important;
}
.ci-table th:last-child {
    border-right: 1px solid #d8e4e7 !important;
}
.ci-table td {
    border-bottom: 1px solid #d8e4e7 !important;
    border-left: 1px solid #d8e4e7 !important;
    border-top: 0 !important;
}
.ci-table td:last-child {
    border-right: 1px solid #d8e4e7 !important;
}
#example td.dataTables_empty {
    border-bottom: 1px solid #d8e4e7 !important;
    border-left: 1px solid #d8e4e7 !important;
    border-top: 0 !important;
    border-right: 1px solid #d8e4e7 !important;
}
#example_wrapper .dataTables_scrollHead {
    max-width: 1223px;
}
#example_wrapper .dataTables_scrollBody {
    max-width: 1223px;
}
.cartImageSection-topicals .catimageSection::after {
    bottom: 0 !important;
    top: -17px !important;
}
.skel-background-none {
    background: none !important;
}
.ske-bgm-none {
    background: none !important;
}
i.fas.fa-angle-down.load-more-angle {
    position: relative;
    top: 1px;
}
.select2-container--default
    .select2-results__option--highlighted[aria-selected] {
    background-color: #ddd !important;
    color: #231f20 !important;
}
.select2-results__option[aria-selected]:hover {
    background-color: #dae3e6 !important;
    color: #231f20 !important;
}
.new-custom-select option.new-custom-option:hover {
    background-color: #dae3e6 !important;
}
.new-custom-select {
    position: absolute !important;
    width: 50px !important;
    min-height: 26px !important;
    right: 170px !important;
    z-index: 9;
    overflow: hidden !important;
    border-radius: 0px;
    border: 1px solid #bfbfbf;
    color: #676767;
    background: transparent;
    box-sizing: content-box;
}
.new-custom-select option {
    position: relative;
    top: 7px;
    width: 50px;
}
.new-custom-select option:checked {
    display: none;
}
.new-custom-select option.selectedVal {
    background: #ddd;
    padding-bottom: 4px;
}
.new-custom-sort {
    position: relative;
    right: 54px;
    top: 4px;
}
.new-custom-sort-caret {
    position: relative;
    right: -5px;
    z-index: 1;
    top: 6px;
}
.new-custom-sort-caret .fa-caret-down {
    position: absolute;
    right: 10px;
    z-index: 0;
    font-size: 16px;
    top: -2px;
}
#dateValue,
#alphaValue {
    left: 2px;
}
.modified-new-select {
    position: relative;
    max-height: 26px;
    border: none;
    background: none;
}

.modified-new-select select {
    display: none; /*hide original SELECT element:*/
}

.modified-new-select .select-selected {
    background: transparent;
    color: #666;
    border: none;
    position: relative;
    bottom: 12px;
    border: none !important;
}

/*style the arrow inside the select element:*/
.modified-new-select .select-selected:after {
    position: absolute;
    content: "";
    top: 14px;
    right: 10px;
    width: 0;
    height: 0;
    border-color: #fff transparent transparent transparent;
}

/*point the arrow upwards when the select box is open (active):*/
.modified-new-select .select-selected.select-arrow-active:after {
    border-color: transparent transparent #fff transparent;
    top: 7px;
}
.modified-new-select .select-arrow-active,
.select-selected {
    position: relative;
    right: 12px;
    bottom: 6px !important;
}
/*style the items (options), including the selected item:*/
.modified-new-select .select-items div,
.select-selected {
    color: #231f20;
    border: none;
    cursor: pointer;
    user-select: none;
    padding: 0.375rem 0.75rem !important;
    font-size: 12px !important;
    line-height: 16px;
}
.modified-new-select .select-selected {
    position: relative;
    bottom: 10px;
    color: #231f20;
    width: 200%;
    text-align: left;
}
.modified-new-select .select-items div {
    font-size: 12px !important;
}
/*style items (options):*/
.modified-new-select .select-items {
    position: absolute;
    background-color: #fff;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1072;
    width: 220px;
    border: 1px solid #d8e4e7;
    color: #231f20;
    max-height: 330px;
    overflow-y: scroll;
    text-align: left;
    box-shadow: 0px 1px 3px #8f8888;
}

/*hide the items when the select box is closed:*/
.modified-new-select .select-hide {
    display: none;
}

.modified-new-select .select-items div:hover,
.same-as-selected {
    background-color: #dae3e6 !important;
    color: #231f20;
}
.modified-new-select .same-as-selected {
    background-color: #ddd !important;
}
/* .select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #050505 transparent transparent transparent !important;
  } */
#ci-commodity-list button.dt-button.processing:after,
#ci-commodity-list div.dt-button.processing:after,
#ci-commodity-list a.dt-button.processing:after {
    display: none !important;
}

#lower-table td {
    word-break: break-all;
}

/* .risk-profile-widgets .btn.disabled {
    opacity: 1;
    color: #fff;
    background: #fff;
    opacity: 1;
    color: #fff;
    border-radius: 0px;
    padding: 0;
    padding-bottom: 2px;
    padding-top: 10px;
    margin-top: 10px;
    display: block;
    height: 93px;
    padding-top: 40px;
    margin: 0 auto;
    padding-left: 0;
    padding-right: 0;
    margin-top: 9px;

} */

.risk-profile-widgets .btn.disabled {
    opacity: 1;
    color: #fff;
    background: #fff;
    opacity: 1;
    color: #fff;
    border-radius: 0px;
    padding: 0;
    padding-bottom: 2px;
    padding-top: 10px;
    margin-top: 10px;
    display: block;
    height: 104px;
    padding-top: 50px;
    margin: 0 auto;
    padding-left: 0;
    padding-right: 0;
    margin-top: 9px;
}

.risk-profile-widgets .risk-head {
    font-size: 31px;
    line-height: 18px;
    font-size: 71px;
}

.risk-profile-widgets .risk-overall {
    width: 150px;
    margin: 0 auto;
    font-size: 21px;
    color: #231f20;
}

.risk-profile-widgets .w-20 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 20% !important;
    flex: 0 0 20% !important;
    max-width: 20%;
    padding: 0;
    padding-top: 4px;
}
.risk-profile-widgets .w-25 {
    -webkit-box-flex: 0;
    -ms-flex: 0 0 25% !important;
    flex: 0 0 25% !important;
    max-width: 25%;
    padding: 0;
    padding-top: 4px;
}

.risk-profile-widgets .progressbar-text {
    text-align: center;
    color: #231f20 !important;
    line-height: 24px;
    top: 50% !important;
}
.risk-profile-widgets .progressbar-text span {
    color: #231f20 !important;
}

.risk-profile-widgets svg {
    height: 100px !important;
}
.risk-profile-widgets .border-color-graph {
    border-color: #bbd6dc !important;
}
.craftTable thead tr:nth-child(1) th {
    background: #8dbac4;
    position: sticky;
    top: 0px;
    z-index: 10;
    color: #fff;
    font-weight: normal;
    border-bottom: none !important;
}

.craftTable td {
    vertical-align: middle !important;
}

.read-more-section {
    text-align: left;
    color: #ccc;
}
.read-more-section a {
    color: #d0e3e6;
}
.read-more-section .fa-caret-right {
    position: relative;
    top: 1px;
    left: 3px;
    color: #d0e3e6;
}
.read-more-section a:hover {
    color: #00b19c;
}
.read-more-section a:hover .fa-caret-right {
    color: #00b19c !important;
}
.craft-row .slimScrollDiv {
    padding-right: 9px;
    z-index: 0;
}
.craft-row .legend-supplier {
    background-color: #e1ecef !important;
}
.craft-row .legendSup {
    background-color: #e1ecef !important;
    height: 10px;
    width: 10px;
    display: inline-block;
}
.riskRowLegend {
    position: relative;
    top: -7px;
}
.NewsMainDiv .riskRowLegend {
    top: 7px;
}
header.tradeHeadWidget {
    padding-left: 10px;
    padding-right: 10px;
    margin-bottom: 0px;
    border-top: 1px solid #8dbac480;
    border-bottom: none !important;
    margin-top: 10px !important;
}
.focus-section {
    position: absolute;
    right: 6px;
    bottom: 6px;
}
.focus-section-pro {
    position: absolute;
    right: 10px;
    bottom: 15px;
}

#loadSolutions .dropdown-item {
    white-space: normal;
    /* word-break: break-all; */
}
#loadSolutions .slimScrollDiv {
    padding-right: 0px;
    z-index: 0;
}
#supplier-craft .table-responsive .slimScrollDiv {
    padding-right: 9px;
    z-index: 0;
}
.font-size14 {
    font-size: 14px;
}
.craftTable {
    margin-bottom: 0 !important;
}

#CCModal .modal-dialog.modal-lg {
    max-width: 87% !important;
}
.loader-info1 {
    position: fixed !important;
}
.clIc {
    font-size: 19px !important;
    color: #667986 !important;
}
.close {
    opacity: 1;
}
.clIc:hover {
    color: #3bcd3f !important;
}
.tooltip {
    z-index: 9999999999 !important;
}

/* Alignment code */
.card-body header {
    padding-left: 0px !important;
    padding-right: 0px !important;
}
.card-body .noteSection {
    padding-left: 0px !important;
    padding-right: 0px !important;
}
.clear-all-btn.btn-primary:not(:disabled):not(.disabled):active {
    background: #fff !important;
    border-color: #0000 !important;
    color: #00b19c !important;
}

.custom-select {
    color: #231f20 !important;
}
.custom-select .fa.fa-caret-down {
    font-size: 13px !important;
}
.select2-container--default
    .select2-selection--single
    .select2-selection__arrow
    b {
    border-color: #231f20 #0000 #0000 #0000 !important;
}
.select2-container--default.select2-container--open
    .select2-selection--single
    .select2-selection__arrow
    b {
    border-color: #0000 #0000 #231f20 #0000 !important;
}
button.btn.dropdown-toggle.btn-light {
    color: #231f20 !important;
    border: 1px solid #dee2e6 !important;
}
.optionSort .multiselect.dropdown-toggle.btn.btn-default {
    color: #231f20 !important;
    border: 1px solid #dee2e6 !important;
}
button.btn.dropdown-toggle.btn-light {
    /* z-index: 999; */
    border: 1px solid #dee2e6 !important;
}
.secondaryOption input {
    border-color: #dee2e6 !important;
}
/* .secondaryOption .arrowIcon {
    position: absolute;
    top: 2px;
    right: 2px;
} */
.bootstrap-select.show button.btn.dropdown-toggle.btn-light {
    z-index: 999 !important;
}
button.btn.dropdown-toggle.btn-light {
    width: 77px !important;
}
.modal-header .icons a {
    min-height: auto;
}
.modal-header .icons a {
    padding: 2px 4px;
}
.modal-header .icons i {
    font-size: 16px;
}
.modal-header .reportViewerIcons.icons a:not(:last-child):after {
    height: 14px;
    top: 4px;
}
.modal-footer,
.modal-header {
    align-items: center;
}
/* .modal .icons {
    padding-bottom: 0 !important;
}
.modal-footer, .modal-header {
    min-height: 39px !important;
}
.modal-header {
    display: flex;
    align-items: center;
} */

.modal-header .close {
    padding-bottom: 5px !important;
    padding-top: 5px !important;
}
.blogContainer {
    /* width: 420px; */
    height: 270px;
}

.custom-select.modified-new-select span:first-child {
    bottom: 3px !important;
}

.cookieContainer {
    background: rgba(30, 42, 57, 0.9);
    color: #fff;
    min-height: 100px;
    border-top: 2px solid #00b19c;
    position: fixed;
    width: 100%;
    bottom: 0px;
    z-index: 999999999 !important;
}

.cookieContainer-inner {
    margin-left: 50%;
    transform: translateX(-50%);
    width: 60%;
    font-size: 12px;
}

.cookieContainer-inner p {
    margin-top: 50px;
    transform: translateY(-25px);
    width: 86%;
    float: left;
    margin-bottom: 0;
    padding-right: 25px;
}

.cookieContainer-inner span {
    display: flex;
    align-items: center;
    min-height: 100px;
    margin-left: 5%;
}

.cookieContainer-inner .cookieBtn {
    border-color: #fff;
    color: #fff;
    min-width: 115px;
    margin-left: -5px;
    background-color: transparent;
    line-height: 1.55;
}

.btn-outline-secondary.cookieBtn:hover {
    background: #231f20;
    border-radius: 0px;
    border-color: white;
    color: white;
}
.btn-outline-secondary.cookieBtn:not(:disabled):not(.disabled).active,
.btn-outline-secondary.cookieBtn:not(:disabled):not(.disabled):active {
    background: #231f20;
    border-color: white;
    color: white;
}

.cookieContainer-inner a:hover,
.cookieContainer-inner a:active {
    color: #00b19c !important;
}

.noImageInsight {
    display: flex;
    align-items: center;
}

.noImageInsight img {
    width: 65px;
    height: auto;
    border: 1px solid #d8e4e7;
    margin: 0 auto;
    display: block;
    padding: 5px;
}

#supplier-craft.highlightTD .table-bordered th,
#supplier-craft.highlightTD .table-bordered td {
    border: 1px solid #edf3f5 !important;
}
.OppAndRiskDive ul,
.OppAndRiskDive p {
    margin-bottom: 0px !important;
}

/* div.pie {
    width: 20px;
    height: 20px;
    display: block;
    border-radius: 50%;
    background-color: #fff;
    /* border: 1px solid #00b19c; */
/* float: none;
    margin: 0.5em auto;
    position: relative */
/* } */

div.pie {
    width: 20px;
    height: 20px;
    display: block;
    border-radius: 50%;
    background-color: #00b19c;
    /* border: 2px solid #00b19c; */
    float: none;
    margin: 0.5em auto;
    position: relative;
}
div.pie::after {
    content: " ";
    position: absolute;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid #00b19c;
}
.pie.low {
    background-image: -webkit-gradient(
            linear,
            left top,
            right top,
            color-stop(50%, transparent),
            color-stop(50%, white)
        ),
        -webkit-gradient(linear, left top, right top, color-stop(50%, white), color-stop(50%, transparent));
    background-image: -webkit-linear-gradient(left, transparent 50%, white 50%),
        -webkit-linear-gradient(left, white 50%, transparent 50%);
    background-image: -o-linear-gradient(left, transparent 50%, white 50%),
        -o-linear-gradient(left, white 50%, transparent 50%);
    background-image: linear-gradient(90deg, transparent 50%, white 50%),
        linear-gradient(90deg, white 50%, transparent 50%);
    /* border: 2px solid #00b19c !important; */
}

.pie.lowmoderate {
    background-image: -webkit-gradient(
            linear,
            left top,
            left bottom,
            color-stop(50%, transparent),
            color-stop(50%, white)
        ),
        -webkit-gradient(linear, left top, right top, color-stop(50%, white), color-stop(50%, transparent));
    background-image: -webkit-linear-gradient(top, transparent 50%, white 50%),
        -webkit-linear-gradient(left, white 50%, transparent 50%);
    background-image: -o-linear-gradient(top, transparent 50%, white 50%),
        -o-linear-gradient(left, white 50%, transparent 50%);
    background-image: linear-gradient(180deg, transparent 50%, white 50%),
        linear-gradient(90deg, white 50%, transparent 50%);
    /* border: 2px solid #00b19c !important; */
}

.pie.moderate {
    background-image: -webkit-gradient(
            linear,
            right top,
            left top,
            color-stop(50%, transparent),
            color-stop(50%, white)
        ),
        -webkit-gradient(linear, left top, right top, color-stop(50%, white), color-stop(50%, transparent));
    background-image: -webkit-linear-gradient(right, transparent 50%, white 50%),
        -webkit-linear-gradient(left, white 50%, transparent 50%);
    background-image: -o-linear-gradient(right, transparent 50%, white 50%),
        -o-linear-gradient(left, white 50%, transparent 50%);
    background-image: linear-gradient(270deg, transparent 50%, white 50%),
        linear-gradient(90deg, white 50%, transparent 50%);
    /* border: 2px solid #00b19c !important; */
}

.pie.high {
    background-image: none;
    /* border: 2px solid #00b19c !important; */
}

.pie.moderatehigh {
    background-image: -webkit-gradient(
            linear,
            left top,
            left bottom,
            color-stop(50%, transparent),
            color-stop(50%, #00b19c)
        ),
        -webkit-gradient(linear, left top, right top, color-stop(50%, white), color-stop(50%, transparent));
    background-image: -webkit-linear-gradient(top, transparent 50%, #00b19c 50%),
        -webkit-linear-gradient(left, white 50%, transparent 50%);
    background-image: -o-linear-gradient(top, transparent 50%, #00b19c 50%),
        -o-linear-gradient(left, white 50%, transparent 50%);
    background-image: linear-gradient(180deg, transparent 50%, #00b19c 50%),
        linear-gradient(90deg, white 50%, transparent 50%);
    /* border: 2px solid #00b19c !important; */
}
.harveyLegends {
    min-width: 170px !important;
}

.harveyLegends div.pie {
    width: 15px;
    height: 15px;
    display: block;
    border-radius: 50%;
    background-color: #00b19c;
    /* border: 2px solid #00b19c; */
    float: none;
    margin: 0.5em auto;
    border: 1px solid #00b19c;
}

.harveyLegends div.pie::after {
    display: none;
}
.newsGroup {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
    float: none !important;
}
.newsDesc {
    margin-left: 20px;
    position: relative;
    min-height: 42px;
    display: block;
    padding-bottom: 15px;
    clear: both;
}

.fixJerk::before {
    content: " ";
    position: absolute;
    background: #8dbac4;
    height: 4px;
    width: calc(100% - 11px);
    top: -2px;
    z-index: 9;
}

.focusIconMode {
    cursor: pointer;
    height: 19px !important;
    margin-left: 2px !important;
    background-image: url(../images/Focusmode.svg);
    display: block;
    width: 19px;
    /* background-repeat: no-repeat; */
}
.focusIconMode:hover {
    cursor: pointer;
    height: 19px !important;
    margin-left: 2px !important;
    background-image: url(../images/Focusmode-hover.svg);
    display: block;
    width: 19px;
    /* background-repeat: no-repeat; */
}

.icons i {
    color: #02104f;
}

.icons i:hover {
    color: #3bcd3f;
}
.plr10 {
    padding-left: 10px !important;
    padding-right: 10px !important;
}
.mlr10 {
    margin-left: -10px !important;
    margin-right: -10px !important;
}

/* Safari Hack for Markers */

/* (Safari + MobileSafari >= 14.6) or (All MobileSafari versions) */
/* @supports  (selector(:nth-child(1 of x))) or (-webkit-touch-callout: none) { */

.saf .ciKeyInsightScroll ul > li::before {
    /*content: "Ã¢â€“Â¶ ";*/
    content: "▶ ";
    color: #3bcd3f;
    margin-left: -15px;
}
.saf .ciKeyInsightScroll-supply ul > li::before,
.saf .porter-table ul > li::before {
    /*content: "Ã¢â€“Â¶ ";*/
    content: "▶ ";
    color: #3bcd3f;
    margin-left: -15px;
}

.saf .ciKeyInsightScroll ul > li > ul > li::before,
.saf .ciKeyInsightScroll-supply ul > li > ul > li::before,
.saf .porter-table ul > li > ul > li::before {
    /*content: "Ã¢â€“Â  ";*/
    content: "■ ";
    margin-left: -15px;
}

.saf .ciKeyInsightScroll ul > li > ul > li > ul > li::before,
.saf
    .ciKeyInsightScroll-supply
    ul
    > li
    > ul
    > li
    > ul
    > li::before
    .porter-table
    ul
    > li
    > ul
    > li
    > ul
    > li::before {
    /*content: "Ã¢â‚¬ï¿½? ";*/
    content: "— ";
    margin-left: -15px;
}

.saf .ciKeyInsightScroll ul > li::before {
    content: "▶ ";
    color: #3bcd3f;
    margin-left: -15px;
}

.saf .ciKeyInsightScroll-supply ul > li::before {
    content: "▶ ";
    color: #3bcd3f;
    margin-left: -15px;
}
.saf .ciKeyInsightScroll ul > li > ul > li::before,
.saf .ciKeyInsightScroll-supply ul > li > ul > li::before {
    content: "■ ";
    margin-left: -15px;
}

.saf .ciKeyInsightScroll ul > li > ul > li::before,
.saf .ciKeyInsightScroll-supply ul > li > ul > li::before {
    content: "■ ";
    margin-left: -15px;
}

.saf .ciKeyInsightScroll ul > li > ul > li > ul > li::before,
.saf .ciKeyInsightScroll-supply ul > li > ul > li > ul > li::before {
    content: "— ";
    margin-left: -15px;
}

.saf .faqSection ul > li::before {
    content: "▶ ";
    color: #3bcd3f;
    margin-left: -15px;
}

.saf .faqSection ul > li > ul > li::before {
    content: "■ ";
    margin-left: -15px;
}
.saf .faqSection ul > li > ul > li > ul > li::before {
    content: "— ";
    margin-left: -15px;
}

.saf .refine-your-search ul > li::before {
    content: "▶ ";
    color: #3bcd3f;
    margin-left: -15px;
}
.saf .refine-your-search ul > li > ul > li::before {
    content: "■ ";
    margin-left: -15px;
}
.saf .refine-your-search ul > li > ul > li > ul > li::before {
    content: "— ";
    margin-left: -15px;
}

#opportunityModal #craftTable2 .table.craftTable::before,
#opportunityModal #craftTable3 .table.craftTable::before {
    content: " " !important;
    background: #8dbac4 !important;
    width: calc(100% - 9px) !important;
    height: 15px !important;
    position: absolute !important;
    top: -14px !important;
    left: 0px !important;
    z-index: 9 !important;
}

#opprMainDiv #craftTable2 .table.craftTable::before,
#opprMainDiv #craftTable3 .table.craftTable::before {
    content: " " !important;
    background: #8dbac4 !important;
    width: calc(100% - 9px) !important;
    height: 15px !important;
    position: absolute !important;
    top: -14px !important;
    left: 0px !important;
    z-index: 9 !important;
}

/* preload */

body:after {
    display: none;
    content: url(../images/Focusmode-hover.svg);
}

#disruptiveDiv .slimScrollDiv #craftTable .table.craftTable::before {
    content: " " !important;
    background: #8dbac4 !important;
    width: calc(100% - 9px) !important;
    height: 15px !important;
    position: absolute !important;
    top: -14px !important;
    left: 0px !important;
    z-index: 9 !important;
}

.modified-new-select1 {
    position: relative;
    max-height: 26px;
    border: none;
    background: none;
}

.modified-new-select1 select {
    display: none; /*hide original SELECT element:*/
}

.modified-new-select1 .select-selected {
    background: transparent;
    color: #666;
    border: none;
    position: relative;
    bottom: 12px;
    border: none !important;
}

/*style the arrow inside the select element:*/
.modified-new-select1 .select-selected:after {
    position: absolute;
    content: "";
    top: 14px;
    right: 10px;
    width: 0;
    height: 0;
    border-color: #fff transparent transparent transparent;
}

/*point the arrow upwards when the select box is open (active):*/
.modified-new-select1 .select-selected.select-arrow-active:after {
    border-color: transparent transparent #fff transparent;
    top: 7px;
}
.modified-new-select1 .select-arrow-active,
.select-selected {
    position: relative;
    right: 12px;
    bottom: 6px !important;
}
/*style the items (options), including the selected item:*/
.modified-new-select1 .select-items div,
.select-selected {
    color: #231f20;
    border: none;
    cursor: pointer;
    user-select: none;
    padding: 0.375rem 0.75rem !important;
    font-size: 12px !important;
    line-height: 16px;
}
.modified-new-select1 .select-selected {
    position: relative;
    bottom: 10px;
    color: #231f20;
    width: 200%;
    text-align: left;
}
.modified-new-select1 .select-items div {
    font-size: 12px !important;
}
/*style items (options):*/
.modified-new-select1 .select-items {
    position: absolute;
    background-color: #fff;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1072;
    width: 220px;
    border: 1px solid #d8e4e7;
    color: #231f20;
    max-height: 330px;
    overflow-y: scroll;
    text-align: left;
    box-shadow: 0px 1px 3px #8f8888;
}

/*hide the items when the select box is closed:*/
.modified-new-select1 .select-hide {
    display: none;
}

.modified-new-select1 .select-items div:hover,
.same-as-selected {
    background-color: #dae3e6 !important;
    color: #231f20;
}
.modified-new-select1 .same-as-selected {
    background-color: #ddd !important;
}

div h1.ci-name + .reportAction.reportViewerIcons {
    z-index: 999 !important;
}

.custom-boxes.customBoxMiddle input {
    position: absolute !important;
    right: 1px !important;
    top: 2px !important;
    height: 20px !important;
    width: 20px !important;
}
.custom-boxes.customBoxMiddle input[type="checkbox"]:checked {
    background-size: 0.9rem !important;
}
.h-28 {
    height: 28px !important;
}
.magicHeader {
    background: #fff !important;
    position: fixed !important;
    top: 80px !important;
    left: 50% !important;
    width: 100% !important;
    z-index: 9 !important;
    border: 1px solid #ddd;
    height: 44px !important;
    width: 1280px !important;
    transform: translateX(-50%);
    padding-right: 10px !important;
}
.magicHeader .breadcrumb,
.magicHeader h1 {
    visibility: hidden !important;
}
/* .cRiskTable thead th {
    background-color: #8dbac4 !important;
    color: white !important;
    border-top: none;
    border-bottom: none;
} */
.guageChartKPI .progressbar-text {
    text-align: center;
    color: #231f20 !important;
    /* line-height: 24px; */
}
.guageChartKPI .openBox1 span.cBullets {
    width: 8px;
    height: 8px;
    border: 1px solid #8dbac4;
    border-radius: 8px;
    display: inline-block;
    margin-right: 2px;
    cursor: pointer;
}
.guageChartKPI {
    padding-left: 10px;
    padding-right: 10px;
}

.popover-header {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0;
    /* font-size: 1rem; */
    font-size: 12px;
    color: inherit;
    background-color: #f7f7f7;
    border-bottom: 1px solid #ebebeb;
    border-top-left-radius: calc(0.3rem - 1px);
    border-top-right-radius: calc(0.3rem - 1px);
    text-align: center;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.popover {
    width: 350px;
    max-width: 350px !important;
}
.popover {
    border: 1px solid #231f20;
    padding: 0;
}
.popover-body {
    padding: 0.5rem 0.75rem !important;
}

.popover-body .table-bordered thead th,
.popover-body .table-bordered thead td {
    border-bottom-width: 1px;
}

.bs-popover-auto[x-placement^="right"] .arrow::after,
.bs-popover-right .arrow::after {
    left: 1px;
    border-right-color: #02104f;
}
.bs-popover-auto[x-placement^="left"] .arrow::after,
.bs-popover-left .arrow::after {
    border-right-color: #02104f;
}
.bs-popover-auto[x-placement^="top"] .arrow::after,
.bs-popover-top .arrow::after {
    border-right-color: #02104f;
}
.bs-popover-auto[x-placement^="bottom"] .arrow::after,
.bs-popover-bottom .arrow::after {
    border-right-color: #02104f;
}

.bs-popover-auto[x-placement^="left"] .arrow,
.bs-popover-left .arrow {
    right: calc((0.5rem + 1.5px) * -1);
    width: 0.5rem;
    height: 1rem;
    margin: 0.3rem 0;
}

.bs-popover-auto[x-placement^="left"] .arrow::before,
.bs-popover-left .arrow::before {
    right: 0;
    border-left-color: #02104f;
}
.bs-popover-auto[x-placement^="left"] .arrow::after,
.bs-popover-left .arrow::after {
    right: 1px;
    border-left-color: #02104f;
}

/* Custom POC Fix */

.modified-new-select-modified {
    position: relative;
    max-height: 26px;
    border: none;
    background: none;
}

.modified-new-select-modified select {
    display: none; /*hide original SELECT element:*/
}

.modified-new-select-modified .select-selected {
    background: transparent;
    color: #666;
    border: none;
    position: relative;
    bottom: 12px;
    border: none !important;
}

/*style the arrow inside the select element:*/
.modified-new-select-modified .select-selected:after {
    position: absolute;
    content: "";
    top: 14px;
    right: 10px;
    width: 0;
    height: 0;
    border-color: #fff transparent transparent transparent;
}

/*point the arrow upwards when the select box is open (active):*/
.modified-new-select-modified .select-selected.select-arrow-active:after {
    border-color: transparent transparent #fff transparent;
    top: 7px;
}
.modified-new-select-modified .select-arrow-active,
.select-selected {
    position: relative;
    right: 12px;
    bottom: 6px !important;
}
/*style the items (options), including the selected item:*/
.modified-new-select-modified .select-items div,
.select-selected {
    color: #231f20;
    border: none;
    cursor: pointer;
    user-select: none;
    padding: 0.375rem 0.75rem !important;
    font-size: 12px !important;
    line-height: 16px;
}
.modified-new-select-modified .select-selected {
    position: relative;
    bottom: 10px;
    color: #231f20;
    width: 200%;
    text-align: left;
}
.modified-new-select-modified .select-items div {
    font-size: 12px !important;
    line-height: 16px !important;
}
/*style items (options):*/
.modified-new-select-modified .select-items {
    position: absolute;
    background-color: #fff;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1072;
    width: 220px;
    border: 1px solid #d8e4e7;
    color: #231f20;
    max-height: 330px;
    overflow-y: scroll;
    text-align: left;
    box-shadow: 0px 1px 3px #8f8888;
}

/*hide the items when the select box is closed:*/
.modified-new-select-modified .select-hide {
    display: none;
}

.modified-new-select-modified .select-items div:hover,
.same-as-selected {
    background-color: #dae3e6 !important;
    color: #231f20;
}
.modified-new-select-modified .same-as-selected {
    background-color: #ddd !important;
}

.custom-select.modified-new-select-modified span:first-child {
    bottom: 3px !important;
}
.popover {
    font-family: "Montserrat", serif !important;
}

/* END Custom POC Fix */

.table.craftTable {
    margin-top: 0 !important;
}
.craftTable thead tr:nth-child(1) th,
.cRiskTable thead tr:nth-child(1) th {
    background: #f2f7f8;
    position: sticky;
    top: 0px;
    z-index: 10;
    color: #231f20;
    font-weight: normal;
    border-bottom: none !important;
}
.cRiskTable thead tr:nth-child(1) th {
    font-weight: bold !important;
}

#opprtabdataDiv .slimScrollDiv .table.craftTable,
#opprMainDiv .slimScrollDiv .table.craftTable,
#opprtabdataDivModal .slimScrollDiv .table.craftTable,
#risktabdataDivModal .slimScrollDiv .table.craftTable,
#disruptiveDiv .slimScrollDiv .table.craftTable,
#supplier-craft .slimScrollDiv .table.craftTable,
#supplier-craft-modal .slimScrollDiv .table.craftTable {
    margin-top: 0 !important;
}
#opprtabdataDiv .slimScrollDiv table.craftTable thead::before,
#opprMainDiv .slimScrollDiv table.craftTable thead::before,
#opprtabdataDivModal .slimScrollDiv table.craftTable thead::before,
#risktabdataDivModal .slimScrollDiv table.craftTable thead::before,
#disruptiveDiv .slimScrollDiv table.craftTable thead::before,
#supplier-craft .slimScrollDiv table.craftTable thead::before,
#supplier-craft-modal .slimScrollDiv table.craftTable thead::before {
    content: " " !important;
    background: #dee2e6 !important;
    height: 1px !important;
    width: calc(100% - 9px) !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 11 !important;
}
#opprtabdataDiv .slimScrollDiv table.craftTable thead::after,
#opprMainDiv .slimScrollDiv table.craftTable thead::after,
#opprtabdataDivModal .slimScrollDiv table.craftTable thead::after,
#risktabdataDivModal .slimScrollDiv table.craftTable thead::after,
#disruptiveDiv .slimScrollDiv table.craftTable thead::after,
#supplier-craft .slimScrollDiv table.craftTable thead::after,
#supplier-craft-modal .slimScrollDiv table.craftTable thead::after {
    content: " " !important;
    background: #dee2e6 !important;
    height: 1px !important;
    width: calc(100% - 9px) !important;
    position: absolute !important;
    top: auto !important;
    left: 0 !important;
    z-index: 11 !important;
}
#opprtabdataDiv .slimScrollDiv .craftTable thead tr:nth-child(1) th,
#opprMainDiv .slimScrollDiv .craftTable thead tr:nth-child(1) th,
#opprtabdataDivModal .slimScrollDiv .craftTable thead tr:nth-child(1) th,
#risktabdataDivModal .slimScrollDiv .craftTable thead tr:nth-child(1) th,
#disruptiveDiv .slimScrollDiv .craftTable thead tr:nth-child(1) th,
#supplier-craft .slimScrollDiv .craftTable thead tr:nth-child(1) th,
#supplier-craft-modal .slimScrollDiv .craftTable thead tr:nth-child(1) th,
.cRiskTable .slimScrollDiv .craftTable thead tr:nth-child(1) th {
    background: #f2f7f8;
    position: sticky;
    top: 0px;
    z-index: 10;
    color: #231f20;
    font-weight: normal;
    border-bottom: none !important;
    border-top: none !important;
}
#opprMainDiv #craftTable2 .table.craftTable::before,
#opprMainDiv #craftTable3 .table.craftTable::before,
#opprtabdataDivModal #craftTable3 .table.craftTable::before,
#risktabdataDivModal #craftTable3 .table.craftTable::before {
    display: none !important;
}
#opportunityModal #craftTable2 .table.craftTable::before,
#opportunityModal #craftTable3 .table.craftTable::before,
#opprtabdataDivModal #craftTable3 .table.craftTable::before,
#risktabdataDivModal #craftTable3 .table.craftTable::before {
    display: none !important;
}
#disruptiveDiv .slimScrollDiv #craftTable .table.craftTable::before {
    display: none;
}

#supplier-craft .slimScrollDiv {
    padding-right: 9px;
    z-index: 0;
}

.freqDiv .radio-switch label:first-of-type {
    padding-right: 35px !important;
}

.freqDiv .radio-switch label:last-child {
    margin-left: -39px;
    padding-left: 56px;
}

.freqDiv .radio-switch label:first-of-type:before {
    background: #3bcd3f;
    border: none;
    border-radius: 100%;
    position: absolute;
    right: -15px;
    /* transform: translateX(0em); */
    transition: transform 0.2s ease-in-out;
    width: 17px;
    z-index: 2;
    box-shadow: 0px 0px 3px 0px rgb(0 0 0 / 40%);
}

.radio-switch label:first-of-type:before,
.radio-switch label:first-of-type:after {
    border: 1px solid #e7f1f3;
    content: "";
    height: 17px !important;
    overflow: hidden;
    pointer-events: none;
    position: absolute;
    vertical-align: middle;
}
.freqDiv .radio-switch label:first-of-type:after {
    background: #e7f1f3;
    border-radius: 17px;
    margin: 0 1em;
    transition: background 0.2s ease-in-out;
    width: 35px;
    margin-left: 10px;
}
.freqDiv .radio-switch label {
    display: inline-block;
    line-height: 18px;
    position: relative;
    /* z-index: 2; */
    margin-bottom: 0 !important;
}
.freqDiv .radio-switch label:first-of-type {
    padding-right: 35px;
}
.freqDiv #nav-tab > label {
    margin-bottom: 0 !important;
}
.freqDiv .nav {
    align-items: center;
}
.freqDiv {
    margin-bottom: 5px;
}
.radio-switch input:first-of-type:checked ~ label:first-of-type:before {
    transform: translateX(-28px) !important;
}

.category-card .col-sm-3:nth-child(1) .tileHeader,
.category-card .col-sm-3:nth-child(1) .skew-div-section:hover {
    background-color: #00b19c !important;
}
.category-card .col-sm-3:nth-child(2) .tileHeader,
.category-card .col-sm-3:nth-child(2) .skew-div-section:hover {
    background-color: #007365 !important;
}
.category-card .col-sm-3:nth-child(3) .tileHeader,
.category-card .col-sm-3:nth-child(3) .skew-div-section:hover {
    background-color: #3bcd3f !important;
}
.category-card .col-sm-3:nth-child(4) .tileHeader,
.category-card .col-sm-3:nth-child(4) .skew-div-section:hover {
    background-color: #02104f !important;
}

.leftArrow {
    position: absolute;
    top: 42%;
    left: -10px;
    z-index: 98;
    cursor: pointer;
}
.rightArrow {
    position: absolute;
    top: 42%;
    right: -10px;
    cursor: pointer;
}

.slideControls .slidePrev {
    left: -10px !important;
}
.slideControls .slideNext {
    right: -10px !important;
}
.footerBannerAdjust {
    margin-left: 15px;
    margin-right: 15px;
}
.slick-dots li button:before {
    position: absolute;
    top: 0;
    left: 0;
    width: 10px;
    height: 10px;
    content: " ";
    text-align: center;
    opacity: 1;
    background: #8dbac4;
    border-radius: 10px;
    border: none;
}
.slick-dots {
    position: absolute;
    bottom: 10px !important;
    display: block;
    width: 100%;
    padding: 0;
    margin: 0;
    list-style: none;
    text-align: center;
}

.cSlide .carousel-indicators {
    position: absolute;
    right: 0;
    bottom: 0px;
    left: 0;
    z-index: 15;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: center;
    justify-content: center;
    padding-left: 0;
    /* margin-right: 11%; */
    /* margin-left: 5%; */
    list-style: none;
}

/* dropdown POC */

/* .navbar-nav .dropdown-menu.dropdown-menu-right .dropdown-toggle::after {
    transform: rotate(0deg) !important;
    transition: 0.1s all ease;
}
.navbar-nav .dropdown-menu.dropdown-menu-right .dropdown-toggle:hover::after {
    transform: rotate(90deg) !important;
} */

#tsc_nav_1 .demo .dchild {
    top: 0;
    /* right: 100%; */
    left: 100%;
}
#tsc_nav_1 .demo.doMagic .dchild {
    top: 0;
    right: 100% !important;
    left: auto !important;
    transition: 0.5s all ease;
}
.navbar-nav .dropdown-menu .demo.doMagic .dropdown-toggle::after {
    transform: rotate(90deg) !important;
    transition: 0.5s all ease;
}

/* HSN Code CSS */
.treeviewHsn a.parentClick {
    display: block;
    padding: 0;
    background: #d9e4e8;
    padding-left: 5px;
    border: 1px solid #ccc;
    color: #231f20;
    text-decoration: none;
    padding-right: 5px;
}
.treeviewHsn ul.tree {
    font-size: 12px;
    font-family: "Montserrat", serif !important;
    line-height: 2em;
    list-style: none;
    padding: 0px;
}

.treeviewHsn ul.tree ul {
    margin: 0;
    padding-left: 17px;
    line-height: 2em;
    list-style: none;
}
.treeviewHsn ul li {
    position: relative;
}
.treeviewHsn ul > li li {
    color: #231f20;
}

.treeviewHsn ul > li li:before {
    position: absolute;
    top: 0;
    left: -15px;
    display: block;
    width: 15px;
    height: 1em;
    content: "";
    border-bottom: 1px dotted #231f20;
    border-left: 1px dotted #231f20;
}
/* hide the vertical line on the first item */
.treeviewHsn ul.tree > li:first-child:before {
    border-left: none;
}
.treeviewHsn ul > li li:after {
    position: absolute;
    top: 1.1em;
    bottom: 1px;
    left: -15px;
    display: block;
    content: "";
    border-left: 1px dotted #231f20;
}

/* hide the lines on the last item */
.treeviewHsn ul li:last-child:after {
    display: none;
}

/* inserted via JS  */
.treeviewHsn .js-toggle-icon {
    position: relative;
    z-index: 1;
    display: inline-block;
    width: 14px;
    margin-right: 2px;
    margin-left: -23px;
    line-height: 14px;
    text-align: center;
    cursor: pointer;
    background-color: #fff;
    border: 1px solid #231f20;
    /* border-radius: 2px; */
    float: right;
    font-size: 19px;

    margin-top: 5px;
}
.treeviewHsn span {
    color: #231f20;
}
.treeviewHsn .size21 {
    font-size: 21px !important;
}

#skeleton_loader .nlq-blinking-text {
    font-family: "Montserrat", serif !important;
}

/* [class^="col-sm-"], [class^=" col-sm-"], [class^="col-4"], [class^=" col-4-"] {
    position:  relative !important;
} */

.popover {
    z-index: 9999 !important;
}

.EventRiskImpact__absolute-center {
    margin-top: -10px;
}

.EventRiskImpact__image {
    display: none;
}

.EventRiskImpact__title {
    font-weight: bolder;
    font-size: 26px;
}

.EventRiskImpact__status {
    color: #231f20;
}

.EventRiskImpact__no-record {
    font-size: 12px;
    display: block;
    width: 534px;
}

.EventRiskImpact__link {
    font-size: 13px;
    margin-top: 19px;
}
.lSSlideWrapper {
    max-width: 100%;
    overflow: hidden;
    padding-top: 20px;
    padding-bottom: 20px;
    padding-left: 24px;
}
#catIntelligence .carouselSectionSlide,
#comIntelligence .carouselSectionSlide,
#loadRecentView .carouselSectionSlide {
    overflow: visible !important;
    padding-left: 0 !important;
    padding-right: 30px !important;
    padding-top: 10px !important;
    padding-bottom: 0px !important;
}

#catIntelligence li.lslide,
#comIntelligence li.lslide,
#loadRecentView li.lslide {
    width: 201.4px !important;
    margin-right: 10px !important;
}
#catIntelligence h4.heading,
#comIntelligence h4.heading,
#loadRecentView h4.heading {
    padding-left: 5px !important;
}

#loadRecentView .carouselSectionSlide {
    padding-top: 0px !important;
}
.slideControls .slidePrev {
    left: 0;
    position: absolute;
    cursor: pointer;
    z-index: 9;
}

#loadComodityGroup .nav-tabs .nav-link,
#loadCategoryDashboardGroup .nav-tabs .nav-link {
    padding: 0.5rem 0.98rem !important;
}

/* bootstrap 5 */

.card {
    height: 100%;
}
[class^="col-sm-"] {
    position: relative !important;
}

.form-control {
    appearance: auto;
}

.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl {
    padding-right: 15px;

    padding-left: 15px;
}

.form-group {
    margin-bottom: 1rem;
}

.border-right {
    border-right: 1px solid #dee2e6 !important;
}

.input-group-text {
    padding: 0.25rem 0.5rem;

    font-size: 0.875rem;

    height: calc(1.75125rem + 0px);
}

.row {
    margin-right: -15px;

    margin-left: -15px;
}

.col,
.col-1,
.col-10,
.col-11,
.col-12,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-auto,
.col-lg,
.col-lg-1,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-auto,
.col-md,
.col-md-1,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-auto,
.col-sm,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-auto,
.col-xl,
.col-xl-1,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-auto {
    padding-right: 15px;

    padding-left: 15px;
}

.border-right-0 {
    border-right: 0 !important;
}

.modified-new-select-modified .select-items div,
.select-selected {
    color: #231f20;
    border: none;
    cursor: pointer;
    user-select: none;
    padding: 0.375rem 1.2rem !important;
    font-size: 12px !important;
    line-height: 26px;
}

nav.desktop a.navbar-brand span.tierName {
    bottom: 5px;
}
.bs-tooltip-auto[data-popper-placement^="right"] .arrow {
    position: relative;
}
.bs-tooltip-auto[data-popper-placement^="right"] .arrow::before,
.bs-tooltip-right .arrow::before,
.bs-tooltip-right .tooltip.large-tooltip.bs-tooltip-auto::before {
    content: " ";
    right: 0;
    border-width: 0.4rem 0.4rem 0.4rem 0;
    border-right-color: #02104f;
}

.modal-header button.close {
    background-color: #fff !important;
    border: none !important;
}
.plans-dialog .card {
    box-shadow: none !important;
}
.plans-dialog .card-body {
    background: #fff !important;
}

.bs-tooltip-auto[data-popper-placement^="right"] .arrow::before,
.bs-tooltip-end .arrow::before {
    right: -1px;
    border-width: calc(var(--bs-tooltip-arrow-width) * 0.5)
        var(--bs-tooltip-arrow-height) calc(var(--bs-tooltip-arrow-width) * 0.5)
        0;
    border-right-color: #02104f;
}

.bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after,
.bs-popover-end > .popover-arrow::after {
    left: var(--bs-popover-border-width);
    border-right-color: #231f20;
}

.cSlide .carousel-indicators li {
    border-top: none !important;
    border-bottom: none !important;
}
#tradeImportExport.row .trade-flow-loader {
    width: auto !important;
}
.loadTariffDataModal .iconDiv {
    padding-bottom: 10px;
}

section.col-sm-12.pt-0.pb-0.categoryRiskDescSection {
    margin-left: -12px !important;
}

#disruptiveDiv td ul {
    margin-bottom: 0 !important;
}
.popover-body {
    padding: 0.5rem 0.55rem !important;
}
div#loadToolsData,

div#grid-data {
    overflow-x: hidden;
}
div#main-carousel {
    overflow-x: hidden;
}
/* icon color change css  */

.icons i {
    color: #02104f;
}
.icons i:hover {
    color: #3bcd3f;
}
#tsc_nav_1 ul.nohover a.nav-link.UserActivityLogger {
    padding: 0;
}
#tsc_nav_1 ul.nohover a.nav-link.UserActivityLogger .hexaIcon {
    width: 22px;
}
.btn-dark {
    color: #fff;
    background-color: #02104f;
    border-color: #02104f;
}
.btn-dark:hover {
    color: #fff;
    background-color: #02104ff2;
    border-color: #02104ff2;
}
.secCount .serbtn .la-search:hover{
    color: #3bcd3f;
}

table tbody tr td,
.table > :not(caption) > * > * {
    color: #231f20;
}

/* New css added for Bootstrap 5 upgrading  */

.moveRight .info-text {
    text-align: left;
    line-height: 21px;
    cursor: pointer;
    display: block;
    white-space: normal;
    overflow: hidden;
    text-overflow: unset;
    width: auto;
    padding-left: 5px;
}

.sector--search input.search-text {
    position: absolute;
    z-index: 5;
    transition: z-index 0.8s, width 0.5s, background 0.3s ease, border 0.3s;
    width: 0;
    right: 10px;
    cursor: pointer;
    border: 1px solid transparent !important;
    background-color: white;
    outline: none;
    padding: 0;
    z-index: 0;
}

.sector--search input.search-text.expanded {
    width: 156px;
    border: 1px solid #a4c8d0 !important;
    padding-right: 10px;
    cursor: auto;
    padding-left: 5px;
    right: 7px;
    top: -3px;
}

.sector--search span.serbtn {
    position: absolute;
    top: 3px;
    cursor: pointer;
    z-index: 99999;
    min-height: 74%;
    background: transparent;
    right: 5px;
}
.sector--search a {
    color: #667986 !important;
}
.secCount .serbtn .la-search {
    color: #02104f;
}

.sector--search .size19 {
    font-size: 19px !important;
}
.sergroup .la-search,
.serbtn .la-search {
    -ms-transform: rotate(269deg);
    -webkit-transform: rotate(269deg);
    transform: rotate(269deg);
}
.secCount .filterBtn::before {
    content: " ";
    position: absolute;
    width: 1px;
    height: 14px;
    background: #ccc;
    left: -4px;
    top: 2px !important;
    z-index: 99999;
}

/* <==== ask pia btns design ====> */
/* ul.list-group.askpia_btn_groups{
  background-color: #f2f8f8;
  padding: 10px;
} */
ul.list-group.askpia_btn_groups .list-group-item {
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    color: #231f20;
}

ul.list-group.askpia_btn_groups .list-group-item:hover,
ul.list-group.askpia_btn_groups .list-group-item.active {
    border-color: #3bcd3f;
    color: #231f20;
}

ul.list-group.askpia_btn_groups .list-group-item i {
    color: #3bcd3f;
    font-size: 14px;
    transition: all 0.3s ease;
}
.treeviewHsn ul.tree {
    margin-bottom: 0;
}
.askpia_btn_groups .list-group-item {
    cursor: pointer;
    padding: 10px;
    border: 1px solid #ccc;
    margin-bottom: 5px;
}
.askpia_btn_groups .list-group-item:last-child {
    margin: 0;
}
.askpia_btn_groups .list-group-item:hover,
.askpia_btn_groups .list-group-item.active {
    background-color: #f0f0f0;
}

div#hsn_code_widget ul.list-group.askpia_btn_groups li.list-group-item.askpia_btn_item.active {
    position: relative !important;
    z-index: 998;
    background-color: #fff;
}

.slideout-panel-askpia {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    padding: 20px;
    transition: all 0.5s ease-in-out;
    z-index: 9;
    padding-top: 0px;
    box-shadow: none;
    /*  height: 100%;*/
    transform: translateX(-150%);
    z-index: 999;
    animation-timing-function: linear;
}

.slideout-panel-askpia.open {
    border-top: 1px solid #ddd;
    display: block;
    transform: translateX(0);
    min-height: 325px;
    box-shadow: -1px -1px 10px 1px rgba(0, 0, 0, 0.1);
    transition: all 0.5s ease-in-out;
}

#bgoverlayask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.1) !important;
    z-index: 99; /* Ensure it appears above most elements */
    display: none; /* Initially hidden */
}

#bgoverlayask.visible {
    display: block;
}

.priceAnalysisDataDiv h3 {
    font-size: 18px;
    margin: 10px 0;
}

.gx-0 .col,
.gx-0 .col-1,
.gx-0 .col-2,
.gx-0 .col-3,
.gx-0 .col-4,
.gx-0 .col-5,
.gx-0 .col-6,
.gx-0 .col-7,
.gx-0 .col-8,
.gx-0 .col-9,
.gx-0 .col-10,
.gx-0 .col-11,
.gx-0 .col-12,
.gx-0 .col-auto,
.gx-0 .col-lg,
.gx-0 .col-lg-1,
.gx-0 .col-lg-2,
.gx-0 .col-lg-3,
.gx-0 .col-lg-4,
.gx-0 .col-lg-5,
.gx-0 .col-lg-6,
.gx-0 .col-lg-7,
.gx-0 .col-lg-8,
.gx-0 .col-lg-9,
.gx-0 .col-lg-10,
.gx-0 .col-lg-11,
.gx-0 .col-lg-12,
.gx-0 .col-lg-auto,
.gx-0 .col-md,
.gx-0 .col-md-1,
.gx-0 .col-md-2,
.gx-0 .col-md-3,
.gx-0 .col-md-4,
.gx-0 .col-md-5,
.gx-0 .col-md-6,
.gx-0 .col-md-7,
.gx-0 .col-md-8,
.gx-0 .col-md-9,
.gx-0 .col-md-10,
.gx-0 .col-md-11,
.gx-0 .col-md-12,
.gx-0 .col-md-auto,
.gx-0 .col-sm,
.gx-0 .col-sm-1,
.gx-0 .col-sm-2,
.gx-0 .col-sm-3,
.gx-0 .col-sm-4,
.gx-0 .col-sm-5,
.gx-0 .col-sm-6,
.gx-0 .col-sm-7,
.gx-0 .col-sm-8,
.gx-0 .col-sm-9,
.gx-0 .col-sm-10,
.gx-0 .col-sm-11,
.gx-0 .col-sm-12,
.gx-0 .col-sm-auto,
.gx-0 .col-xl,
.gx-0 .col-xl-1,
.gx-0 .col-xl-2,
.gx-0 .col-xl-3,
.gx-0 .col-xl-4,
.gx-0 .col-xl-5,
.gx-0 .col-xl-6,
.gx-0 .col-xl-7,
.gx-0 .col-xl-8,
.gx-0 .col-xl-9,
.gx-0 .col-xl-10,
.gx-0 .col-xl-11,
.gx-0 .col-xl-12,
.gx-0 .col-xl-auto {
    padding-right: 0;
    padding-left: 0;
}

.nav-tabs .nav-item a.nav-link {
    height: 100%;
    transition: none;
}

ul#inflationTab.nav-tabs .nav-link.active:hover {
    background-color: white;
    border-color: #d8e4e7 #d8e4e7 white !important;
    border-top: 3px solid #00b19c !important;
}
/* select#sortByFilter{
    border: 1px solid #dee2e6;
    padding: 5.5px 4px;
} */
.bootstrap-select > select.bs-select-hidden,
select.bs-select-hidden,
select.selectpicker,
select#sortByFilter {
    display: none !important;
}
.secondaryOption .bootstrap-select .dropdown-menu.show {
    overflow: visible !important;
    top: -2px;
}
.dropdown.bootstrap-select {
    display: inline-block;
}
.dropdown.bootstrap-select .dropdown-toggle::after {
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
}
.dropdown.bootstrap-select .filter-option-inner {
    text-align: left;
}
.dropdown.bootstrap-select .dropdown-menu ul.dropdown-menu.inner {
    border: 1px solid #dee2e6 !important;
    width: 77px !important;
    min-width: auto !important;
    border-bottom: none !important;
}
.dropdown.bootstrap-select .dropdown-menu ul.dropdown-menu.inner li {
    width: 100%;
}
.dropdown.bootstrap-select
    .dropdown-menu
    ul.dropdown-menu.inner
    li
    .dropdown-item {
    width: 100%;
    cursor: pointer;
}
.navbar-toggler:focus {
    box-shadow: none;
    outline: none;
}
.d-flex {
    display: flex !important;
}
div#allEventTable_wrapper,
div#eventTable_wrapper {
    top: -38px;
}
div.dt-container .dt-paging .dt-paging-button {
    border-color: #00b19c !important;
    border-radius: 0 !important;
}
div.dt-container .dt-paging .dt-paging-button.current, div.dt-container .dt-paging .dt-paging-button.current:hover{
    background-color: #00b19c !important;
}
div.dt-container .dt-paging .dt-paging-button:hover{
    background: #00b19c !important;
}
div.dt-container .dt-search input {
    border-radius: 0 !important;
    height: 25px;
}
.setInsetPostion {
    inset: 0px 0px auto auto !important;
}
.navbar.nav-tabs.intelligence_tabs .nav-link.active:focus,
.navbar.nav-tabs.intelligence_tabs .nav-link.active:hover {
    border-color: initial !important;
    border-left-color: #dee2e6 !important;
    border-right-color: #dee2e6 !important;
}
div#intelligence_tabs div#example_wrapper .table_top_search {
    justify-content: end;
    border-bottom: 1px solid #d8e4e7;
    position: absolute;
    top: -47px;
    width: 100%;
}
div#intelligence_tabs #example_wrapper .dt-buttons {
    float: unset;
    width: auto;
}
div#intelligence_tabs #example_wrapper label {
    position: relative;
}

div#intelligence_tabs #example_wrapper .iconSearchData {
    right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

/* ///////////////////////////////////// */

div#intelligence_tabs div#example2_wrapper .table_top_search {
    justify-content: end;
    border-bottom: 1px solid #d8e4e7;
    position: absolute;
    top: -47px;
    width: 100%;
}
div#intelligence_tabs #example2_wrapper .dt-buttons {
    float: unset;
    width: auto;
}
div#intelligence_tabs #example2_wrapper label {
    position: relative;
}

div#intelligence_tabs #example2_wrapper .iconSearchData {
    right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

/* ///////////////////////// */
div#intelligence_tabs div#example4_wrapper .table_top_search {
    justify-content: end;
    border-bottom: 1px solid #d8e4e7;
    position: absolute;
    top: -47px;
    width: 100%;
}
div#intelligence_tabs #example4_wrapper .dt-buttons {
    float: unset;
    width: auto;
}
div#intelligence_tabs #example4_wrapper label {
    position: relative;
}

div#intelligence_tabs #example4_wrapper .iconSearchData {
    right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}
/* ////////////////////////////////////// */
div#intelligence_tabs .intelligence_tabs {
    display: inline-block;
    position: relative;
    z-index: 99;
}
div#intelligence_tabs .table-responsive {
    overflow: visible;
}

div#intelligence_tabs
    #example_wrapper
    .dt-buttons
    button.buttons-excel.buttons-html5 {
    color: #fff !important;
    cursor: pointer !important;
    margin-bottom: 4.8px !important;
    margin-left: 5px;
}

div#intelligence_tabs1 div#example1_wrapper .table_top_search {
    justify-content: end;
    border-bottom: 1px solid #d8e4e7;
    position: absolute;
    top: -47px;
    width: 100%;
}
div#intelligence_tabs1 #example1_wrapper .dt-buttons {
    float: unset;
    width: auto;
}
div#intelligence_tabs1 #example1_wrapper .iconSearchData {
    right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}
div#intelligence_tabs1 .intelligence_tabs {
    display: inline-block;
    position: relative;
    z-index: 99;
}
div#intelligence_tabs1 .table-responsive {
    overflow: visible;
}

div#intelligence_tabs1
    #example1_wrapper
    .dt-buttons
    button.buttons-excel.buttons-html5 {
    color: #fff !important;
    cursor: pointer !important;
    margin-bottom: 4.8px !important;
    margin-left: 5px;
}

div#intelligence_tabs1 div#example4_wrapper .table_top_search {
    justify-content: end;
    border-bottom: 1px solid #d8e4e7;
    position: absolute;
    top: -47px;
    width: 100%;
}
div#intelligence_tabs1 #example4_wrapper .dt-buttons {
    float: unset;
    width: auto;
}
div#intelligence_tabs1 #example4_wrapper .iconSearchData {
    right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}
div#intelligence_tabs1 .intelligence_tabs {
    display: inline-block;
    position: relative;
    z-index: 99;
}
div#intelligence_tabs1 .table-responsive {
    overflow: visible;
}

div#intelligence_tabs1
    #example4_wrapper
    .dt-buttons
    button.buttons-excel.buttons-html5 {
    color: #fff !important;
    cursor: pointer !important;
    margin-bottom: 4.8px !important;
    margin-left: 5px;
}
#carousel0 .tileHeader {
    background-color: #fff !important;
    color: #231f20;
    font-weight: 600;
    margin: 20px 0 8px 0;
}
#carousel0 .boxContainer {
    height: 152px;
}
#carousel0 .boxContainer .front .descriptionSection {
    margin-top: 14px;
}

div#monitorLine svg g[fill="#000000"], div#chartdivB svg g[fill="#000000"], div#chartdivA svg g[fill="#000000"] {
  /* stroke: #231f20; */
  fill: #231f20;
}
.card.activePlan table th, .card.activePlan table td, .card.defaultPlan table th, .card.defaultPlan table td{
    background-color: transparent;
}

.card.activePlan .card-body, .card.defaultPlan .card-body{
    background-color: transparent !important;
}