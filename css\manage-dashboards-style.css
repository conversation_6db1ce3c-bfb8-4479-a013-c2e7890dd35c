/* Main Layout */
.dashboard-main {
  padding: 24px;
  max-width: 1440px;
  margin: 0 auto;
}

/* Filters Section */
.filters-section {
  background: white;
  border-radius: 0px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 24px;
}

.filters-container {
  display: flex;
  gap: 16px;
  align-items: flex-end;
}

.filter-group {
  flex: 1;
  min-width: 200px;
}

.filter-group label {
  display: block;
  color: var(--slate-grey);
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
}

.filter-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 13px;
  color: var(--wns-black);
  background-color: white;
  transition: all 0.2s ease;
  cursor: pointer;
}

.filter-select:hover {
  border-color: var(--ocean-teal);
}

.filter-select:focus {
  outline: none;
  border-color: var(--ocean-teal);
  box-shadow: 0 0 0 2px var(--ocean-teal-light);
}

.create-dashboard-btn {
  all: unset;
  background: var(--ocean-teal);
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.create-dashboard-btn:hover {
  background: var(--forest-green);
  transform: translateY(-1px);
}

.create-dashboard-btn i {
  font-size: 16px;
}

/* Dashboards Table */
.dashboards-section {
  background: white;
  border-radius: 0px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.dashboards-table {
  width: 100%;
  border-collapse: collapse;
}

.table-header {
  display: flex;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.header-cell {
  flex: 1;
  padding: 12px 16px;
  color: var(--slate-grey);
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.table-row:hover {
  background: #f8fafc;
}

.table-cell {
  flex: 1;
  padding: 16px;
  font-size: 13px;
  color: var(--wns-black);
  display: flex;
  align-items: center;
}

/* Dashboard Name */
.dashboard-name {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--denali-blue);
  font-weight: 500;
}

.dashboard-name i {
  color: var(--ocean-teal);
  font-size: 16px;
}

/* Last Edited */
.last-edited {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.last-edited .date {
  color: var(--wns-black);
}

.last-edited .editor {
  color: var(--slate-grey);
  font-size: 12px;
}

/* Status Badge */
.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.published {
  background: var(--emerald-green-light);
  color: var(--emerald-green);
}

.status-badge.draft {
  background: #f1f5f9;
  color: #64748b;
}

.status-badge.archived {
  background: #fee2e2;
  color: #ef4444;
}

/* Version Info */
.version-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.version {
  color: var(--slate-grey);
  font-size: 12px;
  font-weight: 500;
}

.version-history-btn {
  all: unset;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  color: var(--slate-grey);
  transition: all 0.2s ease;
}

.version-history-btn:hover {
  background: var(--ocean-teal-light);
  color: var(--ocean-teal);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  all: unset;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  color: var(--slate-grey);
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--ocean-teal-light);
  color: var(--ocean-teal);
}

.action-btn.delete:hover {
  background: #fee2e2;
  color: #ef4444;
}

.action-btn i {
  font-size: 16px;
}

/* User Menu */
.user-menu {
  position: relative;
}

.user-btn {
  all: unset;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  color: white;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.user-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-btn i {
  font-size: 18px;
}

/* Dark Theme Support */
body.dark-theme .filters-section,
body.dark-theme .dashboards-section {
  background: #1e293b;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

body.dark-theme .filter-select {
  background: #334155;
  border-color: #475569;
  color: white;
}

body.dark-theme .table-header {
  background: #1e293b;
  border-color: #334155;
}

body.dark-theme .table-row {
  border-color: #334155;
}

body.dark-theme .table-row:hover {
  background: #334155;
}

body.dark-theme .table-cell {
  color: #e2e8f0;
}

body.dark-theme .dashboard-name {
  color: white;
}

body.dark-theme .last-edited .date {
  color: #e2e8f0;
}

body.dark-theme .last-edited .editor {
  color: #94a3b8;
}

body.dark-theme .status-badge.draft {
  background: #334155;
  color: #94a3b8;
}

/* Mobile Optimizations */
@media (max-width: 1024px) {
  .filters-container {
    flex-wrap: wrap;
  }

  .filter-group {
    min-width: calc(50% - 8px);
  }

  .create-dashboard-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .dashboard-main {
    padding: 16px;
  }

  .filter-group {
    min-width: 100%;
  }

  .table-header,
  .table-row {
    display: block;
  }

  .header-cell {
    display: none;
  }

  .table-cell {
    padding: 12px;
    border-bottom: 1px solid #e2e8f0;
  }

  .table-row {
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 8px;
    background: #f8fafc;
  }

  body.dark-theme .table-row {
    background: #334155;
  }
}
