document.write(`
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="https://maxst.icons8.com/vue-static/landings/line-awesome/line-awesome/1.3.0/css/line-awesome.min.css"
    />

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- GridStack extra CSS -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/gridstack@10.3.1/dist/gridstack-extra.min.css"
    />
    <link
      rel="stylesheet"
      href="https://amplifipro.thesmartcube.com/public/css/ion.rangeSlider.min.css?v=2.2.32"
    />
    <link rel="stylesheet" href="./css/skelton2.css" />
    <link rel="stylesheet" href="./inline-raita.css" />
    <!-- Widget Sidebar CSS -->
    <link rel="stylesheet" href="./css/widget-sidebar.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/demo.css" />
    <!-- Grid Consistency Fix -->
    <link rel="stylesheet" href="css/grid-consistency-fix.css" />
    <!-- Workspace Styles -->
    <link rel="stylesheet" href="css/workspace.css" />
    <link rel="stylesheet" href="css/index2.css" />
    <!-- Font Awesome for icons -->
    <link
      rel="stylesheet"
      href="https://maxst.icons8.com/vue-static/landings/line-awesome/line-awesome/1.3.0/css/line-awesome.min.css"
    />
    <!-- Handsontable CSS -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/handsontable/dist/handsontable.full.min.css"
    />
    


    <link rel="stylesheet" href="css/pieChartWidget.css" />
        <link rel="stylesheet" href="css/barChartWidget.css" />
         <link rel="stylesheet" href="css/lineChartWidget.css" />
          <link rel="stylesheet" href="css/sectionContainerWidget.css" />
              <link rel="stylesheet" href="css/handsontableWidget.css" />
               <link rel="stylesheet" href="css/textWidget.css" />
               <link rel="stylesheet" href="css/tableWidget.css" />
               <link rel="stylesheet" href="css/lineSeparatorWidget.css" />
               <link rel="stylesheet" href="css/notesSectionWidget.css" />
               <link rel="stylesheet" href="css/section-widget-styling.css" />
               <link rel="stylesheet" href="css/imageWidget.css" />
                   <link rel="stylesheet" href="css/videoWidget.css" />
                    <link rel="stylesheet" href="css/pdfViewerWidget.css" />
                      <link rel="stylesheet" href="css/wordCloudWidget.css" />
                      <link rel="stylesheet" href="css/stackedColumnChartWidget.css" />
                       <link rel="stylesheet" href="css/kpiWidget.css" />
                       <link rel="stylesheet" href="css/priceChartWidget.css" />
                         <link rel="stylesheet" href="css/tabContainerWidget.css" />
                          <!-- Dashboard Styles -->
    <link rel="stylesheet" href="css/dashboard-style.css" />
    <!-- Inline Editing Styles -->
    <link rel="stylesheet" href="css/inlineEdit.css" />
    <link rel="stylesheet" href="css/custom-design.css" />

    <!-- Widget Height Fix -->
    <link rel="stylesheet" href="css/widget-height-fix.css" />

    <!-- Pie Chart Compact Mode -->
    <link rel="stylesheet" href="css/pie-chart-compact-mode.css" />
    <!-- Widget Gallery Redesign -->
    <link rel="stylesheet" href="css/widget-gallery-redesign.css" />
        <link rel="stylesheet" href="css/bubbleChartWidget.css" />
                <link rel="stylesheet" href="css/stackedColumnChartWidget.css" />
    <link rel="stylesheet" href="css/stacked-column-chart-fix.css" />
     <link rel="stylesheet" href="css/percentStackedColumnChartWidget.css" />
     <link rel="stylesheet" href="css/stockChartWidget.css" />
       <link rel="stylesheet" href="css/areaChartWidget.css" />



    
    `);
