// Add a section container widget using the same UI pattern as other widgets
function addSectionContainerWidget() {
  console.log("Adding section container widget");
  const containerId = "section-" + Date.now();

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 8,
    content: `
      <div class="section-container-widget p-2">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
            <i class="las la-layer-group"></i> Section Container
          </div>
          <div class="widget-icons">
            <div class="widget-icon settings-icon" title="Settings">
              <i class="las la-cog"></i>
            </div>
            <div class="widget-icon " title="Close">
              <i class="las la-times"></i>
            </div>
          </div>
        </div>
        <div id="${containerId}" class="section-content"></div>
      </div>
    `,
    minH: 4, // Set minimum height
    maxH: 12, // Set maximum height
    autoPosition: true,
  });

  // Initialize the section container with a slight delay to ensure DOM is ready
  requestAnimationFrame(() => {
    try {
      initSectionContainer(containerId);
    } catch (error) {
      console.error("Error initializing section container:", error);
    }
  });

  return widget;
}

// Function to add a widget to a section
function addWidgetToSection(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  // Find the nested grid
  const gridElement = container.querySelector(".grid-stack");
  if (!gridElement || !gridElement.gridstack) {
    console.error("Could not find grid in section container");
    return;
  }

  // Add a simple widget to the nested grid
  const nestedGrid = gridElement.gridstack;
  nestedGrid.addWidget({
    x: 0,
    y: 0,
    w: 3,
    h: 2,
    content: `
      <div class="widget p-2 h-100">
        <div class="widget-header">
          <div class="widget-title">
            <i class="las la-chart-bar"></i>
            Nested Widget
          </div>
          <div class="widget-icons">
            <div class="widget-icon" title="Settings">
              <i class="las la-cog"></i>
            </div>
            <div class="widget-icon" title="Close">
              <i class="las la-times"></i>
            </div>
          </div>
        </div>
        <div class="widget-body d-flex align-items-center justify-content-center">
          <div>Nested Content</div>
        </div>
      </div>
    `,
  });
}

// Initialize a section container
function initSectionContainer(containerId) {
  console.log("Initializing section container:", containerId);
  const container = document.getElementById(containerId);

  if (!container) {
    console.error("Section container not found:", containerId);
    return;
  }

  // Create a nested grid inside the container with a clearly marked dropzone
  container.innerHTML = '<div class="grid-stack grid-stack-nested grid-stack-dropzone"></div>';
  const gridElement = container.querySelector(".grid-stack");
  
  // Apply important direct styles to ensure grid is properly identified as a dropzone
  gridElement.style.height = "100%";
  gridElement.style.minHeight = "100px";
  gridElement.style.border = "1px dashed rgba(0, 177, 156, 0.2)";
  gridElement.style.backgroundColor = "rgba(0, 177, 156, 0.05)";
  
  // Initialize the nested grid with optimized settings for accepting drops
  const nestedGrid = GridStack.addGrid(gridElement, {
    column: 6,
    margin: 5,
    cellHeight: 50,
    acceptWidgets: true, // Accept widgets from other grids
    disableOneColumnMode: true, // Prevent responsive mode changes
    float: true,
    staticGrid: false, // Enable interaction
    animate: false, // Disable animations
    draggable: {
      scroll: false,
      handle: '.widget-header',
    },
    // For better dropzone behavior
    removable: '.trash', // Class for trash dropzone if needed
    removeTimeout: 100,
    itemClass: 'grid-stack-item-nested', // Special class for nested items
    dropOverItems: true, // Allow dropping over other items
    children: [],
  });

  // Store grid reference directly on element
  gridElement.gridstack = nestedGrid;
  
  // Add custom classes to help with CSS targeting
  container.classList.add('has-grid');
  
  // Add visual indicator when an item is being dragged
  document.addEventListener('mousemove', function(e) {
    if (document.body.classList.contains('widget-dragging')) {
      gridElement.classList.add('active-dropzone');
    } else {
      gridElement.classList.remove('active-dropzone');
    }
  });
  
  // Debug drop events
  nestedGrid.on('dropped', function(event, previousWidget, newWidget) {
    console.log('Widget dropped into section:', containerId);
    console.log('Widget:', newWidget);
  });
  
  // Handle drops that might not be registered properly
  nestedGrid.on('change', function() {
    console.log('Change detected in section:', containerId);
  });

  console.log("Section container initialized with grid:", nestedGrid);
  return nestedGrid;
}

// Export the functions
window.addSectionContainerWidget = addSectionContainerWidget;
window.addWidgetToSection = addWidgetToSection;
window.initSectionContainer = initSectionContainer;
