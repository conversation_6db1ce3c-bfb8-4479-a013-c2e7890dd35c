// Add a video widget
function addVideoWidget() {
  console.log("Adding video widget");
  const videoId = "video-" + Date.now();
  const settingsId = "settings-" + videoId;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 4,
    content: `
      <div class="video-widget p-2">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
             Video
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#${settingsId}"
                    aria-controls="${settingsId}">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div id="${videoId}" class="video-container d-flex align-items-center justify-content-center">
          <div class="text-center text-muted">
            <i class="las la-film la-3x mb-3"></i>
            <p>No video selected.<br>Use settings to add a video.</p>
          </div>
        </div>
      </div>
    `,
  });

  // Create settings panel in the offcanvas container
  const offcanvasContainer = document.getElementById("offcanvasContainer");
  const settingsPanel = document.createElement("div");
  settingsPanel.className = "offcanvas offcanvas-end";
  settingsPanel.id = settingsId;
  settingsPanel.setAttribute("tabindex", "-1");
  settingsPanel.innerHTML = `
    <div class="offcanvas-header">
      <h5 class="offcanvas-title">Video Settings</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- Video Source Type -->
      <div class="mb-3">
        <label class="form-label">Video Source</label>
        <select class="form-select" id="${settingsId}-source" onchange="toggleVideoSource('${settingsId}')">
          <option value="file">Local File</option>
          <option value="url">URL</option>
          <option value="youtube">YouTube</option>
        </select>
      </div>

      <!-- File Upload -->
      <div class="mb-3" id="${settingsId}-file-input">
        <label for="${settingsId}-upload" class="form-label">Upload Video</label>
        <input class="form-control" type="file" id="${settingsId}-upload" accept="video/*" onchange="previewVideo('${videoId}', this)">
      </div>

      <!-- URL Input -->
      <div class="mb-3 d-none" id="${settingsId}-url-input">
        <label for="${settingsId}-url" class="form-label">Video URL</label>
        <input type="text" class="form-control" id="${settingsId}-url" placeholder="https://example.com/video.mp4">
      </div>

      <!-- YouTube Input -->
      <div class="mb-3 d-none" id="${settingsId}-youtube-input">
        <label for="${settingsId}-youtube" class="form-label">YouTube Video ID or URL</label>
        <input type="text" class="form-control" id="${settingsId}-youtube" placeholder="Enter YouTube video ID or URL">
      </div>

      <!-- Video Controls -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-controls" checked>
          <label class="form-check-label" for="${settingsId}-controls">Show Video Controls</label>
        </div>
      </div>

      <!-- Autoplay -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-autoplay">
          <label class="form-check-label" for="${settingsId}-autoplay">Autoplay</label>
        </div>
      </div>

      <!-- Loop -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-loop">
          <label class="form-check-label" for="${settingsId}-loop">Loop Video</label>
        </div>
      </div>

      <!-- Muted -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-muted">
          <label class="form-check-label" for="${settingsId}-muted">Start Muted</label>
        </div>
      </div>

      <!-- Background Color -->
      <div class="mb-3">
        <label for="${settingsId}-bgcolor" class="form-label">Background Color</label>
        <input type="color" class="form-control form-control-color" id="${settingsId}-bgcolor" value="#000000">
      </div>

      <!-- Border -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-border">
          <label class="form-check-label" for="${settingsId}-border">Show Border</label>
        </div>
      </div>

      <!-- Border Radius -->
      <div class="mb-3">
        <label class="form-label">Border Radius (px)</label>
        <input type="range" class="form-range" min="0" max="50" value="0" id="${settingsId}-radius">
      </div>

      <!-- Apply Button -->
      <button class="btn btn-primary w-100" onclick="applyVideoSettings('${videoId}', '${settingsId}')">
        Apply Changes
      </button>
    </div>
  `;
  offcanvasContainer.appendChild(settingsPanel);

  // Initialize the video widget
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing video widget");
      initVideoWidget(videoId);
    } catch (error) {
      console.error("Error initializing video widget:", error);
    }
  }, 100);

  return widget;
}

// Function to initialize the video widget
function initVideoWidget(videoId) {
  console.log("Initializing video widget:", videoId);
  const settingsId = "settings-" + videoId;

  // Set up the source field toggling
  toggleVideoSource(settingsId);

  console.log("Video widget initialized:", videoId);
}

// Function to toggle video source fields based on selected source
function toggleVideoSource(settingsId) {
  const sourceSelect = document.getElementById(`${settingsId}-source`);
  if (!sourceSelect) {
    console.error("Source select not found for:", settingsId);
    return;
  }

  const selectedSource = sourceSelect.value;

  // Get the file, URL, and YouTube input containers
  const fileInput = document.getElementById(`${settingsId}-file-input`);
  const urlInput = document.getElementById(`${settingsId}-url-input`);
  const youtubeInput = document.getElementById(`${settingsId}-youtube-input`);

  // Hide all inputs first
  if (fileInput) fileInput.classList.add("d-none");
  if (urlInput) urlInput.classList.add("d-none");
  if (youtubeInput) youtubeInput.classList.add("d-none");

  // Show the selected input
  switch (selectedSource) {
    case "file":
      if (fileInput) fileInput.classList.remove("d-none");
      break;
    case "url":
      if (urlInput) urlInput.classList.remove("d-none");
      break;
    case "youtube":
      if (youtubeInput) youtubeInput.classList.remove("d-none");
      break;
  }
}

// Function to handle backdrop cleanup
function handleBackdropCleanup() {
  // Remove all backdrops
  const backdrops = document.querySelectorAll(".offcanvas-backdrop");
  backdrops.forEach((backdrop) => {
    backdrop.remove();
  });
}

// Function to initialize video settings
function initVideoSettings(videoId, settingsId) {
  const settingsPanel = document.getElementById(settingsId);
  if (!settingsPanel) return;

  // Initialize offcanvas with proper options
  const bsOffcanvas = new bootstrap.Offcanvas(settingsPanel, {
    backdrop: true,
    keyboard: true,
    scroll: false,
  });

  // Remove any existing event listeners
  settingsPanel.removeEventListener(
    "hidden.bs.offcanvas",
    handleBackdropCleanup
  );
  // Add event listener for when offcanvas is hidden
  settingsPanel.addEventListener("hidden.bs.offcanvas", handleBackdropCleanup);

  // Show the offcanvas
  bsOffcanvas.show();
}

// Function to apply video settings
function applyVideoSettings(videoId, settingsId) {
  const container = document.getElementById(videoId);
  if (!container) return;

  const sourceType = document.getElementById(`${settingsId}-source`).value;
  const autoplay = document.getElementById(`${settingsId}-autoplay`).checked;
  const mute = document.getElementById(`${settingsId}-muted`).checked;
  const loop = document.getElementById(`${settingsId}-loop`).checked;
  const controls = document.getElementById(`${settingsId}-controls`).checked;

  // Clear the container
  container.innerHTML = "";

  // Create the appropriate embed based on source type
  switch (sourceType) {
    case "youtube":
      const youtubeId = document
        .getElementById(`${settingsId}-youtube`)
        .value.trim();
      if (youtubeId) {
        const youtubeParams = new URLSearchParams({
          autoplay: autoplay ? 1 : 0,
          mute: mute ? 1 : 0,
          loop: loop ? 1 : 0,
          controls: controls ? 1 : 0,
          rel: 0,
          modestbranding: 1,
        });

        if (loop) {
          youtubeParams.append("playlist", youtubeId);
        }

        container.innerHTML = `
          <iframe
            width="100%"
            height="100%"
            src="https://www.youtube.com/embed/${youtubeId}?${youtubeParams.toString()}"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen>
          </iframe>
        `;
      }
      break;

    case "url":
      const videoUrl = document
        .getElementById(`${settingsId}-url`)
        .value.trim();
      if (videoUrl) {
        container.innerHTML = `
          <video
            width="100%"
            height="100%"
            ${autoplay ? "autoplay" : ""}
            ${mute ? "muted" : ""}
            ${loop ? "loop" : ""}
            ${controls ? "controls" : ""}>
            <source src="${videoUrl}" type="video/mp4">
            Your browser does not support the video tag.
          </video>
        `;
      }
      break;

    case "file":
      const fileInput = document.getElementById(`${settingsId}-upload`);
      const file = fileInput.files[0];
      if (file) {
        container.innerHTML = `
          <video
            width="100%"
            height="100%"
            ${autoplay ? "autoplay" : ""}
            ${mute ? "muted" : ""}
            ${loop ? "loop" : ""}
            ${controls ? "controls" : ""}>
            <source src="${file.name}" type="${file.type}">
            Your browser does not support the video tag.
          </video>
        `;
      }
      break;
  }

  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(
    document.getElementById(settingsId)
  );
  if (offcanvas) {
    offcanvas.hide();
  }
}

// Export the functions
window.addVideoWidget = addVideoWidget;
window.initVideoWidget = initVideoWidget;
window.toggleVideoSource = toggleVideoSource;
window.applyVideoSettings = applyVideoSettings;
