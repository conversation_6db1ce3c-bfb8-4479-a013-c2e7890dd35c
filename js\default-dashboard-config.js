// Default dashboard configuration with predefined widgets
const defaultDashboardConfig = {
  grid: {
    column: 12,
    cellHeight: "auto",
    disableOneColumnMode: true
  },
  widgets: [
    // Text widget with realistic content
    {
      x: 0, y: 0, w: 6, h: 4,
      id: "widget-text-1",
      content: `<div class="widget-header">
                  <div class="widget-title">Q2 2025 Performance Summary</div>
                  <div class="widget-icons">
                    <i class="fas fa-cog widget-settings"></i>
                    <i class="fas fa-times widget-close"></i>
                  </div>
                </div>
                <div class="widget-body p-3">
                  <h4>Market Analysis Highlights</h4>
                  <p>Revenue increased by <strong>17.8%</strong> compared to previous quarter, exceeding market expectations by 3.2 percentage points. Key growth drivers include:</p>
                  <ul>
                    <li>Enterprise segment expansion in APAC region (+28%)</li>
                    <li>New strategic partnerships with Fortune 500 clients</li>
                    <li>Successful product launch in emerging markets</li>
                  </ul>
                  <p>Customer retention rate improved to <strong>94.3%</strong>, indicating high satisfaction with recent platform enhancements.</p>
                </div>`
    },
    // Bar chart widget with realistic visualization
    {
      x: 6, y: 0, w: 6, h: 4,
      id: "widget-chart-1",
      content: `<div class="widget-header">
                  <div class="widget-title">Regional Revenue Distribution</div>
                  <div class="widget-icons">
                    <i class="fas fa-cog widget-settings"></i>
                    <i class="fas fa-times widget-close"></i>
                  </div>
                </div>
                <div class="widget-body">
                  <div id="barChart1" class="chart-container"></div>
                  <script>
                    document.addEventListener('DOMContentLoaded', function() {
                      if (typeof am5 !== 'undefined') {
                        // Create root element
                        var root = am5.Root.new("barChart1");
                        
                        // Set themes
                        root.setThemes([am5themes_Animated.new(root)]);
                        
                        // Create chart
                        var chart = root.container.children.push(
                          am5xy.XYChart.new(root, {
                            panX: false,
                            panY: false,
                            wheelX: "none",
                            wheelY: "none",
                            paddingLeft: 0
                          })
                        );
                        
                        // Add legend
                        var legend = chart.children.push(
                          am5.Legend.new(root, {
                            centerX: am5.p50,
                            x: am5.p50
                          })
                        );
                        
                        // Create axes
                        var xAxis = chart.xAxes.push(
                          am5xy.CategoryAxis.new(root, {
                            categoryField: "region",
                            renderer: am5xy.AxisRendererX.new(root, {
                              cellStartLocation: 0.1,
                              cellEndLocation: 0.9
                            }),
                            tooltip: am5.Tooltip.new(root, {})
                          })
                        );
                        
                        xAxis.data.setAll([
                          { region: "North America" },
                          { region: "Europe" },
                          { region: "APAC" },
                          { region: "LATAM" },
                          { region: "MEA" }
                        ]);
                        
                        var yAxis = chart.yAxes.push(
                          am5xy.ValueAxis.new(root, {
                            renderer: am5xy.AxisRendererY.new(root, {})
                          })
                        );
                        
                        // Add series
                        function makeSeries(name, fieldName, data, color) {
                          var series = chart.series.push(
                            am5xy.ColumnSeries.new(root, {
                              name: name,
                              xAxis: xAxis,
                              yAxis: yAxis,
                              valueYField: fieldName,
                              categoryXField: "region",
                              tooltip: am5.Tooltip.new(root, {
                                labelText: "{valueY}"
                              })
                            })
                          );
                          
                          series.columns.template.setAll({
                            tooltipY: 0,
                            strokeOpacity: 0,
                            cornerRadiusTL: 4,
                            cornerRadiusTR: 4
                          });
                          
                          if (color) {
                            series.set("fill", am5.color(color));
                          }
                          
                          series.data.setAll(data);
                          series.appear();
                          
                          return series;
                        }
                        
                        // Add data
                        var data = [
                          { region: "North America", value: 2400 },
                          { region: "Europe", value: 1800 },
                          { region: "APAC", value: 2900 },
                          { region: "LATAM", value: 900 },
                          { region: "MEA", value: 500 }
                        ];
                        
                        makeSeries("Revenue ($K)", "value", data, "#007365");
                        
                        // Add legend
                        legend.data.setAll(chart.series.values);
                      }
                    });
                  </script>
                </div>`
    },
    // Section container with nested widgets
    {
      x: 0, y: 4, w: 12, h: 8,
      id: "section-container-1",
      content: `<div class="widget-header">
                  <div class="widget-title">Product Performance Dashboard</div>
                  <div class="widget-icons">
                    <i class="fas fa-cog widget-settings"></i>
                    <i class="fas fa-times widget-close"></i>
                  </div>
                </div>
                <div class="nested-grid-container"></div>`,
      isSection: true,
      nestedGrid: {
        widgets: [
          // Pie chart nested widget 
          {
            x: 0, y: 0, w: 6, h: 4,
            id: "nested-widget-1",
            content: `<div class="widget-header">
                        <div class="widget-title">Product Category Distribution</div>
                        <div class="widget-icons">
                          <i class="fas fa-cog widget-settings"></i>
                          <i class="fas fa-times widget-close"></i>
                        </div>
                      </div>
                      <div class="widget-body">
                        <div id="pieChart1" class="chart-container"></div>
                        <script>
                          document.addEventListener('DOMContentLoaded', function() {
                            setTimeout(function() {
                              if (typeof am5 !== 'undefined') {
                                // Create root element
                                var root = am5.Root.new("pieChart1");
                                
                                // Set themes
                                root.setThemes([am5themes_Animated.new(root)]);
                                
                                // Create chart
                                var chart = root.container.children.push(
                                  am5percent.PieChart.new(root, {
                                    layout: root.verticalLayout,
                                    innerRadius: am5.percent(50)
                                  })
                                );
                                
                                // Create series
                                var series = chart.series.push(
                                  am5percent.PieSeries.new(root, {
                                    valueField: "value",
                                    categoryField: "category",
                                    endAngle: 270
                                  })
                                );
                                
                                // Set data
                                series.data.setAll([
                                  { category: "SaaS Products", value: 42 },
                                  { category: "Professional Services", value: 28 },
                                  { category: "Hardware Solutions", value: 18 },
                                  { category: "Managed Services", value: 12 }
                                ]);
                                
                                // Create legend
                                var legend = chart.children.push(
                                  am5.Legend.new(root, {
                                    centerX: am5.percent(50),
                                    x: am5.percent(50),
                                    layout: root.horizontalLayout
                                  })
                                );
                                
                                legend.data.setAll(series.dataItems);
                              }
                            }, 500);
                          });
                        </script>
                      </div>`
          },
          // Line chart nested widget
          {
            x: 6, y: 0, w: 6, h: 4,
            id: "nested-widget-2",
            content: `<div class="widget-header">
                        <div class="widget-title">Monthly Sales Trends</div>
                        <div class="widget-icons">
                          <i class="fas fa-cog widget-settings"></i>
                          <i class="fas fa-times widget-close"></i>
                        </div>
                      </div>
                      <div class="widget-body">
                        <div id="lineChart1" class="chart-container"></div>
                        <script>
                          document.addEventListener('DOMContentLoaded', function() {
                            setTimeout(function() {
                              if (typeof am5 !== 'undefined') {
                                // Create root element
                                var root = am5.Root.new("lineChart1");
                                
                                // Set themes
                                root.setThemes([am5themes_Animated.new(root)]);
                                
                                // Create chart
                                var chart = root.container.children.push(
                                  am5xy.XYChart.new(root, {
                                    panX: false,
                                    panY: false,
                                    wheelX: "none",
                                    wheelY: "none",
                                    paddingLeft: 0
                                  })
                                );
                                
                                // Create axes
                                var xAxis = chart.xAxes.push(
                                  am5xy.CategoryAxis.new(root, {
                                    categoryField: "month",
                                    renderer: am5xy.AxisRendererX.new(root, {}),
                                    tooltip: am5.Tooltip.new(root, {})
                                  })
                                );
                                
                                xAxis.data.setAll([
                                  { month: "Jan" },
                                  { month: "Feb" },
                                  { month: "Mar" },
                                  { month: "Apr" },
                                  { month: "May" },
                                  { month: "Jun" }
                                ]);
                                
                                var yAxis = chart.yAxes.push(
                                  am5xy.ValueAxis.new(root, {
                                    renderer: am5xy.AxisRendererY.new(root, {})
                                  })
                                );
                                
                                // Add series
                                var series = chart.series.push(
                                  am5xy.LineSeries.new(root, {
                                    name: "Sales",
                                    xAxis: xAxis,
                                    yAxis: yAxis,
                                    valueYField: "value",
                                    categoryXField: "month",
                                    tooltip: am5.Tooltip.new(root, {
                                      labelText: "${valueY}K"
                                    })
                                  })
                                );
                                
                                series.strokes.template.setAll({
                                  strokeWidth: 3
                                });
                                
                                series.fills.template.setAll({
                                  fillOpacity: 0.3,
                                  visible: true
                                });
                                
                                // Add data
                                series.data.setAll([
                                  { month: "Jan", value: 890 },
                                  { month: "Feb", value: 740 },
                                  { month: "Mar", value: 920 },
                                  { month: "Apr", value: 1020 },
                                  { month: "May", value: 1180 },
                                  { month: "Jun", value: 1340 }
                                ]);
                                
                                // Add bullets
                                series.bullets.push(function() {
                                  return am5.Bullet.new(root, {
                                    sprite: am5.Circle.new(root, {
                                      radius: 5,
                                      fill: series.get("fill"),
                                      stroke: root.interfaceColors.get("background"),
                                      strokeWidth: 2
                                    })
                                  });
                                });
                                
                                // Add cursor
                                chart.set("cursor", am5xy.XYCursor.new(root, {}));
                              }
                            }, 500);
                          });
                        </script>
                      </div>`
          }
        ]
      }
    }
  ]
}; 