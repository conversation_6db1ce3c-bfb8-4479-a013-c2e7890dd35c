/* Specific fixes for area chart height issues */

/* Override widget-height-fix.css styles */
.area-chart-widget {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.area-chart-widget .widget-body {
  flex: 1 1 auto !important;
  min-height: 0 !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.area-chart-widget .chart-container {
  flex: 1 !important;
  height: 100% !important;
  min-height: 300px !important;
  position: relative !important;
}

/* Ensure grid stack items containing area charts have proper height */
.grid-stack-item-content:has(.area-chart-widget) {
  height: 100% !important;
  overflow: visible !important;
}
