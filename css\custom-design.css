/* Author: <PERSON>aisar */
/* Genric css for ampro design  */
.grid-stack-item-content, .section-container-widget, .kpi-widget-wrapper, .tab-container-widget, .grid-placeholder, .price-chart-widget, .chart-type-select, .chart-period-select{
    border-radius: 0 !important;
}
.tab-container-tabs {
    gap: 1px;
    background-color: #fff;
}
.tab-container-tabs .tab-nav-item {
     background-color: #dfebed !important; 
}
.grid-stack > .grid-stack-item > .grid-stack-item-content{
    padding: 0 !important;
}
.grid-stack{
    border-radius: 0 !important;
}
/* Bottom tab design css  */
.grid-stack-item-content .tab-widget-container.bottom-tabs .nav-tabs{
    background-color: transparent;
    border: none;
    gap: 1px;
}
.grid-stack-item-content .tab-widget-container.bottom-tabs .nav-tabs .nav-item{
    flex: 1;
}
.grid-stack-item-content .tab-widget-container.bottom-tabs .nav-tabs .nav-item .nav-link{
    width:100%;
    background-color: #dfebed !important;
    height: 100%;
    align-items: center !important;
}
.grid-stack-item-content .tab-widget-container.bottom-tabs .nav-tabs .nav-item .nav-link:hover{
       background-color: #bbd6dc !important; 
}
.grid-stack-item-content .tab-widget-container.bottom-tabs .nav-tabs .nav-item .nav-link span{
    color: #231f20 !important;
    font-size: 14px !important; 
}
.grid-stack-item-content .tab-widget-container.bottom-tabs .nav-tabs .nav-item .nav-link.active{
   background-color: #bbd6dc !important; 
}
.tab-widget-container.bottom-tabs .nav-tabs .nav-link i{
    color: #02104f !important;
}


/* Preview remove padding css  */

.grid-stack-item-content.preview, .grid-stack-item-content.preview .grid-stack-item, .grid-stack-item-content.preview .grid-stack-item-content, .grid-stack-item-content.preview .grid-stack-item.grid-stack-sub-grid, .grid-stack-item-content.preview .section-container-widget{ 
    padding: 0 !important;
    margin: 0 !important;
}
.grid-stack > .grid-stack-item > .grid-stack-item-content.preview{
   padding: 0 !important;
    margin: 0 !important; 
}
.grid-stack-item-content.preview .grid-stack-item.grid-stack-sub-grid.smart-composer-preview-mode {
    box-shadow: none !important;
}
.grid-stack-item.grid-stack-sub-grid.smart-composer-preview-mode {
    box-shadow: none !important;
    padding: 0 !important;
}




/* toolip design change custom  */

.tooltip {
    font-size: 11px;
    font-family: "Montserrat", serif !important;
}
.tooltip-inner {
    border: 1px solid #02104f;
    background-color: white;
    color: #231f20;
    border-radius: 0;
    max-width: 200px;
    padding: 0.25rem 0.5rem;
}



/* Header changes css  */
.form-control:focus{
    border-color: inherit;
    box-shadow: none;
}
.sergroup .la-search, .serbtn .la-search{
   color: #231f20; 
}
.fs-5 {
    font-size: 1rem !important;
}
.card-body .widget-body h6{
    font-size: 12px !important;
}
.card-body .noteSection{
    line-height: 1.1;
}
.card-body .noteSection small{
    font-size: 10px;
}
