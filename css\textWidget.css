/* Text Widget Styles */
.text-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-widget .widget-header {
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #000;
  border-radius: 6px 6px 0 0;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-widget .widget-header button {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.text-widget .widget-header button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.text-container {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}

.text-content {
  width: 100%;
  height: 100%;
  resize: none;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background-color: #fff;
  outline: none;
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.text-content:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.text-content:empty:before {
  content: "Click here to edit text...";
  color: #999;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .text-widget .widget-header {
    padding: 8px 12px;
    font-size: 12px;
  }

  .text-container {
    padding: 12px;
  }

  .text-content {
    font-size: 12px;
    padding: 8px;
  }
}

/* Settings panel styles */
.text-widget .offcanvas-body {
  padding: 20px;
}

.text-widget .form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.text-widget .form-control,
.text-widget .form-select {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.text-widget .form-control:focus,
.text-widget .form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  outline: none;
}

.text-widget .form-range {
  margin: 8px 0;
}

.text-widget .form-check-input:checked {
  background-color: #667eea;
  border-color: #667eea;
}

.text-widget .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-weight: 600;
  transition: transform 0.2s, box-shadow 0.2s;
}

.text-widget .btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Text formatting styles */
.text-content strong,
.text-content b {
  font-weight: bold;
}

.text-content em,
.text-content i {
  font-style: italic;
}

.text-content u {
  text-decoration: underline;
}

/* Prevent text selection on widget header */
.text-widget .widget-header {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Ensure proper spacing in grid */
.grid-stack-item .text-widget {
  margin: 0;
  height: 100%;
}

/* Dark mode support (if needed) */

/* Animation for smooth transitions */
.text-widget * {
  transition: all 0.2s ease;
}

/* Ensure text widget works well in different container sizes */
.text-widget.compact {
  font-size: 12px;
}

.text-widget.compact .text-content {
  font-size: 11px;
  padding: 6px;
}

.text-widget.compact .widget-header {
  padding: 8px 12px;
  font-size: 12px;
}

/* Print styles */
@media print {
  .text-widget .widget-header button {
    display: none;
  }

  .text-widget {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
