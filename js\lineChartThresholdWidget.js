// Line Chart with Threshold Widget using amCharts v4
function addLineChartThresholdWidget() {
  console.log("Adding Line Chart with Threshold widget");
  const chartId = "linethreshold-" + Date.now();

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 8,
    content: `
      <div class="line-threshold-widget p-2">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
            <i class="las la-chart-line"></i> Line Chart with Threshold
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#lineThresholdSettings"
                    aria-controls="lineThresholdSettings"
                    onclick="initLineThresholdSettings('${chartId}')">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div id="${chartId}" class="chart-container"></div>
      </div>
    `,
  });

  // Initialize the chart with a slight delay
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing line chart with threshold");
      window.initLineThreshold(chartId);
    } catch (error) {
      console.error("Error initializing line chart with threshold:", error);
    }
  }, 1000);

  return widget;
}

// Initialize a line chart with threshold using amCharts v4
window.initLineThreshold = async function (containerId) {
  console.log("Starting line chart with threshold initialization for container:", containerId);
  const container = document.getElementById(containerId);

  if (!container) {
    console.error("Chart container not found:", containerId);
    return;
  }

  // Make sure the container fills the available space
  container.style.width = "100%";
  container.style.height = "100%";
  container.style.overflow = "hidden";

  console.log(
    "Container dimensions:",
    container.offsetWidth,
    "x",
    container.offsetHeight
  );

  // Create chart instance
  let chart = am4core.create(containerId, am4charts.XYChart);

  // Load data from tsc2.0.json
  const chartData = await getLineChartThresholdData();

  // Default data in case loading fails
  let defaultData = [
    {
      "category": "2011",
      "us": 1.2,
      "uk": 3.2
    },
    {
      "category": "2012",
      "us": 2.3,
      "uk": 1.3
    },
    {
      "category": "2013",
      "us": 2.2,
      "uk": 2.1
    },
    {
      "category": "2014",
      "us": 1.7,
      "uk": 1.6
    },
    {
      "category": "2015",
      "us": 1.3,
      "uk": 1.8
    },
    {
      "category": "2016",
      "us": 2.1,
      "uk": 1.9
    },
    {
      "category": "2017",
      "us": 2.9,
      "uk": 2.0
    },
    {
      "category": "2018",
      "us": 2.5,
      "uk": 2.3
    }
  ];

  // If we successfully loaded data from the file, use it
  if (chartData && chartData.data && chartData.data.length > 0) {
    chart.data = chartData.data;
  } else {
    chart.data = defaultData;
  }

  // Create axes
  let categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
  categoryAxis.dataFields.category = "category";
  categoryAxis.title.text = chartData?.xAxisTitle || "Period";
  categoryAxis.renderer.grid.template.location = 0;
  categoryAxis.renderer.minGridDistance = 30;

  let valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
  valueAxis.title.text = chartData?.yAxisTitle || "Price ($/b)";

  // Create series for US
  let series1 = chart.series.push(new am4charts.LineSeries());
  series1.name = "US";
  series1.dataFields.valueY = "us";
  series1.dataFields.categoryX = "category";
  series1.stroke = am4core.color(window.chartConfig.brandColors[0]);
  series1.strokeWidth = 3;
  series1.tooltipText = "US: [bold]{valueY}[/]";

  // Add bullets to first series
  let bullet1 = series1.bullets.push(new am4charts.CircleBullet());
  bullet1.circle.fill = am4core.color(window.chartConfig.brandColors[0]);
  bullet1.circle.strokeWidth = 2;

  // Create series for UK
  let series2 = chart.series.push(new am4charts.LineSeries());
  series2.name = "UK";
  series2.dataFields.valueY = "uk";
  series2.dataFields.categoryX = "category";
  series2.stroke = am4core.color(window.chartConfig.brandColors[1]);
  series2.strokeWidth = 3;
  series2.tooltipText = "UK: [bold]{valueY}[/]";

  // Add bullets to second series
  let bullet2 = series2.bullets.push(new am4charts.CircleBullet());
  bullet2.circle.fill = am4core.color(window.chartConfig.brandColors[1]);
  bullet2.circle.strokeWidth = 2;

  // Add chart title
  let title = chart.titles.create();
  title.text = chartData?.title || "Global Crude Oil Prices ($/barrel, 2011–2018)";
  title.fontSize = 16;
  title.marginBottom = 15;

  // Add legend
  chart.legend = new am4charts.Legend();
  chart.legend.position = "bottom";
  chart.legend.contentAlign = "center";

  // Add cursor
  chart.cursor = new am4charts.XYCursor();
  chart.cursor.behavior = "zoomY";

  // Add a green threshold line
  let greenRange = valueAxis.axisRanges.create();
  greenRange.value = chartData?.greenThreshold || 3;
  greenRange.grid.stroke = am4core.color("#00FF00");
  greenRange.grid.strokeWidth = 2;
  greenRange.grid.strokeOpacity = 1;
  greenRange.label.inside = true;
  greenRange.label.text = "Green Threshold";
  greenRange.label.fill = am4core.color("#00FF00");

  // Add a red threshold line
  let redRange = valueAxis.axisRanges.create();
  redRange.value = chartData?.redThreshold || 3.5;
  redRange.grid.stroke = am4core.color("#FF0000");
  redRange.grid.strokeWidth = 2;
  redRange.grid.strokeOpacity = 1;
  redRange.label.inside = true;
  redRange.label.text = "Red Threshold";
  redRange.label.fill = am4core.color("#FF0000");

  // Store chart instance and thresholds in the container for later access
  container.chart = chart;
  container.greenThreshold = greenRange;
  container.redThreshold = redRange;

  // Add resize event listener to handle container resizing
  const resizeObserver = new ResizeObserver(() => {
    chart.invalidateSize();
  });

  resizeObserver.observe(container);

  // Store the observer in the container for cleanup
  container.resizeObserver = resizeObserver;
};

// Initialize line threshold settings panel
window.initLineThresholdSettings = function (chartId) {
  console.log("Initializing line threshold settings for:", chartId);

  // Store the current chart ID for the settings panel
  document.getElementById("lineThresholdSettings").dataset.chartId = chartId;

  const container = document.getElementById(chartId);
  if (!container || !container.chart) {
    console.error("Chart not found or not initialized");
    return;
  }

  const chart = container.chart;

  // Load chart title
  const chartTitle = chart.titles.getIndex(0);
  if (chartTitle) {
    document.getElementById("lineThresholdSettings-chartTitle").value = chartTitle.text;
  }

  // Load Y-axis title
  const yAxis = chart.yAxes.getIndex(0);
  if (yAxis) {
    document.getElementById("lineThresholdSettings-yAxisTitle").value = yAxis.title.text;
  }

  // Load X-axis title
  const xAxis = chart.xAxes.getIndex(0);
  if (xAxis) {
    document.getElementById("lineThresholdSettings-xAxisTitle").value = xAxis.title.text;
  }

  // Load threshold values
  if (container.greenThreshold) {
    document.getElementById("lineThresholdSettings-greenThreshold").value = container.greenThreshold.value;
  }

  if (container.redThreshold) {
    document.getElementById("lineThresholdSettings-redThreshold").value = container.redThreshold.value;
  }

  // Load legend visibility
  document.getElementById("lineThresholdSettings-legend").checked = chart.legend !== undefined;

  // Load data into the table
  const dataBody = document.getElementById("lineThresholdDataBody");
  dataBody.innerHTML = ""; // Clear existing rows

  chart.data.forEach((item) => {
    addLineThresholdDataRow(item.category, item.us, item.uk);
  });
};

// Apply line threshold settings
window.applyLineThresholdSettings = function () {
  const settingsPanel = document.getElementById("lineThresholdSettings");
  const chartId = settingsPanel.dataset.chartId;

  const container = document.getElementById(chartId);
  if (!container || !container.chart) {
    console.error("Chart not found or not initialized");
    return;
  }

  const chart = container.chart;

  // Update chart title
  const chartTitle = chart.titles.getIndex(0);
  if (chartTitle) {
    chartTitle.text = document.getElementById("lineThresholdSettings-chartTitle").value;
  }

  // Update Y-axis title
  const yAxis = chart.yAxes.getIndex(0);
  if (yAxis) {
    yAxis.title.text = document.getElementById("lineThresholdSettings-yAxisTitle").value;
  }

  // Update X-axis title
  const xAxis = chart.xAxes.getIndex(0);
  if (xAxis) {
    xAxis.title.text = document.getElementById("lineThresholdSettings-xAxisTitle").value;
  }

  // Update threshold values
  if (container.greenThreshold) {
    container.greenThreshold.value = parseFloat(document.getElementById("lineThresholdSettings-greenThreshold").value) || 0;
  }

  if (container.redThreshold) {
    container.redThreshold.value = parseFloat(document.getElementById("lineThresholdSettings-redThreshold").value) || 0;
  }

  // Update legend visibility
  const showLegend = document.getElementById("lineThresholdSettings-legend").checked;
  if (showLegend && !chart.legend) {
    chart.legend = new am4charts.Legend();
    chart.legend.position = "bottom";
    chart.legend.contentAlign = "center";
  } else if (!showLegend && chart.legend) {
    chart.legend.dispose();
    chart.legend = undefined;
  }

  // Collect data from the table
  const dataRows = document.querySelectorAll("#lineThresholdDataBody tr");
  const newData = [];

  dataRows.forEach(row => {
    const categoryInput = row.querySelector(".category-input");
    const usInput = row.querySelector(".us-input");
    const ukInput = row.querySelector(".uk-input");

    if (categoryInput && categoryInput.value.trim()) {
      newData.push({
        category: categoryInput.value.trim(),
        us: parseFloat(usInput.value) || 0,
        uk: parseFloat(ukInput.value) || 0
      });
    }
  });

  // Update chart data if we have rows
  if (newData.length > 0) {
    chart.data = newData;
  }

  // Close the settings panel
  const offcanvasElement = bootstrap.Offcanvas.getInstance(settingsPanel);
  if (offcanvasElement) {
    offcanvasElement.hide();
  }
};

// Helper function to add a data row with specified values
function addLineThresholdDataRow(category, us, uk) {
  const dataBody = document.getElementById("lineThresholdDataBody");
  const rowId = "linethreshold-row-" + Date.now() + "-" + Math.floor(Math.random() * 1000);

  const row = document.createElement("tr");
  row.id = rowId;
  row.innerHTML = `
    <td>
      <input type="text" class="form-control form-control-sm category-input" value="${category || ''}">
    </td>
    <td>
      <input type="number" class="form-control form-control-sm us-input" value="${us || 0}" step="0.1">
    </td>
    <td>
      <input type="number" class="form-control form-control-sm uk-input" value="${uk || 0}" step="0.1">
    </td>
    <td>
      <button class="btn btn-sm btn-outline-danger" onclick="removeLineThresholdDataRow('${rowId}')">
        <i class="las la-trash"></i>
      </button>
    </td>
  `;

  dataBody.appendChild(row);
}

// Function to add a new empty data row
function addNewLineThresholdDataRow() {
  addLineThresholdDataRow("", 0, 0);
}

// Function to remove a data row
function removeLineThresholdDataRow(rowId) {
  const row = document.getElementById(rowId);
  if (row) {
    row.remove();
  }
}
