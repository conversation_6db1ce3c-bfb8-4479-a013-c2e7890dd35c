// Add a 100% stacked column chart widget using amCharts v5

// Series configuration
window.percentSeriesDefaults = [
  { id: 1, name: "Electronics", field: "electronics" },
  { id: 2, name: "Apparel", field: "apparel" },
  { id: 3, name: "Home & Garden", field: "homeGarden" },
  { id: 4, name: "Sports", field: "sports" },
  { id: 5, name: "Books", field: "books" },
  { id: 6, name: "Toys", field: "toys" },
  { id: 7, name: "Beauty", field: "beauty" },
  { id: 8, name: "Food", field: "food" },
  { id: 9, name: "Health", field: "health" },
  { id: 10, name: "Auto", field: "auto" },
];

// Add a 100% stacked column chart widget using amCharts v5
window.addPercentStackedColumnChartWidget = function () {
  console.log("Adding 100% stacked column chart widget");
  const chartId = "percent-stacked-column-" + Date.now();

  // Get the grid instance
  const grid = document.querySelector(".grid-stack").gridstack;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 8,
    content: `
      <div class="percent-stacked-column-chart-widget p-2" style="height: 100%; display: flex; flex-direction: column;">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
            100% Stacked Column Chart
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#percentStackedColumnChartSettings"
                    aria-controls="percentStackedColumnChartSettings"
                    onclick="initPercentStackedColumnChartSettings('${chartId}')">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
          <div id="${chartId}" class="chart-container" style="flex: 1; width: 100%; height: 100%; min-height: 300px; position: relative;"></div>
        </div>
      </div>
    `,
  });

  // Initialize the chart with a slight delay to ensure DOM is ready
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing chart");
      initPercentStackedColumnChart(chartId);
    } catch (error) {
      console.error("Error initializing chart:", error);
    }
  }, 100);

  return widget;
};

// Initialize the 100% stacked column chart
window.initPercentStackedColumnChart = function (chartId) {
  console.log("Initializing 100% stacked column chart:", chartId);

  // Get the chart container
  const chartContainer = document.getElementById(chartId);
  if (!chartContainer) {
    console.error("Chart container not found:", chartId);
    return;
  }

  // Force overflow visible on parent gridstack item content
  const gridStackItemContent = chartContainer.closest(
    ".grid-stack-item-content"
  );
  if (gridStackItemContent) {
    gridStackItemContent.style.overflow = "visible";
    gridStackItemContent.style.height = "100%";
  }

  // Get the widget container
  const widgetContainer = chartContainer.closest(
    ".percent-stacked-column-chart-widget"
  );
  if (widgetContainer) {
    widgetContainer.style.height = "100%";
    widgetContainer.style.display = "flex";
    widgetContainer.style.flexDirection = "column";
  }

  // Dispose of any existing chart instance
  if (chartContainer.chart) {
    chartContainer.chart.root.dispose();
  }

  // Clear the container
  chartContainer.innerHTML = "";

  // Create root element
  const root = am5.Root.new(chartId);

  // Set themes
  root.setThemes([am5themes_Animated.new(root)]);

  // Create a container for the chart and title
  const chartWrapper = root.container.children.push(
    am5.Container.new(root, {
      width: am5.p100,
      height: am5.p100,
      layout: root.verticalLayout,
    })
  );

  // Add chart title at the top of the container
  chartWrapper.children.push(
    am5.Label.new(root, {
      text: "Sales Distribution by Category",
      fontSize: 14,
      fontWeight: "500",
      textAlign: "center",
      x: am5.p50,
      centerX: am5.p50,
      paddingTop: 5,
      paddingBottom: 10,
      fill: am5.color(0x000000), // Black text color
      role: "title", // Used to identify this as the chart title
    })
  );

  // Create chart inside the container
  const chart = chartWrapper.children.push(
    am5xy.XYChart.new(root, {
      panX: false,
      panY: false,
      wheelX: "none",
      wheelY: "none",
      layout: root.verticalLayout,
      paddingTop: 10,
      paddingBottom: 20,
      paddingLeft: 10,
      paddingRight: 10,
      width: am5.p100,
      height: am5.p100,
    })
  );

  // Apply brand colors if available
  if (window.chartConfig && window.chartConfig.brandColors) {
    const brandColorSet = am5.ColorSet.new(root, {
      colors: window.chartConfig.brandColors.map((color) => am5.color(color)),
      reuse: true,
    });
    chart.set("colors", brandColorSet);
    console.log("Applied brand colors to 100% stacked column chart");
  } else {
    console.warn("Brand colors not found. Using default theme colors.");
  }

  // Add legend
  const legend = chart.children.push(
    am5.Legend.new(root, {
      centerX: am5.p50,
      x: am5.p50,
      layout: root.horizontalLayout,
      marginTop: 15,
      marginBottom: 15,
    })
  );

  // Customize legend labels
  legend.labels.template.setAll({
    fontSize: 12,
    fontWeight: "400",
    fill: am5.color(0x000000),
  });

  // Create axes
  const xAxis = chart.xAxes.push(
    am5xy.CategoryAxis.new(root, {
      categoryField: "date",
      renderer: am5xy.AxisRendererX.new(root, {
        cellStartLocation: 0.1,
        cellEndLocation: 0.9,
        minGridDistance: 30,
      }),
      tooltip: am5.Tooltip.new(root, {}),
    })
  );

  // Style X-axis labels
  xAxis.get("renderer").labels.template.setAll({
    fontSize: 11,
    fill: am5.color(0x666666),
  });

  // Create Y-axis with special settings for 100% stacked chart
  const yAxis = chart.yAxes.push(
    am5xy.ValueAxis.new(root, {
      min: 0,
      max: 100,
      strictMinMax: true,
      calculateTotals: true,
      numberFormat: "#'%'",
      renderer: am5xy.AxisRendererY.new(root, {
        minGridDistance: 30,
      }),
    })
  );

  // Style Y-axis labels
  yAxis.get("renderer").labels.template.setAll({
    fontSize: 11,
    fill: am5.color(0x666666),
  });

  // Style Y-axis grid
  yAxis.get("renderer").grid.template.setAll({
    stroke: am5.color(0xdddddd),
    strokeWidth: 1,
    strokeDasharray: [2, 2],
  });

  // Set data
  const data = [
    {
      date: "Jan",
      first: 1250,
      second: 1850,
      third: 950,
    },
    {
      date: "Feb",
      first: 1400,
      second: 1650,
      third: 1100,
    },
    {
      date: "Mar",
      first: 1800,
      second: 2100,
      third: 1350,
    },
    {
      date: "Apr",
      first: 2200,
      second: 1900,
      third: 1500,
    },
    {
      date: "May",
      first: 2500,
      second: 2300,
      third: 1800,
    },
    {
      date: "Jun",
      first: 2300,
      second: 2100,
      third: 1600,
    },
  ];

  xAxis.data.setAll(data);

  // Create series
  function makeSeries(name, fieldName, index) {
    const series = chart.series.push(
      am5xy.ColumnSeries.new(root, {
        name: name,
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: fieldName,
        categoryXField: "date",
        stacked: true,
        // Key settings for 100% stacked chart
        valueYShow: "valueYTotalPercent",
        tooltip: am5.Tooltip.new(root, {
          labelText: "{name}: {valueY.formatNumber('#.0')}%",
        }),
      })
    );

    // Get brand colors
    const brandColors = window.chartConfig?.brandColors || [
      "#00b19c",
      "#3bcd3f",
      "#007365",
      "#8dbac4",
      "#02104f",
    ];

    // Set series colors and properties
    series.columns.template.setAll({
      tooltipText: "{name}, {categoryX}: {valueY.formatNumber('#.0')}%",
      width: am5.percent(90),
      tooltipY: am5.percent(10),
      strokeOpacity: 0,
      fillOpacity: 0.8,
      fill: am5.color(brandColors[index % brandColors.length]),
    });

    // Add hover state
    series.columns.template.states.create("hover", {
      fillOpacity: 1,
    });

    series.data.setAll(data);
    series.appear();

    return series;
  }

  // Add series
  makeSeries("First", "first", 0);
  makeSeries("Second", "second", 1);
  makeSeries("Third", "third", 2);

  // Make stuff animate on load
  chart.appear(1000, 100);

  // Store chart instance on container for future reference
  chartContainer.chart = chart;

  return chart;
};

// Function to initialize settings panel for the chart
window.initPercentStackedColumnChartSettings = function (chartId) {
  console.log("Initializing settings for 100% stacked column chart:", chartId);

  const settingsPanel = document.getElementById(
    "percentStackedColumnChartSettings"
  );
  if (!settingsPanel) {
    console.error("Settings panel not found");
    return;
  }

  // Store the current chart ID
  settingsPanel.dataset.currentChart = chartId;

  // Initialize offcanvas with proper options
  const bsOffcanvas = new bootstrap.Offcanvas(settingsPanel, {
    backdrop: true,
    keyboard: true,
    scroll: false,
  });

  // Remove any existing event listeners
  settingsPanel.removeEventListener(
    "hidden.bs.offcanvas",
    handleBackdropCleanup
  );
  // Add event listener for when offcanvas is hidden
  settingsPanel.addEventListener("hidden.bs.offcanvas", handleBackdropCleanup);

  // Show the offcanvas
  bsOffcanvas.show();

  // We'll implement the settings loading in a future update
  // loadPercentStackedColumnChartSettings(chartId);
};

// Function to apply settings from the settings panel
window.applyPercentStackedColumnChartSettings = function () {
  console.log("Applying 100% stacked column chart settings");

  const settingsPanel = document.getElementById(
    "percentStackedColumnChartSettings"
  );
  const chartId = settingsPanel.dataset.currentChart;

  if (!chartId) {
    console.error("No chart ID found in settings panel");
    return;
  }

  const chartContainer = document.getElementById(chartId);
  if (!chartContainer || !chartContainer.chart) {
    console.error("Chart not found or not initialized");
    return;
  }

  const chart = chartContainer.chart;

  // Get settings values
  const chartTitle = document.getElementById(
    "percentStackedColumnChartSettings-chartTitle"
  ).value;
  const yAxisTitle = document.getElementById(
    "percentStackedColumnChartSettings-yAxisTitle"
  ).value;
  const xAxisTitle = document.getElementById(
    "percentStackedColumnChartSettings-xAxisTitle"
  ).value;
  const showLegend = document.getElementById(
    "percentStackedColumnChartSettings-legend"
  ).checked;

  // Update chart title
  const title = chart.root.container.children.getIndex(0).children.getIndex(0);
  if (title) {
    title.set("text", chartTitle);
  }

  // Update Y-axis title
  const yAxis = chart.yAxes.getIndex(0);
  if (yAxis) {
    yAxis.set("title", yAxisTitle);
  }

  // Update X-axis title
  const xAxis = chart.xAxes.getIndex(0);
  if (xAxis) {
    xAxis.set("title", xAxisTitle);
  }

  // Update legend visibility
  const legend = chart.children.getIndex(0);
  if (legend) {
    legend.set("visible", showLegend);
  }

  // Close the settings panel
  const bsOffcanvas = bootstrap.Offcanvas.getInstance(settingsPanel);
  if (bsOffcanvas) {
    bsOffcanvas.hide();
  }
};

// Add the widget to the widget gallery
document.addEventListener("DOMContentLoaded", function () {
  // Add widget to the chart category in the widget gallery
  const chartCategory = document.querySelector(
    '.widget-gallery[data-category="charts"]'
  );
  if (chartCategory) {
    const widgetItem = document.createElement("div");
    widgetItem.className = "widget-item";
    widgetItem.setAttribute("draggable", "true");
    widgetItem.setAttribute("data-gs-width", "6");
    widgetItem.setAttribute("data-gs-height", "8");
    widgetItem.innerHTML = `
      <i class="las la-chart-bar"></i>
      <span class="widget-label">100% Stacked Column</span>
    `;
    widgetItem.addEventListener("click", function () {
      addPercentStackedColumnChartWidget();
    });
    chartCategory.appendChild(widgetItem);
  }
});
