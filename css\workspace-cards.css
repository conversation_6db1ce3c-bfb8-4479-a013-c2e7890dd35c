/* ===================================================
   WORKSPACE CARDS - Modern Alternative to Tab Container
   ===================================================
   A Pinterest-style card interface that replaces traditional tabs
   with draggable, interactive workspace cards
   =================================================== */

:root {
  --workspace-primary: #6366f1;
  --workspace-secondary: #8b5cf6;
  --workspace-success: #10b981;
  --workspace-warning: #f59e0b;
  --workspace-danger: #ef4444;
  --workspace-dark: #1f2937;
  --workspace-light: #f8fafc;
  --workspace-border: #e5e7eb;
  --workspace-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --workspace-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --workspace-gradient: linear-gradient(
    135deg,
    var(--workspace-primary) 0%,
    var(--workspace-secondary) 100%
  );
}

/* ===================================================
   WORKSPACE CONTAINER
   =================================================== */
.workspace-cards-container {
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.workspace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px 24px;
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.workspace-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
}

.workspace-title h2 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.workspace-title .workspace-icon {
  font-size: 2rem;
  color: #fbbf24;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.workspace-stats {
  display: flex;
  gap: 16px;
  align-items: center;
}

.workspace-stat {
  background: rgba(255, 255, 255, 0.15);
  padding: 8px 16px;
  border-radius: 12px;
  color: white;
  font-size: 0.875rem;
  font-weight: 600;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.workspace-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.workspace-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px 20px;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  gap: 8px;
}

.workspace-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.workspace-btn.primary {
  background: linear-gradient(45deg, #10b981, #059669);
  border: none;
}

.workspace-btn.primary:hover {
  background: linear-gradient(45deg, #059669, #047857);
}

/* ===================================================
   WORKSPACE SEARCH & FILTERS
   =================================================== */
.workspace-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.workspace-search {
  flex: 1;
  max-width: 400px;
  position: relative;
}

.workspace-search input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: var(--workspace-shadow);
}

.workspace-search input:focus {
  outline: none;
  background: white;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
  transform: translateY(-1px);
}

.workspace-search .search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 1.25rem;
}

.workspace-filters {
  display: flex;
  gap: 12px;
  align-items: center;
}

.filter-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  padding: 10px 16px;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  color: #374151;
  backdrop-filter: blur(10px);
}

.filter-btn:hover,
.filter-btn.active {
  background: white;
  color: var(--workspace-primary);
  transform: translateY(-1px);
  box-shadow: var(--workspace-shadow);
}

/* ===================================================
   WORKSPACE CARDS GRID
   =================================================== */
.workspace-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.workspace-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  position: relative;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.workspace-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  background: white;
}

.workspace-card.active {
  border: 2px solid var(--workspace-primary);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.2);
}

.workspace-card.dragging {
  transform: rotate(5deg) scale(1.05);
  z-index: 1000;
  opacity: 0.9;
}

/* Card Header */
.workspace-card-header {
  padding: 20px 24px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid var(--workspace-border);
  position: relative;
}

.workspace-card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.workspace-card-title h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--workspace-dark);
  display: flex;
  align-items: center;
  gap: 8px;
}

.workspace-card-icon {
  width: 32px;
  height: 32px;
  background: var(--workspace-gradient);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.workspace-card-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.workspace-card:hover .workspace-card-actions {
  opacity: 1;
}

.card-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-action-btn:hover {
  background: var(--workspace-primary);
  color: white;
  transform: scale(1.1);
}

.workspace-card-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 0.875rem;
  color: #6b7280;
}

.widget-count-badge {
  background: var(--workspace-primary);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.last-modified {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Card Preview */
.workspace-card-preview {
  padding: 20px 24px;
  min-height: 200px;
  background: white;
  position: relative;
}

.preview-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  height: 160px;
}

.preview-widget {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px dashed #cbd5e0;
}

.preview-widget.has-content {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 2px solid #60a5fa;
  color: #1e40af;
}

.preview-widget:hover {
  transform: scale(1.05);
  box-shadow: var(--workspace-shadow);
}

.empty-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 160px;
  color: #9ca3af;
  text-align: center;
}

.empty-preview i {
  font-size: 2.5rem;
  margin-bottom: 12px;
  opacity: 0.5;
}

/* Card Footer */
.workspace-card-footer {
  padding: 16px 24px 20px;
  background: #f8fafc;
  border-top: 1px solid var(--workspace-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workspace-card-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.workspace-tag {
  background: rgba(99, 102, 241, 0.1);
  color: var(--workspace-primary);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
}

.workspace-card-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--workspace-success);
}

.status-indicator.warning {
  background: var(--workspace-warning);
}

.status-indicator.error {
  background: var(--workspace-danger);
}

/* ===================================================
   ADD NEW WORKSPACE CARD
   =================================================== */
.add-workspace-card {
  background: rgba(255, 255, 255, 0.3);
  border: 2px dashed rgba(255, 255, 255, 0.5);
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.add-workspace-card:hover {
  background: rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.7);
  transform: translateY(-4px);
}

.add-workspace-card i {
  font-size: 3rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 16px;
}

.add-workspace-card h3 {
  color: white;
  margin: 0 0 8px 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.add-workspace-card p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  text-align: center;
  font-size: 0.875rem;
}

/* ===================================================
   WORKSPACE OVERLAY & MODALS
   =================================================== */
.workspace-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.workspace-overlay.active {
  opacity: 1;
  visibility: visible;
}

.workspace-modal {
  background: white;
  border-radius: 24px;
  padding: 32px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: var(--workspace-shadow-lg);
  transform: scale(0.9) translateY(20px);
  transition: transform 0.3s ease;
}

.workspace-overlay.active .workspace-modal {
  transform: scale(1) translateY(0);
}

.workspace-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--workspace-border);
}

.workspace-modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--workspace-dark);
  margin: 0;
}

.modal-close-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 12px;
  background: #f3f4f6;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close-btn:hover {
  background: #e5e7eb;
  color: var(--workspace-dark);
}

/* ===================================================
   WIDGET ASSIGNMENT PANEL
   =================================================== */
.workspace-widget-panel {
  position: fixed;
  right: -400px;
  top: 0;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  transition: right 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 9999;
  overflow-y: auto;
}

.workspace-widget-panel.active {
  right: 0;
}

.widget-panel-header {
  padding: 24px;
  background: var(--workspace-gradient);
  color: white;
  position: sticky;
  top: 0;
  z-index: 100;
}

.widget-panel-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.widget-panel-subtitle {
  font-size: 0.875rem;
  opacity: 0.9;
  margin: 0;
}

.widget-panel-content {
  padding: 24px;
}

.widget-category {
  margin-bottom: 32px;
}

.widget-category-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--workspace-dark);
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--workspace-border);
}

.widget-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.widget-item {
  background: #f8fafc;
  border: 2px solid var(--workspace-border);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.widget-item:hover {
  background: #e0f2fe;
  border-color: var(--workspace-primary);
  transform: translateY(-2px);
}

.widget-item.assigned {
  background: #dcfce7;
  border-color: var(--workspace-success);
}

.widget-item i {
  font-size: 1.5rem;
  color: var(--workspace-primary);
  margin-bottom: 8px;
  display: block;
}

.widget-item.assigned i {
  color: var(--workspace-success);
}

.widget-item-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--workspace-dark);
  margin: 0;
}

/* ===================================================
   RESPONSIVE DESIGN
   =================================================== */
@media (max-width: 1200px) {
  .workspace-cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .workspace-cards-container {
    padding: 16px;
  }

  .workspace-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .workspace-controls {
    flex-direction: column;
    gap: 12px;
  }

  .workspace-search {
    max-width: none;
  }

  .workspace-cards-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .workspace-widget-panel {
    width: 100%;
    right: -100%;
  }

  .workspace-modal {
    margin: 16px;
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .workspace-card-header {
    padding: 16px;
  }

  .workspace-card-preview {
    padding: 16px;
    min-height: 150px;
  }

  .preview-grid {
    height: 120px;
    gap: 8px;
  }

  .workspace-stats {
    flex-direction: column;
    gap: 8px;
  }
}

/* ===================================================
   ANIMATIONS & EFFECTS
   =================================================== */
@keyframes cardAppear {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.workspace-card {
  animation: cardAppear 0.5s ease-out;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.workspace-card.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
.workspace-widget-panel::-webkit-scrollbar {
  width: 6px;
}

.workspace-widget-panel::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.workspace-widget-panel::-webkit-scrollbar-thumb {
  background: var(--workspace-primary);
  border-radius: 3px;
}

.workspace-widget-panel::-webkit-scrollbar-thumb:hover {
  background: var(--workspace-secondary);
}
