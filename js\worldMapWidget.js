let worldMapChartCounter = 0;

// Add a World Map Chart widget using amCharts v5
function addWorldMapWidget(initialConfig = {}) {
  const widgetId = `world-map-${worldMapChartCounter++}`;
  const chartContainerId = `world-map-container-${widgetId}`;
  const settingsId = `worldMapSettings-${widgetId}`;

  // Default data structure for world map
  const defaultData = {
    regions: [
      {
        id: "US",
        name: "United States",
        value: 100,
        fill: am5.color("#00c8b3"),
      },
      {
        id: "GB",
        name: "United Kingdom",
        value: 80,
        fill: am5.color("#60da94"),
      },
      {
        id: "CN",
        name: "China",
        value: 90,
        fill: am5.color("#007f71"),
      },
      {
        id: "IN",
        name: "India",
        value: 85,
        fill: am5.color("#a8e6cf"),
      },
    ],
  };

  const config = {
    title: initialConfig.title || "World Map Chart",
    data: initialConfig.data || defaultData,
    notes:
      initialConfig.notes || "Default Notes: Please provide specific notes.",
    source:
      initialConfig.source ||
      "Default Source: Please provide a specific source.",
    lastUpdate: initialConfig.lastUpdate || "N/A",
    nextUpdate: initialConfig.nextUpdate || "N/A",
  };

  const grid = window.grid;
  const newWidget = grid.addWidget({
    w: initialConfig.w || 12,
    h: initialConfig.h || 12,
    content: `
      <div class="world-map-widget widget p-2" id="${widgetId}" style="height: 100%; display: flex; flex-direction: column;">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div class="widget-title editable-title" data-editable="true" title="Click to edit title">
             
              <span>${config.title}</span>
          </div>
          <div class="widget-actions">
            <button class="btn btn-link" data-bs-toggle="offcanvas" data-bs-target="#${settingsId}" aria-controls="${settingsId}">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-link ms-1" onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex;">
          <div id="${chartContainerId}" class="chart-container" style="width: 100%; height: 100%; position: relative; min-height: 300px;"></div>
        </div>
        <div class="widget-footer mt-2" style="padding: 0.5rem 0; border-top: 1px solid #e5e9f0; font-size: 10px; color: #6c757d; text-align: left;">
          ${
            config.notes
              ? `<div><i class="las la-clipboard"></i> Notes : ${config.notes}</div>`
              : ""
          }
          ${
            config.source
              ? `<div><i class="las la-database"></i> Source : ${config.source}</div>`
              : ""
          }
          ${
            config.lastUpdate || config.nextUpdate
              ? `
          <div class="d-flex mt-1">
            ${
              config.lastUpdate
                ? `<span><i class="las la-calendar-alt"></i> Last update : ${config.lastUpdate}</span>`
                : "<span></span>"
            }
            ${
              config.nextUpdate
                ? `<span class="ms-3"><i class="las la-calendar-plus"></i> Next update : ${config.nextUpdate}</span>`
                : ""
            }
          </div>`
              : ""
          }
        </div>
      </div>
    `,
  });

  // Store config on the widget element
  const widgetElement = document.getElementById(widgetId);
  widgetElement.widgetConfig = config;
  widgetElement.chartContainerId = chartContainerId;

  // Add script for India geodata if not already loaded
  if (!window.am5geodata_worldIndiaLow) {
    const script = document.createElement("script");
    script.src = "//cdn.amcharts.com/lib/5/geodata/worldIndiaLow.js";
    script.onload = () => {
      initializeWorldMap(chartContainerId, config);
    };
    document.head.appendChild(script);
  } else {
    // Initialize the chart after a short delay
    setTimeout(() => {
      if (typeof am5 !== "undefined" && typeof am5map !== "undefined") {
        initializeWorldMap(chartContainerId, config);
      } else {
        console.error("amCharts core or map libraries not loaded properly");
      }
    }, 100);
  }

  // Make title editable
  if (typeof makeTitleEditable === "function") {
    makeTitleEditable(widgetId);
  }

  // Create settings panel
  createWorldMapSettingsOffcanvas(
    settingsId,
    widgetId,
    chartContainerId,
    config
  );
}

function initializeWorldMap(chartContainerId, config) {
  const chartContainerElement = document.getElementById(chartContainerId);
  if (!chartContainerElement) return;

  // Dispose existing chart if any
  if (chartContainerElement.amRoot) {
    chartContainerElement.amRoot.dispose();
  }

  // Create root element
  const root = am5.Root.new(chartContainerId);
  chartContainerElement.amRoot = root;

  // Set themes
  root.setThemes([am5themes_Animated.new(root)]);

  // Create the map chart
  const chart = root.container.children.push(
    am5map.MapChart.new(root, {
      panX: "rotateX",
      panY: "translateY",
      // projection: am5map.geoNaturalEarth1(),
      homeGeoPoint: { latitude: 20, longitude: 78 },
      homeZoomLevel: 2,
    })
  );

  // Get colors from brand colors or defaults
  let colors;
  if (window.chartConfig && window.chartConfig.brandColors) {
    colors = am5.ColorSet.new(root, {
      colors: window.chartConfig.brandColors.map((hex) => am5.color(hex)),
      reuse: true,
    });
  } else {
    colors = am5.ColorSet.new(root, {
      colors: [
        am5.color("#00c8b3"), // Teal
        am5.color("#60da94"), // Light green
        am5.color("#007f71"), // Dark teal
        am5.color("#a8e6cf"), // Pale teal
      ],
      reuse: true,
    });
  }

  // Create polygon series
  const polygonSeries = chart.series.push(
    am5map.MapPolygonSeries.new(root, {
      geoJSON: am5geodata_worldIndiaLow,
      exclude: ["AQ"],
      valueField: "value",
      calculateAggregates: true,
      propertyFields: {
        fill: "fill",
      },
    })
  );

  polygonSeries.mapPolygons.template.setAll({
    tooltipText: "{name}: {value}",
    interactive: true,
    fill: am5.color("#888888"),
    templateField: "polygonSettings",
  });

  // Create hover state with the specified green color
  const hoverState = polygonSeries.mapPolygons.template.states.create("hover", {
    fill: am5.color(0x677935),
  });

  // Set data for specific regions with special handling for India
  const regionData = config.data.regions.map((region) => ({
    id: region.id,
    value: region.value,
    name: region.name,
    polygonSettings: {
      fill:
        region.id === "IN"
          ? am5.color("#00c8b3")
          : region.fill || colors.next(),
    },
  }));

  polygonSeries.data.setAll(regionData);

  // Add zoom control
  chart.set("zoomControl", am5map.ZoomControl.new(root, {}));

  // Add button to zoom to home
  const homeButton = chart.children.push(
    am5.Button.new(root, {
      paddingTop: 10,
      paddingBottom: 10,
      x: am5.percent(100),
      centerX: am5.percent(100),
      opacity: 0.5,
      interactiveChildren: false,
      icon: am5.Graphics.new(root, {
        svgPath:
          "M16,8 L14,8 L14,16 L10,16 L10,10 L6,10 L6,16 L2,16 L2,8 L0,8 L8,0 L16,8 Z M16,8",
        fill: am5.color(0x000000),
      }),
    })
  );

  homeButton.events.on("click", function () {
    chart.goHome();
  });

  // Make chart fade in
  chart.appear(1000, 100);

  // Add resize observer
  const resizeObserver = new ResizeObserver(() => {
    root.resize();
  });

  resizeObserver.observe(chartContainerElement);
  chartContainerElement.resizeObserver = resizeObserver;
}

function createWorldMapSettingsOffcanvas(
  settingsId,
  widgetId,
  chartContainerId,
  currentConfig
) {
  const offcanvasContainer =
    document.getElementById("offcanvasContainer") || document.body;
  const dataJson = JSON.stringify(currentConfig.data, null, 2);

  const offcanvasHtml = `
    <div class="offcanvas offcanvas-end" tabindex="-1" id="${settingsId}" aria-labelledby="${settingsId}Label">
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="${settingsId}Label">World Map Settings</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div class="offcanvas-body">
        <div class="mb-3">
          <label class="form-label">Chart Title</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-chartTitle" value="${currentConfig.title}">
        </div>

        <div class="mb-3">
          <label class="form-label">Data (JSON format)</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-data" rows="10" spellcheck="false">${dataJson}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Notes</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-notes" rows="2">${currentConfig.notes}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Source</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-source" rows="2">${currentConfig.source}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Last Update</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-lastUpdate" value="${currentConfig.lastUpdate}">
        </div>

        <div class="mb-3">
          <label class="form-label">Next Update</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-nextUpdate" value="${currentConfig.nextUpdate}">
        </div>

        <button class="btn btn-primary w-100" onclick="applyWorldMapSettings('${widgetId}', '${settingsId}', '${chartContainerId}')">Apply Changes</button>
      </div>
    </div>
  `;

  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = offcanvasHtml;
  offcanvasContainer.appendChild(tempDiv.firstElementChild);
}

function applyWorldMapSettings(widgetId, settingsId, chartContainerId) {
  const widgetElement = document.getElementById(widgetId);
  if (!widgetElement || !widgetElement.widgetConfig) return;

  const currentConfig = widgetElement.widgetConfig;

  try {
    currentConfig.title = document.getElementById(
      `${settingsId}-chartTitle`
    ).value;
    const dataInput = document.getElementById(`${settingsId}-data`).value;
    currentConfig.data = JSON.parse(dataInput);
    currentConfig.notes = document.getElementById(`${settingsId}-notes`).value;
    currentConfig.source = document.getElementById(
      `${settingsId}-source`
    ).value;
    currentConfig.lastUpdate = document.getElementById(
      `${settingsId}-lastUpdate`
    ).value;
    currentConfig.nextUpdate = document.getElementById(
      `${settingsId}-nextUpdate`
    ).value;

    // Update widget title
    const titleSpan = widgetElement.querySelector(".widget-title span");
    if (titleSpan) {
      titleSpan.textContent = currentConfig.title;
    }

    // Update footer
    if (typeof updateWidgetFooter === "function") {
      updateWidgetFooter(widgetElement, currentConfig);
    }

    // Re-initialize chart
    initializeWorldMap(chartContainerId, currentConfig);

    // Close settings panel
    const offcanvasElement = document.getElementById(settingsId);
    if (offcanvasElement) {
      const offcanvasInstance =
        bootstrap.Offcanvas.getInstance(offcanvasElement);
      if (offcanvasInstance) {
        offcanvasInstance.hide();
      }
    }
  } catch (e) {
    console.error("Error applying settings:", e);
    alert("Error: Invalid JSON format in Data. Please correct and try again.");
  }
}

// Export functions to global scope
window.addWorldMapWidget = addWorldMapWidget;
window.applyWorldMapSettings = applyWorldMapSettings;
