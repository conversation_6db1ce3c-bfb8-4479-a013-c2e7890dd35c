/* Table Widget Styles */
.table-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-widget .widget-header {
  padding: 12px 16px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border-radius: 6px 6px 0 0;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-widget .widget-header button {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.table-widget .widget-header button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.table-container {
  flex: 1;
  padding: 16px;
  overflow: auto;
}

.table-widget .table {
  margin-bottom: 0;
  font-size: 14px;
  border-collapse: collapse;
}

.table-widget .table th,
.table-widget .table td {
  padding: 8px 12px;
  vertical-align: middle;
  border: 1px solid #dee2e6;
  transition: all 0.2s ease;
}

.table-widget .table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-widget .table td[contenteditable="true"],
.table-widget .table th[contenteditable="true"] {
  cursor: text;
  outline: none;
  position: relative;
}

.table-widget .table td[contenteditable="true"]:focus,
.table-widget .table th[contenteditable="true"]:focus {
  background-color: #e3f2fd;
  border-color: #2196f3;
  box-shadow: inset 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.table-widget .table td.editing,
.table-widget .table th.editing {
  background-color: #fff3cd;
  border-color: #ffc107;
}

/* Table hover effects */
.table-widget .table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Table striped rows */
.table-widget .table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.025);
}

/* Dark table theme */
.table-widget .table-dark {
  background-color: #343a40;
  color: #fff;
}

.table-widget .table-dark th,
.table-widget .table-dark td {
  border-color: #495057;
}

.table-widget .table-dark.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Small table */
.table-widget .table-sm th,
.table-widget .table-sm td {
  padding: 4px 8px;
  font-size: 12px;
}

/* Large table */
.table-widget .table-lg th,
.table-widget .table-lg td {
  padding: 12px 16px;
  font-size: 16px;
}

/* Header color themes */
.table-widget .table thead.table-primary th {
  background-color: #007bff;
  color: white;
  border-color: #0056b3;
}

.table-widget .table thead.table-secondary th {
  background-color: #6c757d;
  color: white;
  border-color: #545b62;
}

.table-widget .table thead.table-success th {
  background-color: #28a745;
  color: white;
  border-color: #1e7e34;
}

.table-widget .table thead.table-info th {
  background-color: #17a2b8;
  color: white;
  border-color: #117a8b;
}

.table-widget .table thead.table-warning th {
  background-color: #ffc107;
  color: #212529;
  border-color: #d39e00;
}

.table-widget .table thead.table-danger th {
  background-color: #dc3545;
  color: white;
  border-color: #bd2130;
}

.table-widget .table thead.table-light th {
  background-color: #f8f9fa;
  color: #495057;
  border-color: #dee2e6;
}

.table-widget .table thead.table-dark th {
  background-color: #343a40;
  color: #fff;
  border-color: #495057;
}

/* Responsive table wrapper */
.table-widget .table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-widget .table-responsive .table {
  margin-bottom: 0;
}

/* Cell selection and editing states */
.table-widget .table td[contenteditable="true"]:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.table-widget .table td[contenteditable="true"]:empty:before {
  content: "Click to edit";
  color: #6c757d;
  font-style: italic;
}

/* Scrollbar styling */
.table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .table-widget .widget-header {
    padding: 8px 12px;
    font-size: 12px;
  }

  .table-container {
    padding: 12px;
  }

  .table-widget .table {
    font-size: 12px;
  }

  .table-widget .table th,
  .table-widget .table td {
    padding: 6px 8px;
  }
}

/* Settings panel styles */
.table-widget .offcanvas-body {
  padding: 20px;
}

.table-widget .form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.table-widget .form-control,
.table-widget .form-select {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.table-widget .form-control:focus,
.table-widget .form-select:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
  outline: none;
}

.table-widget .form-range {
  margin: 8px 0;
}

.table-widget .form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

.table-widget .btn-primary {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-weight: 600;
  transition: transform 0.2s, box-shadow 0.2s;
}

.table-widget .btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.table-widget .btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;
}

.table-widget .btn-outline-secondary:hover {
  background-color: #6c757d;
  color: white;
}

.table-widget .btn-outline-danger {
  border-color: #dc3545;
  color: #dc3545;
}

.table-widget .btn-outline-danger:hover {
  background-color: #dc3545;
  color: white;
}

.table-widget .btn-outline-primary {
  border-color: #007bff;
  color: #007bff;
}

.table-widget .btn-outline-primary:hover {
  background-color: #007bff;
  color: white;
}

/* Prevent text selection on widget header */
.table-widget .widget-header {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Ensure proper spacing in grid */
.grid-stack-item .table-widget {
  margin: 0;
  height: 100%;
}

/* Animation for smooth transitions */
.table-widget * {
  transition: all 0.2s ease;
}

/* Print styles */
@media print {
  .table-widget .widget-header button {
    display: none;
  }

  .table-widget {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .table-widget .table {
    border-collapse: collapse;
  }

  .table-widget .table th,
  .table-widget .table td {
    border: 1px solid #000;
    padding: 4px 8px;
  }
}
