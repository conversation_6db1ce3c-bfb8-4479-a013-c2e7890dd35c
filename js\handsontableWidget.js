// Add a Handsontable widget
function addHandsontableWidget() {
  console.log("Adding Handsontable widget");
  const tableId = "handsontable-" + Date.now();
  const settingsId = "settings-" + tableId;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 8,
    content: `
      <div class="handsontable-widget p-2">
        <div class="widget-header mb-2">
          <div>
           Spreadsheet
          </div>
          <div>
            <button data-bs-toggle="offcanvas" data-bs-target="#${settingsId}" aria-controls="${settingsId}">
              <i class="las la-cog"></i>
            </button>
            <button onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div id="${tableId}" class="table-container"></div>
      </div>
    `,
  });

  // Create settings panel in the offcanvas container
  const offcanvasContainer = document.getElementById("offcanvasContainer");
  const settingsPanel = document.createElement("div");
  settingsPanel.className = "offcanvas offcanvas-end";
  settingsPanel.id = settingsId;
  settingsPanel.setAttribute("tabindex", "-1");
  settingsPanel.innerHTML = `
    <div class="offcanvas-header">
      <h5 class="offcanvas-title">Spreadsheet Settings</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- Row Header -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-rowHeaders" checked>
          <label class="form-check-label" for="${settingsId}-rowHeaders">Show Row Headers</label>
        </div>
      </div>

      <!-- Column Header -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-colHeaders" checked>
          <label class="form-check-label" for="${settingsId}-colHeaders">Show Column Headers</label>
        </div>
      </div>

      <!-- Stretchable -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-stretchH" checked>
          <label class="form-check-label" for="${settingsId}-stretchH">Stretch to Fill Width</label>
        </div>
      </div>

      <!-- Read Only -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-readOnly">
          <label class="form-check-label" for="${settingsId}-readOnly">Read Only</label>
        </div>
      </div>

      <!-- Context Menu -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-contextMenu" checked>
          <label class="form-check-label" for="${settingsId}-contextMenu">Enable Context Menu</label>
        </div>
      </div>

      <!-- Row Count -->
      <div class="mb-3">
        <label class="form-label">Number of Rows</label>
        <input type="number" class="form-control" id="${settingsId}-rowCount" min="5" max="100" value="10">
      </div>

      <!-- Column Count -->
      <div class="mb-3">
        <label class="form-label">Number of Columns</label>
        <input type="number" class="form-control" id="${settingsId}-colCount" min="5" max="26" value="10">
      </div>

      <!-- Apply Button -->
      <button class="btn btn-primary w-100" onclick="applyHandsontableSettings('${tableId}', '${settingsId}')">
        Apply Changes
      </button>

      <!-- Import/Export Buttons -->
      <div class="mt-3 d-flex gap-2">
        <button class="btn btn-outline-primary flex-grow-1" onclick="exportHandsontableData('${tableId}')">
          <i class="las la-download me-1"></i> Export
        </button>
        <label class="btn btn-outline-primary flex-grow-1 mb-0">
          <i class="las la-upload me-1"></i> Import
          <input type="file" id="${settingsId}-import" style="display: none;" onchange="importHandsontableData('${tableId}', this)">
        </label>
      </div>
    </div>
  `;
  offcanvasContainer.appendChild(settingsPanel);

  // Initialize Handsontable after a short delay to ensure the DOM is ready
  setTimeout(() => {
    const tableContainer = document.getElementById(tableId);
    if (tableContainer) {
      // Make sure the container is properly sized
      tableContainer.style.width = "100%";
      tableContainer.style.height = "100%";
      tableContainer.style.minHeight = "300px";
      
      try {
        // Initialize Handsontable with proper options
        const hot = new Handsontable(tableContainer, {
          data: generateSampleData(10, 6),
          rowHeaders: true,
          colHeaders: true,
          height: "100%",
          width: "100%",
          licenseKey: "non-commercial-and-evaluation",
          stretchH: "all",
          autoColumnSize: true,
          contextMenu: true,
          manualRowResize: true,
          manualColumnResize: true,
          outsideClickDeselects: false,
          disableVisualSelection: false,
          fragmentSelection: true,
          fillHandle: true,
          renderAllRows: true,
          wordWrap: false,
          fixedColumnsLeft: 0,
          fixedRowsTop: 0,
          copyPaste: true,
          preventOverflow: 'horizontal',
          
          // Add ALL event handlers to capture and stop propagation
          beforeOnCellMouseDown: function(event) {
            event.stopImmediatePropagation();
            event.stopPropagation();
            if (gridItem) gridItem.classList.add('editing-spreadsheet');
            return true;
          },
          afterOnCellMouseDown: function(event) {
            event.stopImmediatePropagation();
            event.stopPropagation();
          },
          beforeOnCellMouseUp: function(event) {
            event.stopImmediatePropagation();
            event.stopPropagation();
            return true;
          },
          afterOnCellMouseUp: function(event) {
            event.stopImmediatePropagation();
            event.stopPropagation();
          },
          beforeOnCellMouseOver: function(event) {
            event.stopImmediatePropagation();
            event.stopPropagation();
            return true;
          },
          afterOnCellMouseOver: function(event) {
            event.stopImmediatePropagation();
            event.stopPropagation();
          },
          beforeOnCellMouseOut: function(event) {
            event.stopImmediatePropagation();
            event.stopPropagation();
            return true;
          },
          afterOnCellMouseOut: function(event) {
            event.stopImmediatePropagation();
            event.stopPropagation();
          },
          beforeKeyDown: function(event) {
            event.stopImmediatePropagation();
            event.stopPropagation();
          },
          afterKeyDown: function(event) {
            event.stopImmediatePropagation();
            event.stopPropagation();
          },
          afterSelectionEnd: function() {
            if (gridItem) gridItem.classList.add('editing-spreadsheet');
          }
        });
        
        // Store the instance for later access
        tableContainer.hotInstance = hot;
        
        // Get the grid item for event handling
        const gridItem = tableContainer.closest('.grid-stack-item');
        
        // Add event listeners to handle interaction between grid and spreadsheet
        if (gridItem) {
          // While table has focus, disable grid-stack dragging
          tableContainer.addEventListener('mouseenter', function() {
            gridItem.classList.add('editing-spreadsheet');
            if (gridItem.gridstackNode && !gridItem.gridstackNode.locked) {
              gridItem.setAttribute('data-gs-no-drag', 'true');
              gridItem.style.pointerEvents = 'none';
              tableContainer.style.pointerEvents = 'auto';
            }
          });
          
          // Restore grid-stack dragging when mouse leaves
          tableContainer.addEventListener('mouseleave', function() {
            if (document.activeElement !== tableContainer && 
                !tableContainer.contains(document.activeElement)) {
              gridItem.classList.remove('editing-spreadsheet');
              if (gridItem.gridstackNode && !gridItem.gridstackNode.locked) {
                gridItem.removeAttribute('data-gs-no-drag');
                gridItem.style.pointerEvents = '';
              }
            }
          });
          
          // Handle document click to restore grid functionality
          document.addEventListener('click', function(e) {
            if (!tableContainer.contains(e.target)) {
              gridItem.classList.remove('editing-spreadsheet');
              if (gridItem.gridstackNode && !gridItem.gridstackNode.locked) {
                gridItem.removeAttribute('data-gs-no-drag');
                gridItem.style.pointerEvents = '';
              }
            }
          });
          
          // Force render after resize to fix display issues
          const resizeObserver = new ResizeObserver(() => {
            setTimeout(() => hot.render(), 0);
          });
          resizeObserver.observe(tableContainer);
        }
        
        console.log("Handsontable initialized successfully for", tableId);
      } catch (error) {
        console.error("Error initializing Handsontable:", error);
      }
    }
  }, 100);
  
  // Function to generate sample data for the spreadsheet
  function generateSampleData(rows = 10, cols = 6) {
    const data = [];
    for (let i = 0; i < rows; i++) {
      const row = [];
      for (let j = 0; j < cols; j++) {
        if (j === 0) {
          row.push(`Item ${i+1}`);
        } else {
          row.push(Math.floor(Math.random() * 100));
        }
      }
      data.push(row);
    }
    return data;
  }

  return widget;
}

// Function to apply Handsontable settings
function applyHandsontableSettings(tableId, settingsId) {
  const container = document.getElementById(tableId);
  if (!container || !container.hotInstance) return;

  const hot = container.hotInstance;

  const rowHeaders = document.getElementById(
    `${settingsId}-rowHeaders`
  ).checked;
  const colHeaders = document.getElementById(
    `${settingsId}-colHeaders`
  ).checked;
  const stretchH = document.getElementById(`${settingsId}-stretchH`).checked
    ? "all"
    : "none";
  const readOnly = document.getElementById(`${settingsId}-readOnly`).checked;
  const contextMenu = document.getElementById(
    `${settingsId}-contextMenu`
  ).checked;
  const rowCount = parseInt(
    document.getElementById(`${settingsId}-rowCount`).value
  );
  const colCount = parseInt(
    document.getElementById(`${settingsId}-colCount`).value
  );

  // Update settings
  hot.updateSettings({
    rowHeaders: rowHeaders,
    colHeaders: colHeaders,
    stretchH: stretchH,
    readOnly: readOnly,
    contextMenu: contextMenu,
  });

  // Adjust data size if needed
  const currentData = hot.getData();
  const newData = [];

  // Create new data array with desired dimensions
  for (let i = 0; i < rowCount; i++) {
    const row = [];
    for (let j = 0; j < colCount; j++) {
      // Preserve existing data if available
      if (i < currentData.length && j < currentData[i].length) {
        row.push(currentData[i][j]);
      } else {
        row.push(null);
      }
    }
    newData.push(row);
  }

  // Set the new data
  hot.loadData(newData);

  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(
    document.getElementById(settingsId)
  );
  offcanvas.hide();
}

// Function to export Handsontable data as CSV
function exportHandsontableData(tableId) {
  const container = document.getElementById(tableId);
  if (!container || !container.hotInstance) return;

  const hot = container.hotInstance;
  const exportPlugin = hot.getPlugin("exportFile");

  exportPlugin.downloadFile("csv", {
    filename: "spreadsheet-export",
    columnHeaders: true,
    rowHeaders: true,
  });
}

// Function to import data from CSV
function importHandsontableData(tableId, fileInput) {
  const container = document.getElementById(tableId);
  if (!container || !container.hotInstance || !fileInput.files.length) return;

  const hot = container.hotInstance;
  const file = fileInput.files[0];
  const reader = new FileReader();

  reader.onload = function (e) {
    const csv = e.target.result;
    const data = parseCSV(csv);
    hot.loadData(data);
    fileInput.value = "";
  };

  reader.readAsText(file);
}

// Helper function to parse CSV
function parseCSV(csv) {
  const lines = csv.split("\n");
  const result = [];

  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim() === "") continue;

    // Handle quoted values with commas inside
    const row = [];
    let inQuote = false;
    let currentValue = "";

    for (let j = 0; j < lines[i].length; j++) {
      const char = lines[i][j];

      if (char === '"') {
        inQuote = !inQuote;
      } else if (char === "," && !inQuote) {
        row.push(currentValue);
        currentValue = "";
      } else {
        currentValue += char;
      }
    }

    // Add the last value
    row.push(currentValue);
    result.push(row);
  }

  return result;
}

// Export the functions
window.addHandsontableWidget = addHandsontableWidget;
window.applyHandsontableSettings = applyHandsontableSettings;
window.exportHandsontableData = exportHandsontableData;
window.importHandsontableData = importHandsontableData;
