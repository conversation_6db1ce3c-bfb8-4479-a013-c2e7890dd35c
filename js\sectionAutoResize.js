// Custom Section Auto-Resize Script
// This script monitors child widgets in section containers and automatically
// adjusts the parent section height based on the content

class SectionAutoResize {
  constructor() {
    this.observers = new Map(); // Store ResizeObserver instances
    this.sectionContainers = new Map(); // Store section container references
    this.debounceTimers = new Map(); // Store debounce timers
    this.retryCounters = new Map(); // Store retry counters for section monitoring
    this.minSectionHeight = 120; // Minimum height for sections in pixels
    this.padding = 40; // Extra padding for section container
    this.maxRetries = 5; // Maximum number of retry attempts
    this.isDragging = false; // Track global drag state
    this.draggedElement = null; // Track the dragged element
    this.pausedSections = new Set(); // Track sections that should pause auto-resize

    this.init();
  }

  init() {
    console.log("Initializing Section Auto-Resize system");
    this.startSectionMonitoring();
    this.monitorExistingSections();
    this.setupGlobalDragDetection();
  }

  // Method to check if any drag operation is currently happening
  isDragOperationActive() {
    // Check global drag state
    if (this.isDragging) return true;

    // Check for drag-related classes on the body
    if (
      document.body.classList.contains("dragging-in-nested-grid") ||
      document.body.classList.contains("main-grid-dragging") ||
      document.body.classList.contains("widget-dragging")
    ) {
      return true;
    }

    // Check for any elements with drag-related classes
    const dragElements = document.querySelectorAll(
      ".ui-draggable-dragging, .grid-stack-item-dragging, .ui-resizable-resizing, .dragging"
    );

    return dragElements.length > 0;
  }

  setupGlobalDragDetection() {
    // Listen for GridStack drag events globally
    document.addEventListener("dragstart", (e) => {
      this.isDragging = true;
      this.draggedElement = e.target;
      console.log("Global drag started - pausing auto-resize");
    });

    document.addEventListener("dragend", (e) => {
      this.isDragging = false;
      this.draggedElement = null;
      console.log("Global drag ended - resuming auto-resize");

      // Resume auto-resize for all sections after a short delay
      setTimeout(() => {
        this.pausedSections.clear();
        this.resumeAllSections();
      }, 100);
    });

    // Also listen for GridStack-specific drag events
    document.addEventListener("mousedown", (e) => {
      // Check if this is a GridStack drag handle
      const gridStackItem = e.target.closest(".grid-stack-item");
      const dragHandle = e.target.closest(
        ".widget-header, .grid-stack-item-content"
      );

      if (gridStackItem && dragHandle) {
        // Check if this is a section container being dragged
        const sectionContainer = gridStackItem.querySelector(
          ".section-container-widget"
        );
        if (sectionContainer) {
          const sectionId = this.getSectionId(sectionContainer);
          if (sectionId) {
            this.pausedSections.add(sectionId);
            console.log(
              `Paused auto-resize for section ${sectionId} during drag`
            );
          }
        }
      }
    });

    document.addEventListener("mouseup", (e) => {
      // Resume auto-resize after mouse up
      setTimeout(() => {
        this.pausedSections.clear();
        this.resumeAllSections();
      }, 200);
    });

    // Listen for GridStack events if available
    if (window.grid) {
      this.setupGridStackDragEvents(window.grid);
    }

    // Monitor for new grids being created
    const originalAddGrid = window.GridStack?.addGrid;
    if (originalAddGrid) {
      window.GridStack.addGrid = (...args) => {
        const result = originalAddGrid.apply(window.GridStack, args);
        if (result) {
          this.setupGridStackDragEvents(result);
        }
        return result;
      };
    }

    // Also monitor for GridStack.init calls
    const originalInit = window.GridStack?.init;
    if (originalInit) {
      window.GridStack.init = (...args) => {
        const result = originalInit.apply(window.GridStack, args);
        if (result) {
          this.setupGridStackDragEvents(result);
        }
        return result;
      };
    }
  }

  setupGridStackDragEvents(grid) {
    if (!grid || grid._autoResizeDragEventsSetup) return;

    grid.on("dragstart", (event, el) => {
      this.isDragging = true;
      this.draggedElement = el;

      // Check if a section container is being dragged
      const sectionContainer = el.querySelector(".section-container-widget");
      if (sectionContainer) {
        const sectionId = this.getSectionId(sectionContainer);
        if (sectionId) {
          this.pausedSections.add(sectionId);
          console.log(
            `Paused auto-resize for section ${sectionId} during GridStack drag`
          );
        }
      }

      console.log("GridStack drag started - pausing auto-resize");
    });

    grid.on("dragstop", (event, el) => {
      this.isDragging = false;
      this.draggedElement = null;

      console.log("GridStack drag stopped - resuming auto-resize");

      // Resume auto-resize after a short delay
      setTimeout(() => {
        this.pausedSections.clear();
        this.resumeAllSections();
      }, 200);
    });

    grid.on("dropped", (event, previousWidget, newWidget) => {
      console.log("Widget dropped - will resume auto-resize after delay");

      // Resume auto-resize after drop with a longer delay
      setTimeout(() => {
        this.pausedSections.clear();
        this.resumeAllSections();
      }, 300);
    });

    grid._autoResizeDragEventsSetup = true;
  }

  resumeAllSections() {
    console.log("Resuming auto-resize for all sections");
    this.sectionContainers.forEach((section, sectionId) => {
      if (!this.pausedSections.has(sectionId)) {
        this.calculateAndSetHeight(sectionId);
      }
    });
  }

  startSectionMonitoring() {
    // Use MutationObserver to detect new section containers
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === "childList") {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Check if this is a section container or contains one
              const sections = node.querySelectorAll
                ? node.querySelectorAll(".section-container-widget")
                : [];

              if (
                node.classList &&
                node.classList.contains("section-container-widget")
              ) {
                this.setupSectionMonitoring(node);
              }

              sections.forEach((section) =>
                this.setupSectionMonitoring(section)
              );
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  monitorExistingSections() {
    // Monitor any existing section containers
    const existingSections = document.querySelectorAll(
      ".section-container-widget"
    );
    existingSections.forEach((section) => this.setupSectionMonitoring(section));
  }

  setupSectionMonitoring(sectionWidget) {
    const sectionId = this.getSectionId(sectionWidget);
    if (!sectionId) return;

    console.log("Setting up monitoring for section:", sectionId);

    // Find the nested grid container - try multiple selectors
    let nestedGrid = sectionWidget.querySelector(".grid-stack-nested");
    if (!nestedGrid) {
      // Also try just .grid-stack in case the nested class isn't applied yet
      nestedGrid = sectionWidget.querySelector(".section-content .grid-stack");
    }

    if (!nestedGrid) {
      // Check retry counter
      const retryCount = this.retryCounters.get(sectionId) || 0;
      if (retryCount >= this.maxRetries) {
        console.error(
          `Failed to find nested grid for section ${sectionId} after ${this.maxRetries} retries`
        );
        this.retryCounters.delete(sectionId);
        return;
      }

      console.warn(
        `No nested grid found in section: ${sectionId} - will retry in 200ms (attempt ${
          retryCount + 1
        }/${this.maxRetries})`
      );
      this.retryCounters.set(sectionId, retryCount + 1);

      // Retry after a short delay to handle async initialization
      setTimeout(() => {
        this.setupSectionMonitoring(sectionWidget);
      }, 200);
      return;
    }

    // Clear retry counter on success
    this.retryCounters.delete(sectionId);

    console.log(`Successfully found nested grid for section: ${sectionId}`);

    // Store references
    this.sectionContainers.set(sectionId, {
      widget: sectionWidget,
      nestedGrid: nestedGrid,
      gridstackItem: sectionWidget.closest(".grid-stack-item"),
    });

    // Set up ResizeObserver for the nested grid
    this.setupResizeObserver(sectionId, nestedGrid);

    // Set up MutationObserver for widget additions/removals
    this.setupMutationObserver(sectionId, nestedGrid);

    // Set up drag detection for the nested grid
    this.setupNestedGridDragEvents(sectionId, nestedGrid);

    // Initial height calculation
    this.calculateAndSetHeight(sectionId);
  }

  setupNestedGridDragEvents(sectionId, nestedGrid) {
    // Listen for drag events within the nested grid
    nestedGrid.addEventListener("dragover", (e) => {
      // Pause auto-resize when something is being dragged over this section
      this.pausedSections.add(sectionId);
    });

    nestedGrid.addEventListener("dragleave", (e) => {
      // Only resume if the drag truly left the nested grid
      if (!nestedGrid.contains(e.relatedTarget)) {
        setTimeout(() => {
          this.pausedSections.delete(sectionId);
        }, 100);
      }
    });

    nestedGrid.addEventListener("drop", (e) => {
      // Resume auto-resize after drop with a delay
      setTimeout(() => {
        this.pausedSections.delete(sectionId);
        this.calculateAndSetHeight(sectionId);
      }, 300);
    });

    // Also listen for GridStack events if the nested grid has a GridStack instance
    if (nestedGrid.gridstack) {
      this.setupGridStackDragEvents(nestedGrid.gridstack);
    }
  }

  setupResizeObserver(sectionId, nestedGrid) {
    if (this.observers.has(sectionId)) {
      this.observers.get(sectionId).disconnect();
    }

    const resizeObserver = new ResizeObserver((entries) => {
      this.debounceResize(sectionId, () => {
        this.calculateAndSetHeight(sectionId);
      });
    });

    // Observe the nested grid and all its children
    resizeObserver.observe(nestedGrid);

    // Observe existing child widgets
    nestedGrid.querySelectorAll(".grid-stack-item").forEach((item) => {
      resizeObserver.observe(item);
    });

    this.observers.set(sectionId, resizeObserver);
  }

  setupMutationObserver(sectionId, nestedGrid) {
    const mutationObserver = new MutationObserver((mutations) => {
      let shouldRecalculate = false;

      mutations.forEach((mutation) => {
        if (mutation.type === "childList") {
          // New widgets added or removed
          mutation.addedNodes.forEach((node) => {
            if (
              node.nodeType === Node.ELEMENT_NODE &&
              node.classList.contains("grid-stack-item")
            ) {
              // Start observing the new widget
              if (this.observers.has(sectionId)) {
                this.observers.get(sectionId).observe(node);
              }
              shouldRecalculate = true;
            }
          });

          mutation.removedNodes.forEach((node) => {
            if (
              node.nodeType === Node.ELEMENT_NODE &&
              node.classList.contains("grid-stack-item")
            ) {
              shouldRecalculate = true;
            }
          });
        }
      });

      if (shouldRecalculate) {
        this.debounceResize(sectionId, () => {
          this.calculateAndSetHeight(sectionId);
        });
      }
    });

    mutationObserver.observe(nestedGrid, {
      childList: true,
      subtree: true,
    });
  }

  calculateAndSetHeight(sectionId) {
    const section = this.sectionContainers.get(sectionId);
    if (!section) return;

    // Skip if auto-resize is paused for this section
    if (this.pausedSections.has(sectionId)) {
      console.log(`Section ${sectionId} auto-resize is paused, skipping`);
      return;
    }

    // Skip if any drag operation is happening
    if (this.isDragOperationActive()) {
      console.log(
        `Drag operation in progress, skipping auto-resize for section ${sectionId}`
      );
      return;
    }

    const { widget, nestedGrid, gridstackItem } = section;

    // Check if the widget is currently being resized manually
    if (
      gridstackItem &&
      gridstackItem.classList.contains("ui-resizable-resizing")
    ) {
      console.log(
        `Section ${sectionId} is being manually resized, skipping auto-resize`
      );
      return;
    }

    // Check if the widget is currently being dragged
    if (
      gridstackItem &&
      (gridstackItem.classList.contains("ui-draggable-dragging") ||
        gridstackItem.classList.contains("grid-stack-item-dragging"))
    ) {
      console.log(
        `Section ${sectionId} is being dragged, skipping auto-resize`
      );
      return;
    }

    // Check if the parent grid is in a valid state
    const parentGrid = gridstackItem?.closest(".grid-stack");
    if (!parentGrid || !parentGrid.gridstack) {
      console.warn(
        `Section ${sectionId}: Parent grid not found or not initialized`
      );
      return;
    }

    const gridInstance = parentGrid.gridstack;
    if (!gridInstance.engine || !gridInstance.engine.nodes) {
      console.warn(`Section ${sectionId}: Grid engine not ready`);
      return;
    }

    // Check if GridStack is currently processing operations
    if (gridInstance._updating || gridInstance._inColumnResize) {
      console.log(
        `Section ${sectionId}: GridStack is updating, skipping auto-resize`
      );
      return;
    }

    // Calculate the height needed for all child widgets
    const childWidgets = nestedGrid.querySelectorAll(".grid-stack-item");
    let maxBottom = 0;
    let hasContent = childWidgets.length > 0;

    if (hasContent) {
      childWidgets.forEach((child) => {
        const rect = child.getBoundingClientRect();
        const nestedRect = nestedGrid.getBoundingClientRect();
        const relativeBottom = rect.bottom - nestedRect.top;
        maxBottom = Math.max(maxBottom, relativeBottom);
      });
    }

    // Calculate required height
    let requiredHeight = hasContent
      ? Math.max(maxBottom + this.padding, this.minSectionHeight)
      : this.minSectionHeight;

    // Add header height
    const header = widget.querySelector(".widget-header");
    if (header) {
      requiredHeight += header.offsetHeight;
    }

    // Convert to grid units (assuming 60px cell height + 5px margin)
    const cellHeight = 60;
    const margin = 5;
    const gridUnits = Math.ceil(requiredHeight / (cellHeight + margin));
    const minGridUnits = 2;
    const finalGridUnits = Math.max(gridUnits, minGridUnits);

    console.log(
      `Section ${sectionId}: Required height: ${requiredHeight}px, Grid units: ${finalGridUnits}`
    );

    // Update the GridStack item height with proper error handling
    if (gridstackItem && gridstackItem.gridstackNode) {
      const gridstackNode = gridstackItem.gridstackNode;

      // Ensure gridstackNode has required properties
      if (
        !gridstackNode.hasOwnProperty("h") ||
        !gridstackNode.hasOwnProperty("w")
      ) {
        console.warn(
          `Section ${sectionId}: gridstackNode missing required properties`,
          gridstackNode
        );
        return;
      }

      const currentHeight = gridstackNode.h;

      if (currentHeight !== finalGridUnits) {
        try {
          // Update the widget height
          gridInstance.update(gridstackItem, {
            h: finalGridUnits,
          });

          console.log(
            `Updated section ${sectionId} height from ${currentHeight} to ${finalGridUnits} units`
          );
        } catch (error) {
          console.error(`Error updating section ${sectionId} height:`, error);
          // Don't rethrow - just log and continue
        }
      }
    } else {
      console.warn(
        `Section ${sectionId}: gridstackItem or gridstackNode not found`
      );
    }

    // Also set explicit height on the section widget for immediate visual feedback
    widget.style.height = `${requiredHeight}px`;
  }

  debounceResize(sectionId, callback) {
    if (this.debounceTimers.has(sectionId)) {
      clearTimeout(this.debounceTimers.get(sectionId));
    }

    const timer = setTimeout(() => {
      // Additional check before executing callback
      const section = this.sectionContainers.get(sectionId);
      if (section && section.gridstackItem) {
        // Skip if widget is being resized, dragged, or auto-resize is paused
        if (
          section.gridstackItem.classList.contains("ui-resizable-resizing") ||
          section.gridstackItem.classList.contains("ui-draggable-dragging") ||
          section.gridstackItem.classList.contains(
            "grid-stack-item-dragging"
          ) ||
          this.pausedSections.has(sectionId) ||
          this.isDragOperationActive()
        ) {
          console.log(
            `Section ${sectionId} is being manipulated or auto-resize is paused, skipping auto-resize`
          );
          return;
        }
      }

      callback();
      this.debounceTimers.delete(sectionId);
    }, 250); // Increased from 100ms to 250ms for better stability

    this.debounceTimers.set(sectionId, timer);
  }

  getSectionId(sectionWidget) {
    // Try to find section ID from the content div
    const sectionContent = sectionWidget.querySelector(".section-content");
    if (sectionContent && sectionContent.id) {
      return sectionContent.id;
    }

    // Fallback: generate ID based on widget position
    const gridItem = sectionWidget.closest(".grid-stack-item");
    if (gridItem && gridItem.gridstackNode) {
      return `section-${gridItem.gridstackNode.x}-${gridItem.gridstackNode.y}`;
    }

    return null;
  }

  // Public method to manually retry monitoring for a section
  retrySection(sectionId) {
    const sections = document.querySelectorAll(".section-container-widget");
    for (const section of sections) {
      if (this.getSectionId(section) === sectionId) {
        this.retryCounters.delete(sectionId);
        this.setupSectionMonitoring(section);
        return;
      }
    }
    console.warn(`Section ${sectionId} not found for retry`);
  }

  // Public method to manually trigger resize for a section
  resizeSection(sectionId) {
    this.calculateAndSetHeight(sectionId);
  }

  // Public method to add a new section to monitoring
  addSection(sectionWidget) {
    this.setupSectionMonitoring(sectionWidget);
  }

  // Public method to remove a section from monitoring
  removeSection(sectionId) {
    if (this.observers.has(sectionId)) {
      this.observers.get(sectionId).disconnect();
      this.observers.delete(sectionId);
    }

    if (this.debounceTimers.has(sectionId)) {
      clearTimeout(this.debounceTimers.get(sectionId));
      this.debounceTimers.delete(sectionId);
    }

    // Clean up retry counter
    this.retryCounters.delete(sectionId);

    this.sectionContainers.delete(sectionId);
    console.log(`Removed monitoring for section: ${sectionId}`);
  }

  // Public method to get current monitored sections
  getMonitoredSections() {
    return Array.from(this.sectionContainers.keys());
  }
}

// Initialize the auto-resize system when DOM is ready
let sectionAutoResize;

document.addEventListener("DOMContentLoaded", function () {
  sectionAutoResize = new SectionAutoResize();

  // Make it globally available
  window.sectionAutoResize = sectionAutoResize;
});

// Export for use in other scripts
window.SectionAutoResize = SectionAutoResize;
