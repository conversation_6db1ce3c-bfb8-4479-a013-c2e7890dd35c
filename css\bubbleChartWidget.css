/* Bubble Chart Widget Styles */
.bubble-chart-widget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent content spillover */
  background-color: #fff; /* Optional: background for the widget */
  border-radius: 0px; /* Match other widgets */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* Optional: subtle shadow */
}

.bubble-chart-widget .widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem; /* Slightly reduced padding */
  border-bottom: 1px solid #e9ecef; /* Separator line */
  flex-shrink: 0; /* Prevent header from shrinking */
  min-height: 40px; /* Ensure consistent header height */
}

.bubble-chart-widget .widget-title {
  font-size: 0.9rem; /* Slightly smaller title */
  font-weight: 600;
  color: #343a40;
  display: flex;
  align-items: center;
  gap: 0.5rem; /* Space between icon and text */
  cursor: text; /* Indicate editable */
  flex-grow: 1; /* Allow title to take available space */
  margin-right: 0.5rem; /* Space before actions */
}

.bubble-chart-widget .widget-actions {
  display: flex;
  align-items: center;
  gap: 0rem; /* Reduced gap between action buttons */
  flex-shrink: 0; /* Prevent actions container from shrinking */
}

.bubble-chart-widget .widget-actions .btn-link {
  color: #6c757d; /* Muted icon color */
  padding: 0.25rem; /* Smaller padding for icon buttons */
  font-size: 1.1rem; /* Adjust icon size */
}

.bubble-chart-widget .widget-actions .btn-link:hover {
  color: #20b2aa; /* Highlight color on hover */
}

.bubble-chart-widget .widget-actions .widget-chat-icon {
  /* Inherit chat icon styles or redefine if needed */
  position: static; /* Override absolute positioning if inherited */
  margin-left: 0.25rem; /* Space before chat icon */
  transform: scale(0.85); /* Slightly smaller chat icon */
}

.bubble-chart-widget .chart-container {
  flex-grow: 1; /* Allow chart to take remaining space */
  width: 100%;
  height: 100%; /* Ensure it tries to fill the height */
  min-height: 150px; /* Minimum height for the chart */
  overflow: hidden; /* Hide any potential overflow */
  padding: 5px; /* Small padding around the chart */
  position: relative; /* Needed for absolute positioned elements inside if any */
}

/* Dark Theme Adjustments */
.dark-theme .bubble-chart-widget {
  background-color: #1a1a1a;
  border-color: #333;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark-theme .bubble-chart-widget .widget-header {
  border-color: #333;
}

.dark-theme .bubble-chart-widget .widget-title {
  color: #fff;
}

.dark-theme .bubble-chart-widget .widget-actions .btn-link {
  color: #999;
}

.dark-theme .bubble-chart-widget .widget-actions .btn-link:hover {
  color: #4db6ac; /* Teal accent for dark mode */
}

/* Ensure amCharts tooltips are visible */
.am5-tooltip {
  z-index: 1090 !important; /* Higher than gridstack items */
}

/* Settings table adjustments */
#bubbleChartDataTable th,
#bubbleChartDataTable td {
  padding: 0.4rem; /* Adjust padding */
  font-size: 0.8rem; /* Smaller font size */
}

#bubbleChartDataTable input {
  padding: 0.25rem 0.5rem; /* Adjust input padding */
  font-size: 0.8rem; /* Match font size */
}

/* Data Table Styles */
#bubbleChartDataTable {
  font-size: 12px;
  border-collapse: collapse;
}

#bubbleChartDataTable input,
#bubbleChartDataTable select {
  font-size: 12px;
  padding: 0.25rem 0.5rem;
  height: 28px;
}

#bubbleChartDataTable th {
  position: sticky;
  top: 0;
  background: #f8f9fa;
  z-index: 1;
  font-size: 12px;
  font-weight: 500;
}

#bubbleChartDataTable td {
  vertical-align: middle;
  font-size: 12px;
}

/* Settings Panel Styles */
#bubbleChartSettings .form-label {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

#bubbleChartSettings .form-control,
#bubbleChartSettings .form-select,
#bubbleChartSettings .form-range {
  font-size: 12px;
}

#bubbleChartSettings .data-editor-section {
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  background-color: #f8f9fa;
  margin-bottom: 15px;
}

#bubbleChartSettings .section-title {
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 14px;
  color: #495057;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #bubbleChartSettings .row.g-2 .col-4,
  #bubbleChartSettings .row.g-2 .col-8 {
    flex: 0 0 100%;
    max-width: 100%;
    margin-bottom: 5px;
  }
}
