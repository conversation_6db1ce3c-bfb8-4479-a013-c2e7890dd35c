/* Special styling for nested widgets inside section containers */
.nested-grid-container .grid-stack-item .widget-header {
  display: flex;
  justify-content: flex-end;
  background: transparent;
  padding: 0.25rem;
  margin: 0;
  border: none;
  position: absolute;
  top: 0;
  right: 0;
  width: auto;
  z-index: 10;
}

.nested-grid-container .grid-stack-item .widget-header::after {
  display: none;
}

.nested-grid-container .grid-stack-item .widget-header > div:first-child {
  display: none;
}

.nested-grid-container .grid-stack-item .widget-header > div:last-child,
.nested-grid-container .grid-stack-item .widget-icons {
  display: flex;
  margin: 0;
  padding: 0;
}

.nested-grid-container .grid-stack-item .widget-icon {
  background-color: rgba(255, 255, 255, 0.8);
  width: 24px;
  height: 24px;
  margin-left: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.nested-grid-container .grid-stack-item .widget-icon:hover {
  background-color: #00b19c;
  color: white;
}

.nested-grid-container .grid-stack-item .widget-icon i {
  font-size: 14px;
}

/* Add a slight shadow to make icons more visible */
.nested-grid-container .grid-stack-item .widget-icons {
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.2));
}

/* Visibility transitions */
.nested-grid-container .grid-stack-item .widget-header {
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

.nested-grid-container .grid-stack-item:hover .widget-header {
  opacity: 1;
}

/* Improve drop target visibility */
.nested-grid-container .grid-stack {
  min-height: 100px;
  border: 1px dashed rgba(0, 177, 156, 0.1);
  transition: all 0.2s ease;
}

.nested-grid-container .grid-stack.ui-droppable-active {
  background-color: rgba(0, 177, 156, 0.05);
  border: 1px dashed rgba(0, 177, 156, 0.3);
}

.nested-grid-container .grid-stack.ui-droppable-hover {
  background-color: rgba(0, 177, 156, 0.1);
  border: 2px dashed rgba(0, 177, 156, 0.5);
}

/* Fix z-index for dragged items */
.ui-draggable-dragging {
  z-index: 999 !important;
}

/* Fix nested grid placeholder visibility */
.nested-grid-container .grid-stack-placeholder > .placeholder-content {
  background: rgba(0, 177, 156, 0.15) !important;
  border: 2px dashed rgba(0, 177, 156, 0.4) !important;
} 