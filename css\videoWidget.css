/* Video Widget Styles */
.video-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
}

.video-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.video-title i {
  font-size: var(--font-size-md);
  color: var(--primary-color);
}

.video-title span {
  font-size: var(--font-size-lg);
}

.video-actions {
  display: flex;
  gap: 0.5rem;
}

.video-actions button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  font-size: var(--font-size-base);
  transition: color 0.2s ease;
}

.video-actions button:hover {
  color: var(--primary-color);
}

.video-body {
  flex: 1;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.video-container {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-container iframe,
.video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.video-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.video-description {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  line-height: 1.5;
}

.video-metadata {
  display: flex;
  gap: 1rem;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.video-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background: var(--background-secondary);
  border-radius: 4px;
}

.video-controls button {
  background: none;
  border: none;
  color: var(--text-primary);
  cursor: pointer;
  font-size: var(--font-size-md);
  padding: 0.25rem;
  transition: color 0.2s ease;
}

.video-controls button:hover {
  color: var(--primary-color);
}

.video-progress {
  flex: 1;
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  cursor: pointer;
}

.video-progress-bar {
  height: 100%;
  background: var(--primary-color);
  border-radius: 2px;
  width: 0;
}

.video-time {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  min-width: 60px;
  text-align: center;
}

/* Dark Theme Support */
body.dark-theme .video-widget {
  background: var(--secondary-color);
}

body.dark-theme .video-header {
  background: rgba(0, 0, 0, 0.1);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme .video-title {
  color: white;
}

body.dark-theme .video-actions button {
  color: rgba(255, 255, 255, 0.7);
}

body.dark-theme .video-actions button:hover {
  color: white;
}

body.dark-theme .video-description {
  color: rgba(255, 255, 255, 0.9);
}

body.dark-theme .video-metadata {
  color: rgba(255, 255, 255, 0.6);
}

body.dark-theme .video-controls {
  background: rgba(0, 0, 0, 0.2);
}

body.dark-theme .video-controls button {
  color: rgba(255, 255, 255, 0.9);
}

body.dark-theme .video-time {
  color: rgba(255, 255, 255, 0.7);
}

/* Responsive Design */
@media (max-width: 768px) {
  .video-header {
    padding: 0.75rem;
  }

  .video-title span {
    font-size: var(--font-size-base);
  }

  .video-title i {
    font-size: var(--font-size-md);
  }

  .video-actions button {
    font-size: var(--font-size-sm);
  }

  .video-description {
    font-size: var(--font-size-sm);
  }

  .video-metadata {
    font-size: var(--font-size-sm);
  }

  .video-controls button {
    font-size: var(--font-size-base);
  }

  .video-time {
    font-size: var(--font-size-sm);
    min-width: 50px;
  }
}
