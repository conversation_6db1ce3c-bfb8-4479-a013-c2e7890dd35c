.grid-stack {
  position: relative;
}
.grid-stack-rtl {
  direction: ltr;
}
.grid-stack-rtl > .grid-stack-item {
  direction: rtl;
}
.grid-stack-placeholder > .placeholder-content {
  background-color: rgba(0, 0, 0, 0.1);
  margin: 0;
  position: absolute;
  width: auto;
  z-index: 0 !important;
}
.grid-stack > .grid-stack-item {
  position: absolute;
  padding: 0;
}
.grid-stack > .grid-stack-item > .grid-stack-item-content {
  margin: 0;
  position: absolute;
  width: auto;
  overflow-x: hidden;
  overflow-y: auto;
}
.grid-stack
  > .grid-stack-item.size-to-content:not(.size-to-content-max)
  > .grid-stack-item-content {
  overflow-y: hidden;
}
.grid-stack-item > .ui-resizable-handle {
  position: absolute;
  font-size: 0.1px;
  display: block;
  -ms-touch-action: none;
  touch-action: none;
}
.grid-stack-item.ui-resizable-autohide > .ui-resizable-handle,
.grid-stack-item.ui-resizable-disabled > .ui-resizable-handle {
  display: none;
}
.grid-stack-item > .ui-resizable-ne,
.grid-stack-item > .ui-resizable-nw,
.grid-stack-item > .ui-resizable-se,
.grid-stack-item > .ui-resizable-sw {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" stroke="%23666" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 20 20"><path d="m10 3 2 2H8l2-2v14l-2-2h4l-2 2"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
}
.grid-stack-item > .ui-resizable-ne {
  transform: rotate(45deg);
}
.grid-stack-item > .ui-resizable-sw {
  transform: rotate(45deg);
}
.grid-stack-item > .ui-resizable-nw {
  transform: rotate(-45deg);
}
.grid-stack-item > .ui-resizable-se {
  transform: rotate(-45deg);
}
.grid-stack-item > .ui-resizable-nw {
  cursor: nw-resize;
  width: 20px;
  height: 20px;
  top: 0;
}
.grid-stack-item > .ui-resizable-n {
  cursor: n-resize;
  height: 10px;
  top: 0;
  left: 25px;
  right: 25px;
}
.grid-stack-item > .ui-resizable-ne {
  cursor: ne-resize;
  width: 20px;
  height: 20px;
  top: 0;
}
.grid-stack-item > .ui-resizable-e {
  cursor: e-resize;
  width: 10px;
  top: 15px;
  bottom: 15px;
}
.grid-stack-item > .ui-resizable-se {
  cursor: se-resize;
  width: 20px;
  height: 20px;
}
.grid-stack-item > .ui-resizable-s {
  cursor: s-resize;
  height: 10px;
  left: 25px;
  bottom: 0;
  right: 25px;
}
.grid-stack-item > .ui-resizable-sw {
  cursor: sw-resize;
  width: 20px;
  height: 20px;
}
.grid-stack-item > .ui-resizable-w {
  cursor: w-resize;
  width: 10px;
  top: 15px;
  bottom: 15px;
}
.grid-stack-item.ui-draggable-dragging > .ui-resizable-handle {
  display: none !important;
}
.grid-stack-item.ui-draggable-dragging {
  will-change: left, top;
  cursor: move;
}
.grid-stack-item.ui-resizable-resizing {
  will-change: width, height;
}
.ui-draggable-dragging,
.ui-resizable-resizing {
  z-index: 10000;
}
.ui-draggable-dragging > .grid-stack-item-content,
.ui-resizable-resizing > .grid-stack-item-content {
  box-shadow: 1px 4px 6px rgba(0, 0, 0, 0.2);
  opacity: 0.8;
}
.grid-stack-animate,
.grid-stack-animate .grid-stack-item {
  transition: left 0.3s, top 0.3s, height 0.3s, width 0.3s;
}
.grid-stack-animate .grid-stack-item.grid-stack-placeholder,
.grid-stack-animate .grid-stack-item.ui-draggable-dragging,
.grid-stack-animate .grid-stack-item.ui-resizable-resizing {
  transition: left 0s, top 0s, height 0s, width 0s;
}
.grid-stack > .grid-stack-item[gs-y="0"] {
  top: 0;
}
.grid-stack > .grid-stack-item[gs-x="0"] {
  left: 0;
}
.gs-12 > .grid-stack-item {
  width: 8.333%;
}
.gs-12 > .grid-stack-item[gs-x="1"] {
  left: 8.333%;
}
.gs-12 > .grid-stack-item[gs-w="2"] {
  width: 16.667%;
}
.gs-12 > .grid-stack-item[gs-x="2"] {
  left: 16.667%;
}
.gs-12 > .grid-stack-item[gs-w="3"] {
  width: 25%;
}
.gs-12 > .grid-stack-item[gs-x="3"] {
  left: 25%;
}
.gs-12 > .grid-stack-item[gs-w="4"] {
  width: 33.333%;
}
.gs-12 > .grid-stack-item[gs-x="4"] {
  left: 33.333%;
}
.gs-12 > .grid-stack-item[gs-w="5"] {
  width: 41.667%;
}
.gs-12 > .grid-stack-item[gs-x="5"] {
  left: 41.667%;
}
.gs-12 > .grid-stack-item[gs-w="6"] {
  width: 50%;
}
.gs-12 > .grid-stack-item[gs-x="6"] {
  left: 50%;
}
.gs-12 > .grid-stack-item[gs-w="7"] {
  width: 58.333%;
}
.gs-12 > .grid-stack-item[gs-x="7"] {
  left: 58.333%;
}
.gs-12 > .grid-stack-item[gs-w="8"] {
  width: 66.667%;
}
.gs-12 > .grid-stack-item[gs-x="8"] {
  left: 66.667%;
}
.gs-12 > .grid-stack-item[gs-w="9"] {
  width: 75%;
}
.gs-12 > .grid-stack-item[gs-x="9"] {
  left: 75%;
}
.gs-12 > .grid-stack-item[gs-w="10"] {
  width: 83.333%;
}
.gs-12 > .grid-stack-item[gs-x="10"] {
  left: 83.333%;
}
.gs-12 > .grid-stack-item[gs-w="11"] {
  width: 91.667%;
}
.gs-12 > .grid-stack-item[gs-x="11"] {
  left: 91.667%;
}
.gs-12 > .grid-stack-item[gs-w="12"] {
  width: 100%;
}
.gs-1 > .grid-stack-item {
  width: 100%;
}

/* Optional styles for demos */
.btn-primary {
  color: #fff;
  background-color: #20b2aa;
}

.btn {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  border-radius: 0.25rem;
}

a {
  text-decoration: none;
}

h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.sidebar {
  background: rgb(215, 243, 215);
  padding: 25px 0;
  height: 100px;
  text-align: center;
}
.sidebar > .grid-stack-item,
.sidebar-item {
  width: 100px;
  height: 50px;
  border: 2px dashed green;
  text-align: center;
  line-height: 35px;
  background: rgb(192, 231, 192);
  cursor: default;
  display: inline-block;
}

.grid-stack {
  background: #fafad2;
}
.grid-stack.grid-stack-static {
  background: #eee;
}

.sidebar > .grid-stack-item,
.grid-stack-item-content {
  text-align: center;
  background-color: #18bc9c;
}

.card-header {
  margin: 0;
  cursor: move;
  min-height: 25px;
  background-color: #16af91;
}
.card-header:hover {
  background-color: #149b80;
}

.ui-draggable-disabled.ui-resizable-disabled > .grid-stack-item-content {
  background-color: #777;
}

.grid-stack-item-removing {
  opacity: 0.5;
}
.trash {
  height: 100px;
  background: rgba(255, 0, 0, 0.1) center center
    url(data:image/svg+xml;utf8;base64,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)
    no-repeat;
}

/* Add these styles for the settings icon */
.widget-header .btn-link {
  padding: 0;
  color: #333;
  text-decoration: none;
}

.widget-header .btn-link:hover {
  color: #02104f;
}

.widget-header la-cog {
  font-size: 1.1rem;
}

/* Style for the pie chart widget header */
.pie-chart-widget .widget-header {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 8px 12px;
  border-radius: 4px;
  margin: -8px -8px 8px -8px;
}

/* make nested grid have slightly darker bg take almost all space (need some to tell them apart) so items inside can have similar to external size+margin */
.grid-stack > .grid-stack-item.grid-stack-sub-grid > .grid-stack-item-content {
  background: rgba(0, 0, 0, 0.1);
  inset: 0 2px;
}
.grid-stack.grid-stack-nested {
  background: none;
  inset: 0;
}

.grid-stack.show-dimensions .grid-stack-item:after {
  content: "1x1";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 2px;
  color: black;
  background-color: white;
  pointer-events: none; /* to not interfere with dragging the item */
}

.grid-stack.show-dimensions .grid-stack-item[gs-h]::after {
  content: "1x" attr(gs-h);
}

.grid-stack.show-dimensions .grid-stack-item[gs-w]::after {
  content: attr(gs-w) "x1";
}

.grid-stack.show-dimensions .grid-stack-item[gs-h][gs-w]::after {
  content: attr(gs-w) "x" attr(gs-h);
}
.widget-header.mb-2 {
  background: #fff !important;
  border-radius: 0px !important;
  padding: 8px 12px;
}
