// Returns the HTML markup for a line separator widget, given separatorId and settingsId
function getLineSeparatorWidgetMarkup({ separatorId, settingsId }) {
  return `
    <div class="line-separator-widget p-2">
      <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
        <div>
           <i class="las la-minus"></i> Line Separator
        </div>
        <div>
          <button class="btn btn-sm btn-link text-dark"
                  data-bs-toggle="offcanvas"
                  data-bs-target="#${settingsId}"
                  aria-controls="${settingsId}">
            <i class="las la-cog"></i>
          </button>
          <button class="btn btn-sm btn-link text-dark ms-1"
                  onclick="removeWidget(this)">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      <div id="${separatorId}" class="line-separator-container">
        <div class="line-separator horizontal solid" style="
          border-top: 2px solid #333;
          width: 100%;
          height: 0;
          margin: 20px 0;
        "></div>
      </div>
    </div>
  `;
}

// Add a line separator widget
function addLineSeparatorWidget() {
  console.log("Adding line separator widget");
  const separatorId = "line-separator-" + Date.now();
  const settingsId = "settings-" + separatorId;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 2,
    h: 1,
    content: getLineSeparatorWidgetMarkup({ separatorId, settingsId }),
  });

  // Create settings panel in the offcanvas container
  const offcanvasContainer = document.getElementById("offcanvasContainer");
  const settingsPanel = document.createElement("div");
  settingsPanel.className = "offcanvas offcanvas-end";
  settingsPanel.id = settingsId;
  settingsPanel.setAttribute("tabindex", "-1");
  settingsPanel.innerHTML = `
    <div class="offcanvas-header">
      <h5 class="offcanvas-title">Line Separator Settings</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- Orientation -->
      <div class="mb-3">
        <label class="form-label">Orientation</label>
        <div class="form-check">
          <input class="form-check-input" type="radio" name="${settingsId}-orientation" id="${settingsId}-horizontal" value="horizontal" checked>
          <label class="form-check-label" for="${settingsId}-horizontal">
            <i class="las la-minus"></i> Horizontal
          </label>
        </div>
        <div class="form-check">
          <input class="form-check-input" type="radio" name="${settingsId}-orientation" id="${settingsId}-vertical" value="vertical">
          <label class="form-check-label" for="${settingsId}-vertical">
            <i class="las la-grip-lines-vertical"></i> Vertical
          </label>
        </div>
      </div>

      <!-- Line Style -->
      <div class="mb-3">
        <label class="form-label">Line Style</label>
        <select class="form-select" id="${settingsId}-style">
          <option value="solid">Solid</option>
          <option value="dashed">Dashed</option>
          <option value="dotted">Dotted</option>
          <option value="double">Double</option>
        </select>
      </div>

      <!-- Line Width -->
      <div class="mb-3">
        <label class="form-label">Line Width (px)</label>
        <input type="range" class="form-range" min="1" max="10" value="2" id="${settingsId}-width">
        <div class="d-flex justify-content-between">
          <small>1px</small>
          <small id="${settingsId}-width-display">2px</small>
          <small>10px</small>
        </div>
      </div>

      <!-- Line Color -->
      <div class="mb-3">
        <label class="form-label">Line Color</label>
        <input type="color" class="form-control form-control-color" id="${settingsId}-color" value="#333333">
      </div>

      <!-- Line Length -->
      <div class="mb-3">
        <label class="form-label">Line Length (%)</label>
        <input type="range" class="form-range" min="10" max="100" value="100" id="${settingsId}-length">
        <div class="d-flex justify-content-between">
          <small>10%</small>
          <small id="${settingsId}-length-display">100%</small>
          <small>100%</small>
        </div>
      </div>

      <!-- Margin -->
      <div class="mb-3">
        <label class="form-label">Margin (px)</label>
        <input type="range" class="form-range" min="0" max="50" value="20" id="${settingsId}-margin">
        <div class="d-flex justify-content-between">
          <small>0px</small>
          <small id="${settingsId}-margin-display">20px</small>
          <small>50px</small>
        </div>
      </div>

      <!-- Alignment -->
      <div class="mb-3">
        <label class="form-label">Alignment</label>
        <select class="form-select" id="${settingsId}-alignment">
          <option value="center">Center</option>
          <option value="start">Start</option>
          <option value="end">End</option>
        </select>
      </div>

      <!-- Apply Button -->
      <button class="btn btn-primary w-100" onclick="applyLineSeparatorSettings('${separatorId}', '${settingsId}')">
        Apply Changes
      </button>
    </div>
  `;
  offcanvasContainer.appendChild(settingsPanel);

  // Initialize the line separator widget
  window.setTimeout(function () {
    try {
      console.log(
        "Widget added to grid, now initializing line separator widget"
      );
      initLineSeparatorWidget(separatorId, settingsId);
    } catch (error) {
      console.error("Error initializing line separator widget:", error);
    }
  }, 100);

  return widget;
}

// Function to initialize the line separator widget
function initLineSeparatorWidget(separatorId, settingsId) {
  console.log("Initializing line separator widget:", separatorId);
  const container = document.getElementById(separatorId);

  if (!container) {
    console.error("Line separator container not found:", separatorId);
    return;
  }

  // Set up real-time range slider updates
  const width = document.getElementById(`${settingsId}-width`);
  const widthDisplay = document.getElementById(`${settingsId}-width-display`);
  const length = document.getElementById(`${settingsId}-length`);
  const lengthDisplay = document.getElementById(`${settingsId}-length-display`);
  const margin = document.getElementById(`${settingsId}-margin`);
  const marginDisplay = document.getElementById(`${settingsId}-margin-display`);

  if (width && widthDisplay) {
    width.addEventListener("input", function () {
      widthDisplay.textContent = this.value + "px";
    });
  }

  if (length && lengthDisplay) {
    length.addEventListener("input", function () {
      lengthDisplay.textContent = this.value + "%";
    });
  }

  if (margin && marginDisplay) {
    margin.addEventListener("input", function () {
      marginDisplay.textContent = this.value + "px";
    });
  }

  // Set up orientation change handlers
  const orientationRadios = document.querySelectorAll(
    `input[name="${settingsId}-orientation"]`
  );
  orientationRadios.forEach((radio) => {
    radio.addEventListener("change", function () {
      if (this.checked) {
        updateLineSeparatorOrientation(separatorId, this.value);
      }
    });
  });

  console.log("Line separator widget initialized:", separatorId);
}

// Function to update line separator orientation
function updateLineSeparatorOrientation(separatorId, orientation) {
  const container = document.getElementById(separatorId);
  const line = container.querySelector(".line-separator");

  if (!line) return;

  // Remove existing orientation classes
  line.classList.remove("horizontal", "vertical");

  // Add new orientation class
  line.classList.add(orientation);

  // Update the parent widget size if needed
  const widget = container.closest(".grid-stack-item");
  if (widget && widget.gridstackNode) {
    const gridstackNode = widget.gridstackNode;
    const grid = widget.closest(".grid-stack").gridstack;

    if (orientation === "horizontal") {
      // Make widget wider but shorter
      if (gridstackNode.h > 1) {
        grid.update(widget, { h: 1 });
      }
    } else {
      // Make widget taller but narrower
      if (gridstackNode.w > 1) {
        grid.update(widget, { w: 1, h: 4 });
      }
    }
  }
}

// Function to apply line separator settings
function applyLineSeparatorSettings(separatorId, settingsId) {
  const container = document.getElementById(separatorId);
  const line = container.querySelector(".line-separator");
  if (!container || !line) return;

  // Get all settings values
  const orientation = document.querySelector(
    `input[name="${settingsId}-orientation"]:checked`
  ).value;
  const style = document.getElementById(`${settingsId}-style`).value;
  const width = document.getElementById(`${settingsId}-width`).value;
  const color = document.getElementById(`${settingsId}-color`).value;
  const length = document.getElementById(`${settingsId}-length`).value;
  const margin = document.getElementById(`${settingsId}-margin`).value;
  const alignment = document.getElementById(`${settingsId}-alignment`).value;

  // Remove existing classes
  line.classList.remove(
    "horizontal",
    "vertical",
    "solid",
    "dashed",
    "dotted",
    "double"
  );

  // Add new classes
  line.classList.add(orientation, style);

  // Apply styles based on orientation
  if (orientation === "horizontal") {
    line.style.borderTop = `${width}px ${style} ${color}`;
    line.style.borderLeft = "none";
    line.style.borderRight = "none";
    line.style.borderBottom = "none";
    line.style.width = `${length}%`;
    line.style.height = "0";
    line.style.margin = `${margin}px auto`;

    // Apply alignment
    if (alignment === "start") {
      line.style.marginLeft = "0";
      line.style.marginRight = "auto";
    } else if (alignment === "end") {
      line.style.marginLeft = "auto";
      line.style.marginRight = "0";
    } else {
      line.style.marginLeft = "auto";
      line.style.marginRight = "auto";
    }
  } else {
    line.style.borderLeft = `${width}px ${style} ${color}`;
    line.style.borderTop = "none";
    line.style.borderRight = "none";
    line.style.borderBottom = "none";
    line.style.width = "0";
    line.style.height = `${length}%`;
    line.style.margin = `auto ${margin}px`;

    // Apply alignment
    if (alignment === "start") {
      line.style.marginTop = "0";
      line.style.marginBottom = "auto";
    } else if (alignment === "end") {
      line.style.marginTop = "auto";
      line.style.marginBottom = "0";
    } else {
      line.style.marginTop = "auto";
      line.style.marginBottom = "auto";
    }
  }

  // Store the settings in the container dataset for persistence
  container.dataset.separatorSettings = JSON.stringify({
    orientation,
    style,
    width,
    color,
    length,
    margin,
    alignment,
  });

  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(
    document.getElementById(settingsId)
  );
  if (offcanvas) {
    offcanvas.hide();
  }
}

// Function to load line separator settings from stored data
function loadLineSeparatorSettings(separatorId, settingsId) {
  const container = document.getElementById(separatorId);
  if (!container || !container.dataset.separatorSettings) return;

  try {
    const settings = JSON.parse(container.dataset.separatorSettings);

    // Load settings into form elements
    const orientationRadio = document.getElementById(
      `${settingsId}-${settings.orientation}`
    );
    if (orientationRadio) orientationRadio.checked = true;

    document.getElementById(`${settingsId}-style`).value =
      settings.style || "solid";
    document.getElementById(`${settingsId}-width`).value = settings.width || 2;
    document.getElementById(`${settingsId}-color`).value =
      settings.color || "#333333";
    document.getElementById(`${settingsId}-length`).value =
      settings.length || 100;
    document.getElementById(`${settingsId}-margin`).value =
      settings.margin || 20;
    document.getElementById(`${settingsId}-alignment`).value =
      settings.alignment || "center";

    // Update display values
    document.getElementById(`${settingsId}-width-display`).textContent =
      (settings.width || 2) + "px";
    document.getElementById(`${settingsId}-length-display`).textContent =
      (settings.length || 100) + "%";
    document.getElementById(`${settingsId}-margin-display`).textContent =
      (settings.margin || 20) + "px";
  } catch (error) {
    console.error("Error loading line separator settings:", error);
  }
}

// Export the functions to global scope
window.addLineSeparatorWidget = addLineSeparatorWidget;
window.initLineSeparatorWidget = initLineSeparatorWidget;
window.updateLineSeparatorOrientation = updateLineSeparatorOrientation;
window.applyLineSeparatorSettings = applyLineSeparatorSettings;
window.loadLineSeparatorSettings = loadLineSeparatorSettings;
window.getLineSeparatorWidgetMarkup = getLineSeparatorWidgetMarkup;
