document.write(`
    <div class="widget-section">
    <div class="widget-categories">
      <div class="widget-categories-left" style="
    display: flex;
    align-items: center;
    justify-content: space-between;
">
        <div class="d-flex">
          <div class="widget-category active" data-category="insert">
          <i class="las la-plus"></i>
          Insert widget
        </div>
        <div class="widget-category" data-category="charts">
          <i class="las la-chart-line"></i>
          Charts
        </div>

        <div class="widget-category" data-category="advanced">
          <i class="las la-rocket"></i>
          Advanced
        </div>
        </div>
        <div class="mb-2 mt-2">
          <button class="btn btn-sm btn-primary" style="padding: 5px 10px">Save</button>
          <a href="./index-final.html" target="_blank" style="padding: 5px 10px" class="btn btn-sm btn-secondary">Preview</a>
        </div>
      </div>
      <!-- <button class="section-gallery-btn">
          <div class="btn-text">
            <i class="las la-th"></i>
            <span>Section gallery</span>
          </div>
          <i class="las la-chevron-right"></i>
        </button> -->
    </div>

    <div class="widget-gallery">
      <!-- Insert Widgets -->

      <div class="widget-item grid-stack-item widget-category-insert d-none" onclick="addHandsontableWidget()">
        <div class="widget-icon">
          <i class="las la-table"></i>
        </div>
        <div class="widget-label">Spreadsheet</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-insert" data-widget-type="text" onclick="addTextWidget()">
        <div class="widget-icon">
          <i class="las la-font"></i>
        </div>
        <div class="widget-label">Text widget</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-insert" data-widget-type="table" onclick="addTableWidget()">
        <div class="widget-icon">
          <i class="las la-table"></i>
        </div>
        <div class="widget-label">Table widget</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-insert d-none" data-widget-type="line-separator" onclick="addLineSeparatorWidget()">
        <div class="widget-icon">
          <i class="las la-minus"></i>
        </div>
        <div class="widget-label">Line Separator</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-insert d-none" data-widget-type="notes-section" onclick="addNotesSectionWidget()">
        <div class="widget-icon">
          <i class="las la-sticky-note"></i>
        </div>
        <div class="widget-label">Notes Section</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-insert d-none" data-widget-type="image" onclick="addImageWidget()">
        <div class="widget-icon">
          <i class="las la-image"></i>
        </div>
        <div class="widget-label">Image widget</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-insert d-none" onclick="addVideoWidget()">
        <div class="widget-icon">
          <i class="las la-video"></i>
        </div>
        <div class="widget-label">Video widget</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-insert" onclick="addSectionContainerWidget()" data-widget-type="section-container">
        <div class="widget-icon">
          <i class="las la-layer-group"></i>
        </div>
        <div class="widget-label">Section container</div>
      </div>

      <!-- Smart Widget Composer -->
      <div class="widget-item grid-stack-item widget-category-insert d-none" onclick="openSmartWidgetComposer()">
        <div class="widget-icon">
          <i class="las la-magic"></i>
        </div>
        <div class="widget-label">Smart Composer</div>
      </div>

      <!-- Chart Widgets -->

       <div class="widget-item grid-stack-item widget-category-charts" onclick="addPercentStackedColumnChartWidget()" data-widget-type="percent-stacked-column-chart">
        <div class="widget-icon">
          <i class="las la-percentage"></i>
        </div>
        <div class="widget-label">100% Stacked Column</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-charts" data-widget-type="bar-chart" onclick="addBarChartWidget()">
        <div class="widget-icon">
          <i class="las la-chart-bar"></i>
        </div>
        <div class="widget-label">Bar chart</div>
      </div>

      <!-- Horizontal Bar Chart -->
      <div class="widget-item grid-stack-item widget-category-charts" data-widget-type="horizontal-bar-chart" onclick="addHorizontalBarChartWidget()">
        <div class="widget-icon">
          <i class="las la-grip-lines"></i>
        </div>
        <div class="widget-label">Horizontal Bar</div>
      </div>

      <!-- Column Chart -->
      <div class="widget-item grid-stack-item widget-category-charts" data-widget-type="column-chart" onclick="addColumnChartWidget()">
        <div class="widget-icon">
          <i class="las la-chart-bar"></i>
        </div>
        <div class="widget-label">Column Chart</div>
      </div>

      <!-- Dual Axis Chart -->
      <div class="widget-item grid-stack-item widget-category-charts" data-widget-type="dual-axis-chart" onclick="addDualAxisChartWidget()">
        <div class="widget-icon">
          <i class="las la-chart-line"></i>
        </div>
        <div class="widget-label">Dual Axis Chart</div>
      </div>






      <div class="widget-item grid-stack-item widget-category-charts" data-widget-type="pie-chart" onclick="addPieChartWidget()">
        <div class="widget-icon">
          <i class="las la-chart-pie"></i>
        </div>
        <div class="widget-label">Pie chart</div>
      </div>

      <!-- Added Linked Pie Chart -->
      <div class="widget-item grid-stack-item widget-category-charts" data-widget-type="linked-pie-chart" onclick="addLinkedPieChartWidget()">
        <div class="widget-icon">
          <i class="las la-chart-pie"></i>
        </div>
        <div class="widget-label">Linked Pie</div>
      </div>

      <!-- Add Nested Donut Chart -->
      <div class="widget-item grid-stack-item widget-category-charts" onclick="addNestedDonutChartWidget()">
        <div class="widget-icon">
          <i class="las la-ring"></i>
        </div>
        <div class="widget-label">Nested Donut</div>
      </div>

      <!-- Add Pie of Pie Chart -->
      <div class="widget-item grid-stack-item widget-category-charts" onclick="addPieOfPieWidget()">
        <div class="widget-icon">
          <i class="las la-chart-pie"></i>
        </div>
        <div class="widget-label">Pie of Pie</div>
      </div>

      



      <div class="widget-item grid-stack-item widget-category-charts" data-widget-type="line-chart" onclick="addLineChartWidget()">
        <div class="widget-icon">
          <i class="las la-chart-line"></i>
        </div>
        <div class="widget-label">Line chart</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-charts" data-widget-type="stacked-column-chart" onclick="addStackedColumnChartWidget()">
        <div class="widget-icon">
          <i class="las la-layer-group"></i>
        </div>
        <div class="widget-label">Stacked Column</div>
      </div>

     

      <div class="widget-item grid-stack-item widget-category-charts" onclick="addAreaChartWidget()" data-widget-type="area-chart">
        <div class="widget-icon">
          <i class="las la-chart-area"></i>
        </div>
        <div class="widget-label">Area Chart</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-charts" onclick="addStockChartWidget()">
        <div class="widget-icon">
          <i class="las la-chart-line"></i>
        </div>
        <div class="widget-label">Stock Chart</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-charts" onclick="addWordCloudWidget()">
        <div class="widget-icon">
          <i class="las la-cloud"></i>
        </div>
        <div class="widget-label">Word cloud</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-charts" onclick="addBubbleChartWidget()">
        <div class="widget-icon">
          <i class="las la-circle"></i>
        </div>
        <div class="widget-label">Bubble chart</div>
      </div>

      <!-- Add Curved Line Map Widget -->
      <div class="widget-item grid-stack-item widget-category-charts" onclick="addCurvedLineMapWidget()">
        <div class="widget-icon">
          <i class="las la-route"></i>
        </div>
        <div class="widget-label">Curved Line Map</div>
      </div>

      <!-- Add Drill Down Map Widget -->
      <div class="widget-item grid-stack-item widget-category-charts" onclick="addDrillDownMapWidget()">
        <div class="widget-icon">
          <i class="bi bi-globe"></i>
        </div>
        <div class="widget-label">Drill Down Map</div>
      </div>

      <!-- Add World Map Widget -->
      <div class="widget-item grid-stack-item widget-category-charts" onclick="addWorldMapWidget()">
        <div class="widget-icon">
          <i class="las la-globe"></i>
        </div>
        <div class="widget-label">World Map</div>
      </div>

      <div class="widget-item grid-stack-item widget-category-charts" onclick="addDynamicPieMapWidget()">
        <div class="widget-icon">
          <i class="las la-globe"></i>
        </div>
        <div class="widget-label">Dynamic Pie Map</div>
      </div>

      <!-- Gauge with Bands Widget has been removed -->

      <div data-widget-type="pdf-viewer" class="widget-item grid-stack-item widget-category-advanced" onclick="addPdfViewerWidget()">
        <div class="widget-icon">
          <i class="las la-file-pdf"></i>
        </div>
        <div class="widget-label">PDF viewer</div>
      </div>

      <!-- KPI Widget -->
      <div class="widget-item grid-stack-item widget-category-advanced d-none" onclick="addKpiWidget()">
        <div class="widget-icon">
          <i class="las la-chart-line"></i>
        </div>
        <div class="widget-label">KPI Widget</div>
      </div>

      <!-- Price Chart Widget -->
      <div class="widget-item grid-stack-item widget-category-advanced d-none" onclick="addPriceChartWidget()">
        <div class="widget-icon">
          <i class="las la-chart-line"></i>
        </div>
        <div class="widget-label">Price Chart</div>
      </div>

      <!-- Tab Container Widget -->
      <div class="widget-item grid-stack-item widget-category-advanced d-none onclick="addTabContainerWidget()">
      
        <div class="widget-label">Tab Container</div>
      </div>

      <!-- TSC2.0 Widgets -->
    </div>
  </div>





    `);
