// Add an image widget
function addImageWidget() {
  console.log("Adding image widget");
  const imageId = "image-" + Date.now();
  const settingsId = "settings-" + imageId;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 4,
    h: 4,
    content: getImageWidgetMarkup({ imageId, settingsId }),
  });

  // Create settings panel in the offcanvas container
  const offcanvasContainer = document.getElementById("offcanvasContainer");
  const settingsPanel = document.createElement("div");
  settingsPanel.className = "offcanvas offcanvas-end";
  settingsPanel.id = settingsId;
  settingsPanel.setAttribute("tabindex", "-1");
  settingsPanel.innerHTML = `
    <div class="offcanvas-header">
      <h5 class="offcanvas-title">Image Settings</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- Image Upload -->
      <div class="mb-3">
        <label for="${settingsId}-upload" class="form-label">Upload Image</label>
        <input class="form-control" type="file" id="${settingsId}-upload" accept="image/*" onchange="previewImage('${imageId}', this)">
      </div>

      <!-- Image URL -->
      <div class="mb-3">
        <label for="${settingsId}-url" class="form-label">Or Enter Image URL</label>
        <input type="text" class="form-control" id="${settingsId}-url" placeholder="https://example.com/image.jpg">
      </div>

      <!-- Fit Mode -->
      <div class="mb-3">
        <label class="form-label">Fit Mode</label>
        <select class="form-select" id="${settingsId}-fit">
          <option value="contain">Contain (show all)</option>
          <option value="cover">Cover (fill area)</option>
          <option value="fill">Stretch to fill</option>
          <option value="none">Original size</option>
        </select>
      </div>

      <!-- Position -->
      <div class="mb-3">
        <label class="form-label">Position</label>
        <select class="form-select" id="${settingsId}-position">
          <option value="center">Center</option>
          <option value="top">Top</option>
          <option value="bottom">Bottom</option>
          <option value="left">Left</option>
          <option value="right">Right</option>
          <option value="top-left">Top Left</option>
          <option value="top-right">Top Right</option>
          <option value="bottom-left">Bottom Left</option>
          <option value="bottom-right">Bottom Right</option>
        </select>
      </div>

      <!-- Background Color -->
      <div class="mb-3">
        <label for="${settingsId}-bgcolor" class="form-label">Background Color</label>
        <input type="color" class="form-control form-control-color" id="${settingsId}-bgcolor" value="#ffffff">
      </div>

      <!-- Border -->
      <div class="mb-3">
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" id="${settingsId}-border">
          <label class="form-check-label" for="${settingsId}-border">Show Border</label>
        </div>
      </div>

      <!-- Border Radius -->
      <div class="mb-3">
        <label class="form-label">Border Radius (px)</label>
        <input type="range" class="form-range" min="0" max="50" value="0" id="${settingsId}-radius">
      </div>

      <!-- Apply Button -->
      <button class="btn btn-primary w-100" onclick="applyImageSettings('${imageId}', '${settingsId}')">
        Apply Changes
      </button>
    </div>
  `;
  offcanvasContainer.appendChild(settingsPanel);

  // Initialize the image widget
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing image widget");
      initImageWidget(imageId);
    } catch (error) {
      console.error("Error initializing image widget:", error);
    }
  }, 100);

  return widget;
}

// Returns the HTML markup for an image widget, given imageId and settingsId
function getImageWidgetMarkup({ imageId, settingsId }) {
  return `
    <div class="image-widget p-2">
      <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
        <div>
           Image
        </div>
        <div>
          <button class="btn btn-sm btn-link text-dark"
                  data-bs-toggle="offcanvas"
                  data-bs-target="#${settingsId}"
                  aria-controls="${settingsId}">
            <i class="las la-cog"></i>
          </button>
          <button class="btn btn-sm btn-link text-dark ms-1"
                  onclick="removeWidget(this)">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      <div id="${imageId}" class="image-container d-flex align-items-center justify-content-center">
        <div class="text-center text-muted">
          <i class="las la-cloud-upload-alt la-3x mb-3"></i>
          <p>No image selected.<br>Use settings to upload an image.</p>
        </div>
      </div>
    </div>
  `;
}

// Function to initialize the image widget
function initImageWidget(imageId) {
  console.log("Initializing image widget:", imageId);
  const container = document.getElementById(imageId);

  if (!container) {
    console.error("Image container not found:", imageId);
    return;
  }

  // Set default properties
  container.style.backgroundColor = "#ffffff";
  container.style.backgroundSize = "contain";
  container.style.backgroundPosition = "center";
  container.style.backgroundRepeat = "no-repeat";

  console.log("Image widget initialized:", imageId);
}

// Function to preview an image from file input
function previewImage(imageId, fileInput) {
  const container = document.getElementById(imageId);
  if (!container || !fileInput.files.length) return;

  const file = fileInput.files[0];
  const reader = new FileReader();

  reader.onload = function (e) {
    // Clear any existing content
    container.innerHTML = "";

    // Set the background image
    container.style.backgroundImage = `url('${e.target.result}')`;

    // Store the image data
    container.dataset.imageSource = "file";
    container.dataset.imageData = e.target.result;
  };

  reader.readAsDataURL(file);
}

// Function to handle backdrop cleanup
function handleBackdropCleanup() {
  // Remove all backdrops
  const backdrops = document.querySelectorAll(".offcanvas-backdrop");
  backdrops.forEach((backdrop) => {
    backdrop.remove();
  });
}

// Function to initialize image settings
function initImageSettings(imageId, settingsId) {
  const settingsPanel = document.getElementById(settingsId);
  if (!settingsPanel) return;

  // Initialize offcanvas with proper options
  const bsOffcanvas = new bootstrap.Offcanvas(settingsPanel, {
    backdrop: true,
    keyboard: true,
    scroll: false,
  });

  // Remove any existing event listeners
  settingsPanel.removeEventListener(
    "hidden.bs.offcanvas",
    handleBackdropCleanup
  );
  // Add event listener for when offcanvas is hidden
  settingsPanel.addEventListener("hidden.bs.offcanvas", handleBackdropCleanup);

  // Show the offcanvas
  bsOffcanvas.show();
}

// Function to apply image settings
function applyImageSettings(imageId, settingsId) {
  const container = document.getElementById(imageId);
  if (!container) return;

  const fileInput = document.getElementById(`${settingsId}-upload`);
  const urlInput = document.getElementById(`${settingsId}-url`);
  const fitMode = document.getElementById(`${settingsId}-fit`).value;
  const position = document.getElementById(`${settingsId}-position`).value;
  const bgcolor = document.getElementById(`${settingsId}-bgcolor`).value;
  const showBorder = document.getElementById(`${settingsId}-border`).checked;
  const borderRadius = document.getElementById(`${settingsId}-radius`).value;

  // Check if a URL was entered
  if (
    urlInput.value &&
    (!container.dataset.imageSource ||
      container.dataset.imageSource !== "url" ||
      container.dataset.imageUrl !== urlInput.value)
  ) {
    // Clear any existing content
    container.innerHTML = "";

    // Set the background image
    container.style.backgroundImage = `url('${urlInput.value}')`;

    // Store the image data
    container.dataset.imageSource = "url";
    container.dataset.imageUrl = urlInput.value;
  }

  // Apply settings
  container.style.backgroundColor = bgcolor;
  container.style.backgroundSize = fitMode;
  container.style.backgroundPosition = position.replace("-", " ");

  // Apply border
  if (showBorder) {
    container.style.border = "1px solid #dee2e6";
  } else {
    container.style.border = "none";
  }

  // Apply border radius
  container.style.borderRadius = `${borderRadius}px`;

  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(
    document.getElementById(settingsId)
  );
  if (offcanvas) {
    offcanvas.hide();
  }
}

// Export the functions to global scope
window.addImageWidget = addImageWidget;
window.initImageWidget = initImageWidget;
window.previewImage = previewImage;
window.initImageSettings = initImageSettings;
window.applyImageSettings = applyImageSettings;
window.getImageWidgetMarkup = getImageWidgetMarkup;
