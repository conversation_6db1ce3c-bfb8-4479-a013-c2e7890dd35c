/* Image Widget Styles */
.image-widget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.image-container {
  flex: 1;
  min-height: 200px;
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

/* Settings icon styles */
.widget-header .btn-link {
  padding: 0;
  color: #333;
  text-decoration: none;
}

.widget-header .btn-link:hover {
  color: #02104f;
}

.widget-header .la-cog,
.widget-header .la-times {
  font-size: 1.1rem;
}

.widget-header .la-times:hover {
  color: #dc3545;
}

/* Widget header styles */
.image-widget .widget-header {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 8px 12px;
  border-radius: 4px;
  margin: -8px -8px 8px -8px;
}

/* Placeholder styles */
.image-container .text-muted {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Form control color input */
.form-control-color {
  max-width: 100%;
  height: 38px;
}
