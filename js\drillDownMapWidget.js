// Initialize counter only if it doesn't exist
if (typeof window.drillDownMapWidgetCounter === "undefined") {
  window.drillDownMapWidgetCounter = 0;
}

// Add a Drill Down Map Chart widget using amCharts v5
function addDrillDownMapWidget(initialConfig = {}) {
  const widgetId = `drilldown-map-widget-${++window.drillDownMapWidgetCounter}`;
  const chartContainerId = `drilldown-map-chart-${window.drillDownMapWidgetCounter}`;
  const settingsId = `drilldown-map-settings-${window.drillDownMapWidgetCounter}`;

  // Default configuration
  const config = {
    title: initialConfig.title || "Drill Down Map",
    notes: initialConfig.notes || "",
    source: initialConfig.source || "",
    lastUpdate: initialConfig.lastUpdate || "",
    nextUpdate: initialConfig.nextUpdate || "",
  };

  // Add widget to grid
  const newWidget = window.grid.addWidget({
    w: initialConfig.w || 12,
    h: initialConfig.h || 12,
    content: `
      <div class="drilldown-map-widget widget p-2" id="${widgetId}" style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div class="widget-title editable-title" data-editable="true" title="Click to edit title">
            <span>${config.title}</span>
          </div>
          <div class="widget-actions">
            <button class="btn btn-link" data-bs-toggle="offcanvas" data-bs-target="#${settingsId}" aria-controls="${settingsId}">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-link ms-1" onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; overflow: hidden;">
          <div id="${chartContainerId}" class="chart-container" style="width: 100%; height: 100%; position: absolute; top: 0; left: 0; right: 0; bottom: 0;"></div>
        </div>
        <div class="widget-footer mt-2" style="padding: 0.5rem 0; border-top: 1px solid #e5e9f0; font-size: 10px; color: #6c757d; text-align: left;">
          ${
            config.notes
              ? `<div><i class="las la-clipboard"></i> Notes : ${config.notes}</div>`
              : ""
          }
          ${
            config.source
              ? `<div><i class="las la-database"></i> Source : ${config.source}</div>`
              : ""
          }
          ${
            config.lastUpdate
              ? `<div><i class="las la-calendar-alt"></i> Last Update : ${config.lastUpdate}</div>`
              : ""
          }
          ${
            config.nextUpdate
              ? `<div><i class="las la-calendar-plus"></i> Next Update : ${config.nextUpdate}</div>`
              : ""
          }
        </div>
      </div>
    `,
  });

  // Create settings panel
  createDrillDownMapSettingsOffcanvas(
    settingsId,
    widgetId,
    chartContainerId,
    config
  );

  // Initialize chart after a short delay to ensure container is ready
  setTimeout(() => {
    initializeDrillDownMap(chartContainerId);
  }, 0);
}

function initializeDrillDownMap(containerId) {
  // Check if amCharts is loaded
  if (!window.am5 || !window.am5map) {
    console.error("amCharts core or map library not loaded");
    return;
  }
  if (!window.am5geodata_continentsLow || !window.am5geodata_worldLow) {
    console.error("Required geodata (continentsLow, worldLow) not loaded");
    return;
  }

  const container = document.getElementById(containerId);
  if (!container) {
    console.error("Container not found:", containerId);
    return;
  }

  // Create root element
  const root = am5.Root.new(containerId);

  // Set themes
  root.setThemes([am5themes_Animated.new(root)]);

  // Create the map chart
  const chart = root.container.children.push(
    am5map.MapChart.new(root, {
      panX: "rotateX",
      projection: am5map.geoNaturalEarth1(), // Use Natural Earth projection like the demo
      homeGeoPoint: { latitude: 0, longitude: 0 }, // Adjust home view if needed
      homeZoomLevel: 1,
    })
  );

  // Define brand colors with fallbacks
  const brandColors = (window.chartConfig &&
    window.chartConfig.brandColors) || [
    "#00b19c", // Default Teal
    "#3bcd3f", // Default Light Green
    "#007365", // Default Dark Teal
    "#8dbac4", // Default Pale Blue
    "#02104f", // Default Dark Blue
  ];

  const continentFillColor = am5.color(brandColors[0] || "#67B7DC");
  const countryFillColor = am5.color(brandColors[1] || "#9FD5F1");
  const hoverFillColor = am5.color(brandColors[2] || "#3c5bdc");

  // Create polygon series for continents
  const continentSeries = chart.series.push(
    am5map.MapPolygonSeries.new(root, {
      geoJSON: am5geodata_continentsLow,
      exclude: ["antarctica"], // Exclude Antarctica like the demo
    })
  );

  continentSeries.mapPolygons.template.setAll({
    tooltipText: "{name}",
    interactive: true,
    fill: continentFillColor,
    stroke: am5.color("#FFFFFF"),
    strokeWidth: 0.5,
  });

  continentSeries.mapPolygons.template.states.create("hover", {
    fill: hoverFillColor,
  });

  // Create polygon series for countries (initially hidden)
  const countrySeries = chart.series.push(
    am5map.MapPolygonSeries.new(root, {
      geoJSON: am5geodata_worldLow,
      exclude: ["AQ"], // Also exclude Antarctica here
      visible: false, // Start hidden
    })
  );

  countrySeries.mapPolygons.template.setAll({
    tooltipText: "{name}",
    interactive: true,
    fill: countryFillColor,
    stroke: am5.color("#FFFFFF"),
    strokeWidth: 0.3,
  });

  countrySeries.mapPolygons.template.states.create("hover", {
    fill: hoverFillColor,
  });

  // Add a button to go back to continents view (initially hidden)
  const homeButton = chart.children.push(
    am5.Button.new(root, {
      paddingTop: 10,
      paddingBottom: 10,
      x: am5.percent(100),
      centerX: am5.percent(95),
      opacity: 0,
      visible: false,
      interactiveChildren: false,
      icon: am5.Graphics.new(root, {
        svgPath:
          "M16,8 L14,8 L14,16 L10,16 L10,10 L6,10 L6,16 L2,16 L2,8 L0,8 L8,0 L16,8 Z M16,8",
        fill: am5.color(0xffffff), // White icon fill
      }),
    })
  );

  // Zoom in on continent click
  continentSeries.mapPolygons.template.events.on("click", function (ev) {
    continentSeries.zoomToDataItem(ev.target.dataItem);
    // Use timeout to ensure zoom animation starts before hiding/showing
    setTimeout(() => {
      continentSeries.hide(0); // Hide instantly
      countrySeries.show(0); // Show instantly
      homeButton.show(); // Fade in button
    }, 50); // Small delay
  });

  // Go back home on button click
  homeButton.events.on("click", function () {
    chart.goHome();
    // Use timeout for smoother transition
    setTimeout(() => {
      continentSeries.show(0);
      countrySeries.hide(0);
      homeButton.hide();
    }, 50);
  });

  // Make chart take up 100% of container
  chart.setAll({
    maxZoomLevel: 32, // Allow deep zoom if needed
    wheelY: "zoom",
    wheelX: "none",
  });

  // Set up chart animations
  chart.appear(1000, 100);

  // Add resize observer to handle widget resizing
  const resizeObserver = new ResizeObserver(() => {
    root.resize();
  });
  resizeObserver.observe(container);

  // Clean up observer when widget is removed
  // Return the dispose function for the root
  return () => {
    resizeObserver.disconnect();
    root.dispose();
  };
}

function createDrillDownMapSettingsOffcanvas(
  settingsId,
  widgetId,
  chartContainerId,
  currentConfig
) {
  const offcanvasContainer =
    document.getElementById("offcanvasContainer") || document.body;

  const offcanvasHtml = `
    <div class="offcanvas offcanvas-end" tabindex="-1" id="${settingsId}" aria-labelledby="${settingsId}Label">
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="${settingsId}Label">Drill Down Map Settings</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div class="offcanvas-body">
        <div class="mb-3">
          <label class="form-label">Chart Title</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-chartTitle" value="${currentConfig.title}">
        </div>

        <div class="mb-3">
          <label class="form-label">Notes</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-notes" rows="2">${currentConfig.notes}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Source</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-source" rows="2">${currentConfig.source}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Last Update</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-lastUpdate" value="${currentConfig.lastUpdate}">
        </div>

        <div class="mb-3">
          <label class="form-label">Next Update</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-nextUpdate" value="${currentConfig.nextUpdate}">
        </div>

        <button class="btn btn-primary w-100" onclick="applyDrillDownMapSettings('${widgetId}', '${settingsId}', '${chartContainerId}')">Apply Changes</button>
      </div>
    </div>
  `;

  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = offcanvasHtml;
  offcanvasContainer.appendChild(tempDiv.firstElementChild);
}

function applyDrillDownMapSettings(widgetId, settingsId, chartContainerId) {
  const widget = document.getElementById(widgetId);
  const settings = document.getElementById(settingsId);

  // Update widget title
  widget.querySelector(".widget-title span").textContent =
    document.getElementById(`${settingsId}-chartTitle`).value;

  // Update notes and source
  const notes = document.getElementById(`${settingsId}-notes`).value;
  const source = document.getElementById(`${settingsId}-source`).value;
  const lastUpdate = document.getElementById(`${settingsId}-lastUpdate`).value;
  const nextUpdate = document.getElementById(`${settingsId}-nextUpdate`).value;

  // Update footer content
  const footer = widget.querySelector(".widget-footer");
  footer.innerHTML = `
    ${
      notes
        ? `<div><i class="las la-clipboard"></i> Notes : ${notes}</div>`
        : ""
    }
    ${
      source
        ? `<div><i class="las la-database"></i> Source : ${source}</div>`
        : ""
    }
    ${
      lastUpdate
        ? `<div><i class="las la-calendar-alt"></i> Last Update : ${lastUpdate}</div>`
        : ""
    }
    ${
      nextUpdate
        ? `<div><i class="las la-calendar-plus"></i> Next Update : ${nextUpdate}</div>`
        : ""
    }
  `;

  // Close settings panel
  const offcanvasElement = document.getElementById(settingsId);
  if (offcanvasElement) {
    const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvasElement);
    if (offcanvasInstance) {
      offcanvasInstance.hide();
    }
  }
}

// Export functions to global scope
window.addDrillDownMapWidget = addDrillDownMapWidget;
window.applyDrillDownMapSettings = applyDrillDownMapSettings;
