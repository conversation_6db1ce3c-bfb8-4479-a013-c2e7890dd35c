// Add a tabbed container widget
function addTabbedContainerWidget() {
  console.log("Adding tabbed container widget");
  const widgetId = "tabbed-container-" + Date.now();
  const settingsId = "settings-" + widgetId;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 12,
    h: 8,
    id: widgetId, // Ensure we have an ID for later reference
    content: `
      <div class="tabbed-container-widget p-2">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
           Tabbed Container
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark toggle-tab-targeting" title="Toggle Tab Targeting" 
                    data-container-id="${widgetId}" id="toggle-${widgetId}">
              <i class="las la-crosshairs"></i>
              <span class="targeting-indicator active">●</span>
            </button>
            <button class="btn btn-sm btn-link text-dark" 
                    data-bs-toggle="offcanvas" 
                    data-bs-target="#${settingsId}" 
                    aria-controls="${settingsId}">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1" 
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="tabs-navigation mb-2">
          <ul class="nav nav-tabs" id="tabs-${widgetId}" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="tab1-${widgetId}-tab" data-bs-toggle="tab" 
                      data-bs-target="#tab1-${widgetId}" type="button" role="tab" 
                      aria-controls="tab1-${widgetId}" aria-selected="true"
                      data-tab-id="${widgetId}" data-tab-number="1">
                Tab 1
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="tab2-${widgetId}-tab" data-bs-toggle="tab" 
                      data-bs-target="#tab2-${widgetId}" type="button" role="tab" 
                      aria-controls="tab2-${widgetId}" aria-selected="false"
                      data-tab-id="${widgetId}" data-tab-number="2">
                Tab 2
              </button>
            </li>
          </ul>
        </div>
        <div class="tab-content" id="tabs-${widgetId}-content">
          <div class="tab-pane fade show active" id="tab1-${widgetId}" role="tabpanel" 
               aria-labelledby="tab1-${widgetId}-tab">
            <div class="nested-grid-container" style="height: 100%; min-height: 200px; position: relative;">
              <div class="grid-stack nested-grid" id="nested-grid-tab1-${widgetId}" 
                   data-gs-parent-id="${widgetId}" data-gs-tab-number="1"></div>
            </div>
          </div>
          <div class="tab-pane fade" id="tab2-${widgetId}" role="tabpanel" 
               aria-labelledby="tab2-${widgetId}-tab">
            <div class="nested-grid-container" style="height: 100%; min-height: 200px; position: relative;">
              <div class="grid-stack nested-grid" id="nested-grid-tab2-${widgetId}"
                   data-gs-parent-id="${widgetId}" data-gs-tab-number="2"></div>
            </div>
          </div>
        </div>
      </div>
    `,
  });

  // Create settings panel in the offcanvas container
  const offcanvasContainer = document.getElementById("offcanvasContainer");
  const settingsPanel = document.createElement("div");
  settingsPanel.className = "offcanvas offcanvas-end";
  settingsPanel.id = settingsId;
  settingsPanel.setAttribute("tabindex", "-1");
  settingsPanel.setAttribute("aria-labelledby", `${settingsId}-label`);
  
  settingsPanel.innerHTML = `
    <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="${settingsId}-label">Tabbed Container Settings</h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- Widget Title -->
      <div class="mb-3">
        <label for="${settingsId}-title" class="form-label">Widget Title</label>
        <input type="text" class="form-control" id="${settingsId}-title" value="Tabbed Container">
      </div>

      <!-- Tabs Management -->
      <div class="mb-3">
        <label class="form-label">Tabs Management</label>
        <div class="d-flex align-items-center mb-2">
          <input type="number" class="form-control" id="${settingsId}-tabCount" min="1" max="10" value="2" style="width: 70px;">
          <label class="ms-2 form-label mb-0" for="${settingsId}-tabCount">Number of Tabs</label>
        </div>
      </div>

      <!-- Tab Names -->
      <div id="${settingsId}-tabs-container" class="mb-3">
        <div class="mb-2">
          <label for="${settingsId}-tab1" class="form-label">Tab 1 Name</label>
          <input type="text" class="form-control" id="${settingsId}-tab1" value="Tab 1">
        </div>
        <div class="mb-2">
          <label for="${settingsId}-tab2" class="form-label">Tab 2 Name</label>
          <input type="text" class="form-control" id="${settingsId}-tab2" value="Tab 2">
        </div>
      </div>

      <!-- Tab Style Options -->
      <div class="mb-3">
        <label class="form-label">Tab Style</label>
        <select class="form-select" id="${settingsId}-tabStyle">
          <option value="tabs" selected>Standard Tabs</option>
          <option value="pills">Pills</option>
          <option value="underlined">Underlined</option>
        </select>
      </div>

      <!-- Apply Button -->
      <button class="btn btn-primary w-100" onclick="applyTabbedContainerSettings('${widgetId}', '${settingsId}')">
        Apply Changes
      </button>
    </div>
  `;
  offcanvasContainer.appendChild(settingsPanel);

  // Initialize nested grids for each tab
  setTimeout(() => {
    initializeNestedGrids(widgetId);
    
    // Store the container ID globally to track which container is active
    window.activeTabContainerId = widgetId;
    window.activeTabNumber = 1;
    
    // Set up tabs for drag and drop
    setupTabsForDragAndDrop(widgetId);
    
    // Override the standard widget addition functions
    patchWidgetAdditionFunctions(widgetId);
    
    // Setup tab targeting toggle button
    setupTabTargetingToggle(widgetId);
    
    // Set tab targeting active by default
    window.tabTargetingActive = true;
  }, 100);

  return widget;
}

// Store all tab grid instances
if (!window.tabGrids) {
  window.tabGrids = {};
}

// Initialize the nested grid for each tab
function initializeNestedGrids(widgetId) {
  console.log("Initializing nested grids for tabbed container:", widgetId);
  
  // Initialize grid for tab 1
  const tab1Grid = document.getElementById(`nested-grid-tab1-${widgetId}`);
  if (tab1Grid) {
    window.tabGrids[`${widgetId}-1`] = GridStack.init({
      column: 12,
      cellHeight: 40,
      margin: 5,
      float: true,
      acceptWidgets: true,
      disableOneColumnMode: true,
      alwaysShowResizeHandle: 'mobile',
      // Add static class to prevent interference with main grid
      staticGrid: false,
      animate: false, // Disable animation to prevent flickering
      // Set proper containment for dragging
      draggable: {
        handle: '.widget-header, .grid-stack-item-content > .chart-container',
        scroll: false,
        appendTo: 'parent',
        containment: 'parent',
        helper: 'clone'
      },
      resizable: {
        handles: 'e,se,s,sw,w',
        containment: 'parent'
      }
    }, tab1Grid);
    
    // Add event handlers to prevent propagation to parent grid
    setupGridEvents(window.tabGrids[`${widgetId}-1`], widgetId, 1);
    
    console.log(`Tab 1 grid initialized for container ${widgetId}:`, window.tabGrids[`${widgetId}-1`]);
  }
  
  // Initialize grid for tab 2
  const tab2Grid = document.getElementById(`nested-grid-tab2-${widgetId}`);
  if (tab2Grid) {
    window.tabGrids[`${widgetId}-2`] = GridStack.init({
      column: 12,
      cellHeight: 40,
      margin: 5,
      float: true,
      acceptWidgets: true,
      disableOneColumnMode: true,
      alwaysShowResizeHandle: 'mobile',
      // Add static class to prevent interference with main grid
      staticGrid: false,
      animate: false, // Disable animation to prevent flickering
      // Set proper containment for dragging
      draggable: {
        handle: '.widget-header, .grid-stack-item-content > .chart-container',
        scroll: false,
        appendTo: 'parent',
        containment: 'parent',
        helper: 'clone'
      },
      resizable: {
        handles: 'e,se,s,sw,w',
        containment: 'parent'
      }
    }, tab2Grid);
    
    // Add event handlers to prevent propagation to parent grid
    setupGridEvents(window.tabGrids[`${widgetId}-2`], widgetId, 2);
    
    console.log(`Tab 2 grid initialized for container ${widgetId}:`, window.tabGrids[`${widgetId}-2`]);
  }

  // Prevent main grid from accepting drops when a nested grid is active
  setupDragDropBoundaries(widgetId);
}

// Set up event handlers for nested grids to prevent event propagation issues
function setupGridEvents(grid, containerId, tabNumber) {
  if (!grid) return;
  
  // Clean up existing handlers if any
  if (grid._tabContainerEventsSetup) {
    // Already set up, avoid duplicate handlers
    return;
  }
  
  // Track when dragging starts in nested grid
  grid.on('dragstart', function(event, el) {
    console.log(`Tab ${tabNumber} grid dragstart in container ${containerId}`);
    
    // Mark that we're dragging within a nested grid
    window.isDraggingInNestedGrid = true;
    window.currentDragContainerId = containerId;
    window.currentDragTabNumber = tabNumber;
    
    // Add a class to the body to indicate dragging state
    document.body.classList.add('dragging-in-nested-grid');
    
    // Add visible containment indicator to help user see boundaries
    const tabPane = document.getElementById(`tab${tabNumber}-${containerId}`);
    if (tabPane) {
      tabPane.classList.add('active-drag-container');
    }
    
    // Stop propagation to prevent parent grid from also starting a drag
    event.stopPropagation();
  });
  
  // Track when dragging ends in nested grid
  grid.on('dragstop', function(event, el) {
    console.log(`Tab ${tabNumber} grid dragstop in container ${containerId}`);
    
    // Reset dragging state
    window.isDraggingInNestedGrid = false;
    window.currentDragContainerId = null;
    window.currentDragTabNumber = null;
    
    // Remove body class
    document.body.classList.remove('dragging-in-nested-grid');
    
    // Remove containment indicator
    const tabPane = document.getElementById(`tab${tabNumber}-${containerId}`);
    if (tabPane) {
      tabPane.classList.remove('active-drag-container');
    }
    
    // Re-enable accepting widgets by the main grid
    if (window.grid && window.grid.opts) {
      window.grid.opts.acceptWidgets = true;
    }
    
    // Stop propagation to prevent parent grid actions
    event.stopPropagation();
  });
  
  // Track when a widget is removed from nested grid
  grid.on('removed', function(event, items) {
    console.log(`Widget removed from tab ${tabNumber} grid in container ${containerId}`);
    
    // If a widget is removed from the nested grid, check if it was dragged outside
    // If outside the tab container, we need to add it to the main grid
    if (window.isDraggingInNestedGrid) {
      items.forEach(item => {
        // Add the widget to the main grid
        setTimeout(() => {
          if (window.grid && item.el) {
            window.grid.addWidget(item.el);
            console.log('Moved widget from nested grid to main grid');
          }
        }, 0);
      });
    }
  });
  
  // Mark as having event handlers set up
  grid._tabContainerEventsSetup = true;
}

// Control which grid accepts drops during dragging
function setupDragDropBoundaries(containerId) {
  // Get the tab container widget element
  const containerElement = document.querySelector(`#${containerId}`);
  if (!containerElement) return;
  
  // Listen for mouse events on the document to detect when mouse leaves tab area
  document.addEventListener('mousemove', function(event) {
    if (!window.isDraggingInNestedGrid) return;
    
    // If we're dragging within a nested grid, check if the mouse is still over the tab content
    const currentTabPane = document.getElementById(`tab${window.currentDragTabNumber}-${window.currentDragContainerId}`);
    if (!currentTabPane) return;
    
    // Get the boundaries of the current tab pane
    const tabRect = currentTabPane.getBoundingClientRect();
    
    // Check if mouse is outside the tab pane
    const isOutside = (
      event.clientX < tabRect.left || 
      event.clientX > tabRect.right || 
      event.clientY < tabRect.top || 
      event.clientY > tabRect.bottom
    );
    
    // If mouse is outside tab pane, allow main grid to accept widgets
    if (isOutside && window.grid) {
      window.grid.opts.acceptWidgets = true;
      currentTabPane.classList.remove('active-drag-container');
      document.body.classList.add('dragging-outside-tab');
    } else {
      // If mouse is inside, only allow nested grid to accept widgets
      if (window.grid) {
        window.grid.opts.acceptWidgets = false;
      }
      currentTabPane.classList.add('active-drag-container');
      document.body.classList.remove('dragging-outside-tab');
    }
  });
  
  // Clean up on mouseup
  document.addEventListener('mouseup', function(event) {
    if (window.isDraggingInNestedGrid) {
      // Reset dragging state
      window.isDraggingInNestedGrid = false;
      window.currentDragContainerId = null;
      window.currentDragTabNumber = null;
      
      // Reset appearance
      document.body.classList.remove('dragging-in-nested-grid');
      document.body.classList.remove('dragging-outside-tab');
      document.querySelectorAll('.active-drag-container').forEach(el => {
        el.classList.remove('active-drag-container');
      });
      
      // Re-enable accepting widgets by the main grid
      if (window.grid && window.grid.opts) {
        window.grid.opts.acceptWidgets = true;
      }
    }
  });
}

// Configure tabs to handle drag and drop
function setupTabsForDragAndDrop(widgetId) {
  // Add click handlers to all tab buttons
  const tabButtons = document.querySelectorAll(`[data-tab-id="${widgetId}"]`);
  
  tabButtons.forEach(button => {
    button.addEventListener('shown.bs.tab', function(event) {
      // Get the tab number from the button
      const tabNumber = this.getAttribute('data-tab-number');
      
      console.log(`Tab ${tabNumber} activated in container ${widgetId}`);
      
      // Update global tracking of active tab
      window.activeTabContainerId = widgetId;
      window.activeTabNumber = tabNumber;
      
      // Make sure the widget gallery knows to add to this tab
      setupWidgetGalleryRedirection();
      
      // Make sure the active grid is correctly responsive
      const activeGrid = window.tabGrids[`${widgetId}-${tabNumber}`];
      if (activeGrid) {
        // Make sure grid dimensions are correct after tab switch
        setTimeout(() => {
          activeGrid.compact();
          activeGrid.onResize();
        }, 50);
      }
    });
  });
  
  // Initially setup redirection for the default active tab
  setupWidgetGalleryRedirection();
  
  // Add CSS to prevent flickering and improve drop behavior
  addDragDropCSS();
}

// Setup tab targeting toggle button
function setupTabTargetingToggle(widgetId) {
  const toggleButton = document.getElementById(`toggle-${widgetId}`);
  if (!toggleButton) return;
  
  // Initialize the toggle button with tooltip
  toggleButton.addEventListener('click', function(event) {
    event.preventDefault();
    event.stopPropagation();
    
    // Toggle tab targeting
    window.tabTargetingActive = !window.tabTargetingActive;
    
    // Update visual indication
    updateTabTargetingIndicators();
    
    console.log(`Tab targeting ${window.tabTargetingActive ? 'enabled' : 'disabled'} for ${widgetId}`);
  });
  
  // Initial state
  updateTabTargetingIndicators();
}

// Update all tab targeting indicators based on current state
function updateTabTargetingIndicators() {
  const indicators = document.querySelectorAll('.targeting-indicator');
  
  indicators.forEach(indicator => {
    if (window.tabTargetingActive) {
      indicator.classList.add('active');
      indicator.closest('button').setAttribute('title', 'Tab Targeting: ON - Click to disable');
    } else {
      indicator.classList.remove('active');
      indicator.closest('button').setAttribute('title', 'Tab Targeting: OFF - Click to enable');
    }
  });
  
  // Update body class for styling
  if (window.tabTargetingActive) {
    document.body.classList.add('tab-targeting-active');
  } else {
    document.body.classList.remove('tab-targeting-active');
  }
}

// This function redirects widget gallery items to add to the active tab
function setupWidgetGalleryRedirection() {
  console.log("Setting up widget gallery redirection to active tab:", 
              window.activeTabContainerId, window.activeTabNumber);
  
  // Clean up old handlers first if they exist
  cleanupWidgetGalleryRedirection();
  
  // Skip if we don't have an active tab
  if (!window.activeTabContainerId || !window.activeTabNumber) {
    console.log("No active tab, skipping gallery redirection setup");
    return;
  }
  
  // Store original handlers if not already stored
  if (!window.originalWidgetHandlers) {
    window.originalWidgetHandlers = {};
  }
  
  // Get all widget gallery items
  const galleryItems = document.querySelectorAll('.widget-item');
  
  // For each gallery item, modify its click behavior
  galleryItems.forEach((item, index) => {
    // Store the original onclick function if not already stored
    if (!window.originalWidgetHandlers[index] && item.onclick) {
      window.originalWidgetHandlers[index] = item.onclick;
    }
    
    // Replace with our custom handler
    item.onclick = function(event) {
      // Prevent default action
      event.preventDefault();
      
      // Get the widget type from class or data attribute
      let widgetType = this.className.match(/widget-category-(\w+)/);
      if (widgetType && widgetType[1]) {
        widgetType = widgetType[1];
      } else {
        widgetType = 'unknown';
      }
      
      // Get the widget label
      const widgetLabel = this.querySelector('.widget-label')?.textContent || 'Widget';
      
      console.log(`Gallery item clicked: ${widgetType} - ${widgetLabel}`);
      
      // Check if tab targeting is active
      if (!window.tabTargetingActive) {
        console.log("Tab targeting is disabled, adding widget to main dashboard");
        const widgetFunction = getWidgetFunctionForGalleryItem(this);
        if (widgetFunction) {
          widgetFunction();
        } else if (window.originalWidgetHandlers[index]) {
          window.originalWidgetHandlers[index].call(this, event);
        }
        return;
      }
      
      // Check if we have an active tab container and if it exists in the DOM
      if (window.activeTabContainerId && window.activeTabNumber) {
        const tabContainer = document.getElementById(`nested-grid-tab${window.activeTabNumber}-${window.activeTabContainerId}`);
        
        if (tabContainer) {
          console.log(`Adding widget to tab ${window.activeTabNumber} in container ${window.activeTabContainerId}`);
          
          // Get the active tab's grid
          const activeTabGrid = window.tabGrids[`${window.activeTabContainerId}-${window.activeTabNumber}`];
          
          if (activeTabGrid) {
            console.log("Found active tab grid:", activeTabGrid);
            
            // Determine which widget to add based on the gallery item clicked
            addWidgetToActiveTab(this, activeTabGrid);
            return;
          }
        } else {
          // Container no longer exists in DOM, clean up global state
          console.log("Tab container not found in DOM, removing active tab references");
          window.activeTabContainerId = null;
          window.activeTabNumber = null;
          cleanupWidgetGalleryRedirection();
        }
      }
      
      console.log("No active tab container, adding widget to main grid");
      
      // Fall back to default behavior - execute the click handler associated with this gallery item
      const widgetFunction = getWidgetFunctionForGalleryItem(this);
      if (widgetFunction) {
        widgetFunction();
      } else if (window.originalWidgetHandlers[index]) {
        // If we have the original handler, use it
        window.originalWidgetHandlers[index].call(this, event);
      }
    };
    
    // Mark this item as having a custom handler
    item.setAttribute('data-has-custom-handler', 'true');
  });
}

// Remove widget gallery redirection handlers and restore original behavior
function cleanupWidgetGalleryRedirection() {
  console.log("Cleaning up widget gallery redirection");
  
  // Get all widget gallery items
  const galleryItems = document.querySelectorAll('.widget-item');
  
  // For each gallery item, restore original click behavior if it exists
  galleryItems.forEach((item, index) => {
    if (item.getAttribute('data-has-custom-handler') === 'true' && window.originalWidgetHandlers && window.originalWidgetHandlers[index]) {
      // Restore original handler
      item.onclick = window.originalWidgetHandlers[index];
      // Remove flag
      item.removeAttribute('data-has-custom-handler');
    }
  });
}

// This handles adding widgets to the active tab grid directly
function addWidgetToActiveTab(galleryItem, activeTabGrid) {
  // Get the function that should create this widget type
  const widgetFunction = getWidgetFunctionForGalleryItem(galleryItem);
  
  if (!widgetFunction) {
    console.error("Could not determine widget function for gallery item");
    return;
  }
  
  console.log("Widget function determined:", widgetFunction.name || "unnamed function");
  
  // Create a temporary wrapper for the widget function that redirects to our tab
  const originalAddWidget = window.grid.addWidget;
  
  // Temporarily replace the grid.addWidget function to capture widget creation
  window.grid.addWidget = function(opts) {
    console.log("Intercepted addWidget call, redirecting to tab grid:", opts);
    
    // Add CSS class to indicate this is in a tab container
    if (opts.content) {
      // Parse content to add class to the widget
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = opts.content;
      const widgetElement = tempDiv.firstElementChild;
      if (widgetElement) {
        widgetElement.classList.add('in-tab-container');
        opts.content = tempDiv.innerHTML;
      }
    }
    
    // Instead of adding to main grid, add to the tab's grid
    return activeTabGrid.addWidget(opts);
  };
  
  try {
    // Call the widget function which will now use our modified addWidget
    console.log("Calling widget function:", widgetFunction.name || "unnamed function");
    const result = widgetFunction();
    console.log("Widget function execution result:", result);
    return result;
  } catch (error) {
    console.error("Error creating widget:", error);
  } finally {
    // Restore the original addWidget function
    window.grid.addWidget = originalAddWidget;
  }
}

// Map gallery items to their corresponding widget creation functions
function getWidgetFunctionForGalleryItem(galleryItem) {
  // Extract function name from onclick attribute or class
  const classNames = galleryItem.className.split(' ');
  let functionName = null;
  
  // Get the widget label for better identification
  const widgetLabel = galleryItem.querySelector('.widget-label')?.textContent || '';
  
  // Check if it has explicit onclick text content
  const onclickText = galleryItem.getAttribute('onclick');
  if (onclickText) {
    functionName = onclickText.trim().replace(/\(\)$/, '');
  } 
  
  console.log(`Mapping widget: "${widgetLabel}" with function name: ${functionName}`);
  
  // Map of gallery item identifiers to widget functions
  const widgetFunctionMap = {
    'addTextWidget': window.addTextWidget,
    'addHandsontableWidget': window.addHandsontableWidget,
    'addImageWidget': window.addImageWidget,
    'addVideoWidget': window.addVideoWidget,
    'addPieChartWidget': window.addPieChartWidget,
    'addBarChartWidget': window.addBarChartWidget,
    'addLineChartWidget': window.addLineChartWidget,
    'addStackedColumnChartWidget': window.addStackedColumnChartWidget,
    'addPercentStackedColumnChartWidget': window.addPercentStackedColumnChartWidget,
    'addAreaChartWidget': window.addAreaChartWidget,
    'addStockChartWidget': window.addStockChartWidget,
    'addWordCloudWidget': window.addWordCloudWidget,
    'addBubbleChartWidget': window.addBubbleChartWidget,
    'addCurvedLineMapWidget': window.addCurvedLineMapWidget,
    'addDrillDownMapWidget': window.addDrillDownMapWidget,
    'addWorldMapWidget': window.addWorldMapWidget,
    'addLinkedPieChartWidget': window.addLinkedPieChartWidget,
    'addPieOfPieWidget': window.addPieOfPieWidget,
    'addFilterWidget': window.addDropdownFilterWidget,
    'addDropdownFilterWidget': window.addDropdownFilterWidget,
    'addPdfViewerWidget': window.addPdfViewerWidget,
    // Make sure we have both case variations
    'addPDFViewerWidget': window.addPdfViewerWidget
  };
  
  // Check both onclick and label to determine the correct function
  if (functionName && widgetFunctionMap[functionName]) {
    console.log(`Using mapped function from onclick: ${functionName}`);
    return widgetFunctionMap[functionName];
  }
  
  // Handle PDF viewer special case - it might be defined in HTML with a different case than the function
  if (widgetLabel === 'PDF viewer' && window.addPdfViewerWidget) {
    console.log("Matched PDF viewer widget by label");
    return window.addPdfViewerWidget;
  }
  
  // Handle Filter Widget special case
  if (widgetLabel === 'Filter Widget' && window.addDropdownFilterWidget) {
    console.log("Matched Filter Widget by label");
    return window.addDropdownFilterWidget;
  }
  
  // Map based on category and label if explicit function not found
  if (galleryItem.classList.contains('widget-category-charts')) {
    if (widgetLabel.includes('Pie')) return window.addPieChartWidget;
    if (widgetLabel.includes('Bar')) return window.addBarChartWidget;
    if (widgetLabel.includes('Line')) return window.addLineChartWidget;
    if (widgetLabel.includes('Area')) return window.addAreaChartWidget;
    if (widgetLabel.includes('Stock')) return window.addStockChartWidget;
    if (widgetLabel.includes('Word Cloud')) return window.addWordCloudWidget;
    if (widgetLabel.includes('Bubble')) return window.addBubbleChartWidget;
    if (widgetLabel.includes('Stacked Column')) return window.addStackedColumnChartWidget;
    if (widgetLabel.includes('Percent Stacked')) return window.addPercentStackedColumnChartWidget;
  } else if (galleryItem.classList.contains('widget-category-insert')) {
    if (widgetLabel.includes('Text')) return window.addTextWidget;
    if (widgetLabel.includes('Spreadsheet')) return window.addHandsontableWidget;
    if (widgetLabel.includes('Image')) return window.addImageWidget;
    if (widgetLabel.includes('Video')) return window.addVideoWidget;
  } else if (galleryItem.classList.contains('widget-category-advanced')) {
    // Map advanced widgets using their label text
    if (widgetLabel.includes('Filter')) return window.addDropdownFilterWidget;
    if (widgetLabel.includes('Section')) return window.addSectionContainerWidget;
    if (widgetLabel.includes('Tabbed Container')) return window.addTabbedContainerWidget;
    // Additional advanced widgets can be added here
  } else if (galleryItem.classList.contains('widget-category-maps')) {
    if (widgetLabel.includes('Curved Line')) return window.addCurvedLineMapWidget;
    if (widgetLabel.includes('Drill Down')) return window.addDrillDownMapWidget;
    if (widgetLabel.includes('World')) return window.addWorldMapWidget;
  }
  
  // For debugging
  console.log(`Could not map widget "${widgetLabel}" to a specific function, class names: ${classNames.join(', ')}`);
  
  // Try to infer from item appearance if everything else fails
  const iconElement = galleryItem.querySelector('.widget-icon i');
  if (iconElement) {
    const iconClass = iconElement.className;
    if (iconClass.includes('la-table')) return window.addHandsontableWidget;
    if (iconClass.includes('la-image')) return window.addImageWidget;
    if (iconClass.includes('la-video')) return window.addVideoWidget;
    if (iconClass.includes('la-chart-pie')) return window.addPieChartWidget;
    if (iconClass.includes('la-chart-bar')) return window.addBarChartWidget;
    if (iconClass.includes('la-chart-line')) return window.addLineChartWidget;
    if (iconClass.includes('la-filter')) return window.addDropdownFilterWidget;
    if (iconClass.includes('la-file-pdf')) return window.addPdfViewerWidget;
  }
  
  // If we can't determine a specific widget function, do a more thorough check
  for (const key in window) {
    if (typeof window[key] === 'function' && 
        key.startsWith('add') && 
        key.endsWith('Widget') && 
        widgetLabel && 
        key.toLowerCase().includes(widgetLabel.toLowerCase().replace(/\s+/g, ''))) {
      console.log(`Found matching function by name: ${key}`);
      return window[key];
    }
  }
  
  // Ultimate fallback - but log a warning
  console.warn(`Could not determine specific widget function for "${widgetLabel}", using Text widget as fallback`);
  return window.addTextWidget;
}

// Patch global widget addition functions to work with active tab
function patchWidgetAdditionFunctions(containerId) {
  // Only do this once
  if (window.widgetFunctionsPatched) return;
  
  // Store original grid addWidget function
  if (!window.originalGridAddWidget) {
    window.originalGridAddWidget = window.grid.addWidget;
  }
  
  // Replace global grid addWidget to check for active tab
  window.grid.addWidget = function(opts) {
    // Check if tab targeting is active and we have an active tab
    if (window.tabTargetingActive && window.activeTabContainerId && window.activeTabNumber) {
      const activeGrid = window.tabGrids[`${window.activeTabContainerId}-${window.activeTabNumber}`];
      if (activeGrid) {
        console.log("Redirecting addWidget to active tab:", window.activeTabContainerId, window.activeTabNumber);
        return activeGrid.addWidget(opts);
      }
    }
    
    // Fall back to original function
    return window.originalGridAddWidget.call(this, opts);
  };
  
  window.widgetFunctionsPatched = true;
}

// Function to apply settings from the settings panel
function applyTabbedContainerSettings(widgetId, settingsId) {
  // Get widget elements
  const widget = document.querySelector(`.grid-stack-item .tabbed-container-widget`);
  const widgetHeader = widget.querySelector('.widget-header > div:first-child');
  
  // Get settings values
  const widgetTitle = document.getElementById(`${settingsId}-title`).value;
  const tabStyle = document.getElementById(`${settingsId}-tabStyle`).value;
  const tabCount = parseInt(document.getElementById(`${settingsId}-tabCount`).value);
  
  // Update widget title
  widgetHeader.innerHTML = `<i class="las la-folder-plus"></i> ${widgetTitle}`;

  // Get tab navigation and content
  const tabsNav = document.getElementById(`tabs-${widgetId}`);
  const tabsContent = document.getElementById(`tabs-${widgetId}-content`);
  
  // Save current state before rebuilding
  const activeTabNumber = window.activeTabNumber || 1;
  const savedTabContents = {};
  
  // Save contents of each tab
  for (let i = 1; i <= 10; i++) {
    const gridKey = `${widgetId}-${i}`;
    if (window.tabGrids[gridKey]) {
      savedTabContents[i] = window.tabGrids[gridKey].save();
    }
  }
  
  // Clear existing tabs
  tabsNav.innerHTML = '';
  tabsContent.innerHTML = '';
  
  // Update tab style
  tabsNav.className = tabStyle === 'pills' ? 'nav nav-pills' : 
                      tabStyle === 'underlined' ? 'nav nav-tabs nav-tabs-underlined' : 
                      'nav nav-tabs';
  
  // Add tabs based on count
  for (let i = 1; i <= tabCount; i++) {
    const tabName = document.getElementById(`${settingsId}-tab${i}`) ? 
                    document.getElementById(`${settingsId}-tab${i}`).value : 
                    `Tab ${i}`;
    
    // Make the previously active tab active again if it still exists, otherwise tab 1
    const isActive = (i === activeTabNumber && i <= tabCount) || (activeTabNumber > tabCount && i === 1);
    
    // Create nav item
    const navItem = document.createElement('li');
    navItem.className = 'nav-item';
    navItem.setAttribute('role', 'presentation');
    
    // Create tab button
    const tabButton = document.createElement('button');
    tabButton.className = isActive ? 'nav-link active' : 'nav-link';
    tabButton.id = `tab${i}-${widgetId}-tab`;
    tabButton.setAttribute('data-bs-toggle', 'tab');
    tabButton.setAttribute('data-bs-target', `#tab${i}-${widgetId}`);
    tabButton.setAttribute('type', 'button');
    tabButton.setAttribute('role', 'tab');
    tabButton.setAttribute('aria-controls', `tab${i}-${widgetId}`);
    tabButton.setAttribute('aria-selected', isActive ? 'true' : 'false');
    tabButton.setAttribute('data-tab-id', widgetId);
    tabButton.setAttribute('data-tab-number', i);
    tabButton.textContent = tabName;
    
    navItem.appendChild(tabButton);
    tabsNav.appendChild(navItem);
    
    // Create tab content
    const tabPane = document.createElement('div');
    tabPane.className = isActive ? 'tab-pane fade show active' : 'tab-pane fade';
    tabPane.id = `tab${i}-${widgetId}`;
    tabPane.setAttribute('role', 'tabpanel');
    tabPane.setAttribute('aria-labelledby', `tab${i}-${widgetId}-tab`);
    
    // Add nested grid container inside tab
    tabPane.innerHTML = `
      <div class="nested-grid-container" style="height: 100%; min-height: 200px; position: relative;">
        <div class="grid-stack nested-grid" id="nested-grid-tab${i}-${widgetId}" 
             data-gs-parent-id="${widgetId}" data-gs-tab-number="${i}"></div>
      </div>
    `;
    
    tabsContent.appendChild(tabPane);
    
    // Update tab name fields in settings panel
    if (!document.getElementById(`${settingsId}-tab${i}`)) {
      const tabContainer = document.getElementById(`${settingsId}-tabs-container`);
      const tabNameField = document.createElement('div');
      tabNameField.className = 'mb-2';
      tabNameField.innerHTML = `
        <label for="${settingsId}-tab${i}" class="form-label">Tab ${i} Name</label>
        <input type="text" class="form-control" id="${settingsId}-tab${i}" value="${tabName}">
      `;
      tabContainer.appendChild(tabNameField);
    }
  }
  
  // Initialize nested grids for new tabs
  setTimeout(() => {
    initializeNestedGrids(widgetId);
    
    // Restore saved contents to tabs
    for (let i = 1; i <= tabCount; i++) {
      const gridKey = `${widgetId}-${i}`;
      if (window.tabGrids[gridKey] && savedTabContents[i]) {
        window.tabGrids[gridKey].load(savedTabContents[i]);
      }
    }
    
    // Set up tabs for drag and drop again
    setupTabsForDragAndDrop(widgetId);
    
    // Update active tab tracking
    window.activeTabContainerId = widgetId;
    window.activeTabNumber = Math.min(activeTabNumber, tabCount);
  }, 100);
  
  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById(settingsId));
  if (offcanvas) {
    offcanvas.hide();
  }
}

// Add the widget to the widget gallery
document.addEventListener("DOMContentLoaded", function () {
  // Check if the Advanced category exists, if not create it
  let advancedCategory = document.querySelector('.widget-category[data-category="advanced"]');
  if (!advancedCategory) {
    console.log("Advanced category not found in DOM, cannot add widget to gallery");
    return;
  }
  
  // Add widget to the widget gallery
  const widgetGallery = document.querySelector('.widget-gallery');
  if (widgetGallery) {
    const widgetItem = document.createElement('div');
    widgetItem.className = 'widget-item widget-category-advanced';
    widgetItem.onclick = function() { addTabbedContainerWidget(); };
    
    widgetItem.innerHTML = `
      <div class="widget-icon">
        <i class="las la-folder-plus"></i>
      </div>
      <div class="widget-label">Tabbed Container</div>
    `;
    
    widgetGallery.appendChild(widgetItem);
  }
});

// Export functions
window.addTabbedContainerWidget = addTabbedContainerWidget;
window.applyTabbedContainerSettings = applyTabbedContainerSettings;

// Add custom CSS rules to help with drag and drop
function addDragDropCSS() {
  if (document.getElementById('tabbed-container-styles')) return;
  
  const styleElement = document.createElement('style');
  styleElement.id = 'tabbed-container-styles';
  styleElement.textContent = `
    /* Prevent flicker during drag */
    .grid-stack-item.ui-draggable-dragging {
      transition: none !important;
    }
    
    /* Style for active drag container */
    .active-drag-container {
      background-color: rgba(240, 248, 255, 0.5);
      outline: 2px dashed #02104f;
      outline-offset: -2px;
    }
    
    /* Show grid lines in nested grids for easier placement */
    .nested-grid {
      background-image: linear-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
      background-size: 8.333% 50px, 8.333% 50px;
      background-position: -1px -1px;
    }
    
    /* Style for widgets in tab container */
    .in-tab-container {
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
    }
    
    /* Style when dragging outside tab */
    body.dragging-outside-tab .nested-grid {
      opacity: 0.7;
    }
    
    /* Targeting indicator styles */
    .targeting-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-left: 3px;
      color: #ccc;
      font-size: 8px;
      transition: color 0.3s ease;
    }
    
    .targeting-indicator.active {
      color: #02104f;
    }
    
    /* Add visual indication when tab targeting is active */

    
    /* Highlight toggle button when active */
    .toggle-tab-targeting {
      position: relative;
      border-radius: 3px;
      transition: background-color 0.3s ease;
    }
    
    body.tab-targeting-active .toggle-tab-targeting {
      background-color: rgba(13, 110, 253, 0.1);
    }
  `;
  
  document.head.appendChild(styleElement);
}

// Function to remove widget from DOM and clean up related event handlers
function removeWidget(btnElement) {
  // Get the widget element (parent grid-stack-item)
  const widgetElement = btnElement.closest('.grid-stack-item');
  if (!widgetElement) return;
  
  // Check if this is a tabbed container
  const tabbedContainer = widgetElement.querySelector('.tabbed-container-widget');
  if (tabbedContainer) {
    const containerId = widgetElement.id;
    console.log("Removing tabbed container:", containerId);
    
    // If this is the active tab container, reset global state
    if (window.activeTabContainerId === containerId) {
      window.activeTabContainerId = null;
      window.activeTabNumber = null;
      window.tabTargetingActive = false;
      
      // Update indicators
      updateTabTargetingIndicators();
      
      // Clean up widget gallery redirections
      cleanupWidgetGalleryRedirection();
    }
    
    // Clean up any tab grids associated with this container
    for (let i = 1; i <= 10; i++) {
      const gridKey = `${containerId}-${i}`;
      if (window.tabGrids[gridKey]) {
        // Remove event listeners
        if (window.tabGrids[gridKey]._gsEventHandler) {
          // Remove event handlers by their keys
          Object.keys(window.tabGrids[gridKey]._gsEventHandler).forEach(key => {
            window.tabGrids[gridKey].off(key);
          });
        }
        
        // Destroy the grid properly
        window.tabGrids[gridKey].destroy(false);
        
        // Delete the reference
        delete window.tabGrids[gridKey];
      }
    }
  }
  
  // Remove the widget from the grid
  if (window.grid) {
    window.grid.removeWidget(widgetElement);
  }
} 