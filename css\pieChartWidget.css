/* Pie Chart Widget Styles */
.pie-chart-widget {
  /* Inherit base widget styles */
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

/* Override specific styles for pie chart */
.pie-chart-widget .widget-body {
  flex: 1;
  min-height: 0;
  position: relative;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

.pie-chart-widget .chart-container {
  flex: 1;
  width: 100%;
  height: 100%;
  position: relative;
  min-height: 0;
  padding-right: 40px; /* Add padding for zoom controls */
}

/* Add cursor styles for draggable chart areas */
.pie-chart-widget .chart-container .am5-Layer {
  cursor: grab;
}

.pie-chart-widget .chart-container .am5-Layer:active {
  cursor: grabbing;
}

/* Ensure non-chart areas don't show grab cursor */
.pie-chart-widget .widget-header,
.pie-chart-widget .widget-actions {
  cursor: default;
}

/* Zoom Controls */
.pie-chart-widget .am5-zoomcontrol {
  position: absolute;
  right: 10px;
  top: 10px;
  z-index: 5;
}

.pie-chart-widget .am5-button {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 4px;
  margin: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pie-chart-widget .am5-button:hover {
  background-color: #f5f5f5;
}

/* Dark theme support */
[data-bs-theme="dark"] .pie-chart-widget {
  background: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .pie-chart-widget .widget-body {
    padding: 0.75rem;
  }
}

/* Chart Container Styles */
.chart-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
  position: relative;
}

/* Chart Settings Panel */
.chart-settings {
  background-color: var(--slate-grey-light);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.chart-settings label {
  color: var(--wns-black);
  font-weight: 500;
  margin-bottom: 0.375rem;
  font-size: 12px;
}

.chart-settings .form-select {
  border-color: var(--slate-grey);
  border-radius: 6px;
  padding: 0.375rem 0.75rem;
  font-size: 12px;
}

.chart-settings .form-range {
  height: 4px;
  border-radius: 2px;
}

.chart-settings .form-range::-webkit-slider-thumb {
  background: var(--emerald-green);
  width: 14px;
  height: 14px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-settings .form-range::-moz-range-thumb {
  background: var(--emerald-green);
  width: 14px;
  height: 14px;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-settings .form-check-input {
  width: 2rem;
  height: 1rem;
  border-radius: 0.5rem;
}

.chart-settings .form-check-input:checked {
  background-color: var(--emerald-green);
  border-color: var(--emerald-green);
}

.chart-settings .form-check-label {
  padding-left: 0.5rem;
  user-select: none;
  font-size: 12px;
}

/* Risk Score Colors */
.risk-score-5 {
  color: var(--high-risk);
}
.risk-score-4-5 {
  color: var(--risk-4-5);
}
.risk-score-4 {
  color: var(--risk-4-0);
}
.risk-score-3-5 {
  color: var(--risk-3-5);
}
.risk-score-3 {
  color: var(--moderate-risk);
}
.risk-score-2-5 {
  color: var(--risk-2-5);
}
.risk-score-2 {
  color: var(--risk-2-0);
}
.risk-score-1-5 {
  color: var(--risk-1-5);
}
.risk-score-1 {
  color: var(--low-risk);
}

/* Legend Styles */
.chart-legend {
  margin-top: 1rem;
  font-size: var(--font-size-base);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: var(--font-size-base);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-label {
  color: var(--text-primary);
  font-size: var(--font-size-base);
}

/* Widget Header */
.pie-chart-widget .widget-header {
  flex: 0 0 auto;
  padding: 1rem;
}

.pie-chart-widget .widget-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.pie-chart-widget .widget-header i {
  font-size: 16px;
  color: var(--ocean-teal);
}

.pie-chart-widget .widget-actions {
  display: flex;
  gap: 0.5rem;
}

.pie-chart-widget .widget-actions button {
  padding: 0.25rem;
  color: var(--text-secondary);
  transition: color 0.2s ease;
}

.pie-chart-widget .widget-actions button:hover {
  color: var(--primary-color);
}

/* Chart Widget Styles */
.chart-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.chart-title i {
  font-size: var(--font-size-md);
  color: var(--primary-color);
}

.chart-title span {
  font-size: var(--font-size-lg);
}

.chart-actions {
  display: flex;
  gap: 0.5rem;
}

.chart-actions button {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  font-size: var(--font-size-base);
  transition: color 0.2s ease;
}

.chart-actions button:hover {
  color: var(--primary-color);
}

.chart-body {
  flex: 1;
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Dark Theme Support */
body.dark-theme .chart-widget {
  background: var(--secondary-color);
}

body.dark-theme .chart-header {
  background: rgba(0, 0, 0, 0.1);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme .chart-title {
  color: white;
}

body.dark-theme .chart-actions button {
  color: rgba(255, 255, 255, 0.7);
}

body.dark-theme .chart-actions button:hover {
  color: white;
}

/* Widget Styles */
.widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
}

.widget-title i {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
}

.widget-actions {
  display: flex;
  gap: 0.5rem;
}

.widget-actions .btn-link {
  color: var(--text-secondary);
  padding: 0.25rem;
  font-size: var(--font-size-md);
  transition: color 0.2s ease;
}

.widget-actions .btn-link:hover {
  color: var(--primary-color);
}

.widget-body {
  flex: 1;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

/* Dark Theme Support */
body.dark-theme .widget {
  background: var(--secondary-color);
}

body.dark-theme .widget-header {
  background: rgba(0, 0, 0, 0.1);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme .widget-title {
  color: white;
}

body.dark-theme .widget-actions .btn-link {
  color: rgba(255, 255, 255, 0.7);
}

body.dark-theme .widget-actions .btn-link:hover {
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .widget-header {
    padding: 0.75rem;
  }

  .widget-title {
    font-size: var(--font-size-base);
  }

  .widget-title i {
    font-size: var(--font-size-md);
  }

  .widget-actions .btn-link {
    font-size: var(--font-size-sm);
  }
}
