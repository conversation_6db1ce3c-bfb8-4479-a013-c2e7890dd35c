/* Widget Sidebar Styles - Updated 2025-07-13 19:50 */
/* CACHE BUSTER: v1.2.3 - Smaller offcanvas width (280px), improved responsive behavior */
.widget-sidebar {
  position: fixed;
  left: 0;
  top: 90px;
  bottom: 0;
  width: 280px;
  background-color: #fff;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1030;
  transform: translateX(-100%);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow-y: auto;
  box-shadow: none;
}

.widget-sidebar.show {
  transform: translateX(0);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}

.widget-sidebar-toggle {
  position: fixed;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background-color: #00b19c;
  color: white;
  border: none;
  border-radius: 0 6px 6px 0;
  padding: 12px 10px;
  z-index: 1031;
  cursor: pointer;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 38px;
  height: 40px;
  overflow: visible;
}

.widget-sidebar-toggle:hover {
  background-color: #009688;
  width: 40px;
}

.widget-sidebar-toggle i {
  font-size: 18px;
}

.widget-sidebar-toggle .badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #ff5722;
  color: white;
  font-size: 10px;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 18px;
  line-height: 1;
  padding: 0;
  margin: 0;
  overflow: visible;
  font-weight: 600;
  text-align: center;
  z-index: 5;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.widget-sidebar-header {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.widget-sidebar-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.widget-sidebar-title i {
  margin-right: 8px;
  color: #00b19c;
  font-size: 18px;
}

.widget-sidebar-close {
  color: #777;
  cursor: pointer;
  font-size: 16px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.widget-sidebar-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.widget-sidebar-content {
  padding: 0;
}

.hidden-widgets-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.hidden-widget-item {
  padding: 14px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  position: relative;
}

.hidden-widget-item:hover {
  background-color: #f8f9fa;
}

.hidden-widget-item:last-child {
  border-bottom: none;
}

.hidden-widget-item.empty-message {
  color: #777;
  font-style: italic;
  cursor: default;
  justify-content: center;
  padding: 25px 20px;
}

.hidden-widget-icon {
  flex: 0 0 36px;
  height: 36px;
  margin-right: 12px;
  color: #00b19c;
  font-size: 18px;
  background-color: rgba(0, 177, 156, 0.08);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hidden-widget-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  flex-grow: 1;
  line-height: 1.4;
}

.hidden-widget-title small {
  display: block;
  color: #777;
  font-size: 12px;
  font-weight: normal;
  margin-top: 2px;
}

.hidden-widget-restore {
  width: 30px;
  height: 30px;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00b19c;
  font-size: 16px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
}

.hidden-widget-item:hover .hidden-widget-restore {
  opacity: 1;
  transform: translateX(0);
}

.hidden-widget-restore:hover {
  background-color: #00b19c;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 177, 156, 0.3);
}

.widget-sidebar-footer {
  padding: 15px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  text-align: center;
  color: #777;
  font-size: 12px;
  background-color: #f8f9fa;
}

.widget-sidebar-footer a {
  color: #00b19c;
  text-decoration: none;
}

.widget-sidebar-footer a:hover {
  text-decoration: underline;
}

/* Widget Types Icons */
.hidden-widget-icon.chart-pie {
  background-color: rgba(255, 87, 34, 0.08);
  color: #ff5722;
}

.hidden-widget-icon.chart-bar {
  background-color: rgba(63, 81, 181, 0.08);
  color: #3f51b5;
}

.hidden-widget-icon.chart-line {
  background-color: rgba(0, 150, 136, 0.08);
  color: #009688;
}

.hidden-widget-icon.table {
  background-color: rgba(33, 150, 243, 0.08);
  color: #2196f3;
}

.hidden-widget-icon.text {
  background-color: rgba(156, 39, 176, 0.08);
  color: #9c27b0;
}

/* Badge for number of widgets */
.widgets-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #00b19c;
  color: white;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  padding: 1px 8px;
  margin-left: 8px;
  min-width: 20px;
}

/* Dark Theme Support */
[data-bs-theme="dark"] .widget-sidebar {
  background-color: #22262a;
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .widget-sidebar-header {
  background-color: #1a1d21;
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .widget-sidebar-title {
  color: #e9ecef;
}

[data-bs-theme="dark"] .widget-sidebar-close {
  color: #adb5bd;
}

[data-bs-theme="dark"] .widget-sidebar-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #f8f9fa;
}

[data-bs-theme="dark"] .hidden-widget-item {
  border-color: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .hidden-widget-item:hover {
  background-color: #2c3135;
}

[data-bs-theme="dark"] .hidden-widget-item.empty-message {
  color: #adb5bd;
}

[data-bs-theme="dark"] .hidden-widget-title {
  color: #e9ecef;
}

[data-bs-theme="dark"] .hidden-widget-title small {
  color: #adb5bd;
}

[data-bs-theme="dark"] .hidden-widget-restore {
  background-color: #343a40;
}

[data-bs-theme="dark"] .widget-sidebar-footer {
  background-color: #1a1d21;
  border-color: rgba(255, 255, 255, 0.1);
  color: #adb5bd;
}

[data-bs-theme="dark"] .widget-sidebar-footer a {
  color: #00b19c;
}

/* Commodity News Widget Styles */
.commodity-news-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;
}

.commodity-news-widget > .card-body {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  padding-bottom: 0;
  overflow: hidden;
  min-height: 0;
  transition: all 0.3s ease;
}

.commodity-news-container {
  flex: 1 1 auto;
  overflow-y: auto;
  min-height: 0;
  padding: 10px;
  font-size: clamp(12px, 1vw, 14px);
  line-height: 1.4;
  transition: all 0.3s ease;
}

.commodity-news-container .news-item {
  font-size: clamp(11px, 0.9vw, 13px);
}

.commodity-news-container strong {
  font-size: clamp(13px, 1.1vw, 15px);
}

.commodity-news-widget .widget-header {
  flex-shrink: 0;
}

.commodity-news-widget .widget-footer {
  flex-shrink: 0;
  margin-top: auto;
  padding: 10px;
}

/* News group styles */
.news-group {
  display: flex;
  margin-bottom: 15px;
}

.news-date-container {
  flex-shrink: 0;
  width: 45px;
  height: 45px;
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  border: 1px solid;
}

.news-date {
  font-size: 18px;
  font-weight: 600;
  line-height: 1;
}

.news-year {
  font-size: 10px;
  line-height: 1;
}

/* News item styles */
.news-item {
  flex: 1;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 10px;
  margin-left: 5px;
}

.news-item-link {
  color: #333;
  text-decoration: none;
  display: block;
}

.news-item-link:hover {
  color: #00b19c;
}

.news-tags {
  display: flex;
  align-items: center;
  margin-top: 8px;
  color: #555;
  font-size: 12px;
}

.news-tags i {
  margin-right: 5px;
  font-size: 13px;
}

.news-tag {
  margin-left: 5px;
}

/* Dashboard Minimap Styles - ENHANCED & REFINED */
.dashboard-minimap {
  position: fixed;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  width: 170px; /* Reduced from 200px */
  max-height: 250px; /* Reduced from 300px */
  background: rgba(255, 255, 255, 0.92); /* Slightly more transparent */
  backdrop-filter: blur(12px);
  border: 1px solid rgba(0, 177, 156, 0.15); /* Use brand color */
  border-radius: 12px; /* Slightly smaller radius */
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08); /* Softer shadow */
  z-index: 1000;
  opacity: 0;
  transform: translateY(-50%) translateX(100%) scale(0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
  overflow: hidden;
  pointer-events: none; /* Initially non-interactive */
}

.dashboard-minimap.show {
  opacity: 1;
  transform: translateY(-50%) translateX(0) scale(1);
  pointer-events: auto;
}

/* Auto-hide after inactivity */
.dashboard-minimap.auto-hide {
  opacity: 0.3;
  transform: translateY(-50%) translateX(50%) scale(0.95);
  pointer-events: none;
}

.dashboard-minimap:hover {
  opacity: 1 !important;
  transform: translateY(-50%) translateX(0) scale(1) !important;
  pointer-events: auto !important;
  box-shadow: 0 6px 32px rgba(0, 0, 0, 0.12);
}

.minimap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 14px; /* Reduced padding */
  border-bottom: 1px solid rgba(0, 177, 156, 0.1);
  background: rgba(248, 249, 250, 0.7);
  backdrop-filter: blur(10px);
}

.minimap-header h6 {
  margin: 0;
  font-size: 11px; /* Smaller font */
  font-weight: 600;
  color: #00b19c; /* Brand color */
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.minimap-controls {
  display: flex;
  gap: 3px; /* Reduced gap */
}

.minimap-controls button {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 177, 156, 0.2);
  padding: 4px; /* Smaller padding */
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-size: 12px; /* Smaller icons */
  color: #6b7280;
  width: 24px; /* Smaller buttons */
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.minimap-controls button:hover {
  background: rgba(0, 177, 156, 0.1);
  border-color: rgba(0, 177, 156, 0.4);
  color: #00b19c;
  transform: scale(1.05);
}

.minimap-content {
  position: relative;
  padding: 12px; /* Reduced padding */
  max-height: 170px; /* Reduced height */
  overflow: hidden;
}

.minimap-canvas {
  display: block;
  border: 1px solid rgba(0, 177, 156, 0.15);
  border-radius: 6px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  width: 100%;
  height: 130px; /* Reduced height */
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.minimap-canvas:hover {
  border-color: #00b19c;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05),
    0 0 0 2px rgba(0, 177, 156, 0.15);
}

.minimap-viewport {
  position: absolute;
  left: 12px;
  border: 2px solid #00b19c; /* Brand color */
  background: rgba(0, 177, 156, 0.12);
  pointer-events: none;
  border-radius: 3px;
  z-index: 10;
  min-height: 6px;
  transition: all 0.1s ease;
  box-shadow: 0 1px 6px rgba(0, 177, 156, 0.25);
}

/* Minimap info display */
.minimap-info {
  position: absolute;
  bottom: 8px;
  left: 12px;
  right: 12px;
  text-align: center;
}

.widget-count {
  font-size: 9px;
  color: #6b7280;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 6px;
  border-radius: 8px;
  border: 1px solid rgba(0, 177, 156, 0.2);
  display: inline-block;
  font-weight: 500;
}

.minimap-toggle {
  position: fixed;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  width: 42px; /* Slightly smaller */
  height: 42px;
  background: linear-gradient(135deg, #00b19c 0%, #009688 100%);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px; /* Smaller icon */
  box-shadow: 0 3px 16px rgba(0, 177, 156, 0.25); /* Softer shadow */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.minimap-toggle:hover {
  transform: translateY(-50%) scale(1.08);
  box-shadow: 0 4px 20px rgba(0, 177, 156, 0.35);
  background: linear-gradient(135deg, #009688 0%, #00796b 100%);
}

.minimap-toggle:active {
  transform: translateY(-50%) scale(0.98);
}

/* Navigation Strip - ENHANCED & REFINED */
.dashboard-nav-strip {
  position: fixed;
  bottom: 16px; /* Closer to bottom */
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.92);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 177, 156, 0.15);
  border-radius: 12px;
  padding: 8px 16px; /* Reduced padding */
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 12px; /* Reduced gap */
  opacity: 0;
  transform: translateX(-50%) translateY(100%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 85vw; /* Better mobile constraint */
  overflow-x: auto;
  scrollbar-width: none; /* Hide scrollbar */
  -ms-overflow-style: none;
}

.dashboard-nav-strip::-webkit-scrollbar {
  display: none; /* Hide scrollbar */
}

.dashboard-nav-strip.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

/* Auto-hide behavior for nav strip */
.dashboard-nav-strip.auto-hide {
  opacity: 0.4;
  transform: translateX(-50%) translateY(80%);
}

.dashboard-nav-strip:hover {
  opacity: 1 !important;
  transform: translateX(-50%) translateY(0) !important;
}

.nav-strip-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px; /* Reduced gap */
  min-width: 50px; /* Smaller sections */
  cursor: pointer;
  padding: 6px; /* Reduced padding */
  border-radius: 8px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.nav-strip-section:hover {
  background: rgba(0, 177, 156, 0.08);
  transform: translateY(-1px);
}

.nav-strip-thumbnail {
  width: 36px; /* Smaller thumbnails */
  height: 24px;
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-radius: 4px;
  border: 1px solid rgba(0, 177, 156, 0.2);
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.nav-strip-thumbnail.current {
  border-color: #00b19c;
  box-shadow: 0 0 0 2px rgba(0, 177, 156, 0.2);
  background: linear-gradient(
    135deg,
    rgba(0, 177, 156, 0.1) 0%,
    rgba(0, 177, 156, 0.2) 100%
  );
}

.nav-strip-label {
  font-size: 9px; /* Smaller text */
  color: #6b7280;
  font-weight: 500;
  text-align: center;
  max-width: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-strip-controls {
  display: flex;
  gap: 6px; /* Reduced gap */
  margin-left: auto;
  padding-left: 12px; /* Reduced padding */
  border-left: 1px solid rgba(0, 177, 156, 0.15);
}

.nav-strip-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 177, 156, 0.2);
  padding: 6px; /* Smaller buttons */
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 12px;
  color: #6b7280;
  width: 32px; /* Smaller buttons */
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-strip-btn:hover {
  background: rgba(0, 177, 156, 0.1);
  border-color: rgba(0, 177, 156, 0.4);
  color: #00b19c;
  transform: scale(1.05);
}

/* Enhanced Auto-fit Mode */
body.auto-fit-mode {
  overflow: hidden;
}

body.auto-fit-mode #grid-container {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* Smoother transition */
  border-radius: 8px; /* Smaller radius */
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15); /* Softer shadow */
}

.auto-fit-hint {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(15px);
  color: white;
  padding: 20px 28px; /* Slightly smaller */
  border-radius: 12px;
  z-index: 10000;
  cursor: pointer;
  animation: fadeInScale 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 350px; /* Smaller max-width */
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.15);
  position: relative;
  overflow: hidden;
}

.auto-fit-hint .hint-content {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px; /* Smaller text */
  justify-content: center;
  font-weight: 500;
  position: relative;
  z-index: 2;
}

.hint-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: #00b19c;
  border-radius: 0 0 12px 12px;
  width: 100%;
  z-index: 1;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes progressShrink {
  0% {
    transform: scaleX(1);
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: scaleX(0);
    opacity: 0;
  }
}

/* Responsive improvements */
@media (max-width: 768px) {
  .dashboard-minimap {
    width: 150px; /* Even smaller on mobile */
    max-height: 220px;
    right: 12px;
  }

  .minimap-toggle {
    width: 38px;
    height: 38px;
    right: 12px;
    font-size: 14px;
  }

  .dashboard-nav-strip {
    bottom: 12px;
    padding: 6px 12px;
    max-width: 92vw;
    gap: 8px;
  }

  .nav-strip-section {
    min-width: 45px;
    padding: 4px;
  }

  .nav-strip-thumbnail {
    width: 32px;
    height: 20px;
  }

  .nav-strip-label {
    font-size: 8px;
    max-width: 45px;
  }

  .nav-strip-btn {
    width: 28px;
    height: 28px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .dashboard-minimap {
    width: 130px;
    max-height: 200px;
  }

  .minimap-content {
    padding: 10px;
  }

  .minimap-canvas {
    height: 110px;
  }

  .dashboard-nav-strip {
    padding: 6px 10px;
    gap: 6px;
  }
}

/* Dark theme support - ENHANCED */
[data-bs-theme="dark"] .dashboard-minimap {
  background: rgba(31, 41, 55, 0.92);
  border-color: rgba(0, 177, 156, 0.25);
}

[data-bs-theme="dark"] .minimap-header {
  background: rgba(17, 24, 39, 0.8);
  border-color: rgba(0, 177, 156, 0.2);
}

[data-bs-theme="dark"] .minimap-header h6 {
  color: #00e5cc; /* Brighter brand color for dark mode */
}

[data-bs-theme="dark"] .minimap-canvas {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  border-color: rgba(0, 177, 156, 0.25);
}

[data-bs-theme="dark"] .dashboard-nav-strip {
  background: rgba(31, 41, 55, 0.92);
  border-color: rgba(0, 177, 156, 0.25);
}

[data-bs-theme="dark"] .nav-strip-thumbnail {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
  border-color: rgba(0, 177, 156, 0.3);
}

[data-bs-theme="dark"] .nav-strip-thumbnail.current {
  border-color: #00e5cc;
  box-shadow: 0 0 0 2px rgba(0, 229, 204, 0.3);
  background: linear-gradient(
    135deg,
    rgba(0, 229, 204, 0.15) 0%,
    rgba(0, 229, 204, 0.25) 100%
  );
}

[data-bs-theme="dark"] .nav-strip-label {
  color: #9ca3af;
}

/* Performance optimizations */
.dashboard-minimap,
.dashboard-nav-strip {
  will-change: transform, opacity;
  contain: layout style paint;
}

.minimap-canvas {
  will-change: transform;
}

/* Improved accessibility */
.minimap-toggle:focus,
.nav-strip-btn:focus,
.minimap-controls button:focus {
  outline: 2px solid #00b19c;
  outline-offset: 2px;
}

/* Better scrollbar for nav strip on desktop */
@media (hover: hover) {
  .dashboard-nav-strip:hover::-webkit-scrollbar {
    display: block;
    height: 2px;
  }

  .dashboard-nav-strip:hover::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 1px;
  }

  .dashboard-nav-strip:hover::-webkit-scrollbar-thumb {
    background: rgba(0, 177, 156, 0.3);
    border-radius: 1px;
  }
}

/* Improved Drop Zone Styles */
.grid-stack-item.grid-stack-placeholder {
  background: linear-gradient(
    135deg,
    rgba(0, 177, 156, 0.1) 0%,
    rgba(0, 177, 156, 0.2) 100%
  ) !important;
  border: 2px dashed #00b19c !important;
  border-radius: 12px !important;
  backdrop-filter: blur(5px);
}

.grid-stack-item.ui-draggable-dragging {
  opacity: 0.9;
  transform: rotate(1deg) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  border-radius: 12px;
  transition: all 0.2s ease;
}

/* Better nested grid styling */
.grid-stack-nested {
  min-height: 120px;
  border: 2px dashed rgba(0, 177, 156, 0.3);
  border-radius: 12px;
  background: linear-gradient(
    135deg,
    rgba(0, 177, 156, 0.02) 0%,
    rgba(0, 177, 156, 0.05) 100%
  );
  transition: all 0.3s ease;
  position: relative;
}

.grid-stack-nested:hover,
.grid-stack-nested.ui-droppable-hover {
  border-color: rgba(0, 177, 156, 0.6);
  background: linear-gradient(
    135deg,
    rgba(0, 177, 156, 0.05) 0%,
    rgba(0, 177, 156, 0.1) 100%
  );
  transform: scale(1.01);
}

.grid-stack-nested:empty::before {
  content: "✨ Drop widgets here";
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 177, 156, 0.6);
  font-style: italic;
  font-weight: 500;
  height: 100%;
  min-height: 100px;
  font-size: 14px;
}

/* Dashboard Slide Navigator - POWERPOINT STYLE */
.widget-navigator-offcanvas {
  position: fixed;
  top: 54px;
  right: -260px; /* Changed from left: -260px to right: -260px */
  width: 260px; /* Reduced from 280px for more space */
  height: 100vh;
  background: var(--bs-body-bg, #ffffff);
  border-left: 1px solid var(--bs-border-color, #dee2e6); /* Changed from border-right to border-left */
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1); /* Changed shadow direction */
  transition: right 0.35s cubic-bezier(0.4, 0, 0.2, 1); /* Changed from left to right */
  z-index: 1050; /* Ensure it's above other content */
  overflow-y: auto;
  pointer-events: auto; /* Ensure it can receive clicks */
}

.widget-navigator-offcanvas.show {
  transform: translateX(0);
  right: 0; /* Changed from left: 0 to right: 0 */
}

body.navigator-open {
  overflow-x: hidden;
}

/* Updated approach: Use width reduction instead of translateX */
body.navigator-open #grid-container {
  width: 100%; /* Reduced from 280px to give more space */
  max-width: 100%;
  margin-left: 0;
  margin-right: 260px; /* Added margin-right to push content away from navigator */
  transition: width 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

body.navigator-open .container-fluid {
  width: 100%; /* Reduced from 280px to give more space */
  max-width: 100%;
  padding-left: 0.5rem; /* Reduced padding to maximize space */
  padding-right: 0.5rem;
  transition: width 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Restore essential dashboard-main container adjustment for navigator */
body.navigator-open #dashboard-main {
  width: calc(100% - 260px); /* Match the navigator width */
  max-width: calc(100% - 260px);
  margin-left: 0;
  margin-right: 260px; /* Added margin-right to push content away from navigator */
  transition: width 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Better grid container adaptation */
body.navigator-open .grid-stack {
  width: 100%;
  margin-left: 0;
  margin-right: 0;
  padding-left: 0.75rem; /* Reduced padding */
  padding-right: 0.75rem;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure col-12 also adapts properly */
body.navigator-open .col-12 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

/* Optimize grid item spacing when navigator is open */
body.navigator-open .grid-stack-item-content {
  margin: 0.25rem; /* Reduced margin for tighter layout */
}

/* Ensure grid items make better use of available space */
body.navigator-open .grid-stack-item {
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Better spacing for nested grids when navigator is open */
body.navigator-open .grid-stack-nested {
  padding: 0.5rem; /* Reduced padding */
}

/* Optimize row spacing */
body.navigator-open .row {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.widget-navigator-offcanvas {
  width: 260px; /* Reduced from 280px to match calculations */
  transition: transform 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

body.navigator-open .widget-navigator-offcanvas {
  box-shadow: -8px 0 32px rgba(0, 0, 0, 0.1); /* Negative X value for left shadow since panel is on right */
}

/* Improved responsive behavior */
@media (max-width: 1200px) {
  .widget-navigator-offcanvas {
    width: 240px; /* Slightly smaller on medium screens */
  }

  body.navigator-open #grid-container {
    width: calc(100% - 240px);
    max-width: calc(100% - 240px);
    margin-right: 240px; /* Updated margin-right */
  }

  body.navigator-open #dashboard-main {
    width: calc(100% - 240px);
    max-width: calc(100% - 240px);
    margin-right: 240px; /* Updated margin-right */
  }

  body.navigator-open .container-fluid {
    width: 100%;
    max-width: 100%;
  }
}

@media (max-width: 900px) {
  body.navigator-open #grid-container {
    width: 100%; /* Full width on mobile */
    max-width: 100%;
    margin-right: 0; /* Reset margin on mobile */
  }

  body.navigator-open #dashboard-main {
    width: 100%; /* Full width on mobile */
    max-width: 100%;
    margin-right: 0; /* Reset margin on mobile */
  }

  body.navigator-open .container-fluid {
    width: 100%;
    max-width: 100%;
  }

  body.navigator-open .grid-stack {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .widget-navigator-offcanvas {
    width: 100vw;
    min-width: 0;
    right: -100vw; /* Start completely off-screen on mobile */
  }

  .widget-navigator-offcanvas.show {
    right: 0; /* Slide in from right on mobile */
  }
}

@media (max-width: 600px) {
  .widget-navigator-offcanvas {
    width: 100vw;
    right: -100vw; /* Start completely off-screen */
  }

  .widget-navigator-offcanvas.show {
    right: 0; /* Slide in from right */
  }
}

/* Navigator Header */
.navigator-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid rgba(0, 177, 156, 0.1);
  background: rgba(0, 177, 156, 0.05);
}

.navigator-header h5 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #00b19c;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navigator-header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.navigator-refresh,
.navigator-close {
  background: none;
  border: none;
  padding: 0.5rem;
  margin-left: 0.25rem;
  border-radius: 0.25rem;
  color: var(--bs-secondary, #6c757d);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  transition: all 0.2s ease;
  font-size: 1.1rem;
}

.navigator-refresh:hover,
.navigator-close:hover {
  background: var(--bs-danger, #dc3545);
  color: white;
  transform: scale(1.05);
}

.navigator-content {
  height: calc(100vh - 80px);
  overflow-y: auto;
  padding: 0;
}

/* Search Section */
.navigator-search {
  position: relative;
  padding: 16px 20px; /* Reduced padding for smaller width */
  border-bottom: 1px solid rgba(0, 177, 156, 0.1);
}

.navigator-search-input {
  width: 100%;
  padding: 10px 14px 10px 36px; /* Reduced padding */
  border: 1px solid rgba(0, 177, 156, 0.2);
  border-radius: 10px; /* Slightly smaller radius */
  background: rgba(255, 255, 255, 0.9);
  font-size: 13px; /* Slightly smaller font */
  transition: all 0.2s ease;
}

.navigator-search-input:focus {
  outline: none;
  border-color: #00b19c;
  box-shadow: 0 0 0 3px rgba(0, 177, 156, 0.1);
  background: #fff;
}

.navigator-search i {
  position: absolute;
  left: 32px; /* Adjusted for smaller padding */
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  font-size: 14px; /* Smaller icon */
  pointer-events: none;
}

/* Stats Section */
.navigator-stats {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 12px 20px; /* Reduced padding */
  background: rgba(248, 249, 250, 0.5);
  border-bottom: 1px solid rgba(0, 177, 156, 0.1);
}

.widget-count-display {
  font-size: 13px; /* Slightly smaller */
  color: #6b7280;
  font-weight: 500;
}

/* Slide List */
.navigator-list {
  padding: 8px 0; /* Reduced padding */
}

.navigator-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px; /* Reduced padding */
  color: #6b7280;
  text-align: center;
}

.navigator-empty i {
  font-size: 36px; /* Smaller icon */
  margin-bottom: 12px; /* Reduced margin */
  opacity: 0.5;
}

.navigator-empty p {
  margin: 0;
  font-size: 14px; /* Smaller font */
  font-weight: 500;
}

/* --- SLIDE NAVIGATOR: PowerPoint-Style Card Look --- */
.slide-navigator-item {
  display: flex;
  align-items: center;
  gap: 12px; /* Reduced gap */
  padding: 12px 18px; /* Reduced padding */
  margin: 8px 14px; /* Reduced margin */
  background: #fff;
  border-radius: 8px; /* Smaller radius */
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05); /* Softer shadow */
  cursor: pointer;
  transition: box-shadow 0.18s, background 0.18s, transform 0.18s;
  border: 1.5px solid transparent;
  position: relative;
}

.slide-navigator-item:hover {
  background: #f0fdfa;
  box-shadow: 0 3px 12px rgba(0, 177, 156, 0.08); /* Softer shadow */
  border-color: #00b19c33;
  transform: translateY(-1px) scale(1.005); /* Subtle transform */
}

.slide-navigator-item.active,
.slide-navigator-item.current-slide {
  background: rgba(0, 177, 156, 0.08);
  border-color: #00b19c;
  box-shadow: 0 4px 16px rgba(0, 177, 156, 0.1); /* Softer shadow */
  transform: scale(1.01); /* Subtle scale */
}

.slide-title {
  flex: 1;
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.slide-navigator-item.active .slide-title,
.slide-navigator-item.current-slide .slide-title {
  color: #00b19c;
  font-weight: 700;
}

.slide-indicator {
  width: 8px; /* Smaller */
  height: 8px;
  background: #00b19c;
  border-radius: 50%;
  margin-left: 8px; /* Reduced margin */
  opacity: 0;
  transition: opacity 0.18s;
}

.slide-navigator-item.active .slide-indicator,
.slide-navigator-item.current-slide .slide-indicator {
  opacity: 1;
}

/* Panel background and shadow */
.widget-navigator-offcanvas {
  background: #f8fafc;
  box-shadow: -6px 0 24px rgba(0, 0, 0, 0.08); /* Softer shadow */
}

/* Section count and search bar grouping */
.navigator-stats {
  background: transparent;
  border-bottom: none;
  padding-bottom: 0;
  margin-bottom: -4px; /* Reduced margin */
}

.widget-count-display {
  font-size: 13px; /* Smaller font */
  color: #00b19c;
  font-weight: 700;
  letter-spacing: 0.02em;
}

/* Enhanced grid container responsiveness */
body.navigator-open .container-fluid {
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

body.navigator-open .grid-stack-item {
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth transitions for all grid elements */
@media (min-width: 901px) {
  body.navigator-open .grid-stack-item-content {
    transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Navigator Toggle Button */
.widget-navigator-toggle {
  position: fixed;
  top: 50%;
  right: 20px; /* Keep toggle button on the right side */
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #00b19c 0%, #009688 100%);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  box-shadow: 0 4px 20px rgba(0, 177, 156, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1040;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.widget-navigator-toggle:hover {
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 6px 25px rgba(0, 177, 156, 0.4);
  background: linear-gradient(135deg, #009688 0%, #00796b 100%);
}

.widget-navigator-toggle:active {
  transform: translateY(-50%) scale(0.95);
}

.navigator-badge {
  display: none !important;
  position: absolute;
  top: -6px;
  right: -6px;
  background: #ff5722;
  color: white;
  font-size: 10px;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
  font-weight: 600;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .widget-navigator-offcanvas {
    width: 100vw;
    border-left: none;
  }

  .navigator-header {
    padding: 14px 18px; /* Reduced padding */
  }

  .navigator-search {
    padding: 14px 18px; /* Reduced padding */
  }

  .navigator-stats {
    padding: 10px 18px; /* Reduced padding */
  }

  .slide-navigator-item {
    padding: 10px 16px; /* Reduced padding */
    margin: 6px 12px; /* Reduced margin */
    gap: 10px; /* Reduced gap */
  }

  .slide-title {
    font-size: 12px; /* Smaller font */
  }

  .widget-navigator-toggle {
    right: 16px;
    width: 46px;
    height: 46px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .navigator-header h5 {
    font-size: 14px; /* Smaller font */
  }

  .slide-title {
    font-size: 11px; /* Even smaller on very small screens */
  }

  .navigator-search {
    padding: 12px 16px; /* Further reduced padding */
  }

  .navigator-stats {
    padding: 8px 16px; /* Further reduced padding */
  }

  .slide-navigator-item {
    padding: 8px 14px; /* Further reduced padding */
    margin: 4px 10px; /* Further reduced margin */
  }
}

/* Dark Theme Support */
[data-bs-theme="dark"] .widget-navigator-offcanvas {
  background: rgba(31, 41, 55, 0.98);
  border-color: rgba(0, 177, 156, 0.25);
}

[data-bs-theme="dark"] .navigator-header {
  background: rgba(0, 177, 156, 0.08);
  border-color: rgba(0, 177, 156, 0.2);
}

[data-bs-theme="dark"] .navigator-header h5 {
  color: #00e5cc;
}

[data-bs-theme="dark"] .navigator-search-input {
  background: rgba(55, 65, 81, 0.9);
  border-color: rgba(0, 177, 156, 0.3);
  color: #f3f4f6;
}

[data-bs-theme="dark"] .navigator-search-input:focus {
  background: #374151;
  border-color: #00e5cc;
}

[data-bs-theme="dark"] .navigator-stats {
  background: rgba(17, 24, 39, 0.5);
  border-color: rgba(0, 177, 156, 0.2);
}

[data-bs-theme="dark"] .widget-count-display {
  color: #9ca3af;
}

[data-bs-theme="dark"] .slide-navigator-item {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(255, 255, 255, 0.05);
}

[data-bs-theme="dark"] .slide-navigator-item:hover {
  background: rgba(0, 229, 204, 0.08);
}

[data-bs-theme="dark"] .slide-navigator-item.active,
[data-bs-theme="dark"] .slide-navigator-item.current-slide {
  background: rgba(0, 229, 204, 0.15);
  border-color: #00e5cc;
}

[data-bs-theme="dark"] .slide-title {
  color: #f3f4f6;
}

[data-bs-theme="dark"] .slide-navigator-item.active .slide-title,
[data-bs-theme="dark"] .slide-navigator-item.current-slide .slide-title {
  color: #00e5cc;
}

/* Performance optimizations */
.widget-navigator-offcanvas {
  will-change: transform;
  contain: layout style paint;
}

.slide-navigator-item {
  will-change: transform, background-color;
}

/* Accessibility */
.widget-navigator-toggle:focus,
.navigator-close:focus,
.auto-fit-btn:focus {
  outline: 2px solid #00b19c;
  outline-offset: 2px;
}

/* Scrollbar styling */
.navigator-content::-webkit-scrollbar {
  width: 6px;
}

.navigator-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

.navigator-content::-webkit-scrollbar-thumb {
  background: rgba(0, 177, 156, 0.3);
  border-radius: 3px;
}

.navigator-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 177, 156, 0.5);
}
