<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab System Demo - Digital Asset Dashboard</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Line Awesome Icons -->
    <link rel="stylesheet" href="https://maxst.icons8.com/vue-static/line-icons/line-icons.css">
    
    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            padding: 20px;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .demo-header {
            background: #007bff;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .demo-content {
            padding: 30px;
        }
        
        .section-container-widget {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            min-height: 400px;
            background: #f8f9fa;
        }
        
        .widget-header {
            background: #e9ecef;
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
        }
        
        .nested-grid-container {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            min-height: 300px;
            padding: 20px;
        }
        
        .demo-controls {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .btn-group {
            margin: 10px 5px;
        }
        
        .code-example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .tab-system-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- Demo Header -->
        <div class="demo-header">
            <h1><i class="las la-layer-group"></i> Tab System Demo</h1>
            <p class="mb-0">Horizontal Tab Strip with Icon Indicators for Section Containers</p>
        </div>
        
        <!-- Demo Controls -->
        <div class="demo-controls">
            <h4>Demo Controls</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="createDemoSection()">
                            <i class="las la-plus"></i> Create Demo Section
                        </button>
                        <button class="btn btn-success" onclick="addTabToSection()">
                            <i class="las la-plus"></i> Add Tab
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="btn-group">
                        <button class="btn btn-info" onclick="showTabInfo()">
                            <i class="las la-info-circle"></i> Show Tab Info
                        </button>
                        <button class="btn btn-warning" onclick="clearDemo()">
                            <i class="las la-trash"></i> Clear Demo
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Demo Content -->
        <div class="demo-content">
            <div class="row">
                <div class="col-md-8">
                    <!-- Demo Section Container -->
                    <div class="demo-section">
                        <h4>Section Container with Tabs</h4>
                        <div id="demo-section-container" class="section-container-widget">
                            <div class="widget-header">
                                <i class="las la-layer-group"></i> Demo Section
                            </div>
                            <div class="nested-grid-container">
                                <p class="text-muted text-center">
                                    <i class="las la-plus-circle" style="font-size: 48px; color: #adb5bd;"></i><br>
                                    Click "Create Demo Section" to initialize the tab system
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <!-- Features List -->
                    <div class="demo-section">
                        <h4>Tab System Features</h4>
                        <ul class="feature-list">
                            <li>Horizontal tab strip with icons</li>
                            <li>Tab switching and management</li>
                            <li>Add/remove tabs dynamically</li>
                            <li>Double-click to rename tabs</li>
                            <li>Keyboard navigation support</li>
                            <li>Responsive design</li>
                            <li>Accessibility features</li>
                            <li>Custom tab content</li>
                            <li>Event system for integration</li>
                            <li>Auto-initialization</li>
                        </ul>
                    </div>
                    
                    <!-- Usage Examples -->
                    <div class="demo-section">
                        <h4>Quick Usage</h4>
                        <div class="code-example">
// Initialize tab system
const tabSystem = initTabSystem(container, {
    defaultTabName: 'New Tab',
    maxTabs: 8,
    showIcons: true
});

// Add a tab
tabSystem.addTab('Analytics', 'las la-chart-line');

// Get active tab
const activeTab = tabSystem.getActiveTab();
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Integration Info -->
        <div class="tab-system-info">
            <h4><i class="las la-info-circle"></i> Integration Information</h4>
            <p><strong>File:</strong> <code>js/tabSystem.js</code></p>
            <p><strong>Include in HTML:</strong> <code>&lt;script src="js/tabSystem.js"&gt;&lt;/script&gt;</code></p>
            <p><strong>Auto-initialization:</strong> Automatically initializes for existing section containers</p>
            <p><strong>Global functions:</strong> Available as <code>window.initTabSystem()</code>, <code>window.createSectionWithTabs()</code></p>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="js/tabSystem.js"></script>
    <script>
        let demoSection = null;
        let demoTabSystem = null;
        
        // Create demo section with tabs
        function createDemoSection() {
            const container = document.getElementById('demo-section-container');
            
            // Clear existing content
            container.innerHTML = '';
            
            // Create section header
            const header = document.createElement('div');
            header.className = 'widget-header mb-2 fw-bold d-flex justify-content-between align-items-center';
            header.innerHTML = `
                <div>
                    <i class="las la-layer-group"></i> Demo Section with Tabs
                </div>
                <div>
                    <button class="btn btn-sm btn-link text-dark ms-1" onclick="clearDemo()">
                        <i class="las la-times"></i>
                    </button>
                </div>
            `;
            
            // Create nested grid container
            const nestedGridContainer = document.createElement('div');
            nestedGridContainer.className = 'nested-grid-container';
            nestedGridContainer.style.cssText = 'height: calc(100% - 40px); overflow: hidden;';
            
            // Assemble section
            container.appendChild(header);
            container.appendChild(nestedGridContainer);
            
            // Initialize tab system
            demoSection = container;
            demoTabSystem = initTabSystem(container, {
                defaultTabName: 'Tab',
                maxTabs: 8,
                allowReorder: true,
                showIcons: true
            });
            
            // Add some demo tabs
            demoTabSystem.addTab('Main', 'las la-home');
            demoTabSystem.addTab('Data', 'las la-table');
            demoTabSystem.addTab('Charts', 'las la-chart-line');
            
            // Add custom content to tabs
            demoTabSystem.setTabContent('tab_1_1', `
                <div class="text-center">
                    <h4><i class="las la-home"></i> Main Tab</h4>
                    <p>This is the main tab content. You can add any HTML content here.</p>
                    <div class="alert alert-info">
                        <i class="las la-info-circle"></i> 
                        This tab system integrates seamlessly with your existing dashboard structure.
                    </div>
                </div>
            `);
            
            demoTabSystem.setTabContent('tab_2_1', `
                <div class="text-center">
                    <h4><i class="las la-table"></i> Data Tab</h4>
                    <p>This tab can contain data tables, forms, or any data-related content.</p>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Column 1</th>
                                    <th>Column 2</th>
                                    <th>Column 3</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td>Data 1</td><td>Data 2</td><td>Data 3</td></tr>
                                <tr><td>Data 4</td><td>Data 5</td><td>Data 6</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `);
            
            demoTabSystem.setTabContent('tab_3_1', `
                <div class="text-center">
                    <h4><i class="las la-chart-line"></i> Charts Tab</h4>
                    <p>This tab is perfect for charts, graphs, and visualizations.</p>
                    <div class="alert alert-success">
                        <i class="las la-chart-area"></i> 
                        You can integrate AmCharts, Chart.js, or any charting library here.
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="las la-chart-bar" style="font-size: 48px; color: #28a745;"></i>
                                    <h5>Bar Chart</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body text-center">
                                    <i class="las la-chart-pie" style="font-size: 48px; color: #007bff;"></i>
                                    <h5>Pie Chart</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `);
            
            console.log('Demo section created with tab system');
        }
        
        // Add a new tab to the demo section
        function addTabToSection() {
            if (!demoTabSystem) {
                alert('Please create a demo section first!');
                return;
            }
            
            const tabNames = ['Analytics', 'Reports', 'Settings', 'Help', 'About'];
            const tabIcons = ['las la-chart-line', 'las la-file-alt', 'las la-cog', 'las la-question-circle', 'las la-info-circle'];
            
            const randomIndex = Math.floor(Math.random() * tabNames.length);
            const tabName = tabNames[randomIndex];
            const tabIcon = tabIcons[randomIndex];
            
            const tabId = demoTabSystem.addTab(tabName, tabIcon);
            
            if (tabId) {
                demoTabSystem.setTabContent(tabId, `
                    <div class="text-center">
                        <h4><i class="${tabIcon}"></i> ${tabName}</h4>
                        <p>This is a dynamically added tab with custom content.</p>
                        <div class="alert alert-warning">
                            <i class="las la-lightbulb"></i> 
                            You can add any content to this tab - widgets, charts, tables, or custom HTML.
                        </div>
                    </div>
                `);
                
                console.log(`Added new tab: ${tabName}`);
            }
        }
        
        // Show information about the current tab system
        function showTabInfo() {
            if (!demoTabSystem) {
                alert('Please create a demo section first!');
                return;
            }
            
            const allTabs = demoTabSystem.getAllTabs();
            const activeTab = demoTabSystem.getActiveTab();
            
            let info = `Tab System Information:\n\n`;
            info += `Total Tabs: ${allTabs.length}\n`;
            info += `Active Tab: ${activeTab ? activeTab.name : 'None'}\n\n`;
            info += `All Tabs:\n`;
            
            allTabs.forEach((tab, index) => {
                info += `${index + 1}. ${tab.name} (${tab.icon}) - ${tab.isActive ? 'Active' : 'Inactive'}\n`;
            });
            
            console.log(info);
            alert(info);
        }
        
        // Clear the demo
        function clearDemo() {
            if (demoTabSystem) {
                demoTabSystem.destroy();
                demoTabSystem = null;
            }
            
            const container = document.getElementById('demo-section-container');
            container.innerHTML = `
                <div class="widget-header">
                    <i class="las la-layer-group"></i> Demo Section
                </div>
                <div class="nested-grid-container">
                    <p class="text-muted text-center">
                        <i class="las la-plus-circle" style="font-size: 48px; color: #adb5bd;"></i><br>
                        Click "Create Demo Section" to initialize the tab system
                    </p>
                </div>
            `;
            
            demoSection = null;
            console.log('Demo cleared');
        }
        
        // Initialize demo when page loads
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Tab System Demo loaded successfully!');
            console.log('Available functions: createDemoSection(), addTabToSection(), showTabInfo(), clearDemo()');
        });
    </script>
</body>
</html>
