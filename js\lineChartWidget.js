// Add a line chart widget using amCharts v5
function addLineChartWidget() {
  console.log("Adding line chart widget");
  const chartId = "linechart-" + Date.now();

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 4,
    content: `
      <div class="line-chart-widget p-2">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
          Line Chart
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#lineChartSettings"
                    aria-controls="lineChartSettings"
                    onclick="initLineChartSettings('${chartId}')">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div id="${chartId}" class="chart-container"></div>
      </div>
    `,
  });

  // Initialize the chart with a slight delay
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing chart");
      window.initLineChart(chartId);
    } catch (error) {
      console.error("Error initializing chart:", error);
    }
  }, 1000);

  return widget;
}

// Function to handle backdrop cleanup
function handleBackdropCleanup() {
  // Remove all backdrops
  const backdrops = document.querySelectorAll(".offcanvas-backdrop");
  backdrops.forEach((backdrop) => {
    backdrop.remove();
  });
}

// Function to initialize line chart settings
function initLineChartSettings(chartId) {
  const settingsPanel = document.getElementById("lineChartSettings");
  if (!settingsPanel) return;

  // Store the current chart ID
  settingsPanel.dataset.currentChart = chartId;

  // Initialize offcanvas with proper options
  const bsOffcanvas = new bootstrap.Offcanvas(settingsPanel, {
    backdrop: true,
    keyboard: true,
    scroll: false,
  });

  // Remove any existing event listeners
  settingsPanel.removeEventListener(
    "hidden.bs.offcanvas",
    handleBackdropCleanup
  );
  // Add event listener for when offcanvas is hidden
  settingsPanel.addEventListener("hidden.bs.offcanvas", handleBackdropCleanup);

  // Show the offcanvas
  bsOffcanvas.show();
}

// Function to apply line chart settings
function applyLineChartSettings() {
  const settingsPanel = document.getElementById("lineChartSettings");
  const chartId = settingsPanel.dataset.currentChart;
  if (!chartId) return;

  const chart = document.getElementById(chartId)?.chart;
  if (!chart) return;

  // Get settings values
  const chartTitle = document.getElementById(
    "lineChartSettings-chartTitle"
  ).value;
  const yAxisTitle = document.getElementById(
    "lineChartSettings-yAxisTitle"
  ).value;
  const showLegend = document.getElementById(
    "lineChartSettings-legend"
  ).checked;
  const activeSeriesIndex = parseInt(
    document.getElementById("lineChartSettings-activeSeries").value
  );

  // Get chart elements
  const yAxis = chart.yAxes.getIndex(0);
  const series1 = chart.series.getIndex(0);
  const series2 = chart.series.getIndex(1);
  const series3 = chart.series.getIndex(2);

  // Update chart title
  // Find existing title or create a new one
  let chartTitleLabel = null;

  // First try to find the title in the chart's parent container
  const chartContainer = chart.parent;
  if (chartContainer) {
    chartContainer.children.each(function (child) {
      if (child.isType("Label") && child.get("role") === "title") {
        chartTitleLabel = child;
        return false; // Break the loop
      }
    });
  }

  // If not found, try to find it in the chart itself (for backward compatibility)
  if (!chartTitleLabel) {
    chart.children.each(function (child) {
      if (child.isType("Label") && child.get("role") === "title") {
        chartTitleLabel = child;
        return false; // Break the loop
      }
    });
  }

  if (chartTitleLabel) {
    chartTitleLabel.set("text", chartTitle);
  } else {
    // If no title found, create a new one in the chart's parent container if available
    if (chartContainer) {
      chartTitleLabel = chartContainer.children.unshift(
        am5.Label.new(chart.root, {
          text: chartTitle,
          fontSize: 12,
          fontWeight: "500",
          textAlign: "center",
          x: am5.p50,
          centerX: am5.p50,
          paddingTop: 5,
          paddingBottom: 10,
          role: "title",
        })
      );
    } else {
      // Fallback to adding title directly to the chart
      chartTitleLabel = chart.children.push(
        am5.Label.new(chart.root, {
          text: chartTitle,
          fontSize: 12,
          fontWeight: "500",
          textAlign: "center",
          x: am5.p50,
          centerX: am5.p50,
          paddingTop: 10,
          paddingBottom: 10,
          role: "title",
        })
      );
    }
  }

  // Update Y-Axis title
  if (yAxis) {
    // Find the existing title label
    let yAxisTitleLabel = null;
    yAxis.children.each(function (child) {
      if (child.isType("Label")) {
        yAxisTitleLabel = child;
        return false; // Break the loop
      }
    });

    // Update the title text if found, otherwise create a new one
    if (yAxisTitleLabel) {
      yAxisTitleLabel.set("text", yAxisTitle);
    } else {
      yAxis.children.unshift(
        am5.Label.new(chart.root, {
          rotation: -90,
          text: yAxisTitle,
          y: am5.p50,
          centerX: am5.p50,
          fill: am5.color(0x000000),
          fontSize: 12,
          fontWeight: "500",
          paddingLeft: 10,
        })
      );
    }
  }

  // Toggle legend
  let legend = null;

  // First try to find the legend in the chart's parent container
  // We already have chartContainer from above, no need to redeclare
  if (chartContainer) {
    chartContainer.children.each(function (child) {
      if (child.isType("Legend")) {
        legend = child;
        return false; // Break the loop
      }
    });
  }

  // If not found, try to find it in the chart itself (for backward compatibility)
  if (!legend) {
    chart.children.each(function (child) {
      if (child.isType("Legend")) {
        legend = child;
        return false; // Break the loop
      }
    });
  }

  if (legend) {
    legend.set("forceHidden", !showLegend);
  }

  // Update series visibility based on active series selection
  if (series1) {
    series1.set("visible", activeSeriesIndex === 0);
  }

  if (series2) {
    series2.set("visible", activeSeriesIndex === 1);
  }

  if (series3) {
    series3.set("visible", activeSeriesIndex === 2);
  }

  // Update Y-axis title based on active series
  const seriesTitles = ["Revenue ($)", "Expenses ($)", "Profit ($)"];
  if (yAxis) {
    // Find the existing title label
    let yAxisTitleLabel = null;
    yAxis.children.each(function (child) {
      if (child.isType("Label")) {
        yAxisTitleLabel = child;
        return false; // Break the loop
      }
    });

    // Update the title text if found
    if (yAxisTitleLabel) {
      // If user has entered a custom title, use that, otherwise use the series title
      if (
        yAxisTitle !== seriesTitles[0] &&
        yAxisTitle !== seriesTitles[1] &&
        yAxisTitle !== seriesTitles[2]
      ) {
        yAxisTitleLabel.set("text", yAxisTitle);
      } else {
        yAxisTitleLabel.set("text", seriesTitles[activeSeriesIndex]);
        // Update the input field to reflect the change
        document.getElementById("lineChartSettings-yAxisTitle").value =
          seriesTitles[activeSeriesIndex];
      }
    }
  }

  // Update the legend to reflect any changes
  if (legend) {
    legend.data.clear();

    // Only include the active series in the legend
    const activeSeries = chart.series.getIndex(activeSeriesIndex);
    if (activeSeries) {
      legend.data.setAll([activeSeries]);
    }
  }

  // Update chart data from the table
  updateChartData(chart);

  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(settingsPanel);
  if (offcanvas) {
    offcanvas.hide();
  }
}

// Initialize a line chart using amCharts v5
window.initLineChart = function (containerId) {
  console.log("Starting line chart initialization for container:", containerId);
  const container = document.getElementById(containerId);

  if (!container) {
    console.error("Chart container not found:", containerId);
    return;
  }

  // Dispose previous amCharts root if it exists
  if (container.am5root) {
    try {
      container.am5root.dispose();
    } catch (e) {
      console.warn("Error disposing previous am5root:", e);
    }
    container.am5root = null;
  }

  // Make sure the container has dimensions
  if (container.offsetWidth === 0 || container.offsetHeight === 0) {
    console.warn("Chart container has zero dimensions, setting explicit size");
    container.style.width = "100%";
    container.style.height = "300px";
  }

  console.log(
    "Container dimensions:",
    container.offsetWidth,
    "x",
    container.offsetHeight
  );

  // Create root element
  const root = am5.Root.new(containerId);
  container.am5root = root;

  // Set themes
  root.setThemes([am5themes_Animated.new(root)]);

  // Create a container for the chart and title
  const chartContainer = root.container.children.push(
    am5.Container.new(root, {
      width: am5.p100,
      height: am5.p100,
      layout: root.verticalLayout,
    })
  );

  // Add chart title at the top of the container
  chartContainer.children.push(
    am5.Label.new(root, {
      text: "Financial Performance",
      fontSize: 12,
      fontWeight: "500",
      textAlign: "center",
      x: am5.p50,
      centerX: am5.p50,
      paddingTop: 5,
      paddingBottom: 10,
      role: "title", // Used to identify this as the chart title
    })
  );

  // Create chart inside the container
  const chart = chartContainer.children.push(
    am5xy.XYChart.new(root, {
      panX: true,
      panY: true,
      wheelX: "panX",
      wheelY: "zoomX",
      pinchZoomX: true,
      paddingTop: 10,
      paddingBottom: 20, // Normal padding since legend is now outside the chart
      paddingLeft: 10,
      paddingRight: 20,
      width: am5.p100,
      height: am5.p100,
    })
  );

  // Add cursor
  const cursor = chart.set(
    "cursor",
    am5xy.XYCursor.new(root, {
      behavior: "none",
    })
  );
  cursor.lineY.set("visible", false);

  // Create axes
  const xAxis = chart.xAxes.push(
    am5xy.DateAxis.new(root, {
      baseInterval: { timeUnit: "day", count: 1 },
      renderer: am5xy.AxisRendererX.new(root, {}),
      tooltip: am5.Tooltip.new(root, {}),
    })
  );

  const yAxis = chart.yAxes.push(
    am5xy.ValueAxis.new(root, {
      renderer: am5xy.AxisRendererY.new(root, {}),
    })
  );

  // Add title to y-axis
  yAxis.children.unshift(
    am5.Label.new(root, {
      rotation: -90,
      text: "Revenue ($)",
      y: am5.p50,
      centerX: am5.p50,
      fill: am5.color(0x000000),
      fontSize: 12,
      fontWeight: "500",
      paddingLeft: 10,
    })
  );

  // Add series
  const series = chart.series.push(
    am5xy.LineSeries.new(root, {
      name: "Revenue",
      xAxis: xAxis,
      yAxis: yAxis,
      valueYField: "value",
      valueXField: "date",
      tooltip: am5.Tooltip.new(root, {
        labelText: "{valueY}",
      }),
    })
  );

  // Set up data processor to parse dates
  series.data.processor = am5.DataProcessor.new(root, {
    dateFormat: "yyyy-MM-dd",
    dateFields: ["date"],
  });

  // Configure series
  series.set("strokeWidth", 2);
  series.set("fillOpacity", 0.2);

  // Set up a color adapter for the stroke
  series.strokes.template.adapters.add("stroke", () => {
    // Use the first brand color for the line
    return am5.color(window.chartConfig.brandColors[0]);
  });

  // Set the fill color directly
  series.set("fill", am5.color(window.chartConfig.brandColors[0]));
  // Set the fill opacity separately
  series.set("fillOpacity", 0.3);

  // Add bullets
  series.bullets.push(function () {
    return am5.Bullet.new(root, {
      sprite: am5.Circle.new(root, {
        radius: 5,
        fill: am5.color(window.chartConfig.brandColors[0]),
        stroke: root.interfaceColors.get("background"),
        strokeWidth: 2,
      }),
    });
  });

  // Generate demo data for multiple series
  const date = new Date();
  date.setHours(0, 0, 0, 0);

  // First series data (Revenue)
  const data1 = [];
  let value1 = 1000;

  // Second series data (Expenses)
  const data2 = [];
  let value2 = 800;

  // Third series data (Profit)
  const data3 = [];
  let value3 = 200;

  for (let i = 0; i < 30; i++) {
    // Generate random values for each series
    value1 = Math.round(Math.random() * 20 - 5 + value1);
    if (value1 < 0) value1 = Math.abs(value1);

    value2 = Math.round(Math.random() * 15 - 5 + value2);
    if (value2 < 0) value2 = Math.abs(value2);

    value3 = value1 - value2; // Profit = Revenue - Expenses

    const currentDate = new Date(date.getTime()).toISOString().split("T")[0];

    data1.push({
      date: currentDate,
      value: value1,
    });

    data2.push({
      date: currentDate,
      value: value2,
    });

    data3.push({
      date: currentDate,
      value: value3,
    });

    date.setDate(date.getDate() + 1);
  }

  // Set data for the first series
  series.data.setAll(data1);

  // Create and configure second series (Expenses) - initially hidden
  const series2 = chart.series.push(
    am5xy.LineSeries.new(root, {
      name: "Expenses",
      xAxis: xAxis,
      yAxis: yAxis,
      valueYField: "value",
      valueXField: "date",
      tooltip: am5.Tooltip.new(root, {
        labelText: "{valueY}",
      }),
      visible: false, // Initially hidden
    })
  );

  // Set up data processor for second series
  series2.data.processor = am5.DataProcessor.new(root, {
    dateFormat: "yyyy-MM-dd",
    dateFields: ["date"],
  });

  // Configure second series
  series2.set("strokeWidth", 2);
  series2.set("fillOpacity", 0.3);

  // Set up a color adapter for the stroke of second series
  series2.strokes.template.adapters.add("stroke", () => {
    // Use the second brand color for the line
    return am5.color(window.chartConfig.brandColors[1]);
  });

  // Set the fill color directly for second series
  series2.set("fill", am5.color(window.chartConfig.brandColors[1]));

  // Add bullets to second series
  series2.bullets.push(function () {
    return am5.Bullet.new(root, {
      sprite: am5.Circle.new(root, {
        radius: 5,
        fill: am5.color(window.chartConfig.brandColors[1]),
        stroke: root.interfaceColors.get("background"),
        strokeWidth: 2,
      }),
    });
  });

  // Set data for the second series
  series2.data.setAll(data2);

  // Create and configure third series (Profit) - initially hidden
  const series3 = chart.series.push(
    am5xy.LineSeries.new(root, {
      name: "Profit",
      xAxis: xAxis,
      yAxis: yAxis,
      valueYField: "value",
      valueXField: "date",
      tooltip: am5.Tooltip.new(root, {
        labelText: "{valueY}",
      }),
      visible: false, // Initially hidden
    })
  );

  // Set up data processor for third series
  series3.data.processor = am5.DataProcessor.new(root, {
    dateFormat: "yyyy-MM-dd",
    dateFields: ["date"],
  });

  // Configure third series
  series3.set("strokeWidth", 2);
  series3.set("fillOpacity", 0.3);

  // Set up a color adapter for the stroke of third series
  series3.strokes.template.adapters.add("stroke", () => {
    // Use the third brand color for the line
    return am5.color(window.chartConfig.brandColors[2]);
  });

  // Set the fill color directly for third series
  series3.set("fill", am5.color(window.chartConfig.brandColors[2]));

  // Add bullets to third series
  series3.bullets.push(function () {
    return am5.Bullet.new(root, {
      sprite: am5.Circle.new(root, {
        radius: 5,
        fill: am5.color(window.chartConfig.brandColors[2]),
        stroke: root.interfaceColors.get("background"),
        strokeWidth: 2,
      }),
    });
  });

  // Set data for the third series
  series3.data.setAll(data3);

  // Add legend as a separate element below the chart
  const legend = chartContainer.children.push(
    am5.Legend.new(root, {
      centerX: am5.p50,
      x: am5.p50,
      layout: root.horizontalLayout,
      marginTop: 10,
      paddingTop: 10,
      paddingBottom: 10,
      background: am5.Rectangle.new(root, {
        fillOpacity: 0.03,
        fill: am5.color(0x000000),
      }),
    })
  );

  // Set legend data items - only show the first series initially
  legend.data.setAll([series]);

  // Make legend initially visible based on settings
  const showLegend =
    document.getElementById("lineChartSettings-legend")?.checked !== false;
  legend.set("forceHidden", !showLegend);

  // Scrollbar removed as requested

  // Add animation
  series.appear(1000, 100);
  chart.appear(1000, 100);

  console.log("Line chart initialization complete for container:", containerId);

  // Store the chart instance on the container element
  container.chart = chart;

  // Return the root object so it doesn't get garbage collected
  return root;
};

// Function to load chart data into the data table
function loadChartDataToTable(chart) {
  const tableBody = document.getElementById("lineChartDataBody");
  if (!tableBody) return;

  // Clear existing rows
  tableBody.innerHTML = "";

  // Get data from all series
  const series1 = chart.series.getIndex(0);
  const series2 = chart.series.getIndex(1);
  const series3 = chart.series.getIndex(2);

  if (!series1 || !series1.data) {
    console.log("No data found in chart, generating sample data");
    generateSampleData(tableBody);
    return;
  }

  // Get the data from all series
  // Use dataItems instead of values to get the actual data
  const data1 = series1.dataItems;
  const data2 = series2 ? series2.dataItems : [];
  const data3 = series3 ? series3.dataItems : [];

  console.log("Data loaded from chart:", {
    series1: data1.length,
    series2: data2.length,
    series3: data3.length,
  });

  // If there's no data in any of the series, try to get the raw data directly
  if (data1.length === 0 && data2.length === 0 && data3.length === 0) {
    console.log("No dataItems found, trying to get raw data");

    // Try to get the raw data
    const rawData1 = series1.data.values || [];
    const rawData2 = series2 ? series2.data.values || [] : [];
    const rawData3 = series3 ? series3.data.values || [] : [];

    console.log("Raw data found:", {
      series1: rawData1.length,
      series2: rawData2.length,
      series3: rawData3.length,
    });

    // If we have raw data, use it
    if (rawData1.length > 0 || rawData2.length > 0 || rawData3.length > 0) {
      // Process the raw data
      processRawData(tableBody, rawData1, rawData2, rawData3);
      return;
    }

    // If still no data, generate sample data
    console.log("No data found in any series, generating sample data");
    generateSampleData(tableBody);
    return;
  }

  // Create a map of dates to values for easier lookup
  const dateMap = new Map();

  // Process data from series 1 (Revenue)
  data1.forEach((item) => {
    const dateValue = item.get("valueX"); // Get the date value
    const numValue = item.get("valueY"); // Get the numeric value

    // Convert date to string format
    let dateStr;
    if (dateValue instanceof Date) {
      dateStr = dateValue.toISOString().split("T")[0];
    } else {
      // If it's already a string or another format
      dateStr = String(dateValue);
    }

    console.log("Series 1 item:", { date: dateStr, value: numValue });

    if (!dateMap.has(dateStr)) {
      dateMap.set(dateStr, { revenue: numValue, expenses: 0, profit: 0 });
    } else {
      dateMap.get(dateStr).revenue = numValue;
    }
  });

  // Process data from series 2 (Expenses)
  data2.forEach((item) => {
    const dateValue = item.get("valueX");
    const numValue = item.get("valueY");

    // Convert date to string format
    let dateStr;
    if (dateValue instanceof Date) {
      dateStr = dateValue.toISOString().split("T")[0];
    } else {
      dateStr = String(dateValue);
    }

    if (!dateMap.has(dateStr)) {
      dateMap.set(dateStr, { revenue: 0, expenses: numValue, profit: 0 });
    } else {
      dateMap.get(dateStr).expenses = numValue;
    }
  });

  // Process data from series 3 (Profit)
  data3.forEach((item) => {
    const dateValue = item.get("valueX");
    const numValue = item.get("valueY");

    // Convert date to string format
    let dateStr;
    if (dateValue instanceof Date) {
      dateStr = dateValue.toISOString().split("T")[0];
    } else {
      dateStr = String(dateValue);
    }

    if (!dateMap.has(dateStr)) {
      dateMap.set(dateStr, { revenue: 0, expenses: 0, profit: numValue });
    } else {
      dateMap.get(dateStr).profit = numValue;
    }
  });

  // Sort dates
  const sortedDates = Array.from(dateMap.keys()).sort();

  // Create rows for each date
  sortedDates.forEach((date) => {
    const data = dateMap.get(date);
    const revenue = data.revenue;
    const expenses = data.expenses;
    const profit = data.profit;

    // Create a new row
    const row = document.createElement("tr");

    // Date cell
    const dateCell = document.createElement("td");
    const dateInput = document.createElement("input");
    dateInput.type = "date";
    dateInput.className = "form-control form-control-sm";

    // Format date if it's not already in YYYY-MM-DD format
    let formattedDate = date;
    if (
      date &&
      typeof date === "string" &&
      !date.match(/^\d{4}-\d{2}-\d{2}$/)
    ) {
      // Try to parse the date
      const dateObj = new Date(date);
      if (!isNaN(dateObj.getTime())) {
        formattedDate = dateObj.toISOString().split("T")[0];
      }
    }

    dateInput.value = formattedDate;
    dateCell.appendChild(dateInput);

    // Revenue cell
    const revenueCell = document.createElement("td");
    const revenueInput = document.createElement("input");
    revenueInput.type = "number";
    revenueInput.className = "form-control form-control-sm";
    revenueInput.value = revenue;
    revenueCell.appendChild(revenueInput);

    // Expenses cell
    const expensesCell = document.createElement("td");
    const expensesInput = document.createElement("input");
    expensesInput.type = "number";
    expensesInput.className = "form-control form-control-sm";
    expensesInput.value = expenses;
    expensesCell.appendChild(expensesInput);

    // Profit cell
    const profitCell = document.createElement("td");
    const profitInput = document.createElement("input");
    profitInput.type = "number";
    profitInput.className = "form-control form-control-sm";
    profitInput.value = profit;
    profitInput.disabled = true; // Profit is calculated automatically
    profitCell.appendChild(profitInput);

    // Calculate profit when revenue or expenses change
    revenueInput.addEventListener("change", () => {
      profitInput.value =
        parseFloat(revenueInput.value || 0) -
        parseFloat(expensesInput.value || 0);
    });

    expensesInput.addEventListener("change", () => {
      profitInput.value =
        parseFloat(revenueInput.value || 0) -
        parseFloat(expensesInput.value || 0);
    });

    // Actions cell
    const actionsCell = document.createElement("td");
    const deleteButton = document.createElement("button");
    deleteButton.className = "btn btn-sm btn-outline-danger";
    deleteButton.innerHTML = "<i class='las la-trash'></i>";
    deleteButton.addEventListener("click", () => {
      row.remove();
    });
    actionsCell.appendChild(deleteButton);

    // Add cells to row
    row.appendChild(dateCell);
    row.appendChild(revenueCell);
    row.appendChild(expensesCell);
    row.appendChild(profitCell);
    row.appendChild(actionsCell);

    // Add row to table
    tableBody.appendChild(row);
  });

  // Set up event listeners for the buttons
  setupDataManagementButtons();
}

// Function to set up event listeners for data management buttons
function setupDataManagementButtons() {
  // Add row button
  const addRowButton = document.getElementById("addLineChartDataRow");
  if (addRowButton) {
    addRowButton.onclick = () => addNewDataRow();
  }

  // Import button
  const importButton = document.getElementById("lineChartDataImport");
  if (importButton) {
    importButton.onclick = () => importCSVData();
  }

  // Export button
  const exportButton = document.getElementById("lineChartDataExport");
  if (exportButton) {
    exportButton.onclick = () => exportCSVData();
  }
}

// Function to import CSV data
function importCSVData() {
  const fileInput = document.getElementById("lineChartDataFile");
  if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
    alert("Please select a CSV file to import.");
    return;
  }

  const file = fileInput.files[0];
  const reader = new FileReader();

  reader.onload = function (e) {
    const contents = e.target.result;
    const lines = contents.split("\n");

    // Clear existing data
    const tableBody = document.getElementById("lineChartDataBody");
    if (!tableBody) return;

    tableBody.innerHTML = "";

    // Process each line
    lines.forEach((line, index) => {
      // Skip empty lines
      if (!line.trim()) return;

      // Skip header row if present
      if (
        index === 0 &&
        line.toLowerCase().includes("date") &&
        line.toLowerCase().includes("revenue")
      ) {
        return;
      }

      const values = line.split(",");
      if (values.length < 2) return; // Need at least date and one value

      const date = values[0].trim();
      const revenue = parseFloat(values[1]) || 0;
      const expenses = values.length > 2 ? parseFloat(values[2]) || 0 : 0;
      const profit =
        values.length > 3 ? parseFloat(values[3]) || 0 : revenue - expenses;

      // Create a new row with this data
      addDataRow(tableBody, date, revenue, expenses, profit);
    });

    alert("CSV data imported successfully!");
  };

  reader.onerror = function () {
    alert("Error reading the file. Please try again.");
  };

  reader.readAsText(file);
}

// Function to export CSV data
function exportCSVData() {
  const tableBody = document.getElementById("lineChartDataBody");
  if (!tableBody) return;

  const rows = tableBody.querySelectorAll("tr");
  if (rows.length === 0) {
    alert("No data to export.");
    return;
  }

  // Create CSV content
  let csvContent = "Date,Revenue,Expenses,Profit\n";

  rows.forEach((row) => {
    const cells = row.querySelectorAll("td");
    if (cells.length >= 4) {
      const dateInput = cells[0].querySelector("input");
      const revenueInput = cells[1].querySelector("input");
      const expensesInput = cells[2].querySelector("input");
      const profitInput = cells[3].querySelector("input");

      if (dateInput && revenueInput && expensesInput && profitInput) {
        const date = dateInput.value;
        const revenue = revenueInput.value;
        const expenses = expensesInput.value;
        const profit = profitInput.value;

        csvContent += `${date},${revenue},${expenses},${profit}\n`;
      }
    }
  });

  // Create a download link
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.setAttribute("href", url);
  link.setAttribute("download", "chart_data.csv");
  link.style.visibility = "hidden";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// Helper function to add a data row with specific values
function addDataRow(tableBody, date, revenue, expenses, profit) {
  if (!tableBody) return;

  // Create a new row
  const row = document.createElement("tr");

  // Date cell
  const dateCell = document.createElement("td");
  const dateInput = document.createElement("input");
  dateInput.type = "date";
  dateInput.className = "form-control form-control-sm";

  // Format date if it's not already in YYYY-MM-DD format
  let formattedDate = date;
  if (date && typeof date === "string" && !date.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // Try to parse the date
    const dateObj = new Date(date);
    if (!isNaN(dateObj.getTime())) {
      formattedDate = dateObj.toISOString().split("T")[0];
    }
  }

  dateInput.value = formattedDate;
  dateCell.appendChild(dateInput);

  // Revenue cell
  const revenueCell = document.createElement("td");
  const revenueInput = document.createElement("input");
  revenueInput.type = "number";
  revenueInput.className = "form-control form-control-sm";
  revenueInput.value = revenue;
  revenueCell.appendChild(revenueInput);

  // Expenses cell
  const expensesCell = document.createElement("td");
  const expensesInput = document.createElement("input");
  expensesInput.type = "number";
  expensesInput.className = "form-control form-control-sm";
  expensesInput.value = expenses;
  expensesCell.appendChild(expensesInput);

  // Profit cell
  const profitCell = document.createElement("td");
  const profitInput = document.createElement("input");
  profitInput.type = "number";
  profitInput.className = "form-control form-control-sm";
  profitInput.value = profit;
  profitInput.disabled = true; // Profit is calculated automatically
  profitCell.appendChild(profitInput);

  // Calculate profit when revenue or expenses change
  revenueInput.addEventListener("change", () => {
    profitInput.value =
      parseFloat(revenueInput.value || 0) -
      parseFloat(expensesInput.value || 0);
  });

  expensesInput.addEventListener("change", () => {
    profitInput.value =
      parseFloat(revenueInput.value || 0) -
      parseFloat(expensesInput.value || 0);
  });

  // Actions cell
  const actionsCell = document.createElement("td");
  actionsCell.className = "text-center";
  const deleteButton = document.createElement("button");
  deleteButton.className = "btn btn-sm btn-outline-danger";
  deleteButton.style.width = "28px";
  deleteButton.style.height = "28px";
  deleteButton.style.padding = "0";
  deleteButton.innerHTML = "<i class='las la-times'></i>";
  deleteButton.title = "Remove row";
  deleteButton.addEventListener("click", () => {
    row.remove();
  });
  actionsCell.appendChild(deleteButton);

  // Add cells to row
  row.appendChild(dateCell);
  row.appendChild(revenueCell);
  row.appendChild(expensesCell);
  row.appendChild(profitCell);
  row.appendChild(actionsCell);

  // Add row to table
  tableBody.appendChild(row);
}

// Function to add a new data row
function addNewDataRow() {
  const tableBody = document.getElementById("lineChartDataBody");
  if (!tableBody) return;

  // Get today's date in YYYY-MM-DD format
  const today = new Date().toISOString().split("T")[0];

  // Add a new row with default values
  addDataRow(tableBody, today, 0, 0, 0);
}

// Function to update chart data from the table
function updateChartData(chart) {
  const tableBody = document.getElementById("lineChartDataBody");
  if (!tableBody || !chart) return;

  // Get all rows
  const rows = tableBody.querySelectorAll("tr");

  // Prepare data arrays
  const data1 = []; // Revenue
  const data2 = []; // Expenses
  const data3 = []; // Profit

  // Extract data from each row
  rows.forEach((row) => {
    const cells = row.querySelectorAll("td");
    if (cells.length >= 4) {
      const dateInput = cells[0].querySelector("input");
      const revenueInput = cells[1].querySelector("input");
      const expensesInput = cells[2].querySelector("input");
      const profitInput = cells[3].querySelector("input");

      if (dateInput && revenueInput && expensesInput && profitInput) {
        const date = dateInput.value;
        if (!date) return; // Skip rows with empty dates

        const revenue = parseFloat(revenueInput.value) || 0;
        const expenses = parseFloat(expensesInput.value) || 0;
        const profit = parseFloat(profitInput.value) || 0;

        // Add data to arrays
        data1.push({
          date: date,
          value: revenue,
        });

        data2.push({
          date: date,
          value: expenses,
        });

        data3.push({
          date: date,
          value: profit,
        });
      }
    }
  });

  // Only proceed if we have data
  if (data1.length === 0) return;

  // Sort data by date
  data1.sort((a, b) => new Date(a.date) - new Date(b.date));
  data2.sort((a, b) => new Date(a.date) - new Date(b.date));
  data3.sort((a, b) => new Date(a.date) - new Date(b.date));

  // Update chart data
  const series1 = chart.series.getIndex(0);
  const series2 = chart.series.getIndex(1);
  const series3 = chart.series.getIndex(2);

  // Log the data we're about to set
  console.log("Updating chart with data:", {
    revenue: data1.length,
    expenses: data2.length,
    profit: data3.length,
    sample: data1[0],
  });

  // Make sure data processors are set up correctly
  if (series1) {
    // Ensure the processor is set up correctly
    if (series1.data.processor) {
      series1.data.processor.dispose();
    }
    series1.data.processor = am5.DataProcessor.new(chart.root, {
      dateFormat: "yyyy-MM-dd",
      dateFields: ["date"],
    });
  }

  if (series2) {
    if (series2.data.processor) {
      series2.data.processor.dispose();
    }
    series2.data.processor = am5.DataProcessor.new(chart.root, {
      dateFormat: "yyyy-MM-dd",
      dateFields: ["date"],
    });
  }

  if (series3) {
    if (series3.data.processor) {
      series3.data.processor.dispose();
    }
    series3.data.processor = am5.DataProcessor.new(chart.root, {
      dateFormat: "yyyy-MM-dd",
      dateFields: ["date"],
    });
  }

  // Set the data
  if (series1) series1.data.setAll(data1);
  if (series2) series2.data.setAll(data2);
  if (series3) series3.data.setAll(data3);

  // Force chart to redraw
  chart.series.each(function (series) {
    series.appear(1000, 100);
  });

  // Update axes
  const xAxis = chart.xAxes.getIndex(0);
  const yAxis = chart.yAxes.getIndex(0);

  if (xAxis) xAxis.zoom(0, 1);
  if (yAxis) yAxis.zoom(0, 1);
}

// Function to process raw data from the chart
function processRawData(tableBody, rawData1, rawData2, rawData3) {
  if (!tableBody) return;

  // Clear existing rows
  tableBody.innerHTML = "";

  // Create a map of dates to values for easier lookup
  const dateMap = new Map();

  // Process data from series 1 (Revenue)
  rawData1.forEach((item) => {
    if (!dateMap.has(item.date)) {
      dateMap.set(item.date, { revenue: item.value, expenses: 0, profit: 0 });
    } else {
      dateMap.get(item.date).revenue = item.value;
    }
  });

  // Process data from series 2 (Expenses)
  rawData2.forEach((item) => {
    if (!dateMap.has(item.date)) {
      dateMap.set(item.date, { revenue: 0, expenses: item.value, profit: 0 });
    } else {
      dateMap.get(item.date).expenses = item.value;
    }
  });

  // Process data from series 3 (Profit)
  rawData3.forEach((item) => {
    if (!dateMap.has(item.date)) {
      dateMap.set(item.date, { revenue: 0, expenses: 0, profit: item.value });
    } else {
      dateMap.get(item.date).profit = item.value;
    }
  });

  // Sort dates
  const sortedDates = Array.from(dateMap.keys()).sort();

  // Create rows for each date
  sortedDates.forEach((date) => {
    const data = dateMap.get(date);
    const revenue = data.revenue;
    const expenses = data.expenses;
    const profit = data.profit;

    // Create a new row
    const row = document.createElement("tr");

    // Date cell
    const dateCell = document.createElement("td");
    const dateInput = document.createElement("input");
    dateInput.type = "date";
    dateInput.className = "form-control form-control-sm";

    // Format date if it's not already in YYYY-MM-DD format
    let formattedDate = date;
    if (
      date &&
      typeof date === "string" &&
      !date.match(/^\d{4}-\d{2}-\d{2}$/)
    ) {
      // Try to parse the date
      const dateObj = new Date(date);
      if (!isNaN(dateObj.getTime())) {
        formattedDate = dateObj.toISOString().split("T")[0];
      }
    }

    dateInput.value = formattedDate;
    dateCell.appendChild(dateInput);

    // Revenue cell
    const revenueCell = document.createElement("td");
    const revenueInput = document.createElement("input");
    revenueInput.type = "number";
    revenueInput.className = "form-control form-control-sm";
    revenueInput.value = revenue;
    revenueCell.appendChild(revenueInput);

    // Expenses cell
    const expensesCell = document.createElement("td");
    const expensesInput = document.createElement("input");
    expensesInput.type = "number";
    expensesInput.className = "form-control form-control-sm";
    expensesInput.value = expenses;
    expensesCell.appendChild(expensesInput);

    // Profit cell
    const profitCell = document.createElement("td");
    const profitInput = document.createElement("input");
    profitInput.type = "number";
    profitInput.className = "form-control form-control-sm";
    profitInput.value = profit;
    profitInput.disabled = true; // Profit is calculated automatically
    profitCell.appendChild(profitInput);

    // Calculate profit when revenue or expenses change
    revenueInput.addEventListener("change", () => {
      profitInput.value =
        parseFloat(revenueInput.value || 0) -
        parseFloat(expensesInput.value || 0);
    });

    expensesInput.addEventListener("change", () => {
      profitInput.value =
        parseFloat(revenueInput.value || 0) -
        parseFloat(expensesInput.value || 0);
    });

    // Actions cell
    const actionsCell = document.createElement("td");
    actionsCell.className = "text-center";
    const deleteButton = document.createElement("button");
    deleteButton.className = "btn btn-sm btn-outline-danger";
    deleteButton.style.width = "28px";
    deleteButton.style.height = "28px";
    deleteButton.style.padding = "0";
    deleteButton.innerHTML = "<i class='las la-times'></i>";
    deleteButton.title = "Remove row";
    deleteButton.addEventListener("click", () => {
      row.remove();
    });
    actionsCell.appendChild(deleteButton);

    // Add cells to row
    row.appendChild(dateCell);
    row.appendChild(revenueCell);
    row.appendChild(expensesCell);
    row.appendChild(profitCell);
    row.appendChild(actionsCell);

    // Add row to table
    tableBody.appendChild(row);
  });

  // Set up event listeners for the buttons
  setupDataManagementButtons();
}

// Function to generate sample data for the data table
function generateSampleData(tableBody) {
  if (!tableBody) return;

  // Clear existing rows
  tableBody.innerHTML = "";

  // Generate sample data for 10 days
  const today = new Date();
  let revenue = 1000;
  let expenses = 800;

  for (let i = 0; i < 10; i++) {
    // Calculate date
    const date = new Date(today);
    date.setDate(date.getDate() - (9 - i)); // Start 9 days ago
    const dateStr = date.toISOString().split("T")[0];

    // Generate random values
    revenue = Math.round(revenue + (Math.random() * 100 - 50));
    expenses = Math.round(expenses + (Math.random() * 80 - 40));
    const profit = revenue - expenses;

    // Create a new row
    const row = document.createElement("tr");

    // Date cell
    const dateCell = document.createElement("td");
    const dateInput = document.createElement("input");
    dateInput.type = "date";
    dateInput.className = "form-control form-control-sm";
    dateInput.value = dateStr;
    dateCell.appendChild(dateInput);

    // Revenue cell
    const revenueCell = document.createElement("td");
    const revenueInput = document.createElement("input");
    revenueInput.type = "number";
    revenueInput.className = "form-control form-control-sm";
    revenueInput.value = revenue;
    revenueCell.appendChild(revenueInput);

    // Expenses cell
    const expensesCell = document.createElement("td");
    const expensesInput = document.createElement("input");
    expensesInput.type = "number";
    expensesInput.className = "form-control form-control-sm";
    expensesInput.value = expenses;
    expensesCell.appendChild(expensesInput);

    // Profit cell
    const profitCell = document.createElement("td");
    const profitInput = document.createElement("input");
    profitInput.type = "number";
    profitInput.className = "form-control form-control-sm";
    profitInput.value = profit;
    profitInput.disabled = true; // Profit is calculated automatically
    profitCell.appendChild(profitInput);

    // Calculate profit when revenue or expenses change
    revenueInput.addEventListener("change", () => {
      profitInput.value =
        parseFloat(revenueInput.value || 0) -
        parseFloat(expensesInput.value || 0);
    });

    expensesInput.addEventListener("change", () => {
      profitInput.value =
        parseFloat(revenueInput.value || 0) -
        parseFloat(expensesInput.value || 0);
    });

    // Actions cell
    const actionsCell = document.createElement("td");
    actionsCell.className = "text-center";
    const deleteButton = document.createElement("button");
    deleteButton.className = "btn btn-sm btn-outline-danger";
    deleteButton.style.width = "28px";
    deleteButton.style.height = "28px";
    deleteButton.style.padding = "0";
    deleteButton.innerHTML = "<i class='las la-times'></i>";
    deleteButton.title = "Remove row";
    deleteButton.addEventListener("click", () => {
      row.remove();
    });
    actionsCell.appendChild(deleteButton);

    // Add cells to row
    row.appendChild(dateCell);
    row.appendChild(revenueCell);
    row.appendChild(expensesCell);
    row.appendChild(profitCell);
    row.appendChild(actionsCell);

    // Add row to table
    tableBody.appendChild(row);
  }

  // Set up event listeners for the buttons
  setupDataManagementButtons();
}

// Export the functions
window.addLineChartWidget = addLineChartWidget;
window.applyLineChartSettings = applyLineChartSettings;
