/**
 * Tab Container Widget
 * Provides a tabbed interface for organizing content sections
 */

// Global counter for tab containers
let tabContainerCounter = 0;
let nestedGrids = new Map(); // Store nested grid instances

// Tab Configuration Storage
let tabConfigurations = new Map(); // Store tab configurations for each container

// Store event handlers to prevent duplicates
let modalEventHandlers = new Map(); // Store modal event handlers

// Default Tab Templates
const DEFAULT_TAB_TEMPLATES = {
  nestedGrid: {
    type: "nestedGrid",
    hasGrid: true,
    hasPlaceholder: true,
    template: "nestedGridTemplate",
  },
  simpleContent: {
    type: "simpleContent",
    hasGrid: false,
    hasPlaceholder: false,
    template: "simpleContentTemplate",
  },
};

// Default Tab Configuration
const DEFAULT_TAB_CONFIG = {
  tabs: [
    {
      id: "analysis",
      name: "Analysis",
      icon: "las la-chart-line",
      type: "nestedGrid",
      enabled: true,
      order: 1,
      removable: true,
    },
    {
      id: "forecast",
      name: "Forecast",
      icon: "las la-crystal-ball",
      type: "nestedGrid",
      enabled: true,
      order: 2,
      removable: true,
    },
    {
      id: "data",
      name: "Data",
      icon: "las la-table",
      type: "nestedGrid",
      enabled: true,
      order: 3,
      removable: true,
    },
  ],
  settings: {
    maxTabs: 10,
    allowReorder: true,
    allowDelete: true,
    allowAdd: true,
  },
};

// Tab Configuration Management Functions
function getTabConfiguration(containerId) {
  if (!tabConfigurations.has(containerId)) {
    // Initialize with default configuration
    tabConfigurations.set(
      containerId,
      JSON.parse(JSON.stringify(DEFAULT_TAB_CONFIG))
    );
  }
  return tabConfigurations.get(containerId);
}

function updateTabConfiguration(containerId, config) {
  tabConfigurations.set(containerId, config);
  // Optionally save to localStorage
  try {
    localStorage.setItem(`tabConfig_${containerId}`, JSON.stringify(config));
  } catch (e) {
    console.warn("Could not save tab configuration to localStorage:", e);
  }
}

function loadTabConfiguration(containerId) {
  try {
    const saved = localStorage.getItem(`tabConfig_${containerId}`);
    if (saved) {
      const config = JSON.parse(saved);
      tabConfigurations.set(containerId, config);
      return config;
    }
  } catch (e) {
    console.warn("Could not load tab configuration from localStorage:", e);
  }
  return getTabConfiguration(containerId);
}

// Generate Tab HTML based on configuration
function generateTabPanelsHTML(containerId, tabConfig) {
  let panelsHTML = "";

  // Sort tabs by order
  const sortedTabs = tabConfig.tabs
    .filter((tab) => tab.enabled)
    .sort((a, b) => a.order - b.order);

  sortedTabs.forEach((tab) => {
    if (tab.type === "nestedGrid") {
      panelsHTML += generateNestedGridTabHTML(containerId, tab);
    } else {
      panelsHTML += generateSimpleContentTabHTML(containerId, tab);
    }
  });

  return panelsHTML;
}

function generateNestedGridTabHTML(containerId, tab) {
  return `
    <div class="tab-slideout-panel" id="${containerId}-panel-${tab.id}">
      <div class="panel-content">
        <div class="content-header">
          <h4><i class="${tab.icon}"></i> ${tab.name} Grid</h4>
          <div class="panel-actions">
            <button class="btn btn-sm btn-outline-primary" onclick="clearNestedGrid('${containerId}', '${
    tab.id
  }')">
              <i class="las la-trash"></i> Clear Grid
            </button>
            <button class="close-panel-btn" onclick="closeTabPanel('${containerId}')">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="content-body">
          <div class="nested-grid-container">
            <div class="nested-grid" id="${containerId}-${tab.id}-grid">
              <!-- Nested widgets will be added here -->
            </div>
            <div class="grid-placeholder" id="${containerId}-${
    tab.id
  }-placeholder">
              <div class="placeholder-content">
                <i class="las la-th"></i>
                <h5>Drop Zone</h5>
                <p>Drag widgets from the panel to create your ${tab.name.toLowerCase()} dashboard</p>
                <div class="drop-hint">
                  <i class="las la-mouse-pointer"></i>
                  <span>Drag & Drop widgets here</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

function generateSimpleContentTabHTML(containerId, tab) {
  return `
    <div class="tab-slideout-panel" id="${containerId}-panel-${tab.id}">
      <div class="panel-content">
        <div class="content-header">
          <h4><i class="${tab.icon}"></i> ${tab.name}</h4>
          <div class="panel-actions">
            <button class="btn btn-sm btn-outline-primary" onclick="clearTabContent('${containerId}', '${tab.id}')">
              <i class="las la-trash"></i> Clear Content
            </button>
            <button class="close-panel-btn" onclick="closeTabPanel('${containerId}')">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="content-body">
          <div class="simple-content-container" id="${containerId}-${tab.id}-content">
            <div class="content-placeholder">
              <i class="${tab.icon}"></i>
              <h5>${tab.name}</h5>
              <p>Add your content here</p>
              <div class="content-actions">
                <button class="btn btn-primary btn-sm" onclick="addContentToTab('${containerId}', '${tab.id}', 'text')">
                  <i class="las la-file-alt"></i> Add Text
                </button>
                <button class="btn btn-secondary btn-sm" onclick="addContentToTab('${containerId}', '${tab.id}', 'list')">
                  <i class="las la-list"></i> Add List
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

// Generate Tab Navigation HTML
function generateTabNavigationHTML(containerId, tabConfig) {
  let navHTML = "";

  // Sort tabs by order
  const sortedTabs = tabConfig.tabs
    .filter((tab) => tab.enabled)
    .sort((a, b) => a.order - b.order);

  sortedTabs.forEach((tab) => {
    navHTML += `
      <li class="tab-nav-item">
        <button class="tab-nav-link" onclick="openTabPanel('${containerId}', '${tab.id}')">
          <i class="${tab.icon}"></i>
          <span>${tab.name}</span>
        </button>
      </li>
    `;
  });

  return navHTML;
}

// Add Tab Container Widget
function addTabContainerWidget() {
  const containerId = `tabContainer${++tabContainerCounter}`;
  const settingsId = `tabContainerSettings${tabContainerCounter}`;

  // Load or create tab configuration
  const tabConfig = loadTabConfiguration(containerId);

  // Create the widget content HTML
  const widgetContent = createTabContainerHTML(
    containerId,
    settingsId,
    tabConfig
  );

  try {
    // Add widget using GridStack's addWidget method with proper options
    const widgetElement = grid.addWidget({
      w: 6,
      h: 8,
      id: containerId,
      content: widgetContent,
    });

    // Initialize the tab container after adding to grid
    setTimeout(() => {
      initializeTabContainer(containerId);
      createTabContainerSettingsOffcanvas(settingsId, containerId);
      initializeNestedGrids(containerId);

      // Ensure resize handles are properly initialized
      if (widgetElement && grid) {
        // Use proper GridStack API instead of internal methods
        try {
          // Enable resizing for the grid
          grid.enableResize(true);

          // If the widget has a gridstack node, make sure it's resizable
          if (widgetElement.gridstackNode) {
            // Update the widget's resizable state
            grid.update(widgetElement, { resizable: true });
          }
        } catch (error) {
          console.warn(
            "Could not setup resizable for tab container widget:",
            error
          );
        }
      }
    }, 100);
  } catch (error) {
    console.error("Error adding Tab Container widget:", error);
  }
}

// Create Tab Container HTML with dynamic tab generation
function createTabContainerHTML(containerId, settingsId, tabConfig) {
  const tabNavigationHTML = generateTabNavigationHTML(containerId, tabConfig);
  const tabPanelsHTML = generateTabPanelsHTML(containerId, tabConfig);

  return `
    <div class="tab-container-widget" id="${containerId}">
      <div class="widget-header">
        <div class="widget-title">
          <i class="las la-folder-open"></i>
          <span>Tab Container</span>
        </div>
        <div class="widget-actions">
          <button class="widget-action-btn" onclick="showAddTabModal('${containerId}')" 
                  title="Add Tab">
            <i class="las la-plus"></i>
          </button>
          <button class="widget-action-btn" data-bs-toggle="offcanvas" data-bs-target="#${settingsId}" aria-controls="${settingsId}"
                  title="Settings">
            <i class="las la-cog"></i>
          </button>
          <button class="widget-action-btn" onclick="refreshTabContainer('${containerId}')" 
                  title="Refresh">
            <i class="las la-redo-alt"></i>
          </button>
        </div>
      </div>
      
      <div class="tab-container-body">
        <!-- Tab Navigation -->
        <div class="tab-navigation">
          <ul class="tab-nav-list" id="${containerId}-tab-nav">
            ${tabNavigationHTML}
          </ul>
        </div>
        
        <!-- Default content shown when no panel is open -->
        <div class="tab-container-default-content">
          <div class="default-content-placeholder">
            <i class="las la-layer-group"></i>
            <h3>Nested Grid Container</h3>
            <p>Click on the tabs below to access droppable grid areas</p>
            <div class="grid-info">
              <span class="badge bg-primary">Drag & Drop Enabled</span>
              <span class="badge bg-secondary">Configurable Tabs</span>
            </div>
          </div>
        </div>
        
        <!-- Dynamic Tab Panels -->
        ${tabPanelsHTML}
      </div>
    </div>
  `;
}

// Create Tab Container Settings Offcanvas
function createTabContainerSettingsOffcanvas(settingsId, containerId) {
  const offcanvasContainer =
    document.getElementById("offcanvasContainer") || document.body;

  // Check if modal already exists to prevent duplicates
  const existingModal = document.getElementById(`${containerId}-tab-modal`);
  if (existingModal) {
    console.log(`Modal for ${containerId} already exists, skipping creation`);
    return;
  }

  const tabConfig = getTabConfiguration(containerId);

  const offcanvasHtml = `
    <div class="offcanvas offcanvas-end" tabindex="-1" id="${settingsId}" aria-labelledby="${settingsId}Label">
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="${settingsId}Label">Tab Container Settings</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div class="offcanvas-body">
        <!-- Container Settings -->
        <h6 class="mb-3">Container Settings</h6>
        
        <!-- Container Title -->
        <div class="mb-3">
          <label class="form-label">Container Title</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-title" value="Tab Container">
        </div>
        
        <!-- Tab Position -->
        <div class="mb-3">
          <label class="form-label">Tab Position</label>
          <select class="form-control form-control-sm" id="${settingsId}-tab-position">
            <option value="top">Top</option>
            <option value="bottom">Bottom</option>
          </select>
        </div>
        
        <!-- Theme -->
        <div class="mb-3">
          <label class="form-label">Theme</label>
          <select class="form-control form-control-sm" id="${settingsId}-theme">
            <option value="default">Default</option>
            <option value="primary">Primary</option>
            <option value="success">Success</option>
            <option value="warning">Warning</option>
            <option value="info">Info</option>
          </select>
        </div>
        
        <!-- Max Tabs -->
        <div class="mb-3">
          <label class="form-label">Maximum Tabs</label>
          <input type="number" class="form-control form-control-sm" id="${settingsId}-max-tabs" value="${
    tabConfig.settings.maxTabs
  }" min="1" max="20">
        </div>
        
        <!-- Tab Features -->
        <div class="mb-3">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="${settingsId}-closeable" ${
    tabConfig.settings.allowDelete ? "checked" : ""
  }>
            <label class="form-check-label" for="${settingsId}-closeable">Allow Tab Deletion</label>
          </div>
        </div>
        
        <div class="mb-3">
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="${settingsId}-draggable" ${
    tabConfig.settings.allowReorder ? "checked" : ""
  }>
            <label class="form-check-label" for="${settingsId}-draggable">Enable Tab Reordering</label>
          </div>
        </div>

        <hr class="my-4">

        <!-- Tab Management -->
        <div class="d-flex justify-content-between align-items-center mb-3">
          <h6 class="mb-0">Tab Management</h6>
          <button class="btn btn-sm btn-primary" onclick="showAddTabModalFromSettings('${containerId}')">
            <i class="las la-plus"></i> Add Tab
          </button>
        </div>
        
        <!-- Current Tabs List -->
        <div class="tab-management-list" id="${settingsId}-tab-list">
          ${generateTabManagementList(containerId, tabConfig)}
        </div>

        <hr class="my-4">
        
        <!-- Apply Button -->
        <button class="btn btn-primary w-100" onclick="applyTabContainerSettings('${containerId}', '${settingsId}')">
          Apply Changes
        </button>
      </div>
    </div>

    <!-- Add/Edit Tab Modal -->
    <div class="modal fade" id="${containerId}-tab-modal" tabindex="-1" aria-labelledby="${containerId}-tab-modal-title" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="${containerId}-tab-modal-title">Add New Tab</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <form id="${containerId}-tab-form">
              <input type="hidden" id="${containerId}-tab-edit-id" value="">
              
              <!-- Tab Name -->
              <div class="mb-3">
                <label class="form-label" for="${containerId}-tab-name">Tab Name *</label>
                <input type="text" class="form-control" id="${containerId}-tab-name" required>
              </div>
              
              <!-- Tab Icon -->
              <div class="mb-3">
                <label class="form-label" for="${containerId}-tab-icon">Tab Icon</label>
                <div class="input-group">
                  <span class="input-group-text"><i id="${containerId}-tab-icon-preview" class="las la-cube"></i></span>
                  <input type="text" class="form-control" id="${containerId}-tab-icon" value="las la-cube" placeholder="las la-cube">
                </div>
                <div class="form-text">Use Line Awesome icons (e.g., las la-chart-bar)</div>
              </div>
              
              <!-- Tab Type -->
              <div class="mb-3">
                <label class="form-label" for="${containerId}-tab-type">Tab Type *</label>
                <select class="form-control" id="${containerId}-tab-type" onchange="updateTabTypeDescription('${containerId}')">
                  <option value="nestedGrid">Nested Grid</option>
                  <option value="simpleContent">Simple Content</option>
                </select>
                <div class="form-text" id="${containerId}-tab-type-description">
                  Nested Grid: Provides a droppable grid area for widgets (like Analysis, Forecast, Data tabs)
                </div>
              </div>
              
              <!-- Tab Order -->
              <div class="mb-3">
                <label class="form-label" for="${containerId}-tab-order">Tab Order</label>
                <input type="number" class="form-control" id="${containerId}-tab-order" min="1" value="1">
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="saveTabContainerConfiguration('${containerId}')">Save Tab</button>
          </div>
        </div>
      </div>
    </div>
  `;

  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = offcanvasHtml;
  // Append both the offcanvas and modal
  const elements = tempDiv.children;
  while (elements.length > 0) {
    offcanvasContainer.appendChild(elements[0]);
  }

  // Set up modal event cleanup
  const modal = document.getElementById(`${containerId}-tab-modal`);
  if (modal) {
    modal.addEventListener("hidden.bs.modal", function () {
      cleanupModalEventHandlers(containerId);
    });
  }

  // DEBUGGING: Check if modal and button are properly created
  console.log("🔍 DEBUGGING MODAL CREATION:");
  console.log("Modal element found:", modal);

  const saveButton = document.querySelector(
    `button[onclick="saveTabContainerConfiguration('${containerId}')"]`
  );
  console.log("Save Tab button found:", saveButton);

  if (saveButton) {
    console.log("🔧 Setting up Save Tab button click handler...");

    // Remove the broken onclick attribute
    saveButton.removeAttribute("onclick");

    // Add a single, clean event listener
    saveButton.addEventListener("click", function (e) {
      console.log("💾 Save Tab button clicked!");
      e.preventDefault(); // Prevent any default behavior
      e.stopPropagation(); // Stop event bubbling

      // Call the save function with error handling
      try {
        console.log(
          "🔄 About to call saveTabContainerConfiguration with:",
          containerId
        );
        console.log(
          "🔍 Function check - saveTabContainerConfiguration exists:",
          typeof saveTabContainerConfiguration === "function"
        );
        console.log(
          "🔍 Function check - window.saveTabContainerConfiguration exists:",
          typeof window.saveTabContainerConfiguration === "function"
        );

        saveTabContainerConfiguration(containerId);
        console.log("✅ saveTabContainerConfiguration call completed");
      } catch (error) {
        console.error("❌ Error calling saveTabContainerConfiguration:", error);
        alert("Error saving tab: " + error.message);
      }
    });

    console.log("✅ Save Tab button event listener attached successfully");
  } else {
    console.error("❌ Save Tab button not found in DOM!");
  }
}

// Generate Tab Management List HTML
function generateTabManagementList(containerId, tabConfig) {
  let listHTML = "";

  // Sort tabs by order
  const sortedTabs = tabConfig.tabs.sort((a, b) => a.order - b.order);

  sortedTabs.forEach((tab) => {
    const typeLabel =
      tab.type === "nestedGrid" ? "Nested Grid" : "Simple Content";
    const statusClass = tab.enabled ? "text-success" : "text-muted";
    const statusIcon = tab.enabled
      ? "las la-check-circle"
      : "las la-times-circle";

    listHTML += `
      <div class="tab-management-item ${
        tab.enabled ? "" : "disabled"
      }" data-tab-id="${tab.id}">
        <div class="tab-item-drag-handle">
          <i class="las la-grip-vertical"></i>
        </div>
        <div class="tab-item-info">
          <div class="tab-item-header">
            <i class="${tab.icon}"></i>
            <span class="tab-item-name">${tab.name}</span>
            <span class="badge badge-sm bg-light text-dark">${typeLabel}</span>
          </div>
          <div class="tab-item-meta">
            <span class="tab-item-order">Order: ${tab.order}</span>
            <span class="tab-item-status ${statusClass}">
              <i class="${statusIcon}"></i>
              ${tab.enabled ? "Enabled" : "Disabled"}
            </span>
          </div>
        </div>
        <div class="tab-item-actions">
          <button class="btn btn-sm btn-outline-secondary" onclick="toggleTabEnabled('${containerId}', '${
      tab.id
    }')" title="${tab.enabled ? "Disable" : "Enable"} Tab">
            <i class="${tab.enabled ? "las la-eye-slash" : "las la-eye"}"></i>
          </button>
          <button class="btn btn-sm btn-outline-primary" onclick="editTabFromSettings('${containerId}', '${
      tab.id
    }')" title="Edit Tab">
            <i class="las la-edit"></i>
          </button>
          ${
            tab.removable
              ? `
            <button class="btn btn-sm btn-outline-danger" onclick="deleteTab('${containerId}', '${tab.id}')" title="Delete Tab">
              <i class="las la-trash"></i>
            </button>
          `
              : ""
          }
        </div>
      </div>
    `;
  });

  return (
    listHTML ||
    '<div class="text-center text-muted p-3">No tabs configured</div>'
  );
}

// Initialize Tab Container
function initializeTabContainer(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  // Set up tab switching functionality
  const tabItems = container.querySelectorAll(".tab-item");
  tabItems.forEach((tab) => {
    tab.addEventListener("click", (e) => {
      if (!e.target.closest(".tab-close")) {
        switchTab(containerId, tab.dataset.tab);
      }
    });
  });

  // Enable drag and drop for tab reordering
  enableTabDragAndDrop(containerId);

  console.log(`Tab Container ${containerId} initialized`);
}

// Switch Tab
function switchTab(containerId, tabId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  // Update tab navigation
  const tabItems = container.querySelectorAll(".tab-item");
  tabItems.forEach((item) => {
    if (item.dataset.tab === tabId) {
      item.classList.add("active");
    } else {
      item.classList.remove("active");
    }
  });

  // Update tab content
  const tabPanes = container.querySelectorAll(".tab-pane");
  tabPanes.forEach((pane) => {
    if (pane.id === `${containerId}-${tabId}`) {
      pane.classList.add("active");
    } else {
      pane.classList.remove("active");
    }
  });
}

// Add Tab to Container
function addTabToContainer(containerId) {
  // Redirect to new modal interface
  showAddTabModal(containerId);
}

// Remove Tab
function removeTab(containerId, tabId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  const tabItem = container.querySelector(`[data-tab="${tabId}"]`);
  const tabPane = container.querySelector(`#${containerId}-${tabId}`);

  if (tabItem && tabPane) {
    // Check if this is the active tab
    const isActive = tabItem.classList.contains("active");

    // Remove tab and pane
    tabItem.remove();
    tabPane.remove();

    // If removed tab was active, switch to first available tab
    if (isActive) {
      const remainingTabs = container.querySelectorAll(".tab-item");
      if (remainingTabs.length > 0) {
        switchTab(containerId, remainingTabs[0].dataset.tab);
      }
    }
  }
}

// Add Content to Tab
function addContentToTab(containerId, tabId, contentType) {
  const tabPane = document.getElementById(`${containerId}-${tabId}`);
  if (!tabPane) return;

  const contentArea = tabPane.querySelector(".tab-pane-content");
  const placeholder = contentArea.querySelector(".content-placeholder");

  let newContent = "";

  switch (contentType) {
    case "chart":
      newContent = `
        <div class="tab-content-item chart-content">
          <div class="content-header">
            <h4>Chart Widget</h4>
            <button class="remove-content" onclick="this.parentElement.parentElement.remove()">
              <i class="las la-times"></i>
            </button>
          </div>
          <div class="chart-placeholder">
            <i class="las la-chart-bar"></i>
            <p>Chart will be displayed here</p>
          </div>
        </div>
      `;
      break;
    case "text":
      newContent = `
        <div class="tab-content-item text-content">
          <div class="content-header">
            <h4>Text Content</h4>
            <button class="remove-content" onclick="this.parentElement.parentElement.remove()">
              <i class="las la-times"></i>
            </button>
          </div>
          <div class="text-editor" contenteditable="true">
            Click here to add your text content...
          </div>
        </div>
      `;
      break;
    case "table":
      newContent = `
        <div class="tab-content-item table-content">
          <div class="content-header">
            <h4>Data Table</h4>
            <button class="remove-content" onclick="this.parentElement.parentElement.remove()">
              <i class="las la-times"></i>
            </button>
          </div>
          <div class="table-placeholder">
            <i class="las la-table"></i>
            <p>Data table will be displayed here</p>
          </div>
        </div>
      `;
      break;
    case "forecast":
      newContent = `
        <div class="tab-content-item forecast-content">
          <div class="content-header">
            <h4>Forecast Model</h4>
            <button class="remove-content" onclick="this.parentElement.parentElement.remove()">
              <i class="las la-times"></i>
            </button>
          </div>
          <div class="forecast-placeholder">
            <i class="las la-crystal-ball"></i>
            <p>Forecast visualization will be displayed here</p>
          </div>
        </div>
      `;
      break;
    case "kpi":
      newContent = `
        <div class="tab-content-item kpi-content">
          <div class="content-header">
            <h4>KPI Metrics</h4>
            <button class="remove-content" onclick="this.parentElement.parentElement.remove()">
              <i class="las la-times"></i>
            </button>
          </div>
          <div class="kpi-placeholder">
            <i class="las la-tachometer-alt"></i>
            <p>KPI metrics will be displayed here</p>
          </div>
        </div>
      `;
      break;
    default:
      newContent = `
        <div class="tab-content-item">
          <div class="content-header">
            <h4>Content Item</h4>
            <button class="remove-content" onclick="this.parentElement.parentElement.remove()">
              <i class="las la-times"></i>
            </button>
          </div>
          <div class="content-body">
            <p>Content added successfully</p>
          </div>
        </div>
      `;
  }

  // Hide placeholder if this is the first content item
  if (placeholder && contentArea.children.length === 1) {
    placeholder.style.display = "none";
  }

  // Add new content
  const contentElement = document.createElement("div");
  contentElement.innerHTML = newContent;
  contentArea.appendChild(contentElement.firstElementChild);
}

// Enable Tab Drag and Drop
function enableTabDragAndDrop(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  const tabNav = container.querySelector(".tab-nav");

  let draggedTab = null;

  tabNav.addEventListener("dragstart", (e) => {
    if (e.target.classList.contains("tab-item")) {
      draggedTab = e.target;
      e.target.style.opacity = "0.5";
    }
  });

  tabNav.addEventListener("dragend", (e) => {
    if (e.target.classList.contains("tab-item")) {
      e.target.style.opacity = "";
      draggedTab = null;
    }
  });

  tabNav.addEventListener("dragover", (e) => {
    e.preventDefault();
  });

  tabNav.addEventListener("drop", (e) => {
    e.preventDefault();
    if (
      draggedTab &&
      e.target.classList.contains("tab-item") &&
      e.target !== draggedTab
    ) {
      const rect = e.target.getBoundingClientRect();
      const next = (e.clientX - rect.left) / (rect.right - rect.left) > 0.5;

      tabNav.insertBefore(draggedTab, next ? e.target.nextSibling : e.target);
    }
  });

  // Enable draggable attribute on tab items
  const tabItems = container.querySelectorAll(".tab-item");
  tabItems.forEach((tab) => {
    tab.draggable = true;
  });
}

// Apply Tab Container Settings
function applyTabContainerSettings(containerId, settingsId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  const title = document.getElementById(`${settingsId}-title`).value;
  const tabPosition = document.getElementById(
    `${settingsId}-tab-position`
  ).value;
  const theme = document.getElementById(`${settingsId}-theme`).value;
  const maxTabs = document.getElementById(`${settingsId}-max-tabs`).value;
  const closeable = document.getElementById(`${settingsId}-closeable`).checked;
  const draggable = document.getElementById(`${settingsId}-draggable`).checked;

  // Update title
  const titleElement = container.querySelector(".widget-title span");
  if (titleElement) {
    titleElement.textContent = title;
  }

  // Update tab position
  const content = container.querySelector(".tab-container-content");
  if (tabPosition === "bottom") {
    content.classList.add("tabs-bottom");
  } else {
    content.classList.remove("tabs-bottom");
  }

  // Update theme
  container.className = `tab-container-widget theme-${theme}`;

  // Apply closeable setting
  const closeButtons = container.querySelectorAll(".tab-close");
  closeButtons.forEach((btn) => {
    btn.style.display = closeable ? "flex" : "none";
  });

  // Apply draggable setting
  const tabItems = container.querySelectorAll(".tab-item");
  tabItems.forEach((tab) => {
    tab.draggable = draggable;
  });

  // Store max tabs for future reference
  container.dataset.maxTabs = maxTabs;

  // Close the offcanvas
  const offcanvasElement = document.getElementById(settingsId);
  if (offcanvasElement) {
    const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvasElement);
    if (offcanvasInstance) {
      offcanvasInstance.hide();
    }
  }

  console.log(`Tab Container ${containerId} settings applied`);
}

// Refresh Tab Container
function refreshTabContainer(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  // Close any open panel
  closeTabPanel(containerId);

  // Show success message
  const defaultContent = container.querySelector(
    ".tab-container-default-content"
  );
  if (defaultContent) {
    const placeholder = defaultContent.querySelector(
      ".default-content-placeholder"
    );
    const originalContent = placeholder.innerHTML;

    placeholder.innerHTML = `
      <i class="las la-check-circle" style="color: #10b981;"></i>
      <h3>Refreshed!</h3>
      <p>Tab container has been refreshed successfully</p>
    `;

    setTimeout(() => {
      placeholder.innerHTML = originalContent;
    }, 2000);
  }
}

// Open Tab Panel with slide-up animation
function openTabPanel(containerId, panelType) {
  const container = document.getElementById(containerId);
  if (!container) return;

  // Close any currently open panel
  closeTabPanel(containerId);

  // Get panel elements
  const panel = document.getElementById(`${containerId}-panel-${panelType}`);
  const defaultContent = container.querySelector(
    ".tab-container-default-content"
  );

  if (!panel) return;

  // Update active tab state
  const tabs = container.querySelectorAll(".tab-nav-link");
  const clickedTab = container.querySelector(`[onclick*="${panelType}"]`);

  tabs.forEach((tab) => tab.classList.remove("active"));
  if (clickedTab) {
    clickedTab.classList.add("active");
  }

  // Hide default content
  if (defaultContent) {
    defaultContent.style.opacity = "0";
    setTimeout(() => {
      defaultContent.style.display = "none";
    }, 300);
  }

  // Show and animate panel
  setTimeout(() => {
    panel.classList.add("active");
    panel.style.display = "block";

    // Trigger slide-up animation
    setTimeout(() => {
      panel.style.transform = "translateY(0%)";
      panel.style.opacity = "1";
    }, 50);
  }, 300);

  // Store current panel reference
  container.setAttribute("data-current-panel", panelType);

  // Prevent scrolling on body when panel is open
  document.body.style.overflow = "hidden";
}

// Close Tab Panel with slide-down animation
function closeTabPanel(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  const currentPanel = container.getAttribute("data-current-panel");
  if (!currentPanel) return;

  const panel = document.getElementById(`${containerId}-panel-${currentPanel}`);
  const defaultContent = container.querySelector(
    ".tab-container-default-content"
  );

  if (panel) {
    // Animate panel out
    panel.style.transform = "translateY(100%)";
    panel.style.opacity = "0";

    setTimeout(() => {
      panel.classList.remove("active");
      panel.style.display = "none";
    }, 300);
  }

  // Show default content
  if (defaultContent) {
    setTimeout(() => {
      defaultContent.style.display = "block";
      defaultContent.style.opacity = "1";
    }, 150);
  }

  // Remove active state from tabs
  const tabs = container.querySelectorAll(".tab-nav-link");
  tabs.forEach((tab) => tab.classList.remove("active"));

  // Clear current panel reference
  container.removeAttribute("data-current-panel");

  // Restore body scrolling
  document.body.style.overflow = "";
}

// Add content to panel
function addContentToPanel(containerId, panelType, contentType) {
  const panel = document.getElementById(`${containerId}-panel-${panelType}`);
  if (!panel) return;

  const contentBody = panel.querySelector(".content-body");
  const placeholder = contentBody.querySelector(".content-placeholder");

  // Create new content element based on type
  let newContent;
  switch (contentType) {
    case "chart":
      newContent = createChartContent();
      break;
    case "kpi":
      newContent = createKPIContent();
      break;
    case "table":
      newContent = createTableContent();
      break;
    case "list":
      newContent = createListContent();
      break;
    case "model":
      newContent = createModelContent();
      break;
    case "trends":
      newContent = createTrendsContent();
      break;
    default:
      newContent = createDefaultContent(contentType);
  }

  // Hide placeholder and add new content
  if (placeholder) {
    placeholder.style.display = "none";
  }

  contentBody.appendChild(newContent);
}

// Content creation functions
function createChartContent() {
  const chartContainer = document.createElement("div");
  chartContainer.className = "chart-content";
  chartContainer.innerHTML = `
    <div class="chart-header">
      <h6><i class="las la-chart-bar"></i> Sample Chart</h6>
      <button class="btn btn-sm btn-outline-danger" onclick="removeContent(this)">
        <i class="las la-times"></i>
      </button>
    </div>
    <div class="chart-body">
      <canvas id="chart-${Date.now()}" width="400" height="200"></canvas>
    </div>
  `;
  return chartContainer;
}

function createKPIContent() {
  const kpiContainer = document.createElement("div");
  kpiContainer.className = "kpi-content";
  kpiContainer.innerHTML = `
    <div class="kpi-header">
      <h6><i class="las la-tachometer-alt"></i> Key Performance Indicator</h6>
      <button class="btn btn-sm btn-outline-danger" onclick="removeContent(this)">
        <i class="las la-times"></i>
      </button>
    </div>
    <div class="kpi-body">
      <div class="kpi-value">$1,234</div>
      <div class="kpi-label">Revenue</div>
      <div class="kpi-change positive">+12.5%</div>
    </div>
  `;
  return kpiContainer;
}

function createTableContent() {
  const tableContainer = document.createElement("div");
  tableContainer.className = "table-content";
  tableContainer.innerHTML = `
    <div class="table-header">
      <h6><i class="las la-table"></i> Data Table</h6>
      <button class="btn btn-sm btn-outline-danger" onclick="removeContent(this)">
        <i class="las la-times"></i>
      </button>
    </div>
    <div class="table-body">
      <table class="table table-sm">
        <thead>
          <tr>
            <th>Item</th>
            <th>Value</th>
            <th>Change</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Product A</td>
            <td>$250</td>
            <td class="text-success">+5%</td>
          </tr>
          <tr>
            <td>Product B</td>
            <td>$180</td>
            <td class="text-danger">-2%</td>
          </tr>
        </tbody>
      </table>
    </div>
  `;
  return tableContainer;
}

function createListContent() {
  const listContainer = document.createElement("div");
  listContainer.className = "list-content";
  listContainer.innerHTML = `
    <div class="list-header">
      <h6><i class="las la-list"></i> Data List</h6>
      <button class="btn btn-sm btn-outline-danger" onclick="removeContent(this)">
        <i class="las la-times"></i>
      </button>
    </div>
    <div class="list-body">
      <ul class="list-group">
        <li class="list-group-item d-flex justify-content-between align-items-center">
          Market Analysis
          <span class="badge bg-primary rounded-pill">14</span>
        </li>
        <li class="list-group-item d-flex justify-content-between align-items-center">
          Forecast Models
          <span class="badge bg-primary rounded-pill">7</span>
        </li>
        <li class="list-group-item d-flex justify-content-between align-items-center">
          Data Sources
          <span class="badge bg-primary rounded-pill">23</span>
        </li>
      </ul>
    </div>
  `;
  return listContainer;
}

function createModelContent() {
  const modelContainer = document.createElement("div");
  modelContainer.className = "model-content";
  modelContainer.innerHTML = `
    <div class="model-header">
      <h6><i class="las la-brain"></i> Forecasting Model</h6>
      <button class="btn btn-sm btn-outline-danger" onclick="removeContent(this)">
        <i class="las la-times"></i>
      </button>
    </div>
    <div class="model-body">
      <div class="model-info">
        <div class="model-name">Linear Regression Model</div>
        <div class="model-accuracy">Accuracy: 92.3%</div>
        <div class="model-status">
          <span class="badge bg-success">Active</span>
        </div>
      </div>
      <div class="model-actions">
        <button class="btn btn-sm btn-primary">Run Prediction</button>
        <button class="btn btn-sm btn-secondary">View Details</button>
      </div>
    </div>
  `;
  return modelContainer;
}

function createTrendsContent() {
  const trendsContainer = document.createElement("div");
  trendsContainer.className = "trends-content";
  trendsContainer.innerHTML = `
    <div class="trends-header">
      <h6><i class="las la-chart-line"></i> Market Trends</h6>
      <button class="btn btn-sm btn-outline-danger" onclick="removeContent(this)">
        <i class="las la-times"></i>
      </button>
    </div>
    <div class="trends-body">
      <div class="trend-item">
        <div class="trend-label">Upward Trend</div>
        <div class="trend-value text-success">+15.2%</div>
      </div>
      <div class="trend-item">
        <div class="trend-label">Market Volatility</div>
        <div class="trend-value text-warning">Medium</div>
      </div>
      <div class="trend-item">
        <div class="trend-label">Forecast Confidence</div>
        <div class="trend-value text-info">87%</div>
      </div>
    </div>
  `;
  return trendsContainer;
}

function createDefaultContent(contentType) {
  const defaultContainer = document.createElement("div");
  defaultContainer.className = "default-content";
  defaultContainer.innerHTML = `
    <div class="content-header">
      <h6><i class="las la-cube"></i> ${
        contentType.charAt(0).toUpperCase() + contentType.slice(1)
      } Content</h6>
      <button class="btn btn-sm btn-outline-danger" onclick="removeContent(this)">
        <i class="las la-times"></i>
      </button>
    </div>
    <div class="content-body">
      <p>This is a ${contentType} content block.</p>
    </div>
  `;
  return defaultContainer;
}

// Remove content function
function removeContent(button) {
  const contentElement = button.closest(
    ".chart-content, .kpi-content, .table-content, .list-content, .model-content, .trends-content, .default-content"
  );
  if (contentElement) {
    contentElement.remove();

    // Check if we need to show placeholder again
    const contentBody = button.closest(".content-body");
    const hasOtherContent = contentBody.querySelector(
      ".chart-content, .kpi-content, .table-content, .list-content, .model-content, .trends-content, .default-content"
    );

    if (!hasOtherContent) {
      const placeholder = contentBody.querySelector(".content-placeholder");
      if (placeholder) {
        placeholder.style.display = "block";
      }
    }
  }
}

// ESC key handler for closing panels
document.addEventListener("keydown", function (event) {
  if (event.key === "Escape") {
    // Find all tab containers with open panels
    const containers = document.querySelectorAll(
      ".tab-container-widget[data-current-panel]"
    );
    containers.forEach((container) => {
      closeTabPanel(container.id);
    });
  }
});

// Initialize nested grids for each tab panel
function initializeNestedGrids(containerId) {
  // Get the actual tab configuration instead of hardcoded panels
  const tabConfig = getTabConfiguration(containerId);

  // Get all enabled tabs of type 'nestedGrid'
  const nestedGridTabs = tabConfig.tabs.filter(
    (tab) => tab.enabled && tab.type === "nestedGrid"
  );

  nestedGridTabs.forEach((tab) => {
    const panelType = tab.id;
    const gridElement = document.getElementById(
      `${containerId}-${panelType}-grid`
    );
    if (gridElement) {
      try {
        // Initialize nested GridStack
        const nestedGrid = GridStack.init(
          {
            cellHeight: 60,
            margin: 8,
            minRow: 4,
            maxRow: 20,
            animate: true,
            removeTimeout: 100,
            acceptWidgets: true,
            dragOut: true,
            float: true,
            column: 12,
            resizable: {
              handles: "se, sw, ne, nw",
              autoHide: false, // Keep handles visible for nested grids
            },
            draggable: {
              handle: ".widget-header",
            },
          },
          gridElement
        );

        // Store the nested grid instance
        nestedGrids.set(`${containerId}-${panelType}`, nestedGrid);

        // Handle widget addition
        nestedGrid.on("added", function (event, items) {
          items.forEach((item) => {
            const widget = item.el;
            const placeholder = document.getElementById(
              `${containerId}-${panelType}-placeholder`
            );
            if (placeholder) {
              placeholder.style.display = "none";
            }

            // Initialize any widget-specific functionality
            initializeNestedWidget(widget);
          });
        });

        // Handle widget removal
        nestedGrid.on("removed", function (event, items) {
          const remainingWidgets = nestedGrid.getGridItems();
          if (remainingWidgets.length === 0) {
            const placeholder = document.getElementById(
              `${containerId}-${panelType}-placeholder`
            );
            if (placeholder) {
              placeholder.style.display = "flex";
            }
          }
        });

        // Enable drag and drop from main grid
        setupNestedGridDragDrop(containerId, panelType, nestedGrid);
      } catch (error) {
        console.error(
          `Error initializing nested grid for ${tab.name} (${panelType}):`,
          error
        );
      }
    }
  });
}

// Setup drag and drop between main grid and nested grids
function setupNestedGridDragDrop(containerId, panelType, nestedGrid) {
  const mainGrid = grid; // Reference to main GridStack instance

  // Allow dragging from main grid to nested grid
  if (mainGrid && nestedGrid) {
    // Configure the connection between grids
    nestedGrid.acceptWidgets = true;

    // Handle drag over events
    const gridElement = document.getElementById(
      `${containerId}-${panelType}-grid`
    );
    if (gridElement) {
      gridElement.addEventListener("dragover", function (e) {
        e.preventDefault();
        gridElement.classList.add("drag-over");
      });

      gridElement.addEventListener("dragleave", function (e) {
        if (!gridElement.contains(e.relatedTarget)) {
          gridElement.classList.remove("drag-over");
        }
      });

      gridElement.addEventListener("drop", function (e) {
        gridElement.classList.remove("drag-over");
      });
    }
  }
}

// Initialize nested widget functionality
function initializeNestedWidget(widget) {
  // Add nested widget class
  widget.classList.add("nested-widget");

  // Ensure resize handles are properly initialized for nested widgets
  setTimeout(() => {
    const resizeHandles = widget.querySelectorAll(".ui-resizable-handle");
    if (resizeHandles.length === 0) {
      console.log("Adding missing resize handles to nested widget:", widget.id);

      // Force jQuery UI resizable if GridStack didn't add handles
      if (window.jQuery && jQuery(widget).resizable) {
        try {
          jQuery(widget).resizable({
            handles: "se, sw, ne, nw",
            autoHide: false,
            containment: "parent",
          });
        } catch (error) {
          console.error("Error adding resize handles to nested widget:", error);
        }
      }
    }
  }, 100);

  // Initialize specific widget types
  const widgetType = widget.className.match(/(\w+)-widget/);
  if (widgetType) {
    const type = widgetType[1];

    // Initialize based on widget type
    switch (type) {
      case "kpi":
        if (typeof initializeKPI === "function") {
          initializeKPI(widget.id);
        }
        break;
      case "chart":
      case "pie":
      case "line":
      case "bar":
        // Initialize chart widgets
        setTimeout(() => {
          if (typeof initializeChart === "function") {
            initializeChart(widget.id);
          }
        }, 500);
        break;
      default:
        console.log(`Nested widget initialized: ${type}`);
    }
  }
}

// Clear nested grid
function clearNestedGrid(containerId, panelType) {
  const nestedGrid = nestedGrids.get(`${containerId}-${panelType}`);
  if (nestedGrid) {
    const items = nestedGrid.getGridItems();
    if (items.length > 0) {
      if (
        confirm(
          `Are you sure you want to remove all ${items.length} widgets from this grid?`
        )
      ) {
        nestedGrid.removeAll();

        // Show placeholder
        const placeholder = document.getElementById(
          `${containerId}-${panelType}-placeholder`
        );
        if (placeholder) {
          placeholder.style.display = "flex";
        }
      }
    } else {
      alert("Grid is already empty");
    }
  }
}

// Get nested grid statistics
function getNestedGridStats(containerId, panelType) {
  const nestedGrid = nestedGrids.get(`${containerId}-${panelType}`);
  if (nestedGrid) {
    const items = nestedGrid.getGridItems();
    return {
      totalWidgets: items.length,
      gridSize: {
        columns: nestedGrid.column,
        rows: nestedGrid.getRow(),
      },
    };
  }
  return null;
}

// Enhanced initialization function for tab container widgets with cross-grid drag-drop
function initializeCrossGridDragDrop() {
  // Wait for the main grid to be available
  const checkMainGrid = () => {
    if (window.grid) {
      setupMainGridDragDrop();
    } else {
      setTimeout(checkMainGrid, 100);
    }
  };
  checkMainGrid();
}

// Setup drag-drop for the main grid to work with nested grids
function setupMainGridDragDrop() {
  if (!window.grid) return;

  // Enable accepting widgets from nested grids using proper GridStack events
  window.grid.on("added", function (event, items) {
    console.log("Widget added to main grid:", items);

    // Handle widgets that were moved from nested grids
    items.forEach((item) => {
      if (item.el && item.el.dataset && item.el.dataset.sourceGrid) {
        console.log("Widget moved from nested grid to main grid");
        // Clean up any source grid references
        delete item.el.dataset.sourceGrid;
      }
      // Auto-initialize section container widgets after drag-in
      if (item.el) {
        const sectionContainer = item.el.querySelector(
          ".section-container-widget"
        );
        if (sectionContainer) {
          // Find the section-content div and its id
          const sectionContent =
            sectionContainer.querySelector(".section-content");
          if (sectionContent && window.initSectionContainer) {
            window.initSectionContainer(sectionContent.id);
          }
        }
      }
    });
  });

  // Track when widgets are removed from main grid
  window.grid.on("removed", function (event, items) {
    console.log("Widget removed from main grid:", items);
  });

  // Setup drag start functionality for main grid widgets
  window.grid.on("dragstart", function (event, ui) {
    console.log("Drag started from main grid");

    // Add visual feedback
    document.body.classList.add("main-grid-dragging");

    // Store current overflow state and temporarily allow scrolling during drag
    document.body.setAttribute(
      "data-pre-drag-overflow",
      document.body.style.overflow || ""
    );
    document.body.style.overflow = "";

    // Highlight all nested grids as potential drop targets
    const nestedGrids = document.querySelectorAll(".nested-grid .grid-stack");
    nestedGrids.forEach((grid) => {
      grid.classList.add("potential-drop-target");
    });
  });

  window.grid.on("dragstop", function (event, ui) {
    console.log("Drag stopped from main grid");

    // Remove visual feedback
    document.body.classList.remove("main-grid-dragging");

    // Restore previous overflow state
    const preDropOverflow = document.body.getAttribute(
      "data-pre-drag-overflow"
    );
    if (preDropOverflow !== null) {
      document.body.style.overflow = preDropOverflow;
      document.body.removeAttribute("data-pre-drag-overflow");
    }

    // Remove highlight from nested grids
    const nestedGrids = document.querySelectorAll(".nested-grid .grid-stack");
    nestedGrids.forEach((grid) => {
      grid.classList.remove("potential-drop-target");
    });
  });

  // Handle layout changes
  window.grid.on("change", function (event, items) {
    console.log("Main grid layout changed:", items);
    // You can add logic here to handle layout changes if needed
  });
}

// Initialize cross-grid functionality when document is ready
document.addEventListener("DOMContentLoaded", function () {
  initializeCrossGridDragDrop();

  // Global cleanup for any orphaned modals or focus traps
  cleanupOrphanedModals();

  // Add global error handler for Bootstrap modal errors
  document.addEventListener("shown.bs.modal", function (event) {
    // Ensure modal has proper focus management
    const modal = event.target;
    const firstFocusable = modal.querySelector(
      'input:not([type="hidden"]), select, textarea, button:not([data-bs-dismiss])'
    );
    if (firstFocusable) {
      setTimeout(() => {
        firstFocusable.focus();
      }, 100);
    }
  });

  // Handle modal hidden events
  document.addEventListener("hidden.bs.modal", function (event) {
    const modal = event.target;
    const containerId = modal.id.replace("-tab-modal", "");
    if (containerId && containerId.startsWith("tabContainer")) {
      cleanupModalEventHandlers(containerId);

      // Ensure proper focus management after modal closes
      setTimeout(() => {
        const container = document.getElementById(containerId);
        if (container) {
          const settingsButton = container.querySelector(
            '.widget-action-btn[title="Settings"]'
          );
          if (settingsButton) {
            settingsButton.focus();
          }
        }
      }, 100);
    }
  });

  // Add proper focus management for modal showing
  document.addEventListener("show.bs.modal", function (event) {
    const modal = event.target;

    // Close any open offcanvas when a modal is about to show
    const openOffcanvas = document.querySelector(".offcanvas.show");
    if (openOffcanvas) {
      const offcanvasInstance = bootstrap.Offcanvas.getInstance(openOffcanvas);
      if (offcanvasInstance) {
        console.log("Closing conflicting offcanvas before modal");
        offcanvasInstance.hide();
      }
    }

    // Remove any existing aria-hidden attributes that might conflict
    modal.removeAttribute("aria-hidden");

    // Ensure modal has proper aria attributes
    modal.setAttribute("aria-modal", "true");
    modal.setAttribute("role", "dialog");
  });

  // Add focus management for modal shown event
  document.addEventListener("shown.bs.modal", function (event) {
    const modal = event.target;

    // Find the first focusable element that's not a close button
    const firstFocusable = modal.querySelector(
      'input:not([type="hidden"]):not([disabled]), select:not([disabled]), textarea:not([disabled])'
    );

    if (firstFocusable) {
      setTimeout(() => {
        firstFocusable.focus();
      }, 100);
    }
  });

  // Add focus management for modal hiding
  document.addEventListener("hide.bs.modal", function (event) {
    const modal = event.target;

    // Remove focus from any focused elements within the modal
    const focusedElement = modal.querySelector(":focus");
    if (focusedElement) {
      focusedElement.blur();
    }

    // Clear any active element focus
    if (document.activeElement && modal.contains(document.activeElement)) {
      document.activeElement.blur();
    }
  });

  // Add error handling for focus trap errors
  window.addEventListener("error", function (event) {
    if (
      event.error &&
      event.error.message &&
      event.error.message.includes("Maximum call stack size exceeded")
    ) {
      const focusTrapError =
        event.error.stack && event.error.stack.includes("focustrap.js");
      if (focusTrapError) {
        console.error("Focus trap error detected, attempting cleanup...");

        // Close all modals
        const openModals = document.querySelectorAll(".modal.show");
        openModals.forEach((modal) => {
          const modalInstance = bootstrap.Modal.getInstance(modal);
          if (modalInstance) {
            modalInstance.hide();
          }
        });

        // Close all offcanvas
        const openOffcanvas = document.querySelectorAll(".offcanvas.show");
        openOffcanvas.forEach((offcanvas) => {
          const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvas);
          if (offcanvasInstance) {
            offcanvasInstance.hide();
          }
        });

        // Prevent the error from propagating
        event.preventDefault();
        return false;
      }
    }
  });
});

// Global cleanup function for orphaned modals
function cleanupOrphanedModals() {
  // Find and clean up any orphaned tab modals
  const orphanedModals = document.querySelectorAll('[id$="-tab-modal"]');
  orphanedModals.forEach((modal) => {
    const containerId = modal.id.replace("-tab-modal", "");
    const container = document.getElementById(containerId);

    // If the container doesn't exist, clean up the modal
    if (!container) {
      console.log(`Cleaning up orphaned modal: ${modal.id}`);

      // Close modal if open
      const modalInstance = bootstrap.Modal.getInstance(modal);
      if (modalInstance) {
        modalInstance.hide();
      }

      // Remove event handlers
      cleanupModalEventHandlers(containerId);

      // Remove from DOM after a delay
      setTimeout(() => {
        if (modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
      }, 300);
    }
  });
}

// Fix resize handles for tab container widgets
function fixTabContainerResizeHandles() {
  // Find all tab container widgets
  const tabContainers = document.querySelectorAll(".tab-container-widget");

  tabContainers.forEach((container) => {
    // Find the parent grid-stack-item
    const gridItem = container.closest(".grid-stack-item");

    if (gridItem && window.grid) {
      // Check if resize handles exist
      const resizeHandles = gridItem.querySelectorAll(".ui-resizable-handle");

      if (resizeHandles.length === 0) {
        console.log("Fixing resize handles for tab container:", container.id);

        // Force GridStack to re-initialize this widget
        try {
          // Remove and re-add the widget to force proper initialization
          const node = gridItem.gridstackNode;
          if (node) {
            // Get current position and size
            const options = {
              x: node.x,
              y: node.y,
              w: node.w,
              h: node.h,
              id: node.id || container.id,
              content: container.outerHTML,
            };

            // Remove the old widget
            window.grid.removeWidget(gridItem, false);

            // Add it back with proper structure
            setTimeout(() => {
              const newWidget = window.grid.addWidget(options);

              // Re-initialize the tab container functionality
              if (container.id) {
                setTimeout(() => {
                  initializeTabContainer(container.id);
                  initializeNestedGrids(container.id);
                }, 50);
              }
            }, 50);
          }
        } catch (error) {
          console.error(
            "Error fixing resize handles for tab container:",
            error
          );
        }
      }
    }
  });
}

// Auto-fix resize handles when page loads
document.addEventListener("DOMContentLoaded", () => {
  setTimeout(fixTabContainerResizeHandles, 1000);

  // Also run cleanup after a longer delay to catch any late-loading elements
  setTimeout(cleanupOrphanedModals, 2000);
});

// Export the fix function for manual use
window.fixTabContainerResizeHandles = fixTabContainerResizeHandles;

// Export cleanup function for manual use
window.cleanupTabContainer = cleanupTabContainer;
window.cleanupOrphanedModals = cleanupOrphanedModals;

// Enhanced Tab Management Functions

// Clean up event handlers for a specific container
function cleanupModalEventHandlers(containerId) {
  const handler = modalEventHandlers.get(containerId);
  if (handler) {
    const iconInput = document.getElementById(`${containerId}-tab-icon`);
    if (iconInput) {
      iconInput.removeEventListener("input", handler);
    }
    modalEventHandlers.delete(containerId);
  }
}

// Show Add Tab Modal from Settings (handles offcanvas conflict)
function showAddTabModalFromSettings(containerId) {
  const settingsId = `tabContainerSettings${containerId.replace(
    "tabContainer",
    ""
  )}`;
  const offcanvasElement = document.getElementById(settingsId);

  if (offcanvasElement) {
    const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvasElement);

    if (offcanvasInstance) {
      // Close the offcanvas first
      offcanvasInstance.hide();

      // Wait for offcanvas to fully close before opening modal
      offcanvasElement.addEventListener(
        "hidden.bs.offcanvas",
        function onOffcanvasHidden() {
          // Remove this event listener to prevent multiple calls
          offcanvasElement.removeEventListener(
            "hidden.bs.offcanvas",
            onOffcanvasHidden
          );

          // Now safely open the modal
          setTimeout(() => {
            showAddTabModal(containerId);
          }, 100);
        }
      );
    } else {
      // Offcanvas not open, directly show modal
      showAddTabModal(containerId);
    }
  } else {
    // Offcanvas element not found, directly show modal
    showAddTabModal(containerId);
  }
}

// Show Add Tab Modal
function showAddTabModal(containerId) {
  console.log("showAddTabModal called with containerId:", containerId);

  const modalElement = document.getElementById(`${containerId}-tab-modal`);
  if (!modalElement) {
    console.error(`Modal ${containerId}-tab-modal not found`);
    alert("Error: Modal not found. Please try refreshing the page.");
    return;
  }

  console.log("Modal element found:", modalElement);

  const modal = new bootstrap.Modal(modalElement);
  const tabConfig = getTabConfiguration(containerId);

  console.log("Resetting form for new tab...");

  // Reset form
  const editIdElement = document.getElementById(`${containerId}-tab-edit-id`);
  const nameElement = document.getElementById(`${containerId}-tab-name`);
  const iconElement = document.getElementById(`${containerId}-tab-icon`);
  const iconPreviewElement = document.getElementById(
    `${containerId}-tab-icon-preview`
  );
  const typeElement = document.getElementById(`${containerId}-tab-type`);
  const orderElement = document.getElementById(`${containerId}-tab-order`);

  console.log("Form elements for reset:", {
    editIdElement,
    nameElement,
    iconElement,
    iconPreviewElement,
    typeElement,
    orderElement,
  });

  if (editIdElement) editIdElement.value = "";
  if (nameElement) nameElement.value = "";
  if (iconElement) iconElement.value = "las la-cube";
  if (iconPreviewElement) iconPreviewElement.className = "las la-cube";
  if (typeElement) typeElement.value = "nestedGrid";
  if (orderElement) orderElement.value = tabConfig.tabs.length + 1;

  // Update modal title
  const modalTitleElement = document.getElementById(
    `${containerId}-tab-modal-title`
  );
  if (modalTitleElement) {
    modalTitleElement.textContent = "Add New Tab";
    console.log("Modal title updated");
  } else {
    console.warn("Modal title element not found");
  }

  // Update type description
  console.log("Updating type description...");
  updateTabTypeDescription(containerId);

  // Clean up existing event listeners first
  console.log("Cleaning up existing event listeners...");
  cleanupModalEventHandlers(containerId);

  // Create and store new event handler
  const iconInputHandler = function () {
    updateIconPreview(containerId);
  };

  modalEventHandlers.set(containerId, iconInputHandler);

  // Add event listener for icon input
  const iconInput = document.getElementById(`${containerId}-tab-icon`);
  if (iconInput) {
    iconInput.addEventListener("input", iconInputHandler);
    console.log("Icon input event listener added");
  } else {
    console.warn("Icon input element not found for event listener");
  }

  console.log("Showing modal...");
  modal.show();

  // Verify modal is shown
  setTimeout(() => {
    const isShown = modalElement.classList.contains("show");
    console.log("Modal shown status:", isShown);
    if (!isShown) {
      console.error("Modal failed to show!");
    }
  }, 500);
}

// Edit Tab from Settings (handles offcanvas conflict)
function editTabFromSettings(containerId, tabId) {
  const settingsId = `tabContainerSettings${containerId.replace(
    "tabContainer",
    ""
  )}`;
  const offcanvasElement = document.getElementById(settingsId);

  if (offcanvasElement) {
    const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvasElement);

    if (offcanvasInstance) {
      // Close the offcanvas first
      offcanvasInstance.hide();

      // Wait for offcanvas to fully close before opening modal
      offcanvasElement.addEventListener(
        "hidden.bs.offcanvas",
        function onOffcanvasHidden() {
          // Remove this event listener to prevent multiple calls
          offcanvasElement.removeEventListener(
            "hidden.bs.offcanvas",
            onOffcanvasHidden
          );

          // Now safely open the edit modal
          setTimeout(() => {
            editTab(containerId, tabId);
          }, 100);
        }
      );
    } else {
      // Offcanvas not open, directly show modal
      editTab(containerId, tabId);
    }
  } else {
    // Offcanvas element not found, directly show modal
    editTab(containerId, tabId);
  }
}

// Edit Tab
function editTab(containerId, tabId) {
  const tabConfig = getTabConfiguration(containerId);
  const tab = tabConfig.tabs.find((t) => t.id === tabId);

  if (!tab) return;

  const modalElement = document.getElementById(`${containerId}-tab-modal`);
  if (!modalElement) {
    console.error(`Modal ${containerId}-tab-modal not found`);
    return;
  }

  const modal = new bootstrap.Modal(modalElement);

  // Fill form with tab data
  document.getElementById(`${containerId}-tab-edit-id`).value = tabId;
  document.getElementById(`${containerId}-tab-name`).value = tab.name;
  document.getElementById(`${containerId}-tab-icon`).value = tab.icon;
  document.getElementById(`${containerId}-tab-icon-preview`).className =
    tab.icon;
  document.getElementById(`${containerId}-tab-type`).value = tab.type;
  document.getElementById(`${containerId}-tab-order`).value = tab.order;

  // Update modal title
  document.getElementById(`${containerId}-tab-modal-title`).textContent =
    "Edit Tab";

  // Update type description
  updateTabTypeDescription(containerId);

  // Clean up existing event listeners first
  cleanupModalEventHandlers(containerId);

  // Create and store new event handler
  const iconInputHandler = function () {
    updateIconPreview(containerId);
  };

  modalEventHandlers.set(containerId, iconInputHandler);

  // Add event listener for icon input
  const iconInput = document.getElementById(`${containerId}-tab-icon`);
  if (iconInput) {
    iconInput.addEventListener("input", iconInputHandler);
  }

  modal.show();
}

// Save Tab Configuration
function saveTabContainerConfiguration(containerId) {
  // DEBUGGING: Check if function is called at all
  console.log(
    "🔥 SAVE TAB BUTTON CLICKED! Function called with containerId:",
    containerId
  );
  console.log("🔥 This confirms the button click handler is working");

  console.log(
    "saveTabContainerConfiguration called with containerId:",
    containerId
  );

  try {
    const tabConfig = getTabConfiguration(containerId);
    console.log("Current tab config:", tabConfig);

    const editId = document.getElementById(`${containerId}-tab-edit-id`).value;
    const nameElement = document.getElementById(`${containerId}-tab-name`);
    const iconElement = document.getElementById(`${containerId}-tab-icon`);
    const typeElement = document.getElementById(`${containerId}-tab-type`);
    const orderElement = document.getElementById(`${containerId}-tab-order`);

    console.log("Form elements found:", {
      editId: editId,
      nameElement: nameElement,
      iconElement: iconElement,
      typeElement: typeElement,
      orderElement: orderElement,
    });

    if (!nameElement || !iconElement || !typeElement || !orderElement) {
      console.error("Missing form elements!");
      alert("Error: Form elements not found. Please try refreshing the page.");
      return;
    }

    const name = nameElement.value.trim();
    const icon = iconElement.value.trim();
    const type = typeElement.value;
    const order = parseInt(orderElement.value);

    console.log("Form values:", { name, icon, type, order });

    if (!name) {
      console.log("Validation failed: Tab name is required");
      alert("Tab name is required");
      return;
    }

    if (editId) {
      console.log("Editing existing tab:", editId);
      // Edit existing tab
      const tabIndex = tabConfig.tabs.findIndex((t) => t.id === editId);
      if (tabIndex !== -1) {
        tabConfig.tabs[tabIndex] = {
          ...tabConfig.tabs[tabIndex],
          name,
          icon,
          type,
          order,
        };
        console.log("Updated existing tab at index:", tabIndex);
      } else {
        console.error("Tab to edit not found:", editId);
      }
    } else {
      console.log("Adding new tab");
      // Add new tab
      if (tabConfig.tabs.length >= tabConfig.settings.maxTabs) {
        console.log("Max tabs reached:", tabConfig.settings.maxTabs);
        alert(`Maximum ${tabConfig.settings.maxTabs} tabs allowed`);
        return;
      }

      const newTab = {
        id: `tab-${Date.now()}`,
        name,
        icon,
        type,
        enabled: true,
        order,
        removable: true,
      };

      tabConfig.tabs.push(newTab);
      console.log("Added new tab:", newTab);
    }

    // Update configuration
    console.log("Updating tab configuration...");
    updateTabConfiguration(containerId, tabConfig);

    // Refresh the container
    console.log("Refreshing tab container structure...");
    refreshTabContainerStructure(containerId);

    // Close modal properly with focus management
    console.log("Closing modal...");
    const modalElement = document.getElementById(`${containerId}-tab-modal`);
    if (modalElement) {
      // Remove focus from any focused elements within the modal before closing
      const focusedElement = modalElement.querySelector(":focus");
      if (focusedElement) {
        focusedElement.blur();
      }

      // Clear any active element focus if it's within the modal
      if (
        document.activeElement &&
        modalElement.contains(document.activeElement)
      ) {
        document.activeElement.blur();
      }

      const modal = bootstrap.Modal.getInstance(modalElement);
      if (modal) {
        modal.hide();
        console.log("Modal close initiated");
      } else {
        console.warn("Modal instance not found");
        // Fallback: manually hide modal
        modalElement.style.display = "none";
        modalElement.classList.remove("show");
        modalElement.setAttribute("aria-hidden", "true");
        modalElement.removeAttribute("aria-modal");

        // Remove backdrop if exists
        const backdrop = document.querySelector(".modal-backdrop");
        if (backdrop) {
          backdrop.remove();
        }

        // Restore body classes
        document.body.classList.remove("modal-open");
        document.body.style.overflow = "";
        document.body.style.paddingRight = "";
      }
    } else {
      console.error("Modal element not found:", `${containerId}-tab-modal`);
    }

    // Update settings panel if open
    console.log("Updating settings panel...");
    const settingsId = `tabContainerSettings${containerId.replace(
      "tabContainer",
      ""
    )}`;
    const tabList = document.getElementById(`${settingsId}-tab-list`);
    if (tabList) {
      tabList.innerHTML = generateTabManagementList(containerId, tabConfig);
      console.log("Settings panel updated");
    } else {
      console.log("Settings panel not found or not open");
    }

    console.log("saveTabContainerConfiguration completed successfully");
  } catch (error) {
    console.error("Error in saveTabContainerConfiguration:", error);
    alert(
      "An error occurred while saving the tab. Please check the console for details."
    );
  }
}

// Delete Tab
function deleteTab(containerId, tabId) {
  const tabConfig = getTabConfiguration(containerId);
  const tab = tabConfig.tabs.find((t) => t.id === tabId);

  if (!tab || !tab.removable) return;

  if (confirm(`Are you sure you want to delete the "${tab.name}" tab?`)) {
    // Remove tab from configuration
    tabConfig.tabs = tabConfig.tabs.filter((t) => t.id !== tabId);

    // Update configuration
    updateTabConfiguration(containerId, tabConfig);

    // Refresh the container
    refreshTabContainerStructure(containerId);

    // Update settings panel if open
    const settingsId = `tabContainerSettings${containerId.replace(
      "tabContainer",
      ""
    )}`;
    const tabList = document.getElementById(`${settingsId}-tab-list`);
    if (tabList) {
      tabList.innerHTML = generateTabManagementList(containerId, tabConfig);
    }
  }
}

// Toggle Tab Enabled State
function toggleTabEnabled(containerId, tabId) {
  const tabConfig = getTabConfiguration(containerId);
  const tab = tabConfig.tabs.find((t) => t.id === tabId);

  if (!tab) return;

  tab.enabled = !tab.enabled;

  // Update configuration
  updateTabConfiguration(containerId, tabConfig);

  // Refresh the container
  refreshTabContainerStructure(containerId);

  // Update settings panel if open
  const settingsId = `tabContainerSettings${containerId.replace(
    "tabContainer",
    ""
  )}`;
  const tabList = document.getElementById(`${settingsId}-tab-list`);
  if (tabList) {
    tabList.innerHTML = generateTabManagementList(containerId, tabConfig);
  }
}

// Update Tab Type Description
function updateTabTypeDescription(containerId) {
  const typeSelect = document.getElementById(`${containerId}-tab-type`);
  const description = document.getElementById(
    `${containerId}-tab-type-description`
  );

  if (typeSelect && description) {
    if (typeSelect.value === "nestedGrid") {
      description.textContent =
        "Nested Grid: Provides a droppable grid area for widgets (like Analysis, Forecast, Data tabs)";
    } else {
      description.textContent =
        "Simple Content: Basic content area for text, lists, and simple elements";
    }
  }
}

// Update Icon Preview
function updateIconPreview(containerId) {
  const iconInput = document.getElementById(`${containerId}-tab-icon`);
  const iconPreview = document.getElementById(
    `${containerId}-tab-icon-preview`
  );

  if (iconInput && iconPreview) {
    iconPreview.className = iconInput.value || "las la-cube";
  }
}

// Refresh Tab Container Structure
function refreshTabContainerStructure(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  const tabConfig = getTabConfiguration(containerId);

  // Update tab navigation
  const tabNav = container.querySelector(".tab-nav-list");
  if (tabNav) {
    tabNav.innerHTML = generateTabNavigationHTML(containerId, tabConfig);
  }

  // Update tab panels
  const tabBody = container.querySelector(".tab-container-body");
  if (tabBody) {
    // Remove existing panels
    const existingPanels = tabBody.querySelectorAll(".tab-slideout-panel");
    existingPanels.forEach((panel) => panel.remove());

    // Add new panels
    const newPanelsHTML = generateTabPanelsHTML(containerId, tabConfig);
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = newPanelsHTML;

    while (tempDiv.children.length > 0) {
      tabBody.appendChild(tempDiv.children[0]);
    }
  }

  // Reinitialize nested grids for new tabs
  setTimeout(() => {
    initializeNestedGrids(containerId);
  }, 100);
}

// Clear Tab Content (for simple content tabs)
function clearTabContent(containerId, tabId) {
  const contentContainer = document.getElementById(
    `${containerId}-${tabId}-content`
  );
  if (
    contentContainer &&
    confirm("Are you sure you want to clear all content from this tab?")
  ) {
    contentContainer.innerHTML = `
      <div class="content-placeholder">
        <i class="las la-cube"></i>
        <h5>Content Cleared</h5>
        <p>Add your content here</p>
        <div class="content-actions">
          <button class="btn btn-primary btn-sm" onclick="addContentToTab('${containerId}', '${tabId}', 'text')">
            <i class="las la-file-alt"></i> Add Text
          </button>
          <button class="btn btn-secondary btn-sm" onclick="addContentToTab('${containerId}', '${tabId}', 'list')">
            <i class="las la-list"></i> Add List
          </button>
        </div>
      </div>
    `;
  }
}

// Cleanup function for when tab containers are removed
function cleanupTabContainer(containerId) {
  // Clean up modal event handlers
  cleanupModalEventHandlers(containerId);

  // Clean up nested grids
  const nestedGridKeys = Array.from(nestedGrids.keys()).filter((key) =>
    key.startsWith(containerId)
  );
  nestedGridKeys.forEach((key) => {
    const grid = nestedGrids.get(key);
    if (grid && typeof grid.destroy === "function") {
      try {
        grid.destroy();
      } catch (e) {
        console.warn("Error destroying nested grid:", e);
      }
    }
    nestedGrids.delete(key);
  });

  // Clean up tab configuration
  tabConfigurations.delete(containerId);

  // Remove modal and offcanvas elements
  const modal = document.getElementById(`${containerId}-tab-modal`);
  if (modal) {
    // Close modal if open
    const modalInstance = bootstrap.Modal.getInstance(modal);
    if (modalInstance) {
      modalInstance.hide();
    }
    // Remove from DOM
    setTimeout(() => {
      if (modal.parentNode) {
        modal.parentNode.removeChild(modal);
      }
    }, 300);
  }

  const settingsId = `tabContainerSettings${containerId.replace(
    "tabContainer",
    ""
  )}`;
  const offcanvas = document.getElementById(settingsId);
  if (offcanvas) {
    // Close offcanvas if open
    const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvas);
    if (offcanvasInstance) {
      offcanvasInstance.hide();
    }
    // Remove from DOM
    setTimeout(() => {
      if (offcanvas.parentNode) {
        offcanvas.parentNode.removeChild(offcanvas);
      }
    }, 300);
  }

  // Remove from localStorage
  try {
    localStorage.removeItem(`tabConfig_${containerId}`);
  } catch (e) {
    console.warn("Error removing tab configuration from localStorage:", e);
  }

  console.log(`Tab container ${containerId} cleaned up`);
}

// Enhanced error handling for modal operations
function safeModalOperation(containerId, operation) {
  try {
    const modalElement = document.getElementById(`${containerId}-tab-modal`);
    if (!modalElement) {
      console.error(`Modal ${containerId}-tab-modal not found`);
      return false;
    }

    return operation(modalElement);
  } catch (error) {
    console.error(`Error in modal operation for ${containerId}:`, error);
    return false;
  }
}
