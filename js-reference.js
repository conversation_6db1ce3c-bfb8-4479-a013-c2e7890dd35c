document.write(`
        <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/map.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
    <!-- amCharts 5 Geodata -->
    <script src="https://cdn.amcharts.com/lib/5/geodata/worldLow.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/geodata/data/countries2.js"></script>
    <!-- Other amCharts modules -->
    <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
    <!-- amCharts 5 Stock Chart -->
    <script src="https://cdn.amcharts.com/lib/5/stock.js"></script>
    <!-- amCharts 5 Word Cloud -->
    <script src="https://cdn.amcharts.com/lib/5/wc.js"></script>
    <!-- amCharts 5 Radar Charts (needed for Gauge) -->
    <script src="https://cdn.amcharts.com/lib/5/radar.js"></script>
    <!-- Add country level geodata -->
    <script src="https://cdn.amcharts.com/lib/5/geodata/worldLow.js"></script>
    <!-- Add country-specific geodata -->
    <script src="https://cdn.amcharts.com/lib/5/geodata/continentsLow.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/geodata/worldLow.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/geodata/indiaHigh.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/geodata/chinaHigh.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/geodata/usaHigh.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/geodata/russiaHigh.js"></script>
    <!-- Add more country geodata as needed -->
    <!-- amCharts 4 Core -->
    <script src="https://cdn.amcharts.com/lib/4/core.js"></script>
    <script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
    <script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
    <script src="https://cdn.amcharts.com/lib/4/maps.js"></script>
    <!-- Add this before your chart widget scripts -->
    <script src="js/chartConfig.js"></script>
    <!-- Pie Chart Widget -->
    <script src="js/pieChartWidget.js"></script>

    <!-- Bar Chart Widget -->
    <script src="js/barChartWidget.js"></script>

    <!-- Line Chart Widget -->
    <script src="js/lineChartWidget.js"></script>

    <!-- Nested Donut Chart Widget -->
    <script src="js/nestedDonutChartWidget.js"></script>
    <!-- Pie of Pie Chart Widget -->
    <script src="js/pieOfPieWidget.js"></script>
    <!-- Section Container Widget -->
    <script src="js/sectionContainerWidget.js"></script>

    <!-- Handsontable -->
    <script src="https://cdn.jsdelivr.net/npm/handsontable/dist/handsontable.full.min.js"></script>
    <!-- Handsontable Widget -->
    <script src="js/handsontableWidget.js"></script>

    <!-- Text Widget -->
    <script src="js/textWidget.js"></script>

    <!-- Table Widget -->
    <script src="js/tableWidget.js"></script>

    <!-- Line Separator Widget -->
    <script src="js/lineSeparatorWidget.js"></script>

    <!-- Notes Section Widget -->
    <script src="js/notesSectionWidget.js"></script>

    <!-- Image Widget -->
    <script src="js/imageWidget.js"></script>

    <!-- Video Widget -->
    <script src="js/videoWidget.js"></script>

    <!-- PDF.js Library -->
    <script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.min.js"></script>
    <!-- PDF Viewer Widget -->
    <script src="js/pdfViewerWidget.js"></script>

    <!-- Word Cloud Widget -->
    <script src="js/wordCloudWidget.js"></script>

    <!-- TSC2.0 Data Loader -->
    <!-- Bar Chart Search -->
    <script src="js/barChartSearch.js"></script>
    <script src="https://cdn.amcharts.com/lib/5/geodata/worldLow.js"></script>
    <script src="js/dynamicPieMapWidget.js"></script>
    <!-- TSC2.0 Widgets -->
    <script src="js/worldMapWidget.js"></script>
    <script src="js/drillDownMapWidget.js"></script>
    <script src="js/dualAxisWidget.js"></script>
    <script src="js/lineChartThresholdWidget.js"></script>
    <!-- Stacked Column Chart Widget -->
    <script src="js/stackedColumnChartWidget.js"></script>

    <!-- KPI Widget -->
    <script src="js/kpiWidget.js"></script>

    <!-- Price Chart Widget -->
    <script src="js/priceChartWidget.js"></script>

    <!-- Tab Container Widget -->
    <script src="js/tabContainerWidget.js"></script>

    <!-- Smart Widget Composer Styles -->
    <!-- <link rel="stylesheet" href="css/smartWidgetComposer.css" /> -->

    <!-- Bubble Chart Widget -->
    <script src="js/bubbleChartWidget.js"></script>

    <!-- Linked Pie Chart Widget -->
    <script src="js/linkedPieChartWidget.js"></script>

    <!-- Horizontal Bar Chart Widget -->
    <script src="js/horizontalBarChartWidget.js"></script>

    <!-- Column Chart Widget -->
    <script src="js/columnChartWidget.js"></script>

    <!-- Dual Axis Chart Widget -->
    <script src="js/dualAxisChartWidget.js"></script>
    <!-- Add CSS if needed: <link rel="stylesheet" href="linkedPieChartWidget.css" /> -->
    <!-- Curved Line Map Widget -->
    <script src="js/curvedLineMapWidget.js"></script>
    <!-- Drill Down Map Widget -->
    <script src="js/drillDownMapWidget.js"></script>
    <!-- Stacked Column Chart Widget -->
    <script src="js/stackedColumnChartWidget.js"></script>

    <!-- 100% Stacked Column Chart Widget -->
    <script src="js/percentStackedColumnChartWidget.js"></script>

    <!-- Area Chart Widget -->
    <script src="js/areaChartWidget.js"></script>

    <!-- Stock Chart Widget -->
    <script src="js/stockChartWidget.js"></script>

    <!-- Widget Drag & Drop Setup - Centralized drag-in configuration -->
    <script src="js/widget-drag-drop-setup.js"></script>

    `);
