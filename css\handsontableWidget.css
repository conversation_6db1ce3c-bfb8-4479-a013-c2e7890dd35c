/* Handsontable Widget Styles */
.handsontable-widget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  z-index: 20 !important;
}

.table-container {
  flex: 1;
  min-height: 300px;
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  z-index: 25 !important;
}

/* Settings icon styles */
.widget-header .btn-link {
  padding: 0;
  color: #333;
  text-decoration: none;
}

.widget-header .btn-link:hover {
  color: #02104f;
}

.widget-header .la-cog,
.widget-header .la-times {
  font-size: 1.1rem;
}

.widget-header .la-times:hover {
  color: #dc3545;
}

/* Widget header styles */
.handsontable-widget .widget-header {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 8px 12px;
  border-radius: 4px;
  margin: -8px -8px 8px -8px;
  z-index: 25 !important;
  position: relative;
}

/* Customize Handsontable appearance */
.handsontable-widget .handsontable {
  font-size: 13px;
  position: relative;
  z-index: 25 !important;
}

.handsontable-widget .handsontable th {
  background-color: #f8f9fa;
  font-weight: bold;
  color: #495057;
}

.handsontable-widget .handsontable td.current {
  background-color: rgba(13, 110, 253, 0.1);
}

.handsontable-widget .handsontable .htDimmed {
  color: #6c757d;
}

/* Fix for Handsontable in GridStack */
.handsontable-widget .wtHolder {
  width: 100% !important;
  height: 100% !important;
  z-index: 25 !important;
}

/* Fix for pointer events and z-index - CRITICAL */
.grid-stack-item-content .handsontable-widget,
.grid-stack-item-content .table-container,
.grid-stack-item-content .handsontable,
.grid-stack-item-content .wtHolder,
.grid-stack-item-content .wtHider,
.grid-stack-item-content .wtSpreader,
.grid-stack-item-content .ht_master,
.grid-stack-item-content .htCore,
.grid-stack-item-content .htCore th,
.grid-stack-item-content .htCore td {
  pointer-events: auto !important;
  z-index: 25 !important;
}

/* Fix for context menu */
.handsontable .htContextMenu,
.handsontable .htDropdownMenu,
.handsontable .htFilters {
  z-index: 1000 !important;
}

/* Disable grid-stack event capturing */
.grid-stack > .grid-stack-item > .ui-resizable-handle {
  z-index: 5 !important; /* Lower than spreadsheet */
}

/* Forcibly override any other rules that might interfere */
.ht_clone_top, 
.ht_clone_left, 
.ht_clone_corner {
  pointer-events: auto !important;
  z-index: 100 !important;
}

/* Remove all other pointer-events when interacting with spreadsheet */
.grid-stack-item:has(.handsontable:hover) {
  pointer-events: none;
}

.grid-stack-item:has(.handsontable:hover) .grid-stack-item-content {
  pointer-events: auto;
}
