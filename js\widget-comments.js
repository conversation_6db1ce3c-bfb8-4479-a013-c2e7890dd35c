/**
 * Widget Comments Functionality
 * This script adds the ability to add comments to widgets in preview mode.
 */

// Initialize comments functionality when the document is ready
document.addEventListener('DOMContentLoaded', function() {
  // We'll initialize the comments functionality when entering preview mode
});

// Current active widget for comments
let currentWidgetId = null;
let currentWidgetTitle = null;

// Sample comments data (in a real app, this would come from a database)
const widgetComments = {};

// Add chat icons to all widgets in preview mode
function addChatIconsToWidgets() {
  console.log('Adding chat icons to widgets');

  // Find all widget headers
  const widgetHeaders = document.querySelectorAll('.widget-header');
  console.log('Found widget headers:', widgetHeaders.length);

  widgetHeaders.forEach((header, index) => {
    // Create a unique ID for each widget if it doesn't have one
    const widgetContainer = header.closest('.grid-stack-item-content');
    if (!widgetContainer) {
      console.error('Could not find widget container for header:', header);
      return;
    }

    if (!widgetContainer.id) {
      widgetContainer.id = 'widget-' + index;
    }

    // Get the widget title
    const titleElement = header.querySelector('div:first-child');
    const widgetTitle = titleElement ? titleElement.textContent.trim() : 'Widget ' + (index + 1);
    console.log('Processing widget:', widgetTitle);

    // Store the widget title as a data attribute
    header.setAttribute('data-widget-title', widgetTitle);
    header.setAttribute('data-widget-id', widgetContainer.id);

    // Remove any existing event listeners (to prevent duplicates)
    header.removeEventListener('click', function() {
      openWidgetComments(widgetContainer.id, widgetTitle);
    });

    // Add click event to the header
    header.addEventListener('click', function() {
      console.log('Widget header clicked:', widgetTitle);
      openWidgetComments(widgetContainer.id, widgetTitle);
    });

    // Remove any existing chat icons
    const existingIcons = widgetContainer.querySelectorAll('.widget-chat-icon');
    existingIcons.forEach(icon => icon.remove());

    // Create chat icon
    const chatIcon = document.createElement('div');
    chatIcon.className = 'widget-chat-icon';
    chatIcon.innerHTML = '<i class="las la-comment"></i>';
    chatIcon.title = 'Add comments to this widget';

    // Add click event to the chat icon
    chatIcon.addEventListener('click', function(e) {
      console.log('Chat icon clicked for widget:', widgetTitle);
      e.stopPropagation(); // Prevent the header click event from firing
      openWidgetComments(widgetContainer.id, widgetTitle);
    });

    // Add the chat icon to the widget container
    widgetContainer.style.position = 'relative';
    widgetContainer.appendChild(chatIcon);
    console.log('Added chat icon to widget:', widgetTitle);
  });
}

// Open the widget comments panel
function openWidgetComments(widgetId, widgetTitle) {
  console.log('Opening widget comments for:', widgetId, widgetTitle);

  // Set the current widget
  currentWidgetId = widgetId;
  currentWidgetTitle = widgetTitle;

  // Update the widget info in the panel
  updateWidgetInfo(widgetId, widgetTitle);

  // Load comments for this widget
  loadWidgetComments(widgetId);

  // Show the offcanvas
  const commentPanelElement = document.getElementById('widget-comment-panel');
  if (!commentPanelElement) {
    console.error('Could not find widget-comment-panel element');
    return;
  }

  try {
    // Force any existing offcanvas to hide first
    const existingOffcanvas = bootstrap.Offcanvas.getInstance(commentPanelElement);
    if (existingOffcanvas) {
      existingOffcanvas.dispose();
    }

    // Create a new offcanvas instance with explicit options
    const commentPanel = new bootstrap.Offcanvas(commentPanelElement, {
      backdrop: true,
      keyboard: true,
      scroll: false
    });

    // Show the offcanvas
    commentPanel.show();
    console.log('Offcanvas panel shown');

    // Add a direct click handler to ensure it's working
    const backdrop = document.querySelector('.offcanvas-backdrop');
    if (backdrop) {
      backdrop.addEventListener('click', function() {
        commentPanel.hide();
      });
    }
  } catch (error) {
    console.error('Error showing offcanvas panel:', error);

    // Fallback method if the Bootstrap method fails
    commentPanelElement.classList.add('show');
    document.body.classList.add('offcanvas-open');

    // Create a backdrop manually if needed
    if (!document.querySelector('.offcanvas-backdrop')) {
      const backdrop = document.createElement('div');
      backdrop.className = 'offcanvas-backdrop show';
      document.body.appendChild(backdrop);

      // Add click handler to backdrop
      backdrop.addEventListener('click', function() {
        commentPanelElement.classList.remove('show');
        document.body.classList.remove('offcanvas-open');
        backdrop.remove();
      });
    }
  }
}

// Update widget info in the comment panel
function updateWidgetInfo(widgetId, widgetTitle) {
  // Update the widget title
  const widgetTitleElement = document.getElementById('comment-widget-title');
  if (widgetTitleElement) {
    widgetTitleElement.textContent = widgetTitle;
  }

  // Determine widget type based on title or content
  let widgetType = 'Widget';
  if (widgetTitle.includes('Chart') || widgetTitle.includes('Graph')) {
    widgetType = 'Chart Widget';
  } else if (widgetTitle.includes('Table') || widgetTitle.includes('Grid')) {
    widgetType = 'Table Widget';
  } else if (widgetTitle.includes('Image') || widgetTitle.includes('Photo')) {
    widgetType = 'Image Widget';
  } else if (widgetTitle.includes('Video')) {
    widgetType = 'Video Widget';
  } else if (widgetTitle.includes('Text') || widgetTitle.includes('Paragraph')) {
    widgetType = 'Text Widget';
  }

  // Update the widget type
  const widgetTypeElement = document.getElementById('comment-widget-type');
  if (widgetTypeElement) {
    widgetTypeElement.textContent = widgetType;
  }

  // Update the widget icon based on type
  const widgetIconElement = document.querySelector('.widget-info-icon i');
  if (widgetIconElement) {
    if (widgetType.includes('Chart')) {
      widgetIconElement.className = 'las la-chart-bar';
    } else if (widgetType.includes('Table')) {
      widgetIconElement.className = 'las la-table';
    } else if (widgetType.includes('Image')) {
      widgetIconElement.className = 'las la-image';
    } else if (widgetType.includes('Video')) {
      widgetIconElement.className = 'las la-video';
    } else if (widgetType.includes('Text')) {
      widgetIconElement.className = 'las la-font';
    } else {
      widgetIconElement.className = 'las la-cube';
    }
  }
}

// Load comments for a specific widget
function loadWidgetComments(widgetId) {
  const commentList = document.getElementById('comment-list');

  // Clear existing comments
  commentList.innerHTML = '';

  // Get comments for this widget
  const comments = widgetComments[widgetId] || [];

  if (comments.length === 0) {
    // Show no comments message
    commentList.innerHTML = `
      <div class="no-comments-message">
        <div class="no-comments-icon">
          <i class="las la-comments"></i>
        </div>
        <h6>No comments yet</h6>
        <p>Be the first to add a comment to this widget</p>
      </div>
    `;
  } else {
    // Add each comment to the list
    comments.forEach(comment => {
      const commentItem = document.createElement('div');
      commentItem.className = 'comment-item';

      // Get initials for avatar
      const initials = getInitials(comment.author);

      commentItem.innerHTML = `
        <div class="comment-avatar">${initials}</div>
        <div class="comment-content">
          <div class="comment-header">
            <div class="comment-author">${comment.author}</div>
            <div class="comment-time">${comment.time}</div>
          </div>
          <div class="comment-text">${comment.text}</div>
        </div>
      `;
      commentList.appendChild(commentItem);
    });
  }
}

// Helper function to get initials from a name
function getInitials(name) {
  if (!name) return 'U';

  const parts = name.split(' ');
  if (parts.length === 1) {
    return parts[0].charAt(0).toUpperCase();
  } else {
    return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
  }
}

// Add a new comment
function addComment(text) {
  if (!currentWidgetId || !text.trim()) return;

  // Get the current user name (in a real app, this would come from the user's profile)
  const currentUser = 'Vimal Thapliyal';

  // Create a new comment
  const newComment = {
    author: currentUser,
    time: formatCommentTime(new Date()),
    text: text.trim()
  };

  // Initialize the comments array for this widget if it doesn't exist
  if (!widgetComments[currentWidgetId]) {
    widgetComments[currentWidgetId] = [];
  }

  // Add the comment
  widgetComments[currentWidgetId].push(newComment);

  // Reload the comments
  loadWidgetComments(currentWidgetId);

  // Clear the comment text
  document.getElementById('comment-text').value = '';

  // Show a subtle notification
  showCommentNotification('Comment added successfully');
}

// Format comment time in a user-friendly way
function formatCommentTime(date) {
  // For recent comments, show relative time
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffSec < 60) {
    return 'Just now';
  } else if (diffMin < 60) {
    return `${diffMin} ${diffMin === 1 ? 'minute' : 'minutes'} ago`;
  } else if (diffHour < 24) {
    return `${diffHour} ${diffHour === 1 ? 'hour' : 'hours'} ago`;
  } else if (diffDay < 7) {
    return `${diffDay} ${diffDay === 1 ? 'day' : 'days'} ago`;
  } else {
    // For older comments, show the date
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }
}

// Show a subtle notification for comment actions
function showCommentNotification(message) {
  // Create a notification element
  const notification = document.createElement('div');
  notification.className = 'comment-notification';
  notification.textContent = message;

  // Add it to the document
  document.body.appendChild(notification);

  // Animate it
  setTimeout(() => {
    notification.classList.add('show');
  }, 10);

  // Remove it after a delay
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, 3000);
}

// Initialize the comments functionality
function initializeWidgetComments() {
  console.log('Initializing widget comments functionality');

  // Add event listener for the submit comment button
  const submitBtn = document.getElementById('submit-comment-btn');
  if (!submitBtn) {
    console.error('Could not find submit-comment-btn element');
    return;
  }

  // Remove any existing event listeners
  submitBtn.removeEventListener('click', submitCommentHandler);

  // Add event listener
  submitBtn.addEventListener('click', submitCommentHandler);
  console.log('Added event listener to submit button');

  // Add event listener for pressing Enter in the comment text area
  const commentTextArea = document.getElementById('comment-text');
  if (!commentTextArea) {
    console.error('Could not find comment-text element');
    return;
  }

  // Remove any existing event listeners
  commentTextArea.removeEventListener('keydown', commentKeydownHandler);

  // Add event listener
  commentTextArea.addEventListener('keydown', commentKeydownHandler);
  console.log('Added event listener to comment text area');
}

// Handler for submit comment button
function submitCommentHandler() {
  console.log('Submit comment button clicked');
  const commentText = document.getElementById('comment-text').value;
  addComment(commentText);
}

// Handler for comment text area keydown
function commentKeydownHandler(e) {
  if (e.key === 'Enter' && e.ctrlKey) {
    console.log('Ctrl+Enter pressed in comment text area');
    const commentText = this.value;
    addComment(commentText);
  }
}

// Export functions
window.addChatIconsToWidgets = addChatIconsToWidgets;
window.initializeWidgetComments = initializeWidgetComments;
