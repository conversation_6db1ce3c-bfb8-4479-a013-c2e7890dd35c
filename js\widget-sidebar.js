/**
 * Widget Sidebar
 * Manages hiding/showing widgets and storing them in a sidebar for easy access
 */

// Immediate test log
console.log("🚀 WIDGET SIDEBAR: Script loaded!");

// Store hidden widgets data
const hiddenWidgets = [];

// Initialize when DOM is ready
function initializeWidgetSidebar() {
  if (window.widgetSidebarInitialized) {
    console.log("Widget sidebar: Already initialized, skipping");
    return;
  }

  console.log("Widget sidebar: Initializing...");

  initWidgetSidebar();
  setupWidgetInterceptor();
  setupNewsWidgetEvents();
  createDashboardMinimap(); // Add minimap functionality
  window.widgetSidebarInitialized = true;
  console.log("Widget sidebar: Initialization complete");
}

// Check if DOM is already ready
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", initializeWidgetSidebar);
} else {
  // DOM is already ready
  initializeWidgetSidebar();
}

// Also try to initialize after a short delay as a fallback
setTimeout(() => {
  if (!window.widgetSidebarInitialized) {
    console.log("Widget sidebar: Fallback initialization");
    initializeWidgetSidebar();
    window.widgetSidebarInitialized = true;
  }
}, 1000);

// Get appropriate icon for widget based on title or content
function getWidgetIconClass(title, content) {
  title = title.toLowerCase();

  // Check for section container first
  if (
    title.includes("section") ||
    title.includes("container") ||
    content.includes("section-container")
  ) {
    return "la-layer-group";
  } else if (
    (content && content.includes("chart-pie")) ||
    title.includes("pie")
  ) {
    return "la-chart-pie";
  } else if (
    (content && content.includes("chart-bar")) ||
    title.includes("bar")
  ) {
    return "la-chart-bar";
  } else if (
    (content && content.includes("chart-line")) ||
    title.includes("line") ||
    title.includes("trend")
  ) {
    return "la-chart-line";
  } else if (
    (content && content.includes("table")) ||
    title.includes("table") ||
    title.includes("data")
  ) {
    return "la-table";
  } else if (title.includes("image") || title.includes("picture")) {
    return "la-image";
  } else if (title.includes("text") || title.includes("insight")) {
    return "la-font";
  } else if (title.includes("note")) {
    return "la-sticky-note";
  } else if (title.includes("pdf")) {
    return "la-file-pdf";
  } else if (title.includes("video")) {
    return "la-video";
  }

  return "la-th-large"; // Default icon
}

// Get appropriate widget type/category description
function getWidgetTypeDescription(title, content) {
  title = title.toLowerCase();

  // Check for section container first
  if (
    title.includes("section") ||
    title.includes("container") ||
    content.includes("section-container")
  ) {
    return "Section Container";
  } else if (
    (content && content.includes("chart-pie")) ||
    title.includes("pie")
  ) {
    return "Pie Chart";
  } else if (
    (content && content.includes("chart-bar")) ||
    title.includes("bar")
  ) {
    return "Bar Chart";
  } else if (
    (content && content.includes("chart-line")) ||
    title.includes("line") ||
    title.includes("trend")
  ) {
    return "Line Chart";
  } else if (
    (content && content.includes("table")) ||
    title.includes("table") ||
    title.includes("data")
  ) {
    return "Data Table";
  } else if (title.includes("image") || title.includes("picture")) {
    return "Image Widget";
  } else if (title.includes("text")) {
    return "Text Widget";
  } else if (title.includes("insight")) {
    return "Insights Widget";
  } else if (title.includes("note")) {
    return "Notes Widget";
  } else if (title.includes("pdf")) {
    return "PDF Viewer";
  } else if (title.includes("video")) {
    return "Video Widget";
  }

  return "Widget";
}

// Initialize the widget sidebar
function initWidgetSidebar() {
  // Create sidebar HTML structure
  const sidebarHTML = `
    <div class="widget-sidebar">
      <div class="widget-sidebar-header">
        <h5 class="widget-sidebar-title">
          <i class="las la-layer-group"></i>
          Hidden Widgets
          <span class="widgets-count">0</span>
        </h5>
        <div class="widget-sidebar-close">
          <i class="las la-times"></i>
        </div>
      </div>
      <div class="widget-sidebar-content">
        <ul class="hidden-widgets-list">
          <li class="hidden-widget-item empty-message">No hidden widgets</li>
        </ul>
      </div>
      <div class="widget-sidebar-footer">
        Hidden widgets are stored here until you restore them
      </div>
    </div>
    <button class="widget-sidebar-toggle" aria-label="Show hidden widgets">
      <i class="las la-layer-group"></i>
      <span class="badge">0</span>
    </button>
  `;

  // Add sidebar to the document
  const sidebarContainer = document.createElement("div");
  sidebarContainer.innerHTML = sidebarHTML;
  document.body.appendChild(sidebarContainer);

  // Add event listener to toggle button
  const toggleButton = document.querySelector(".widget-sidebar-toggle");
  const sidebar = document.querySelector(".widget-sidebar");
  const closeBtn = document.querySelector(".widget-sidebar-close");

  toggleButton.addEventListener("click", () => {
    sidebar.classList.toggle("show");
  });

  // Close button functionality
  closeBtn.addEventListener("click", () => {
    sidebar.classList.remove("show");
  });

  // Close sidebar when clicking outside
  document.addEventListener("click", (event) => {
    if (
      !sidebar.contains(event.target) &&
      !toggleButton.contains(event.target) &&
      sidebar.classList.contains("show")
    ) {
      sidebar.classList.remove("show");
    }
  });

  // Initial state (hide the toggle if no widgets are stored)
  toggleButton.style.display = "none";
}

// Set up widget removal interceptor using event delegation
function setupWidgetInterceptor() {
  console.log("🎯 WIDGET SIDEBAR: Setting up event delegation interceptor");

  // Use event delegation to catch all clicks on close buttons
  document.addEventListener(
    "click",
    function (event) {
      console.log("🎯 CLICK DEBUG: Element clicked:", event.target);

      // Check multiple patterns for close buttons
      const isCloseButton =
        event.target.classList.contains("la-times") || // Close icon
        event.target.closest('.widget-icon[title="Close"]') || // Widget icon with close title
        (event.target.closest(".widget-icon") &&
          event.target
            .closest(".widget-icon")
            .innerHTML.includes("la-times")) || // Widget icon containing close icon
        (event.target.tagName === "BUTTON" &&
          event.target.innerHTML.includes("la-times")) || // Button with close icon
        (event.target.onclick &&
          event.target.onclick.toString().includes("removeWidget")); // Button with removeWidget onclick

      if (isCloseButton) {
        console.log("🎯 WIDGET SIDEBAR: Close button detected!", event.target);

        // Find the widget element
        const widget = event.target.closest(".grid-stack-item");

        if (widget) {
          console.log("🎯 WIDGET SIDEBAR: Found widget to hide:", widget);

          // Skip if this is inside a slideout panel or widget sidebar
          if (
            widget.closest(".slideout-panel") ||
            widget.closest(".widget-sidebar")
          ) {
            console.log(
              "🎯 WIDGET SIDEBAR: Skipping - inside slideout panel or sidebar"
            );
            return;
          }

          // Prevent the default removal
          event.preventDefault();
          event.stopPropagation();

          // Hide the widget instead
          hideWidget(widget);

          // Show notification
          showTemporaryNotification(
            "Widget moved to sidebar instead of being deleted!"
          );

          return false;
        } else {
          console.log(
            "🎯 WIDGET SIDEBAR: Close button found but no widget container"
          );
        }
      }
    },
    true
  ); // Use capture phase to intercept before other handlers

  console.log("🎯 WIDGET SIDEBAR: Event delegation setup complete");
}

// Show a temporary notification
function showTemporaryNotification(message) {
  const notification = document.createElement("div");
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #00B19C;
    color: white;
    padding: 15px 20px;
    border-radius: 5px;
    z-index: 10000;
    font-weight: bold;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
  `;
  notification.textContent = message;

  document.body.appendChild(notification);

  // Remove after 3 seconds
  setTimeout(() => {
    notification.style.opacity = "0";
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

// Hide widget and add to sidebar
function hideWidget(widgetElement) {
  // Get widget data
  const widgetId = widgetElement.id || `widget-${Date.now()}`;
  if (!widgetElement.id) {
    widgetElement.id = widgetId;
  }

  // Check if this is a section container
  const isSectionContainer = widgetElement.querySelector(
    ".section-container-widget"
  );
  let nestedWidgetsData = null;
  let sectionId = null;

  // If it's a section container, preserve nested widgets
  if (isSectionContainer) {
    console.log(
      "📦 WIDGET SIDEBAR: Hiding section container, preserving nested widgets"
    );

    // Find the section ID
    const sectionContent = isSectionContainer.querySelector(".section-content");
    if (sectionContent) {
      sectionId = sectionContent.id;
    }

    // Find nested grid and preserve widgets
    const nestedGrid = isSectionContainer.querySelector(".grid-stack-nested");
    if (nestedGrid && nestedGrid.gridstack) {
      try {
        // Get all nested widgets data
        const nestedItems = nestedGrid.gridstack.getGridItems();
        nestedWidgetsData = nestedItems.map((item) => {
          const node = item.gridstackNode;
          return {
            x: node.x,
            y: node.y,
            w: node.w,
            h: node.h,
            id: node.id || item.id,
            content: item.innerHTML,
          };
        });
        console.log(
          `📦 WIDGET SIDEBAR: Preserved ${nestedWidgetsData.length} nested widgets`
        );
      } catch (error) {
        console.error("Error preserving nested widgets:", error);
        nestedWidgetsData = [];
      }
    }
  }

  // Try multiple approaches to find the widget title
  let widgetTitle = "Unnamed Widget";

  // Method 1: Look for editable-title
  const editableTitle = widgetElement.querySelector(".editable-title span");
  if (editableTitle) {
    widgetTitle = editableTitle.textContent.trim();
  }
  // Method 2: Look for widget-title-text
  else {
    const titleText = widgetElement.querySelector(".widget-title-text");
    if (titleText) {
      widgetTitle = titleText.textContent.trim();
    }
    // Method 3: Look for widget-title
    else {
      const titleElement = widgetElement.querySelector(".widget-title");
      if (titleElement) {
        // Get text content but exclude icon text
        const titleSpan = titleElement.querySelector("span");
        if (titleSpan) {
          widgetTitle = titleSpan.textContent.trim();
        } else {
          // Fallback: get all text but try to exclude icon classes
          const titleText = titleElement.textContent.trim();
          // Remove common icon text patterns
          widgetTitle =
            titleText.replace(/^\s*[\w\s]*\s+/, "").trim() || titleText;
        }
      }
      // Method 4: Look in widget header
      else {
        const widgetHeader = widgetElement.querySelector(".widget-header");
        if (widgetHeader) {
          const headerDiv = widgetHeader.querySelector("div:first-child");
          if (headerDiv) {
            widgetTitle = headerDiv.textContent.trim();
          }
        }
      }
    }
  }

  // If still no title found, try to determine from widget class
  if (widgetTitle === "Unnamed Widget" || widgetTitle === "") {
    const widgetClasses = widgetElement.className;
    if (widgetClasses.includes("section-container")) {
      widgetTitle = "Section Container";
    } else if (widgetClasses.includes("text-widget")) {
      widgetTitle = "Text Widget";
    } else if (widgetClasses.includes("table-widget")) {
      widgetTitle = "Table Widget";
    } else if (widgetClasses.includes("image-widget")) {
      widgetTitle = "Image Widget";
    } else if (widgetClasses.includes("chart-widget")) {
      widgetTitle = "Chart Widget";
    }
  }

  const widgetContent = widgetElement.innerHTML;
  const widgetIconClass = getWidgetIconClass(widgetTitle, widgetContent);
  const widgetTypeDesc = getWidgetTypeDescription(widgetTitle, widgetContent);

  // Store widget data
  const widgetData = {
    id: widgetId,
    title: widgetTitle,
    type: widgetTypeDesc,
    iconClass: widgetIconClass,
    element: widgetElement,
    timestamp: new Date().toISOString(),
    // Section container specific data
    isSectionContainer: isSectionContainer !== null,
    sectionId: sectionId,
    nestedWidgets: nestedWidgetsData,
  };

  // Add to hidden widgets array
  hiddenWidgets.push(widgetData);

  // Hide the widget
  widgetElement.style.display = "none";

  // Show a brief notification
  showHideNotification(widgetTitle);

  // Update sidebar
  updateSidebar();
}

// Show a brief notification when a widget is hidden
function showHideNotification(widgetTitle) {
  // Create notification element
  const notification = document.createElement("div");
  notification.className = "widget-hide-notification";
  notification.innerHTML = `
    <div class="notification-content">
      <i class="las la-check-circle"></i>
      <span>"${widgetTitle}" moved to hidden widgets</span>
    </div>
    <div class="notification-action">Undo</div>
  `;

  // Style the notification
  notification.style.position = "fixed";
  notification.style.bottom = "20px";
  notification.style.right = "20px";
  notification.style.backgroundColor = "#00B19C";
  notification.style.color = "white";
  notification.style.padding = "10px 15px";
  notification.style.borderRadius = "4px";
  notification.style.boxShadow = "0 2px 8px rgba(0, 0, 0, 0.2)";
  notification.style.zIndex = "9999";
  notification.style.display = "flex";
  notification.style.alignItems = "center";
  notification.style.justifyContent = "space-between";
  notification.style.minWidth = "250px";
  notification.style.transform = "translateY(100px)";
  notification.style.opacity = "0";
  notification.style.transition = "all 0.3s ease";
  notification.style.pointerEvents = "none"; // Allow scroll events to pass through

  // Style the notification content
  const content = notification.querySelector(".notification-content");
  content.style.display = "flex";
  content.style.alignItems = "center";
  content.style.gap = "8px";

  // Style the notification action
  const action = notification.querySelector(".notification-action");
  action.style.marginLeft = "15px";
  action.style.fontWeight = "bold";
  action.style.cursor = "pointer";
  action.style.padding = "5px";
  action.style.borderRadius = "4px";
  action.style.pointerEvents = "auto"; // Keep the button interactive

  action.addEventListener("mouseenter", () => {
    action.style.backgroundColor = "rgba(255, 255, 255, 0.2)";
  });

  action.addEventListener("mouseleave", () => {
    action.style.backgroundColor = "transparent";
  });

  // Add to document
  document.body.appendChild(notification);

  // Trigger animation
  setTimeout(() => {
    notification.style.transform = "translateY(0)";
    notification.style.opacity = "1";
  }, 10);

  // Add undo functionality
  action.addEventListener("click", () => {
    // Restore the last hidden widget
    restoreWidget(hiddenWidgets.length - 1);
    // Remove notification
    notification.style.transform = "translateY(100px)";
    notification.style.opacity = "0";
    setTimeout(() => notification.remove(), 300);
  });

  // Auto-remove after 5 seconds
  setTimeout(() => {
    if (document.body.contains(notification)) {
      notification.style.transform = "translateY(100px)";
      notification.style.opacity = "0";
      setTimeout(() => {
        if (document.body.contains(notification)) {
          notification.remove();
        }
      }, 300);
    }
  }, 5000);
}

// Update the sidebar with hidden widgets
function updateSidebar() {
  const widgetsList = document.querySelector(".hidden-widgets-list");
  const toggleButton = document.querySelector(".widget-sidebar-toggle");
  const widgetsCounter = document.querySelector(".widgets-count");
  const badgeCounter = toggleButton.querySelector(".badge");

  // Update counters
  widgetsCounter.textContent = hiddenWidgets.length;
  badgeCounter.textContent = hiddenWidgets.length.toString();

  // If more than 9 widgets, show 9+
  if (hiddenWidgets.length > 9) {
    badgeCounter.textContent = "9+";
    badgeCounter.style.fontSize = "8px";
  } else {
    badgeCounter.style.fontSize = "10px";
  }

  // Show/hide toggle button based on widgets count
  toggleButton.style.display = hiddenWidgets.length > 0 ? "flex" : "none";

  // Clear current list
  widgetsList.innerHTML = "";

  if (hiddenWidgets.length === 0) {
    widgetsList.innerHTML =
      '<li class="hidden-widget-item empty-message">No hidden widgets</li>';
    return;
  }

  // Add each hidden widget to the list
  hiddenWidgets.forEach((widget, index) => {
    const widgetItem = document.createElement("li");
    widgetItem.className = "hidden-widget-item";
    widgetItem.dataset.widgetId = widget.id;

    // Format time for display
    const timeAgo = getTimeAgo(widget.timestamp);

    widgetItem.innerHTML = `
      <div class="hidden-widget-icon ${widget.iconClass}">
        <i class="las ${widget.iconClass}"></i>
      </div>
      <div class="hidden-widget-title">
        ${widget.title}
        <small>${widget.type} &middot; Hidden ${timeAgo}</small>
      </div>
      <div class="hidden-widget-restore" title="Restore widget">
        <i class="las la-eye"></i>
      </div>
    `;

    // Add click event to restore widget
    widgetItem.addEventListener("click", (event) => {
      // Only trigger on the item itself, not on the restore button (which has its own handler)
      if (!event.target.closest(".hidden-widget-restore")) {
        restoreWidget(index);
      }
    });

    // Add specific click handler for the restore button
    const restoreBtn = widgetItem.querySelector(".hidden-widget-restore");
    restoreBtn.addEventListener("click", (event) => {
      event.stopPropagation(); // Prevent triggering the parent click
      restoreWidget(index);
    });

    widgetsList.appendChild(widgetItem);
  });
}

// Format timestamp to "time ago" format
function getTimeAgo(timestamp) {
  const date = new Date(timestamp);
  const now = new Date();
  const seconds = Math.floor((now - date) / 1000);

  if (seconds < 60) {
    return "just now";
  }

  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) {
    return `${minutes} ${minutes === 1 ? "minute" : "minutes"} ago`;
  }

  const hours = Math.floor(minutes / 60);
  if (hours < 24) {
    return `${hours} ${hours === 1 ? "hour" : "hours"} ago`;
  }

  const days = Math.floor(hours / 24);
  if (days < 30) {
    return `${days} ${days === 1 ? "day" : "days"} ago`;
  }

  const months = Math.floor(days / 30);
  if (months < 12) {
    return `${months} ${months === 1 ? "month" : "months"} ago`;
  }

  const years = Math.floor(months / 12);
  return `${years} ${years === 1 ? "year" : "years"} ago`;
}

// Restore a widget from the sidebar
function restoreWidget(index) {
  // Get the widget data
  const widgetData = hiddenWidgets[index];
  if (!widgetData) return;

  // Show the widget
  widgetData.element.style.display = "";

  // Special handling for section containers
  if (widgetData.isSectionContainer && widgetData.sectionId) {
    console.log(
      "📦 WIDGET SIDEBAR: Restoring section container, re-initializing nested grid"
    );

    // Re-initialize the section container
    setTimeout(() => {
      try {
        // Check if initSectionContainer is available
        if (typeof window.initSectionContainer === "function") {
          // Re-initialize the section container
          const nestedGrid = window.initSectionContainer(widgetData.sectionId);

          // Restore nested widgets if any were preserved
          if (
            nestedGrid &&
            widgetData.nestedWidgets &&
            widgetData.nestedWidgets.length > 0
          ) {
            setTimeout(() => {
              try {
                // Add each nested widget back
                widgetData.nestedWidgets.forEach((widgetInfo) => {
                  nestedGrid.addWidget({
                    x: widgetInfo.x,
                    y: widgetInfo.y,
                    w: widgetInfo.w,
                    h: widgetInfo.h,
                    id: widgetInfo.id,
                    content: widgetInfo.content,
                  });
                });
                console.log(
                  `📦 WIDGET SIDEBAR: Restored ${widgetData.nestedWidgets.length} nested widgets`
                );
              } catch (error) {
                console.error("Error restoring nested widgets:", error);
              }
            }, 100);
          }
        } else {
          console.warn(
            "📦 WIDGET SIDEBAR: initSectionContainer function not available"
          );
        }
      } catch (error) {
        console.error("Error re-initializing section container:", error);
      }
    }, 50);
  }

  // Add a highlight effect
  widgetData.element.style.transition =
    "box-shadow 0.3s ease, transform 0.3s ease";
  widgetData.element.style.boxShadow = "0 0 0 2px #00B19C";
  widgetData.element.style.transform = "scale(1.01)";

  // Remove highlight after animation
  setTimeout(() => {
    widgetData.element.style.boxShadow = "";
    widgetData.element.style.transform = "";

    // Remove the transition after it's done
    setTimeout(() => {
      widgetData.element.style.transition = "";
    }, 300);
  }, 1500);

  // Remove from hidden widgets
  hiddenWidgets.splice(index, 1);

  // Update sidebar
  updateSidebar();

  // Close the sidebar if no widgets left
  if (hiddenWidgets.length === 0) {
    document.querySelector(".widget-sidebar").classList.remove("show");
  }
}

// Function to programmatically hide a widget
window.hideWidgetById = function (widgetId) {
  const widget = document.getElementById(widgetId);
  if (widget) {
    hideWidget(widget);
  }
};

// Function to programmatically restore a widget
window.restoreWidgetById = function (widgetId) {
  const index = hiddenWidgets.findIndex((w) => w.id === widgetId);
  if (index !== -1) {
    restoreWidget(index);
  }
};

// Add this function to handle news widget click events
function setupNewsWidgetEvents() {
  document.querySelectorAll(".news-item-link").forEach((link) => {
    link.addEventListener("click", function (event) {
      if (this.classList.contains("open-news")) {
        event.preventDefault();

        const title = this.dataset.title;
        const description = this.dataset.description;
        const date = this.dataset.date_published;
        const tag = this.dataset.supply_demand_macro;

        // Create a form for POST submission
        const form = document.createElement("form");
        form.action = "https://amplifipro.thesmartcube.com/commodityNewsDesc";
        form.method = "POST";
        form.target = "_blank";

        // Add CSRF token
        const csrfInput = document.createElement("input");
        csrfInput.type = "hidden";
        csrfInput.name = "_token";
        csrfInput.value = document.querySelector(
          'meta[name="csrf-token"]'
        ).content;
        form.appendChild(csrfInput);

        // Add data fields
        const titleInput = document.createElement("input");
        titleInput.type = "hidden";
        titleInput.name = "title";
        titleInput.value = title;
        form.appendChild(titleInput);

        const descInput = document.createElement("input");
        descInput.type = "hidden";
        descInput.name = "description";
        descInput.value = description;
        form.appendChild(descInput);

        const dateInput = document.createElement("input");
        dateInput.type = "hidden";
        dateInput.name = "date";
        dateInput.value = date;
        form.appendChild(dateInput);

        const tagInput = document.createElement("input");
        tagInput.type = "hidden";
        tagInput.name = "tag";
        tagInput.value = tag;
        form.appendChild(tagInput);

        // Submit form
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
      }
    });
  });
}

// Add dashboard minimap functionality
function createDashboardMinimap() {
  console.log(
    "🗺️ SLIDE NAVIGATOR: Creating PowerPoint-style slide navigator..."
  );

  const navigatorHTML = `
    <div class="widget-navigator-offcanvas">
      <div class="navigator-header">
        <h5>
          <i class="las la-list"></i>
          Slide Navigator
        </h5>
        <div class="navigator-header-actions">
          <button class="navigator-refresh" title="Refresh slide list">
            <i class="las la-sync-alt"></i>
          </button>
          <button class="navigator-close">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      <div class="navigator-content">
        <div class="navigator-search">
          <input type="text" placeholder="Search slides..." class="navigator-search-input">
          <i class="las la-search"></i>
        </div>
        <div class="navigator-stats">
          <span class="widget-count-display">0 sections</span>
        </div>
        <div class="navigator-list">
          <div class="navigator-empty">
            <i class="las la-layer-group"></i>
            <p>No sections found</p>
          </div>
        </div>
      </div>
    </div>
    <button class="widget-navigator-toggle" title="Open slide navigator">
      <i class="las la-list"></i>
      <span class="navigator-badge">0</span>
    </button>
  `;

  const navigatorContainer = document.createElement("div");
  navigatorContainer.innerHTML = navigatorHTML;
  document.body.appendChild(navigatorContainer);

  console.log("🗺️ SLIDE NAVIGATOR: PowerPoint-style HTML elements created");

  // Initialize navigator functionality
  setupNavigatorInteractions();
  setupSmartBehavior();

  console.log("🗺️ SLIDE NAVIGATOR: PowerPoint-style initialization complete");
}

// Setup enhanced navigator interactions
function setupNavigatorInteractions() {
  console.log("🗺️ WIDGET NAVIGATOR: Setting up enhanced interactions...");

  const toggleBtn = document.querySelector(".widget-navigator-toggle");
  const navigator = document.querySelector(".widget-navigator-offcanvas");
  const closeBtn = document.querySelector(".navigator-close");
  const searchInput = document.querySelector(".navigator-search-input");
  const refreshBtn = document.querySelector(".navigator-refresh");

  if (!toggleBtn || !navigator || !closeBtn) {
    console.error("🗺️ WIDGET NAVIGATOR: Required elements not found", {
      toggleBtn: !!toggleBtn,
      navigator: !!navigator,
      closeBtn: !!closeBtn,
    });
    return;
  }

  // Enhanced toggle with smart behavior
  toggleBtn.addEventListener("click", () => {
    console.log("🗺️ WIDGET NAVIGATOR: Toggle clicked");
    const isShowing = navigator.classList.contains("show");

    if (isShowing) {
      navigator.classList.remove("show");
      document.body.classList.remove("navigator-open");
    } else {
      navigator.classList.add("show");
      document.body.classList.add("navigator-open");
      // Always update navigator view when opening
      console.log("🗺️ WIDGET NAVIGATOR: Opening navigator, updating view...");
      updateNavigatorView();
      // Focus search input for better UX
      setTimeout(() => searchInput?.focus(), 300);
    }
  });

  closeBtn.addEventListener("click", () => {
    console.log("🗺️ WIDGET NAVIGATOR: Close clicked");
    navigator.classList.remove("show");
    document.body.classList.remove("navigator-open");
  });

  // Search functionality
  if (searchInput) {
    searchInput.addEventListener("input", (e) => {
      const searchTerm = e.target.value.toLowerCase();
      filterSlides(searchTerm);
    });
  }

  // Refresh button functionality
  if (refreshBtn) {
    refreshBtn.addEventListener("click", () => {
      console.log("🗺️ WIDGET NAVIGATOR: Refresh button clicked");
      updateNavigatorView();
    });
  }

  // Close on escape key
  document.addEventListener("keydown", (e) => {
    if (e.key === "Escape" && navigator.classList.contains("show")) {
      navigator.classList.remove("show");
      document.body.classList.remove("navigator-open");
    }
  });

  // Update navigator when widgets change - with debouncing
  let mutationTimeout;
  const observer = new MutationObserver(() => {
    if (navigator.classList.contains("show")) {
      clearTimeout(mutationTimeout);
      mutationTimeout = setTimeout(() => {
        updateNavigatorView();
      }, 250); // Debounce mutations
    }
  });

  const gridContainer = document.querySelector("#grid-container");
  if (gridContainer) {
    observer.observe(gridContainer, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ["style", "class"],
    });
  }

  console.log("🗺️ WIDGET NAVIGATOR: Enhanced event listeners attached");
}

// Enhanced navigator view update with better widget detection and organization
function updateNavigatorView() {
  const navigatorList = document.querySelector(".navigator-list");
  const widgetCountEl = document.querySelector(".widget-count-display");
  const navigatorBadge = document.querySelector(".navigator-badge");

  if (!navigatorList) {
    console.error("🗺️ WIDGET NAVIGATOR: Navigator list not found");
    return;
  }

  console.log("🗺️ WIDGET NAVIGATOR: Starting widget detection...");

  // Enhanced widget detection with multiple selectors
  const selectors = [
    "#grid-container .grid-stack-item",
    ".grid-stack .grid-stack-item",
    ".grid-stack-item",
    "[class*='grid-stack-item']",
  ];

  let widgets = [];

  // Try each selector and log results
  for (const selector of selectors) {
    const foundWidgets = document.querySelectorAll(selector);
    console.log(
      `🗺️ WIDGET NAVIGATOR: Selector '${selector}' found ${foundWidgets.length} elements`
    );

    if (foundWidgets.length > 0) {
      // Filter out hidden widgets and invalid elements
      const validWidgets = Array.from(foundWidgets).filter((widget) => {
        const isVisible =
          widget.style.display !== "none" &&
          !widget.hasAttribute("hidden") &&
          widget.offsetParent !== null;
        const hasContent =
          widget.querySelector(".grid-stack-item-content") ||
          widget.querySelector(".widget") ||
          widget.querySelector(".card") ||
          widget.textContent.trim().length > 0;

        console.log(
          `🗺️ WIDGET NAVIGATOR: Widget ${
            widget.id || "no-id"
          } - visible: ${isVisible}, hasContent: ${hasContent}`
        );
        return isVisible && hasContent;
      });

      if (validWidgets.length > 0) {
        widgets = validWidgets;
        console.log(
          `🗺️ WIDGET NAVIGATOR: Using ${validWidgets.length} widgets from selector '${selector}'`
        );
        break;
      }
    }
  }

  // If still no widgets found, try a broader search
  if (widgets.length === 0) {
    console.log(
      "🗺️ WIDGET NAVIGATOR: No widgets found with standard selectors, trying broader search..."
    );

    // Look for any elements that might be widgets
    const potentialWidgets = document.querySelectorAll(`
      .widget,
      .card,
      [class*="widget"],
      [class*="chart"],
      [data-widget-id],
      .grid-stack-item-content
    `);

    console.log(
      `🗺️ WIDGET NAVIGATOR: Found ${potentialWidgets.length} potential widget elements`
    );

    // Filter and log each potential widget
    widgets = Array.from(potentialWidgets)
      .filter((element) => {
        const isVisible =
          element.style.display !== "none" &&
          !element.hasAttribute("hidden") &&
          element.offsetParent !== null;
        const isInGrid = element.closest(".grid-stack") !== null;
        const hasMinSize =
          element.offsetWidth > 50 && element.offsetHeight > 50;

        console.log(
          `🗺️ WIDGET NAVIGATOR: Potential widget - visible: ${isVisible}, inGrid: ${isInGrid}, minSize: ${hasMinSize}, classes: ${element.className}`
        );

        return isVisible && (isInGrid || hasMinSize);
      })
      .slice(0, 20); // Limit to prevent overwhelming
  }

  console.log(`🗺️ WIDGET NAVIGATOR: Final widget count: ${widgets.length}`);

  // Update counters
  if (widgetCountEl) {
    widgetCountEl.textContent = `${widgets.length} section${
      widgets.length !== 1 ? "s" : ""
    }`;
  }
  if (navigatorBadge) {
    navigatorBadge.textContent = widgets.length > 99 ? "99+" : widgets.length;
  }

  // Clear current list
  navigatorList.innerHTML = "";

  if (widgets.length === 0) {
    navigatorList.innerHTML = `
      <div class="navigator-empty">
        <i class="las la-layer-group"></i>
        <p>No sections found</p>
        <small>Try adding some widgets to the dashboard</small>
      </div>
    `;
    return;
  }

  // Organize widgets by their vertical position (row-wise)
  const widgetData = Array.from(widgets)
    .map((widget, index) => {
      const rect = widget.getBoundingClientRect();

      // Get widget title with multiple fallback methods
      let title = getWidgetTitle(widget);

      // Ensure widget has an ID for navigation
      let widgetId = widget.id || widget.dataset.widgetId;
      if (!widgetId) {
        widgetId = `widget-nav-${index + 1}`;
        widget.id = widgetId; // Assign the ID to the element
      }

      console.log(
        `🗺️ WIDGET NAVIGATOR: Processing section: ${title} (ID: ${widgetId})`
      );

      return {
        element: widget,
        title: title,
        top: rect.top + window.pageYOffset,
        left: rect.left + window.pageXOffset,
        visible: rect.height > 0 && rect.width > 0,
        id: widgetId,
      };
    })
    .filter((w) => w.visible)
    .sort((a, b) => {
      // Sort by top position first (row-wise), then by left position
      const rowDiff = Math.abs(a.top - b.top);
      if (rowDiff < 50) {
        // If widgets are roughly in the same row (within 50px), sort by left position
        return a.left - b.left;
      }
      return a.top - b.top;
    });

  console.log(`🗺️ WIDGET NAVIGATOR: Organized ${widgetData.length} sections`);

  // Create clean slide-style navigator items
  widgetData.forEach((widget) => {
    const slideItem = document.createElement("div");
    slideItem.className = "slide-navigator-item";
    slideItem.dataset.widgetId = widget.id;

    slideItem.innerHTML = `
      <div class="slide-title">${widget.title}</div>
      <div class="slide-indicator"></div>
    `;

    // Add click handler for navigation
    slideItem.addEventListener("click", () => {
      console.log(
        "🗺️ WIDGET NAVIGATOR: Click detected on",
        widget.title,
        "ID:",
        widget.id
      );
      navigateToWidget(widget);
      // Add active state
      document.querySelectorAll(".slide-navigator-item").forEach((item) => {
        item.classList.remove("active");
      });
      slideItem.classList.add("active");
    });

    // Enhanced hover effects with improved state management to prevent flickering
    slideItem.addEventListener("mouseenter", () => {
      // Clear any existing global hover state immediately
      if (window.navigatorHoverState) {
        if (window.navigatorHoverState.timeout) {
          clearTimeout(window.navigatorHoverState.timeout);
          window.navigatorHoverState.timeout = null;
        }
        if (window.navigatorHoverState.debounceTimeout) {
          clearTimeout(window.navigatorHoverState.debounceTimeout);
          window.navigatorHoverState.debounceTimeout = null;
        }
      }

      // Initialize global hover state if not exists
      if (!window.navigatorHoverState) {
        window.navigatorHoverState = {
          active: false,
          timeout: null,
          currentElement: null,
          debounceTimeout: null,
        };
      }

      // Set new hover state
      window.navigatorHoverState.active = true;
      window.navigatorHoverState.currentElement = widget.element;

      // Add simple visual feedback without outline borders
      slideItem.style.backgroundColor = "rgba(0, 177, 156, 0.1)";
    });

    slideItem.addEventListener("mouseleave", () => {
      // Clear all timeouts immediately
      if (window.navigatorHoverState) {
        if (window.navigatorHoverState.timeout) {
          clearTimeout(window.navigatorHoverState.timeout);
          window.navigatorHoverState.timeout = null;
        }
        if (window.navigatorHoverState.debounceTimeout) {
          clearTimeout(window.navigatorHoverState.debounceTimeout);
          window.navigatorHoverState.debounceTimeout = null;
        }
      }

      // Reset visual feedback
      slideItem.style.backgroundColor = "";

      // Reset global hover state only if this was the active element
      if (
        window.navigatorHoverState &&
        window.navigatorHoverState.currentElement === widget.element
      ) {
        window.navigatorHoverState.active = false;
        window.navigatorHoverState.currentElement = null;
      }
    });

    navigatorList.appendChild(slideItem);
  });

  // Update current slide indicator
  updateCurrentSlideIndicator();

  console.log("🗺️ WIDGET NAVIGATOR: Clean slide view updated", {
    sections: widgets.length,
    organized: widgetData.length,
  });
}

// Get widget title with enhanced detection
function getWidgetTitle(widget) {
  // Method 1: Look for editable-title
  const editableTitle = widget.querySelector(".editable-title span");
  if (editableTitle && editableTitle.textContent.trim()) {
    return editableTitle.textContent.trim();
  }

  // Method 2: Look for widget-title-text
  const titleText = widget.querySelector(".widget-title-text");
  if (titleText && titleText.textContent.trim()) {
    return titleText.textContent.trim();
  }

  // Method 3: Look for widget-title (enhanced)
  const titleElement = widget.querySelector(".widget-title");
  if (titleElement) {
    const titleSpan = titleElement.querySelector("span");
    if (titleSpan && titleSpan.textContent.trim()) {
      return titleSpan.textContent.trim();
    }
    // Fallback: get all text but exclude icon text
    const titleText = titleElement.textContent.trim();
    if (titleText) {
      return titleText.replace(/^\s*[\w\s]*\s+/, "").trim() || titleText;
    }
  }

  // Method 4: Look in widget header (enhanced with more selectors)
  const widgetHeader = widget.querySelector(
    ".widget-header h1, .widget-header h2, .widget-header h3, .widget-header h4, .widget-header h5, .widget-header h6, .widget-header .title, .widget-header .widget-title"
  );
  if (widgetHeader && widgetHeader.textContent.trim()) {
    return widgetHeader.textContent.trim();
  }

  // Method 5: Look for card titles
  const cardTitle = widget.querySelector(
    ".card-title, .card-header h1, .card-header h2, .card-header h3, .card-header h4, .card-header h5, .card-header h6"
  );
  if (cardTitle && cardTitle.textContent.trim()) {
    return cardTitle.textContent.trim();
  }

  // Method 6: Look for any title elements within the widget
  const anyTitle = widget.querySelector(
    "h1, h2, h3, h4, h5, h6, .title, [class*='title'], [class*='heading']"
  );
  if (anyTitle && anyTitle.textContent.trim()) {
    const titleText = anyTitle.textContent.trim();
    // Filter out very generic or empty titles
    if (
      titleText.length > 2 &&
      !titleText.match(/^(widget|title|heading|\d+)$/i)
    ) {
      return titleText;
    }
  }

  // Method 7: Look for data attributes
  if (widget.dataset.title && widget.dataset.title.trim()) {
    return widget.dataset.title.trim();
  }
  if (widget.dataset.name && widget.dataset.name.trim()) {
    return widget.dataset.name.trim();
  }

  // Method 8: Check for specific widget types based on content
  const widgetClasses = widget.className.toLowerCase();
  const widgetContent = widget.innerHTML.toLowerCase();

  if (
    widgetClasses.includes("section-container") ||
    widgetContent.includes("section-container")
  ) {
    return "Section Container";
  }
  if (widgetClasses.includes("kpi") || widgetContent.includes("kpi")) {
    return "KPI Dashboard";
  }
  if (
    widgetClasses.includes("text-widget") ||
    widgetContent.includes("text-widget")
  ) {
    return "Text Content";
  }
  if (
    widgetClasses.includes("table-widget") ||
    widgetContent.includes("table-widget")
  ) {
    return "Data Table";
  }
  if (widgetClasses.includes("chart") || widgetContent.includes("chart")) {
    // Try to be more specific about chart types
    if (widgetContent.includes("pie")) return "Pie Chart";
    if (widgetContent.includes("bar")) return "Bar Chart";
    if (widgetContent.includes("line")) return "Line Chart";
    if (widgetContent.includes("area")) return "Area Chart";
    return "Chart Widget";
  }
  if (
    widgetClasses.includes("image-widget") ||
    widgetContent.includes("image-widget")
  ) {
    return "Image Gallery";
  }
  if (widgetClasses.includes("video") || widgetContent.includes("video")) {
    return "Video Player";
  }
  if (widgetClasses.includes("pdf") || widgetContent.includes("pdf")) {
    return "PDF Viewer";
  }

  // Method 9: Look for meaningful text content
  const textContent = widget.textContent.trim();
  if (textContent) {
    // Extract first meaningful line that's not too long
    const lines = textContent
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0);
    for (const line of lines) {
      if (
        line.length > 3 &&
        line.length < 50 &&
        !line.match(/^\d+$/) &&
        !line.match(/^(widget|section|container)$/i)
      ) {
        return line;
      }
    }
  }

  // Method 10: Use widget ID as fallback (improved)
  if (widget.id && !widget.id.startsWith("widget-nav-")) {
    return widget.id
      .replace(/[-_]/g, " ")
      .replace(/\b\w/g, (l) => l.toUpperCase());
  }

  // Method 11: Generate meaningful name based on position and type
  const parentElement = widget.parentElement;
  if (parentElement) {
    const siblings = Array.from(parentElement.children).filter(
      (child) =>
        child.classList.contains("grid-stack-item") &&
        child.style.display !== "none"
    );
    const position = siblings.indexOf(widget) + 1;

    // Try to determine widget type from classes or content for better naming
    if (widgetClasses.includes("chart")) return `Chart ${position}`;
    if (widgetClasses.includes("table")) return `Table ${position}`;
    if (widgetClasses.includes("text")) return `Text Block ${position}`;
    if (widgetClasses.includes("kpi")) return `KPI ${position}`;
    if (widgetClasses.includes("image")) return `Image ${position}`;

    return `Section ${position}`;
  }

  return "Untitled Widget";
}

// Get widget description
function getWidgetDescription(widget) {
  // Look for description in various places
  const desc = widget.querySelector(
    ".widget-description, .widget-subtitle, .card-subtitle"
  );
  if (desc && desc.textContent.trim()) {
    return (
      desc.textContent.trim().substring(0, 60) +
      (desc.textContent.trim().length > 60 ? "..." : "")
    );
  }

  // Check for data attributes
  if (widget.dataset.description) {
    return (
      widget.dataset.description.substring(0, 60) +
      (widget.dataset.description.length > 60 ? "..." : "")
    );
  }

  return null;
}

// Get widget type with enhanced detection
function getWidgetType(widget) {
  const classList = widget.className.toLowerCase();
  const content = widget.innerHTML.toLowerCase();
  const title = getWidgetTitle(widget).toLowerCase();

  // Enhanced type detection with more specific categories
  if (
    classList.includes("section-container") ||
    content.includes("section-container")
  )
    return "Section Container";
  if (
    classList.includes("kpi") ||
    content.includes("kpi") ||
    title.includes("kpi")
  )
    return "KPI";
  if (
    (classList.includes("chart") || content.includes("chart")) &&
    (content.includes("pie") || title.includes("pie"))
  )
    return "Pie Chart";
  if (
    (classList.includes("chart") || content.includes("chart")) &&
    (content.includes("bar") || title.includes("bar"))
  )
    return "Bar Chart";
  if (
    (classList.includes("chart") || content.includes("chart")) &&
    (content.includes("line") || title.includes("line"))
  )
    return "Line Chart";
  if (
    (classList.includes("chart") || content.includes("chart")) &&
    (content.includes("area") || title.includes("area"))
  )
    return "Area Chart";
  if (
    (classList.includes("chart") || content.includes("chart")) &&
    (content.includes("bubble") || title.includes("bubble"))
  )
    return "Bubble Chart";
  if (
    (classList.includes("chart") || content.includes("chart")) &&
    (content.includes("stock") || title.includes("stock"))
  )
    return "Stock Chart";
  if (classList.includes("chart") || content.includes("chart")) return "Chart";
  if (classList.includes("table") || content.includes("table")) return "Table";
  if (classList.includes("text") || content.includes("text")) return "Text";
  if (classList.includes("image") || content.includes("image")) return "Image";
  if (classList.includes("video") || content.includes("video")) return "Video";
  if (classList.includes("pdf") || content.includes("pdf")) return "PDF";
  if (classList.includes("note") || content.includes("note")) return "Notes";
  if (classList.includes("map") || content.includes("map")) return "Map";
  if (classList.includes("tab") || content.includes("tab")) return "Tabs";

  return "Widget";
}

// Get widget type color
function getWidgetTypeColor(type) {
  const colors = {
    "Section Container": "#6366f1",
    KPI: "#ef4444",
    "Pie Chart": "#10b981",
    "Bar Chart": "#3b82f6",
    "Line Chart": "#a855f7",
    "Area Chart": "#06b6d4",
    "Bubble Chart": "#f59e0b",
    "Stock Chart": "#84cc16",
    Chart: "#10b981",
    Table: "#3b82f6",
    Text: "#a855f7",
    Image: "#f59e0b",
    Video: "#ec4899",
    PDF: "#ef4444",
    Notes: "#6b7280",
    Map: "#059669",
    Tabs: "#7c3aed",
    Widget: "#00b19c",
  };

  return colors[type] || colors["Widget"];
}

// Create widget thumbnail
function createWidgetThumbnail(widget) {
  const type = widget.type;
  const typeColor = widget.typeColor;

  // Create different thumbnail styles based on widget type
  if (type === "Section Container") {
    return `
      <div class="thumbnail-section">
        <div class="section-header" style="background: ${typeColor}"></div>
        <div class="section-content">
          <div class="mini-widget"></div>
          <div class="mini-widget"></div>
        </div>
      </div>
    `;
  } else if (type.includes("Chart")) {
    return `
      <div class="thumbnail-chart" style="border-color: ${typeColor}">
        <div class="chart-bars" style="background: linear-gradient(45deg, ${typeColor}40, ${typeColor}80)">
          <span style="background: ${typeColor}"></span>
          <span style="background: ${typeColor}"></span>
          <span style="background: ${typeColor}"></span>
        </div>
      </div>
    `;
  } else if (type === "Table") {
    return `
      <div class="thumbnail-table" style="border-color: ${typeColor}">
        <div class="table-header" style="background: ${typeColor}40"></div>
        <div class="table-rows">
          <div style="background: ${typeColor}20"></div>
          <div style="background: ${typeColor}20"></div>
          <div style="background: ${typeColor}20"></div>
        </div>
      </div>
    `;
  } else if (type === "Text") {
    return `
      <div class="thumbnail-text" style="border-color: ${typeColor}">
        <div class="text-lines">
          <div style="background: ${typeColor}60"></div>
          <div style="background: ${typeColor}40"></div>
          <div style="background: ${typeColor}60"></div>
        </div>
      </div>
    `;
  } else if (type === "Image") {
    return `
      <div class="thumbnail-image" style="border-color: ${typeColor}; background: ${typeColor}20">
        <i class="las la-image" style="color: ${typeColor}"></i>
      </div>
    `;
  } else if (type === "KPI") {
    return `
      <div class="thumbnail-kpi" style="border-color: ${typeColor}">
        <div class="kpi-number" style="color: ${typeColor}">42</div>
        <div class="kpi-label" style="background: ${typeColor}40"></div>
      </div>
    `;
  } else {
    return `
      <div class="thumbnail-default" style="border-color: ${typeColor}; background: ${typeColor}20">
        <i class="las la-th-large" style="color: ${typeColor}"></i>
      </div>
    `;
  }
}

// Navigate to widget with smooth scrolling and highlighting
function navigateToWidget(widget) {
  console.log(
    "🗺️ WIDGET NAVIGATOR: Navigating to widget",
    widget.title,
    "ID:",
    widget.id
  );

  const element = widget.element;

  if (!element) {
    console.error(
      "🗺️ WIDGET NAVIGATOR: Widget element not found for",
      widget.id
    );
    return;
  }

  // Temporarily disable scroll event processing to prevent conflicts
  window.isNavigating = true;

  // Get current position of the element
  const rect = element.getBoundingClientRect();
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

  console.log("🗺️ WIDGET NAVIGATOR: Element position", {
    rect: rect,
    scrollTop: scrollTop,
    elementTop: rect.top + scrollTop,
  });

  // Calculate target position - position section at top with small offset
  const elementTop = rect.top + scrollTop;
  const headerOffset = 80; // Account for any fixed headers or padding
  const targetY = elementTop - headerOffset;

  console.log("🗺️ WIDGET NAVIGATOR: Scrolling to", {
    targetY: targetY,
    elementTop: elementTop,
    headerOffset: headerOffset,
  });

  // Smooth scroll to widget
  window.scrollTo({
    top: Math.max(0, targetY),
    behavior: "smooth",
  });

  // Update active state in navigator
  document.querySelectorAll(".slide-navigator-item").forEach((item) => {
    item.classList.remove("active");
  });

  // Find and activate the corresponding navigator item
  const navigatorItem = document.querySelector(
    `.slide-navigator-item[data-widget-id="${widget.id}"]`
  );
  if (navigatorItem) {
    navigatorItem.classList.add("active");
    console.log("🗺️ WIDGET NAVIGATOR: Activated navigator item for", widget.id);
  } else {
    console.warn(
      "🗺️ WIDGET NAVIGATOR: Navigator item not found for",
      widget.id
    );
  }

  // Close navigator on mobile after a delay to see the navigation
  if (window.innerWidth <= 900) {
    setTimeout(() => {
      const navigator = document.querySelector(".widget-navigator-offcanvas");
      if (navigator) {
        navigator.classList.remove("show");
        document.body.classList.remove("navigator-open");
      }
    }, 800);
  }

  // Re-enable scroll event processing and update slide indicator after navigation completes
  setTimeout(() => {
    window.isNavigating = false;
    updateCurrentSlideIndicator();
  }, 800);
}

// Update current widget indicator
function updateCurrentWidgetIndicator() {
  const navigatorItems = document.querySelectorAll(".navigator-widget-item");
  const viewportCenter = window.pageYOffset + window.innerHeight / 2;

  let currentWidget = null;
  let minDistance = Infinity;

  navigatorItems.forEach((item) => {
    const widgetId = item.dataset.widgetId;
    const widget = document.getElementById(widgetId);

    if (widget) {
      const rect = widget.getBoundingClientRect();
      const widgetCenter = rect.top + window.pageYOffset + rect.height / 2;
      const distance = Math.abs(widgetCenter - viewportCenter);

      if (distance < minDistance) {
        minDistance = distance;
        currentWidget = item;
      }
    }

    item.classList.remove("current-widget");
  });

  if (currentWidget) {
    currentWidget.classList.add("current-widget");
  }
}

// Update current slide indicator
function updateCurrentSlideIndicator() {
  const slideItems = document.querySelectorAll(".slide-navigator-item");
  const viewportCenter = window.pageYOffset + window.innerHeight / 2;

  let currentSlide = null;
  let minDistance = Infinity;

  slideItems.forEach((item) => {
    const widgetId = item.dataset.widgetId;
    const widget = document.getElementById(widgetId);

    if (widget) {
      const rect = widget.getBoundingClientRect();
      const widgetCenter = rect.top + window.pageYOffset + rect.height / 2;
      const distance = Math.abs(widgetCenter - viewportCenter);

      if (distance < minDistance) {
        minDistance = distance;
        currentSlide = item;
      }
    }

    item.classList.remove("current-slide");
  });

  if (currentSlide) {
    currentSlide.classList.add("current-slide");
  }
}

// Filter slides based on search
function filterSlides(searchTerm) {
  const slideItems = document.querySelectorAll(".slide-navigator-item");

  slideItems.forEach((item) => {
    const title = item.querySelector(".slide-title").textContent.toLowerCase();

    const matches = title.includes(searchTerm);

    item.style.display = matches ? "flex" : "none";
  });
}

// Setup smart behavior for the navigator
function setupSmartBehavior() {
  console.log("🗺️ WIDGET NAVIGATOR: Setting up smart behavior...");

  // Consolidated scroll handling with proper debouncing
  let scrollTimeout;
  let lastScrollY = window.pageYOffset;
  let isScrolling = false;

  // Single scroll event listener to handle all scroll-related functionality
  let lastScrollTime = 0;
  let scrollEventCount = 0;
  let isHighlightingActive = false;

  window.addEventListener(
    "scroll",
    () => {
      const now = Date.now();

      // Enhanced prevention of infinite loops and conflicts
      if (isScrolling || window.isNavigating) {
        // Remove console.log to prevent infinite messages
        return;
      }

      // Enhanced rapid scroll event detection
      if (now - lastScrollTime < 16) {
        // Less than 1 frame (16ms)
        scrollEventCount++;
        if (scrollEventCount > 2) {
          // Reduced threshold for better detection
          // Only log if debug mode is enabled to prevent spam
          if (window.debugWidgetNavigator) {
            console.log(
              "🗺️ WIDGET NAVIGATOR: Ignoring rapid scroll events (layout-triggered)",
              { count: scrollEventCount, timeDiff: now - lastScrollTime }
            );
          }
          return;
        }
      } else {
        scrollEventCount = 0; // Reset counter for normal scroll events
      }

      lastScrollTime = now;

      const navigator = document.querySelector(".widget-navigator-offcanvas");
      const currentScrollY = window.pageYOffset;

      // Only process if navigator is open
      if (navigator && navigator.classList.contains("show")) {
        // Mobile auto-hide behavior
        if (window.innerWidth <= 768) {
          // Hide navigator on scroll down, show on scroll up
          if (currentScrollY > lastScrollY && currentScrollY > 100) {
            navigator.style.transform = "translateX(100%)";
          } else {
            navigator.style.transform = "translateX(0)";
          }

          lastScrollY = currentScrollY;
        }

        // Debounced slide indicator update
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
          if (
            navigator &&
            navigator.classList.contains("show") &&
            !window.isNavigating
          ) {
            updateCurrentSlideIndicator();

            // Reset mobile transform after scroll stops
            if (window.innerWidth <= 768) {
              navigator.style.transform = "translateX(0)";
            }
          }
        }, 150);
      }
    },
    { passive: true }
  );

  // Auto-update navigator when window resizes
  let resizeTimeout;
  window.addEventListener("resize", () => {
    const navigator = document.querySelector(".widget-navigator-offcanvas");
    if (navigator && navigator.classList.contains("show")) {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        updateNavigatorView();
      }, 200); // Debounce resize events
    }
  });

  // Keyboard shortcuts
  document.addEventListener("keydown", (e) => {
    // Ctrl/Cmd + M to toggle navigator
    if ((e.ctrlKey || e.metaKey) && e.key === "m") {
      e.preventDefault();
      const toggleBtn = document.querySelector(".widget-navigator-toggle");
      if (toggleBtn) {
        toggleBtn.click();
      }
    }

    // Ctrl/Cmd + F to focus search when navigator is open
    if ((e.ctrlKey || e.metaKey) && e.key === "f") {
      const navigator = document.querySelector(".widget-navigator-offcanvas");
      const searchInput = document.querySelector(".navigator-search-input");

      if (navigator && navigator.classList.contains("show") && searchInput) {
        e.preventDefault();
        searchInput.focus();
        searchInput.select();
      }
    }
  });

  console.log("🗺️ WIDGET NAVIGATOR: Smart behavior setup complete");
}

// Global debugging function for testing widget detection
window.debugWidgetDetection = function () {
  console.log("🔍 DEBUG: Starting manual widget detection test...");

  // Test all possible selectors
  const selectors = [
    "#grid-container",
    "#grid-container .grid-stack-item",
    ".grid-stack",
    ".grid-stack .grid-stack-item",
    ".grid-stack-item",
    ".widget",
    ".card",
    "[data-widget-id]",
    "[class*='widget']",
    "[class*='chart']",
  ];

  selectors.forEach((selector) => {
    try {
      const elements = document.querySelectorAll(selector);
      console.log(
        `🔍 Selector '${selector}': ${elements.length} elements found`
      );

      if (elements.length > 0 && elements.length <= 10) {
        elements.forEach((el, index) => {
          console.log(
            `  [${index}] ID: ${el.id || "none"}, Classes: ${
              el.className
            }, Visible: ${el.offsetParent !== null}`
          );
        });
      }
    } catch (error) {
      console.error(`🔍 Error with selector '${selector}':`, error);
    }
  });

  // Test if navigator exists
  const navigator = document.querySelector(".widget-navigator-offcanvas");
  console.log(`🔍 Navigator element exists: ${!!navigator}`);

  // Test if toggle button exists
  const toggle = document.querySelector(".widget-navigator-toggle");
  console.log(`🔍 Toggle button exists: ${!!toggle}`);

  // Try to force update navigator view
  if (typeof updateNavigatorView === "function") {
    console.log("🔍 Calling updateNavigatorView...");
    updateNavigatorView();
  } else {
    console.error("🔍 updateNavigatorView function not available");
  }

  console.log(
    "🔍 DEBUG: Widget detection test complete. Check console output above."
  );
};

// Debug toggle for navigator logging
window.toggleNavigatorDebug = function () {
  window.debugWidgetNavigator = !window.debugWidgetNavigator;
  console.log(
    `🗺️ WIDGET NAVIGATOR: Debug mode ${
      window.debugWidgetNavigator ? "ENABLED" : "DISABLED"
    }`
  );

  if (window.debugWidgetNavigator) {
    console.log(
      "🗺️ Debug mode enabled. Scroll and hover events will now be logged."
    );
    console.log("🗺️ Call toggleNavigatorDebug() again to disable debug mode.");
  }
};

// Initialize debug mode as disabled
window.debugWidgetNavigator = false;

// --- PATCH: Unify section container drag-in markup and initialization ---
// Listen for GridStack 'dropped' event to patch drag-in for section container
if (window.grid) {
  window.grid.on("dropped", function (event, previousWidget, newWidget) {
    if (!newWidget || !newWidget.el) return;
    // Check if this is a section container drag-in (by class or data attribute)
    const el = newWidget.el;
    // If the content is empty or looks like a placeholder, replace it
    if (
      el.innerHTML.trim() === "" ||
      el.innerHTML.includes("Section container") ||
      el.innerHTML.includes("section-content") ||
      el.innerHTML.includes("section-container")
    ) {
      // Generate a unique ID for the nested grid container
      const sectionId =
        "section-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
      // Use the unified markup function
      if (window.getSectionContainerWidgetMarkup) {
        el.innerHTML = window.getSectionContainerWidgetMarkup(sectionId);
        // After DOM update, initialize the section container
        setTimeout(function () {
          if (window.initSectionContainer) {
            window.initSectionContainer(sectionId);
          }
        }, 0);
      }
    }
  });
}
