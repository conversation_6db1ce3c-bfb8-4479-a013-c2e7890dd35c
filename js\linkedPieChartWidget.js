let linkedPieChartCounter = 0;

/**
 * Generate linked pie chart widget markup
 * Shared between onclick and drag-drop functionality
 */
function getLinkedPieChartWidgetMarkup(widgetId, chartContainerId, settingsId, config) {
  return `
    <div class="linked-pie-chart-widget widget p-2" id="${widgetId}" style="height: 100%; display: flex; flex-direction: column;">
      <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
        <div class="widget-title editable-title" data-editable="true" title="Click to edit title">
          <span>${config.title}</span>
        </div>
        <div class="widget-actions">
          <button class="btn btn-link" data-bs-toggle="offcanvas" data-bs-target="#${settingsId}" aria-controls="${settingsId}">
            <i class="las la-cog"></i>
          </button>
          <button class="btn btn-link ms-1" onclick="removeWidget(this)">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex;">
        <div id="${chartContainerId}" class="chart-container" style="width: 100%; height: 100%; position: relative; min-height: 300px;"></div>
      </div>
      <!-- Widget Footer for Metadata -->
      <div class="widget-footer mt-2" style="padding: 0.5rem 0; border-top: 1px solid #e5e9f0; font-size: 10px; color: #6c757d; text-align: left;">
        ${
          config.notes
            ? `<div><i class="las la-clipboard"></i> Notes : ${config.notes}</div>`
            : ""
        }
        ${
          config.source
            ? `<div><i class="las la-database"></i> Source : ${config.source}</div>`
            : ""
        }
        ${
          config.lastUpdate || config.nextUpdate
            ? `
        <div class="d-flex mt-1">
          ${
            config.lastUpdate
              ? `<span><i class="las la-calendar-alt"></i> Last update : ${config.lastUpdate}</span>`
              : "<span></span>"
          }
          ${
            config.nextUpdate
              ? `<span class="ms-3"><i class="las la-calendar-plus"></i> Next update : ${config.nextUpdate}</span>`
              : ""
          }
        </div>
        `
            : ""
        }
      </div>
    </div>
  `;
}

// Add a Linked Pie Chart widget using amCharts v5
function addLinkedPieChartWidget(initialConfig = {}) {
  const widgetId = `linked-pie-chart-${linkedPieChartCounter++}`;
  const chartContainerId = `linked-pie-chart-container-${widgetId}`; // ID for the div holding the amCharts root
  const settingsId = `linkedPieChartSettings-${widgetId}`;

  // --- Default Data (Example Structure) ---
  const defaultData1 = [
    { category: "Category A", value: 35 },
    { category: "Category B", value: 25 },
    { category: "Category C", value: 15 },
    { category: "Category D", value: 25 },
  ];
  const defaultData2 = [
    { category: "Category A", value: 15 },
    { category: "Category B", value: 10 },
    { category: "Category C", value: 5 },
    { category: "Category D", value: 10 },
  ];

  const config = {
    title: initialConfig.title || "Linked Pie Charts",
    data1: initialConfig.data1 || defaultData1,
    data2: initialConfig.data2 || defaultData2,
    innerRadius:
      initialConfig.innerRadius !== undefined ? initialConfig.innerRadius : 60, // Percentage
    // Metadata fields with default placeholders
    notes:
      initialConfig.notes || "Default Notes: Please provide specific notes.",
    source:
      initialConfig.source ||
      "Default Source: Please provide a specific source.",
    lastUpdate: initialConfig.lastUpdate || "N/A",
    nextUpdate: initialConfig.nextUpdate || "N/A",
  };

  // DEBUG: Log the config object right before generating HTML
  console.log(
    "DEBUG: Linked Pie Config before HTML generation:",
    JSON.stringify(config, null, 2)
  );

  const grid = window.grid;
  const newWidget = grid.addWidget({
    w: initialConfig.w || 12, // Increased default width from 8 to 12
    h: initialConfig.h || 12, // Increased default height from 10 to 12
    content: getLinkedPieChartWidgetMarkup(widgetId, chartContainerId, settingsId, config)
  });

  // Create and append settings offcanvas
  createLinkedPieChartSettingsOffcanvas(
    settingsId,
    widgetId,
    chartContainerId,
    config
  );

  // Store config on the widget element
  const widgetElement = document.getElementById(widgetId);
  widgetElement.widgetConfig = config;
  widgetElement.chartContainerId = chartContainerId; // Store the container ID

  // Initialize the chart after a short delay
  setTimeout(() => {
    if (typeof am5 !== "undefined" && typeof am5percent !== "undefined") {
      initializeLinkedPieChart(chartContainerId, config);
    } else {
      console.error("amCharts core or percent libraries not loaded properly");
    }
  }, 100);

  // Make title editable (reuse function if available globally or define locally)
  if (typeof makeTitleEditable === "function") {
    makeTitleEditable(widgetId);
  } else {
    console.warn("makeTitleEditable function not found globally.");
    // Define locally if needed, or ensure it's loaded globally
  }
}

// --- initializeLinkedPieChart function ---
function initializeLinkedPieChart(chartContainerId, config) {
  console.log(
    "Initializing Linked Pie chart with container ID:",
    chartContainerId
  );
  const chartContainerElement = document.getElementById(chartContainerId);

  // Debug container dimensions
  console.log("Container dimensions:", {
    offsetWidth: chartContainerElement.offsetWidth,
    offsetHeight: chartContainerElement.offsetHeight,
    clientWidth: chartContainerElement.clientWidth,
    clientHeight: chartContainerElement.clientHeight,
  });

  if (
    !chartContainerElement ||
    typeof am5 === "undefined" ||
    typeof am5percent === "undefined"
  ) {
    console.error(
      `Chart container not found or amCharts libraries not loaded: ${chartContainerId}`
    );
    return;
  }

  // Force overflow visible on parent gridstack item content
  const gridStackItemContent = chartContainerElement.closest(
    ".grid-stack-item-content"
  );
  if (gridStackItemContent) {
    gridStackItemContent.style.overflow = "visible";
    gridStackItemContent.style.height = "100%";
  }

  // Ensure the widget and its parents have proper height
  const widget = chartContainerElement.closest(".linked-pie-chart-widget");
  if (widget) {
    widget.style.height = "100%";
    widget.style.minHeight = "0";
  }

  const widgetBody = chartContainerElement.closest(".widget-body");
  if (widgetBody) {
    widgetBody.style.flex = "1 1 auto";
    widgetBody.style.minHeight = "0";
    widgetBody.style.position = "relative";
    widgetBody.style.display = "flex"; // Add flex display
  }

  // Set container styles
  chartContainerElement.style.width = "100%";
  chartContainerElement.style.height = "100%";
  chartContainerElement.style.position = "relative";
  chartContainerElement.style.flex = "1"; // Add flex: 1

  // Dispose existing chart root if any
  if (chartContainerElement.amRoot) {
    chartContainerElement.amRoot.dispose();
  }

  try {
    // Create root element
    const root = am5.Root.new(chartContainerId);
    chartContainerElement.amRoot = root;

    // Set themes
    root.setThemes([am5themes_Animated.new(root)]);

    // Set root container settings
    root.container.set("paddingBottom", 0);
    root.container.set("paddingTop", 0);
    root.container.set("height", am5.p100);
    root.container.set("width", am5.p100);
    root.container.set("layout", root.verticalLayout);

    // Debug root dimensions
    console.log("Root dimensions:", {
      width: root.width(),
      height: root.height(),
    });

    // Apply brand colors if available (reusing logic)
    let colors;
    if (window.chartConfig && window.chartConfig.brandColors) {
      colors = am5.ColorSet.new(root, {
        colors: window.chartConfig.brandColors.map((hex) => am5.color(hex)),
      });
      console.log(
        "Applied brand colors to linked pie chart:",
        window.chartConfig.brandColors
      );
    } else {
      console.warn("Brand colors not found. Using default theme colors.");
      colors = root.container.get("colors");
    }

    // Main container for layout (Vertical: Charts container + Legend)
    const mainContainer = root.container.children.push(
      am5.Container.new(root, {
        width: am5.p100,
        height: am5.p100,
        layout: root.verticalLayout,
        paddingTop: 0,
        paddingBottom: 0,
      })
    );

    // Container to hold the two charts horizontally
    const chartsContainer = mainContainer.children.push(
      am5.Container.new(root, {
        layout: root.horizontalLayout,
        width: am5.p100,
        height: am5.p100,
        centerX: am5.p50,
        x: am5.p50,
        paddingLeft: 20,
        paddingRight: 20,
      })
    );

    // Add chart1 to the horizontal chartsContainer
    const chart1 = chartsContainer.children.push(
      am5percent.PieChart.new(root, {
        radius: am5.percent(85),
        centerY: am5.p50,
        y: am5.p50,
        layout: root.verticalLayout,
      })
    );

    const series1 = chart1.series.push(
      am5percent.PieSeries.new(root, {
        valueField: "value",
        categoryField: "category",
        alignLabels: false,
        radius: am5.percent(85),
        innerRadius: am5.percent(config.innerRadius),
      })
    );

    // Apply colors to series1
    series1.set("colors", colors);

    // Increase label size for better readability
    series1.labels.template.setAll({
      fontSize: 12,
      maxWidth: 120,
    });

    series1.ticks.template.setAll({
      forceHidden: false,
      strokeOpacity: 0.4,
    });

    // Add chart2 to the horizontal chartsContainer
    const chart2 = chartsContainer.children.push(
      am5percent.PieChart.new(root, {
        radius: am5.percent(85),
        centerY: am5.p50,
        y: am5.p50,
        layout: root.verticalLayout,
      })
    );

    const series2 = chart2.series.push(
      am5percent.PieSeries.new(root, {
        valueField: "value",
        categoryField: "category",
        alignLabels: false,
        radius: am5.percent(85),
        innerRadius: am5.percent(config.innerRadius),
      })
    );

    // Apply colors to series2
    series2.set("colors", colors);

    // Match label settings for consistency
    series2.labels.template.setAll({
      fontSize: 12,
      maxWidth: 120,
    });

    series2.ticks.template.setAll({
      forceHidden: false,
      strokeOpacity: 0.4,
    });

    // --- Linking Interaction Logic ---
    function getSlice(dataItem, targetSeries) {
      let otherSlice;
      am5.array.each(targetSeries.dataItems, function (di) {
        if (di.get("category") === dataItem.get("category")) {
          otherSlice = di.get("slice");
        }
      });
      return otherSlice;
    }

    // Link series 1 events to series 2
    series1.slices.template.events.on("pointerover", function (ev) {
      const otherSlice = getSlice(ev.target.dataItem, series2);
      if (otherSlice) otherSlice.hover();
    });
    series1.slices.template.events.on("pointerout", function (ev) {
      const otherSlice = getSlice(ev.target.dataItem, series2);
      if (otherSlice) otherSlice.unhover();
    });
    series1.slices.template.on("active", function (active, target) {
      const otherSlice = getSlice(target.dataItem, series2);
      if (otherSlice) otherSlice.set("active", active);
    });

    // Link series 2 events to series 1
    series2.slices.template.events.on("pointerover", function (ev) {
      const otherSlice = getSlice(ev.target.dataItem, series1);
      if (otherSlice) otherSlice.hover();
    });
    series2.slices.template.events.on("pointerout", function (ev) {
      const otherSlice = getSlice(ev.target.dataItem, series1);
      if (otherSlice) otherSlice.unhover();
    });
    series2.slices.template.on("active", function (active, target) {
      const otherSlice = getSlice(target.dataItem, series1);
      if (otherSlice) otherSlice.set("active", active);
    });

    // --- Set Data ---
    series1.data.setAll(config.data1);
    series2.data.setAll(config.data2);

    // --- Create Legend ---
    // Add legend with adjusted settings
    const legend = mainContainer.children.push(
      am5.Legend.new(root, {
        centerX: am5.p50,
        x: am5.p50,
        layout: root.horizontalLayout,
        height: 30,
        paddingTop: 15,
        marginTop: 15,
      })
    );

    // Improve legend readability
    legend.labels.template.setAll({
      fontSize: 12,
      fontWeight: "500",
    });

    legend.valueLabels.template.setAll({
      fontSize: 12,
    });

    legend.markers.template.setAll({
      width: 16,
      height: 16,
    });

    // Apply same colors to legend
    legend.set("colors", colors);

    // Link legend events to series 2 slices (as per example)
    legend.itemContainers.template.events.on("pointerover", function (ev) {
      const slice = getSlice(ev.target.dataItem.dataContext, series2);
      if (slice) slice.hover();
    });
    legend.itemContainers.template.events.on("pointerout", function (ev) {
      const slice = getSlice(ev.target.dataItem.dataContext, series2);
      if (slice) slice.unhover();
    });
    legend.itemContainers.template.on("disabled", function (disabled, target) {
      const slice = getSlice(target.dataItem.dataContext, series2);
      if (slice) {
        if (disabled) series2.hideDataItem(slice.dataItem);
        else series2.showDataItem(slice.dataItem);
      }
    });

    // Set legend data (using series1 data items as the source of truth for categories)
    legend.data.setAll(series1.dataItems);

    // Appear animations
    series1.appear(1000, 100);
    series2.appear(1000, 100);

    // Add resize observer
    const resizeObserver = new ResizeObserver(() => {
      root.resize();
    });
    resizeObserver.observe(chartContainerElement);
    chartContainerElement.resizeObserver = resizeObserver; // Store for cleanup

    // Force resize after setup
    root.resize();
  } catch (error) {
    console.error("Error initializing Linked Pie chart:", error);
  }
}

// --- createLinkedPieChartSettingsOffcanvas function ---
function createLinkedPieChartSettingsOffcanvas(
  settingsId,
  widgetId,
  chartContainerId,
  currentConfig
) {
  const offcanvasContainer =
    document.getElementById("offcanvasContainer") || document.body;

  // Convert data objects to pretty-printed JSON strings for textareas
  const data1Json = JSON.stringify(currentConfig.data1, null, 2);
  const data2Json = JSON.stringify(currentConfig.data2, null, 2);

  const offcanvasHtml = `
    <div class="offcanvas offcanvas-end" tabindex="-1" id="${settingsId}" aria-labelledby="${settingsId}Label">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="${settingsId}Label">Linked Pie Chart Settings</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body"> 
            <!-- Chart Appearance -->
            <h6 class="mb-3">Chart Appearance</h6>
            <div class="mb-3">
                <label class="form-label">Chart Title</label>
                <input type="text" class="form-control form-control-sm" id="${settingsId}-chartTitle" value="${currentConfig.title}">
            </div>
            <div class="mb-3">
                <label for="${settingsId}-innerRadius" class="form-label">Inner Radius (%) <span class="text-muted">(${currentConfig.innerRadius}%)</span></label>
                <input type="range" class="form-range" min="0" max="90" step="5" value="${currentConfig.innerRadius}" id="${settingsId}-innerRadius" oninput="this.previousElementSibling.querySelector('span').textContent = '('+this.value+'%)'">
            </div>

            <!-- Data Management -->
            <div class="mb-4">
              <h6 class="mb-3">Data Management</h6>
              <div class="mb-3">
                  <label class="form-label">Data Set 1 (JSON format)</label>
                  <textarea class="form-control form-control-sm" id="${settingsId}-data1" rows="5" spellcheck="false">${data1Json}</textarea>
              </div>
               <div class="mb-3">
                  <label class="form-label">Data Set 2 (JSON format)</label>
                  <textarea class="form-control form-control-sm" id="${settingsId}-data2" rows="5" spellcheck="false">${data2Json}</textarea>
              </div>
            </div>

            <!-- Metadata -->
            <div class="mb-4">
                <h6 class="mb-3">Metadata</h6>
                <div class="mb-3">
                    <label class="form-label">Notes</label>
                    <textarea class="form-control form-control-sm" id="${settingsId}-notes" rows="2">${currentConfig.notes}</textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Source</label>
                    <textarea class="form-control form-control-sm" id="${settingsId}-source" rows="2">${currentConfig.source}</textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Last Update</label>
                    <input type="text" class="form-control form-control-sm" id="${settingsId}-lastUpdate" value="${currentConfig.lastUpdate}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Next Update</label>
                    <input type="text" class="form-control form-control-sm" id="${settingsId}-nextUpdate" value="${currentConfig.nextUpdate}">
                </div>
            </div>

            <!-- Apply Button -->
            <button class="btn btn-primary w-100" onclick="applyLinkedPieChartSettings('${widgetId}', '${settingsId}', '${chartContainerId}')">Apply Changes</button>
        </div>
    </div>
    `;

  // Append the offcanvas HTML to the container
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = offcanvasHtml;
  offcanvasContainer.appendChild(tempDiv.firstElementChild);
}

// --- applyLinkedPieChartSettings function ---
function applyLinkedPieChartSettings(widgetId, settingsId, chartContainerId) {
  const widgetElement = document.getElementById(widgetId);
  if (!widgetElement || !widgetElement.widgetConfig) {
    console.error("Widget element or config not found for ID:", widgetId);
    return;
  }

  const currentConfig = widgetElement.widgetConfig;

  // Get general settings
  currentConfig.title = document.getElementById(
    `${settingsId}-chartTitle`
  ).value;
  currentConfig.innerRadius = parseInt(
    document.getElementById(`${settingsId}-innerRadius`).value,
    10
  );

  // Get and parse data (with error handling)
  try {
    const data1Input = document.getElementById(`${settingsId}-data1`).value;
    currentConfig.data1 = JSON.parse(data1Input);
  } catch (e) {
    console.error("Invalid JSON in Data Set 1:", e);
    alert(
      "Error: Invalid JSON format in Data Set 1. Please correct and try again."
    );
    return; // Stop applying settings if data is invalid
  }
  try {
    const data2Input = document.getElementById(`${settingsId}-data2`).value;
    currentConfig.data2 = JSON.parse(data2Input);
  } catch (e) {
    console.error("Invalid JSON in Data Set 2:", e);
    alert(
      "Error: Invalid JSON format in Data Set 2. Please correct and try again."
    );
    return; // Stop applying settings if data is invalid
  }

  // Get metadata
  currentConfig.notes = document.getElementById(`${settingsId}-notes`).value;
  currentConfig.source = document.getElementById(`${settingsId}-source`).value;
  currentConfig.lastUpdate = document.getElementById(
    `${settingsId}-lastUpdate`
  ).value;
  currentConfig.nextUpdate = document.getElementById(
    `${settingsId}-nextUpdate`
  ).value;

  // Update widget title in the header
  const titleSpan = widgetElement.querySelector(".widget-title span");
  if (titleSpan) {
    titleSpan.textContent = currentConfig.title;
  }

  // Update the widget footer (assuming updateWidgetFooter is available)
  if (typeof updateWidgetFooter === "function") {
    updateWidgetFooter(widgetElement, currentConfig);
  } else {
    console.warn(
      "updateWidgetFooter function not found to update footer display."
    );
  }

  // Re-initialize the chart with new settings
  initializeLinkedPieChart(chartContainerId, currentConfig);

  // Close the offcanvas
  const offcanvasElement = document.getElementById(settingsId);
  if (offcanvasElement) {
    const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvasElement);
    if (offcanvasInstance) {
      offcanvasInstance.hide();
    }
  }

  console.log("Applied Linked Pie Chart settings:", currentConfig);
}

// --- updateWidgetFooter (reuse from bubble chart or define here) ---
// Assume updateWidgetFooter exists globally or copy its definition here
if (typeof updateWidgetFooter !== "function") {
  function updateWidgetFooter(widgetElement, config) {
    const footerElement = widgetElement.querySelector(".widget-footer");
    if (!footerElement) {
      console.warn("Widget footer element not found for update.");
      return;
    }
    footerElement.innerHTML = "";
    let footerHtml = "";
    if (config.notes)
      footerHtml += `<div><i class="las la-clipboard"></i> Notes : ${config.notes}</div>`;
    if (config.source)
      footerHtml += `<div><i class="las la-database"></i> Source : ${config.source}</div>`;
    if (config.lastUpdate || config.nextUpdate) {
      footerHtml += '<div class="d-flex mt-1">';
      if (config.lastUpdate)
        footerHtml += `<span><i class="las la-calendar-alt"></i> Last update : ${config.lastUpdate}</span>`;
      else footerHtml += "<span></span>";
      if (config.nextUpdate)
        footerHtml += `<span class="ms-3"><i class="las la-calendar-plus"></i> Next update : ${config.nextUpdate}</span>`;
      footerHtml += "</div>";
    }
    footerElement.innerHTML = footerHtml;
  }
}

/**
 * Setup drag-drop functionality for linked pie chart widgets
 * This ensures both onclick and drag-drop use the same markup and logic
 */
function setupLinkedPieChartDragDrop() {
  console.log('🔗 Setting up linked pie chart drag-drop functionality...');

  const linkedPieChartSidebarContent = [
    {
      w: 12,
      h: 12,
      get content() {
        const widgetId = `linked-pie-chart-${linkedPieChartCounter++}`;
        const chartContainerId = `linked-pie-chart-container-${widgetId}`;
        const settingsId = `linkedPieChartSettings-${widgetId}`;

        // Default configuration for drag-drop
        const config = {
          title: "Linked Pie Charts",
          data1: [
            { category: "Category A", value: 35 },
            { category: "Category B", value: 25 },
            { category: "Category C", value: 15 },
            { category: "Category D", value: 25 },
          ],
          data2: [
            { category: "Category A", value: 15 },
            { category: "Category B", value: 10 },
            { category: "Category C", value: 5 },
            { category: "Category D", value: 10 },
          ],
          innerRadius: 60,
          notes: "Default Notes: Please provide specific notes.",
          source: "Default Source: Please provide a specific source.",
          lastUpdate: "N/A",
          nextUpdate: "N/A",
        };

        // Use the same markup function as onclick
        const markup = getLinkedPieChartWidgetMarkup(widgetId, chartContainerId, settingsId, config);

        // Store initialization data for the grid "added" event handler to pick up
        // This is more reliable than trying to find the element immediately
        if (!window.pendingLinkedPieChartInits) {
          window.pendingLinkedPieChartInits = new Map();
        }

        window.pendingLinkedPieChartInits.set(widgetId, {
          widgetId,
          chartContainerId,
          settingsId,
          config,
          timestamp: Date.now()
        });

        console.log('🔗 Drag-drop: Stored initialization data for', widgetId);

        return markup;
      },
    },
  ];

  // Setup GridStack drag-in
  if (typeof GridStack !== 'undefined' && GridStack.setupDragIn) {
    GridStack.setupDragIn(
      '.widget-item[data-widget-type="linked-pie-chart"]',
      undefined,
      linkedPieChartSidebarContent
    );
    console.log('✅ Linked pie chart drag-in setup complete');
  } else {
    console.warn('⚠️ GridStack not available, skipping linked pie chart drag-in setup');
  }
}

// Cleanup old pending initializations (prevent memory leaks)
function cleanupOldPendingInits() {
  if (window.pendingLinkedPieChartInits) {
    const now = Date.now();
    const maxAge = 30000; // 30 seconds

    for (const [widgetId, initData] of window.pendingLinkedPieChartInits.entries()) {
      if (now - initData.timestamp > maxAge) {
        console.warn('🔗 Cleaning up old pending initialization for', widgetId);
        window.pendingLinkedPieChartInits.delete(widgetId);
      }
    }
  }
}

// Run cleanup periodically
setInterval(cleanupOldPendingInits, 60000); // Every minute

// Auto-setup drag-drop when this script loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', setupLinkedPieChartDragDrop);
} else {
  // If DOM is already loaded, setup immediately
  setTimeout(setupLinkedPieChartDragDrop, 100);
}

// --- Global Exports ---
window.addLinkedPieChartWidget = addLinkedPieChartWidget;
window.getLinkedPieChartWidgetMarkup = getLinkedPieChartWidgetMarkup; // Export markup function
window.setupLinkedPieChartDragDrop = setupLinkedPieChartDragDrop; // Export drag-drop setup
window.initializeLinkedPieChart = initializeLinkedPieChart; // Export chart initialization
window.createLinkedPieChartSettingsOffcanvas = createLinkedPieChartSettingsOffcanvas; // Export settings creation
window.applyLinkedPieChartSettings = applyLinkedPieChartSettings; // Export apply function
