/* Notes Section Widget Styles */
.notes-section-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notes-section-widget .widget-header {
  padding: 8px 12px;
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  border-radius: 6px 6px 0 0;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.notes-section-widget .widget-header button {
  background: none;
  border: none;
  color: white;
  font-size: 14px;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.notes-section-widget .widget-header button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.notes-section-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 8px;
  overflow: hidden;
}

/* Widget Footer Styles - matching the provided markup */
.widget-footer {
  background: transparent;
  border: none;
  padding: 8px 0;
  font-size: 10px;
  line-height: 1.4;
  color: #666;
}

.widget-footer .mb-0 {
  margin-bottom: 0 !important;
}

/* Size classes */
.size10 {
  font-size: 10px;
}

.size14 {
  font-size: 14px;
}

/* Key Insight Sections */
.notes-keyInsight,
.source-keyInsight,
.last-update-keyInsight,
.next-update-keyInsight {
  display: block;
  margin-bottom: 4px;
  color: #666;
  font-size: 10px;
  line-height: 1.4;
}

/* Notes and Source sections - full width on their own lines */
.notes-keyInsight,
.source-keyInsight {
  width: 100%;
}

/* Last update and Next update sections - side by side on same line */
.last-update-keyInsight,
.next-update-keyInsight {
  display: inline-block;
  width: auto;
  margin-right: 16px;
}

/* Ensure last update and next update are on the same line */
.last-update-keyInsight {
  margin-bottom: 0;
}

.next-update-keyInsight {
  margin-bottom: 0;
}

.notes-keyInsight i,
.source-keyInsight i,
.last-update-keyInsight i,
.next-update-keyInsight i {
  margin-right: 4px;
  color: #888;
}

/* Content spans */
.notes-content,
.source-content,
.last-update-content,
.next-update-content {
  font-weight: 500;
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .notes-section-widget .widget-header {
    padding: 6px 8px;
    font-size: 11px;
  }

  .notes-section-container {
    padding: 6px;
  }

  .widget-footer {
    padding: 6px 0;
  }

  /* Maintain the same layout on mobile */
  .notes-keyInsight,
  .source-keyInsight {
    display: block;
    width: 100%;
    margin-bottom: 4px;
  }

  .last-update-keyInsight,
  .next-update-keyInsight {
    display: inline-block;
    width: auto;
    margin-right: 12px;
    margin-bottom: 0;
  }
}

/* Settings panel styles */
.notes-section-widget .offcanvas-body {
  padding: 20px;
}

.notes-section-widget .form-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.notes-section-widget .form-control,
.notes-section-widget .form-select {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.notes-section-widget .form-control:focus,
.notes-section-widget .form-select:focus {
  border-color: #6c757d;
  box-shadow: 0 0 0 2px rgba(108, 117, 125, 0.2);
  outline: none;
}

.notes-section-widget .form-check-input:checked {
  background-color: #6c757d;
  border-color: #6c757d;
}

.notes-section-widget .form-check-label {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.notes-section-widget .form-check-label i {
  font-size: 16px;
}

.notes-section-widget .btn-primary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-weight: 600;
  transition: transform 0.2s, box-shadow 0.2s;
}

.notes-section-widget .btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.4);
}

/* Form switch styling */
.notes-section-widget .form-switch .form-check-input {
  width: 2em;
  height: 1em;
  margin-top: 0.25em;
}

.notes-section-widget .form-switch .form-check-input:checked {
  background-color: #6c757d;
  border-color: #6c757d;
}

/* Prevent text selection on widget header */
.notes-section-widget .widget-header {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Ensure proper spacing in grid */
.grid-stack-item .notes-section-widget {
  margin: 0;
  height: 100%;
}

/* Animation for smooth transitions */
.notes-section-widget * {
  transition: all 0.2s ease;
}

/* Print styles */
@media print {
  .notes-section-widget .widget-header button {
    display: none;
  }

  .notes-section-widget {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .widget-footer {
    color: #000 !important;
  }

  .notes-content,
  .source-content,
  .last-update-content,
  .next-update-content {
    color: #000 !important;
  }
}

/* Special styling for very small widgets */
.grid-stack-item[gs-h="1"] .notes-section-widget .widget-header {
  padding: 4px 8px;
  font-size: 10px;
}

.grid-stack-item[gs-h="1"] .notes-section-container {
  padding: 4px;
}

.grid-stack-item[gs-h="1"] .widget-footer {
  padding: 4px 0;
}

/* Hover effects for better user experience */
.notes-section-widget:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Focus styles for accessibility */
.notes-section-widget:focus-within {
  outline: 2px solid #6c757d;
  outline-offset: 2px;
}

/* Ensure footer stays at bottom */
.notes-section-container {
  justify-content: flex-end;
}

/* Spacing between sections - updated for new layout */
.notes-keyInsight {
  margin-bottom: 4px;
}

.source-keyInsight {
  margin-bottom: 4px;
}

.last-update-keyInsight {
  margin-right: 16px;
}

.next-update-keyInsight {
  margin-right: 0;
}

/* Icon alignment */
.notes-keyInsight i,
.source-keyInsight i,
.last-update-keyInsight i,
.next-update-keyInsight i {
  vertical-align: middle;
  margin-right: 4px;
}

/* Compact mode for smaller widgets */
.grid-stack-item[gs-w="3"] .notes-keyInsight,
.grid-stack-item[gs-w="3"] .source-keyInsight {
  display: block;
  width: 100%;
  margin-bottom: 2px;
}

.grid-stack-item[gs-w="3"] .last-update-keyInsight,
.grid-stack-item[gs-w="3"] .next-update-keyInsight {
  display: inline-block;
  width: auto;
  margin-right: 8px;
  margin-bottom: 0;
}
