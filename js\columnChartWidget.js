let columnChartCounter = 0;

/**
 * Generate column chart widget markup
 * Shared between onclick and drag-drop functionality
 */
function getColumnChartWidgetMarkup(chartId) {
  return `
    <div class="widget p-2">
      <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
        <div class="widget-title">
          <span>Column Chart</span>
        </div>
        <div class="widget-actions">
          <button class="btn btn-link"
                  data-bs-toggle="offcanvas"
                  data-bs-target="#columnChartSettings"
                  aria-controls="columnChartSettings"
                  onclick="initColumnChartSettings('${chartId}')">
            <i class="las la-cog"></i>
          </button>
          <button class="btn btn-link ms-1"
                  onclick="removeWidget(this)">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
        <div id="${chartId}" class="chart-container" style="flex: 1; width: 100%; height: 100%; min-height: 400px; position: relative;"></div>
      </div>
    </div>
  `;
}

// Add a column chart widget using amCharts v5
function addColumnChartWidget() {
  console.log("🔄 Adding column chart widget...");

  // Check if grid is available
  if (typeof window.grid === 'undefined' && typeof grid === 'undefined') {
    console.error("❌ Grid not available. Make sure GridStack is initialized.");
    console.log("🔍 Available globals:", Object.keys(window).filter(k => k.includes('grid') || k.includes('Grid')));
    alert("Grid not available. Please refresh the page and try again.");
    return;
  }

  // Use global grid variable (try both window.grid and grid)
  const gridInstance = window.grid || grid;

  try {
    const chartId = `column-chart-${Date.now()}`;
    console.log("📊 Creating column chart with ID:", chartId);

    // Add the widget to the grid using shared markup
    gridInstance.addWidget({
      x: 0,
      y: 0,
      w: 6,
      h: 4,
      content: getColumnChartWidgetMarkup(chartId)
    });

    console.log("✅ Widget added to grid successfully");

    // Initialize the chart after a short delay to ensure DOM is ready
    setTimeout(async () => {
      try {
        console.log("🔄 Initializing column chart...");
        await waitForAmCharts();
        await initColumnChart(chartId);
        console.log("✅ Column chart initialized successfully");
      } catch (error) {
        console.error("❌ Error initializing column chart:", error);
        
        // Show error in the chart container
        const container = document.getElementById(chartId);
        if (container) {
          container.innerHTML = `
            <div class="alert alert-danger m-3">
              <h6>Chart Initialization Failed</h6>
              <p>Failed to initialize column chart. Please try again.</p>
              <small class="text-muted">${error.message}</small>
            </div>
          `;
        }
      }
    }, 100);

  } catch (error) {
    console.error("❌ Error adding column chart widget:", error);
    alert("Failed to add column chart widget. Please try again.");
  }
}

/**
 * Initialize column chart with amCharts v5
 */
async function initColumnChart(chartId) {
  console.log("Initializing column chart:", chartId);

  const chartContainer = document.getElementById(chartId);
  if (!chartContainer) {
    throw new Error(`Chart container with ID ${chartId} not found`);
  }

  // Check if chart is already being initialized or already exists
  if (chartContainer.hasAttribute('data-initializing')) {
    console.log("⚠️ Chart is already being initialized, skipping...");
    return;
  }

  if (chartContainer.hasAttribute('data-initialized')) {
    console.log("⚠️ Chart is already initialized, skipping...");
    return;
  }

  // Mark as initializing
  chartContainer.setAttribute('data-initializing', 'true');

  // Check if there's already a chart instance and dispose it
  if (chartContainer.chart) {
    console.log("🔄 Disposing existing chart instance...");
    try {
      chartContainer.chart.dispose();
    } catch (e) {
      console.warn("Warning disposing chart:", e);
    }
    chartContainer.chart = null;
  }

  // Clear any existing content
  chartContainer.innerHTML = '';

  // Ensure container has proper dimensions
  if (chartContainer.offsetWidth === 0 || chartContainer.offsetHeight === 0) {
    console.warn("⚠️ Chart container has zero dimensions, setting minimum size");
    chartContainer.style.minWidth = '400px';
    chartContainer.style.minHeight = '300px';
  }

  // Add temporary visual indicator
  chartContainer.style.border = '2px dashed #00b19c';
  chartContainer.style.backgroundColor = '#f8f9fa';

  // Add loading text
  chartContainer.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">Loading Column Chart...</div>';

  console.log("📊 Container prepared with dimensions:", chartContainer.offsetWidth, 'x', chartContainer.offsetHeight);

  try {
    // Clear loading indicator
    chartContainer.innerHTML = '';
    chartContainer.style.border = 'none';
    chartContainer.style.backgroundColor = 'transparent';

    // Create root element
    const root = am5.Root.new(chartId);

    // Set themes with custom branding
    root.setThemes([
      am5themes_Animated.new(root)
    ]);

    // Create chart
    const chart = root.container.children.push(am5xy.XYChart.new(root, {
      panX: false,
      panY: false,
      wheelX: "panX",
      wheelY: "zoomX",
      paddingLeft: 0,
      layout: root.verticalLayout
    }));

    // Sample data - using timestamps (milliseconds) for amCharts v5 compatibility
    const data = [
      { "date": 1672531200000, "value": 100 },  // Jan 2023
      { "date": 1675209600000, "value": 120 },  // Feb 2023
      { "date": 1677628800000, "value": 90 },   // Mar 2023
      { "date": 1680307200000, "value": 150 },  // Apr 2023
      { "date": 1682899200000, "value": 180 },  // May 2023
      { "date": 1685577600000, "value": 200 },  // Jun 2023
      { "date": 1688169600000, "value": 220 },  // Jul 2023
      { "date": 1690848000000, "value": 250 },  // Aug 2023
      { "date": 1693526400000, "value": 280 },  // Sep 2023
      { "date": 1696118400000, "value": 300 },  // Oct 2023
      { "date": 1698796800000, "value": 320 },  // Nov 2023
      { "date": 1701388800000, "value": 350 },  // Dec 2023
      { "date": 1704067200000, "value": 400 },  // Jan 2024
      { "date": 1706745600000, "value": 450 },  // Feb 2024
      { "date": 1709251200000, "value": 420 },  // Mar 2024
      { "date": 1711929600000, "value": 480 },  // Apr 2024
      { "date": 1714521600000, "value": 520 },  // May 2024
      { "date": 1717200000000, "value": 550 },  // Jun 2024
      { "date": 1719792000000, "value": 580 },  // Jul 2024
      { "date": 1722470400000, "value": 600 },  // Aug 2024
      { "date": 1725148800000, "value": 650 },  // Sep 2024
      { "date": 1727740800000, "value": 680 },  // Oct 2024
      { "date": 1730419200000, "value": 720 },  // Nov 2024
      { "date": 1733011200000, "value": 750 }   // Dec 2024
    ];

    // Create axes
    // X-axis (Date axis)
    const xAxis = chart.xAxes.push(am5xy.DateAxis.new(root, {
      maxDeviation: 0,
      baseInterval: {
        timeUnit: "month",
        count: 1
      },
      renderer: am5xy.AxisRendererX.new(root, {
        minorGridEnabled: false,
        minGridDistance: 50,
        cellStartLocation: 0.1,
        cellEndLocation: 0.9
      }),
      tooltip: am5.Tooltip.new(root, {})
    }));

    console.log("📊 Created X-axis (Date axis)");

    // Style X-axis labels
    xAxis.get("renderer").labels.template.setAll({
      fontSize: "12px",
      fontFamily: "Montserrat, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif",
      fill: am5.color("#02104f") // Brand navy color
    });

    // Y-axis (Value axis)
    const yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
      renderer: am5xy.AxisRendererY.new(root, {
        strokeOpacity: 0.1
      }),
      tooltip: am5.Tooltip.new(root, {})
    }));

    // Style Y-axis labels
    yAxis.get("renderer").labels.template.setAll({
      fontSize: "12px",
      fontFamily: "Montserrat, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif",
      fill: am5.color("#02104f") // Brand navy color
    });

    console.log("📊 Created Y-axis (Value axis)");

    // Create series (columns)
    const series = chart.series.push(am5xy.ColumnSeries.new(root, {
      name: "Column Series",
      xAxis: xAxis,
      yAxis: yAxis,
      valueYField: "value",
      valueXField: "date",
      tooltip: am5.Tooltip.new(root, {
        labelText: "{valueY}",
        pointerOrientation: "vertical"
      })
    }));

    console.log("📊 Created column series");

    // Apply brand colors - simplified approach
    const brandColors = window.chartConfig?.brandColors || [
      "#00b19c", "#3bcd3f", "#007365", "#8dbac4", "#02104f"
    ];

    // Set the primary brand color for all columns
    const primaryColor = am5.color(brandColors[0]); // Use first brand color

    console.log("Applied brand color to column chart:", brandColors[0]);

    // Custom styling
    series.columns.template.setAll({
      fill: primaryColor,
      stroke: am5.color("#ffffff"),
      strokeWidth: 2,
      cornerRadiusTL: 4,
      cornerRadiusTR: 4,
      fillOpacity: 0.9,
      width: am5.percent(80)
    });

    // Hover effects
    series.columns.template.states.create("hover", {
      fillOpacity: 1,
      strokeWidth: 3
    });

    // Add scrollbar
    chart.set("scrollbarX", am5.Scrollbar.new(root, {
      orientation: "horizontal"
    }));

    // Add cursor
    const cursor = chart.set("cursor", am5xy.XYCursor.new(root, {
      behavior: "none"
    }));
    cursor.lineY.set("visible", false);

    // Set data
    console.log("📊 Setting data for column chart:", data.length, "data points");
    console.log("📊 Sample data point:", data[0]);

    series.data.setAll(data);
    xAxis.data.setAll(data);

    console.log("📊 Data set on series and axis");

    // Animations
    series.appear(1000, 100);
    chart.appear(1000, 100);

    // Store chart reference for cleanup
    chartContainer.chart = root;

    // Check container dimensions
    const containerRect = chartContainer.getBoundingClientRect();
    console.log("📊 Container dimensions:", {
      width: containerRect.width,
      height: containerRect.height,
      visible: containerRect.width > 0 && containerRect.height > 0
    });

    // Force a resize to ensure proper rendering
    setTimeout(() => {
      if (root && !root.isDisposed()) {
        root.resize();
        console.log("📊 Forced chart resize");
      }
    }, 100);

    // Mark as successfully initialized
    chartContainer.removeAttribute('data-initializing');
    chartContainer.setAttribute('data-initialized', 'true');

    console.log("✅ Column chart initialized successfully");

  } catch (error) {
    console.error("❌ Error in initColumnChart:", error);
    
    // Clean up on error
    chartContainer.removeAttribute('data-initializing');
    
    // Show error message in container
    chartContainer.innerHTML = `
      <div class="alert alert-danger m-3">
        <h6>Chart Initialization Failed</h6>
        <p>Failed to initialize column chart.</p>
        <small class="text-muted">${error.message}</small>
        <br>
        <button class="btn btn-sm btn-outline-danger mt-2" onclick="initColumnChart('${chartId}')">
          Retry
        </button>
      </div>
    `;
    
    throw error;
  }
}

/**
 * Wait for amCharts libraries to load
 */
function waitForAmCharts() {
  return new Promise((resolve, reject) => {
    let attempts = 0;
    const maxAttempts = 50;
    
    const checkAmCharts = () => {
      attempts++;
      
      if (typeof am5 !== 'undefined' && typeof am5xy !== 'undefined' && typeof am5themes_Animated !== 'undefined') {
        resolve();
      } else if (attempts >= maxAttempts) {
        reject(new Error('amCharts libraries failed to load after maximum attempts'));
      } else {
        setTimeout(checkAmCharts, 100);
      }
    };
    
    checkAmCharts();
  });
}

/**
 * Initialize settings for column chart (placeholder)
 */
function initColumnChartSettings(chartId) {
  console.log('Opening settings for column chart:', chartId);
  // Settings functionality can be implemented later
}

/**
 * Setup drag-drop functionality for column chart widgets
 * This ensures both onclick and drag-drop use the same markup and logic
 */
function setupColumnChartDragDrop() {
  console.log('📊 Setting up column chart drag-drop functionality...');

  const columnChartSidebarContent = [
    {
      w: 6,
      h: 4,
      get content() {
        const chartId = "column-chart-" + Date.now() + "-" + Math.floor(Math.random() * 100000);

        // Use the same markup function as onclick
        const markup = getColumnChartWidgetMarkup(chartId);

        // Store initialization data for the grid "added" event handler to pick up
        if (!window.pendingColumnChartInits) {
          window.pendingColumnChartInits = new Map();
        }

        window.pendingColumnChartInits.set(chartId, {
          chartId,
          timestamp: Date.now()
        });

        console.log('📊 Drag-drop: Stored initialization data for', chartId);

        return markup;
      },
    },
  ];

  // Setup GridStack drag-in
  if (typeof GridStack !== 'undefined' && GridStack.setupDragIn) {
    GridStack.setupDragIn(
      '.widget-item[data-widget-type="column-chart"]',
      undefined,
      columnChartSidebarContent
    );
    console.log('✅ Column chart drag-in setup complete');
  } else {
    console.warn('⚠️ GridStack not available, skipping column chart drag-in setup');
  }
}

// Cleanup old pending initializations (prevent memory leaks)
function cleanupOldPendingColumnChartInits() {
  if (window.pendingColumnChartInits) {
    const now = Date.now();
    const maxAge = 30000; // 30 seconds

    for (const [chartId, initData] of window.pendingColumnChartInits.entries()) {
      if (now - initData.timestamp > maxAge) {
        console.warn('📊 Cleaning up old pending initialization for', chartId);
        window.pendingColumnChartInits.delete(chartId);
      }
    }
  }
}

// Run cleanup periodically
setInterval(cleanupOldPendingColumnChartInits, 60000); // Every minute

// Auto-setup drag-drop when this script loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', setupColumnChartDragDrop);
} else {
  // If DOM is already loaded, setup immediately
  setTimeout(setupColumnChartDragDrop, 100);
}

// Debug function to test if the widget is working
function testColumnChartWidget() {
  console.log("🧪 Testing Column Chart Widget...");
  console.log("✅ Function available:", typeof addColumnChartWidget);
  console.log("✅ Grid available:", typeof window.grid);
  console.log("✅ amCharts available:", typeof am5);

  if (typeof addColumnChartWidget === 'function') {
    console.log("🎯 Attempting to add widget...");
    addColumnChartWidget();
  } else {
    console.error("❌ addColumnChartWidget function not found!");
  }
}

// Debug function to test chart initialization directly
function testColumnChartInit() {
  console.log("🧪 Testing Column Chart Initialization...");

  // Create a test container
  const testContainer = document.createElement('div');
  testContainer.id = 'test-column-chart-' + Date.now();
  testContainer.style.width = '500px';
  testContainer.style.height = '400px';
  document.body.appendChild(testContainer);

  console.log("📊 Created test container:", testContainer.id);

  // Try to initialize the chart
  initColumnChart(testContainer.id).then(() => {
    console.log("✅ Chart initialization successful!");
  }).catch((error) => {
    console.error("❌ Chart initialization failed:", error);
  });
}

// --- Global Exports ---
window.addColumnChartWidget = addColumnChartWidget;
window.getColumnChartWidgetMarkup = getColumnChartWidgetMarkup; // Export markup function
window.setupColumnChartDragDrop = setupColumnChartDragDrop; // Export drag-drop setup
window.initColumnChart = initColumnChart; // Export chart initialization
window.initColumnChartSettings = initColumnChartSettings; // Export settings function
window.testColumnChartWidget = testColumnChartWidget; // Export test function
window.testColumnChartInit = testColumnChartInit; // Export direct init test function

// Log that the script has loaded
console.log("📊 Column Chart Widget script loaded successfully");
console.log("🔧 Available functions:", {
  addColumnChartWidget: typeof window.addColumnChartWidget,
  getColumnChartWidgetMarkup: typeof window.getColumnChartWidgetMarkup,
  setupColumnChartDragDrop: typeof window.setupColumnChartDragDrop,
  initColumnChart: typeof window.initColumnChart
});
