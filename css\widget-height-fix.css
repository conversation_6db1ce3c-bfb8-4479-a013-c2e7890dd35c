/* Widget Height Auto-Adjustment Fixes */

/* Make grid stack items auto-height based on content */
.grid-stack > .grid-stack-item > .grid-stack-item-content {
  overflow: auto !important;
  height: auto !important;
  padding: 0rem !important;
  inset: 0.5rem !important;
}

/* Always show resize handles */
.grid-stack > .grid-stack-item > .ui-resizable-handle {
  opacity: 1 !important;
  visibility: visible !important;
  background-color: rgba(0, 177, 156, 0.15) !important;
  border: 1px solid rgba(0, 177, 156, 0.25) !important;
  z-index: 10 !important;
}

/* Style individual resize handles */
.grid-stack > .grid-stack-item > .ui-resizable-se,
.grid-stack > .grid-stack-item > .ui-resizable-sw,
.grid-stack > .grid-stack-item > .ui-resizable-ne,
.grid-stack > .grid-stack-item > .ui-resizable-nw {
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
}

.grid-stack > .grid-stack-item > .ui-resizable-e,
.grid-stack > .grid-stack-item > .ui-resizable-w {
  width: 8px !important;
  height: 40px !important;
  border-radius: 4px !important;
}

.grid-stack > .grid-stack-item > .ui-resizable-s,
.grid-stack > .grid-stack-item > .ui-resizable-n {
  height: 8px !important;
  width: 40px !important;
  border-radius: 4px !important;
}

/* Hover effect for resize handles */
.grid-stack > .grid-stack-item > .ui-resizable-handle:hover {
  background-color: rgba(0, 177, 156, 0.3) !important;
  border-color: rgba(0, 177, 156, 0.5) !important;
  box-shadow: 0 0 0 1px rgba(0, 177, 156, 0.1) !important;
}

/* Prevent flickering during drag operations */
.grid-stack-item.no-transition,
.grid-stack-item.no-transition * {
  transition: none !important;
}

.grid-stack-item.ui-draggable-dragging {
  transition: none !important;
  will-change: transform;
}

.grid-stack-item.ui-draggable-dragging > .grid-stack-item-content {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Fix chart containers to not have fixed heights */
.chart-container {
  height: auto !important;
  min-height: auto !important;
}

/* Fix pie chart widget to adjust height based on content */
.pie-chart-widget {
  height: auto !important;
}

/* Fix spreadsheet container to adjust height based on content */
.spreadsheet-container {
  height: auto !important;
}

/* Fix widget body to enable scrolling when needed */
.widget-body {
  overflow: auto !important;
}

/* Ensure grid stack items can grow based on content */
.grid-stack-item {
  min-height: 0 !important;
}

/* Remove fixed height calculations */
.chart-container,
.spreadsheet-container {
  height: auto !important;
}

/* Ensure grid stack properly sizes items */
.grid-stack > .grid-stack-item {
  min-height: 0 !important;
}

/* Fix for nested grids */
.grid-stack .grid-stack {
  height: auto !important;
  min-height: 0 !important;
}

/* Fix for section containers */
.section-container-widget .grid-stack {
  height: 100% !important;
  min-height: 200px !important;
  position: relative;
  transform: translateZ(0);
}

/* Ensure widget content can scroll when needed */
.grid-stack-item-content {
  overflow: auto !important;
  transform: translateZ(0);
  height: auto !important;
}

/* Fix for widget headers */
.widget-header {
  flex-shrink: 0;
}
