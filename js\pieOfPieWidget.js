let pieOfPieChartCounter = 0;

// Add a Pie of Pie Chart widget using amCharts v5
function addPieOfPieWidget(initialConfig = {}) {
  const widgetId = `pie-of-pie-${pieOfPieChartCounter++}`;
  const chartContainerId = `pie-of-pie-container-${widgetId}`;
  const settingsId = `pieOfPieSettings-${widgetId}`;

  // Default data structure for pie of pie
  const defaultData = {
    mainPie: [
      {
        category: "Category A",
        value: 35,
        subData: [
          { category: "Sub A1", value: 15 },
          { category: "Sub A2", value: 10 },
          { category: "Sub A3", value: 10 },
        ],
      },
      {
        category: "Category B",
        value: 25,
        subData: [
          { category: "Sub B1", value: 12 },
          { category: "Sub B2", value: 13 },
        ],
      },
      {
        category: "Category C",
        value: 20,
        subData: [
          { category: "Sub C1", value: 8 },
          { category: "Sub C2", value: 6 },
          { category: "Sub C3", value: 6 },
        ],
      },
      {
        category: "Category D",
        value: 20,
        subData: [
          { category: "Sub D1", value: 10 },
          { category: "Sub D2", value: 10 },
        ],
      },
    ],
  };

  const config = {
    title: initialConfig.title || "Pie of Pie Chart",
    data: initialConfig.data || defaultData,
    notes:
      initialConfig.notes || "Default Notes: Please provide specific notes.",
    source:
      initialConfig.source ||
      "Default Source: Please provide a specific source.",
    lastUpdate: initialConfig.lastUpdate || "N/A",
    nextUpdate: initialConfig.nextUpdate || "N/A",
  };

  const grid = window.grid;
  const newWidget = grid.addWidget({
    w: initialConfig.w || 12,
    h: initialConfig.h || 12,
    content: `
      <div class="pie-of-pie-widget widget p-2" id="${widgetId}" style="height: 100%; display: flex; flex-direction: column;">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div class="widget-title editable-title" data-editable="true" title="Click to edit title">
              
              <span>${config.title}</span>
          </div>
          <div class="widget-actions">
            <button class="btn btn-link" data-bs-toggle="offcanvas" data-bs-target="#${settingsId}" aria-controls="${settingsId}">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-link ms-1" onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex;">
          <div id="${chartContainerId}" class="chart-container" style="width: 100%; height: 100%; position: relative; min-height: 300px;"></div>
        </div>
        <div class="widget-footer mt-2" style="padding: 0.5rem 0; border-top: 1px solid #e5e9f0; font-size: 10px; color: #6c757d; text-align: left;">
          ${
            config.notes
              ? `<div><i class="las la-clipboard"></i> Notes : ${config.notes}</div>`
              : ""
          }
          ${
            config.source
              ? `<div><i class="las la-database"></i> Source : ${config.source}</div>`
              : ""
          }
          ${
            config.lastUpdate || config.nextUpdate
              ? `
          <div class="d-flex mt-1">
            ${
              config.lastUpdate
                ? `<span><i class="las la-calendar-alt"></i> Last update : ${config.lastUpdate}</span>`
                : "<span></span>"
            }
            ${
              config.nextUpdate
                ? `<span class="ms-3"><i class="las la-calendar-plus"></i> Next update : ${config.nextUpdate}</span>`
                : ""
            }
          </div>`
              : ""
          }
        </div>
      </div>
    `,
  });

  // Store config on the widget element
  const widgetElement = document.getElementById(widgetId);
  widgetElement.widgetConfig = config;
  widgetElement.chartContainerId = chartContainerId;

  // Initialize the chart after a short delay
  setTimeout(() => {
    if (typeof am5 !== "undefined" && typeof am5percent !== "undefined") {
      initializePieOfPieChart(chartContainerId, config);
    } else {
      console.error("amCharts core or percent libraries not loaded properly");
    }
  }, 100);

  // Make title editable
  if (typeof makeTitleEditable === "function") {
    makeTitleEditable(widgetId);
  }

  // Create settings panel
  createPieOfPieSettingsOffcanvas(
    settingsId,
    widgetId,
    chartContainerId,
    config
  );
}

function initializePieOfPieChart(chartContainerId, config) {
  const chartContainerElement = document.getElementById(chartContainerId);
  if (!chartContainerElement) return;

  // Dispose existing chart if any
  if (chartContainerElement.amRoot) {
    chartContainerElement.amRoot.dispose();
  }

  // Create root element
  const root = am5.Root.new(chartContainerId);
  chartContainerElement.amRoot = root;

  // Set themes
  root.setThemes([am5themes_Animated.new(root)]);

  // Create chart container
  const container = root.container.children.push(
    am5.Container.new(root, {
      width: am5.percent(100),
      height: am5.percent(100),
      layout: root.horizontalLayout,
    })
  );

  // Create main chart
  const mainChart = container.children.push(
    am5percent.PieChart.new(root, {
      tooltip: am5.Tooltip.new(root, {}),
    })
  );

  // Create main series
  const mainSeries = mainChart.series.push(
    am5percent.PieSeries.new(root, {
      valueField: "value",
      categoryField: "category",
      alignLabels: false,
    })
  );

  // Get colors from brand colors or defaults
  let colors;
  if (window.chartConfig && window.chartConfig.brandColors) {
    colors = am5.ColorSet.new(root, {
      colors: window.chartConfig.brandColors.map((hex) => am5.color(hex)),
      reuse: true,
    });
  } else {
    colors = am5.ColorSet.new(root, {
      colors: [
        am5.color("#00c8b3"), // Teal
        am5.color("#60da94"), // Light green
        am5.color("#007f71"), // Dark teal
        am5.color("#a8e6cf"), // Pale teal
      ],
      reuse: true,
    });
  }

  // Apply colors to main series
  mainSeries.set("colors", colors);

  mainSeries.labels.template.setAll({
    textType: "circular",
    radius: 4,
  });
  mainSeries.ticks.template.set("visible", false);
  mainSeries.slices.template.set("toggleKey", "none");

  // Create sub chart
  const subChart = container.children.push(
    am5percent.PieChart.new(root, {
      radius: am5.percent(50),
      tooltip: am5.Tooltip.new(root, {}),
    })
  );

  // Create sub series
  const subSeries = subChart.series.push(
    am5percent.PieSeries.new(root, {
      valueField: "value",
      categoryField: "category",
    })
  );

  // Apply same colors to sub series
  subSeries.set("colors", colors);

  // Initialize empty sub data
  subSeries.data.setAll([
    { category: "A", value: 0 },
    { category: "B", value: 0 },
    { category: "C", value: 0 },
    { category: "D", value: 0 },
    { category: "E", value: 0 },
    { category: "F", value: 0 },
    { category: "G", value: 0 },
  ]);
  subSeries.slices.template.set("toggleKey", "none");

  let selectedSlice;

  // Create connecting lines
  const line0 = container.children.push(
    am5.Line.new(root, {
      position: "absolute",
      stroke: root.interfaceColors.get("text"),
      strokeDasharray: [2, 2],
    })
  );
  const line1 = container.children.push(
    am5.Line.new(root, {
      position: "absolute",
      stroke: root.interfaceColors.get("text"),
      strokeDasharray: [2, 2],
    })
  );

  // Update connecting lines
  function updateLines() {
    if (selectedSlice) {
      const startAngle = selectedSlice.get("startAngle");
      const arc = selectedSlice.get("arc");
      const radius = selectedSlice.get("radius");

      const x00 = radius * am5.math.cos(startAngle);
      const y00 = radius * am5.math.sin(startAngle);

      const x10 = radius * am5.math.cos(startAngle + arc);
      const y10 = radius * am5.math.sin(startAngle + arc);

      const subRadius = subSeries.slices.getIndex(0).get("radius");
      const x01 = 0;
      const y01 = -subRadius;

      const x11 = 0;
      const y11 = subRadius;

      const point00 = mainSeries.toGlobal({ x: x00, y: y00 });
      const point10 = mainSeries.toGlobal({ x: x10, y: y10 });

      const point01 = subSeries.toGlobal({ x: x01, y: y01 });
      const point11 = subSeries.toGlobal({ x: x11, y: y11 });

      line0.set("points", [point00, point01]);
      line1.set("points", [point10, point11]);
    }
  }

  // Add events for line updates
  mainSeries.on("startAngle", function () {
    updateLines();
  });

  container.events.on("boundschanged", function () {
    root.events.once("frameended", function () {
      updateLines();
    });
  });

  // Handle slice selection
  function selectSlice(slice) {
    selectedSlice = slice;
    const dataItem = slice.dataItem;
    const dataContext = dataItem.dataContext;

    if (dataContext) {
      let i = 0;
      subSeries.data.each(function (dataObject) {
        const dataObj = dataContext.subData[i];
        if (dataObj) {
          if (!subSeries.dataItems[i].get("visible")) {
            subSeries.dataItems[i].show();
          }
          subSeries.data.setIndex(i, dataObj);
        } else {
          subSeries.dataItems[i].hide();
        }
        i++;
      });
    }

    const middleAngle = slice.get("startAngle") + slice.get("arc") / 2;
    const firstAngle = mainSeries.dataItems[0].get("slice").get("startAngle");

    mainSeries.animate({
      key: "startAngle",
      to: firstAngle - middleAngle,
      duration: 1000,
      easing: am5.ease.out(am5.ease.cubic),
    });
    mainSeries.animate({
      key: "endAngle",
      to: firstAngle - middleAngle + 360,
      duration: 1000,
      easing: am5.ease.out(am5.ease.cubic),
    });
  }

  // Add click events
  mainSeries.slices.template.events.on("click", function (e) {
    selectSlice(e.target);
  });

  // Set data
  mainSeries.data.setAll(config.data.mainPie);

  // Initial selection
  mainSeries.events.on("datavalidated", function () {
    selectSlice(mainSeries.slices.getIndex(0));
  });

  // Add resize observer
  const resizeObserver = new ResizeObserver(() => {
    root.resize();
  });

  resizeObserver.observe(chartContainerElement);
  chartContainerElement.resizeObserver = resizeObserver;

  // Initial sizing and animation
  root.resize();
  container.appear(1000, 10);
}

// Settings panel creation (similar to nestedDonutChartWidget)
function createPieOfPieSettingsOffcanvas(
  settingsId,
  widgetId,
  chartContainerId,
  currentConfig
) {
  const offcanvasContainer =
    document.getElementById("offcanvasContainer") || document.body;
  const dataJson = JSON.stringify(currentConfig.data, null, 2);

  const offcanvasHtml = `
    <div class="offcanvas offcanvas-end" tabindex="-1" id="${settingsId}" aria-labelledby="${settingsId}Label">
      <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="${settingsId}Label">Pie of Pie Chart Settings</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div class="offcanvas-body">
        <div class="mb-3">
          <label class="form-label">Chart Title</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-chartTitle" value="${currentConfig.title}">
        </div>

        <div class="mb-3">
          <label class="form-label">Data (JSON format)</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-data" rows="10" spellcheck="false">${dataJson}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Notes</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-notes" rows="2">${currentConfig.notes}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Source</label>
          <textarea class="form-control form-control-sm" id="${settingsId}-source" rows="2">${currentConfig.source}</textarea>
        </div>

        <div class="mb-3">
          <label class="form-label">Last Update</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-lastUpdate" value="${currentConfig.lastUpdate}">
        </div>

        <div class="mb-3">
          <label class="form-label">Next Update</label>
          <input type="text" class="form-control form-control-sm" id="${settingsId}-nextUpdate" value="${currentConfig.nextUpdate}">
        </div>

        <button class="btn btn-primary w-100" onclick="applyPieOfPieSettings('${widgetId}', '${settingsId}', '${chartContainerId}')">Apply Changes</button>
      </div>
    </div>
  `;

  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = offcanvasHtml;
  offcanvasContainer.appendChild(tempDiv.firstElementChild);
}

function applyPieOfPieSettings(widgetId, settingsId, chartContainerId) {
  const widgetElement = document.getElementById(widgetId);
  if (!widgetElement || !widgetElement.widgetConfig) return;

  const currentConfig = widgetElement.widgetConfig;

  try {
    currentConfig.title = document.getElementById(
      `${settingsId}-chartTitle`
    ).value;
    const dataInput = document.getElementById(`${settingsId}-data`).value;
    currentConfig.data = JSON.parse(dataInput);
    currentConfig.notes = document.getElementById(`${settingsId}-notes`).value;
    currentConfig.source = document.getElementById(
      `${settingsId}-source`
    ).value;
    currentConfig.lastUpdate = document.getElementById(
      `${settingsId}-lastUpdate`
    ).value;
    currentConfig.nextUpdate = document.getElementById(
      `${settingsId}-nextUpdate`
    ).value;

    // Update widget title
    const titleSpan = widgetElement.querySelector(".widget-title span");
    if (titleSpan) {
      titleSpan.textContent = currentConfig.title;
    }

    // Update footer
    if (typeof updateWidgetFooter === "function") {
      updateWidgetFooter(widgetElement, currentConfig);
    }

    // Re-initialize chart
    initializePieOfPieChart(chartContainerId, currentConfig);

    // Close settings panel
    const offcanvasElement = document.getElementById(settingsId);
    if (offcanvasElement) {
      const offcanvasInstance =
        bootstrap.Offcanvas.getInstance(offcanvasElement);
      if (offcanvasInstance) {
        offcanvasInstance.hide();
      }
    }
  } catch (e) {
    console.error("Error applying settings:", e);
    alert("Error: Invalid JSON format in Data. Please correct and try again.");
  }
}

// Export functions
window.addPieOfPieWidget = addPieOfPieWidget;
window.applyPieOfPieSettings = applyPieOfPieSettings;
