/* Import brand colors from dashboard-style.css */
:root {
  --ocean-teal: #00b19c;
  --emerald-green: #3bcd3f;
  --forest-green: #007365;
  --slate-grey: #8dbac4;
  --denali-blue: #02104f;
  --wns-black: #231f20;
  --ocean-teal-light: #cce9e6;
  --primary-green: #4caf50;
  --primary-green-dark: #388e3c;
  --primary-green-light: #a5d6a7;
  --primary-green-lighter: rgba(76, 175, 80, 0.1);
  --text-dark: #263238;
  --text-light: #546e7a;
  --error-red: #ef5350;
  --background-grey: #f5f5f5;
  --border-color: #e0e0e0;
  --white: #ffffff;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Montserrat", "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial,
    sans-serif;
  background: var(--white);
  min-height: 100vh;
  margin: 0;
  color: var(--text-dark);
}

.page-container {
  display: flex;
  min-height: 100vh;
}

/* Brand Section (Left Side) */
.brand-section {
  flex: 1.2;
  background: linear-gradient(
    135deg,
    var(--ocean-teal) 0%,
    var(--forest-green) 100%
  );
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px;
  overflow: hidden;
}

.brand-content {
  position: relative;
  z-index: 2;
  text-align: left;
  max-width: 580px;
  margin-left: 40px;
}

.brand-text {
  position: relative;
  z-index: 3;
  transform: translateY(-40px);
}

.brand-title {
  font-size: 86px;
  line-height: 1;
  color: var(--white);
  margin-bottom: 32px;
  display: flex;
  flex-direction: column;
}

.brand-title .light {
  font-weight: 200;
  letter-spacing: -2px;
  transform: translateX(-6px);
  opacity: 0.95;
}

.brand-title .bold {
  font-weight: 700;
  letter-spacing: -3px;
  transform: translateY(-8px);
}

.brand-subtitle {
  font-size: 32px;
  color: var(--white);
  opacity: 0.9;
  font-weight: 300;
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-left: 4px;
}

.brand-subtitle .line {
  position: relative;
  display: inline-block;
}

.brand-subtitle .line:first-child {
  opacity: 0.85;
  font-weight: 300;
  letter-spacing: -0.5px;
}

.brand-subtitle .line:last-child {
  font-weight: 500;
  color: var(--ocean-teal-light);
  letter-spacing: -0.5px;
  transform: translateY(-4px);
}

/* Animated Shapes */
.brand-shapes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.shape {
  position: absolute;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15),
    rgba(255, 255, 255, 0.05)
  );
  border-radius: 50%;
  backdrop-filter: blur(8px);
}

.shape-1 {
  width: 400px;
  height: 400px;
  top: -150px;
  right: -100px;
  animation: float 12s ease-in-out infinite;
}

.shape-2 {
  width: 300px;
  height: 300px;
  bottom: 50px;
  left: -80px;
  animation: float 15s ease-in-out infinite;
  animation-delay: -3s;
}

.shape-3 {
  width: 200px;
  height: 200px;
  bottom: -50px;
  right: 80px;
  animation: float 18s ease-in-out infinite;
  animation-delay: -6s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) scale(1) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) scale(1.05) rotate(5deg);
  }
}

.geometric-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    120deg,
    rgba(255, 255, 255, 0.12) 0%,
    transparent 100%
  );
  clip-path: polygon(0 0, 100% 0, 90% 100%, 0 90%);
}

/* Additional decorative element */
.brand-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
}

/* Login Section (Right Side) */
.login-section {
  width: 580px;
  background: var(--white);
  padding: 48px;
  display: flex;
  flex-direction: column;
}

/* Header Logo */
.header-logo {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  margin-bottom: 64px;
}

.wns-logo {
  height: 42px;
  width: auto;
  object-fit: contain;
  object-position: center;
}

.powered-by {
  font-size: 13px;
  color: var(--text-light);
  font-weight: 500;
}

/* Login Container */
.login-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.login-content {
  max-width: 420px;
  margin: 0 auto;
  width: 100%;
}

.login-content h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 32px;
}

/* Form Styles */
.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  font-size: 14px;
  color: var(--text-dark);
  margin-bottom: 8px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  font-size: 14px;
  border: 1px solid var(--border-color);
  border-radius: 0;
  transition: all 0.2s ease;
  font-family: "Montserrat", sans-serif;
}

.form-control:focus {
  outline: none;
  border-color: var(--ocean-teal);
  box-shadow: 0 0 0 2px var(--ocean-teal-light);
}

.error-message {
  color: var(--error-red);
  font-size: 12px;
  margin-top: 4px;
  display: none;
}

.form-control.error {
  border-color: var(--error-red);
}

.form-control.error + .error-message {
  display: block;
}

/* Buttons */
.continue-btn {
  width: 100%;
  padding: 12px;
  background: var(--ocean-teal);
  color: var(--white);
  border: none;
  border-radius: 0;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: "Montserrat", sans-serif;
}

.continue-btn:hover {
  background: var(--forest-green);
  transform: translateY(-1px);
}

.continue-btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px var(--ocean-teal-light);
}

.login-divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 24px 0;
}

.login-divider::before,
.login-divider::after {
  content: "";
  flex: 1;
  border-bottom: 1px solid var(--border-color);
}

.login-divider span {
  padding: 0 16px;
  color: var(--text-light);
  font-size: 14px;
}

.instasafe-btn {
  width: 100%;
  padding: 12px;
  background: var(--white);
  color: var(--text-dark);
  border: 1px solid var(--border-color);
  border-radius: 0;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
  font-family: "Montserrat", sans-serif;
}

.instasafe-btn:hover {
  background: var(--background-grey);
  border-color: var(--ocean-teal);
}

.instasafe-btn:focus {
  outline: none;
  border-color: var(--ocean-teal);
  box-shadow: 0 0 0 2px var(--ocean-teal-light);
}

.instasafe-btn i {
  font-size: 18px;
}

/* Footer Styles */
.login-footer {
  margin-top: 32px;
  text-align: center;
}

.signup-text {
  font-size: 14px;
  color: var(--text-light);
  margin-bottom: 16px;
}

.signup-text a {
  color: var(--ocean-teal);
  text-decoration: none;
  font-weight: 600;
}

.terms-text {
  font-size: 12px;
  color: var(--text-light);
  line-height: 1.5;
}

.terms-text a {
  color: var(--ocean-teal);
  text-decoration: none;
}

/* Page Footer */
.page-footer {
  margin-top: auto;
  text-align: center;
}

.footer-links {
  margin-bottom: 8px;
}

.footer-links a {
  color: var(--text-light);
  text-decoration: none;
  font-size: 13px;
}

.separator {
  margin: 0 8px;
  color: var(--text-light);
}

.browser-support {
  color: var(--text-light);
  font-size: 12px;
  margin: 0;
}

/* Mobile Optimizations */
@media (max-width: 1400px) {
  .brand-section {
    padding: 40px;
  }

  .brand-content {
    margin-left: 20px;
  }

  .brand-title {
    font-size: 72px;
  }

  .brand-subtitle {
    font-size: 28px;
  }
}

@media (max-width: 1200px) {
  .brand-section {
    display: none;
  }

  .login-section {
    width: 100%;
    max-width: 680px;
    margin: 0 auto;
  }

  .login-content {
    max-width: 480px;
  }
}

@media (max-width: 680px) {
  .login-section {
    padding: 32px;
  }

  .login-content {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .login-section {
    padding: 24px;
  }

  .header-logo {
    margin-bottom: 40px;
  }

  .login-content h2 {
    font-size: 20px;
    margin-bottom: 24px;
  }

  .form-control,
  .continue-btn,
  .instasafe-btn {
    padding: 10px;
  }

  .wns-logo {
    height: 28px;
  }

  .powered-by {
    font-size: 12px;
  }
}
