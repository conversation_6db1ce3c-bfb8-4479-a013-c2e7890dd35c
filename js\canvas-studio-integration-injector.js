// Canvas Studio Integration Injector for index3.html
// This script dynamically adds the Canvas Studio Integration without modifying the original file

(function () {
  "use strict";

  // Wait for DOM to be ready
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initIntegration);
  } else {
    initIntegration();
  }

  function initIntegration() {
    // Add CSS
    addIntegrationCSS();

    // Add the Canvas Studio button to header
    addCanvasStudioButton();

    // Load the main integration script
    loadIntegrationScript();
  }

  function addIntegrationCSS() {
    if (document.getElementById("canvas-studio-integration-css")) return;

    const link = document.createElement("link");
    link.id = "canvas-studio-integration-css";
    link.rel = "stylesheet";
    link.href = "css/canvas-studio-integration-index3.css";
    document.head.appendChild(link);
  }

  function addCanvasStudioButton() {
    // Find the header button group
    const headerBtnGroup = document.querySelector(".header-btn-group");
    if (!headerBtnGroup) {
      console.warn(
        "Header button group not found, trying alternative selectors"
      );
      return;
    }

    // Check if button already exists
    if (document.getElementById("canvas-studio-integration-btn")) {
      return;
    }

    // Create the Canvas Studio button
    const canvasStudioBtn = document.createElement("button");
    canvasStudioBtn.className = "header-btn secondary";
    canvasStudioBtn.id = "canvas-studio-integration-btn";
    canvasStudioBtn.title = "Configure Canvas Studio Tabs";
    canvasStudioBtn.innerHTML = `
            <i class="las la-layer-group"></i>
            <span>Canvas Studio</span>
        `;

    // Add click handler (will be replaced when main script loads)
    canvasStudioBtn.onclick = function () {
      if (window.canvasStudioIntegration) {
        window.canvasStudioIntegration.showTabConfigurationModal(
          "default-workspace"
        );
      } else {
        console.log("Canvas Studio Integration loading...");
      }
    };

    // Insert as first button
    headerBtnGroup.insertBefore(canvasStudioBtn, headerBtnGroup.firstChild);

    console.log("Canvas Studio button added to header");
  }

  function loadIntegrationScript() {
    if (document.getElementById("canvas-studio-integration-script")) return;

    const script = document.createElement("script");
    script.id = "canvas-studio-integration-script";
    script.src = "js/canvas-studio-integration-index3.js";
    script.onload = function () {
      console.log("Canvas Studio Integration loaded successfully");
    };
    script.onerror = function () {
      console.error("Failed to load Canvas Studio Integration script");
    };

    document.head.appendChild(script);
  }
})();
