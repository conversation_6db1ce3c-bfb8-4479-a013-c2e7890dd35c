/**
 * ===================================================
 * WORKSPACE CARDS - Modern Tab Container Alternative
 * ===================================================
 * A Pinterest-style card interface with drag & drop,
 * widget assignment, and modern UX interactions
 * ===================================================
 */

class WorkspaceCards {
  constructor() {
    this.workspaces = new Map();
    this.activeWorkspace = null;
    this.draggedCard = null;
    this.widgetPanel = null;
    this.currentEditingCard = null;
    this.initialized = false;
    this.canvasStudioIntegration = null;

    this.init();
  }

  init(containerSelector = null) {
    if (this.initialized) {
      console.warn(
        "⚠️ WARNING: WorkspaceCards already initialized. Skipping duplicate initialization."
      );
      return;
    }

    if (containerSelector) {
      // Use the provided container
      this.container = document.querySelector(containerSelector);
      if (this.container) {
        this.container.innerHTML = this.getWorkspaceHTML();
        console.log(
          `✅ SUCCESS: Using provided container: ${containerSelector}`
        );
      } else {
        console.error(`❌ ERROR: Container not found: ${containerSelector}`);
        console.log(
          "Available containers:",
          document.querySelectorAll('[id*="workspace"], [class*="workspace"]')
        );
        return; // Don't proceed if the specified container doesn't exist
      }
    } else {
      // Create our own container (fallback)
      this.createWorkspaceContainer();
    }

    this.setupEventListeners();
    this.loadSampleWorkspaces();
    this.initialized = true;
  }

  createWorkspaceContainer() {
    const container = document.createElement("div");
    container.className = "workspace-cards-container";
    container.innerHTML = this.getWorkspaceHTML();

    // Replace the existing smart-tab-container
    const existingContainer = document.querySelector(".smart-tab-container");
    if (existingContainer && existingContainer.parentNode) {
      existingContainer.parentNode.replaceChild(container, existingContainer);
    } else {
      document.body.appendChild(container);
    }

    this.container = container;
  }

  getWorkspaceHTML() {
    return `
      <div class="workspace-header">
        <div class="workspace-header-left">
          <h2>Workspaces</h2>
          <p>Manage your workspace configurations</p>
        </div>
        <div class="workspace-header-actions">
          <button class="workspace-btn" onclick="window.workspaceCards.showSearch()">
            <i class="las la-search"></i>
            Search
          </button>
          <button class="workspace-btn primary" onclick="window.workspaceCards.createNewWorkspace()">
            <i class="las la-plus"></i>
            New Workspace
          </button>
        </div>
      </div>

      <div class="workspace-filters">
        <button class="filter-btn active" onclick="window.workspaceCards.handleFilter('all', this)">All</button>
        <button class="filter-btn" onclick="window.workspaceCards.handleFilter('draft', this)">Drafts</button>
        <button class="filter-btn" onclick="window.workspaceCards.handleFilter('active', this)">Active</button>
        <button class="filter-btn" onclick="window.workspaceCards.handleFilter('archived', this)">Archived</button>
      </div>

      <div class="workspace-search" style="display: none;">
        <input type="text" placeholder="Search workspaces..." id="workspace-search-input">
      </div>

      <div class="workspace-grid" id="workspace-grid">
        <!-- Workspace cards will be inserted here -->
      </div>

      <!-- Modal Overlay -->
      <div class="workspace-overlay" id="workspace-overlay">
        <div class="workspace-modal">
          <div class="modal-header">
            <h3 id="modal-title">Modal Title</h3>
            <button class="modal-close-btn" onclick="window.workspaceCards.closeModal()">
              <i class="las la-times"></i>
            </button>
          </div>
          <div class="modal-content" id="modal-content">
            <!-- Modal content will be inserted here -->
          </div>
        </div>
      </div>

      <!-- Widget Panel -->
      <div class="workspace-widget-panel" id="workspace-widget-panel">
        <div class="widget-panel-header">
          <h3>Add Widgets</h3>
          <button class="panel-close-btn" onclick="window.workspaceCards.closeWidgetPanel()">
            <i class="las la-times"></i>
          </button>
        </div>
        <div class="widget-panel-content" id="widget-panel-content">
          <!-- Widget categories will be loaded here -->
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById("workspace-search-input");
    if (searchInput) {
      searchInput.addEventListener("input", (e) =>
        this.handleSearch(e.target.value)
      );
    }

    // Filter buttons
    document.addEventListener("click", (e) => {
      if (e.target.classList.contains("filter-btn")) {
        this.handleFilter(e.target.dataset.filter, e.target);
      }
    });

    // Close modal on overlay click
    document.addEventListener("click", (e) => {
      if (e.target.id === "workspace-overlay") {
        this.closeModal();
      }
    });

    // Close widget panel on outside click
    document.addEventListener("click", (e) => {
      const panel = document.getElementById("workspace-widget-panel");
      if (
        panel &&
        !panel.contains(e.target) &&
        !e.target.closest(".card-action-btn")
      ) {
        this.closeWidgetPanel();
      }
    });

    // Initialize Canvas Studio integration
    if (typeof CanvasStudioIntegration !== "undefined") {
      this.canvasStudioIntegration = new CanvasStudioIntegration(this);
    }
  }

  loadSampleWorkspaces() {
    const sampleWorkspaces = [
      {
        id: "ws-1",
        title: "Analytics Dashboard",
        description: "Main analytics and reporting workspace",
        widgets: [
          { type: "chart", title: "Revenue Chart" },
          { type: "table", title: "Sales Data" },
          { type: "kpi", title: "Key Metrics" },
        ],
        status: "active",
        lastModified: new Date(),
        tags: ["analytics", "dashboard", "reports"],
        color: "#3b82f6",
        tabCount: 3,
        tabConfiguration: {
          tabs: [
            { id: "tab-1", name: "Overview", widgets: ["kpi"] },
            { id: "tab-2", name: "Charts", widgets: ["chart"] },
            { id: "tab-3", name: "Data", widgets: ["table"] },
          ],
          settings: {
            allowReorder: true,
            showTabIcons: true,
            tabPosition: "top",
          },
        },
      },
      {
        id: "ws-2",
        title: "Marketing Insights",
        description: "Marketing campaign performance tracking",
        widgets: [
          { type: "chart", title: "Campaign Performance" },
          { type: "social", title: "Social Media Stats" },
        ],
        status: "active",
        lastModified: new Date(Date.now() - 86400000),
        tags: ["marketing", "campaigns", "social"],
        color: "#10b981",
        tabCount: 2,
        tabConfiguration: {
          tabs: [
            { id: "tab-1", name: "Campaigns", widgets: ["chart"] },
            { id: "tab-2", name: "Social", widgets: ["social"] },
          ],
          settings: {
            allowReorder: true,
            showTabIcons: false,
            tabPosition: "top",
          },
        },
      },
      {
        id: "ws-3",
        title: "Financial Overview",
        description: "Financial data and budget tracking",
        widgets: [
          { type: "chart", title: "Budget vs Actual" },
          { type: "table", title: "Expense Report" },
          { type: "kpi", title: "Financial KPIs" },
          { type: "calendar", title: "Payment Schedule" },
        ],
        status: "draft",
        lastModified: new Date(Date.now() - 172800000),
        tags: ["finance", "budget", "expenses"],
        color: "#8b5cf6",
        tabCount: 4,
        tabConfiguration: {
          tabs: [
            { id: "tab-1", name: "Budget", widgets: ["chart"] },
            { id: "tab-2", name: "Expenses", widgets: ["table"] },
            { id: "tab-3", name: "KPIs", widgets: ["kpi"] },
            { id: "tab-4", name: "Schedule", widgets: ["calendar"] },
          ],
          settings: {
            allowReorder: false,
            showTabIcons: true,
            tabPosition: "top",
          },
        },
      },
    ];

    sampleWorkspaces.forEach((workspace) => {
      this.workspaces.set(workspace.id, workspace);
    });

    this.renderWorkspaces();
  }

  renderWorkspaces() {
    const grid = document.getElementById("workspace-grid");
    if (!grid) return;

    grid.innerHTML = "";

    // Add existing workspace cards
    this.workspaces.forEach((workspace) => {
      grid.appendChild(this.createWorkspaceCard(workspace));
    });

    // Add "Add New" card
    grid.appendChild(this.createAddNewCard());
  }

  createWorkspaceCard(workspace) {
    const card = document.createElement("div");
    card.className = "workspace-card";
    card.draggable = true;
    card.dataset.workspaceId = workspace.id;

    const statusClass =
      workspace.status === "active"
        ? ""
        : workspace.status === "draft"
        ? "warning"
        : "error";

    card.innerHTML = `
      <div class="workspace-card-header">
        <div class="workspace-card-title">
          <h3>
            <div class="workspace-card-icon">
              <i class="las la-${this.getWorkspaceIcon(workspace.tags[0])}"></i>
            </div>
            ${workspace.title}
          </h3>
          <div class="workspace-card-actions">
            <button class="card-action-btn" onclick="window.workspaceCards.configureWorkspace('${
              workspace.id
            }')" title="Configure Tabs">
              <i class="las la-cog"></i>
            </button>
            <button class="card-action-btn" onclick="window.workspaceCards.editWorkspace('${
              workspace.id
            }')" title="Edit">
              <i class="las la-edit"></i>
            </button>
            <button class="card-action-btn" onclick="window.workspaceCards.openWidgetPanel('${
              workspace.id
            }')" title="Add Widgets">
              <i class="las la-plus"></i>
            </button>
            <button class="card-action-btn" onclick="window.workspaceCards.duplicateWorkspace('${
              workspace.id
            }')" title="Duplicate">
              <i class="las la-copy"></i>
            </button>
            <button class="card-action-btn" onclick="window.workspaceCards.deleteWorkspace('${
              workspace.id
            }')" title="Delete">
              <i class="las la-trash"></i>
            </button>
          </div>
        </div>
        <div class="workspace-card-meta">
          <span class="widget-count-badge">${
            workspace.widgets.length
          } widgets</span>
          <span class="last-modified">
            <i class="las la-clock"></i>
            ${this.formatDate(workspace.lastModified)}
          </span>
        </div>
      </div>

      <div class="workspace-card-preview">
        ${
          workspace.widgets.length > 0
            ? this.createPreviewGrid(workspace.widgets)
            : this.createEmptyPreview()
        }
      </div>

      <div class="workspace-card-footer">
        <div class="workspace-card-tags">
          ${workspace.tags
            .map((tag) => `<span class="workspace-tag">${tag}</span>`)
            .join("")}
        </div>
        <div class="workspace-card-status">
          <span class="status-indicator ${statusClass}"></span>
          ${workspace.status}
        </div>
      </div>
    `;

    // Add drag and drop event listeners
    this.setupCardDragAndDrop(card);

    return card;
  }

  createPreviewGrid(widgets) {
    if (widgets.length === 0) return this.createEmptyPreview();

    return `
      <div class="preview-grid">
        ${widgets
          .slice(0, 4)
          .map(
            (widget) => `
          <div class="preview-widget has-content">
            <i class="las la-${this.getWidgetIcon(widget.type)}"></i>
            <span>${widget.title}</span>
          </div>
        `
          )
          .join("")}
        ${
          widgets.length > 4
            ? `
          <div class="preview-widget">
            +${widgets.length - 3} more
          </div>
        `
            : ""
        }
      </div>
    `;
  }

  createEmptyPreview() {
    return `
      <div class="empty-preview">
        <i class="las la-plus-circle"></i>
        <h4>No widgets yet</h4>
        <p>Click the + button to add widgets</p>
      </div>
    `;
  }

  createAddNewCard() {
    const card = document.createElement("div");
    card.className = "add-workspace-card";
    card.onclick = () => this.createNewWorkspace();

    card.innerHTML = `
      <i class="las la-plus"></i>
      <h3>Create New Workspace</h3>
      <p>Start building your next dashboard</p>
    `;

    return card;
  }

  setupCardDragAndDrop(card) {
    card.addEventListener("dragstart", (e) => {
      this.draggedCard = card;
      card.classList.add("dragging");
      e.dataTransfer.effectAllowed = "move";
    });

    card.addEventListener("dragend", () => {
      card.classList.remove("dragging");
      this.draggedCard = null;
    });

    card.addEventListener("dragover", (e) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = "move";
    });

    card.addEventListener("drop", (e) => {
      e.preventDefault();
      if (this.draggedCard && this.draggedCard !== card) {
        this.reorderCards(this.draggedCard, card);
      }
    });
  }

  reorderCards(draggedCard, targetCard) {
    const grid = document.getElementById("workspace-grid");
    const draggedRect = draggedCard.getBoundingClientRect();
    const targetRect = targetCard.getBoundingClientRect();

    if (draggedRect.left < targetRect.left) {
      targetCard.parentNode.insertBefore(draggedCard, targetCard.nextSibling);
    } else {
      targetCard.parentNode.insertBefore(draggedCard, targetCard);
    }
  }

  // Workspace Management Methods
  createNewWorkspace() {
    const id = "ws-" + Date.now();
    const newWorkspace = {
      id,
      title: "New Workspace",
      description: "Describe your workspace",
      widgets: [],
      status: "draft",
      lastModified: new Date(),
      tags: ["new"],
    };

    this.workspaces.set(id, newWorkspace);
    this.renderWorkspaces();
    this.editWorkspace(id);
  }

  editWorkspace(workspaceId) {
    console.log("🔥 DEBUG: editWorkspace called with ID:", workspaceId);
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) {
      console.log(
        "❌ DEBUG: No workspace found for editWorkspace ID:",
        workspaceId
      );
      return;
    }

    // Set the current editing card ID so saveWorkspace knows which one to update
    this.currentEditingCard = workspaceId;
    console.log(
      "✅ DEBUG: currentEditingCard set to:",
      this.currentEditingCard
    );

    const editHTML = this.getEditWorkspaceHTML(workspace);
    this.showModal("Edit Workspace", editHTML);
    console.log("✅ DEBUG: Modal opened for editing");
  }

  duplicateWorkspace(workspaceId) {
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) return;

    const newId = "ws-" + Date.now();
    const duplicatedWorkspace = {
      ...workspace,
      id: newId,
      title: workspace.title + " (Copy)",
      lastModified: new Date(),
    };

    this.workspaces.set(newId, duplicatedWorkspace);
    this.renderWorkspaces();
  }

  deleteWorkspace(workspaceId) {
    if (confirm("Are you sure you want to delete this workspace?")) {
      this.workspaces.delete(workspaceId);
      this.renderWorkspaces();
    }
  }

  openWidgetPanel(workspaceId) {
    this.currentEditingCard = workspaceId;
    this.loadWidgetPanel();

    const panel = document.getElementById("workspace-widget-panel");
    panel.classList.add("active");
  }

  closeWidgetPanel() {
    const panel = document.getElementById("workspace-widget-panel");
    panel.classList.remove("active");
    this.currentEditingCard = null;
  }

  loadWidgetPanel() {
    const content = document.getElementById("widget-panel-content");
    if (!content) return;

    const widgetCategories = [
      {
        title: "Charts & Graphs",
        widgets: [
          { type: "chart", title: "Line Chart", icon: "chart-line" },
          { type: "bar", title: "Bar Chart", icon: "chart-bar" },
          { type: "pie", title: "Pie Chart", icon: "chart-pie" },
          { type: "area", title: "Area Chart", icon: "chart-area" },
        ],
      },
      {
        title: "Data Display",
        widgets: [
          { type: "table", title: "Data Table", icon: "table" },
          { type: "list", title: "List View", icon: "list" },
          { type: "grid", title: "Grid View", icon: "th" },
          { type: "cards", title: "Card View", icon: "id-card" },
        ],
      },
      {
        title: "Metrics & KPIs",
        widgets: [
          { type: "kpi", title: "KPI Card", icon: "tachometer-alt" },
          { type: "gauge", title: "Gauge", icon: "gauge" },
          { type: "progress", title: "Progress Bar", icon: "tasks" },
          { type: "counter", title: "Counter", icon: "calculator" },
        ],
      },
      {
        title: "Interactive",
        widgets: [
          { type: "filter", title: "Filter Panel", icon: "filter" },
          { type: "search", title: "Search Box", icon: "search" },
          { type: "calendar", title: "Calendar", icon: "calendar" },
          { type: "map", title: "Map View", icon: "map" },
        ],
      },
    ];

    content.innerHTML = widgetCategories
      .map(
        (category) => `
      <div class="widget-category">
        <h4 class="widget-category-title">${category.title}</h4>
        <div class="widget-grid">
          ${category.widgets
            .map(
              (widget) => `
            <div class="widget-item" onclick="window.workspaceCards.addWidgetToWorkspace('${widget.type}', '${widget.title}')">
              <i class="las la-${widget.icon}"></i>
              <p class="widget-item-title">${widget.title}</p>
            </div>
          `
            )
            .join("")}
        </div>
      </div>
    `
      )
      .join("");
  }

  addWidgetToWorkspace(type, title) {
    if (!this.currentEditingCard) return;

    const workspace = this.workspaces.get(this.currentEditingCard);
    if (!workspace) return;

    const newWidget = {
      id: "widget-" + Date.now(),
      type,
      title,
      config: {},
    };

    workspace.widgets.push(newWidget);
    workspace.lastModified = new Date();

    this.renderWorkspaces();
    this.closeWidgetPanel();
  }

  // Modal Methods
  showModal(title, content) {
    const overlay = document.getElementById("workspace-overlay");
    const modalTitle = document.getElementById("modal-title");
    const modalContent = document.getElementById("modal-content");

    modalTitle.textContent = title;
    modalContent.innerHTML = content;
    overlay.classList.add("active");
  }

  closeModal() {
    const overlay = document.getElementById("workspace-overlay");
    overlay.classList.remove("active");
    this.currentEditingCard = null;
  }

  getEditWorkspaceHTML(workspace) {
    return `
      <form onsubmit="window.workspaceCards.saveWorkspace(event)" data-workspace-id="${
        workspace.id
      }">
        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 600;">Workspace Name</label>
          <input type="text" id="workspace-title" value="${workspace.title}" 
                 style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px;">
        </div>
        
        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 600;">Description</label>
          <textarea id="workspace-description" rows="3" 
                    style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px; resize: vertical;">${
                      workspace.description
                    }</textarea>
        </div>
        
        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 600;">Tags (comma separated)</label>
          <input type="text" id="workspace-tags" value="${workspace.tags.join(
            ", "
          )}" 
                 style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px;">
        </div>
        
        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: 600;">Status</label>
          <select id="workspace-status" style="width: 100%; padding: 12px; border: 2px solid #e5e7eb; border-radius: 8px; font-size: 16px;">
            <option value="draft" ${
              workspace.status === "draft" ? "selected" : ""
            }>Draft</option>
            <option value="active" ${
              workspace.status === "active" ? "selected" : ""
            }>Active</option>
            <option value="archived" ${
              workspace.status === "archived" ? "selected" : ""
            }>Archived</option>
          </select>
        </div>
        
        <div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 24px;">
          <button type="button" onclick="window.workspaceCards.closeModal()" 
                  style="padding: 12px 24px; border: 2px solid #e5e7eb; background: white; border-radius: 8px; cursor: pointer;">
            Cancel
          </button>
          <button type="submit" 
                  style="padding: 12px 24px; border: none; background: #6366f1; color: white; border-radius: 8px; cursor: pointer; font-weight: 600;">
            Save Changes
          </button>
        </div>
      </form>
    `;
  }

  saveWorkspace(event) {
    console.log(
      "🔥 DEBUG: saveWorkspace called - currentEditingCard:",
      this.currentEditingCard
    );
    console.log(
      "🔥 DEBUG: Event type:",
      event.type,
      "Event target:",
      event.target
    );

    // BULLETPROOF form submission prevention
    event.preventDefault();
    event.stopPropagation();
    event.stopImmediatePropagation();

    // Additional protection against form submission
    if (event.target && event.target.tagName === "FORM") {
      console.log("🛡️ Additional form submission prevention");
    }

    // DEBUG: Check the current state
    console.log("🔍 DEBUG: Current state check:");
    console.log("  - this.currentEditingCard:", this.currentEditingCard);
    console.log(
      "  - Modal active:",
      document.getElementById("workspace-overlay")?.classList.contains("active")
    );
    console.log("  - Form elements exist:", {
      title: !!document.getElementById("workspace-title"),
      description: !!document.getElementById("workspace-description"),
      tags: !!document.getElementById("workspace-tags"),
      status: !!document.getElementById("workspace-status"),
    });

    if (!this.currentEditingCard) {
      console.log(
        "❌ DEBUG: No currentEditingCard found:",
        this.currentEditingCard
      );

      // ENHANCED FALLBACK: Multiple strategies to find the workspace
      const modal = document.getElementById("workspace-overlay");
      if (modal && modal.classList.contains("active")) {
        console.log(
          "🔄 FALLBACK: Modal is active, trying to identify workspace..."
        );

        // Strategy 1: Find by title match
        const titleElement = document.getElementById("workspace-title");
        if (
          titleElement &&
          titleElement.value &&
          titleElement.value !== "New Workspace"
        ) {
          for (let [id, workspace] of this.workspaces) {
            if (workspace.title === titleElement.value) {
              console.log("🔄 FALLBACK: Found workspace by title:", id);
              this.currentEditingCard = id;
              break;
            }
          }
        }

        // Strategy 2: If title is "New Workspace", find the most recent one
        if (
          !this.currentEditingCard &&
          titleElement &&
          titleElement.value === "New Workspace"
        ) {
          let mostRecentId = null;
          let mostRecentTime = 0;
          for (let [id, workspace] of this.workspaces) {
            if (
              workspace.title === "New Workspace" &&
              workspace.lastModified.getTime() > mostRecentTime
            ) {
              mostRecentTime = workspace.lastModified.getTime();
              mostRecentId = id;
            }
          }
          if (mostRecentId) {
            console.log(
              "🔄 FALLBACK: Found most recent 'New Workspace':",
              mostRecentId
            );
            this.currentEditingCard = mostRecentId;
          }
        }

        // Strategy 3: Check if there's a data attribute on the form or modal
        const form = event.target;
        if (form && form.dataset && form.dataset.workspaceId) {
          console.log(
            "🔄 FALLBACK: Found workspace ID in form data:",
            form.dataset.workspaceId
          );
          this.currentEditingCard = form.dataset.workspaceId;
        }
      }

      // If still no card found, show error and return
      if (!this.currentEditingCard) {
        console.log("❌ FALLBACK FAILED: Could not identify workspace");
        this.showToast(
          "Error: Could not identify workspace to save. Please close the modal and try again.",
          "error"
        );
        return false;
      }
    }

    console.log("✅ DEBUG: currentEditingCard found:", this.currentEditingCard);

    // Store the ID before it might get cleared
    const workspaceId = this.currentEditingCard;
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) {
      console.log("❌ DEBUG: No workspace found for ID:", workspaceId);
      return;
    }
    console.log("✅ DEBUG: workspace found:", workspace);

    const titleElement = document.getElementById("workspace-title");
    const descriptionElement = document.getElementById("workspace-description");
    const tagsElement = document.getElementById("workspace-tags");
    const statusElement = document.getElementById("workspace-status");

    console.log("🔍 DEBUG: Form elements found:", {
      titleElement,
      descriptionElement,
      tagsElement,
      statusElement,
    });

    if (
      !titleElement ||
      !descriptionElement ||
      !tagsElement ||
      !statusElement
    ) {
      console.log("❌ DEBUG: Missing form elements");
      return;
    }

    workspace.title = titleElement.value;
    workspace.description = descriptionElement.value;
    workspace.tags = tagsElement.value
      .split(",")
      .map((tag) => tag.trim())
      .filter((tag) => tag);
    workspace.status = statusElement.value;
    workspace.lastModified = new Date();

    console.log("✅ DEBUG: Workspace updated:", workspace);

    this.renderWorkspaces();
    console.log("✅ DEBUG: renderWorkspaces() called");

    // Manually close modal without calling closeModal() which clears currentEditingCard
    const overlay = document.getElementById("workspace-overlay");
    overlay.classList.remove("active");
    console.log("✅ DEBUG: Modal closed manually");

    // Clear the editing state AFTER saving is complete
    this.currentEditingCard = null;
    console.log("✅ DEBUG: currentEditingCard cleared");

    this.showToast("Workspace updated successfully!", "success");
    console.log("✅ DEBUG: showToast() called");
  }

  // Search and Filter Methods
  handleSearch(query) {
    const cards = document.querySelectorAll(".workspace-card");
    const searchTerm = query.toLowerCase();

    cards.forEach((card) => {
      const workspaceId = card.dataset.workspaceId;
      const workspace = this.workspaces.get(workspaceId);

      if (!workspace) return;

      const searchableText = [
        workspace.title,
        workspace.description,
        ...workspace.tags,
      ]
        .join(" ")
        .toLowerCase();

      if (searchableText.includes(searchTerm)) {
        card.style.display = "";
      } else {
        card.style.display = "none";
      }
    });
  }

  handleFilter(filter, button) {
    // Update active filter button
    document
      .querySelectorAll(".filter-btn")
      .forEach((btn) => btn.classList.remove("active"));
    button.classList.add("active");

    // Filter cards
    const cards = document.querySelectorAll(".workspace-card");

    cards.forEach((card) => {
      const workspaceId = card.dataset.workspaceId;
      const workspace = this.workspaces.get(workspaceId);

      if (!workspace) return;

      if (filter === "all" || workspace.status === filter) {
        card.style.display = "";
      } else {
        card.style.display = "none";
      }
    });
  }

  // Utility Methods
  getWorkspaceIcon(tag) {
    const iconMap = {
      analytics: "chart-bar",
      marketing: "bullhorn",
      finance: "dollar-sign",
      dashboard: "tachometer-alt",
      reports: "file-alt",
      social: "share-alt",
      campaigns: "rocket",
      budget: "calculator",
      expenses: "receipt",
      new: "star",
    };
    return iconMap[tag] || "cube";
  }

  getWidgetIcon(type) {
    const iconMap = {
      chart: "chart-line",
      table: "table",
      kpi: "tachometer-alt",
      social: "share-alt",
      calendar: "calendar",
      bar: "chart-bar",
      pie: "chart-pie",
      area: "chart-area",
      list: "list",
      grid: "th",
      cards: "id-card",
      gauge: "gauge",
      progress: "tasks",
      counter: "calculator",
      filter: "filter",
      search: "search",
      map: "map",
    };
    return iconMap[type] || "cube";
  }

  formatDate(date) {
    const now = new Date();
    const diff = now - date;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) return "Today";
    if (days === 1) return "Yesterday";
    if (days < 7) return `${days} days ago`;
    return date.toLocaleDateString();
  }

  showSearch() {
    const searchInput = document.getElementById("workspace-search-input");
    if (searchInput) {
      searchInput.focus();
    }
  }

  // Canvas Studio Integration Methods
  configureWorkspace(workspaceId) {
    if (this.canvasStudioIntegration) {
      this.canvasStudioIntegration.showTabConfigurationModal(workspaceId);
    } else {
      this.showBasicConfiguration(workspaceId);
    }
  }

  showBasicConfiguration(workspaceId) {
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) return;

    const modalContent = `
      <div class="config-form">
        <div class="form-group">
          <label>Workspace Title</label>
          <input type="text" value="${workspace.title}" id="workspaceTitle" />
        </div>
        <div class="form-group">
          <label>Description</label>
          <textarea id="workspaceDescription">${
            workspace.description
          }</textarea>
        </div>
        <div class="form-group">
          <label>Number of Tabs</label>
          <input type="number" value="${
            workspace.tabCount || 1
          }" min="1" max="10" id="tabCount" />
        </div>
        <div class="form-actions">
          <button class="btn-primary" onclick="window.workspaceCards.saveBasicConfiguration('${workspaceId}')">
            Save Changes
          </button>
          <button class="btn-secondary" onclick="window.workspaceCards.closeModal()">
            Cancel
          </button>
        </div>
      </div>
    `;

    this.showModal("Configure Workspace", modalContent);
  }

  saveBasicConfiguration(workspaceId) {
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) return;

    const title = document.querySelector("#workspaceTitle")?.value;
    const description = document.querySelector("#workspaceDescription")?.value;
    const tabCount = parseInt(document.querySelector("#tabCount")?.value) || 1;

    if (title) workspace.title = title;
    if (description) workspace.description = description;
    workspace.tabCount = tabCount;

    // Update tab configuration if needed
    if (!workspace.tabConfiguration) {
      workspace.tabConfiguration = {
        tabs: [],
        settings: {
          allowReorder: true,
          showTabIcons: false,
          tabPosition: "top",
        },
      };
    }

    // Ensure we have the right number of tabs
    const currentTabCount = workspace.tabConfiguration.tabs.length;
    if (tabCount > currentTabCount) {
      // Add more tabs
      for (let i = currentTabCount; i < tabCount; i++) {
        workspace.tabConfiguration.tabs.push({
          id: `tab-${i + 1}`,
          name: `Tab ${i + 1}`,
          widgets: [],
        });
      }
    } else if (tabCount < currentTabCount) {
      // Remove excess tabs
      workspace.tabConfiguration.tabs = workspace.tabConfiguration.tabs.slice(
        0,
        tabCount
      );
    }

    this.renderWorkspaces();
    this.closeModal();
    this.showToast("Workspace updated successfully!", "success");
  }

  openWorkspace(workspaceId) {
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) return;

    // If Canvas Studio integration is available, use it
    if (this.canvasStudioIntegration) {
      this.canvasStudioIntegration.renderWorkspaceInCanvas(workspace);
    } else {
      // Basic implementation
      this.showToast(`Opening ${workspace.title}...`, "info");
    }
  }

  showToast(message, type = "info") {
    // Create toast notification
    const toast = document.createElement("div");
    toast.className = `workspace-toast ${type}`;
    toast.innerHTML = `
      <div class="toast-content">
        <i class="las la-${
          type === "success"
            ? "check-circle"
            : type === "error"
            ? "exclamation-circle"
            : "info-circle"
        }"></i>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(toast);

    // Show toast
    setTimeout(() => toast.classList.add("show"), 100);

    // Hide and remove toast
    setTimeout(() => {
      toast.classList.remove("show");
      setTimeout(() => document.body.removeChild(toast), 300);
    }, 3000);
  }
}

// Export for global access
window.WorkspaceCards = WorkspaceCards;
