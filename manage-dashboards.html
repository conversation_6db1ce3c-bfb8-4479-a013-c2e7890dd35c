<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Digital Asset</title>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet" />
  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <link rel="stylesheet"
    href="https://maxst.icons8.com/vue-static/landings/line-awesome/line-awesome/1.3.0/css/line-awesome.min.css" />

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <!-- GridStack CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/gridstack@10.3.1/dist/gridstack.min.css" />
  <!-- GridStack extra CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/gridstack@10.3.1/dist/gridstack-extra.min.css" />

  <link rel="stylesheet" href="./inline-raita.css">
  <!-- Widget Sidebar CSS -->
  <link rel="stylesheet" href="./css/widget-sidebar.css">

  <!-- Custom Styles for Manage Dashboards -->
  <style>
    /* WNS Procurement Color Palette */
    :root {
      --ocean-teal: #00B19C;
      --emerald-green: #3BCD3F;
      --forest-green: #007365;
      --denali-blue: #02104F;
      --wns-black: #231F20;
      --slate-grey: #8DBAC4;
      --wns-orange: #F37021;
      --wns-red: #ED1C24;

      /* Status colors */
      --status-red: #BB3609;
      --status-green: #25BB3D;
      --status-amber: #FBC20D;
    }

    body {
      color: var(--wns-black);
      /* background-color: #f8f9fa; */
    }

    .dashboard-link {
      font-weight: 500;
      color: var(--ocean-teal);
      text-decoration: none;
      font-size: 12px;
    }

    .dashboard-link:hover {
      color: var(--forest-green);
      text-decoration: underline;
    }

    .dashboard-icon {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 20px;
    }

    .bg-teal {
      background-color: var(--ocean-teal);
    }

    .bg-blue {
      background-color: var(--denali-blue);
    }

    .bg-green {
      background-color: var(--emerald-green);
    }

    .bg-purple {
      background-color: var(--forest-green);
    }

    .avatar-circle {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      margin-right: -12px;
      border: 2px solid white;
      transition: all 0.2s ease;
      position: relative;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      cursor: pointer;
    }

    .collaborator-avatars {
      display: flex;
      align-items: center;
      padding-left: 5px;
    }

    /* Avatar color variations based on brand colors */
    .avatar-circle[data-initials="HS"] {
      background-color: var(--ocean-teal);
      color: white;
      z-index: 3;
    }

    .avatar-circle[data-initials="RT"] {
      background-color: var(--slate-grey);
      color: var(--denali-blue);
      z-index: 2;
    }

    .avatar-circle[data-initials="VT"] {
      background-color: var(--denali-blue);
      color: white;
      z-index: 2;
    }

    .avatar-circle[data-initials="SI"] {
      background-color: var(--emerald-green);
      color: white;
      z-index: 2;
    }

    .avatar-circle[data-initials="AB"] {
      background-color: var(--forest-green);
      color: white;
      z-index: 2;
    }

    /* Gradient background options for avatars */
    .avatar-circle.gradient-teal {
      background: linear-gradient(135deg, var(--ocean-teal) 0%, var(--emerald-green) 100%);
      color: white;
    }

    .avatar-circle.gradient-blue {
      background: linear-gradient(135deg, var(--denali-blue) 0%, var(--slate-grey) 100%);
      color: white;
    }

    .avatar-circle.gradient-orange {
      background: linear-gradient(135deg, var(--wns-orange) 0%, #FFB347 100%);
      color: white;
    }

    /* Subtle shine effect for avatars */
    .avatar-circle::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0, 0.05) 100%);
      pointer-events: none;
    }

    /* Hover effects */
    .avatar-circle:hover {
      transform: translateY(-3px) scale(1.05);
      box-shadow: 0 5px 8px rgba(0, 0, 0, 0.15), 0 0 0 2px rgba(0, 177, 156, 0.3);
      z-index: 10 !important;
    }

    /* Additional styling for the +X avatar */
    .avatar-circle.more-users {
      background-color: var(--wns-orange);
      color: white;
      margin-left: -5px;
      z-index: 1;
    }

    /* Pulse animation for the +X avatar */
    @keyframes subtle-pulse {
      0% {
        transform: scale(1);
      }

      50% {
        transform: scale(1.05);
      }

      100% {
        transform: scale(1);
      }
    }

    .avatar-circle.more-users:hover {
      animation: subtle-pulse 1.5s infinite;
    }

    .table th {
      font-weight: 500;
      color: var(--wns-black);
      border-bottom: 2px solid var(--slate-grey);
    }

    .badge {
      font-weight: 500;
      padding: 5px 10px;
      font-size: 12px;
      border-radius: 0 !important;
    }

    .badge.bg-info {
      background-color: var(--slate-grey) !important;
      color: var(--wns-black) !important;
    }

    .badge.bg-primary {
      background-color: var(--ocean-teal) !important;
    }

    .badge.bg-success {
      background-color: var(--emerald-green) !important;
    }

    .badge.bg-danger {
      background-color: var(--wns-orange) !important;
    }

    .badge.bg-secondary {
      background-color: var(--denali-blue) !important;
    }

    .create-dashboard-card {
      border: 2px dashed var(--slate-grey);
      transition: all 0.2s ease;
    }

    .create-dashboard-card:hover {
      border-color: var(--ocean-teal);
      background-color: rgba(0, 177, 156, 0.05);
    }

    .create-icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background-color: rgba(0, 177, 156, 0.1);
      color: var(--ocean-teal);
      font-size: 24px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }

    .btn-primary {
      background-color: var(--ocean-teal) !important;
      border-color: var(--ocean-teal) !important;
    }

    .btn-primary:hover {
      background-color: var(--forest-green) !important;
      border-color: var(--forest-green) !important;
    }

    #create-dashboard-btn {
      background-color: var(--ocean-teal);
      border-color: var(--ocean-teal);
    }

    #create-dashboard-btn:hover {
      background-color: var(--forest-green);
      border-color: var(--forest-green);
    }

    .card {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }

    .btn-outline-primary {
      color: var(--ocean-teal);
      border-color: var(--ocean-teal);
    }

    .btn-outline-primary:hover {
      background-color: var(--ocean-teal);
      border-color: var(--ocean-teal);
    }

    select,
    input,
    button {
      border-radius: 0px !important;
    }

    .form-label {
      color: var(--wns-black);
      font-weight: 500;
    }

    .form-select:focus,
    .form-control:focus {
      border-color: var(--ocean-teal);
      box-shadow: 0 0 0 0.25rem rgba(0, 177, 156, 0.25);
    }

    .app-header {
      background-color: white;
      border-bottom: 3px solid var(--forest-green);
    }

    .divider {
      background-color: var(--slate-grey);
    }

    .user-avatar {
      background-color: var(--ocean-teal);
    }

    .dashboard-title {
      color: var(--denali-blue) !important;
    }

    .dropdown-item:active {
      background-color: var(--ocean-teal);
    }

    .dropdown-item.text-danger {
      color: var(--wns-red) !important;
    }

    .dropdown-item.text-danger:hover {
      background-color: rgba(237, 28, 36, 0.1);
    }

    /* DataTables Custom Styling */
    .dataTables_wrapper .dataTables_length select {
      background-color: white;
      border: 1px solid var(--slate-grey);
      padding: 6px 10px;
      margin: 0 5px;
    }

    .dataTables_wrapper .dataTables_filter input {
      background-color: white;
      border: 1px solid var(--slate-grey);
      padding: 6px 12px;
      margin-left: 10px;
    }

    .dataTables_wrapper .dataTables_filter input:focus {
      border-color: var(--ocean-teal);
      box-shadow: 0 0 0 0.25rem rgba(0, 177, 156, 0.25);
      outline: none;
    }

    /* Clean pagination styling */
    .dataTables_wrapper .dataTables_paginate {
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
      border: none !important;
      background: none !important;
      padding: 6px 0px !important;
      margin: 0;
      color: var(--wns-black) !important;
      box-shadow: none !important;
      position: relative;
      font-weight: 500;
      border-radius: 0 !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
      background-color: var(--ocean-teal) !important;
      color: white !important;
      border: none !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover:not(.current) {
      background-color: rgba(0, 177, 156, 0.1) !important;
      color: var(--ocean-teal) !important;
      border: none !important;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:active {
      box-shadow: none !important;
      background: var(--forest-green) !important;
      color: white !important;
    }

    /* Override any DataTables default colors that might appear blue */
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
      color: #666 !important;
    }

    .dataTables_wrapper .dataTables_paginate .ellipsis {
      padding: 0 1em;
    }

    .dataTables_wrapper .dataTables_info {
      padding-top: 15px;
      color: var(--wns-black);
      font-size: 14px;
    }

    /* Overall table styling improvements */
    .dataTables_wrapper {
      padding: 0;
    }

    .dataTables_wrapper .row:first-child {
      margin-bottom: 20px;
    }

    .dataTables_filter label,
    .dataTables_length label {
      font-weight: 500;
      color: var(--wns-black);
    }

    /* Add spacing between badges when stacked vertically */
    .badge+.badge {
      margin-left: 4px;
    }

    /* Ensure badges wrap properly on smaller screens */
    td .badge {
      display: inline-block;
      margin-bottom: 4px;
    }

    /* Additional table styling to match theme */
    .craftTable {
      border-collapse: collapse;
    }

    .craftTable thead th {
      border-bottom: 2px solid var(--slate-grey);
      border-top: none;
      font-weight: 500;
      padding: 12px 8px;
    }

    .craftTable tbody tr {
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .craftTable tbody tr:hover {
      background-color: rgba(0, 177, 156, 0.05);
    }

    /* Override default DataTables styling */
    table.dataTable {
      border-spacing: 0 !important;
    }

    /* Ensure there's no rounded corners anywhere */
    .dataTables_wrapper .form-control,
    .dataTables_wrapper select,
    .dataTables_wrapper input,
    .dataTables_wrapper button,
    .dataTables_wrapper .page-item,
    .dataTables_wrapper .page-link,
    .dataTables_wrapper .paginate_button {
      border-radius: 0 !important;
    }

    /* Ensure all blue focus states use Ocean Teal */
    .dataTables_wrapper input:focus,
    .dataTables_wrapper select:focus,
    .form-control:focus {
      border-color: var(--ocean-teal) !important;
      box-shadow: 0 0 0 0.25rem rgba(0, 177, 156, 0.25) !important;
    }

    /* Compact pagination with no gaps */
    .dataTables_wrapper .dataTables_paginate {
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
      border: none !important;
      background: none !important;
      padding: 4px 0px !important;
      margin: 0 !important;
      color: var(--wns-black) !important;
      box-shadow: none !important;
      position: relative;
      font-weight: 500;
      border-radius: 0 !important;
      line-height: 1.5;
    }

    /* Remove any space between pagination buttons */
    .dataTables_wrapper .paginate_button+.paginate_button {
      margin-left: 0 !important;
    }

    .active>.page-link,
    .page-link.active {
      background-color: var(--ocean-teal) !important;
      border-color: var(--ocean-teal) !important;
    }

    div.dataTables_wrapper div.dataTables_length select {
      width: 60px !important;
      display: inline-block;
    }

    /* Action buttons styling */
    .action-btn {
      width: 30px;
      height: 30px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border: none;
      background-color: transparent;
      color: var(--ocean-teal);
      border-radius: 0;
      transition: all 0.2s ease;
      margin-right: 2px;
      position: relative;
      cursor: pointer;
      padding: 0;
    }

    .action-btn:hover {
      background-color: rgba(0, 177, 156, 0.1);
      transform: translateY(-2px);
    }

    .action-btn::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 50%;
      width: 0;
      height: 2px;
      background-color: transparent;
      transform: translateX(-50%);
      transition: all 0.2s ease;
    }

    .action-btn:hover::after {
      width: 80%;
      background-color: currentColor;
    }

    .action-btn i {
      font-size: 20px;
    }

    .action-btn-edit:hover {
      color: var(--ocean-teal);
    }

    .action-btn-edit:hover::after {
      background-color: var(--ocean-teal);
    }

    .action-btn-share:hover {
      color: var(--ocean-teal);
    }

    .action-btn-share:hover::after {
      background-color: var(--ocean-teal);
    }

    .action-btn-duplicate:hover {
      color: var(--ocean-teal);
    }

    .action-btn-duplicate:hover::after {
      background-color: var(--ocean-teal);
    }

    .action-btn-delete:hover {
      color: var(--wns-red);
      background-color: rgba(237, 28, 36, 0.1);
    }

    .action-btn-delete:hover::after {
      background-color: var(--wns-red);
    }

    .actions-wrapper {
      white-space: nowrap;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    /* Remove separators between buttons */
    .action-btn:not(:last-child)::before {
      display: none;
    }

    /* Table styling */
    .dashboard-table th:last-child,
    .dashboard-table td:last-child {
      text-align: center;
    }

    /* Remove sorting arrows from Actions column */
    .dashboard-table th:last-child::before,
    .dashboard-table th:last-child::after {
      display: none !important;
    }

    /* Add a "not-sortable" class to the header */
    .dashboard-table th.no-sort {
      pointer-events: none;
    }

    /* Custom sharp, compact alert for create dashboard modal */
    #create-dashboard-alert.alert {
      border-radius: 0 !important;
      padding: 0.25rem 0.75rem !important;
    }

    body {
      font-size: 12px;
    }

    .form-select {
      font-size: 12px;
    }

    .bgDark th {
      color: #fff;
      background: #231f20;
      font-weight: normal;
    }

    .table td,
    .table th {
      font-size: 12px;
    }

    .bg-light {
      background: #8dbac4 !important;
    }

    .font-size14 {
      font-size: 14px;
    }

    .border_collapse {
      border: 1px solid #8dbac4;
      padding-left: 10px !important;
      padding-right: 10px !important;
      margin-left: 1px;
      width: 99.87%;
    }

     .themeBorderBottom .nav-link,  .themeBorderBottom .nav-link:hover{
      color: #231f20;
    }
    .footer {
    font-size: 12px;
    position: relative;
    width: 100%;
    background: #02104f;
    border-top: 1px solid #02104f;
    padding: 8px 0;
    color: white;
    flex: 0;
}
   .footer a{
    font-size: 12px;
    color: #fff;
   }
   .widget-categories {
    margin-bottom: 5px;
}
.widget-section {
  padding-top: 5px;
  padding-bottom: 5px
}
.widget-section {
    height: 120px !important;
}
.widget-section {
    padding-left: 5px;
    padding-right: 5px;
}

nav#tsc_nav_1 {
    border-bottom: 3px solid #00b19c;
    margin-bottom: 5px;
    min-height: 50px;
}
.grid-stack {
    background: #f8f9fab8;
    border-radius: 0;
    border: none;
    margin: 0 !important;
    padding: 0 !important;
    border: 1px solid #cccccc30;
}
.widget-item {
  min-height: 60px !important;
}
#tsc_nav_1 .nav-link {
    padding: 0.7rem;
    font-size: 12px;
    display: block;
    padding: 0.4rem 0.5rem;
}
nav#tsc_nav_1 {
  padding-left: 0px;
}
  </style>
</head>

<body>
  <!-- Dashboard Header -->
     <nav class="navbar navbar-expand-lg navbar-dark bg-primary1 themeBorderBottom py-0" id="tsc_nav_1">
    <a class="navbar-brand" href="#">
        <div class="font-size14 logo-text">
            <img src="https://d29wfqajwlhhm6.cloudfront.net/Amplifipro-UAT/logo.svg" style="height: 30px;"
                onclick="window.location.href='https://amplifipro-qa.tsclabs.in'">
        </div>
    </a>

    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown"
        aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>

    <div class="collapse navbar-collapse justify-content-end" id="navbarNavDropdown">
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" href="https://amplifipro-qa.tsclabs.in/tschome">Home</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" target="_blank" href="https://amplifipro-qa.tsclabs.in/clear-cache">Clear cache</a>
            </li>

            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="manageContentDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    Manage Content
                </a>
                <ul class="dropdown-menu" aria-labelledby="manageContentDropdown">
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/banner">Manage banners</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/collateral">Manage collateral</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/reportList">Manage reports</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/manage-ci">Manage CI details</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/manage-report-details">Manage reports details</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/ManageThemes">Manage procurement themes</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/productReportMapping">Product report mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/categoryWidgetMapping">Category insights & widget mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/smartriskcategorymapping">Manage Smartrisk category mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/smartrisksuppliermapping">Manage Smartrisk supplier mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/industryCodeMapping">Industry codes mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/commodityWidgetMapping">Client-wise dashboard and widget mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/cost-calculator-upload">Manage cost calculator data</a></li>
                </ul>
            </li>

            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="manageTierDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    Manage Tier
                </a>
                <ul class="dropdown-menu" aria-labelledby="manageTierDropdown">
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/tier">Manage tier master</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/listClientTierMapping">Client configuration (client & tier mapping)</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/manageClientApi">Manage client API limit</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/downloadClientApiUses">Download API usage report</a></li>
                </ul>
            </li>

            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="manageCategoryDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    Manage Category & Mapping
                </a>
                <ul class="dropdown-menu" aria-labelledby="manageCategoryDropdown">
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/category">Manage category</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/categoryReportMapping">Manage category report mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/categoryCSMapping">Manage category cs mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/categoryCommodityIdMappings">Manage category & dashboard mapping</a></li>
                </ul>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="https://amplifipro-qa.tsclabs.in/tschome/tagMasterList">Manage tags</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="https://amplifipro-qa.tsclabs.in/tschome/jobs">Manage Jobs</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="https://amplifipro-qa.tsclabs.in/tschome/addUserRoleMapping">Manage user role mapping</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" target="_blank" href="https://suchna.tsclabs.in/">Notifications</a>
            </li>

            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-user"></i> Md Qaisar
                </a>
                <ul class="dropdown-menu" aria-labelledby="userDropdown">
                    <li>
                        <a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/logout" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a>
                        <form id="logout-form" action="https://amplifipro-qa.tsclabs.in/logout" method="POST" style="display: none;">
                            <input type="hidden" name="_token" value="8rzjr5thxFJ3PIUsEFzlJjewv2KAi3vkCe93IYpY" autocomplete="off">
                        </form>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
  </nav>

  <!-- Main Content -->
  <div class="container-fluid" style="margin-top: 80px;">
    <!-- Dashboard Table Section -->
     <section>
      <div class="row px-3">
        <div class="col-12 px-0">
          <div class="row">
            <div class="col-sm-6">
              <h3 class="font-size16 mb-2 ">My dashboards</h3>
            </div>
            <div class="col-sm-6 text-end">
              <button class="btn btn-sm btn-primary" id="create-dashboard-btn">
                <i class="las la-plus"></i> Create dashboard
              </button>
            </div>
          </div>
          <div class="row px-3 mt-2">
            <div class="col-sm-6 bg-light">
              <h3 class="font-size14 mb-0 py-2 text-white">Filter </h3>
            </div>
            <div class="col-sm-6 bg-light d-flex text-white justify-content-end align-items-center">
              <span class="filterIcon1 float-right toggleForm1">
                <i class="fas fa-chevron-down"></i>
              </span>
            </div>
          </div>
          <div class="align-items-end border_collapse mb-3 pb-3 pt-2 row" id="collapseFilter1">
            <div class="col-md-3">
              <label for="client-filter" class="form-label">Client</label>
              <select class="form-select" id="client-filter">
                <option value="" selected disabled>Select client</option>
                <option value="global-industries">Global Industries</option>
                <option value="wns-procurement">WNS Procurement</option>
                <option value="thesmartcube">TheSmartCube</option>
              </select>
            </div>
            <div class="col-md-3">
              <label for="project-filter" class="form-label">Dashboard</label>
              <select class="form-select" id="project-filter">
                <option value="" selected>Select dashboard</option>
                <!-- Projects will be populated based on client selection -->
              </select>
            </div>
            <div class="col-md-3">
              <button class="btn btn-sm btn-primary" id="apply-filters-btn" type="button"
                style="min-width: 80px;">Apply</button>
            </div>
          </div>
          <div class="table-responsive">
            <table class="table table-bordered table-striped table-sm">
              <thead>
                <tr class="bgDark">
                  <th style="min-width:400px;width:400px;">Dashboard name</th>
                  <th style="min-width:400px;width:400px;">Client</th>
                  <th class="text-center">Last edited</th>
                  <th  class="text-center">Actions</th>
             
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><div class="d-flex align-items-center" style="outline-color: rgb(0, 177, 156);">
                          <div style="outline-color: rgb(0, 177, 156);">
                            <a href="index2.html" class="dashboard-link" style="outline-color: rgb(0, 177, 156);">Cobalt</a>
                            <div class="small text-muted" style="outline-color: rgb(0, 177, 156);">Last edited by Vimal Thapliyal · 3 weeks ago</div>
                          </div>
                        </div></td>
                  <td>TSC Client Master	</td>
                  <td></td>
              
                  <td><div class="actions-wrapper text-center" style="outline-color: rgb(0, 177, 156);">
                          <button class="action-btn action-btn-edit" data-bs-toggle="tooltip" aria-label="Edit dashboard" data-bs-original-title="Edit dashboard" style="outline-color: rgb(0, 177, 156);">
                            <i class="las la-edit" style="outline-color: rgb(0, 177, 156);"></i>
                          </button>
                          <button class="action-btn action-btn-share" data-bs-toggle="tooltip" aria-label="Share dashboard" data-bs-original-title="Share dashboard" style="outline-color: rgb(0, 177, 156);">
                            <i class="las la-share" style="outline-color: rgb(0, 177, 156);"></i>
                          </button>
                          <button class="action-btn action-btn-duplicate" data-bs-toggle="tooltip" aria-label="Duplicate dashboard" data-bs-original-title="Duplicate dashboard" style="outline-color: rgb(0, 177, 156);">
                            <i class="las la-copy" style="outline-color: rgb(0, 177, 156);"></i>
                          </button>
                          <button class="action-btn action-btn-delete" data-bs-toggle="tooltip" aria-label="Delete dashboard" data-bs-original-title="Delete dashboard" style="outline-color: rgb(0, 177, 156);">
                            <i class="las la-trash" style="outline-color: rgb(0, 177, 156);"></i>
                          </button>
                        </div></td>
                </tr>
                <tr>
                  <td><div class="d-flex align-items-center" style="outline-color: rgb(0, 177, 156);">
                          <div style="outline-color: rgb(0, 177, 156);">
                            <a href="index2.html" class="dashboard-link" style="outline-color: rgb(0, 177, 156);">Cobalt</a>
                            <div class="small text-muted" style="outline-color: rgb(0, 177, 156);">Last edited by Vimal Thapliyal · 3 weeks ago</div>
                          </div>
                        </div></td>
                  <td>Nestle	</td>
                  <td></td>
              
                  <td><div class="actions-wrapper text-center" style="outline-color: rgb(0, 177, 156);">
                          <button class="action-btn action-btn-edit" data-bs-toggle="tooltip" aria-label="Edit dashboard" data-bs-original-title="Edit dashboard" style="outline-color: rgb(0, 177, 156);">
                            <i class="las la-edit" style="outline-color: rgb(0, 177, 156);"></i>
                          </button>
                          <button class="action-btn action-btn-share" data-bs-toggle="tooltip" aria-label="Share dashboard" data-bs-original-title="Share dashboard" style="outline-color: rgb(0, 177, 156);">
                            <i class="las la-share" style="outline-color: rgb(0, 177, 156);"></i>
                          </button>
                          <button class="action-btn action-btn-duplicate" data-bs-toggle="tooltip" aria-label="Duplicate dashboard" data-bs-original-title="Duplicate dashboard" style="outline-color: rgb(0, 177, 156);">
                            <i class="las la-copy" style="outline-color: rgb(0, 177, 156);"></i>
                          </button>
                          <button class="action-btn action-btn-delete" data-bs-toggle="tooltip" aria-label="Delete dashboard" data-bs-original-title="Delete dashboard" style="outline-color: rgb(0, 177, 156);">
                            <i class="las la-trash" style="outline-color: rgb(0, 177, 156);"></i>
                          </button>
                        </div></td>
                </tr>
                <tr>
                  <td><div class="d-flex align-items-center" style="outline-color: rgb(0, 177, 156);">
                          <div style="outline-color: rgb(0, 177, 156);">
                            <a href="index2.html" class="dashboard-link" style="outline-color: rgb(0, 177, 156);">Cobalt</a>
                            <div class="small text-muted" style="outline-color: rgb(0, 177, 156);">Last edited by Vimal Thapliyal · 3 weeks ago</div>
                          </div>
                        </div></td>
                  <td>JnJ	</td>
                  <td></td>
              
                  <td><div class="actions-wrapper text-center" style="outline-color: rgb(0, 177, 156);">
                          <button class="action-btn action-btn-edit" data-bs-toggle="tooltip" aria-label="Edit dashboard" data-bs-original-title="Edit dashboard" style="outline-color: rgb(0, 177, 156);">
                            <i class="las la-edit" style="outline-color: rgb(0, 177, 156);"></i>
                          </button>
                          <button class="action-btn action-btn-share" data-bs-toggle="tooltip" aria-label="Share dashboard" data-bs-original-title="Share dashboard" style="outline-color: rgb(0, 177, 156);">
                            <i class="las la-share" style="outline-color: rgb(0, 177, 156);"></i>
                          </button>
                          <button class="action-btn action-btn-duplicate" data-bs-toggle="tooltip" aria-label="Duplicate dashboard" data-bs-original-title="Duplicate dashboard" style="outline-color: rgb(0, 177, 156);">
                            <i class="las la-copy" style="outline-color: rgb(0, 177, 156);"></i>
                          </button>
                          <button class="action-btn action-btn-delete" data-bs-toggle="tooltip" aria-label="Delete dashboard" data-bs-original-title="Delete dashboard" style="outline-color: rgb(0, 177, 156);">
                            <i class="las la-trash" style="outline-color: rgb(0, 177, 156);"></i>
                          </button>
                        </div></td>
                </tr>
                
                
                
              </tbody>

            </table>
          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- JavaScript Libraries -->
  <!-- jQuery (required for many Bootstrap components) -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <!-- Bootstrap JS Bundle -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <!-- DataTables CSS and JS -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
  <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
  <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

  <!-- Custom JavaScript for Manage Dashboards -->
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Initialize DataTable
      $('.dashboard-table').DataTable({
        pageLength: 5,
        lengthMenu: [5, 10, 25, 50],
        language: {
          search: "Search dashboards:",
          lengthMenu: "Show _MENU_ dashboards per page",
          info: "Showing _START_ to _END_ of _TOTAL_ dashboards",
          paginate: {
            first: "First",
            last: "Last",
            next: "Next",
            previous: "Previous"
          }
        },
        // Customized DataTables settings
        dom: '<"row align-items-center mb-3"<"col-md-6"l><"col-md-6 text-md-end"f>>rt<"row mt-3"<"col-md-6"i><"col-md-6"p>>',
        searching: true,
        stateSave: false,
        responsive: true,
        // Disable sorting on Actions column (last column)
        columnDefs: [
          {
            targets: -1,
            orderable: false,
            className: 'no-sort'
          }
        ],
        // Remove default styling that conflicts with our theme
        drawCallback: function () {
          // Remove any inline styling that might be added by DataTables
          $('.paginate_button').removeAttr('style');

          // Remove borders around the entire table
          $(this).closest('.dataTables_wrapper').find('.dataTable').css('border', 'none');

          // Add clean table class
          $(this).closest('.dataTables_wrapper').addClass('clean-datatable');

          // Ensure pagination is centered properly
          $('.dataTables_paginate').addClass('d-flex justify-content-end');

          // Reinitialize tooltips after draw
          reinitializeTooltips();
        },
        initComplete: function () {
          // Set the search box placeholder
          $('.dataTables_filter input').attr('placeholder', 'Search...');

          // Replace any default blue focus colors
          $('*').css('outline-color', '#00B19C');

          // Initialize tooltips
          reinitializeTooltips();
        }
      });

      // Function to initialize tooltips with custom configuration
      function reinitializeTooltips() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.forEach(function (tooltipTriggerEl) {
          var tooltip = bootstrap.Tooltip.getInstance(tooltipTriggerEl);
          if (tooltip) {
            tooltip.dispose();
          }

          new bootstrap.Tooltip(tooltipTriggerEl, {
            placement: 'top',
            boundary: 'window',
            delay: { show: 300, hide: 100 }
          });
        });
      }

      // Add click handler for avatars to show collaborator information
      $('.avatar-circle').on('click', function (e) {
        // Prevent default link behavior
        e.preventDefault();
        e.stopPropagation();

        // Get initials
        var initials = $(this).data('initials');

        // Show a different visual feedback beyond the tooltip
        if (!$(this).hasClass('more-users')) {
          $(this).addClass('active-avatar').css({
            'transform': 'scale(1.1)',
            'box-shadow': '0 0 0 3px rgba(0, 177, 156, 0.5)'
          });

          // Remove effect after delay
          setTimeout(function () {
            $('.active-avatar').removeClass('active-avatar').css({
              'transform': '',
              'box-shadow': ''
            });
          }, 1500);
        }
      });

      // Handle client filter change
      const clientFilter = document.getElementById('client-filter');
      const projectFilter = document.getElementById('project-filter');

      clientFilter.addEventListener('change', function () {
        // Reset project filter
        projectFilter.innerHTML = '<option value="" selected>Select project</option>';

        // Get selected client
        const selectedClient = this.value;

        if (selectedClient) {
          // Add projects based on selected client
          if (selectedClient === 'global-industries') {
            addOption(projectFilter, 'gi-market-report', 'Market Report Q1 2024');
            addOption(projectFilter, 'gi-procurement', 'Procurement Strategy');
            addOption(projectFilter, 'gi-financial', 'Financial Analysis');
          } else if (selectedClient === 'wns-procurement') {
            addOption(projectFilter, 'wns-supply-chain', 'Supply Chain Optimization');
            addOption(projectFilter, 'wns-vendor', 'Vendor Management');
          } else if (selectedClient === 'thesmartcube') {
            addOption(projectFilter, 'tsc-market-intel', 'Market Intelligence');
            addOption(projectFilter, 'tsc-competitor', 'Competitor Analysis');
          }
        }
      });

      // Handle filter functionality with Apply button
      const dateFilter = document.getElementById('date-filter');
      document.getElementById('apply-filters-btn').addEventListener('click', function () {
        // Get the DataTable instance
        const dataTable = $('.dashboard-table').DataTable();
        // Clear existing filters
        dataTable.search('').columns().search('').draw();
        // Apply filters
        const clientValue = clientFilter.value;
        const projectValue = projectFilter.value;
        const dateValue = dateFilter.value;
        // Build a compound search string
        let searchString = '';
        if (clientValue) searchString += clientValue + ' ';
        if (projectValue) searchString += projectValue + ' ';
        if (dateValue) searchString += dateValue + ' ';
        // Apply search
        if (searchString) {
          dataTable.search(searchString.trim()).draw();
        } else {
          dataTable.draw();
        }
      });

      // Handle create dashboard button (open modal instead of redirect)
      const createDashboardBtn = document.getElementById('create-dashboard-btn');
      const createDashboardModal = new bootstrap.Modal(document.getElementById('createDashboardModal'));
      createDashboardBtn.addEventListener('click', function () {
        document.getElementById('createDashboardForm').reset();
        document.getElementById('modal-dashboard-select').innerHTML = '<option value="" selected>Select dashboard</option>';
        createDashboardModal.show();
      });

      // Modal dropdown logic
      const modalClientSelect = document.getElementById('modal-client-select');
      const modalDashboardSelect = document.getElementById('modal-dashboard-select');
      modalClientSelect.addEventListener('change', function () {
        modalDashboardSelect.innerHTML = '<option value="" selected>Select dashboard</option>';
        if (this.value === 'global-industries') {
          addOption(modalDashboardSelect, 'gi-market-report', 'Market Report Q1 2024');
          addOption(modalDashboardSelect, 'gi-procurement', 'Procurement Strategy');
          addOption(modalDashboardSelect, 'gi-financial', 'Financial Analysis');
        } else if (this.value === 'wns-procurement') {
          addOption(modalDashboardSelect, 'wns-supply-chain', 'Supply Chain Optimization');
          addOption(modalDashboardSelect, 'wns-vendor', 'Vendor Management');
        } else if (this.value === 'thesmartcube') {
          addOption(modalDashboardSelect, 'tsc-market-intel', 'Market Intelligence');
          addOption(modalDashboardSelect, 'tsc-competitor', 'Competitor Analysis');
        }
      });

      // Handle Create button in modal
      document.getElementById('modal-create-dashboard-btn').addEventListener('click', function () {
        if (modalClientSelect.value && modalDashboardSelect.value) {
          // Get DataTable instance
          const dataTable = $('.dashboard-table').DataTable();
          // Get selected dashboard name
          const dashboardName = modalDashboardSelect.options[modalDashboardSelect.selectedIndex].text;
          // Get selected client name
          const clientName = modalClientSelect.options[modalClientSelect.selectedIndex].text;
          // Set default values for new row
          const category = '<span class="badge bg-info">' + (clientName === 'WNS Procurement' ? 'Supply Chain' : 'Market Intelligence') + '</span>';
          const lastEdited = 'just now';
          const views = '0';
          const collaborators = `<div class="collaborator-avatars">
              <span class="avatar-circle gradient-teal" data-initials="HS" data-bs-toggle="tooltip" title="Hemant Saigal (Owner)">HS</span>
            </div>`;
          const actions = `<div class="actions-wrapper text-center">
              <button class="action-btn action-btn-edit" data-bs-toggle="tooltip" title="Edit dashboard"><i class="las la-edit"></i></button>
              <button class="action-btn action-btn-share" data-bs-toggle="tooltip" title="Share dashboard"><i class="las la-share"></i></button>
              <button class="action-btn action-btn-duplicate" data-bs-toggle="tooltip" title="Duplicate dashboard"><i class="las la-copy"></i></button>
              <button class="action-btn action-btn-delete" data-bs-toggle="tooltip" title="Delete dashboard"><i class="las la-trash"></i></button>
            </div>`;
          // Add new row to DataTable as the first row
          dataTable.row.add([
            `<div class=\"d-flex align-items-center\"><div><span class=\"dashboard-link\">${dashboardName}</span><div class=\"small text-muted\">Last edited by Hemant Saigal · just now</div></div></div>`,
            clientName,
            lastEdited,
            collaborators,
            actions
          ]).draw(false);
          // Move the new row to the top
          const rows = dataTable.rows().nodes();
          if (rows.length > 1) {
            // Move the last row (newly added) to the top
            $(rows[rows.length - 1]).prependTo($(dataTable.table().body()));
          }
          // Reinitialize tooltips for new row
          var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
          tooltipTriggerList.forEach(function (tooltipTriggerEl) {
            var tooltip = bootstrap.Tooltip.getInstance(tooltipTriggerEl);
            if (tooltip) { tooltip.dispose(); }
            new bootstrap.Tooltip(tooltipTriggerEl, { placement: 'top', boundary: 'window', delay: { show: 300, hide: 100 } });
          });
          // Close modal
          createDashboardModal.hide();
          // Hide alert if visible
          document.getElementById('create-dashboard-alert').classList.add('d-none');
        } else {
          var alertBox = document.getElementById('create-dashboard-alert');
          alertBox.textContent = 'Please select both client and dashboard.';
          alertBox.classList.remove('d-none');
        }
      });

      // Hide alert when user changes dropdowns
      modalClientSelect.addEventListener('change', function () {
        document.getElementById('create-dashboard-alert').classList.add('d-none');
      });
      modalDashboardSelect.addEventListener('change', function () {
        document.getElementById('create-dashboard-alert').classList.add('d-none');
      });

      // Helper function to add options to select element
      function addOption(selectElement, value, text) {
        const option = document.createElement('option');
        option.value = value;
        option.textContent = text;
        selectElement.appendChild(option);
      }
    });
  </script>

  <!-- Create Dashboard Modal -->
  <div class="modal fade" id="createDashboardModal" tabindex="-1" aria-labelledby="createDashboardModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="createDashboardModalLabel">Create new dashboard</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div id="create-dashboard-alert" class="alert alert-danger d-none" role="alert"></div>
          <form id="createDashboardForm">
            <div class="mb-3">
              <label for="modal-client-select" class="form-label">Client</label>
              <select class="form-select" id="modal-client-select" required>
                <option value="" selected>Select client</option>
                <option value="global-industries">Global Industries</option>
                <option value="wns-procurement">WNS Procurement</option>
                <option value="thesmartcube">TheSmartCube</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="modal-dashboard-select" class="form-label">Dashboard</label>
              <select class="form-select" id="modal-dashboard-select" required>
                <option value="" selected>Select dashboard</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="modal-create-dashboard-btn">Create</button>
        </div>
      </div>
    </div>
  </div>
</body>

</html>