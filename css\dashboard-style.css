/* Dashboard UI Styles - Modern, Clean Interface */
@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap");

/* Brand Colors */
:root {
  /* Primary Colors */
  --ocean-teal: #00b19c;
  --emerald-green: #3bcd3f;
  --forest-green: #007365;
  --slate-grey: #8dbac4;
  --denali-blue: #02104f;
  --wns-black: #231f20;

  /* Color Variants (20% Tints) */
  --ocean-teal-light: #cce9e6;
  --emerald-green-light: #d8f5d9;
  --forest-green-light: #cce1df;
  --slate-grey-light: #e3edef;
  --denali-blue-light: #ccd0df;

  /* Risk Colors */
  --high-risk: #ba3909;
  --moderate-risk: #fac209;
  --low-risk: #28ba3b;

  /* Risk Gradients */
  --risk-4-5: #cc5b08;
  --risk-4-0: #dc7c0a;
  --risk-3-5: #ec9f0a;
  --risk-2-5: #c5c01b;
  --risk-2-0: #91bf23;
  --risk-1-5: #5dbc2f;

  /* Base Typography */
  --font-size-base: 12px;
  --font-size-sm: 11px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
}

/* Main Dashboard Layout */
body {
  font-family: "Montserrat", "Segoe UI", Roboto, "Helvetica Neue", Arial,
    sans-serif;
  background-color: #fff;
  color: #231f20;
  margin: 0;
  padding: 0;
  font-size: var(--font-size-base);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}

/* Dashboard Header */
.dashboard-header {
  background: var(--denali-blue);
  color: white;
  padding: 0.75rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.dashboard-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--ocean-teal);
}

.dashboard-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin: 0;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  letter-spacing: 0.5px;
  position: relative;
  text-transform: capitalize;
}

.dashboard-title::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--ocean-teal);
  transition: width 0.3s ease;
}

.dashboard-title:hover::after {
  width: 100%;
}

.dashboard-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.dashboard-btn {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.375rem 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 12px;
  font-weight: 500;
}

.dashboard-btn i {
  font-size: 14px;
  transition: transform 0.2s ease;
}

.dashboard-btn:hover {
  background-color: var(--ocean-teal);
  border-color: var(--ocean-teal);
}

.dashboard-btn:active {
  transform: translateY(1px);
}

/* Widget Categories and Gallery Container */
.widget-section {
  background: white;
  margin: 0.5rem;
  border: 1px solid var(--slate-grey-light);
}

/* Widget Categories Section */
.widget-categories {
  padding: 0.5rem;
  display: flex;
  gap: 0.5rem;
  align-items: center;
  border-bottom: 1px solid var(--slate-grey-light);
  position: relative;
  z-index: 3;
  background: var(--slate-grey-light);
}

.widget-category {
  position: relative;
  padding: 0.375rem 0.75rem;
  cursor: pointer;
  color: var(--wns-black);
  transition: all 0.2s ease;
  font-weight: 500;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 0.375rem;
  background: white;
}

.widget-category i {
  font-size: 16px;
  color: var(--ocean-teal);
}

.widget-category:hover {
  color: var(--ocean-teal);
  background-color: var(--ocean-teal-light);
}

.widget-category.active {
  background: var(--ocean-teal);
  color: white;
  font-weight: 600;
}

.widget-category.active i {
  color: white;
}

/* Widget Gallery */
.widget-gallery {
  padding: 0.75rem;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 0.5rem;
  background: #f8f9fa;
}

.widget-item {
  background: white;
  padding: 0.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--slate-grey-light);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.375rem;
  min-height: 80px;
}

.widget-item:hover {
  background: var(--ocean-teal);
}

.widget-item:hover .widget-icon,
.widget-item:hover .widget-label {
  color: white;
}

.widget-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: var(--ocean-teal);
  transition: all 0.2s ease;
}

.widget-label {
  color: var(--wns-black);
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all 0.2s ease;
  line-height: 1.2;
  margin: 0;
  text-transform: capitalize;
}

/* Widget Category Colors */
.widget-category-insert .widget-icon {
  color: var(--ocean-teal);
}

.widget-category-charts .widget-icon {
  color: var(--forest-green);
}

.widget-category-additional .widget-icon {
  color: var(--denali-blue);
}

.widget-category-advanced .widget-icon {
  color: var(--slate-grey);
}

/* Grid Container Styles */
.grid-stack {
  background: #f8fafc;
  padding: 1rem;
  margin: -0.5rem;
  position: relative;
  gap: 1rem;
  z-index: 1;
}

.grid-stack::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: #fff;
}

/* Grid Stack Item */
.grid-stack-item-content {
  position: relative;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  inset: 0.5rem;
  overflow: visible;
  height: auto;
}

.grid-stack-item-content:hover {
  border-color: var(--ocean-teal);
  box-shadow: 0 4px 12px rgba(0, 177, 156, 0.08);
  transform: translateY(-1px);
}

/* Grid Stack Placeholder */
.grid-stack-placeholder > .placeholder-content {
  background: rgba(0, 177, 156, 0.04) !important;
  border: 2px dashed rgba(0, 177, 156, 0.2) !important;
  inset: 0.5rem !important;
  border-radius: 6px;
  backdrop-filter: blur(2px);
}

/* Grid Stack Resize Handles */
.grid-stack > .grid-stack-item > .ui-resizable-handle {
  width: 8px;
  height: 8px;
  background: #ffffff;
  border: 1.5px solid #e2e8f0;
  transition: all 0.2s ease;
  border-radius: 2px;
  opacity: 0;
}

/* Nested Grid Resize Handles - Fix for tab container nested grids */
.nested-grid .grid-stack > .grid-stack-item > .ui-resizable-handle,
.tab-container-widget .grid-stack > .grid-stack-item > .ui-resizable-handle,
.tab-gridstack-container .grid-stack > .grid-stack-item > .ui-resizable-handle {
  width: 8px;
  height: 8px;
  background: #ffffff;
  border: 1.5px solid #e2e8f0;
  transition: all 0.2s ease;
  border-radius: 2px;
  opacity: 0;
  z-index: 10;
}

.grid-stack-item:hover > .ui-resizable-handle {
  opacity: 1;
}

/* Nested Grid Hover States */
.nested-grid .grid-stack-item:hover > .ui-resizable-handle,
.tab-container-widget .grid-stack-item:hover > .ui-resizable-handle,
.tab-gridstack-container .grid-stack-item:hover > .ui-resizable-handle {
  opacity: 1;
}

/* Nested Grid Handle Hover States */
.nested-grid .grid-stack > .grid-stack-item > .ui-resizable-handle:hover,
.tab-container-widget
  .grid-stack
  > .grid-stack-item
  > .ui-resizable-handle:hover,
.tab-gridstack-container
  .grid-stack
  > .grid-stack-item
  > .ui-resizable-handle:hover {
  background: var(--ocean-teal);
  border-color: var(--ocean-teal);
  transform: scale(1.2);
}

.grid-stack > .grid-stack-item > .ui-resizable-handle:hover {
  background: var(--ocean-teal);
  border-color: var(--ocean-teal);
  transform: scale(1.2);
}

.grid-stack > .grid-stack-item > .ui-resizable-se {
  bottom: 6px;
  right: 6px;
}

.grid-stack > .grid-stack-item > .ui-resizable-sw {
  bottom: 6px;
  left: 6px;
}

.grid-stack > .grid-stack-item > .ui-resizable-ne {
  top: 6px;
  right: 6px;
}

.grid-stack > .grid-stack-item > .ui-resizable-nw {
  top: 6px;
  left: 6px;
}

/* Nested Grid Handle Positions */
.nested-grid .grid-stack > .grid-stack-item > .ui-resizable-se,
.tab-container-widget .grid-stack > .grid-stack-item > .ui-resizable-se,
.tab-gridstack-container .grid-stack > .grid-stack-item > .ui-resizable-se {
  bottom: 6px;
  right: 6px;
}

.nested-grid .grid-stack > .grid-stack-item > .ui-resizable-sw,
.tab-container-widget .grid-stack > .grid-stack-item > .ui-resizable-sw,
.tab-gridstack-container .grid-stack > .grid-stack-item > .ui-resizable-sw {
  bottom: 6px;
  left: 6px;
}

.nested-grid .grid-stack > .grid-stack-item > .ui-resizable-ne,
.tab-container-widget .grid-stack > .grid-stack-item > .ui-resizable-ne,
.tab-gridstack-container .grid-stack > .grid-stack-item > .ui-resizable-ne {
  top: 6px;
  right: 6px;
}

.nested-grid .grid-stack > .grid-stack-item > .ui-resizable-nw,
.tab-container-widget .grid-stack > .grid-stack-item > .ui-resizable-nw,
.tab-gridstack-container .grid-stack > .grid-stack-item > .ui-resizable-nw {
  top: 6px;
  left: 6px;
}

/* Grid Stack Item States */
.grid-stack-item.ui-draggable-dragging {
  z-index: 100;
}

.grid-stack-item.ui-draggable-dragging .grid-stack-item-content {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border: 1.5px solid var(--ocean-teal);
  background: #ffffff !important;
  opacity: 0.9;
  backdrop-filter: blur(4px);
}

.grid-stack-item.ui-resizable-resizing .grid-stack-item-content {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border: 1.5px solid var(--ocean-teal);
  background: #ffffff !important;
  backdrop-filter: blur(4px);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .grid-stack {
    padding: 0.75rem;
    margin: 0;
    gap: 0.75rem;
  }

  .grid-stack-item-content {
    inset: 0.375rem;
    border-radius: 4px;
    padding: 0.5rem;
  }

  .grid-stack > .grid-stack-item > .ui-resizable-handle {
    width: 6px;
    height: 6px;
  }
}

/* Dark Theme Support */
body.dark-theme .grid-stack {
  background: #0f172a;
}

body.dark-theme .grid-stack::before {
  background: #334155;
}

body.dark-theme .grid-stack-item-content {
  background: #1e293b !important;
  border-color: #334155;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

body.dark-theme .grid-stack-item-content:hover {
  border-color: var(--ocean-teal);
  box-shadow: 0 4px 12px rgba(0, 177, 156, 0.1);
}

body.dark-theme .grid-stack-placeholder > .placeholder-content {
  background: rgba(0, 177, 156, 0.08) !important;
  border: 2px dashed rgba(0, 177, 156, 0.3) !important;
}

body.dark-theme .grid-stack > .grid-stack-item > .ui-resizable-handle {
  background: #1e293b;
  border-color: #475569;
}

body.dark-theme .grid-stack > .grid-stack-item > .ui-resizable-handle:hover {
  background: var(--ocean-teal);
  border-color: var(--ocean-teal);
}

body.dark-theme .grid-stack-item.ui-draggable-dragging .grid-stack-item-content,
body.dark-theme
  .grid-stack-item.ui-resizable-resizing
  .grid-stack-item-content {
  background: #1e293b !important;
  border-color: var(--ocean-teal);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

/* Remove all card-header related styles */
.card-header,
.card-title,
.card-actions,
.card-btn {
  display: none !important;
}

/* Control Panel Styles */
.control-panel {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 1.5rem;
}

.control-panel .card-body {
  padding: 1rem;
}

.control-panel .btn-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.control-panel .btn {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.375rem 0.75rem;
  font-size: 12px;
  transition: all 0.2s;
}

.control-panel .btn:hover {
  background-color: #e9ecef;
  color: #212529;
}

.control-panel .btn i {
  font-size: 14px;
  margin-right: 0.5rem;
}

/* Chart Settings Panel */
.chart-settings label {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 0.375rem;
}

.chart-settings .form-select {
  font-size: 12px;
  padding: 0.375rem 0.75rem;
}

.chart-settings .form-check-label {
  font-size: 12px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .widget-section {
    margin: 0.375rem;
  }

  .widget-categories {
    padding: 0.375rem;
    gap: 0.375rem;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
  }

  .widget-categories::-webkit-scrollbar {
    display: none;
  }

  .widget-category {
    padding: 0.25rem 0.5rem;
    font-size: 11px;
    white-space: nowrap;
  }

  .widget-gallery {
    padding: 0.5rem;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.375rem;
  }

  .widget-item {
    padding: 0.375rem;
    min-height: 70px;
    gap: 0.25rem;
  }

  .widget-icon {
    width: 20px;
    height: 20px;
    font-size: 16px;
  }

  .widget-label {
    font-size: 10px;
  }
}

/* Dark Theme */
body.dark-theme {
  background-color: var(--denali-blue);
  color: white;
}

body.dark-theme .dashboard-header {
  background-color: #0a0a23;
}

body.dark-theme .widget-categories,
body.dark-theme .widget-gallery {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
}

body.dark-theme .widget-category {
  color: #e9ecef;
}

body.dark-theme .widget-label {
  color: #e9ecef;
}

body.dark-theme .grid-stack {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

body.dark-theme .grid-stack-item-content {
  background-color: var(--denali-blue-light) !important;
  border-color: var(--slate-grey);
}

body.dark-theme .widget-header {
  background-color: #1e293b;
  border-color: #334155;
}

body.dark-theme .control-panel {
  background-color: #2a2a2a;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

body.dark-theme .control-panel .btn {
  background-color: #333333;
  color: #e9ecef;
  border-color: #3a3a3a;
}

body.dark-theme .control-panel .btn:hover {
  background-color: #444444;
  color: #ffffff;
}

/* Settings Panels */
.offcanvas {
  position: fixed;
  top: 50px !important;
  right: 0;
  bottom: 0;
  width: 600px;
  max-width: 100%;
  background-color: #fff;
  border-left: 1px solid var(--slate-grey-light);
  transform: translateX(100%);
  visibility: hidden;
  z-index: 1045;
  transition: transform 0.3s ease-in-out;
}

.offcanvas.show {
  transform: translateX(0);
  visibility: visible;
}

.offcanvas-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(2, 16, 79, 0.2);
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.15s linear;
  pointer-events: none;
}

.offcanvas-backdrop.show {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

.offcanvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--ocean-teal);
  position: relative;
}

.offcanvas-header h5 {
  color: white;
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.offcanvas-header h5 i {
  font-size: 16px;
}

.offcanvas-body {
  padding: 16px;
  overflow-y: auto;
}

/* Remove Bootstrap's default close button styling */
.btn-close,
.offcanvas-header .btn-close {
  all: unset;
  cursor: pointer;
  width: 20px;
  height: 20px;
  position: relative;
  opacity: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Custom close icon */
.btn-close::before,
.btn-close::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 2px;
  background-color: white;
  top: 50%;
  left: 50%;
}

.btn-close::before {
  transform: translate(-50%, -50%) rotate(45deg);
}

.btn-close::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}

.btn-close:hover::before,
.btn-close:hover::after {
  background-color: rgba(255, 255, 255, 0.8);
}

/* Dark theme support */
body.dark-theme .offcanvas {
  background-color: var(--denali-blue);
  border-left: 1px solid var(--slate-grey);
}

body.dark-theme .offcanvas-header {
  background: var(--ocean-teal);
}

body.dark-theme .offcanvas-body {
  background: var(--denali-blue);
  color: white;
}

body.dark-theme .offcanvas-backdrop {
  background-color: rgba(0, 0, 0, 0.5);
}

/* Risk Indicators */
.risk-high {
  color: var(--high-risk);
}

.risk-moderate {
  color: var(--moderate-risk);
}

.risk-low {
  color: var(--low-risk);
}

/* Card and Grid Layout */
.card {
  background: white;
  border: none;
  margin-bottom: 0.75rem;
  position: relative;
}

.card-body {
  padding: 0.75rem;
  background: white;
}

.card-header {
  padding: 0.875rem 1rem;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--slate-grey-light);
  position: relative;
}

.card-header::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, var(--ocean-teal), transparent);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin: 0;
  font-size: 13px;
  font-weight: 600;
  color: var(--denali-blue);
  letter-spacing: 0.2px;
}

.card-title i {
  color: var(--ocean-teal);
  font-size: 15px;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-btn {
  width: 26px;
  height: 26px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: var(--slate-grey-light);
  color: var(--denali-blue);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
}

.card-btn:hover {
  background: var(--ocean-teal-light);
  color: var(--ocean-teal);
}

.card-btn i {
  font-size: 14px;
}

/* Tooltip styles */
.card-btn[title] {
  position: relative;
}

.card-btn[title]::before {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-4px);
  padding: 4px 8px;
  background: var(--denali-blue);
  color: white;
  font-size: 11px;
  white-space: nowrap;
  border-radius: 3px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}

.card-btn[title]::after {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(4px);
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid var(--denali-blue);
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}

.card-btn[title]:hover::before,
.card-btn[title]:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

@media (max-width: 768px) {
  .card-header {
    padding: 0.75rem;
  }

  .card-title {
    font-size: 12px;
    gap: 0.5rem;
  }

  .card-title i {
    font-size: 14px;
  }

  .card-btn {
    width: 24px;
    height: 24px;
  }

  .card-btn i {
    font-size: 13px;
  }
}

/* Widget Header - Professional Design */
.widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 0 16px;
  background: #ffffff;
  border-bottom: 1px solid #dee2e6;
  margin: -8px -8px 8px -8px;
  position: relative;
}
/* 
.widget-header::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, var(--ocean-teal), transparent);
} */

.widget-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--denali-blue);
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.2px;
}

.widget-header > div:first-child i {
  color: var(--ocean-teal);
  font-size: 16px;
}

.widget-header > div:last-child {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.widget-icons {
  display: flex;
  align-items: center;
  gap: 12px;
  height: 100%;
}

.widget-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  /* color: #64748b; */
  transition: all 0.2s ease;
  border-radius: 6px;
  /* background: #f1f5f9; */
  /* border: 1px solid #e2e8f0; */
}

.widget-icon:hover {
  color: var(--ocean-teal);
  background: var(--ocean-teal-light);
  border-color: var(--ocean-teal);
}

.widget-icon i {
  font-size: 16px;
}

/* Comment Trigger */
.comment-trigger {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #64748b;
  transition: all 0.2s ease;
  border-radius: 6px;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  position: relative;
}

.comment-trigger:hover {
  color: var(--ocean-teal);
  background: var(--ocean-teal-light);
  border-color: var(--ocean-teal);
}

.comment-trigger i {
  font-size: 16px;
}

/* Close icon special styling */
.widget-icon.la-times:hover {
  background: #fecdd3;
  color: #e11d48;
  border-color: #fda4af;
}

/* Dark Theme Support */
body.dark-theme .widget-header {
  background: #1e293b;
  border-color: #334155;
}

body.dark-theme .widget-header > div:first-child {
  color: #f1f5f9;
}

body.dark-theme .widget-icon,
body.dark-theme .comment-trigger {
  background: #334155;
  border-color: #475569;
  color: #94a3b8;
}

body.dark-theme .widget-icon:hover,
body.dark-theme .comment-trigger:hover {
  background: rgba(0, 177, 156, 0.2);
  border-color: var(--ocean-teal);
  color: var(--ocean-teal-light);
}

body.dark-theme .widget-icon.la-times:hover {
  background: rgba(225, 29, 72, 0.2);
  border-color: #e11d48;
  color: #fda4af;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  .widget-header {
    height: 44px;
    padding: 0 12px;
  }

  .widget-header > div:first-child {
    font-size: 13px;
    gap: 8px;
  }

  .widget-icon,
  .comment-trigger {
    width: 28px;
    height: 28px;
  }

  .widget-icon i,
  .comment-trigger i {
    font-size: 14px;
  }

  .widget-icons {
    gap: 8px;
  }
}

/* Ensure proper stacking context */
.grid-stack {
  z-index: 1;
}

.dashboard-header {
  z-index: 2;
}

.widget-categories {
  z-index: 3;
}

/* Prevent interference */
.grid-stack-item {
  z-index: auto;
}

.grid-stack-item.ui-draggable-dragging {
  z-index: 100;
}

/* Widget Header Icons */
.widget-header > div {
  display: flex;
  align-items: center;
  gap: 8px;
}

.widget-header i.las {
  font-size: 18px;
  color: #64748b;
  cursor: pointer;
  transition: color 0.2s ease;
}

.widget-header i.las:hover {
  color: var(--ocean-teal);
}

.widget-header i.la-times:hover {
  color: #dc3545;
}

/* Remove all button styling */
.widget-header button,
.widget-header .btn-link,
.widget-header [class*="btn-"] {
  all: unset;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 !important;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Remove button styling from all widgets */
.grid-stack-item button,
.grid-stack-item .btn-link,
.grid-stack-item [class*="btn-"] {
  border-radius: 0 !important;
  background: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Override any Bootstrap button styles */
.btn,
.btn-link,
[class*="btn-"] {
  border-radius: 0 !important;
}

/* Remove hover effects from buttons */
.widget-header button:hover,
.widget-header .btn-link:hover,
.widget-header [class*="btn-"]:hover {
  background: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Ensure icons are the only clickable elements */
.widget-header button i,
.widget-header .btn-link i,
.widget-header [class*="btn-"] i {
  pointer-events: none;
}

/* Offcanvas Form Controls and Buttons */
.offcanvas .form-control:focus {
  border-color: var(--ocean-teal);
  box-shadow: 0 0 0 1px var(--ocean-teal-light);
}

.offcanvas .btn-primary,
.offcanvas [class*="btn-primary"],
.offcanvas .btn[type="submit"],
.offcanvas button[type="submit"] {
  background: var(--ocean-teal) !important;
  border: none !important;
  color: white !important;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.offcanvas .btn-primary:hover,
.offcanvas [class*="btn-primary"]:hover,
.offcanvas .btn[type="submit"]:hover,
.offcanvas button[type="submit"]:hover {
  background: var(--forest-green) !important;
  transform: translateY(-1px);
}

.offcanvas .form-switch .form-check-input:checked {
  background-color: var(--ocean-teal);
  border-color: var(--ocean-teal);
}

.offcanvas .form-range::-webkit-slider-thumb {
  background: var(--ocean-teal);
}

.offcanvas .form-range::-moz-range-thumb {
  background: var(--ocean-teal);
}

.offcanvas .form-select:focus {
  border-color: var(--ocean-teal);
  box-shadow: 0 0 0 1px var(--ocean-teal-light);
}

/* Dark theme support for form controls */
body.dark-theme .offcanvas .form-control,
body.dark-theme .offcanvas .form-select {
  background-color: #334155;
  border-color: #475569;
  color: white;
}

body.dark-theme .offcanvas .form-control:focus,
body.dark-theme .offcanvas .form-select:focus {
  border-color: var(--ocean-teal);
  box-shadow: 0 0 0 1px var(--ocean-teal-light);
}

body.dark-theme .offcanvas .btn-primary,
body.dark-theme .offcanvas [class*="btn-primary"],
body.dark-theme .offcanvas .btn[type="submit"],
body.dark-theme .offcanvas button[type="submit"] {
  background: var(--ocean-teal) !important;
}

body.dark-theme .offcanvas .btn-primary:hover,
body.dark-theme .offcanvas [class*="btn-primary"]:hover,
body.dark-theme .offcanvas .btn[type="submit"]:hover,
body.dark-theme .offcanvas button[type="submit"]:hover {
  background: var(--forest-green) !important;
}

/* Section Header */
.section-header {
  font-size: var(--font-size-md);
  font-weight: 600;
  text-transform: capitalize;
}

/* Navigation and Controls */
.nav-item {
  font-size: var(--font-size-base);
  text-transform: capitalize;
}

.toolbar-button {
  font-size: var(--font-size-base);
  text-transform: capitalize;
}

.dropdown-item {
  font-size: var(--font-size-base);
  text-transform: capitalize;
}

/* Widget Content */
.widget-content {
  font-size: var(--font-size-base);
}

.chart-title {
  font-size: var(--font-size-md);
  font-weight: 500;
  text-transform: capitalize;
}

.chart-label {
  font-size: var(--font-size-sm);
}

.chart-value {
  font-size: var(--font-size-base);
}

/* Form Elements */
input,
select,
textarea {
  font-size: var(--font-size-base);
  font-family: "Montserrat", sans-serif;
}

.form-label {
  font-size: var(--font-size-base);
  text-transform: capitalize;
}

/* Status and Metadata */
.status-indicator {
  font-size: var(--font-size-sm);
  text-transform: capitalize;
}

.metadata {
  font-size: var(--font-size-sm);
}

/* Tooltips and Help Text */
.tooltip {
  font-size: var(--font-size-sm);
}

.help-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* Special styling for nested widgets inside section containers */
.section-content .grid-stack-item .widget-header {
  display: flex;
  justify-content: flex-end;
  background: transparent;
  padding: 0.25rem;
  margin: 0;
  border: none;
  position: absolute;
  top: 0;
  right: 0;
  width: auto;
  z-index: 10;
}

.section-content .grid-stack-item .widget-header::after {
  display: none;
}

.section-content .grid-stack-item .widget-header > div:first-child {
  display: none;
  pointer-events: none;
}

.section-content .grid-stack-item .widget-header > div:last-child,
.section-content .grid-stack-item .widget-icons {
  display: flex;
  margin: 0;
  padding: 0;
}

.section-content .grid-stack-item .widget-icon {
  background-color: rgba(255, 255, 255, 0.8);
  width: 24px;
  height: 24px;
  margin-left: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.section-content .grid-stack-item .widget-icon:hover {
  background-color: var(--ocean-teal);
  color: white;
}

.section-content .grid-stack-item .widget-icon i {
  font-size: 14px;
}

/* Add a slight shadow to make icons more visible against any background */
.section-content .grid-stack-item .widget-icons {
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.2));
}

/* Dark theme adjustments */
body.dark-theme .section-content .grid-stack-item .widget-icon {
  background-color: rgba(40, 40, 40, 0.8);
  color: rgba(255, 255, 255, 0.9);
}

body.dark-theme .section-content .grid-stack-item .widget-icon:hover {
  background-color: var(--ocean-teal);
  color: white;
}

/* Make sure drag handles still work correctly */
.section-content .grid-stack-item .widget-header {
  pointer-events: all;
}

.section-content .grid-stack-item .widget-icon {
  pointer-events: all;
}

/* Ensure widgets inside sections don't have double padding */
.section-content .grid-stack-item-content {
  padding: 0;
}

/* Preserve draggable behavior for widgets inside sections */
.section-content .grid-stack > .grid-stack-item > .grid-stack-item-content {
  cursor: grab;
}

.section-content
  .grid-stack
  > .grid-stack-item.ui-draggable-dragging
  > .grid-stack-item-content {
  cursor: grabbing;
}

/* Improve section container drag-and-drop behavior */
body.widget-dragging .section-content .grid-stack {
  background-color: rgba(0, 177, 156, 0.1);
  border: 2px dashed rgba(0, 177, 156, 0.4) !important;
  min-height: 100px; /* Ensure there's always space to drop */
}

/* Make drop indicators more visible */
.grid-stack-item.ui-draggable-dragging {
  z-index: 999 !important; /* Ensure dragged items are always on top */
}

.grid-stack-placeholder {
  z-index: 998 !important; /* Ensure placeholders are visible but below dragged items */
}

.section-content .grid-stack-placeholder > .placeholder-content {
  background: rgba(0, 177, 156, 0.15) !important;
  border: 2px dashed rgba(0, 177, 156, 0.4) !important;
  z-index: 5 !important;
}

/* Enhanced dropzone visual indicators */
.grid-stack-nested.active-dropzone {
  background-color: rgba(0, 177, 156, 0.2) !important;
  border: 2px dashed rgba(0, 177, 156, 0.6) !important;
  transition: all 0.2s ease;
}

.grid-stack-nested.grid-stack-dropzone:empty::after {
  content: "Drop widgets here";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(0, 0, 0, 0.3);
  font-size: 14px;
  white-space: nowrap;
  pointer-events: none;
}

.grid-stack-item-nested {
  z-index: 10; /* Ensure nested items stay above the nested grid but below dragged items */
}

/* Dark Theme - Nested Grid Resize Handles */
body.dark-theme
  .nested-grid
  .grid-stack
  > .grid-stack-item
  > .ui-resizable-handle,
body.dark-theme
  .tab-container-widget
  .grid-stack
  > .grid-stack-item
  > .ui-resizable-handle,
body.dark-theme
  .tab-gridstack-container
  .grid-stack
  > .grid-stack-item
  > .ui-resizable-handle {
  background: #1e293b;
  border-color: #475569;
}

body.dark-theme .grid-stack > .grid-stack-item > .ui-resizable-handle:hover {
  background: var(--ocean-teal);
  border-color: var(--ocean-teal);
}

/* Dark Theme - Nested Grid Handle Hover */
body.dark-theme
  .nested-grid
  .grid-stack
  > .grid-stack-item
  > .ui-resizable-handle:hover,
body.dark-theme
  .tab-container-widget
  .grid-stack
  > .grid-stack-item
  > .ui-resizable-handle:hover,
body.dark-theme
  .tab-gridstack-container
  .grid-stack
  > .grid-stack-item
  > .ui-resizable-handle:hover {
  background: var(--ocean-teal);
  border-color: var(--ocean-teal);
}
