/* Updated header styles */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1.5rem;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  height: 64px;
  border-bottom: 2px solid #00b19c;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-logo {
  height: 32px;
  width: auto;
}

.divider {
  width: 1px;
  height: 24px;
  background: #e0e0e0;
  margin: 0 1rem;
}

.logo-section h1 {
  font-size: 1.25rem;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.notifications-btn {
  position: relative;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  padding: 0.25rem;
  font-size: 0.75rem;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #fff;
}

.user-menu {
  display: flex;
  align-items: center;
}

.user-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.user-avatar {
  width: 36px;
  height: 36px;
  background: #02104f;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.user-info {
  text-align: left;
}

.user-name {
  display: block;
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.user-role {
  display: block;
  color: #666;
  font-size: 0.8rem;
}

.hexa-logo img {
  height: 32px;
  width: auto;
}

/* Updated button styles */
.dashboard-actions {
  display: flex;
  gap: 0.75rem;
  margin-left: 1rem;
}

.dashboard-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 0;
  background: white;
  color: #666;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.dashboard-btn:hover {
  background: #f8f9fa;
  border-color: #ddd;
}

.dashboard-btn i {
  font-size: 1rem;
}

/* Dark theme modifications */
.dark-theme .app-header {
  background: #1a1a1a;
  border-bottom-color: #00b19c;
}

.dark-theme .logo-section h1,
.dark-theme .user-name {
  color: #fff;
}

.dark-theme .user-role,
.dark-theme .notifications-btn {
  color: #999;
}

.dark-theme .divider {
  background: #333;
}

.dark-theme .dashboard-btn {
  background: #2d2d2d;
  border-color: #333;
  color: #fff;
}

.dark-theme .dashboard-btn:hover {
  background: #363636;
  border-color: #404040;
}

.grid-stack {
  background: #fff;
  border-radius: 0;
  border: none;
  margin: 0 !important;
  padding: 0 !important;
}

.grid-stack-item-content {
  color: #fff;
  text-align: left;
  background-color: #02104f;
  border-radius: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
#tsc_nav_1 .nav-link {
  padding: 0.7rem;
  font-size: 12px;
  display: block;
  padding: 0.4rem 0.5rem;
}
.widget-gallery {
  overflow: hidden !important;
}
button.widget-action-btn.widget-preview-btn {
  display: none !important;
}
.widget-section {
  padding-left: 5px;
  padding-right: 5px;
  margin-top: 62px !important;
}

.grid-stack .grid-stack .grid-stack-item-content {
  background: #6610f2;
}

/* Settings and close icon styles */
.widget-header mb-2 .btn-link {
  padding: 0;
  color: #333;
  text-decoration: none;
}

.widget-header mb-2 .btn-link:hover {
  color: #02104f;
}

.widget-header mb-2 .la-cog,
.widget-header mb-2 .la-times {
  font-size: 1.1rem;
}

.widget-header mb-2 .la-times:hover {
  color: #dc3545;
}

.sidebar-item {
  padding: 15px;
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0;
  cursor: move;
  text-align: center;
  width: 150px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.sidebar-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
  background-color: #02104f;
  color: white;
  border-bottom: none;
}

/* Style for selected nodes */
.selected-node {
  box-shadow: 0 0 0 3px #ffc107 !important;
  /* Yellow highlight */
  position: relative;
  z-index: 10;
}

.selected-node::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 2px dashed #ffc107;
  pointer-events: none;
  border-radius: 0;
}

/* Pie Chart Widget Styles */
.pie-chart-widget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* TSC2.0 Widget Styles */
.stack-chart-widget,
.word-map-widget,
.dual-axis-widget,
.line-threshold-widget {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chart-container {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 200px;
  position: relative;
  overflow: visible !important;
  /* Allow zoom controls to be visible */
}

/* Handsontable container styles */
.spreadsheet-container {
  flex: 1 1 auto !important;
  width: 100% !important;
  height: 100% !important;
  min-height: 200px !important;
  position: relative !important;
}

/* Fix for Handsontable rendering */
.handsontable {
  width: 100% !important;
  height: 100% !important;
}

/* Layout adjustments for main content */
body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.widget-section {
  padding: 1rem;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.container-fluid {
  flex: 1;
  padding-top: 1rem;
  padding-bottom: 2rem;
}

/* Widget gallery improvements */
.widget-categories {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 0.5rem;
  border-bottom: 1px solid #e0e0e0;
  justify-content: space-between;
  /* Added to separate left and right content */
}

/* New styles for left categories group */
.widget-categories-left {
  display: flex;
  gap: 1rem;
}

.widget-category {
  padding: 0.5rem 1rem;
  cursor: pointer;
  border-radius: 0px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  transition: all 0.2s ease;
}

/* New styles for Section Gallery button */
.section-gallery-btn {
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, #00b19c 0%, #00897b 100%);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 115, 101, 0.2);
  font-weight: 500;
  position: relative;
  overflow: hidden;
  height: 36px;
  font-size: 0.875rem;
}

.section-gallery-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.section-gallery-btn:hover {
  background: linear-gradient(135deg, #00635a 0%, #00796b 100%);
  box-shadow: 0 3px 6px rgba(0, 115, 101, 0.3);
  transform: translateY(-1px);
}

.section-gallery-btn:hover::before {
  opacity: 1;
}

.section-gallery-btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 115, 101, 0.2);
}

.section-gallery-btn i {
  font-size: 1rem;
}

.section-gallery-btn .btn-text {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.35rem;
}

.section-gallery-btn .btn-text::after {
  content: "";
  display: block;
  width: 1px;
  height: 14px;
  background: rgba(255, 255, 255, 0.3);
  margin-left: 0.35rem;
}

.dark-theme .section-gallery-btn {
  background: linear-gradient(135deg, #4db6ac 0%, #26a69a 100%);
  color: #1a1a1a;
  box-shadow: 0 2px 4px rgba(77, 182, 172, 0.2);
}

.dark-theme .section-gallery-btn:hover {
  background: linear-gradient(135deg, #5dc6bc 0%, #2bbbad 100%);
  box-shadow: 0 4px 8px rgba(77, 182, 172, 0.3);
}

.dark-theme .section-gallery-btn .btn-text::after {
  background: rgba(0, 0, 0, 0.2);
}

.widget-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  padding: 0.5rem;
}

.widget-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.widget-label {
  font-size: 0.9rem;
  color: #333;
}

/* Dark theme adjustments for main content */
.dark-theme {
  background: #121212;
}

.dark-theme .widget-section {
  background: #1a1a1a;
  border-color: #333;
}

.dark-theme .widget-categories {
  border-color: #333;
}

.dark-theme .widget-category {
  color: #999;
}

.dark-theme .widget-item {
  background: #1a1a1a;
  border-color: #333;
}

.dark-theme .widget-label {
  color: #fff;
}

.dark-theme .card {
  background: #1a1a1a;
  border-color: #333;
}

.dark-theme .card-header {
  background: #2d2d2d;
  border-color: #333;
}

.dark-theme .grid-stack {
  background: #1a1a1a;
  border-color: #333;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1.5rem;
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
  height: 90px;
  border-bottom: 3px solid #00b19c;
}

/* Updated button styles */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-right: 1.5rem;
  height: 40px;
}

.btn-group {
  display: flex;
  gap: 2px;
  background: #f5f5f5;
  padding: 2px;
  height: 100%;
}

.btn-action {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0 1.25rem;
  height: 100%;
  border: none;
  background: transparent;
  color: #555;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.btn-action i {
  font-size: 1.1rem;
}

.btn-action:hover {
  background: #fff;
  color: #00b19c;
}

.btn-action.active {
  background: #fff;
  color: #00b19c;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-primary {
  background: #00b19c;
  color: white;
}

.btn-primary:hover {
  background: #005a4f;
  color: white;
}

.btn-action .btn-tooltip {
  position: absolute;
  bottom: -32px;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}

.btn-action:hover .btn-tooltip {
  opacity: 1;
  visibility: visible;
}

/* Dark theme adjustments */
.dark-theme .btn-group {
  background: #2d2d2d;
}

.dark-theme .btn-action {
  color: #999;
}

.dark-theme .btn-action:hover {
  background: #363636;
  color: #4db6ac;
}

.dark-theme .btn-action.active {
  background: #363636;
  color: #4db6ac;
}

.dark-theme .btn-primary {
  background: #00b19c;
  color: white;
}

.dark-theme .btn-primary:hover {
  background: #005a4f;
  color: white;
}

/* Status indicator */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0 1rem;
  font-size: 0.875rem;
  color: #666;
  border-left: 1px solid #e0e0e0;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ffd700;
}

.dark-theme .status-indicator {
  color: #999;
  border-left-color: #333;
}

/* Improved action buttons container */
.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Icon buttons container */
.header-icons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

/* Status badge styling */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.4rem 0.8rem;
  background: rgba(0, 115, 101, 0.1);
  border: 1px solid #00b19c;
  font-size: 0.875rem;
  color: #00b19c;
  font-weight: 500;
}

.status-badge .status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-badge.draft .status-dot {
  background: #00b19c;
}

/* Button group styling */
.header-btn-group {
  display: flex;
  gap: 0.75rem;
}

/* Common button styles */
.header-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  height: 38px;
  min-width: 38px;
}

/* Secondary button style */
.header-btn.secondary {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #00b19c;
}

.header-btn.secondary:hover {
  background: rgba(0, 115, 101, 0.1);
  border-color: #00b19c;
  color: #00b19c;
}

/* Primary button style */
.header-btn.primary {
  background: #00b19c;
  color: white;
}

.header-btn.primary:hover {
  background: #005a4f;
}

/* Icon button style */
.header-btn.icon-btn {
  padding: 0.5rem;
  background: transparent;
  position: relative;
  width: 38px;
}

.header-btn.icon-btn:hover {
  background: rgba(0, 115, 101, 0.1);
}

/* Button icons */
.header-btn i {
  font-size: 1.25rem;
}

/* Dark theme adjustments */
.dark-theme .status-badge {
  background: rgba(0, 115, 101, 0.2);
  border-color: #00b19c;
  color: #4db6ac;
}

.dark-theme .header-btn.secondary {
  background: #2d2d2d;
  border-color: #00b19c;
  color: #e9ecef;
}

.dark-theme .header-btn.secondary:hover {
  background: rgba(0, 115, 101, 0.2);
  border-color: #00b19c;
  color: #4db6ac;
}

.dark-theme .header-btn.icon-btn:hover {
  background: rgba(0, 115, 101, 0.2);
}

.dark-theme .notification-badge {
  border-color: #1a1a1a;
}

/* Section Gallery Offcanvas Styles - Redesigned */
.section-gallery-offcanvas {
  width: 540px !important;
  border-left: 1px solid rgba(0, 115, 101, 0.1);
  background: #ffffff;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.section-gallery-header {
  background: linear-gradient(135deg, #00b19c 0%, #00635a 100%);
  padding: 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.section-gallery-title {
  font-size: 1rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-gallery-search {
  position: relative;
}

.section-gallery-search input {
  width: 100%;
  padding: 0.625rem 2.25rem 0.625rem 2.25rem;
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  font-size: 0.875rem;
}

.section-gallery-search input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.section-gallery-search i {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.6);
  font-size: 1rem;
}

.section-gallery-tabs {
  display: flex;
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  gap: 0.5rem;
  overflow-x: auto;
  flex-shrink: 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.section-gallery-tabs::-webkit-scrollbar {
  display: none;
}

.section-gallery-tab {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  color: #64748b;
  cursor: pointer;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.section-gallery-tab:hover {
  background: rgba(0, 115, 101, 0.08);
  color: #00b19c;
}

.section-gallery-tab.active {
  background: rgba(0, 115, 101, 0.12);
  color: #00b19c;
  font-weight: 500;
}

.section-gallery-body {
  padding: 1rem;
  background: #f8f9fa;
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 115, 101, 0.3) transparent;
}

.section-gallery-body::-webkit-scrollbar {
  width: 6px;
}

.section-gallery-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 115, 101, 0.3);
}

.section-templates-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

.section-template {
  background: #fff;
  border: 1px solid #e2e8f0;
  position: relative;
  transition: all 0.2s ease;
  cursor: pointer;
}

.section-template:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: rgba(0, 115, 101, 0.3);
}

.template-header {
  padding: 1rem;
  background: linear-gradient(
    to right,
    rgba(0, 115, 101, 0.04),
    rgba(0, 115, 101, 0.08)
  );
  border-bottom: 1px solid #e8e8e8;
}

.template-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.template-content {
  padding: 1rem;
}

.template-description {
  font-size: 0.813rem;
  color: #64748b;
  margin-bottom: 1rem;
  line-height: 1.5;
  padding: 1.5rem;
}

.widget-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.widget-item {
  padding: 0.5rem 0.75rem;
  background: #f8f9fa;
  border: 1px solid #e2e8f0;
  font-size: 0.813rem;
  color: #475569;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
}

.widget-item:hover {
  background: rgba(0, 115, 101, 0.04);
  border-color: rgba(0, 115, 101, 0.2);
}

.widget-item i {
  color: #231f20;
  font-size: 1rem;
}

.template-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  padding: 0.25rem 0.5rem;
  background: rgba(0, 115, 101, 0.1);
  color: #00b19c;
  font-size: 0.75rem;
  font-weight: 500;
  z-index: 1;
}

.template-stats {
  display: flex;
  gap: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e2e8f0;
  margin-top: 0.5rem;
}

.template-stat {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  color: #64748b;
}

.template-stat i {
  font-size: 0.875rem;
  color: #00b19c;
}

/* Dark theme adjustments */
.dark-theme .section-gallery-offcanvas {
  background: #1a1a1a;
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .section-gallery-tabs {
  background: #2d2d2d;
  border-color: #333;
}

.dark-theme .section-gallery-tab {
  color: #a0aec0;
}

.dark-theme .section-gallery-tab:hover {
  background: rgba(77, 182, 172, 0.1);
  color: #4db6ac;
}

.dark-theme .section-gallery-tab.active {
  background: rgba(77, 182, 172, 0.15);
  color: #4db6ac;
}

.dark-theme .section-gallery-body {
  background: #1a1a1a;
}

.dark-theme .section-template {
  background: #2d2d2d;
  border-color: #333;
}

.dark-theme .section-template:hover {
  border-color: rgba(77, 182, 172, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.dark-theme .template-header {
  background: linear-gradient(
    to right,
    rgba(77, 182, 172, 0.05),
    rgba(77, 182, 172, 0.1)
  );
  border-color: #333;
}

.dark-theme .template-title {
  color: #e2e8f0;
}

.dark-theme .template-description {
  color: #a0aec0;
}

.dark-theme .widget-item {
  background: #2d2d2d;
  border-color: #333;
  color: #e2e8f0;
}

.dark-theme .widget-item:hover {
  background: rgba(77, 182, 172, 0.1);
  border-color: rgba(77, 182, 172, 0.2);
}

.dark-theme .widget-item i {
  color: #4db6ac;
}

.dark-theme .template-badge {
  background: rgba(77, 182, 172, 0.15);
  color: #4db6ac;
}

.dark-theme .template-stats {
  border-color: #333;
}

.dark-theme .template-stat {
  color: #a0aec0;
}

.dark-theme .template-stat i {
  color: #4db6ac;
}

/* Preview Mode Styles */
.preview-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: auto;
  background: #fff;
  z-index: 9999;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Ensure modal backdrops appear above preview mode */
.modal-backdrop {
  z-index: 99;
  /* Same as preview mode */
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border-bottom: 2px solid #00b19c;
  height: 70px;
}

.preview-title h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.preview-subtitle {
  font-size: 0.875rem;
  color: #666;
}

.preview-actions {
  display: flex;
  gap: 0.75rem;
}

.preview-actions button {
  display: inline-flex !important;
  align-items: center;
  gap: 0.5rem;
  z-index: 10000;
}

.preview-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

/* Ensure proper spacing in both preview mode and regular mode */

.grid-stack-item {
  padding: 5px !important;
}

.grid-stack-item-content {
  margin: 5px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden !important;
}

/* Additional spacing for preview mode */
#preview-container .grid-stack {
  /* margin: 10px !important; */
}

#preview-container .grid-stack-item {
  padding: 5px !important;
}

#preview-container .grid-stack-item-content {
  margin: 5px !important;
}

/* Section Gallery Offcanvas */
.section-gallery-offcanvas {
  width: 600px;
  max-width: 90vw;
  z-index: 1050 !important;
  /* Higher than default Bootstrap offcanvas z-index */
}

/* Ensure the offcanvas backdrop is properly z-indexed */
.offcanvas-backdrop {
  z-index: 1040 !important;
  /* Just below the offcanvas itself */
}

/* Dark theme adjustments for preview mode */
.dark-theme .preview-mode {
  background: #121212;
}

.dark-theme .preview-header {
  background: #1a1a1a;
  border-color: #00b19c;
}

.dark-theme .preview-title h3 {
  color: #fff;
}

.dark-theme .preview-subtitle {
  color: #999;
}

/* Feedback Modal Styles */
#feedback-modal {
  z-index: 10000;
  /* Higher than preview mode z-index */
}

#feedback-modal .modal-content {
  border-radius: 0;
  border: none;
}

#feedback-modal .modal-header {
  background: #f8f9fa;
  border-bottom: 2px solid #00b19c;
}

#feedback-modal .modal-title {
  color: #333;
  font-weight: 500;
}

#feedback-modal .btn-primary {
  background: #00b19c;
  border-color: #00b19c;
}

#feedback-modal .btn-primary:hover {
  background: #005a4f;
  border-color: #005a4f;
}

/* Dark theme adjustments for feedback modal */
.dark-theme #feedback-modal .modal-content {
  background: #1a1a1a;
  color: #fff;
}

.dark-theme #feedback-modal .modal-header {
  background: #2d2d2d;
  border-color: #00b19c;
}

.dark-theme #feedback-modal .modal-title {
  color: #fff;
}

.dark-theme #feedback-modal .form-control,
.dark-theme #feedback-modal .form-select {
  background: #2d2d2d;
  border-color: #404040;
  color: #fff;
}

/* Widget Comment Styles */
.widget-chat-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 32px;
  height: 32px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  background: rgba(0, 115, 101, 0.1);
  color: #00b19c;
  border-radius: 50%;
  cursor: pointer;
  z-index: 10000;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 115, 101, 0.2);
  pointer-events: auto !important;
}

.widget-chat-icon:hover {
  background: rgba(0, 115, 101, 0.2);
  transform: scale(1.05);
}

.widget-chat-icon i {
  font-size: 18px;
}

.widget-header mb-2 {
  cursor: pointer;
  position: relative;
}

.widget-header:hover {
  background: rgba(0, 115, 101, 0.05);
}

/* Professional Comment Panel */
#widget-comment-panel {
  width: 380px;
  z-index: 10001;
  /* Higher than preview mode (9999) and other elements (10000) */
  border: none;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  border-left: 1px solid #e2e8f0;
}

/* Ensure the offcanvas backdrop is also above preview mode */
.offcanvas-backdrop {
  z-index: 10000;
  /* Same as other high z-index elements */
}

/* Comment Panel Structure */
.comment-panel-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}

/* Comment Panel Header */
.comment-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
}

.comment-panel-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.comment-panel-icon {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  color: #333333;
}

.comment-panel-icon i {
  font-size: 16px;
}

.comment-panel-title h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333333;
}

.comment-panel-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.comment-panel-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #718096;
}

.status-indicator {
  width: 8px;
  height: 8px;
  background: #38b2ac;
  border-radius: 8px;
}

.comment-panel-close {
  background: none;
  border: none;
  color: #718096;
  cursor: pointer;
  padding: 4px;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.comment-panel-close:hover {
  color: #e53e3e;
}

/* Comment Panel Content */
.comment-panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* Widget Info */
.widget-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background: #f7fafc;
}

.widget-info-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  color: #333333;
  font-size: 16px;
}

.widget-info-details {
  flex: 1;
}

.widget-info-title {
  margin: 0 0 4px 0;
  font-size: 13px;
  font-weight: 600;
  color: #333333;
}

.widget-info-type {
  margin: 0;
  font-size: 12px;
  color: #666666;
}

/* Comment Thread */
.comment-thread {
  padding: 20px;
}

.no-comments-message {
  text-align: center;
  padding: 40px 20px;
  color: #a0aec0;
}

.no-comments-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #cbd5e0;
}

.no-comments-message h6 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #666666;
}

.no-comments-message p {
  font-size: 12px;
  margin: 0;
}

/* Comment Item */
.comment-item {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.comment-avatar {
  width: 28px;
  height: 28px;
  background: #666666;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.comment-author {
  font-size: 13px;
  font-weight: 600;
  color: #333333;
}

.comment-time {
  font-size: 11px;
  color: #666666;
}

.comment-text {
  font-size: 12px;
  line-height: 1.5;
  color: #333333;
  background: #f7f7f7;
  padding: 12px 16px;
  border-left: 2px solid #e0e0e0;
}

/* Comment Panel Footer */
.comment-panel-footer {
  border-top: 1px solid #e2e8f0;
  padding: 16px 20px;
}

.comment-form {
  display: flex;
  gap: 12px;
}

.comment-form-avatar {
  flex-shrink: 0;
}

.comment-form-avatar .user-avatar {
  width: 28px;
  height: 28px;
  background: #666666;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.comment-form-input {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.comment-form-input textarea {
  width: 100%;
  min-height: 80px;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  resize: none;
  font-size: 12px;
  color: #333333;
  background: #f7f7f7;
  transition: border-color 0.2s ease;
}

.comment-form-input textarea:focus {
  outline: none;
  border-color: #666666;
}

.comment-form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.comment-form-tools {
  display: none;
  /* Hide formatting tools as requested */
}

.comment-submit {
  background: #333333;
  color: white;
  border: none;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease;
  margin-left: auto;
  /* Push to right side */
}

.comment-submit:hover {
  background: #555555;
}

/* Dark Theme Support */
.dark-theme #widget-comment-panel {
  background: #222222;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
  border-left: 1px solid #333333;
}

.dark-theme .comment-panel-wrapper {
  background: #222222;
}

.dark-theme .comment-panel-header {
  border-color: #333333;
}

.dark-theme .comment-panel-title h5 {
  color: #f0f0f0;
}

.dark-theme .comment-panel-icon {
  background: #333333;
  color: #f0f0f0;
}

.dark-theme .comment-panel-status {
  color: #a0aec0;
}

.dark-theme .comment-panel-close {
  color: #a0aec0;
}

.dark-theme .widget-info {
  background: #2a2a2a;
  border-color: #333333;
}

.dark-theme .widget-info-icon {
  background: #333333;
  color: #f0f0f0;
}

.dark-theme .widget-info-title {
  color: #f0f0f0;
}

.dark-theme .widget-info-type {
  color: #cccccc;
}

.dark-theme .no-comments-message {
  color: #718096;
}

.dark-theme .no-comments-icon {
  color: #4a5568;
}

.dark-theme .no-comments-message h6 {
  color: #a0aec0;
}

.dark-theme .comment-author {
  color: #f0f0f0;
}

.dark-theme .comment-time {
  color: #cccccc;
}

.dark-theme .comment-text {
  color: #f0f0f0;
  background: #2a2a2a;
  border-left: 2px solid #444444;
}

.dark-theme .comment-panel-footer {
  border-color: #2d3748;
}

.dark-theme .comment-form-input textarea {
  background: #2a2a2a;
  border-color: #444444;
  color: #f0f0f0;
}

.dark-theme .comment-form-input textarea:focus {
  border-color: #666666;
}

.dark-theme .comment-submit {
  background: #444444;
}

.dark-theme .comment-submit:hover {
  background: #555555;
}

/* Comment Notification */
.comment-notification {
  position: fixed;
  bottom: 24px;
  right: 24px;
  background: #333333;
  color: white;
  padding: 12px 20px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  font-size: 12px;
  z-index: 10002;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.comment-notification.show {
  opacity: 1;
  transform: translateY(0);
}

.dark-theme .comment-notification {
  background: #444444;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.dark-theme .widget-chat-icon {
  background: rgba(0, 115, 101, 0.2);
  color: #4db6ac;
  border-color: rgba(0, 115, 101, 0.3);
}

.dark-theme .widget-chat-icon:hover {
  background: rgba(0, 115, 101, 0.3);
}

.dark-theme .widget-header:hover {
  background: rgba(0, 115, 101, 0.1);
}

/* Notification Modal Styles */
#notification-modal,
#confirmation-modal {
  z-index: 10000;
  /* Higher than preview mode z-index */
}

#notification-modal .modal-content,
#confirmation-modal .modal-content {
  border-radius: 0px;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

#notification-modal .modal-header,
#confirmation-modal .modal-header {
  background: #f8f9fa;
  border-bottom: 2px solid #00b19c;
  padding: 1rem 1.5rem;
}

#notification-modal .modal-title,
#confirmation-modal .modal-title {
  color: #333;
  font-weight: 600;
  font-size: 1.25rem;
}

#notification-modal .modal-body,
#confirmation-modal .modal-body {
  padding: 2rem;
}

#notification-modal .notification-icon i,
#confirmation-modal .confirmation-icon i {
  color: #00b19c;
}

#notification-modal.success .notification-icon i {
  color: #28a745;
}

#notification-modal.warning .notification-icon i {
  color: #ffc107;
}

#notification-modal.error .notification-icon i {
  color: #dc3545;
}

#notification-modal .modal-footer,
#confirmation-modal .modal-footer {
  border-top: none;
  padding: 0 1.5rem 1.5rem;
  justify-content: center;
}

#notification-modal .btn-primary,
#confirmation-modal .btn-primary {
  background: #00b19c;
  border-color: #00b19c;
  min-width: 100px;
}

#notification-modal .btn-primary:hover,
#confirmation-modal .btn-primary:hover {
  background: #005a4f;
  border-color: #005a4f;
}

/* Dark theme adjustments for notification modals */
.dark-theme #notification-modal .modal-content,
.dark-theme #confirmation-modal .modal-content {
  background: #1a1a1a;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dark-theme #notification-modal .modal-header,
.dark-theme #confirmation-modal .modal-header {
  background: #2d2d2d;
  border-color: #00b19c;
}

.dark-theme #notification-modal .modal-title,
.dark-theme #confirmation-modal .modal-title {
  color: #fff;
}

.dark-theme #notification-modal .notification-message,
.dark-theme #confirmation-modal .confirmation-message {
  color: #e2e8f0;
}

.dark-theme #notification-modal.success .notification-icon i {
  color: #48bb78;
}

.dark-theme #notification-modal.warning .notification-icon i {
  color: #ecc94b;
}

.dark-theme #notification-modal.error .notification-icon i {
  color: #f56565;
}

/* Search Results Styles */
.search-results {
  background: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 0px;
  padding: 0.75rem;
  font-size: 0.813rem;
}

.search-results-header {
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.search-results-body {
  max-height: 200px;
  overflow-y: auto;
}

.search-result-item {
  padding: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background: rgba(0, 115, 101, 0.05);
}

.search-result-item .result-title {
  font-weight: 500;
  color: #333;
}

.search-result-item .result-source {
  font-size: 0.75rem;
  color: #64748b;
}

.search-result-item .result-action {
  color: #00b19c;
}

.dark-theme .search-results {
  background: #2d2d2d;
  border-color: #333;
}

.dark-theme .search-results-header {
  border-color: #333;
}

.dark-theme .search-result-item {
  border-color: #333;
}

.dark-theme .search-result-item:hover {
  background: rgba(77, 182, 172, 0.1);
}

.dark-theme .search-result-item .result-title {
  color: #e2e8f0;
}

.dark-theme .search-result-item .result-source {
  color: #a0aec0;
}

.dark-theme .search-result-item .result-action {
  color: #4db6ac;
}

.chart-container {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 200px;
  position: relative;
  overflow: visible !important;
  /* Allow zoom controls to be visible */
}

.grid-stack-item-content {
  overflow: visible !important;
  /* Allow chart container to overflow for zoom controls */
  height: 100%;
  display: flex;
  flex-direction: column;
}

.widget-body {
  flex: 1;
  min-height: 0;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: visible !important;
  /* Allow chart container to overflow */
}

[data-bs-theme="dark"] .widget-container {
  background: #22262a;
  border-color: rgba(255, 255, 255, 0.1);
}

/* Smart Widget Composer Styles */
.composer-container {
  padding: 1rem 0;
}

.composer-section {
  margin-bottom: 2rem;
}

.composer-section-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.template-card {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #fff;
}

.template-card:hover {
  border-color: #00b19c;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.template-card.selected {
  border-color: #00b19c;
  background: #f8f9ff;
}

.template-preview {
  height: 80px;
  margin-bottom: 0.75rem;
  border-radius: 4px;
  background: #f8f9fa;
  padding: 0.5rem;
}

.preview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 4px;
  height: 100%;
}

.preview-item {
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-item.chart {
  background: linear-gradient(45deg, #00b19c, #0056b3);
}

.preview-item.kpi {
  background: linear-gradient(45deg, #28a745, #1e7e34);
}

.preview-item.table {
  background: linear-gradient(45deg, #6c757d, #495057);
}

.preview-item.text {
  background: linear-gradient(45deg, #17a2b8, #117a8b);
}

.preview-item.large {
  grid-column: span 2;
}

.preview-item.empty {
  background: #e9ecef;
  border: 2px dashed #adb5bd;
  color: #6c757d;
  font-size: 1.5rem;
}

.template-info h6 {
  margin: 0 0 0.25rem 0;
  font-weight: 600;
  color: #2c3e50;
}

.template-info p {
  margin: 0;
  font-size: 0.875rem;
  color: #6c757d;
}

.widget-customization {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
  margin-top: 1rem;
}

.selected-template-preview {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  background: #f8f9fa;
}

.template-layout {
  min-height: 200px;
  display: grid;
  gap: 8px;
  padding: 1rem;
  background: #fff;
  border-radius: 4px;
}

.widget-options {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.widget-category-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.widget-tab {
  flex: 1;
  padding: 0.75rem 0.5rem;
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.widget-tab:hover {
  background: #e9ecef;
  color: #495057;
}

.widget-tab.active {
  background: #00b19c;
  color: #fff;
}

.widget-list {
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
}

.widget-options-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.widget-option {
  padding: 0.75rem;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  text-align: center;
  background: #fff;
}

.widget-option:hover {
  border-color: #00b19c;
  background: #f8f9ff;
}

.widget-option.selected {
  border-color: #00b19c;
  background: #00b19c;
  color: #fff;
}

.widget-option i {
  font-size: 1.5rem;
}

.widget-option span {
  font-size: 0.875rem;
  font-weight: 500;
}

.configuration-form {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* Dark theme adjustments for Smart Widget Composer */
[data-bs-theme="dark"] .composer-section-title {
  color: #e9ecef;
}

[data-bs-theme="dark"] .template-card {
  background: #22262a;
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .template-card:hover {
  border-color: #02104f;
  background: #1a1d21;
}

[data-bs-theme="dark"] .template-card.selected {
  border-color: #02104f;
  background: #1a1d21;
}

[data-bs-theme="dark"] .template-preview {
  background: #1a1d21;
}

[data-bs-theme="dark"] .template-info h6 {
  color: #e9ecef;
}

[data-bs-theme="dark"] .template-info p {
  color: #adb5bd;
}

[data-bs-theme="dark"] .selected-template-preview {
  background: #1a1d21;
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .template-layout {
  background: #22262a;
}

[data-bs-theme="dark"] .widget-options {
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .widget-category-tabs {
  background: #1a1d21;
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .widget-tab {
  color: #adb5bd;
}

[data-bs-theme="dark"] .widget-tab:hover {
  background: #22262a;
  color: #e9ecef;
}

[data-bs-theme="dark"] .widget-tab.active {
  background: #02104f;
  color: #fff;
}

[data-bs-theme="dark"] .widget-option {
  background: #22262a;
  border-color: rgba(255, 255, 255, 0.1);
  color: #e9ecef;
}

[data-bs-theme="dark"] .widget-option:hover {
  border-color: #02104f;
  background: #1a1d21;
}

[data-bs-theme="dark"] .widget-option.selected {
  border-color: #02104f;
  background: #02104f;
  color: #fff;
}

[data-bs-theme="dark"] .configuration-form {
  background: #1a1d21;
  border-color: rgba(255, 255, 255, 0.1);
}
.themeBorderBottom {
  padding: 0.5rem 1rem;
}
.themeBorderBottom .nav-link,
.themeBorderBottom .nav-link:hover {
  color: #231f20;
}
.footer {
  font-size: 12px;
  position: relative;
  width: 100%;
  background: #02104f;
  border-top: 1px solid #02104f;
  padding: 8px 0;
  color: white;
  flex: 0;
}
.footer a {
  font-size: 12px;
  color: #fff;
}
.widget-categories {
  margin-bottom: 5px;
}
.widget-section {
  padding-top: 5px;
  padding-bottom: 5px;
}
.widget-section {
  min-height: 120px !important;
}
.widget-section {
  padding-left: 5px;
  padding-right: 5px;
}

nav#tsc_nav_1 {
  border-bottom: 3px solid #00b19c;
  margin-bottom: 5px;
  min-height: 50px;
}
.grid-stack {
  background: #f8f9fab8;
  border-radius: 0;
  border: none;
  margin: 0 !important;
  padding: 0 !important;
  border: 1px solid #cccccc30;
}
.widget-item {
  min-height: 60px !important;
}
a.focus-icon-link {
  display: none;
}
.card-body header {
  padding-left: 10px !important;
  padding-right: 10px !important;
}
.card.h-100,
.card.h-100 .card-body {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: none;
}

.table-loader th {
  padding: 0 !important;
  width: 100px;
  border: 1px solid #eee;
}

.table-loader td {
  padding: 5px 10px !important;
  width: 100px;
  border: 1px solid #eee;
  border-bottom: none;
  height: 30px;
}

.loaderTable0 {
  width: 161.43px !important;
  height: 48.8px !important;
}

.loaderTable1 {
  padding: 0px !important;
  width: 100% !important;
}

.loaderTable2 {
  width: 100% !important;
  height: 10px !important;
}

.loaderTable3 {
  width: 150.8px !important;
  height: 48.8px !important;
}

.loaderTable4 {
  width: 153.48px !important;
  height: 48.8px !important;
}

.loaderTable5 {
  width: 153.53px !important;
  height: 48.8px !important;
}

/* Section Container Styles */
.section-container {
  background: #f8f9fa;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: #fff;
  border-radius: 8px 8px 0 0;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #2c3e50;
}

.section-actions {
  display: flex;
  gap: 0.5rem;
}

.widget-container {
  position: relative;
  height: 100%;
  transition: all 0.2s ease;
}

.widget-container:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Dark theme adjustments */
[data-bs-theme="dark"] .section-container {
  background: #1a1d21;
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .section-header {
  background: #22262a;
  border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .section-title {
  color: #e9ecef;
}

[data-bs-theme="dark"] .widget-container {
  background: #22262a;
  border-color: rgba(255, 255, 255, 0.1);
}
footer.widget-footer * {
  font-size: 10px !important;
}
.widget-body {
  background-color: #fff !important;
}
.EventRiskImpact__title {
  /* font-weight: bolder; */
  font-size: 24px;
}
.EventRiskImpact__no-record {
  font-size: 12px;
  display: block;
  width: 486px;
}

/* Preview mode overlay */
.preview-overlay {
  position: fixed;
  top: 100px;
  right: 0px;
  z-index: 9999;
  background-color: rgba(0, 177, 156, 0.9);
  color: white;
  padding: 10px 15px;
  border-radius: 0px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  display: none;
  transition: all 0.3s ease;
}

.preview-overlay p {
  margin: 0;
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 14px;
}

.preview-overlay button {
  background: white;
  color: #00b19c;
  border: none;
  padding: 5px 12px;
  border-radius: 0px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.preview-overlay button:hover {
  background: #f0f0f0;
}

/* Preview mode styles */
body.preview-mode .grid-stack-placeholder,
body.preview-mode .grid-stack > .grid-stack-item > .ui-resizable-handle,
body.preview-mode .grid-stack-item.ui-draggable-handle {
  display: none !important;
}

/* Only hide close buttons with la-times icon instead of all widget icons */
body.preview-mode .widget-icons a i.las.la-times {
  display: none !important;
}

/* Also hide the parent link of close buttons */
body.preview-mode .widget-icons a:last-child {
  display: none !important;
}

body.preview-mode .widget-header {
  cursor: default !important;
}

body.preview-mode .grid-stack-item {
  cursor: auto !important;
}

body.preview-mode .grid-stack-item-content {
  cursor: auto !important;
  pointer-events: auto !important;
}

body.preview-mode .card {
  transition: box-shadow 0.3s ease;
}

/* Reduce spacing in preview mode */
body.preview-mode .container-fluid {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

body.preview-mode .grid-stack {
  margin: -3px !important;
}

body.preview-mode .row {
  margin-left: -5px !important;
  margin-right: -5px !important;
}

body.preview-mode .col-12 {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

body.preview-mode .card-body {
  padding: 0.75rem !important;
}

/* Ensure scrolling works in preview mode */
body.preview-mode {
  overflow: auto !important;
  position: static !important;
  /* Ensure body is not fixed */
  background-color: white !important;
  /* Change background to white in preview mode */
}

body.preview-mode .container-fluid,
body.preview-mode .grid-stack,
body.preview-mode .card-body {
  overflow: visible !important;
  pointer-events: auto !important;
}

body.preview-mode .header-btn.secondary {
  border: 1px solid rgba(0, 177, 156, 0.3);
}

body.preview-mode .header-btn.primary {
  background-color: transparent;
  color: #00b19c;
  border: 1px solid #00b19c;
}

body.preview-mode #preview-btn {
  background-color: #00b19c;
  color: white;
  border: 1px solid #00b19c;
}

/* Additional preview mode improvements */
body.preview-mode .app-header {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

body.preview-mode .dashboard-title {
  margin-left: 5px !important;
  font-size: 22px !important;
}

body.preview-mode .py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.3rem !important;
}

/* Responsive max height for the commodity news scroll area */
.ciScrollNews {
  max-height: 350px;
  /* Adjust as needed for your layout */
  overflow-y: auto;
}

@media (max-width: 991px) {
  .ciScrollNews {
    max-height: 220px;
    /* Smaller height for tablets */
  }
}

@media (max-width: 575px) {
  .ciScrollNews {
    max-height: 140px;
    /* Even smaller for mobile */
  }
}

/* Flexbox layout for grid-stack-item-content and card */
.grid-stack-item-content,
.card.h-100 {
  height: 100% !important;
  display: flex;
  flex-direction: column;
}

/* Make card-body fill available space and allow scroll area to grow/shrink */
.card.h-100 > .card-body {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* Important for flex children to shrink */
}

/* Make ciScrollNews take remaining space and scroll */
.ciScrollNews {
  flex: 1 1 auto;
  min-height: 0;
  overflow-y: auto;
  max-height: unset;
  /* Remove fixed max-height if using flex */
}

.footer {
  padding: 8px 0;
  color: #fff;
}

.footer a {
  color: #fff;
}

#scroll {
  text-align: center;
  width: 30px;
  height: 30px;
  background: #02104f;
  border-radius: 50%;
  margin: 0 auto;
  /* margin-top: -55px; */
  /* margin-right: -25px; */
  color: white;
  z-index: 99999 !important;
  padding-top: 0px;
  font-size: 16px;
  line-height: 6px;
  cursor: pointer;
  position: absolute;
  right: 50%;
  -webkit-transition: -webkit-transform 0.6s ease;
  -moz-transition: -moz-transform 0.6s ease;
  transition: transform 0.6s ease;
  bottom: 4px;
  z-index: 99;
}

#scroll.clicked {
  -webkit-transform: rotate(360deg);
  -moz-transform: rotate(360deg);
  -o-transform: rotate(360deg);
  -ms-transform: rotate(360deg);
}

#scroll.rotate {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  -ms-transform: rotate(3180deg);
}

#scroll span {
  position: relative;
  bottom: -10px;
}

.arrow-bounce {
  -webkit-animation: arrow 1s infinite;
  -moz-animation: arrow 1s infinite;
  -o-animation: arrow 1s infinite;
  animation: arrow 1s infinite;
  animation-timing-function: cubic-bezier(0.4, 0, 0.6, 1);
}

@-webkit-keyframes arrow {
  0% {
    bottom: -13px;
  }

  50% {
    bottom: -8px;
  }

  100% {
    bottom: -13px;
  }
}

@-moz-keyframes arrow {
  0% {
    bottom: -13px;
  }

  50% {
    bottom: -8px;
  }

  100% {
    bottom: -13px;
  }
}

@keyframes arrow {
  0% {
    bottom: -13px;
  }

  50% {
    bottom: -8px;
  }

  100% {
    bottom: -13px;
  }
}

td .irs--round .irs-grid {
  display: none !important;
}

td .irs--round.irs-with-grid {
  height: 48px;
  margin-top: -10px;
}

td .irs--round .irs-from,
td .irs--round .irs-to,
td .irs--round .irs-single {
  font-size: 12px;
  line-height: 1;
  text-shadow: none;
  padding: 10px 5px;
  background-color: transparent;
  color: #007365;
  border-radius: none;
  font-size: 12px;
  font-weight: normal;
}

td .irs--round .irs-line {
  top: 27px;
  height: 5px;
  background-color: #d6e3e7;
  border-radius: 0;
}

td .irs--round .irs-bar {
  top: 27px;
  height: 5px;
  background-color: #3bcd3f !important;
}

td .irs--round .irs-handle {
  top: 25px;
  width: 10px;
  height: 10px;
  border: 5px solid #3bcd3f !important;
  background-color: #3bcd3f !important;
  border-radius: 15px;
  box-shadow: none;
}

.cloudy::after {
  content: " ";
  position: absolute;
  bottom: 0;
  left: 30px;
  width: 96%;
  height: 10px;
  height: 4px;
  background-color: #ffffff00;
  border-radius: 10px;
  box-shadow: 1px -20px 20px 18px rgb(255 255 255 / 83%);
  z-index: 999;
}
/* Simplified grid styling */
.grid-stack {
  min-height: 50px; /* Reduced from 100px */
}

.grid-stack-item {
  min-height: auto !important;
}

.grid-stack-item-content {
  overflow: auto;
}

/* Simplified section container styling */
.section-container-widget {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.section-content {
  flex: 1;
  overflow: auto;
}

/* Simplified nested grid styling */
.grid-stack-nested {
  height: 100% !important;
  overflow: auto;
}

.grid-stack-item-nested {
  min-height: auto !important;
}

/* Improved dropzone styling */
.grid-stack-nested:empty::before {
  content: "Drop widgets here";
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 177, 156, 0.6);
  font-style: italic;
  height: 100%;
  min-height: 80px; /* Reduced from 150px */
}

/* Active dropzone styling */
.active-dropzone {
  border-color: rgba(0, 177, 156, 0.5) !important;
  background-color: rgba(0, 177, 156, 0.1) !important;
}

/* Remove excessive transitions */
.grid-stack-item {
  transition: none; /* Remove height transitions */
}

/* Ensure proper widget content sizing */
.widget {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.widget-body {
  flex: 1;
  overflow: auto;
}

/* Enhanced drag and drop visual feedback */
.grid-stack-item.ui-draggable-dragging {
  z-index: 1000;
  opacity: 0.8;
  transform: rotate(2deg);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

/* Prevent section containers from moving during drag operations */
.section-container-widget.drag-target {
  border: 2px dashed rgba(0, 177, 156, 0.7);
  background-color: rgba(0, 177, 156, 0.1);
}

/* Stable positioning during drag operations */
.grid-stack-item.ui-draggable-dragging .section-container-widget {
  pointer-events: none;
}

/* Better nested grid drop zone indication */
.grid-stack-nested.drag-over {
  border: 2px solid rgba(0, 177, 156, 0.8);
  background-color: rgba(0, 177, 156, 0.15);
  box-shadow: inset 0 0 10px rgba(0, 177, 156, 0.3);
}

/* Visual feedback for active drag operations in nested grids */
.grid-stack-nested.drag-active {
  border: 2px dashed rgba(0, 177, 156, 0.6);
  background-color: rgba(0, 177, 156, 0.08);
}

/* Prevent layout shifts during drag operations */
.grid-stack-item.ui-draggable-dragging {
  transition: none !important;
}

.grid-stack-item.ui-resizable-resizing {
  transition: none !important;
}

/* Visual feedback for drag operations */
body.dragging-in-nested-grid .grid-stack-nested {
  background-color: rgba(0, 177, 156, 0.05);
}

body.main-grid-dragging .grid-stack-nested {
  border-color: rgba(0, 177, 156, 0.5);
}

/* Smooth transitions for auto-resize when not dragging */
.grid-stack-item:not(.ui-draggable-dragging):not(.ui-resizable-resizing) {
  transition: height 0.2s ease-out;
}
