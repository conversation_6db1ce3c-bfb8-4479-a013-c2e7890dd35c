<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Dashboard POC</title>

    <script src="./css-reference.js"></script>
    <script src="./js-reference.js"></script>
    <!-- Note: Widget drag & drop setup is now centralized in js/widget-drag-drop-setup.js (included via js-reference.js) -->
  </head>

  <body>
    <!-- Dashboard Header -->
    <script src="./navbar.js"></script>
    <script src="./widgetsection.js"></script>

    <div id="dashboard-main" style="margin-top: 158px">
      <div class="container-fluid">
        <!-- Test Controls -->
        <div class="row mb-3">
          <div class="col-12">
            <div class="alert alert-info">
              <strong>Autoscroll Fix Test:</strong>
              <button class="btn btn-sm btn-primary ms-2" onclick="addSectionContainerWidget()">Add Section Container</button>
              <button class="btn btn-sm btn-success ms-2" onclick="addWidget()">Add Simple Widget</button>
              <span class="ms-3">Try dragging widgets inside the section container - autoscroll should be disabled.</span>
            </div>
          </div>
        </div>
        <!-- Grid Container -->
        <div class="row">
          <div class="col-12 px-2">
            <div id="grid-container" class="grid-stack">

            </div>
          </div>
        </div>
        
        <!-- Chart Containers for dummy_chart.js -->
        <div id="chartdiv1" style="display: none;"></div>
        <div id="chartdiv1a" style="display: none;"></div>
        <div id="chartdiv2" style="display: none;"></div>
        <div id="chartdiv2a" style="display: none;"></div>
        <div id="chartdiv3" style="display: none;"></div>
        <div id="chartdiv4" style="display: none;"></div>
        

      </div>
    </div>

    <script src="./modals.js"></script>

    <!-- Footer section  -->
    <script src="./footer.js"></script>
    <!-- Required Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://gridstackjs.com/node_modules/gridstack/dist/gridstack-all.js"></script>

    <script src="js/gridstack-autoscroll-auto.js"></script>
    <!-- Widget Comments Script -->
    <script src="js/widget-comments.js"></script>

    <!-- Inline Editing Script -->
    <script src="js/inlineEdit.js"></script>

  

    <script type="text/javascript">
      // Initialize widget counter
      let widgetCount = 0;

      // Auto-resize testing functionality removed

      function addEvents(grid, id) {
        let g = id !== undefined ? "grid" + id + " " : "";

        grid
          .on("added removed change", function (event, items) {
            let str = "";
            items.forEach(function (item) {
              str +=
                " (" +
                item.x +
                "," +
                item.y +
                " " +
                item.w +
                "x" +
                item.h +
                ")";
            });
            console.log(
              g + event.type + " " + items.length + " items (x,y w h):" + str
            );
          })
          .on("enable", function (event) {
            let grid = event.target;
            console.log(g + "enable");
          })
          .on("disable", function (event) {
            let grid = event.target;
            console.log(g + "disable");
          })
          .on("dragstart", function (event, el) {
            let n = el.gridstackNode;
            let x = el.getAttribute("gs-x"); // verify node (easiest) and attr are the same
            let y = el.getAttribute("gs-y");
            console.log(
              g +
                "dragstart " +
                (n.content || "") +
                " pos: (" +
                n.x +
                "," +
                n.y +
                ") = (" +
                x +
                "," +
                y +
                ")"
            );
          })
          .on("drag", function (event, el) {
            let n = el.gridstackNode;
            let x = el.getAttribute("gs-x"); // verify node (easiest) and attr are the same
            let y = el.getAttribute("gs-y");
            // console.log(g + 'drag ' + (n.content || '') + ' pos: (' + n.x + ',' + n.y + ') = (' + x + ',' + y + ')');
          })
          .on("dragstop", function (event, el) {
            let n = el.gridstackNode;
            let x = el.getAttribute("gs-x"); // verify node (easiest) and attr are the same
            let y = el.getAttribute("gs-y");
            console.log(
              g +
                "dragstop " +
                (n.content || "") +
                " pos: (" +
                n.x +
                "," +
                n.y +
                ") = (" +
                x +
                "," +
                y +
                ")"
            );
          })
          .on("dropped", function (event, previousNode, newNode) {
            if (previousNode) {
              console.log(
                g + "dropped - Removed widget from grid:",
                previousNode
              );
            }
            if (newNode) {
              console.log(g + "dropped - Added widget in grid:", newNode);
            }
          })
          .on("resizestart", function (event, el) {
            let n = el.gridstackNode;
            let rec = el.getBoundingClientRect();
            console.log(
              `${g} resizestart ${n.content || ""} size: (${n.w}x${
                n.h
              }) = (${Math.round(rec.width)}x${Math.round(rec.height)})px`
            );
          })
          .on("resize", function (event, el) {
            let n = el.gridstackNode;
            let rec = el.getBoundingClientRect();
            console.log(
              `${g} resize ${n.content || ""} size: (${n.w}x${
                n.h
              }) = (${Math.round(rec.width)}x${Math.round(rec.height)})px`
            );
          })
          .on("resizestop", function (event, el) {
            let n = el.gridstackNode;
            let rec = el.getBoundingClientRect();
            console.log(
              `${g} resizestop ${n.content || ""} size: (${n.w}x${
                n.h
              }) = (${Math.round(rec.width)}x${Math.round(rec.height)})px`
            );
          });
      }
      // This render callback is used to set the innerHTML when loading a widget
      GridStack.renderCB = function (el, widget) {
        if (widget.content) el.innerHTML = widget.content;
      };

      // Global variables
      let staticGrid = false;
      let count = 0;

      // MCP (Model-Controller-Presenter) Pattern Implementation

      // Model - Stores the state and data
      class GridModel {
        constructor() {
          this.selectedNode = null;
          this.grids = {};
        }

        setSelectedNode(node) {
          this.selectedNode = node;
        }

        getSelectedNode() {
          return this.selectedNode;
        }

        registerGrid(id, gridInstance) {
          this.grids[id] = gridInstance;
        }

        getGrid(id) {
          return this.grids[id];
        }

        getAllGrids() {
          return Object.values(this.grids);
        }
      }

      // Controller - Handles the business logic
      class GridController {
        constructor(model) {
          this.model = model;
        }

        selectNode(node) {
          // Deselect previously selected node if any
          const previousNode = this.model.getSelectedNode();
          if (previousNode) {
            this.deselectNode(previousNode);
          }

          // Select the new node
          this.model.setSelectedNode(node);

          // Highlight the selected node
          if (node) {
            node.classList.add("selected-node");
          }
        }

        deselectNode(node) {
          if (node) {
            node.classList.remove("selected-node");
          }
          if (this.model.getSelectedNode() === node) {
            this.model.setSelectedNode(null);
          }
        }

        getSelectedNodeInfo() {
          const selectedNode = this.model.getSelectedNode();
          if (!selectedNode) {
            return { status: "error", message: "No node selected" };
          }

          // Find which grid this node belongs to
          const gridNode = selectedNode.closest(".grid-stack-item");
          if (!gridNode || !gridNode.gridstackNode) {
            return {
              status: "error",
              message: "Selected node is not a grid item",
            };
          }

          // Get the grid instance
          const gridElement = gridNode.closest(".grid-stack");
          if (!gridElement || !gridElement.gridstack) {
            return {
              status: "error",
              message: "Cannot find grid for selected node",
            };
          }

          const grid = gridElement.gridstack;
          const gridstackNode = gridNode.gridstackNode;

          return {
            status: "success",
            node: gridstackNode,
            grid: grid,
            element: gridNode,
            content: selectedNode.innerHTML,
            position: {
              x: gridstackNode.x,
              y: gridstackNode.y,
              w: gridstackNode.w,
              h: gridstackNode.h,
            },
            gridId: grid.opts.id || "unknown",
          };
        }

        removeSelectedNode() {
          const selectedNode = this.model.getSelectedNode();
          if (!selectedNode) {
            return { status: "error", message: "No node selected" };
          }

          // Find which grid this node belongs to
          const gridNode = selectedNode.closest(".grid-stack-item");
          if (!gridNode || !gridNode.gridstackNode) {
            return {
              status: "error",
              message: "Selected node is not a grid item",
            };
          }

          // Get the grid instance
          const gridElement = gridNode.closest(".grid-stack");
          if (!gridElement || !gridElement.gridstack) {
            return {
              status: "error",
              message: "Cannot find grid for selected node",
            };
          }

          const grid = gridElement.gridstack;

          // Remove the node from the grid with compact
          grid.removeWidget(gridNode, true);

          // Clear the selection
          this.model.setSelectedNode(null);

          return { status: "success", message: "Node removed successfully" };
        }
      }

      // Presenter - Handles the UI updates
      class GridPresenter {
        constructor(model, controller) {
          this.model = model;
          this.controller = controller;
          this.setupEventListeners();
        }

        setupEventListeners() {
          // Add click event listeners to all grid items
          document.addEventListener("click", (event) => {
            // Check if clicked on a grid item content
            const gridItemContent = event.target.closest(
              ".grid-stack-item-content"
            );
            if (gridItemContent) {
              this.controller.selectNode(gridItemContent);
              event.stopPropagation();
            } else if (!event.target.closest(".grid-stack-item")) {
              // Clicked outside any grid item, deselect
              const selectedNode = this.model.getSelectedNode();
              if (selectedNode) {
                this.controller.deselectNode(selectedNode);
              }
            }
          });
        }
      }

      // Nested grid layouts removed - not used in current implementation

      // Main grid options (including nested grids)
      let options = {
        staticGrid: staticGrid,
        cellHeight: 60, // Changed from 'auto' to fixed height
        margin: 5,
        minRow: 2,
        acceptWidgets: true,
        id: "main",
        sizeToContent: false, // Disable auto-resizing for main grid
        resizable: { handles: "se,e,s,sw,w" },
        // Remove the children array to start with an empty grid
        children: [], // Empty array for clean initial state
      };

      // Create the main grid inside the dedicated grid container
      let grid = GridStack.addGrid(
        document.getElementById("grid-container"),
        options
      );

      // Optionally attach debug event handlers
      let gridEls = GridStack.getElements(".grid-stack");
      gridEls.forEach(function (gridEl) {
        let g = gridEl.gridstack;
        if (typeof addEvents === "function") {
          addEvents(g, g.opts.id);
        }
        // Attach handler for added widgets to initialize text widgets and table widgets
        g.on("added", function (event, items) {
          items.forEach(function (item) {
            // Text widget auto-init
            const textWidget = item.el.querySelector(".text-widget");
            if (textWidget) {
              const textContainer = textWidget.querySelector(".text-container");
              if (
                textContainer &&
                textContainer.id &&
                typeof window.initTextWidget === "function"
              ) {
                const textId = textContainer.id;
                const settingsId = "settings-" + textId;
                if (
                  !document.getElementById(settingsId) &&
                  typeof window.getTextWidgetMarkup === "function"
                ) {
                  const offcanvasContainer =
                    document.getElementById("offcanvasContainer");
                  if (offcanvasContainer) {
                    const settingsPanel = document.createElement("div");
                    settingsPanel.className = "offcanvas offcanvas-end";
                    settingsPanel.id = settingsId;
                    settingsPanel.setAttribute("tabindex", "-1");
                    settingsPanel.innerHTML = `
                            <div class="offcanvas-header">
                              <h5 class="offcanvas-title">Text Settings</h5>
                              <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                            </div>
                            <div class="offcanvas-body">
                              <!-- Text Content -->
                              <div class="mb-3">
                                <label for="${settingsId}-content" class="form-label">Text Content</label>
                                <textarea class="form-control" id="${settingsId}-content" rows="4" placeholder="Enter your text content here..."></textarea>
                              </div>
                              <!-- Font Family -->
                              <div class="mb-3">
                                <label class="form-label">Font Family</label>
                                <select class="form-select" id="${settingsId}-fontFamily">
                                  <option value="Arial, sans-serif">Arial</option>
                                  <option value="Georgia, serif">Georgia</option>
                                  <option value="Times New Roman, serif">Times New Roman</option>
                                  <option value="Helvetica, sans-serif">Helvetica</option>
                                  <option value="Verdana, sans-serif">Verdana</option>
                                  <option value="Courier New, monospace">Courier New</option>
                                </select>
                              </div>
                              <!-- Font Size -->
                              <div class="mb-3">
                                <label class="form-label">Font Size (px)</label>
                                <input type="range" class="form-range" min="10" max="36" value="14" id="${settingsId}-fontSize">
                                <div class="d-flex justify-content-between">
                                  <small>10px</small>
                                  <small id="${settingsId}-fontSize-display">14px</small>
                                  <small>36px</small>
                                </div>
                              </div>
                              <!-- Text Alignment -->
                              <div class="mb-3">
                                <label class="form-label">Text Alignment</label>
                                <select class="form-select" id="${settingsId}-textAlign">
                                  <option value="left">Left</option>
                                  <option value="center">Center</option>
                                  <option value="right">Right</option>
                                  <option value="justify">Justify</option>
                                </select>
                              </div>
                              <!-- Text Color -->
                              <div class="mb-3">
                                <label for="${settingsId}-textColor" class="form-label">Text Color</label>
                                <input type="color" class="form-control form-control-color" id="${settingsId}-textColor" value="#333333">
                              </div>
                              <!-- Background Color -->
                              <div class="mb-3">
                                <label for="${settingsId}-bgcolor" class="form-label">Background Color</label>
                                <input type="color" class="form-control form-control-color" id="${settingsId}-bgcolor" value="#ffffff">
                              </div>
                              <!-- Line Height -->
                              <div class="mb-3">
                                <label class="form-label">Line Height</label>
                                <input type="range" class="form-range" min="1" max="3" step="0.1" value="1.5" id="${settingsId}-lineHeight">
                                <div class="d-flex justify-content-between">
                                  <small>1.0</small>
                                  <small id="${settingsId}-lineHeight-display">1.5</small>
                                  <small>3.0</small>
                                </div>
                              </div>
                              <!-- Text Formatting -->
                              <div class="mb-3">
                                <label class="form-label">Text Formatting</label>
                                <div class="d-flex gap-2">
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="${settingsId}-bold">
                                    <label class="form-check-label" for="${settingsId}-bold">
                                      <strong>Bold</strong>
                                    </label>
                                  </div>
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="${settingsId}-italic">
                                    <label class="form-check-label" for="${settingsId}-italic">
                                      <em>Italic</em>
                                    </label>
                                  </div>
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="${settingsId}-underline">
                                    <label class="form-check-label" for="${settingsId}-underline">
                                      <u>Underline</u>
                                    </label>
                                  </div>
                                </div>
                              </div>
                              <!-- Border -->
                              <div class="mb-3">
                                <div class="form-check form-switch">
                                  <input class="form-check-input" type="checkbox" id="${settingsId}-border" checked>
                                  <label class="form-check-label" for="${settingsId}-border">Show Border</label>
                                </div>
                              </div>
                              <!-- Border Radius -->
                              <div class="mb-3">
                                <label class="form-label">Border Radius (px)</label>
                                <input type="range" class="form-range" min="0" max="20" value="4" id="${settingsId}-radius">
                                <div class="d-flex justify-content-between">
                                  <small>0px</small>
                                  <small id="${settingsId}-radius-display">4px</small>
                                  <small>20px</small>
                                </div>
                              </div>
                              <!-- Padding -->
                              <div class="mb-3">
                                <label class="form-label">Padding (px)</label>
                                <input type="range" class="form-range" min="5" max="30" value="10" id="${settingsId}-padding">
                                <div class="d-flex justify-content-between">
                                  <small>5px</small>
                                  <small id="${settingsId}-padding-display">10px</small>
                                  <small>30px</small>
                                </div>
                              </div>
                              <!-- Apply Button -->
                              <button class="btn btn-primary w-100" onclick="applyTextSettings('${textId}', '${settingsId}')">
                                Apply Changes
                              </button>
                            </div>`;
                    offcanvasContainer.appendChild(settingsPanel);
                  }
                }
                // Now initialize the widget (event listeners, etc.)
                window.initTextWidget(textId, settingsId);
              }
            }
            // Table widget auto-init
            const tableWidget = item.el.querySelector(".table-widget");
            if (tableWidget) {
              const tableContainer =
                tableWidget.querySelector(".table-container");
              if (
                tableContainer &&
                tableContainer.id &&
                typeof window.initTableWidget === "function"
              ) {
                const tableId = tableContainer.id;
                const settingsId = "settings-" + tableId;
                if (
                  !document.getElementById(settingsId) &&
                  typeof window.getTableWidgetMarkup === "function"
                ) {
                  const offcanvasContainer =
                    document.getElementById("offcanvasContainer");
                  if (offcanvasContainer) {
                    const settingsPanel = document.createElement("div");
                    settingsPanel.className = "offcanvas offcanvas-end";
                    settingsPanel.id = settingsId;
                    settingsPanel.setAttribute("tabindex", "-1");
                    settingsPanel.innerHTML = `
                            <div class="offcanvas-header">
                              <h5 class="offcanvas-title">Table Settings</h5>
                              <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                            </div>
                            <div class="offcanvas-body">
                              <!-- Table Content -->
                              <div class="mb-3">
                                <label for="${settingsId}-content" class="form-label">Table Content</label>
                                <textarea class="form-control" id="${settingsId}-content" rows="4" placeholder="Enter your table content here..."></textarea>
                              </div>
                              <!-- Font Family -->
                              <div class="mb-3">
                                <label class="form-label">Font Family</label>
                                <select class="form-select" id="${settingsId}-fontFamily">
                                  <option value="Arial, sans-serif">Arial</option>
                                  <option value="Georgia, serif">Georgia</option>
                                  <option value="Times New Roman, serif">Times New Roman</option>
                                  <option value="Helvetica, sans-serif">Helvetica</option>
                                  <option value="Verdana, sans-serif">Verdana</option>
                                  <option value="Courier New, monospace">Courier New</option>
                                </select>
                              </div>
                              <!-- Font Size -->
                              <div class="mb-3">
                                <label class="form-label">Font Size (px)</label>
                                <input type="range" class="form-range" min="10" max="36" value="14" id="${settingsId}-fontSize">
                                <div class="d-flex justify-content-between">
                                  <small>10px</small>
                                  <small id="${settingsId}-fontSize-display">14px</small>
                                  <small>36px</small>
                                </div>
                              </div>
                              <!-- Text Alignment -->
                              <div class="mb-3">
                                <label class="form-label">Text Alignment</label>
                                <select class="form-select" id="${settingsId}-textAlign">
                                  <option value="left">Left</option>
                                  <option value="center">Center</option>
                                  <option value="right">Right</option>
                                  <option value="justify">Justify</option>
                                </select>
                              </div>
                              <!-- Text Color -->
                              <div class="mb-3">
                                <label for="${settingsId}-textColor" class="form-label">Text Color</label>
                                <input type="color" class="form-control form-control-color" id="${settingsId}-textColor" value="#333333">
                              </div>
                              <!-- Background Color -->
                              <div class="mb-3">
                                <label for="${settingsId}-bgcolor" class="form-label">Background Color</label>
                                <input type="color" class="form-control form-control-color" id="${settingsId}-bgcolor" value="#ffffff">
                              </div>
                              <!-- Line Height -->
                              <div class="mb-3">
                                <label class="form-label">Line Height</label>
                                <input type="range" class="form-range" min="1" max="3" step="0.1" value="1.5" id="${settingsId}-lineHeight">
                                <div class="d-flex justify-content-between">
                                  <small>1.0</small>
                                  <small id="${settingsId}-lineHeight-display">1.5</small>
                                  <small>3.0</small>
                                </div>
                              </div>
                              <!-- Text Formatting -->
                              <div class="mb-3">
                                <label class="form-label">Text Formatting</label>
                                <div class="d-flex gap-2">
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="${settingsId}-bold">
                                    <label class="form-check-label" for="${settingsId}-bold">
                                      <strong>Bold</strong>
                                    </label>
                                  </div>
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="${settingsId}-italic">
                                    <label class="form-check-label" for="${settingsId}-italic">
                                      <em>Italic</em>
                                    </label>
                                  </div>
                                  <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="${settingsId}-underline">
                                    <label class="form-check-label" for="${settingsId}-underline">
                                      <u>Underline</u>
                                    </label>
                                  </div>
                                </div>
                              </div>
                              <!-- Border -->
                              <div class="mb-3">
                                <div class="form-check form-switch">
                                  <input class="form-check-input" type="checkbox" id="${settingsId}-border" checked>
                                  <label class="form-check-label" for="${settingsId}-border">Show Border</label>
                                </div>
                              </div>
                              <!-- Border Radius -->
                              <div class="mb-3">
                                <label class="form-label">Border Radius (px)</label>
                                <input type="range" class="form-range" min="0" max="20" value="4" id="${settingsId}-radius">
                                <div class="d-flex justify-content-between">
                                  <small>0px</small>
                                  <small id="${settingsId}-radius-display">4px</small>
                                  <small>20px</small>
                                </div>
                              </div>
                              <!-- Padding -->
                              <div class="mb-3">
                                <label class="form-label">Padding (px)</label>
                                <input type="range" class="form-range" min="5" max="30" value="10" id="${settingsId}-padding">
                                <div class="d-flex justify-content-between">
                                  <small>5px</small>
                                  <small id="${settingsId}-padding-display">10px</small>
                                  <small>30px</small>
                                </div>
                              </div>
                              <!-- Apply Button -->
                              <button class="btn btn-primary w-100" onclick="applyTableSettings('${tableId}', '${settingsId}')">
                                Apply Changes
                              </button>
                            </div>`;
                    offcanvasContainer.appendChild(settingsPanel);
                  }
                }
                // Now initialize the widget (event listeners, etc.)
                window.initTableWidget(tableId, settingsId);
              }
            }

            const pieChartWidgets = item.el.querySelectorAll(
              '.widget .chart-container[id^="piechart-"]'
            );
            pieChartWidgets.forEach(function (container) {
              if (
                container &&
                container.id &&
                typeof window.initPieChart === "function"
              ) {
                setTimeout(function () {
                  window.initPieChart(container.id);
                }, 0);
              }
            });

            // Linked pie chart widget auto-init
            const linkedPieChartWidgets = item.el.querySelectorAll(
              '.linked-pie-chart-widget'
            );
            linkedPieChartWidgets.forEach(function (widget) {
              const chartContainer = widget.querySelector('.chart-container[id^="linked-pie-chart-container-"]');
              if (chartContainer && chartContainer.id) {
                // Get the widget element to access stored config
                const widgetElement = widget.closest('[id^="linked-pie-chart-"]');

                // Check for pending drag-drop initialization data
                if (window.pendingLinkedPieChartInits && window.pendingLinkedPieChartInits.has(widgetElement.id)) {
                  const initData = window.pendingLinkedPieChartInits.get(widgetElement.id);
                  console.log('🔗 Grid event: Found pending initialization for', widgetElement.id);

                  // Store config on widget element
                  widgetElement.widgetConfig = initData.config;
                  widgetElement.chartContainerId = initData.chartContainerId;

                  // Create settings offcanvas
                  if (typeof createLinkedPieChartSettingsOffcanvas === "function") {
                    createLinkedPieChartSettingsOffcanvas(
                      initData.settingsId,
                      initData.widgetId,
                      initData.chartContainerId,
                      initData.config
                    );
                    console.log('🔗 Grid event: Settings offcanvas created');
                  }

                  // Initialize the chart
                  setTimeout(function () {
                    if (!chartContainer.hasAttribute('data-initialized')) {
                      if (typeof initializeLinkedPieChart === "function") {
                        console.log('🔗 Grid event: Initializing linked pie chart...');
                        initializeLinkedPieChart(initData.chartContainerId, initData.config);
                        chartContainer.setAttribute('data-initialized', 'true');
                        console.log('✅ Linked pie chart initialized via grid event');
                      }
                    }
                  }, 100);

                  // Clean up pending initialization
                  window.pendingLinkedPieChartInits.delete(widgetElement.id);

                } else if (widgetElement && widgetElement.widgetConfig) {
                  // Handle click-added widgets (already have config stored)
                  if (!chartContainer.hasAttribute('data-initialized')) {
                    setTimeout(function () {
                      if (typeof initializeLinkedPieChart === "function") {
                        initializeLinkedPieChart(chartContainer.id, widgetElement.widgetConfig);
                        chartContainer.setAttribute('data-initialized', 'true');
                        console.log('✅ Linked pie chart initialized via click');
                      }
                    }, 100);

                    // Create settings offcanvas if not already created
                    const settingsId = widget.querySelector('[data-bs-target]')?.getAttribute('data-bs-target')?.substring(1);
                    if (settingsId && !document.getElementById(settingsId)) {
                      setTimeout(function () {
                        if (typeof createLinkedPieChartSettingsOffcanvas === "function") {
                          createLinkedPieChartSettingsOffcanvas(
                            settingsId,
                            widgetElement.id,
                            chartContainer.id,
                            widgetElement.widgetConfig
                          );
                        }
                      }, 150);
                    }
                  }
                }
              }
            });

            // Horizontal bar chart widget auto-init
            const horizontalBarChartWidgets = item.el.querySelectorAll(
              '.widget .chart-container[id^="horizontal-bar-chart-"]'
            );
            horizontalBarChartWidgets.forEach(function (container) {
              if (container && container.id) {
                // Check for pending drag-drop initialization data
                if (window.pendingHorizontalBarChartInits && window.pendingHorizontalBarChartInits.has(container.id)) {
                  const initData = window.pendingHorizontalBarChartInits.get(container.id);
                  console.log('📊 Grid event: Found pending initialization for', container.id);

                  // Initialize the chart
                  setTimeout(function () {
                    if (!container.hasAttribute('data-initialized')) {
                      if (typeof initHorizontalBarChart === "function") {
                        console.log('📊 Grid event: Initializing horizontal bar chart...');
                        initHorizontalBarChart(container.id);
                        container.setAttribute('data-initialized', 'true');
                        console.log('✅ Horizontal bar chart initialized via grid event');
                      }
                    }
                  }, 100);

                  // Clean up pending initialization
                  window.pendingHorizontalBarChartInits.delete(container.id);

                } else if (typeof window.initHorizontalBarChart === "function") {
                  // Handle click-added widgets (fallback)
                  if (!container.hasAttribute('data-initialized')) {
                    setTimeout(function () {
                      initHorizontalBarChart(container.id);
                      container.setAttribute('data-initialized', 'true');
                      console.log('✅ Horizontal bar chart initialized via click');
                    }, 100);
                  }
                }
              }
            });

            // Column chart widget auto-init
            const columnChartWidgets = item.el.querySelectorAll(
              '.widget .chart-container[id^="column-chart-"]'
            );
            columnChartWidgets.forEach(function (container) {
              if (container && container.id) {
                // Check for pending drag-drop initialization data
                if (window.pendingColumnChartInits && window.pendingColumnChartInits.has(container.id)) {
                  const initData = window.pendingColumnChartInits.get(container.id);
                  console.log('📊 Grid event: Found pending initialization for', container.id);

                  // Initialize the chart
                  setTimeout(function () {
                    if (!container.hasAttribute('data-initialized')) {
                      if (typeof initColumnChart === "function") {
                        console.log('📊 Grid event: Initializing column chart...');
                        initColumnChart(container.id);
                        container.setAttribute('data-initialized', 'true');
                        console.log('✅ Column chart initialized via grid event');
                      }
                    }
                  }, 100);

                  // Clean up pending initialization
                  window.pendingColumnChartInits.delete(container.id);

                } else if (typeof window.initColumnChart === "function") {
                  // Handle click-added widgets (fallback)
                  if (!container.hasAttribute('data-initialized')) {
                    setTimeout(function () {
                      initColumnChart(container.id);
                      container.setAttribute('data-initialized', 'true');
                      console.log('✅ Column chart initialized via click');
                    }, 100);
                  }
                }
              }
            });

            // Dual axis chart widget auto-init
            const dualAxisChartWidgets = item.el.querySelectorAll(
              '.widget .chart-container[id^="dual-axis-chart-"]'
            );
            dualAxisChartWidgets.forEach(function (container) {
              if (container && container.id) {
                // Check for pending drag-drop initialization data
                if (window.pendingDualAxisChartInits && window.pendingDualAxisChartInits.has(container.id)) {
                  const initData = window.pendingDualAxisChartInits.get(container.id);
                  console.log('📊 Grid event: Found pending initialization for', container.id);

                  // Initialize the chart
                  setTimeout(function () {
                    if (!container.hasAttribute('data-initialized')) {
                      if (typeof initDualAxisChart === "function") {
                        console.log('📊 Grid event: Initializing dual axis chart...');
                        initDualAxisChart(container.id);
                        container.setAttribute('data-initialized', 'true');
                        console.log('✅ Dual axis chart initialized via grid event');
                      }
                    }
                  }, 100);

                  // Clean up pending initialization
                  window.pendingDualAxisChartInits.delete(container.id);

                } else if (typeof window.initDualAxisChart === "function") {
                  // Handle click-added widgets (fallback)
                  if (!container.hasAttribute('data-initialized')) {
                    setTimeout(function () {
                      initDualAxisChart(container.id);
                      container.setAttribute('data-initialized', 'true');
                      console.log('✅ Dual axis chart initialized via click');
                    }, 100);
                  }
                }
              }
            });

            const sectionContainer = item.el.querySelector(
              ".section-container-widget"
            );
            if (sectionContainer) {
              const nestedGridContainer = sectionContainer.querySelector(
                ".nested-grid-container"
              );
              if (
                nestedGridContainer &&
                typeof window.initSectionContainer === "function"
              ) {
                // Only initialize if not already initialized
                if (!nestedGridContainer.querySelector(".grid-stack")) {
                  window.initSectionContainer(nestedGridContainer.id);
                }
              }
            }
          });
        });
      });

      // Initialize the MCP pattern
      const gridModel = new GridModel();
      const gridController = new GridController(gridModel);
      const gridPresenter = new GridPresenter(gridModel, gridController);

      // Auto-create test section with charts on page load
      // This functionality is now DISABLED - section will not be created automatically

      // Register the main grid with the model
      gridModel.registerGrid("main", grid);

      // Function to check the selected node
      function checkSelectedNode() {
        const info = gridController.getSelectedNodeInfo();

        if (info.status === "error") {
          alert(info.message);
          return;
        }

        // Create a formatted message with the node information
        const message =
          `Selected Node Information:\n\n` +
          `Grid ID: ${info.gridId}\n` +
          `Position: (${info.position.x}, ${info.position.y})\n` +
          `Size: ${info.position.w}x${info.position.h}\n` +
          `Content: ${info.content.substring(0, 50)}${
            info.content.length > 50 ? "..." : ""
          }`;

        alert(message);
        console.log("Selected Node Details:", info);
      }

      // Function to remove the selected node
      function removeSelectedNode() {
        const result = gridController.removeSelectedNode();
        if (result.status === "error") {
          alert(result.message);
        } else {
          console.log(result.message);
        }
      }

      // Functions to switch grid mode
      function setStatic(val) {
        staticGrid = val;
        grid.setStatic(staticGrid);
      }

      // Add a new simple widget to the main grid
      function addWidget() {
        grid.addWidget({ x: 0, y: 100, content: "new item" });
      }

      // Function to create a section container widget
      function addSectionContainerWidget() {
        const containerId =
          "section-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
        const widget = grid.addWidget({
          x: 0,
          y: 0,
          w: 6,
          h: 4,
          content: getSectionContainerWidgetMarkup(containerId),
        });
        // Initialize nested grid after the widget is added to DOM
        requestAnimationFrame(() => {
          initSectionContainer(containerId);
        });
      }

      // Function to remove a section container
      function removeSectionContainer(button) {
        // Find the section container widget
        const sectionContainer = button.closest(".section-container-widget");
        if (!sectionContainer) {
          console.error("Could not find section container");
          return;
        }

        // Find the grid-stack-item that contains this section
        const gridItem = sectionContainer.closest(".grid-stack-item");
        if (!gridItem) {
          console.error("Could not find grid item");
          return;
        }

        // Find the nested grid inside this section
        const nestedGridContainer = sectionContainer.querySelector(
          ".nested-grid-container"
        );
        if (!nestedGridContainer) {
          console.error("Could not find nested grid container");
          return;
        }

        const nestedGridElement =
          nestedGridContainer.querySelector(".grid-stack");
        if (nestedGridElement) {
          // Find and dispose all chart instances in this section
          const chartContainers =
            nestedGridElement.querySelectorAll(".chart-container");
          chartContainers.forEach((container) => {
            if (container.chart) {
              container.chart.dispose();
            }
          });

          // Find and destroy all Handsontable instances in this section
          const spreadsheetContainers = nestedGridElement.querySelectorAll(
            ".spreadsheet-container"
          );
          spreadsheetContainers.forEach((container) => {
            if (container.hotInstance) {
              container.hotInstance.destroy();
            }
          });

          // Destroy the nested grid instance if it exists
          if (nestedGridElement.gridstack) {
            nestedGridElement.gridstack.destroy(true);
          }
        }

        // Get the main grid instance and remove the section widget
        if (window.grid && window.grid.engine) {
          try {
            window.grid.removeWidget(gridItem, true); // true enables compact
          } catch (error) {
            console.error("Error removing widget:", error);
          }
        } else {
          console.error("Main grid not initialized");
          // Fallback: remove the grid item directly from DOM if grid instance is not available
          gridItem.remove();
        }
      }

      // addNewWidget function removed - not used in current implementation

      // Function to remove a widget
      function removeWidget(element) {
        console.log("🗑️ removeWidget called with element:", element);

        // Find the widget container
        const widgetElement = element.closest(".grid-stack-item");
        console.log("📦 Found widget element:", widgetElement);

        if (widgetElement) {
          // Find any chart containers within the widget
          const chartContainers =
            widgetElement.querySelectorAll(".chart-container");

          // Clean up any amCharts instances and observers
          chartContainers.forEach((container) => {
            // Clean up amCharts v4 instances
            if (container.chart) {
              console.log("Disposing amCharts instance");
              container.chart.dispose();
            }

            // Clean up amCharts v5 instances
            if (container.am5root) {
              console.log("Disposing amCharts v5 instance");
              container.am5root.dispose();
            }

            // Clean up resize observers
            if (container.resizeObserver) {
              console.log("Disconnecting resize observer");
              container.resizeObserver.disconnect();
            }
          });

          // Remove the widget from the grid and force compact
          const gridInstance = window.grid || grid;
          console.log("🔧 Grid instance found:", !!gridInstance);
          console.log("🔧 Grid removeWidget method:", typeof gridInstance?.removeWidget);
          console.log("🔧 Grid float setting:", gridInstance?.opts?.float);
          console.log("🔧 Grid compact setting:", gridInstance?.opts?.compact);

          if (gridInstance && gridInstance.removeWidget) {
            console.log("🗑️ About to remove widget from grid...");

            // Remove the widget
            gridInstance.removeWidget(widgetElement);
            console.log("✅ Widget removed from grid");

            // Multiple approaches to ensure compacting
            setTimeout(() => {
              console.log("🔄 Starting compacting process...");

              // Method 1: Try compact method
              if (typeof gridInstance.compact === 'function') {
                gridInstance.compact();
                console.log("✅ Grid compacted using compact()");
              } else {
                console.log("❌ compact() method not available");
              }

              // Method 2: Try engine compact
              if (gridInstance.engine && typeof gridInstance.engine.compact === 'function') {
                gridInstance.engine.compact();
                console.log("✅ Grid compacted using engine.compact()");
              } else {
                console.log("❌ engine.compact() method not available");
              }

              // Method 3: Try batchUpdate with compact
              if (typeof gridInstance.batchUpdate === 'function') {
                gridInstance.batchUpdate();
                console.log("✅ Grid batch updated");
              }

              // Method 4: Force all widgets to move up
              const allItems = document.querySelectorAll('.grid-stack-item');
              console.log("📦 Found", allItems.length, "widgets to reposition");

              if (allItems.length > 0) {
                // Get all widget positions
                const widgets = Array.from(allItems).map(item => {
                  const rect = item.getBoundingClientRect();
                  return {
                    element: item,
                    x: parseInt(item.getAttribute('gs-x') || '0'),
                    y: parseInt(item.getAttribute('gs-y') || '0'),
                    w: parseInt(item.getAttribute('gs-w') || '1'),
                    h: parseInt(item.getAttribute('gs-h') || '1')
                  };
                }).filter(w => w.element.parentNode); // Only keep widgets still in DOM

                // Sort by position (top to bottom, left to right)
                widgets.sort((a, b) => {
                  if (a.y !== b.y) return a.y - b.y;
                  return a.x - b.x;
                });

                console.log("📦 Widgets to reposition:", widgets.length);

                // Reposition widgets to fill gaps
                let currentRow = 0;
                let currentCol = 0;
                const maxCols = 12; // Grid has 12 columns

                widgets.forEach((widget, index) => {
                  // Check if widget fits in current row
                  if (currentCol + widget.w > maxCols) {
                    currentRow++;
                    currentCol = 0;
                  }

                  // Move widget if position changed
                  if (widget.x !== currentCol || widget.y !== currentRow) {
                    console.log(`📦 Moving widget from (${widget.x},${widget.y}) to (${currentCol},${currentRow})`);

                    try {
                      gridInstance.update(widget.element, {
                        x: currentCol,
                        y: currentRow
                      });
                    } catch (error) {
                      console.error("❌ Error updating widget position:", error);
                    }
                  }

                  currentCol += widget.w;
                });

                console.log("✅ Manual repositioning completed");
              }

            }, 100);

            console.log("✅ Widget removed and compacting initiated");
          } else {
            console.error("❌ Grid instance not found");
          }
        } else {
          console.error("Could not find widget element");
        }
      }

      // Save, Destroy, and Load functions to test serialization
      // Removed save, destroy, and load functions
    </script>

    <!-- Widget Category Switching Script -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Widget category switching
        const categories = document.querySelectorAll(".widget-category");
        const allWidgets = document.querySelectorAll(".widget-item");

        categories.forEach((category) => {
          category.addEventListener("click", function () {
            // Remove active class from all categories
            categories.forEach((c) => c.classList.remove("active"));

            // Add active class to clicked category
            this.classList.add("active");

            // Get the selected category
            const selectedCategory = this.getAttribute("data-category");

            // Show/hide widgets based on category
            allWidgets.forEach((widget) => {
              if (
                widget.classList.contains(`widget-category-${selectedCategory}`)
              ) {
                widget.style.display = "flex";
              } else {
                widget.style.display = "none";
              }
            });
          });
        });

        // Show "insert" category widgets by default
        document
          .querySelector('.widget-category[data-category="insert"]')
          .click();
      });
    </script>

    <!-- Section Gallery system removed - was broken due to missing UI -->

    <!-- Section Gallery template handler removed - was broken due to missing UI -->

    <script>
      // Function to initialize line chart
      function initializeLineChart(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) {
          console.error("Container not found:", containerId);
          return;
        }

        // Create root element
        var root = am5.Root.new(containerId);

        // Set themes
        root.setThemes([am5themes_Animated.new(root)]);

        // Create chart
        var chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "none",
            wheelY: "none",
          })
        );

        // Create axes
        var xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "category",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll(data.categories.map((category) => ({ category })));

        var yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        // Create series
        var series = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: data.series[0].name,
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "category",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        // Set data
        series.data.setAll(
          data.series[0].data.map((value, index) => ({
            category: data.categories[index],
            value: value,
          }))
        );

        // Add cursor
        chart.set("cursor", am5xy.XYCursor.new(root, {}));

        // Add legend
        var legend = chart.rightAxes.push(
          am5.Legend.new(root, {
            centerY: am5.p50,
            y: am5.p50,
            layout: root.verticalLayout,
          })
        );
        legend.data.setAll(chart.series.values);

        // Save chart instance to container for later access
        container.chart = chart;
      }

      // Function to initialize spreadsheet
      function initializeSpreadsheet(containerId, data) {
        const container = document.getElementById(containerId);
        if (!container) {
          console.error("Container not found:", containerId);
          return;
        }

        new Handsontable(container, {
          data: data,
          rowHeaders: true,
          colHeaders: true,
          height: "100%",
          width: "100%",
          licenseKey: "non-commercial-and-evaluation",
          stretchH: "all",
          autoColumnSize: true,
          contextMenu: true,
          manualRowResize: true,
          manualColumnResize: true,
        });
      }

      // Function to remove individual widget from nested grid
      function removeWidget(button) {
        const widgetItem = button.closest(".grid-stack-item");
        if (!widgetItem) return;

        // Get the nested grid instance
        const nestedGrid = widgetItem.closest(".grid-stack").gridstack;
        if (!nestedGrid) return;

        // Check if it's a chart widget and clean up
        const chartContainer = widgetItem.querySelector(".chart-container");
        if (chartContainer) {
          // Clean up amCharts v4 instances
          if (chartContainer.chart) {
            console.log("Disposing amCharts v4 instance from nested grid");
            chartContainer.chart.dispose();
          }
          // Clean up amCharts v5 instances
          if (chartContainer.am5root) {
            console.log("Disposing amCharts v5 instance from nested grid");
            chartContainer.am5root.dispose();
          }
        }

        // Check if it's a spreadsheet widget and clean up
        const spreadsheetContainer = widgetItem.querySelector(
          ".spreadsheet-container"
        );
        if (spreadsheetContainer && spreadsheetContainer.hotInstance) {
          spreadsheetContainer.hotInstance.destroy();
        }

        // Remove the widget from the nested grid and force compact
        nestedGrid.removeWidget(widgetItem, true); // true enables compact

        // Force compact if the above doesn't work
        setTimeout(() => {
          if (nestedGrid.compact) {
            nestedGrid.compact();
            console.log("🔄 Nested grid manually compacted");
          } else if (nestedGrid.engine && nestedGrid.engine.compact) {
            nestedGrid.engine.compact();
            console.log("🔄 Nested grid engine manually compacted");
          }
        }, 100);

        console.log("✅ Widget removed from nested grid and compacted");
      }

      // createAnalyticsSection function removed - not accessible due to missing UI

      // Function to remove a section container
      function removeSectionContainer(button) {
        // Find the section container widget
        const sectionContainer = button.closest(".section-container-widget");
        if (!sectionContainer) {
          console.error("Could not find section container");
          return;
        }

        // Find the grid-stack-item that contains this section
        const gridItem = sectionContainer.closest(".grid-stack-item");
        if (!gridItem) {
          console.error("Could not find grid item");
          return;
        }

        // Find the nested grid inside this section
        const nestedGridContainer = sectionContainer.querySelector(
          ".nested-grid-container"
        );
        if (!nestedGridContainer) {
          console.error("Could not find nested grid container");
          return;
        }

        const nestedGridElement =
          nestedGridContainer.querySelector(".grid-stack");
        if (nestedGridElement) {
          // Find and dispose all chart instances in this section
          const chartContainers =
            nestedGridElement.querySelectorAll(".chart-container");
          chartContainers.forEach((container) => {
            if (container.chart) {
              container.chart.dispose();
            }
          });

          // Find and destroy all Handsontable instances in this section
          const spreadsheetContainers = nestedGridElement.querySelectorAll(
            ".spreadsheet-container"
          );
          spreadsheetContainers.forEach((container) => {
            if (container.hotInstance) {
              container.hotInstance.destroy();
            }
          });

          // Destroy the nested grid instance if it exists
          if (nestedGridElement.gridstack) {
            nestedGridElement.gridstack.destroy(true);
          }
        }

        // Get the main grid instance and remove the section widget
        if (window.grid && window.grid.engine) {
          try {
            window.grid.removeWidget(gridItem, true); // true enables compact
          } catch (error) {
            console.error("Error removing widget:", error);
          }
        } else {
          console.error("Main grid not initialized");
          // Fallback: remove the grid item directly from DOM if grid instance is not available
          gridItem.remove();
        }
      }

      // createDashboardSection function removed - not accessible due to missing UI

      // Update the Line Chart Widget to ensure proper height
      function createLineChartWidget(title) {
        return `
          <div class="card">
            <div class="card-body d-flex flex-column">
              <h6 class="card-title text-muted mb-3">${title}</h6>
              <div class="chart-container" style="position: relative;"></div>
            </div>
          </div>
        `;
      }

      // Update the KPI Card Widget with minimum height
      function createKPICardWidget(title, value, change) {
        return `
          <div class="card">
            <div class="card-body d-flex flex-column justify-content-between">
              <h6 class="card-title text-muted">${title}</h6>
              <div class="d-flex align-items-center justify-content-between">
                <h3 class="mb-0">${value}</h3>
                <span class="badge ${
                  change.startsWith("+") ? "bg-success" : "bg-danger"
                }">${change}</span>
              </div>
            </div>
          </div>
        `;
      }

      // Update the Data Table Widget with minimum height
      function createDataTableWidget(title) {
        return `
          <div class="card">
            <div class="card-body d-flex flex-column">
              <h6 class="card-title text-muted mb-3">${title}</h6>
              <div class="table-responsive">
                <table class="table table-sm table-hover">
                  <thead>
                    <tr>
                      <th>Metric</th>
                      <th>Current</th>
                      <th>Previous</th>
                      <th>Change</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>Revenue</td>
                      <td>$150,000</td>
                      <td>$130,000</td>
                      <td class="text-success">+15%</td>
                    </tr>
                    <tr>
                      <td>Users</td>
                      <td>25,000</td>
                      <td>23,000</td>
                      <td class="text-success">+8%</td>
                    </tr>
                    <tr>
                      <td>Conversion</td>
                      <td>3.2%</td>
                      <td>2.8%</td>
                      <td class="text-success">+14%</td>
                    </tr>
                    <tr>
                      <td>Engagement</td>
                      <td>68%</td>
                      <td>65%</td>
                      <td class="text-success">+4%</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        `;
      }

      // createComparisonSection function removed - not accessible due to missing UI

      // createSummarySection function removed - not accessible due to missing UI

      // createTrendSection function removed - not accessible due to missing UI

      // Helper functions for widget creation
      function createLineChartWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-line"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createKPICardWidget(title, value, change) {
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-square"></i> ${title}
              </div>
            </div>
            <div class="d-flex flex-column justify-content-center align-items-center h-100">
              <h3 class="mb-0">${value}</h3>
              <span class="text-success">${change}</span>
            </div>
          </div>
        `;
      }

      function createDataTableWidget(title) {
        const tableId = `table-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-table"></i> ${title}
              </div>
            </div>
            <div id="${tableId}" class="spreadsheet-container" style="flex: 1 1 auto; min-height: 200px; width: 100%; height: 100%; position: relative;"></div>
          </div>
        `;
      }

      function createBarChartWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-bar"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createPercentageWidget(title) {
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-percentage"></i> ${title}
              </div>
            </div>
            <div class="d-flex justify-content-around align-items-center h-100">
              <div class="text-center">
                <h4>Product A</h4>
                <h2>45%</h2>
              </div>
              <div class="text-center">
                <h4>Product B</h4>
                <h2>35%</h2>
              </div>
              <div class="text-center">
                <h4>Others</h4>
                <h2>20%</h2>
              </div>
            </div>
          </div>
        `;
      }

      function createComparisonTableWidget(title) {
        const tableId = `table-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-arrows-alt-h"></i> ${title}
              </div>
            </div>
            <div id="${tableId}" class="spreadsheet-container" style="flex: 1 1 auto; min-height: 200px; width: 100%; height: 100%; position: relative;"></div>
          </div>
        `;
      }

      function createTextSummaryWidget(title) {
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-file-alt"></i> ${title}
              </div>
            </div>
            <div class="p-3" style="flex: 1; overflow: auto;">
              <p>This is an executive summary of the key findings and insights from our analysis. The data shows significant trends in market performance and user engagement.</p>
              <p>Key highlights include increased revenue growth, improved user retention, and expanding market presence.</p>
            </div>
          </div>
        `;
      }

      // createPieChartWidget removed - functionality moved to js/pieChartWidget.js

      function createKeyPointsWidget(title) {
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-list"></i> ${title}
              </div>
            </div>
            <div class="p-3" style="flex: 1; overflow: auto;">
              <ul class="list-unstyled">
                <li class="mb-2"><i class="las la-check text-success me-2"></i> Revenue increased by 25% YoY</li>
                <li class="mb-2"><i class="las la-check text-success me-2"></i> Customer satisfaction reached 92%</li>
                <li class="mb-2"><i class="las la-check text-success me-2"></i> New market penetration successful</li>
                <li class="mb-2"><i class="las la-check text-success me-2"></i> Product adoption rate up by 15%</li>
              </ul>
            </div>
          </div>
        `;
      }

      function createTimeSeriesWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-line"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createAreaChartWidget(title) {
        const chartId = `chart-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-chart-area"></i> ${title}
              </div>
            </div>
            <div id="${chartId}" class="chart-container" style="flex: 1; overflow: hidden;"></div>
          </div>
        `;
      }

      function createTrendTableWidget(title) {
        const tableId = `table-${Date.now()}`;
        return `
          <div style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
            <div class="widget-header mb-2 mb-2">
              <div>
                <i class="las la-table"></i> ${title}
              </div>
            </div>
            <div id="${tableId}" class="spreadsheet-container" style="flex: 1 1 auto; min-height: 200px; width: 100%; height: 100%; position: relative;"></div>
          </div>
        `;
      }

      function initializeWidgets() {
        // Initialize all chart containers
        document.querySelectorAll(".chart-container").forEach((container) => {
          if (!container.chart) {
            const root = am5.Root.new(container.id);
            root.setThemes([am5themes_Animated.new(root)]);

            // Create chart based on container's parent widget type
            if (container.closest('[class*="line-chart"]')) {
              createLineChart(root);
            } else if (container.closest('[class*="bar-chart"]')) {
              createBarChart(root);
            } else if (container.closest('[class*="pie-chart"]')) {
              createPieChart(root);
            } else if (container.closest('[class*="area-chart"]')) {
              createAreaChart(root);
            }

            container.chart = root;
          }
        });

        // Initialize all spreadsheet containers
        document
          .querySelectorAll(".spreadsheet-container")
          .forEach((container) => {
            if (!container.hotInstance) {
              console.log(
                "Initializing Handsontable for container:",
                container.id
              );
              console.log(
                "Container dimensions:",
                container.offsetWidth,
                "x",
                container.offsetHeight
              );

              // Ensure the container has proper dimensions
              if (container.offsetWidth === 0 || container.offsetHeight === 0) {
                console.warn(
                  "Container has zero dimensions, setting explicit size"
                );
                container.style.width = "100%";
                container.style.height = "300px";
                container.style.minHeight = "200px";
              }

              try {
                const hot = new Handsontable(container, {
                  data: generateSampleData(),
                  rowHeaders: true,
                  colHeaders: true,
                  height: "100%",
                  width: "100%",
                  licenseKey: "non-commercial-and-evaluation",
                  stretchH: "all",
                  autoColumnSize: true,
                  contextMenu: true,
                  manualColumnResize: true,
                  manualRowResize: true,
                });
                container.hotInstance = hot;
                console.log(
                  "Handsontable initialized successfully for",
                  container.id
                );
              } catch (error) {
                console.error("Error initializing Handsontable:", error);
              }
            }
          });
      }

      function generateSampleData() {
        return [
          ["Category", "Value 1", "Value 2", "Value 3"],
          ["A", 100, 120, 140],
          ["B", 200, 220, 240],
          ["C", 300, 320, 340],
          ["D", 400, 420, 440],
        ];
      }

      function createLineChart(root) {
        const chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "none",
            wheelY: "none",
          })
        );

        const data = [
          { date: "2023-01", value: 100 },
          { date: "2023-02", value: 120 },
          { date: "2023-03", value: 140 },
          { date: "2023-04", value: 130 },
          { date: "2023-05", value: 170 },
        ];

        const xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "date",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll(data);

        const yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        const series = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Series",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "date",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        series.data.setAll(data);
        chart.set("cursor", am5xy.XYCursor.new(root, {}));
      }

      function createBarChart(root) {
        const chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "none",
            wheelY: "none",
          })
        );

        const data = [
          { category: "A", value1: 100, value2: 90 },
          { category: "B", value1: 120, value2: 110 },
          { category: "C", value1: 140, value2: 130 },
          { category: "D", value1: 130, value2: 120 },
        ];

        const xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "category",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll(data);

        const yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        const series = chart.series.push(
          am5xy.ColumnSeries.new(root, {
            name: "Series",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value1",
            categoryXField: "category",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        series.data.setAll(data);
        chart.set("cursor", am5xy.XYCursor.new(root, {}));
      }

      function createPieChart(root) {
        const chart = root.container.children.push(
          am5percent.PieChart.new(root, {
            layout: root.verticalLayout,
          })
        );

        const data = [
          { category: "A", value: 30 },
          { category: "B", value: 25 },
          { category: "C", value: 20 },
          { category: "D", value: 15 },
          { category: "E", value: 10 },
        ];

        const series = chart.series.push(
          am5percent.PieSeries.new(root, {
            valueField: "value",
            categoryField: "category",
            endAngle: 270,
          })
        );

        series.data.setAll(data);
        series.appear(1000, 100);
      }

      function createAreaChart(root) {
        const chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "none",
            wheelY: "none",
          })
        );

        const data = [
          { date: "2023-01", value: 100 },
          { date: "2023-02", value: 120 },
          { date: "2023-03", value: 140 },
          { date: "2023-04", value: 130 },
          { date: "2023-05", value: 170 },
        ];

        const xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "date",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll(data);

        const yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        const series = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Series",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "date",
            fill: am5.color(0x68ad5c),
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        series.fills.template.setAll({
          fillOpacity: 0.3,
          visible: true,
        });

        series.data.setAll(data);
        chart.set("cursor", am5xy.XYCursor.new(root, {}));
      }

      // closeGallery function removed - no longer needed
    </script>

    <!-- Add this before the closing body tag -->
    <!-- Spreadsheet Settings Offcanvas -->

    <script>
      // Function to remove a section container
      function removeSectionContainer(button) {
        // Find the section container widget
        const sectionContainer = button.closest(".section-container-widget");
        if (!sectionContainer) {
          console.error("Could not find section container");
          return;
        }

        // Find the grid-stack-item that contains this section
        const gridItem = sectionContainer.closest(".grid-stack-item");
        if (!gridItem) {
          console.error("Could not find grid item");
          return;
        }

        // Find the nested grid inside this section
        const nestedGridContainer = sectionContainer.querySelector(
          ".nested-grid-container"
        );
        if (!nestedGridContainer) {
          console.error("Could not find nested grid container");
          return;
        }

        const nestedGridElement =
          nestedGridContainer.querySelector(".grid-stack");
        if (nestedGridElement) {
          // Find and dispose all chart instances in this section
          const chartContainers =
            nestedGridElement.querySelectorAll(".chart-container");
          chartContainers.forEach((container) => {
            if (container.chart) {
              container.chart.dispose();
            }
          });

          // Find and destroy all Handsontable instances in this section
          const spreadsheetContainers = nestedGridElement.querySelectorAll(
            ".spreadsheet-container"
          );
          spreadsheetContainers.forEach((container) => {
            if (container.hotInstance) {
              container.hotInstance.destroy();
            }
          });

          // Destroy the nested grid instance if it exists
          if (nestedGridElement.gridstack) {
            nestedGridElement.gridstack.destroy(true);
          }
        }

        // Get the main grid instance and remove the section widget
        if (window.grid && window.grid.engine) {
          try {
            window.grid.removeWidget(gridItem, true); // true enables compact
          } catch (error) {
            console.error("Error removing widget:", error);
          }
        } else {
          console.error("Main grid not initialized");
          // Fallback: remove the grid item directly from DOM if grid instance is not available
          gridItem.remove();
        }
      }

      // Function to apply spreadsheet settings
      function applySpreadsheetSettings() {
        const title = document.getElementById("spreadsheetTitle").value;
        const showRowNumbers =
          document.getElementById("showRowNumbers").checked;
        const showColumnHeaders =
          document.getElementById("showColumnHeaders").checked;

        // Find the active spreadsheet container
        const activeContainer = document.querySelector(
          ".spreadsheet-container"
        );
        if (activeContainer && activeContainer.hotInstance) {
          const hot = activeContainer.hotInstance;

          // Update settings
          hot.updateSettings({
            rowHeaders: showRowNumbers,
            colHeaders: showColumnHeaders,
          });

          // Update title if provided
          if (title) {
            const headerDiv = activeContainer
              .closest(".grid-stack-item")
              .querySelector(".widget-header mb-2 div:first-child");
            if (headerDiv) {
              headerDiv.innerHTML = `<i class="las la-table"></i> ${title}`;
            }
          }
        }

        // Close the offcanvas
        const offcanvas = bootstrap.Offcanvas.getInstance(
          document.getElementById("spreadsheetSettings")
        );
        if (offcanvas) {
          offcanvas.hide();
        }
      }
    </script>

    <!-- Initialize the main grid -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Wait for GridStack to be available
        if (typeof GridStack !== 'undefined') {
          if (!window.grid) {
            window.grid = GridStack.init(
              {
                column: 12,
                cellHeight: 60,
                margin: 5,
                float: false, // Changed to false to enable auto-compact
                animate: true, // Changed to true for smooth animations
                removable: true,
                removeTimeout: 0,
                disableOneColumnMode: true,
                sizeToContent: false,
                compact: 'up', // Enable upward compacting
              },
              ".grid-stack"
            );
            console.log("Main grid initialized successfully");
          }
        } else {
          console.error("GridStack library not loaded");
        }
      });

      function removeSectionContainer(button) {
        if (!window.grid) {
          console.error("Grid not initialized");
          return;
        }

        // Find the section container widget
        const sectionContainer = button.closest(".section-container-widget");
        if (!sectionContainer) {
          console.error("Could not find section container");
          return;
        }

        // Find the grid-stack-item that contains this section
        const gridItem = sectionContainer.closest(".grid-stack-item");
        if (!gridItem) {
          console.error("Could not find grid item");
          return;
        }

        // Find the nested grid inside this section
        const nestedGridContainer = sectionContainer.querySelector(
          ".nested-grid-container"
        );
        if (!nestedGridContainer) {
          console.error("Could not find nested grid container");
          return;
        }

        const nestedGridElement =
          nestedGridContainer.querySelector(".grid-stack");
        if (nestedGridElement) {
          // Find and dispose all chart instances in this section
          const chartContainers =
            nestedGridElement.querySelectorAll(".chart-container");
          chartContainers.forEach((container) => {
            if (container.chart) {
              try {
                container.chart.dispose();
              } catch (e) {
                console.error("Error disposing chart:", e);
              }
            }
          });

          // Find and destroy all Handsontable instances in this section
          const spreadsheetContainers = nestedGridElement.querySelectorAll(
            ".spreadsheet-container"
          );
          spreadsheetContainers.forEach((container) => {
            if (container.hotInstance) {
              try {
                container.hotInstance.destroy();
              } catch (e) {
                console.error("Error destroying spreadsheet:", e);
              }
            }
          });

          // Destroy the nested grid instance if it exists
          if (nestedGridElement.gridstack) {
            try {
              nestedGridElement.gridstack.destroy(true);
            } catch (e) {
              console.error("Error destroying nested grid:", e);
            }
          }
        }

        try {
          // Remove the widget from the main grid with compact
          window.grid.removeWidget(gridItem, true); // true enables compact
        } catch (error) {
          console.error("Error removing widget:", error);
          // Fallback: remove the grid item directly from DOM
          try {
            gridItem.remove();
          } catch (e) {
            console.error("Error removing grid item from DOM:", e);
          }
        }
      }
    </script>


   


    <!-- <script src="js/smartWidgetComposer.js"></script> -->

    <script src="./js/widget-sidebar.js"></script>





<script>
      // Setup drag-in functionality after DOM is ready
      let textSidebarContent = [
        {
          w: 4,
          h: 4,
          // minW: 2, // to restrict the minimum width
          // minH: 2, // to restrict the minimum height
          get content() {
            // Generate unique ids for drag-in
            const textId =
              "text-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
            const settingsId = "settings-" + textId;
            if (typeof getTextWidgetMarkup === "function") {
              return getTextWidgetMarkup({ textId, settingsId });
            }
            // fallback to old markup if function not loaded
            return `
          <div class="text-widget p-2">
            <div class="widget-header mb-2 fw-bold">
              <i class="las la-font"></i> Text Widget
            </div>
            <div class="text-container">
              <div class="text-content" contenteditable="true" style="
                min-height: 100px;
                padding: 10px;
                border: 1px solid #e0e0e0;
                border-radius: 4px;
                outline: none;
                font-family: Arial, sans-serif;
                font-size: 14px;
                line-height: 1.5;
                color: #333;
                background-color: #fff;
              ">
                <p>Click here to edit text...</p>
              </div>
            </div>
          </div>
          `;
          },
        },
      ];

      let tableSidebarContent = [
        {
          w: 6,
          h: 6,
          get content() {
            const tableId =
              "table-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
            const settingsId = "settings-" + tableId;
            if (typeof getTableWidgetMarkup === "function") {
              return getTableWidgetMarkup({ tableId, settingsId });
            }
            // fallback to old markup if function not loaded
            return `
          <div class="table-widget p-2">
            <div class="widget-header mb-2 fw-bold">
              <i class="las la-table"></i> Table Widget
            </div>
            <div class="table-container">
              <div class="table-responsive">
                <table class="table table-bordered table-striped">
                  <thead>
                    <tr>
                      <th>Column 1</th>
                      <th>Column 2</th>
                      <th>Column 3</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td contenteditable="true">Row 1, Col 1</td>
                      <td contenteditable="true">Row 1, Col 2</td>
                      <td contenteditable="true">Row 1, Col 3</td>
                    </tr>
                    <tr>
                      <td contenteditable="true">Row 2, Col 1</td>
                      <td contenteditable="true">Row 2, Col 2</td>
                      <td contenteditable="true">Row 2, Col 3</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          `;
          },
        },
      ];

      // Line separator widget hidden
      /*
      let lineSeparatorSidebarContent = [
        {
          w: 2,
          h: 1,
          get content() {
            const separatorId =
              "line-separator-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            const settingsId = "settings-" + separatorId;
            if (typeof getLineSeparatorWidgetMarkup === "function") {
              return getLineSeparatorWidgetMarkup({ separatorId, settingsId });
            }
            // fallback to old markup if function not loaded
            return `
          <div class="line-separator-widget p-2">
            <div class="widget-header mb-2 fw-bold">
              <i class="las la-minus"></i> Line Separator
            </div>
            <div class="line-separator-container">
              <div class="line-separator horizontal solid" style="
                border-top: 2px solid #333;
                width: 100%;
                height: 0;
                margin: 20px 0;
              "></div>
            </div>
          </div>
          `;
          },
        },
      ];
      */

      // Image widget hidden
      /*
      let imageSidebarContent = [
        {
          w: 4,
          h: 4,
          get content() {
            const imageId =
              "image-" + Date.now() + "-" + Math.floor(Math.random() * 100000);
            const settingsId = "settings-" + imageId;
            if (typeof getImageWidgetMarkup === "function") {
              return getImageWidgetMarkup({ imageId, settingsId });
            }
            throw new Error("getImageWidgetMarkup is not loaded");
          },
        },
      ];
      */

      let notesSectionSidebarContent = [
        {
          w: 6,
          h: 2,
          get content() {
            const notesId =
              "notes-section-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            const settingsId = "settings-" + notesId;
            if (typeof getNotesSectionWidgetMarkup === "function") {
              return getNotesSectionWidgetMarkup({ notesId, settingsId });
            }
            // fallback to old markup if function not loaded
            return `
          <div class="notes-section-widget p-2">
            <div class="widget-header mb-2 fw-bold">
              <i class="las la-sticky-note"></i> Notes Section
            </div>
            <div class="notes-section-container">
              <footer class="widget-footer">
                <div class="mb-0">
                  <span class="size10 notes-keyInsight" style="display: none">
                    <i class="las la-clipboard size14"></i> Notes :
                    <span class="notes-content">The Smart Cube Knowledge Repository</span>
                    <br>
                  </span>

                  <span class="size10 source-keyInsight" style="">
                    <i class="las la-database size14"></i> Source :
                    <span class="source-content">The Smart Cube Research and Analysis</span>
                    <br>
                  </span>
                  
                  <span class="size10 last-update-keyInsight" style="">
                    <i class="las size14 la-calendar-minus"></i> Last update :
                    <span class="last-update-content">Apr-2025</span>
                  </span>
                  
                  <span class="size10 next-update-keyInsight" style="">
                    <i class="las size14 la-calendar-plus"></i> Next update :
                    <span class="next-update-content">May-2025</span>
                  </span>
                </div>
              </footer>
            </div>
          </div>
          `;
          },
        },
      ];

      let sectionContainerSidebarContent = [
        {
          w: 6,
          h: 6,
          get content() {
            const sectionId =
              "section-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            // Always use the single source of truth for markup
            return typeof getSectionContainerWidgetMarkup === "function"
              ? getSectionContainerWidgetMarkup(sectionId)
              : "";
          },
        },
      ];


      // Section container drag-in also handled by js/widget-drag-drop-setup.js

      // Patch: Enable autoscroll for sidebar drag-in (event delegation)
      setTimeout(function () {
        var sidebar = document.querySelector(".sidebar");
        if (sidebar) {
          sidebar.addEventListener("dragstart", function (e) {
            if (
              e.target.classList &&
              e.target.classList.contains("widget-item")
            ) {
              if (
                window.GridStackAutoScrollAuto &&
                typeof window.GridStackAutoScrollAuto.startExternalDrag ===
                  "function"
              ) {
                window.GridStackAutoScrollAuto.startExternalDrag();
              }
            }
          });
          sidebar.addEventListener("dragend", function (e) {
            if (
              e.target.classList &&
              e.target.classList.contains("widget-item")
            ) {
              if (
                window.GridStackAutoScrollAuto &&
                typeof window.GridStackAutoScrollAuto.stopExternalDrag ===
                  "function"
              ) {
                window.GridStackAutoScrollAuto.stopExternalDrag();
              }
            }
          });
        }
      }, 500);

     
    </script>

    <script>
      // --- Price Analysis 1 Section Builder ---
      async function createPriceAnalysis1Section() {
        // Helper to wait for a DOM element
        function waitForElement(selector, timeout = 5000) {
          return new Promise((resolve, reject) => {
            const start = Date.now();
            function check() {
              const el = document.querySelector(selector);
              if (el) return resolve(el);
              if (Date.now() - start > timeout)
                return reject("Timeout: " + selector);
              setTimeout(check, 50);
            }
            check();
          });
        }

        // 1. Create top-level section container
        const mainSectionWidget = window.addSectionContainerWidget();
        // Wait for the section-content div to be available
        const mainSection = mainSectionWidget.querySelector(".section-content");
        const mainSectionId = mainSection.id;
        // Set header title
        mainSectionWidget.querySelector(".widget-header > div").textContent =
          "Price Analysis 1";

        // 2. Initialize nested grid in main section
        await waitForElement(`#${mainSectionId} .grid-stack`);
        const mainNestedGrid =
          mainSection.querySelector(".grid-stack").gridstack;

        // --- Helper to create a section inside a gridstack ---
        async function createSectionInGrid(grid, title, w, h) {
          const sectionWidget = grid.addWidget({
            x: 0,
            y: 0,
            w,
            h,
            content: `<div class='section-container-widget p-2' style='height: 100%;'>
            <div class='widget-header mb-2 fw-bold d-flex justify-content-between align-items-center'>
              <div>${title}</div>
              <div><button class='btn btn-sm btn-link text-dark ms-1' onclick='removeSectionContainer(this)'><i class='las la-times'></i></button></div>
            </div>
            <div class='section-content' style='height: calc(100% - 40px);'></div>
          </div>`,
          });
          // Give the section-content a unique id
          const sectionContent =
            sectionWidget.querySelector(".section-content");
          sectionContent.id = `${title
            .toLowerCase()
            .replace(/\s+/g, "-")}-content-${Date.now()}`;
          // Initialize nested grid
          window.initSectionContainer(sectionContent.id);
          await waitForElement(`#${sectionContent.id} .grid-stack`);
          return sectionContent;
        }

        // --- 3. Price Forecast Section ---
        const priceForecastSection = await createSectionInGrid(
          mainNestedGrid,
          "Price Forecast",
          12,
          6
        );
        const priceForecastGrid =
          priceForecastSection.querySelector(".grid-stack").gridstack;
        // Add Price Chart Widget
        if (window.addPriceChartWidget) {
          const chartWidget = await window.addPriceChartWidget();
          priceForecastGrid.addWidget(chartWidget);
        }
        // Add two Text Widgets
        if (window.addTextWidget) {
          const textWidget1 = window.addTextWidget();
          priceForecastGrid.addWidget(textWidget1);
          const textWidget2 = window.addTextWidget();
          priceForecastGrid.addWidget(textWidget2);
        }

        // --- 4. Supply Demand Section ---
        const supplyDemandSection = await createSectionInGrid(
          mainNestedGrid,
          "Supply Demand",
          12,
          3
        );
        const supplyDemandGrid =
          supplyDemandSection.querySelector(".grid-stack").gridstack;
        if (window.addTextWidget) {
          const supplyTextWidget = window.addTextWidget();
          supplyDemandGrid.addWidget(supplyTextWidget);
        }

        // --- 5. Other Analysis Section ---
        const otherAnalysisSection = await createSectionInGrid(
          mainNestedGrid,
          "Other Analysis",
          12,
          6
        );
        const otherAnalysisGrid =
          otherAnalysisSection.querySelector(".grid-stack").gridstack;
        // Add Bar Chart Widget
        if (window.addBarChartWidget) {
          const barChartWidget = window.addBarChartWidget();
          otherAnalysisGrid.addWidget(barChartWidget);
        }
        // Add PDF Viewer Widget
        if (window.addPdfViewerWidget) {
          const pdfWidget = window.addPdfViewerWidget();
          otherAnalysisGrid.addWidget(pdfWidget);
        }
      }
      // Run the builder automatically on page load (or call manually)
      // createPriceAnalysis1Section();
      // To run manually, open console and call: createPriceAnalysis1Section();
    </script>

    <script>
      // Patch: Dynamically watch for GridStack drag helper elements and attach listeners
      (function () {
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
              if (
                node.nodeType === 1 &&
                node.classList.contains("ui-draggable-dragging")
              ) {
                // Attach listeners to the helper
                node.addEventListener("dragstart", function () {
                  if (
                    window.GridStackAutoScrollAuto &&
                    typeof window.GridStackAutoScrollAuto.startExternalDrag ===
                      "function"
                  ) {
                    window.GridStackAutoScrollAuto.startExternalDrag();
                  }
                });
                node.addEventListener("dragend", function () {
                  if (
                    window.GridStackAutoScrollAuto &&
                    typeof window.GridStackAutoScrollAuto.stopExternalDrag ===
                      "function"
                  ) {
                    window.GridStackAutoScrollAuto.stopExternalDrag();
                  }
                });
                // Also trigger startExternalDrag immediately (in case dragstart doesn't fire)
                if (
                  window.GridStackAutoScrollAuto &&
                  typeof window.GridStackAutoScrollAuto.startExternalDrag ===
                    "function"
                ) {
                  window.GridStackAutoScrollAuto.startExternalDrag();
                }
              }
            });
          });
        });
        observer.observe(document.body, { childList: true, subtree: true });
      })();
    </script>

  

    <script>
      // ... existing code ...
      let barChartSidebarContent = [
        {
          w: 6,
          h: 8,
          get content() {
            const chartId =
              "barchart-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            // Use the same markup function as click-to-add if available
            if (typeof getBarChartWidgetMarkup === "function") {
              return getBarChartWidgetMarkup({ chartId });
            }
            // fallback markup if function not loaded
            return `
              <div class="bar-chart-widget p-2">
                <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
                  <div>Bar Chart</div>
                  <div>
                    <button class="btn btn-sm btn-link text-dark"
                            data-bs-toggle="offcanvas"
                            data-bs-target="#barChartSettings"
                            aria-controls="barChartSettings"
                            onclick="initBarChartSettings('${chartId}')">
                      <i class="las la-cog"></i>
                    </button>
                    <button class="btn btn-sm btn-link text-dark ms-1"
                            onclick="removeWidget(this)">
                      <i class="las la-times"></i>
                    </button>
                  </div>
                </div>
                <div id="${chartId}" class="chart-container"></div>
              </div>
            `;
          },
        },
      ];

      // Bar chart drag-in now handled by js/widget-drag-drop-setup.js
    </script>

    <script>
      // Add this after your GridStack instance is created
      grid.on("added", function (event, items) {
        items.forEach(function (item) {
          // Look for bar chart widgets in the added item
          const barCharts = item.el.querySelectorAll(".bar-chart-widget");
          barCharts.forEach(function (widget) {
            // Find the chart container inside the widget
            const chartContainer = widget.querySelector(
              '.chart-container[id^="barchart-"]'
            );
            if (chartContainer && chartContainer.id) {
              // Delay to ensure DOM is ready
              setTimeout(function () {
                if (window.initBarChart) {
                  window.initBarChart(chartContainer.id);
                }
              }, 0);
            }
          });
        });
      });
    </script>

    <script>
      // ... existing code ...
      let lineChartSidebarContent = [
        {
          w: 6,
          h: 8,
          get content() {
            const chartId =
              "linechart-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            // Use the same markup function as click-to-add if available
            if (typeof getLineChartWidgetMarkup === "function") {
              return getLineChartWidgetMarkup({ chartId });
            }
            // fallback markup if function not loaded
            return `
              <div class="line-chart-widget p-2">
                <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
                  <div>Line Chart</div>
                  <div>
                    <button class="btn btn-sm btn-link text-dark"
                            data-bs-toggle="offcanvas"
                            data-bs-target="#lineChartSettings"
                            aria-controls="lineChartSettings"
                            onclick="initLineChartSettings('${chartId}')">
                      <i class="las la-cog"></i>
                    </button>
                    <button class="btn btn-sm btn-link text-dark ms-1"
                            onclick="removeWidget(this)">
                      <i class="las la-times"></i>
                    </button>
                  </div>
                </div>
                <div id="${chartId}" class="chart-container"></div>
              </div>
            `;
          },
        },
      ];

      // Line chart drag-in now handled by js/widget-drag-drop-setup.js
    </script>

    <script>
      grid.on("added", function (event, items) {
        items.forEach(function (item) {
          // Look for line chart widgets in the added item
          const lineCharts = item.el.querySelectorAll(".line-chart-widget");
          lineCharts.forEach(function (widget) {
            // Find the chart container inside the widget
            const chartContainer = widget.querySelector(
              '.chart-container[id^="linechart-"]'
            );
            if (chartContainer && chartContainer.id) {
              // Delay to ensure DOM is ready
              setTimeout(function () {
                if (window.initLineChart) {
                  window.initLineChart(chartContainer.id);
                }
              }, 0);
            }
          });
        });
      });
    </script>

    <script>
      // ... existing code ...
      let stackedColumnChartSidebarContent = [
        {
          w: 6,
          h: 8,
          get content() {
            const chartId =
              "stacked-column-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            // Use the same markup function as click-to-add if available
            if (typeof getStackedColumnChartWidgetMarkup === "function") {
              return getStackedColumnChartWidgetMarkup({ chartId });
            }
            // fallback markup if function not loaded
            return `
              <div class="stacked-column-chart-widget p-2" style="height: 100%; display: flex; flex-direction: column;">
                <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
                  <div>Stacked Column Chart</div>
                  <div>
                    <button class="btn btn-sm btn-link text-dark"
                            data-bs-toggle="offcanvas"
                            data-bs-target="#stackedColumnChartSettings"
                            aria-controls="stackedColumnChartSettings"
                            onclick="initStackedColumnChartSettings('${chartId}')">
                      <i class="las la-cog"></i>
                    </button>
                    <button class="btn btn-sm btn-link text-dark ms-1"
                            onclick="removeWidget(this)">
                      <i class="las la-times"></i>
                    </button>
                  </div>
                </div>
                <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
                  <div id="${chartId}" class="chart-container" style="flex: 1; width: 100%; height: 100%; min-height: 300px; position: relative;"></div>
                </div>
              </div>
            `;
          },
        },
      ];

      // Stacked column chart drag-in now handled by js/widget-drag-drop-setup.js
    </script>

    <script>
      grid.on("added", function (event, items) {
        items.forEach(function (item) {
          // Look for stacked column chart widgets in the added item
          const stackedColumnCharts = item.el.querySelectorAll(
            ".stacked-column-chart-widget"
          );
          stackedColumnCharts.forEach(function (widget) {
            // Find the chart container inside the widget
            const chartContainer = widget.querySelector(
              '.chart-container[id^="stacked-column-"]'
            );
            if (chartContainer && chartContainer.id) {
              // Delay to ensure DOM is ready
              setTimeout(function () {
                if (window.initStackedColumnChart) {
                  window.initStackedColumnChart(chartContainer.id);
                }
              }, 0);
            }
          });
        });
      });

      // --- Add after stackedColumnChartSidebarContent and its setup ---

      function getPercentStackedColumnChartWidgetMarkup({ chartId }) {
        return `
        <div class="percent-stacked-column-chart p-2" style="height: 100%; display: flex; flex-direction: column">
          <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
            <div>100% Stacked Column Chart</div>
            <div>
              <button class="btn btn-sm btn-link text-dark"
                      data-bs-toggle="offcanvas"
                      data-bs-target="#percentStackedColumnChartSettings"
                      aria-controls="percentStackedColumnChartSettings"
                      onclick="initPercentStackedColumnChartSettings('${chartId}')">
                <i class="las la-cog"></i>
              </button>
              <button class="btn btn-sm btn-link text-dark ms-1"
                      onclick="removeWidget(this)">
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
            <div id="${chartId}" class="chart-container" style="flex: 1; width: 100%; height: 100%; min-height: 300px; position: relative;"></div>
          </div>
        </div>
      `;
      }

      let percentStackedColumnChartSidebarContent = [
        {
          w: 6,
          h: 8,
          get content() {
            const chartId =
              "percent-stacked-column-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            return getPercentStackedColumnChartWidgetMarkup({ chartId });
          },
        },
      ];
      // Percent stacked column chart drag-in now handled by js/widget-drag-drop-setup.js

      grid.on("added", function (event, items) {
        items.forEach(function (item) {
          const percentStackedCharts = item.el.querySelectorAll(
            ".percent-stacked-column-chart"
          );
          percentStackedCharts.forEach(function (widget) {
            const chartContainer = widget.querySelector(
              '.chart-container[id^="percent-stacked-column-"]'
            );
            if (chartContainer && chartContainer.id) {
              console.log(
                "[percent-stacked-column] Initializing chart for:",
                chartContainer.id,
                chartContainer
              );
              setTimeout(function () {
                if (window.initPercentStackedColumnChart) {
                  window.initPercentStackedColumnChart(chartContainer.id);
                } else {
                  console.warn("initPercentStackedColumnChart is not defined");
                }
              }, 0);
            } else {
              console.warn(
                "No chart container found in percent-stacked-column-chart widget",
                widget
              );
            }
          });
        });
      });

      // 1. Add the Area Chart markup function
      //
      function getAreaChartWidgetMarkup({ chartId }) {
        return `
        <div
          class="area-chart-widget p-2"
          style="height: 100%; display: flex; flex-direction: column"
        >
          <div
            class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center"
          >
            <div>Area Chart</div>
            <div>
              <button
                class="btn btn-sm btn-link text-dark"
                data-bs-toggle="offcanvas"
                data-bs-target="#areaChartSettings"
                aria-controls="areaChartSettings"
                onclick="initAreaChartSettings('${chartId}')"
              >
                <i class="las la-cog"></i>
              </button>
              <button
                class="btn btn-sm btn-link text-dark ms-1"
                onclick="removeWidget(this)"
              >
                <i class="las la-times"></i>
              </button>
            </div>
          </div>
          <div
            class="widget-body"
            style="
              flex: 1 1 auto;
              min-height: 0;
              position: relative;
              display: flex;
              flex-direction: column;
              height: 100%;
            "
          >
            <div
              id="${chartId}"
              class="chart-container"
              style="
                flex: 1;
                width: 100%;
                height: 100%;
                min-height: 300px;
                position: relative;
              "
            ></div>
          </div>
        </div>
        `;
      } // ... existing code ... //
      //
      // 2. Update areaChartSidebarContent to always
      // use the markup

      let areaChartSidebarContent = [
        {
          w: 6,
          h: 8,
          get content() {
            const chartId =
              "areachart-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            return getAreaChartWidgetMarkup({
              chartId,
            });
          },
        },
      ]; // ... existing code ... //
      //
      //
      // Area chart drag-in now handled by js/widget-drag-drop-setup.js // ... existing code ...
      //
      // // 4. Add
      grid.on("added", function (event, items) {
        items.forEach(function (item) {
          const areaCharts = item.el.querySelectorAll(".area-chart-widget");
          areaCharts.forEach(function (widget) {
            const chartContainer = widget.querySelector(
              '.chart-container[id^="areachart-"]'
            );
            if (chartContainer && chartContainer.id) {
              console.log(
                "[area-chart] Initializing chart for:",
                chartContainer.id,
                chartContainer
              );
              setTimeout(function () {
                if (window.initAreaChart) {
                  window.initAreaChart(chartContainer.id);
                } else {
                  console.warn("initAreaChart is not defined");
                }
              }, 0);
            } else {
              console.warn(
                "No chart container found in area-chart-widget",
                widget
              );
            }
          });
        });
      }); // ... existing code ...
    </script>

    <script>
      // ... existing code ...
      // 1. Add the PDF Viewer markup function
      function getPdfViewerWidgetMarkup({ pdfId }) {
        return `
          <div class="pdf-viewer-widget p-2" style="height: 100%; display: flex; flex-direction: column">
            <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
              <div>PDF Viewer</div>
              <div>
                <button class="btn btn-sm btn-link text-dark"
                        data-bs-toggle="offcanvas"
                        data-bs-target="#pdfViewerSettings"
                        aria-controls="pdfViewerSettings"
                        onclick="initPdfViewerSettings('${pdfId}')">
                  <i class="las la-cog"></i>
                </button>
                <button class="btn btn-sm btn-link text-dark ms-1"
                        onclick="removeWidget(this)">
                  <i class="las la-times"></i>
                </button>
              </div>
            </div>
            <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
              <div id="${pdfId}" class="pdf-container" style="flex: 1; width: 100%; height: 100%; min-height: 300px; position: relative; background: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                <span class="text-muted">No PDF loaded</span>
              </div>
            </div>
          </div>
        `;
      }

      let pdfViewerSidebarContent = [
        {
          w: 6,
          h: 8,
          get content() {
            const pdfId =
              "pdfviewer-" +
              Date.now() +
              "-" +
              Math.floor(Math.random() * 100000);
            return getPdfViewerWidgetMarkup({ pdfId });
          },
        },
      ];

      grid.on("added", function (event, items) {
        items.forEach(function (item) {
          const pdfViewers = item.el.querySelectorAll(".pdf-viewer-widget");
          pdfViewers.forEach(function (widget) {
            const pdfContainer = widget.querySelector(
              '.pdf-container[id^="pdfviewer-"]'
            );
            if (pdfContainer && pdfContainer.id) {
              console.log(
                "[pdf-viewer] Initializing PDF viewer for:",
                pdfContainer.id,
                pdfContainer
              );
              setTimeout(function () {
                if (window.initPdfViewer) {
                  window.initPdfViewer(pdfContainer.id);
                } else {
                  console.warn("initPdfViewer is not defined");
                }
              }, 0);
            } else {
              console.warn(
                "No pdf-container found in pdf-viewer-widget",
                widget
              );
            }
          });
        });
      });
      // ... existing code ...

      // ===== BORDER SETTINGS FUNCTIONALITY =====

      // Initialize border settings when the page loads
      document.addEventListener('DOMContentLoaded', function() {
        initBarChartBorderSettings();
      });

      function initBarChartBorderSettings() {
        console.log('🎨 Initializing bar chart border settings...');

        // Current widget reference
        let currentWidget = null;
        let currentBorderSettings = {
          top: false,
          right: false,
          bottom: false,
          left: false,
          style: 'solid',
          width: 1,
          color: '#e5e9f0'
        };

        // Get DOM elements
        const borderPreview = document.getElementById('barChartBorderPreview');
        const borderToggles = document.querySelectorAll('.border-toggle');
        const borderStyle = document.getElementById('barChart-border-style');
        const borderWidth = document.getElementById('barChart-border-width');
        const borderWidthValue = document.getElementById('barChart-border-width-value');
        const colorOptions = document.querySelectorAll('.color-option');
        const borderPresets = document.querySelectorAll('.border-preset');

        // Initialize border toggle buttons
        borderToggles.forEach(toggle => {
          toggle.addEventListener('click', function() {
            const borderSide = this.getAttribute('data-border');
            const isActive = this.classList.contains('active');

            if (isActive) {
              this.classList.remove('active');
              currentBorderSettings[borderSide] = false;
            } else {
              this.classList.add('active');
              currentBorderSettings[borderSide] = true;
            }

            updateBorderPreview();
            applyBorderToWidget();

            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
              this.style.transform = '';
            }, 150);
          });
        });

        // Initialize border style selector
        if (borderStyle) {
          borderStyle.addEventListener('change', function() {
            currentBorderSettings.style = this.value;
            updateBorderPreview();
            applyBorderToWidget();

            // Add visual feedback
            this.style.transform = 'scale(1.02)';
            setTimeout(() => {
              this.style.transform = '';
            }, 200);
          });
        }

        // Initialize border width slider
        if (borderWidth && borderWidthValue) {
          borderWidth.addEventListener('input', function() {
            currentBorderSettings.width = parseInt(this.value);
            borderWidthValue.textContent = this.value + 'px';
            updateBorderPreview();
            applyBorderToWidget();
          });
        }

        // Initialize color options
        colorOptions.forEach(option => {
          option.addEventListener('click', function() {
            // Remove active class from all options
            colorOptions.forEach(opt => opt.classList.remove('active'));

            // Add active class to clicked option
            this.classList.add('active');

            // Update color
            currentBorderSettings.color = this.getAttribute('data-color');
            updateBorderPreview();
            applyBorderToWidget();

            // Add visual feedback
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
              this.style.transform = '';
            }, 150);
          });
        });

        // Initialize preset buttons
        borderPresets.forEach(preset => {
          preset.addEventListener('click', function() {
            const presetType = this.getAttribute('data-preset');

            // Remove active class from all presets
            borderPresets.forEach(p => p.classList.remove('active'));

            // Add active class to clicked preset
            this.classList.add('active');

            // Apply preset
            applyBorderPreset(presetType);

            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
              this.style.transform = '';
              this.classList.remove('active');
            }, 300);
          });
        });

        // Function to apply border presets
        function applyBorderPreset(presetType) {
          switch(presetType) {
            case 'none':
              currentBorderSettings = {
                top: false, right: false, bottom: false, left: false,
                style: 'solid', width: 1, color: '#e5e9f0'
              };
              break;
            case 'all':
              currentBorderSettings.top = true;
              currentBorderSettings.right = true;
              currentBorderSettings.bottom = true;
              currentBorderSettings.left = true;
              break;
            case 'card':
              currentBorderSettings = {
                top: true, right: true, bottom: true, left: true,
                style: 'solid', width: 1, color: '#e5e9f0'
              };
              break;
          }

          updateBorderToggles();
          updateBorderPreview();
          applyBorderToWidget();
        }

        // Function to update border toggle buttons
        function updateBorderToggles() {
          borderToggles.forEach(toggle => {
            const borderSide = toggle.getAttribute('data-border');
            if (currentBorderSettings[borderSide]) {
              toggle.classList.add('active');
            } else {
              toggle.classList.remove('active');
            }
          });
        }

        // Function to update border preview
        function updateBorderPreview() {
          if (!borderPreview) return;

          const { top, right, bottom, left, style, width, color } = currentBorderSettings;

          let borderCSS = '';
          if (top) borderCSS += `border-top: ${width}px ${style} ${color}; `;
          if (right) borderCSS += `border-right: ${width}px ${style} ${color}; `;
          if (bottom) borderCSS += `border-bottom: ${width}px ${style} ${color}; `;
          if (left) borderCSS += `border-left: ${width}px ${style} ${color}; `;

          borderPreview.style.cssText = borderCSS;

          // Add updating animation
          borderPreview.classList.add('updating');
          setTimeout(() => {
            borderPreview.classList.remove('updating');
          }, 1000);
        }

        // Function to apply border to actual widget
        function applyBorderToWidget() {
          if (!currentWidget) return;

          const { top, right, bottom, left, style, width, color } = currentBorderSettings;

          // Find the widget container
          const widgetContainer = currentWidget.querySelector('.widget') || currentWidget;

          if (widgetContainer) {
            // Reset borders
            widgetContainer.style.borderTop = '';
            widgetContainer.style.borderRight = '';
            widgetContainer.style.borderBottom = '';
            widgetContainer.style.borderLeft = '';

            // Apply new borders
            if (top) widgetContainer.style.borderTop = `${width}px ${style} ${color}`;
            if (right) widgetContainer.style.borderRight = `${width}px ${style} ${color}`;
            if (bottom) widgetContainer.style.borderBottom = `${width}px ${style} ${color}`;
            if (left) widgetContainer.style.borderLeft = `${width}px ${style} ${color}`;

            console.log('🎨 Applied border settings to widget:', currentBorderSettings);
          }
        }

        // Function to set current widget (called when settings are opened)
        window.setCurrentBarChartWidget = function(widgetElement) {
          currentWidget = widgetElement;
          console.log('🎨 Set current widget for border settings:', widgetElement);

          // Load existing border settings from widget if any
          loadExistingBorderSettings();
        };

        // Function to load existing border settings from widget
        function loadExistingBorderSettings() {
          if (!currentWidget) return;

          const widgetContainer = currentWidget.querySelector('.widget') || currentWidget;
          if (!widgetContainer) return;

          const computedStyle = window.getComputedStyle(widgetContainer);

          // Check existing borders and update settings
          currentBorderSettings.top = computedStyle.borderTopWidth !== '0px';
          currentBorderSettings.right = computedStyle.borderRightWidth !== '0px';
          currentBorderSettings.bottom = computedStyle.borderBottomWidth !== '0px';
          currentBorderSettings.left = computedStyle.borderLeftWidth !== '0px';

          if (computedStyle.borderTopWidth !== '0px') {
            currentBorderSettings.width = parseInt(computedStyle.borderTopWidth);
            currentBorderSettings.style = computedStyle.borderTopStyle;
            currentBorderSettings.color = rgbToHex(computedStyle.borderTopColor);
          }

          updateBorderToggles();
          updateBorderPreview();

          // Update UI controls
          if (borderWidth) borderWidth.value = currentBorderSettings.width;
          if (borderWidthValue) borderWidthValue.textContent = currentBorderSettings.width + 'px';
          if (borderStyle) borderStyle.value = currentBorderSettings.style;

          // Update color selection
          colorOptions.forEach(option => {
            option.classList.remove('active');
            if (option.getAttribute('data-color') === currentBorderSettings.color) {
              option.classList.add('active');
            }
          });
        }

        // Helper function to convert RGB to Hex
        function rgbToHex(rgb) {
          if (rgb.startsWith('#')) return rgb;

          const result = rgb.match(/\d+/g);
          if (!result || result.length < 3) return '#e5e9f0';

          return '#' + result.slice(0, 3).map(x => {
            const hex = parseInt(x).toString(16);
            return hex.length === 1 ? '0' + hex : hex;
          }).join('');
        }

        console.log('✅ Bar chart border settings initialized successfully');
      }
    </script>


  </body>
</html>
