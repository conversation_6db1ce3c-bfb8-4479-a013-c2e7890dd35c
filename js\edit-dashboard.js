// Initialize dashboard with title from URL
document.addEventListener("DOMContentLoaded", () => {
  console.log("DOM Content Loaded - Starting initialization...");
  
  // Get dashboard title from URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const dashboardTitle = decodeURIComponent(
    urlParams.get("title") || "New Dashboard"
  );

  // Set the dashboard title in the header
  const titleElement = document.querySelector(".page-header h2");
  if (titleElement) {
    titleElement.textContent = dashboardTitle;
  }
  
  // The grid is initialized in the HTML file using GridStack.addGrid
  // You can reference window.grid here if needed for any additional initialization
  
  // Initialize preview functionality
  initializePreviewMode();
});

// Function to load predefined widgets
function loadDefaultWidgets() {
  if (!window.grid) {
    console.error("Grid not initialized, cannot load widgets");
    return;
  }
  
  console.log("Loading predefined widgets");
  
  // First text widget
  window.grid.addWidget({
    x: 0, y: 0, w: 6, h: 4,
    content: `<div class="widget-header">
                <div class="widget-title">Text Widget</div>
                <div class="widget-icons">
                  <i class="fas fa-cog widget-settings"></i>
                  <i class="fas fa-times widget-close"></i>
                </div>
              </div>
              <div class="widget-body">
                <p>This is a sample text widget in your dashboard.</p>
              </div>`
  });
  
  // Chart widget
  window.grid.addWidget({
    x: 6, y: 0, w: 6, h: 4,
    content: `<div class="widget-header">
                <div class="widget-title">Chart Widget</div>
                <div class="widget-icons">
                  <i class="fas fa-cog widget-settings"></i>
                  <i class="fas fa-times widget-close"></i>
                </div>
              </div>
              <div class="widget-body">
                <canvas id="chart-1-canvas"></canvas>
              </div>`
  });
  
  // Section container widget with nested grid
  const sectionWidget = window.grid.addWidget({
    x: 0, y: 4, w: 12, h: 8,
    content: `<div class="widget-header">
                <div class="widget-title">Section Container</div>
                <div class="widget-icons">
                  <i class="fas fa-cog widget-settings"></i>
                  <i class="fas fa-times widget-close"></i>
                </div>
              </div>
              <div class="nested-grid-container"></div>`
  });
  
  // Initialize the nested grid
  const nestedGridContainer = sectionWidget.querySelector('.nested-grid-container');
  if (nestedGridContainer) {
    const nestedGrid = GridStack.init({
      column: 12,
      cellHeight: 'auto',
      disableOneColumnMode: true,
      // Use the same drag handle selector as the main grid
      draggable: {
        handle: '.grid-stack-item-content:not(.handsontable-widget):not(.handsontable):not(.spreadsheet-container)'
      }
    }, nestedGridContainer);
    
    // Add nested widgets
    nestedGrid.addWidget({
      x: 0, y: 0, w: 6, h: 4,
      content: `<div class="widget-header">
                  <div class="widget-title">Nested Widget 1</div>
                  <div class="widget-icons">
                    <i class="fas fa-cog widget-settings"></i>
                    <i class="fas fa-times widget-close"></i>
                  </div>
                </div>
                <div class="widget-body">
                  <p>This is a nested widget inside a section container.</p>
                </div>`
    });
    
    nestedGrid.addWidget({
      x: 6, y: 0, w: 6, h: 4,
      content: `<div class="widget-header">
                  <div class="widget-title">Nested Widget 2</div>
                  <div class="widget-icons">
                    <i class="fas fa-cog widget-settings"></i>
                    <i class="fas fa-times widget-close"></i>
                  </div>
                </div>
                <div class="widget-body">
                  <p>This is another nested widget inside a section container.</p>
                </div>`
    });
  }
}

// Handle clicks on widget icons through event delegation
function handleWidgetIconClick(event) {
  // Find if the click was on a widget icon
  const iconElement = event.target.closest('.widget-icon');
  if (!iconElement) return;
  
  // Get the widget element
  const widgetElement = iconElement.closest('.widget');
  if (!widgetElement) return;
  
  // Determine action based on icon
  if (iconElement.querySelector('.la-cog')) {
    // Settings icon clicked
    const widgetId = widgetElement.querySelector('[id]')?.id || '';
    if (widgetId) {
      configureWidget(widgetId);
    }
  } else if (iconElement.querySelector('.la-times')) {
    // Close icon clicked
    const gridItem = widgetElement.closest('.grid-stack-item');
    if (gridItem) {
      // Instead of removing, we now hide and add to sidebar
      if (typeof hideWidget === 'function') {
        hideWidget(gridItem);
      } else {
        // Fallback to original behavior if our sidebar code is not loaded
        const parentGridElement = gridItem.closest('.grid-stack');
        if (parentGridElement && parentGridElement.gridstack) {
          parentGridElement.gridstack.removeWidget(gridItem);
        } else if (window.grid) {
          window.grid.removeWidget(gridItem);
        }
      }
    }
  }
}

// Widget creation functions
function createPieChartWidget() {
  const widgetId = "pie-chart-" + Date.now();
  return `
        <div class="widget">
            <div class="widget-header">
                <div class="widget-title">
                    <i class="las la-chart-pie"></i>
                    Market Share Analysis
                </div>
                <div class="widget-actions">
                    <button class="btn btn-sm btn-link" onclick="configureWidget('${widgetId}')">
                        <i class="las la-cog"></i>
                    </button>
                    <button class="btn btn-sm btn-link" onclick="removeWidget('${widgetId}')">
                        <i class="las la-times"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body">
                <div id="${widgetId}" class="chart-container"></div>
            </div>
        </div>
    `;
}

function createBarChartWidget() {
  const widgetId = "bar-chart-" + Date.now();
  return `
        <div class="widget">
            <div class="widget-header">
                <div class="widget-title">
                    <i class="las la-chart-bar"></i>
                    Revenue Comparison
                </div>
                <div class="widget-actions">
                    <button class="btn btn-sm btn-link" onclick="configureWidget('${widgetId}')">
                        <i class="las la-cog"></i>
                    </button>
                    <button class="btn btn-sm btn-link" onclick="removeWidget('${widgetId}')">
                        <i class="las la-times"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body">
                <div id="${widgetId}" class="chart-container"></div>
            </div>
        </div>
    `;
}

function createLineChartWidget() {
  const widgetId = "line-chart-" + Date.now();
  return `
        <div class="widget">
            <div class="widget-header">
                <div class="widget-title">
                    <i class="las la-chart-line"></i>
                    Growth Trends
                </div>
                <div class="widget-actions">
                    <button class="btn btn-sm btn-link" onclick="configureWidget('${widgetId}')">
                        <i class="las la-cog"></i>
                    </button>
                    <button class="btn btn-sm btn-link" onclick="removeWidget('${widgetId}')">
                        <i class="las la-times"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body">
                <div id="${widgetId}" class="chart-container"></div>
            </div>
        </div>
    `;
}

function createPdfViewerWidget() {
  const widgetId = "pdf-viewer-" + Date.now();
  return `
        <div class="widget">
            <div class="widget-header">
                <div class="widget-title">
                    <i class="las la-file-pdf"></i>
                    Market Report
                </div>
                <div class="widget-actions">
                    <button class="btn btn-sm btn-link" onclick="configureWidget('${widgetId}')">
                        <i class="las la-cog"></i>
                    </button>
                    <button class="btn btn-sm btn-link" onclick="removeWidget('${widgetId}')">
                        <i class="las la-times"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body">
                <div id="${widgetId}" class="pdf-container"></div>
            </div>
        </div>
    `;
}

// Widget action functions
function addPieChart() {
  grid.addWidget({
    x: 0,
    y: 0,
    w: 4,
    h: 6,
    content: createPieChartWidget(),
  });
}

function addBarChart() {
  grid.addWidget({
    x: 0,
    y: 0,
    w: 4,
    h: 6,
    content: createBarChartWidget(),
  });
}

function addLineChart() {
  grid.addWidget({
    x: 0,
    y: 0,
    w: 4,
    h: 6,
    content: createLineChartWidget(),
  });
}

function addPdfViewer() {
  grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 8,
    content: createPdfViewerWidget(),
  });
}

function configureWidget(widgetId) {
  // This will be implemented based on widget type
  console.log("Configuring widget:", widgetId);
}

// Override the removeWidget function to use our hide functionality
function removeWidget(widgetId) {
  const widget = document.getElementById(widgetId);
  if (!widget) return;
  
  const gridItem = widget.closest('.grid-stack-item');
  if (!gridItem) return;
  
  // Use our hide function if available
  if (typeof hideWidget === 'function') {
    hideWidget(gridItem);
  } else {
    // Fallback to original behavior
    if (window.grid) {
      window.grid.removeWidget(gridItem);
    }
  }
}

// Save dashboard layout
function saveDashboard() {
  const layout = grid.save();
  // TODO: Implement saving to backend
  console.log("Saving dashboard layout:", layout);

  // Show the Preview button after saving as draft
  document.getElementById('preview-btn').style.display = 'inline-flex';
}

// Preview dashboard
function previewDashboard() {
  // Enter preview mode
  enterPreviewMode();
}

// Initialize preview mode functionality
function initializePreviewMode() {
  // Event listeners for buttons
  document.getElementById('save-draft-btn').addEventListener('click', saveDashboard);
  document.getElementById('preview-btn').addEventListener('click', previewDashboard);
  document.getElementById('exit-preview-btn').addEventListener('click', exitPreviewMode);
  document.getElementById('feedback-btn').addEventListener('click', openFeedbackModal);
  document.getElementById('submit-feedback-btn').addEventListener('click', submitFeedback);
  document.getElementById('publish-btn').addEventListener('click', publishDashboard);
}

// Enter preview mode
function enterPreviewMode() {
  // Clone the dashboard content for preview
  const gridContainer = document.getElementById('grid-container');
  const previewContent = document.getElementById('preview-content');

  // Clear previous content
  previewContent.innerHTML = '';

  // Clone the grid container
  const gridClone = gridContainer.cloneNode(true);
  previewContent.appendChild(gridClone);

  // Hide the widget section and header
  document.querySelector('.widget-section').style.display = 'none';
  document.querySelector('.app-header').style.display = 'none';

  // Show the preview container
  document.getElementById('preview-container').style.display = 'flex';
}

// Exit preview mode
function exitPreviewMode() {
  // Hide the preview container
  document.getElementById('preview-container').style.display = 'none';

  // Show the widget section and header
  document.querySelector('.widget-section').style.display = 'block';
  document.querySelector('.app-header').style.display = 'flex';
}

// Open feedback modal
function openFeedbackModal() {
  // Clear previous feedback
  document.getElementById('feedback-text').value = '';
  document.getElementById('feedback-type').value = 'general';

  // Show the modal
  const feedbackModal = new bootstrap.Modal(document.getElementById('feedback-modal'));
  feedbackModal.show();
}

// Submit feedback
function submitFeedback() {
  const feedbackType = document.getElementById('feedback-type').value;
  const feedbackText = document.getElementById('feedback-text').value;

  if (!feedbackText.trim()) {
    alert('Please enter your feedback before submitting.');
    return;
  }

  // TODO: Implement sending feedback to backend
  console.log('Submitting feedback:', {
    type: feedbackType,
    text: feedbackText
  });

  // Close the modal
  const feedbackModal = bootstrap.Modal.getInstance(document.getElementById('feedback-modal'));
  feedbackModal.hide();

  // Show confirmation
  alert('Thank you for your feedback!');
}

// Publish dashboard
function publishDashboard() {
  // TODO: Implement publishing to backend
  console.log('Publishing dashboard');

  // Show confirmation
  if (confirm('Are you sure you want to publish this dashboard?')) {
    alert('Dashboard published successfully!');
    exitPreviewMode();
  }
}
