/* Workspace Feature Styles - Premium Design System */

/* Design Tokens */
:root {
  /* Brand Colors */
  --brand-teal: #00b19c;
  --brand-green: #3bcd3f;
  --brand-dark-teal: #007365;
  --brand-slate: #8dbac4;
  --brand-navy: #02104f;
  --brand-light-teal: #33c1b0;
  --brand-light-green: #62d765;
  --brand-forest: #338f84;
  --brand-light-slate: #a4c8d0;
  --brand-dark-slate: #696f92;
  --brand-aqua: #66d0c4;
  --brand-mint: #89e18c;
  --brand-sage: #66aba3;
  --brand-powder: #bbd6dc;
  --brand-steel: #9b9fb6;

  /* Surfaces */
  --surface-1: #ffffff;
  --surface-2: #f8f9fa;
  --surface-3: #f3f4f6;

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 2px 4px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.1);

  /* Typography */
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
}

/* Workspace Panel */
.workspace-panel {
  width: 400px;
  max-width: 95vw;
  background: var(--surface-1);
  box-shadow: var(--shadow-lg);
  border: none;
  font-family: var(--font-sans);
}

/* Header */
.offcanvas-header {
  padding: var(--space-4);
  background: var(--brand-navy);
  border-bottom: 2px solid var(--brand-teal);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.offcanvas-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  letter-spacing: -0.02em;
}

.offcanvas-title i {
  color: var(--brand-light-teal);
}

.btn-close {
  width: 28px;
  height: 28px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.btn-close:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

/* Navigation */
.workspace-nav {
  padding: var(--space-2) var(--space-4);
  display: flex;
  gap: var(--space-1);
  border-bottom: 2px solid var(--brand-teal);
  background: var(--brand-navy);
}

.workspace-nav-item {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--brand-light-slate);
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  user-select: none;
  border-bottom: 2px solid transparent;
  margin-bottom: -2px;
}

.workspace-nav-item:hover {
  color: var(--brand-light-teal);
  background: rgba(51, 193, 176, 0.1);
  border-bottom-color: var(--brand-light-teal);
}

.workspace-nav-item.active {
  color: var(--brand-teal);
  background: rgba(51, 193, 176, 0.15);
  font-weight: 600;
  border-bottom-color: var(--brand-teal);
}

.workspace-nav-item i {
  color: var(--brand-light-slate);
  transition: all 0.2s ease;
}

.workspace-nav-item:hover i {
  color: var(--brand-light-teal);
}

.workspace-nav-item.active i {
  color: var(--brand-teal);
}

/* Search & Filters */
.workspace-toolbar {
  padding: var(--space-4);
  background: var(--surface-2);
  border-bottom: 1px solid var(--brand-powder);
}

.workspace-search {
  position: relative;
  margin-bottom: var(--space-4);
}

.workspace-search input {
  width: 100%;
  padding: var(--space-3) var(--space-6) var(--space-3) var(--space-12);
  font-size: var(--text-sm);
  color: var(--brand-navy);
  background: var(--surface-1);
  border: 1px solid var(--brand-slate);
  transition: all 0.2s ease;
}

.workspace-search input:hover {
  border-color: var(--brand-teal);
}

.workspace-search input:focus {
  outline: none;
  border-color: var(--brand-teal);
  box-shadow: 0 0 0 2px var(--brand-powder);
}

.workspace-search i {
  position: absolute;
  left: var(--space-4);
  top: 50%;
  transform: translateY(-50%);
  color: var(--brand-slate);
  font-size: var(--text-lg);
  pointer-events: none;
}

/* User Filter */
.workspace-users {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-2);
}

.workspace-user {
  padding: var(--space-3);
  background: var(--surface-1);
  border: 1px solid var(--brand-powder);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
}

.workspace-user:hover {
  border-color: var(--brand-teal);
  background: rgba(51, 193, 176, 0.1);
}

.workspace-user.active {
  background: rgba(51, 193, 176, 0.15);
  border-color: var(--brand-teal);
}

.workspace-user-avatar {
  width: 36px;
  height: 36px;
  background: linear-gradient(
    135deg,
    var(--brand-teal),
    var(--brand-dark-teal)
  );
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--text-sm);
  position: relative;
  overflow: hidden;
}

.workspace-user-avatar::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    var(--brand-teal) 0%,
    var(--brand-light-teal) 25%,
    var(--brand-aqua) 50%,
    var(--brand-sage) 75%,
    var(--brand-forest) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.workspace-user:hover .workspace-user-avatar::before {
  opacity: 1;
}

.workspace-user.active .workspace-user-avatar::before {
  opacity: 1;
  background: linear-gradient(
    45deg,
    var(--brand-teal) 0%,
    var(--brand-light-teal) 50%,
    var(--brand-dark-teal) 100%
  );
}

.workspace-user-name {
  font-size: var(--text-xs);
  font-weight: 500;
  color: var(--brand-navy);
  text-align: center;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Content Area */
.workspace-content {
  padding: var(--space-4);
  background: var(--surface-2);
  min-height: calc(100vh - 200px);
}

/* Item Cards */
.workspace-item {
  background: var(--surface-1);
  margin-bottom: var(--space-3);
  transition: all 0.2s ease;
  border: 1px solid var(--brand-powder);
  position: relative;
}

.workspace-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: var(--brand-teal);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.workspace-item:hover {
  border-color: var(--brand-teal);
}

.workspace-item:hover::before {
  opacity: 1;
}

.workspace-item-content {
  padding: var(--space-4);
  display: flex;
  gap: var(--space-4);
}

.workspace-item-icon {
  width: 40px;
  height: 40px;
  background: var(--brand-powder);
  color: var(--brand-teal);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-lg);
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.workspace-item:hover .workspace-item-icon {
  background: var(--brand-teal);
  color: white;
}

.workspace-item-details {
  flex: 1;
  min-width: 0;
}

.workspace-item-title {
  font-weight: 600;
  color: var(--brand-navy);
  margin-bottom: var(--space-1);
  font-size: var(--text-base);
}

.workspace-item-type {
  color: var(--brand-dark-slate);
  font-size: var(--text-sm);
}

.workspace-item-actions {
  display: flex;
  gap: var(--space-2);
}

.workspace-item-actions button {
  width: 32px;
  height: 32px;
  background: var(--surface-2);
  color: var(--brand-dark-slate);
  border: 1px solid var(--brand-powder);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.workspace-item-actions button:hover {
  background: var(--brand-teal);
  color: white;
  border-color: var(--brand-dark-teal);
}

/* Empty States */
.workspace-alert {
  padding: var(--space-6);
  text-align: center;
  background: var(--surface-1);
  border: 1px dashed var(--brand-powder);
}

.workspace-alert i {
  font-size: 36px;
  color: var(--brand-slate);
  margin-bottom: var(--space-4);
  display: block;
}

.workspace-alert strong {
  display: block;
  font-size: var(--text-lg);
  color: var(--brand-navy);
  margin-bottom: var(--space-2);
  font-weight: 600;
}

.workspace-alert div:last-child {
  color: var(--brand-dark-slate);
  font-size: var(--text-base);
  max-width: 24rem;
  margin: 0 auto;
}

/* Dark Theme */
[data-bs-theme="dark"] {
  --surface-1: var(--brand-navy);
  --surface-2: #1a1f3d;
  --surface-3: #2a2f4d;
}
