/**
 * Price Chart Widget Styles
 * Professional chart styling with interactive controls and forecasting visualization
 */

.price-chart-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.price-chart-widget:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Widget Header */
.price-chart-widget .widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  min-height: 60px;
}

.price-chart-widget .widget-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
}

.price-chart-widget .widget-title i {
  font-size: 20px;
}

.price-chart-widget .widget-actions {
  display: flex;
  gap: 8px;
}

.price-chart-widget .widget-action-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.price-chart-widget .widget-action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* Chart Content */
.price-chart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  height: 100%;
}

/* Chart Toolbar */
.chart-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
  gap: 12px;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.chart-type-select,
.chart-period-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 13px;
  min-width: 120px;
}

.chart-toggles {
  display: flex;
  gap: 16px;
  align-items: center;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #374151;
  cursor: pointer;
  user-select: none;
}

.toggle-label input[type="checkbox"] {
  width: 14px;
  height: 14px;
  accent-color: #3b82f6;
}

/* Chart Info */
.chart-info {
  display: flex;
  align-items: center;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.current-price {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
}

.price-change {
  font-size: 14px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
}

.price-change.positive {
  color: #166534;
  background: #dcfce7;
}

.price-change.negative {
  color: #dc2626;
  background: #fef2f2;
}

/* Chart Container */
.chart-container {
  flex: 1;
  min-height: 300px;
  padding: 0 16px;
  background: white;
}

/* Chart Legend */
.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 12px 16px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #374151;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Settings Panel */
.price-chart-settings {
  padding: 20px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  max-height: 400px;
  overflow-y: auto;
}

.settings-section {
  margin-bottom: 16px;
}

.settings-section label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
}

.settings-section .form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 13px;
  background: white;
  transition: border-color 0.2s ease;
}

.settings-section .form-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.settings-actions {
  display: flex;
  gap: 8px;
  margin-top: 20px;
}

.settings-actions .btn {
  flex: 1;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.settings-actions .btn-primary {
  background: #3b82f6;
  color: white;
}

.settings-actions .btn-primary:hover {
  background: #2563eb;
}

.settings-actions .btn-secondary {
  background: #e5e7eb;
  color: #374151;
}

.settings-actions .btn-secondary:hover {
  background: #d1d5db;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .chart-toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .chart-controls {
    justify-content: center;
  }

  .chart-info {
    justify-content: center;
  }

  .chart-toggles {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .price-chart-widget .widget-header {
    padding: 12px 16px;
  }

  .chart-toolbar {
    padding: 8px 12px;
  }

  .chart-controls {
    gap: 8px;
  }

  .chart-type-select,
  .chart-period-select {
    min-width: 100px;
    font-size: 12px;
  }

  .toggle-label {
    font-size: 11px;
  }

  .current-price {
    font-size: 16px;
  }

  .price-change {
    font-size: 12px;
  }

  .chart-legend {
    gap: 12px;
  }

  .legend-item {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .chart-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .chart-type-select,
  .chart-period-select {
    width: 100%;
  }

  .chart-toggles {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .price-info {
    flex-direction: column;
    align-items: center;
    gap: 6px;
  }

  .chart-legend {
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
}

/* Animation for data updates */
.price-change {
  transition: all 0.3s ease;
}

.price-change.updated {
  transform: scale(1.05);
}

/* Fullscreen styles */
.price-chart-widget:-webkit-full-screen {
  width: 100vw;
  height: 100vh;
  border-radius: 0;
}

.price-chart-widget:fullscreen {
  width: 100vw;
  height: 100vh;
  border-radius: 0;
}

/* Loading states */
.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #64748b;
  font-size: 14px;
}

.chart-loading::before {
  content: "";
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Error states */
.alert {
  padding: 12px 16px;
  border-radius: 6px;
  margin: 16px;
}

.alert-danger {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.alert .btn {
  margin-top: 8px;
  padding: 6px 12px;
  font-size: 12px;
}
