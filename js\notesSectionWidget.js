// Add a notes section widget
function addNotesSectionWidget() {
  console.log("Adding notes section widget");
  const notesId = "notes-section-" + Date.now();
  const settingsId = "settings-" + notesId;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 2,
    content: getNotesSectionWidgetMarkup({ notesId, settingsId }),
  });

  // Create settings panel in the offcanvas container
  const offcanvasContainer = document.getElementById("offcanvasContainer");
  const settingsPanel = document.createElement("div");
  settingsPanel.className = "offcanvas offcanvas-end";
  settingsPanel.id = settingsId;
  settingsPanel.setAttribute("tabindex", "-1");
  settingsPanel.innerHTML = `
    <div class="offcanvas-header">
      <h5 class="offcanvas-title">Notes Section Settings</h5>
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body">
      <!-- Notes Section -->
      <div class="mb-4">
        <div class="form-check form-switch mb-2">
          <input class="form-check-input" type="checkbox" id="${settingsId}-notes-toggle">
          <label class="form-check-label fw-bold" for="${settingsId}-notes-toggle">
            <i class="las la-clipboard"></i> Show Notes
          </label>
        </div>
        <div class="mb-3">
          <label class="form-label">Notes Content</label>
          <textarea class="form-control" id="${settingsId}-notes-content" rows="2" placeholder="Enter notes content">The Smart Cube Knowledge Repository</textarea>
        </div>
      </div>

      <!-- Source Section -->
      <div class="mb-4">
        <div class="form-check form-switch mb-2">
          <input class="form-check-input" type="checkbox" id="${settingsId}-source-toggle" checked>
          <label class="form-check-label fw-bold" for="${settingsId}-source-toggle">
            <i class="las la-database"></i> Show Source
          </label>
        </div>
        <div class="mb-3">
          <label class="form-label">Source Content</label>
          <input type="text" class="form-control" id="${settingsId}-source-content" placeholder="Enter source" value="The Smart Cube Research and Analysis">
        </div>
      </div>

      <!-- Last Update Section -->
      <div class="mb-4">
        <div class="form-check form-switch mb-2">
          <input class="form-check-input" type="checkbox" id="${settingsId}-last-update-toggle" checked>
          <label class="form-check-label fw-bold" for="${settingsId}-last-update-toggle">
            <i class="las la-calendar-minus"></i> Show Last Update
          </label>
        </div>
        <div class="mb-3">
          <label class="form-label">Last Update</label>
          <input type="text" class="form-control" id="${settingsId}-last-update-content" placeholder="Enter last update date" value="Apr-2025">
        </div>
      </div>

      <!-- Next Update Section -->
      <div class="mb-4">
        <div class="form-check form-switch mb-2">
          <input class="form-check-input" type="checkbox" id="${settingsId}-next-update-toggle" checked>
          <label class="form-check-label fw-bold" for="${settingsId}-next-update-toggle">
            <i class="las la-calendar-plus"></i> Show Next Update
          </label>
        </div>
        <div class="mb-3">
          <label class="form-label">Next Update</label>
          <input type="text" class="form-control" id="${settingsId}-next-update-content" placeholder="Enter next update date" value="May-2025">
        </div>
      </div>

      <!-- Apply Button -->
      <button class="btn btn-primary w-100" onclick="applyNotesSectionSettings('${notesId}', '${settingsId}')">
        Apply Changes
      </button>
    </div>
  `;
  offcanvasContainer.appendChild(settingsPanel);

  // Initialize the notes section widget
  window.setTimeout(function () {
    try {
      console.log(
        "Widget added to grid, now initializing notes section widget"
      );
      initNotesSectionWidget(notesId, settingsId);
    } catch (error) {
      console.error("Error initializing notes section widget:", error);
    }
  }, 100);

  return widget;
}

// Returns the HTML markup for a notes section widget, given notesId and settingsId
function getNotesSectionWidgetMarkup({ notesId, settingsId }) {
  return `
    <div class="notes-section-widget p-2">
      <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
        <div>
           <i class="las la-sticky-note"></i> Notes Section
        </div>
        <div>
          <button class="btn btn-sm btn-link text-dark"
                  data-bs-toggle="offcanvas"
                  data-bs-target="#${settingsId}"
                  aria-controls="${settingsId}">
            <i class="las la-cog"></i>
          </button>
          <button class="btn btn-sm btn-link text-dark ms-1"
                  onclick="removeWidget(this)">
            <i class="las la-times"></i>
          </button>
        </div>
      </div>
      <div id="${notesId}" class="notes-section-container">
        <footer class="widget-footer">
          <div class="mb-0">
            <span class="size10 notes-keyInsight" style="display: none">
              <i class="las la-clipboard size14"></i> Notes :
              <span class="notes-content">The Smart Cube Knowledge Repository</span>
              <br>
            </span>

            <span class="size10 source-keyInsight" style="">
              <i class="las la-database size14"></i> Source :
              <span class="source-content">The Smart Cube Research and Analysis</span>
              <br>
            </span>
            
            <span class="size10 last-update-keyInsight" style="">
              <i class="las size14 la-calendar-minus"></i> Last update :
              <span class="last-update-content">Apr-2025</span>
            </span>
            
            <span class="size10 next-update-keyInsight" style="">
              <i class="las size14 la-calendar-plus"></i> Next update :
              <span class="next-update-content">May-2025</span>
            </span>
          </div>
        </footer>
      </div>
    </div>
  `;
}

// Function to initialize the notes section widget
function initNotesSectionWidget(notesId, settingsId) {
  console.log("Initializing notes section widget:", notesId);
  const container = document.getElementById(notesId);

  if (!container) {
    console.error("Notes section container not found:", notesId);
    return;
  }

  // Set up real-time updates for content inputs
  const contentInputs = [
    { id: `${settingsId}-notes-content`, target: ".notes-content" },
    { id: `${settingsId}-source-content`, target: ".source-content" },
    { id: `${settingsId}-last-update-content`, target: ".last-update-content" },
    { id: `${settingsId}-next-update-content`, target: ".next-update-content" },
  ];

  contentInputs.forEach((input) => {
    const inputElement = document.getElementById(input.id);
    if (inputElement) {
      inputElement.addEventListener("input", function () {
        const targetElement = container.querySelector(input.target);
        if (targetElement) {
          targetElement.textContent = this.value;
        }
      });
    }
  });

  // Set up toggle switches for real-time visibility updates
  const toggles = [
    { id: `${settingsId}-notes-toggle`, target: ".notes-keyInsight" },
    { id: `${settingsId}-source-toggle`, target: ".source-keyInsight" },
    {
      id: `${settingsId}-last-update-toggle`,
      target: ".last-update-keyInsight",
    },
    {
      id: `${settingsId}-next-update-toggle`,
      target: ".next-update-keyInsight",
    },
  ];

  toggles.forEach((toggle) => {
    const toggleElement = document.getElementById(toggle.id);
    if (toggleElement) {
      toggleElement.addEventListener("change", function () {
        const targetElement = container.querySelector(toggle.target);
        if (targetElement) {
          targetElement.style.display = this.checked ? "" : "none";
        }
      });
    }
  });

  console.log("Notes section widget initialized:", notesId);
}

// Function to apply notes section settings
function applyNotesSectionSettings(notesId, settingsId) {
  const container = document.getElementById(notesId);
  if (!container) return;

  // Get all settings values
  const settings = {
    notes: {
      visible: document.getElementById(`${settingsId}-notes-toggle`).checked,
      content: document.getElementById(`${settingsId}-notes-content`).value,
    },
    source: {
      visible: document.getElementById(`${settingsId}-source-toggle`).checked,
      content: document.getElementById(`${settingsId}-source-content`).value,
    },
    lastUpdate: {
      visible: document.getElementById(`${settingsId}-last-update-toggle`)
        .checked,
      content: document.getElementById(`${settingsId}-last-update-content`)
        .value,
    },
    nextUpdate: {
      visible: document.getElementById(`${settingsId}-next-update-toggle`)
        .checked,
      content: document.getElementById(`${settingsId}-next-update-content`)
        .value,
    },
  };

  // Apply settings
  const notesSpan = container.querySelector(".notes-keyInsight");
  const sourceSpan = container.querySelector(".source-keyInsight");
  const lastUpdateSpan = container.querySelector(".last-update-keyInsight");
  const nextUpdateSpan = container.querySelector(".next-update-keyInsight");

  if (notesSpan) {
    notesSpan.style.display = settings.notes.visible ? "" : "none";
    const notesContent = container.querySelector(".notes-content");
    if (notesContent) notesContent.textContent = settings.notes.content;
  }

  if (sourceSpan) {
    sourceSpan.style.display = settings.source.visible ? "" : "none";
    const sourceContent = container.querySelector(".source-content");
    if (sourceContent) sourceContent.textContent = settings.source.content;
  }

  if (lastUpdateSpan) {
    lastUpdateSpan.style.display = settings.lastUpdate.visible ? "" : "none";
    const lastUpdateContent = container.querySelector(".last-update-content");
    if (lastUpdateContent)
      lastUpdateContent.textContent = settings.lastUpdate.content;
  }

  if (nextUpdateSpan) {
    nextUpdateSpan.style.display = settings.nextUpdate.visible ? "" : "none";
    const nextUpdateContent = container.querySelector(".next-update-content");
    if (nextUpdateContent)
      nextUpdateContent.textContent = settings.nextUpdate.content;
  }

  // Store the settings in the container dataset for persistence
  container.dataset.notesSettings = JSON.stringify(settings);

  // Close the offcanvas
  const offcanvas = bootstrap.Offcanvas.getInstance(
    document.getElementById(settingsId)
  );
  if (offcanvas) {
    offcanvas.hide();
  }
}

// Function to load notes section settings from stored data
function loadNotesSectionSettings(notesId, settingsId) {
  const container = document.getElementById(notesId);
  if (!container || !container.dataset.notesSettings) return;

  try {
    const settings = JSON.parse(container.dataset.notesSettings);

    // Load settings into form elements
    document.getElementById(`${settingsId}-notes-toggle`).checked =
      settings.notes.visible;
    document.getElementById(`${settingsId}-notes-content`).value =
      settings.notes.content;
    document.getElementById(`${settingsId}-source-toggle`).checked =
      settings.source.visible;
    document.getElementById(`${settingsId}-source-content`).value =
      settings.source.content;
    document.getElementById(`${settingsId}-last-update-toggle`).checked =
      settings.lastUpdate.visible;
    document.getElementById(`${settingsId}-last-update-content`).value =
      settings.lastUpdate.content;
    document.getElementById(`${settingsId}-next-update-toggle`).checked =
      settings.nextUpdate.visible;
    document.getElementById(`${settingsId}-next-update-content`).value =
      settings.nextUpdate.content;
  } catch (error) {
    console.error("Error loading notes section settings:", error);
  }
}

// Export the functions to global scope
window.addNotesSectionWidget = addNotesSectionWidget;
window.initNotesSectionWidget = initNotesSectionWidget;
window.applyNotesSectionSettings = applyNotesSectionSettings;
window.loadNotesSectionSettings = loadNotesSectionSettings;
window.getNotesSectionWidgetMarkup = getNotesSectionWidgetMarkup;
