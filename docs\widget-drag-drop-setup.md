# Widget Drag & Drop Setup

A centralized script that handles all GridStack drag-in functionality for your widget section. This script automatically enables drag and drop behavior for all widgets when included.

## 🚀 Quick Start

Simply include the script in your HTML and drag & drop will work automatically:

```html
<script src="js/widget-drag-drop-setup.js"></script>
```

The script is already included in `js-reference.js`, so if you're using that file, you're all set!

## 📦 What It Does

- **Auto-initializes** drag and drop for all widget types
- **Centralized configuration** - all drag-in setup in one place
- **Consistent behavior** across all widgets
- **Fallback markup** if widget markup functions aren't available
- **Console logging** for debugging

## 🎯 Supported Widgets

### ✅ Active Widgets (Drag & Drop Enabled)
- **Text Widget** - `data-widget-type="text"` (centralized setup)
- **Table Widget** - `data-widget-type="table"` (centralized setup)
- **Section Container** - `data-widget-type="section-container"` (centralized setup)
- **Pie Chart** - `data-widget-type="pie-chart"` ⭐ **Self-contained in `js/pieChartWidget.js`**
- **Linked Pie Chart** - `data-widget-type="linked-pie-chart"` ⭐ **Self-contained in `js/linkedPieChartWidget.js`**
- **Horizontal Bar Chart** - `data-widget-type="horizontal-bar-chart"` ⭐ **Self-contained in `js/horizontalBarChartWidget.js`**
- **Column Chart** - `data-widget-type="column-chart"` ⭐ **Self-contained in `js/columnChartWidget.js`**
- **Dual Axis Chart** - `data-widget-type="dual-axis-chart"` ⭐ **Self-contained in `js/dualAxisChartWidget.js`**
- **Bar Chart** - `data-widget-type="bar-chart"` (centralized setup)
- **Line Chart** - `data-widget-type="line-chart"`
- **Stacked Column Chart** - `data-widget-type="stacked-column-chart"`
- **100% Stacked Column Chart** - `data-widget-type="percent-stacked-column-chart"`
- **Area Chart** - `data-widget-type="area-chart"`
- **PDF Viewer** - `data-widget-type="pdf-viewer"`

### 🚫 Hidden Widgets (Available but Commented Out)
- **Line Separator** - `data-widget-type="line-separator"`
- **Notes Section** - `data-widget-type="notes-section"`
- **Image Widget** - `data-widget-type="image"`

## 🔧 How It Works

### 1. Automatic Initialization
```javascript
// Auto-initializes when script loads
WidgetDragDropSetup.init();
```

### 2. Widget Detection
The script looks for widgets with specific `data-widget-type` attributes:
```html
<div class="widget-item grid-stack-item widget-category-insert" 
     data-widget-type="text" 
     onclick="addTextWidget()">
```

### 3. GridStack Setup
For each widget type, it calls:
```javascript
GridStack.setupDragIn(
  '.widget-item[data-widget-type="text"]',  // Selector
  undefined,                                // Options
  textSidebarContent                       // Content configuration
);
```

## ⭐ **Self-Contained Widget Pattern (Recommended)**

The **Pie Chart Widget** and **Linked Pie Chart Widget** demonstrate the new recommended pattern where both `onclick` and drag-drop functionality are contained in the same widget file (`js/pieChartWidget.js` and `js/linkedPieChartWidget.js`). This ensures:

- ✅ **Consistent markup** between click and drag
- ✅ **Single source of truth** for widget logic
- ✅ **Easier maintenance** - everything in one place
- ✅ **No markup duplication** or inconsistencies

### Example: Pie Chart Implementation
```javascript
// Shared markup function
function getPieChartWidgetMarkup(chartId) {
  return `<div class="widget">...</div>`;
}

// Click handler uses shared markup
function addPieChartWidget() {
  const chartId = `piechart-${Date.now()}`;
  grid.addWidget({
    content: getPieChartWidgetMarkup(chartId)
  });
}

// Drag-drop setup uses same markup
function setupPieChartDragDrop() {
  const sidebarContent = [{
    get content() {
      const chartId = "piechart-" + Date.now();
      return getPieChartWidgetMarkup(chartId); // Same function!
    }
  }];
  GridStack.setupDragIn('.widget-item[data-widget-type="pie-chart"]', undefined, sidebarContent);
}
```

## 🎨 Adding New Widgets

### Option 1: Self-Contained (Recommended)
Create everything in your widget's JS file like the pie chart example above.

### Option 2: Centralized Setup
To add a new widget type to the centralized system:

1. **Add the setup method** to `WidgetDragDropSetup`:
```javascript
setupMyNewWidget() {
  const myWidgetSidebarContent = [
    {
      w: 4,  // Width in grid units
      h: 3,  // Height in grid units
      get content() {
        const widgetId = "my-widget-" + Date.now();
        return `<div class="my-widget">...</div>`;
      },
    },
  ];

  GridStack.setupDragIn(
    '.widget-item[data-widget-type="my-widget"]',
    undefined,
    myWidgetSidebarContent
  );
  console.log('🎯 My widget drag-in setup complete');
},
```

2. **Call it in setupAllWidgets()**:
```javascript
setupAllWidgets() {
  // ... existing widgets ...
  this.setupMyNewWidget();
}
```

3. **Add the widget to your gallery** with the correct `data-widget-type`:
```html
<div class="widget-item grid-stack-item widget-category-insert" 
     data-widget-type="my-widget" 
     onclick="addMyWidget()">
  <div class="widget-icon">
    <i class="las la-star"></i>
  </div>
  <div class="widget-label">My Widget</div>
</div>
```

## 🔄 Enabling Hidden Widgets

To enable the hidden widgets (Line Separator, Notes Section, Image Widget):

1. **Uncomment the setup calls** in `setupAllWidgets()`:
```javascript
setupAllWidgets() {
  // ... existing widgets ...
  
  // Enable hidden widgets
  this.setupLineSeparatorWidget();
  this.setupNotesSectionWidget();
  this.setupImageWidget();
}
```

2. **Remove `d-none` class** from the widget gallery items in `widgetsection.js`:
```html
<!-- Change this: -->
<div class="widget-item grid-stack-item widget-category-insert d-none" 
     data-widget-type="line-separator">

<!-- To this: -->
<div class="widget-item grid-stack-item widget-category-insert" 
     data-widget-type="line-separator">
```

## 🐛 Debugging

The script provides console logging for each widget setup:
```
🚀 Initializing Widget Drag & Drop Setup...
📦 Setting up drag-in for all widgets...
📝 Text widget drag-in setup complete
📊 Table widget drag-in setup complete
📦 Section container widget drag-in setup complete
...
✅ All widget drag-in setup complete!
```

## 🔧 Manual Control

If you need manual control over initialization:
```javascript
// Disable auto-initialization by commenting out:
// WidgetDragDropSetup.init();

// Then manually initialize when needed:
window.WidgetDragDropSetup.init();

// Or setup individual widgets:
window.WidgetDragDropSetup.setupTextWidget();
```

## 📋 Requirements

- **GridStack.js** - Must be loaded before this script
- **Widget markup functions** - Optional, fallback markup provided
- **Widget gallery** - Must have correct `data-widget-type` attributes

## 🎯 Benefits

- **Single source of truth** for all drag-in configurations
- **Easy maintenance** - add/remove widgets in one place
- **Consistent behavior** across all widget types
- **Automatic initialization** - no manual setup required
- **Fallback support** - works even if widget functions aren't loaded
- **Debug friendly** - clear console logging
