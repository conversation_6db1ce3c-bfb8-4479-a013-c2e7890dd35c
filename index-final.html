<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Digital Asset</title>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet" />
  <!-- Font Awesome for icons -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
  <link rel="stylesheet"
    href="https://maxst.icons8.com/vue-static/landings/line-awesome/line-awesome/1.3.0/css/line-awesome.min.css" />

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
  <!-- GridStack CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/gridstack@10.3.1/dist/gridstack.min.css" />
  <!-- GridStack extra CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/gridstack@10.3.1/dist/gridstack-extra.min.css" />
  <link rel="stylesheet" href="https://amplifipro.wnsprocurement.com/public/plugins/pdfjs/web/viewer.php">
  <link rel="stylesheet" href="https://amplifipro.thesmartcube.com/public/css/ion.rangeSlider.min.css?v=2.2.32">
  <link rel="stylesheet" href="./css/skelton2.css" />
  <link rel="stylesheet" href="./inline-raita.css" />
  <link rel="stylesheet" href="./css/defaultbs.css" />
  <link rel="stylesheet" href="./css/custom-design.css" />
  <!-- Widget Sidebar CSS -->
  <link rel="stylesheet" href="./css/widget-sidebar.css" />

  <!-- Preview Mode Styles -->
  <style>
    /* Preview mode overlay */
    .preview-overlay {
      position: fixed;
      top: 100px;
      right: 0px;
      z-index: 9999;
      background-color: rgba(0, 177, 156, 0.9);
      color: white;
      padding: 10px 15px;
      border-radius: 0px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      display: none;
      transition: all 0.3s ease;
    }

    .preview-overlay p {
      margin: 0;
      font-weight: 500;
      margin-bottom: 8px;
      font-size: 14px;
    }

    .preview-overlay button {
      background: white;
      color: #00b19c;
      border: none;
      padding: 5px 12px;
      border-radius: 0px;
      font-weight: 500;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .preview-overlay button:hover {
      background: #f0f0f0;
    }

    /* Preview mode styles */
    body.preview-mode .grid-stack-placeholder,
    body.preview-mode .grid-stack>.grid-stack-item>.ui-resizable-handle,
    body.preview-mode .grid-stack-item.ui-draggable-handle {
      display: none !important;
    }

    /* Only hide close buttons with la-times icon instead of all widget icons */
    body.preview-mode .widget-icons a i.las.la-times {
      display: none !important;
    }

    /* Also hide the parent link of close buttons */
    body.preview-mode .widget-icons a:last-child {
      display: none !important;
    }

    body.preview-mode .widget-header {
      cursor: default !important;
    }

    body.preview-mode .grid-stack-item {
      cursor: auto !important;
    }

    body.preview-mode .grid-stack-item-content {
      cursor: auto !important;
      pointer-events: auto !important;
    }

    body.preview-mode .card {
      transition: box-shadow 0.3s ease;
    }

    /* Reduce spacing in preview mode */
    body.preview-mode .container-fluid {
      padding-left: 10px !important;
      padding-right: 10px !important;
    }

    body.preview-mode .grid-stack {
      margin: -3px !important;
    }

    body.preview-mode .row {
      margin-left: -5px !important;
      margin-right: -5px !important;
    }

    body.preview-mode .col-12 {
      padding-left: 5px !important;
      padding-right: 5px !important;
    }

    body.preview-mode .card-body {
      padding: 0.75rem !important;
    }

    /* Ensure scrolling works in preview mode */
    body.preview-mode {
      overflow: auto !important;
      position: static !important;
      /* Ensure body is not fixed */
      background-color: white !important;
      /* Change background to white in preview mode */
    }

    body.preview-mode .container-fluid,
    body.preview-mode .grid-stack,
    body.preview-mode .card-body {
      overflow: visible !important;
      pointer-events: auto !important;
    }

    body.preview-mode .header-btn.secondary {
      border: 1px solid rgba(0, 177, 156, 0.3);
    }

    body.preview-mode .header-btn.primary {
      background-color: transparent;
      color: #00b19c;
      border: 1px solid #00b19c;
    }

    body.preview-mode #preview-btn {
      background-color: #00b19c;
      color: white;
      border: 1px solid #00b19c;
    }

    /* Additional preview mode improvements */
    body.preview-mode .app-header {
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    }

    body.preview-mode .dashboard-title {
      margin-left: 5px !important;
      font-size: 22px !important;
    }

    body.preview-mode .py-2 {
      padding-top: 0.5rem !important;
      padding-bottom: 0.3rem !important;
    }

    /* Responsive max height for the commodity news scroll area */
    .ciScrollNews {
      max-height: 350px;
      /* Adjust as needed for your layout */
      overflow-y: auto;
    }

    @media (max-width: 991px) {
      .ciScrollNews {
        max-height: 220px;
        /* Smaller height for tablets */
      }
    }

    @media (max-width: 575px) {
      .ciScrollNews {
        max-height: 140px;
        /* Even smaller for mobile */
      }
    }

    /* Flexbox layout for grid-stack-item-content and card */
    .grid-stack-item-content,
    .card.h-100 {
      height: 100% !important;
      display: flex;
      flex-direction: column;
    }

    /* Make card-body fill available space and allow scroll area to grow/shrink */
    .card.h-100>.card-body {
      flex: 1 1 auto;
      display: flex;
      flex-direction: column;
      min-height: 0;
      /* Important for flex children to shrink */
    }

    /* Make ciScrollNews take remaining space and scroll */
    .ciScrollNews {
      flex: 1 1 auto;
      min-height: 0;
      overflow-y: auto;
      max-height: unset;
      /* Remove fixed max-height if using flex */
    }

    .footer {
      padding: 8px 0;
      color: #fff;
    }

    .footer a {
      color: #fff;
    }

    #scroll {
      text-align: center;
      width: 30px;
      height: 30px;
      background: #02104f;
      border-radius: 50%;
      margin: 0 auto;
      /* margin-top: -55px; */
      /* margin-right: -25px; */
      color: white;
      z-index: 99999 !important;
      padding-top: 0px;
      font-size: 16px;
      line-height: 6px;
      cursor: pointer;
      position: absolute;
      right: 50%;
      -webkit-transition: -webkit-transform 0.6s ease;
      -moz-transition: -moz-transform 0.6s ease;
      transition: transform 0.6s ease;
      bottom: 4px;
      z-index: 99;
    }

    #scroll.clicked {
      -webkit-transform: rotate(360deg);
      -moz-transform: rotate(360deg);
      -o-transform: rotate(360deg);
      -ms-transform: rotate(360deg);
    }

    #scroll.rotate {
      -webkit-transform: rotate(180deg);
      -moz-transform: rotate(180deg);
      -o-transform: rotate(180deg);
      -ms-transform: rotate(3180deg);
    }

    #scroll span {
      position: relative;
      bottom: -10px;
    }

    .arrow-bounce {
      -webkit-animation: arrow 1s infinite;
      -moz-animation: arrow 1s infinite;
      -o-animation: arrow 1s infinite;
      animation: arrow 1s infinite;
      animation-timing-function: cubic-bezier(0.4, 0, 0.6, 1);
    }

    @-webkit-keyframes arrow {
      0% {
        bottom: -13px;
      }

      50% {
        bottom: -8px;
      }

      100% {
        bottom: -13px;
      }
    }

    @-moz-keyframes arrow {
      0% {
        bottom: -13px;
      }

      50% {
        bottom: -8px;
      }

      100% {
        bottom: -13px;
      }
    }

    @keyframes arrow {
      0% {
        bottom: -13px;
      }

      50% {
        bottom: -8px;
      }

      100% {
        bottom: -13px;
      }
    }

    td .irs--round .irs-grid {
      display: none !important;
    }

    td .irs--round.irs-with-grid {
      height: 48px;
      margin-top: -10px;
    }

    td .irs--round .irs-from,
    td .irs--round .irs-to,
    td .irs--round .irs-single {
      font-size: 12px;
      line-height: 1;
      text-shadow: none;
      padding: 10px 5px;
      background-color: transparent;
      color: #007365;
      border-radius: none;
      font-size: 12px;
      font-weight: normal;
    }

    td .irs--round .irs-line {
      top: 27px;
      height: 5px;
      background-color: #d6e3e7;
      border-radius: 0;
    }

    td .irs--round .irs-bar {
      top: 27px;
      height: 5px;
      background-color: #3bcd3f !important;
    }

    td .irs--round .irs-handle {
      top: 25px;
      width: 10px;
      height: 10px;
      border: 5px solid #3bcd3f !important;
      background-color: #3bcd3f !important;
      border-radius: 15px;
      box-shadow: none;
    }

    .cloudy::after {
      content: " ";
      position: absolute;
      bottom: 0;
      left: 30px;
      width: 96%;
      height: 10px;
      height: 4px;
      background-color: #ffffff00;
      border-radius: 10px;
      box-shadow: 1px -20px 20px 18px rgb(255 255 255 / 83%);
      z-index: 999;
    }
  </style>
</head>

<body>
  <!-- AmplifiPro Header  -->
  <div class="themeBorderBottom">
    <div class="container-fluid">
      <style>
        .text-danger {
          color: #3bcd3f !important;
        }

        .recBtn {
          background: transparent;
          border: none;
          font-size: 18px;
          position: absolute;
          top: 6px;
          right: 59px;
          cursor: pointer;
          color: #6c757d;
          z-index: 0;
          height: 23px;
          padding-top: 0px;
          z-index: 999;
        }

        #backgroundOverlay {
          background: rgba(0, 0, 0, 0);
          transition: background 0.2s;
          height: 100%;
          left: 0;
          position: fixed;
          top: 80px;
          width: 100%;
          z-index: 5;
          display: none;
        }

        ul li.dropdown:hover+#backgroundOverlay {
          background: rgba(0, 0, 0, .1) !important;
          display: block;
        }


        .leftNavSec a {
          line-height: 22px;
          font-size: 12.8px;
          // font-weight: bold;
          border-bottom: 1px solid transparent !important;
        }

        #tsc_nav_1 .dropdown-item {
          padding: 0.4rem 0.4rem !important;
        }

        .desktop.navbar .input-group {
          background: #f7f7f7;
          cursor: pointer;
        }

        .modified-new-select-modified {
          background: #fff;

        }

        .recBtn {
          top: 5px !important;
          right: 56px !important;

        }
      </style>
      <nav class="desktop navbar navbar-expand-lg navbar-light px-0" id="tsc_nav_1">
        <a id="navbar-brand-header" class="navbar-brand">
          <img class="UserActivityLogger" style="cursor: pointer"
            src="https://d29wfqajwlhhm6.cloudfront.net/Amplifipro-UAT/logo.svg"
            onclick="window.location='https://amplifipro-qa.tsclabs.in/login'" actionname="Link click"
            sectionofpage="Header" pagename="Home" comment="AmpliiPRO logo clicked.">
          <span style="cursor: pointer;color: #007365 ;" data-tier-name=""
            class="UserActivityLogger tierName tier-plan " data-bs-toggle="tooltip" actionname="Link click"
            sectionofpage="Logo Header" pagename="Home" comment="Enterprise plan clicked"
            data-bs-original-title="View plans">Enterprise</span>
        </a>

        <div>
          <div class="d-flex flexEnd"> </div>
          <a class="navbar-toggler" data-bs-toggle="collapse" data-target="#navbarNavDropdown"
            aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation"> <span
              class="navbar-toggler-icon"></span> </a>
          <div class="collapse navbar-collapse justify-content-end" id="navbarNavDropdown">
            <div class="desktop deskFlex">
              <ul class="navbar-nav nohover posRel">
                <li class="nav-item adjustSideBar">
                  <form method="POST" action="https://amplifipro-qa.tsclabs.in/searchresults" id="search-form"
                    autocomplete="off"><input type="hidden" name="_token"
                      value="27goOWLdmlHuy0rBsZ6XYAvwM1BGYprJloN9EkAw">

                    <input type="hidden" id="s_result_type_id" name="s_result_type_id" value="">
                    <div id="search_wrapper" class="input-group border">
                      <div class="input-group-btn search-panel inp">
                        <div class="custom-select modified-new-select-modified">
                          <span style="position: absolute;right: 12px;bottom: 0px;"><i style="font-size: 16px"
                              class="fa fa-caret-down"></i></span>
                          <select id="search_type_list" class="form-control b-0 pl-1 resizing_select" name="type"
                            style="width: 53.6px;">
                            <option value="0" selected="selected">All</option>

                            <option value="0" selected="selected">All</option>
                            <option value="6">CASME insights</option>
                            <option value="37">Category insights</option>
                            <option value="30">Category news</option>
                            <option value="31">Commodity insights </option>
                            <option value="39">Commodity news</option>
                            <option value="12">Commodity prices</option>
                            <option value="10">Cost structures</option>
                            <option value="11">Inflation monitor</option>
                            <option value="4">Mega trends</option>
                            <option value="38">Procurement KPIs/benchmarking</option>
                            <option value="13">Resources</option>
                            <option value="36">Supplier intelligence by CRAFT</option>
                            <option value="5">Tools and templates</option>
                            <option value="14">Topical insights</option>
                          </select>

                          <div class="select-selected">All</div>
                          <div class="select-items select-hide">
                            <div class="same-as-selected">All</div>
                            <div>CASME insights</div>
                            <div>Category insights</div>
                            <div>Category news</div>
                            <div>Commodity insights </div>
                            <div>Commodity news</div>
                            <div>Commodity prices</div>
                            <div>Cost structures</div>
                            <div>Inflation monitor</div>
                            <div>Mega trends</div>
                            <div>Procurement KPIs/benchmarking</div>
                            <div>Resources</div>
                            <div>Supplier intelligence by CRAFT</div>
                            <div>Tools and templates</div>
                            <div>Topical insights</div>
                          </div>
                        </div>
                      </div>
                      <style>
                        .bootstrap-autocomplete {
                          cursor: pointer;
                        }
                      </style>
                      <!-- end of TODO -->
                      <input type="text" class="form-control border-top-0 border-bottom-0 border-right-0 inp m-0 p-1"
                        autocomplete="off" name="searchText" noresultstext=""
                        placeholder="Ask PIA+ and uncover the power of PAL and generative AI" value="" id="searchText">

                      <span class="input-group-btn ps-2 pe-1">

                        <a class="nav-link11 text-primary serbtn sergroup   searchbutton" actionname="Search"
                          sectionofpage="Keyword search" pagename="Top - Menu Search" comment="searched"
                          href="javascript:void(0);">
                          <i class="la la-search la-lg" style="

                                       font-size: 18px !important;
                                        top: 2px;
                                        position: relative;


                                    "></i>

                        </a><img data-bs-toggle="tooltip" data-bs-title="PIA+"
                          src="https://amplifipro-qa.tsclabs.in/public/images/logo1b.svg" style="width: 20px;">
                      </span>


                    </div>

                  </form>
                  <button id="start-record-btn" data-toggle="tooltip" data-title="Start Recording" class="recBtn"
                    aria-label="Start Recording" data-bs-original-title="Start Recording"><i
                      class="las la-microphone"></i></button>
                  <button id="pause-record-btn" data-toggle="tooltip" data-title="Stop Recording" class="recBtn d-none"
                    aria-label="Stop Recording" data-bs-original-title="Stop Recording"><i
                      class="las la-microphone text-danger"></i></button>
                </li>
                <!-- <li class="nav-item serli">
                        <a class="nav-link text-primary sergroup"  href="#" >
                           <i class="la la-search la-lg"></i></a>
                     </li> -->
                <li class="nav-item dropdown">
                  <a class="nav-link btn dropdown-toggle usernav" href="#" id="navbarDropdownMenuLinka"
                    data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"><i
                      class="la la-user la-lg align-middle"></i></a>
                  <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdownMenuLinka">
                    <a class="dropdown-item fw-bold noClick" href="#">Hi, Md</a>
                    <ul class="navbar-nav p-0">
                      <li class="nav-item dropdown dparent p-0 demo">
                        <a class="dropdown-item dropdown-toggle UserActivityLogger" actionname="Link click"
                          sectionofpage="My demos" pagename="Home - Top Menu" href="#" role="button"
                          data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> My demos</a>
                        <div class="dropdown-menu dchild mydemos">

                          <!-- <a class="dropdown-item UserActivityLogger"
                                    actionName="Link click"
                                       sectionofPage="Smart Risk"
                                        pageName="Home - Top Menu"
                                    href="https://smartrisk-qa.tsclabs.in/sr/public/demoLogin?email=************************************" target="_blank">Smart Risk</a> -->


                          <!--  -->

                          <a actionname="Link click" sectionofpage="My Demo" comment="Category Excellence selected."
                            pagename="Home - Top Menu"
                            href="https://mail.google.com/mail/u/0/#sent/QgrcJHrnvrztxSkcxhdhBHTXhJfbwLcbzqB?param=ai1%2fcHbOeajSqLFS%2byIJGD9by5wAKPAxyXV9yiNrEzKW1RBWFJRxd8DFZRFSwmlDtEUkGZlwumFByr2vtqbwj%2bKXxyHpS2HAJxmPIPCuUbM%3d"
                            class="dropdown-item UserActivityLogger" target="_blank">Category Excellence</a><a
                            actionname="Link click" sectionofpage="My Demo"
                            comment="Commodity Intelligence Demo selected." pagename="Home - Top Menu"
                            href="http://tscdelapptest01:8007/?param=ai1%2fcHbOeajSqLFS%2byIJGD9by5wAKPAxyXV9yiNrEzKW1RBWFJRxd8DFZRFSwmlDtEUkGZlwumFByr2vtqbwj%2bKXxyHpS2HAJxmPIPCuUbM%3d"
                            class="dropdown-item UserActivityLogger" target="_blank">Commodity Intelligence Demo</a><a
                            actionname="Link click" sectionofpage="My Demo" comment="GE selected."
                            pagename="Home - Top Menu"
                            href="http://tscdelapptest01:8007/?param=ai1%2fcHbOeajSqLFS%2byIJGD9by5wAKPAxyXV9yiNrEzKW1RBWFJRxd8DFZRFSwmlDtEUkGZlwumFByr2vtqbwj%2bKXxyHpS2HAJxmPIPCuUbM%3d"
                            class="dropdown-item UserActivityLogger" target="_blank">GE</a><a actionname="Link click"
                            sectionofpage="My Demo" comment="ITDepartment selected." pagename="Home - Top Menu"
                            href="https://itdepartment-test.thesmartcube.com/?param=ai1%2fcHbOeajSqLFS%2byIJGD9by5wAKPAxyXV9yiNrEzKW1RBWFJRxd8DFZRFSwmlDtEUkGZlwumFByr2vtqbwj%2bKXxyHpS2HAJxmPIPCuUbM%3d"
                            class="dropdown-item UserActivityLogger" target="_blank">ITDepartment</a><a
                            actionname="Link click" sectionofpage="My Demo" comment="Smart Risk Demo selected."
                            pagename="Home - Top Menu"
                            href="https://smartrisk-qa.tsclabs.in/sr/public/demoLogin?email=************************************&amp;userId=NjczOA==&amp;token=PJ5MNHre6rkEAm-20250703-170755&amp;systemId=ODc5&amp;clientId=MQ=="
                            class="dropdown-item UserActivityLogger" target="_blank">Smart Risk Demo</a><a
                            actionname="Link click" sectionofpage="My Demo" comment="Supplier Engagement selected."
                            pagename="Home - Top Menu"
                            href="http://thesmartnet/?param=ai1%2fcHbOeajSqLFS%2byIJGD9by5wAKPAxyXV9yiNrEzKW1RBWFJRxd8DFZRFSwmlDtEUkGZlwumFByr2vtqbwj%2bKXxyHpS2HAJxmPIPCuUbM%3d"
                            class="dropdown-item UserActivityLogger" target="_blank">Supplier Engagement</a>
                        </div>
                      </li>

                    </ul>
                    <!-- Auhtor Prashant sutail / US 44945 Solutions menu restructuring -->
                    <div>
                      <ul class="navbar-nav p-0">
                        <li class="nav-item dropdown dparent p-0 demo mysol-view">
                          <a class="dropdown-item dropdown-toggle UserActivityLogger" actionname="Link click"
                            sectionofpage="My solutions" pagename="Home - Top Menu"
                            comment="Click on Solutions - My solutions link." href="#" id="navbarDropdownMenuLinkc"
                            role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">My
                            solutions</a>

                          <div id="loadSolutions" class="dropdown-menu dchild"
                            aria-labelledby="navbarDropdownMenuLinkc">
                            <div class="scrollSolution"><a id="MyRequestURLElse0" actionname="Link click"
                                sectionofpage="My Demo" comment="Availablesol 3 selected." pagename="Home - Top Menu"
                                class="dropdown-item UserActivityLogger" target="_blank"
                                style="cursor: pointer;">Availablesol 3</a><a id="MyRequestURLElse2"
                                actionname="Link click" sectionofpage="My Demo" comment="Category PRO selected."
                                pagename="Home - Top Menu" class="dropdown-item UserActivityLogger" target="_blank"
                                style="cursor: pointer;">Category PRO</a><a id="MyRequestURLElse9"
                                actionname="Link click" sectionofpage="My Demo" comment="SmartRisk selected."
                                pagename="Home - Top Menu" class="dropdown-item UserActivityLogger" target="_blank"
                                style="cursor: pointer;">SmartRisk</a></div>
                          </div>
                        </li>
                      </ul>
                      <ul class="navbar-nav p-0">
                        <li class="nav-item dropdown dparent p-0" id="MyRequestURL_li">
                          <a id="MyRequestURL" actionname="Link click" sectionofpage="My requests"
                            pagename="Home - Top Menu"
                            href="http://192.168.0.17:8004?param=ai1%2fcHbOeajSqLFS%2byIJGD9by5wAKPAxyXV9yiNrEzKW1RBWFJRxd8DFZRFSwmlDtEUkGZlwumFByr2vtqbwjyRdYtsxffSOVadNEqKD7Ft0H4oCftzw%2bDwwl6MP9Tc6Ca0wUOMP1PwpO9oDvSMd2aC3B2ayVwdL8P1oYf3mVVrdDDs3hPbAcTooSPzUFyyU"
                            class="dropdown-item UserActivityLogger" target="_blank">My requests</a>
                        </li>
                      </ul>
                      <ul class="navbar-nav p-0">
                        <li class="nav-item dropdown dparent p-0" id="MyDeliverablesURL_li">
                          <a id="MyDeliverablesURL" actionname="Link click" sectionofpage="My deliverables"
                            pagename="Home - Top Menu"
                            href="http://192.168.0.17:8004?param=ai1%2fcHbOeajSqLFS%2byIJGD9by5wAKPAxyXV9yiNrEzKW1RBWFJRxd8DFZRFSwmlDtEUkGZlwumFByr2vtqbwjyRdYtsxffSOVadNEqKD7Ft0H4oCftzw%2bDwwl6MP9Tc6dRrFzIu7ylnZuQ0K04yYZm3vh1dj50%2fLpL6SIlXXAntNHuUY89ntGbHghYb5ktZr"
                            class="dropdown-item UserActivityLogger" target="_blank">My deliverables</a>
                        </li>
                      </ul>
                    </div>

                    <a class="dropdown-item UserActivityLogger" actionname="Link click" sectionofpage="My favorites"
                      pagename="Home - Top Menu" comment="My favourite landing page selected."
                      href="https://amplifipro-qa.tsclabs.in/favourite">My favourites</a>
                    <!-- <a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/community">My community</a>                                 -->
                    <a class="dropdown-item UserActivityLogger" actionname="Link click" sectionofpage="Profile"
                      pagename="Home - Top Menu" href="https://amplifipro-qa.tsclabs.in/userProfile">Profile</a>
                    <a class="dropdown-item UserActivityLogger" actionname="Link click" sectionofpage="My searches"
                      pagename="Home - Top Menu" href="https://amplifipro-qa.tsclabs.in/my-searches">My searches</a>
                    <a class="dropdown-item UserActivityLogger" actionname="Link click"
                      sectionofpage="Manage notifications" pagename="Home - Top Menu"
                      href="https://amplifipro-qa.tsclabs.in/manage-notification/eyJpdiI6ImVLcE1Pb3dueVUxQzJlcXRzYjJYVlE9PSIsInZhbHVlIjoiUEhEbjEydjRrM01LaGZRcjZWSDRqdz09IiwibWFjIjoiMjQxMDFlNGE4NzJmMjY2ZTliM2E4NDYwNmIwMTBiMjBhYjQ0OTQ5ZDI5NDM3MmRkOGMzNjVhMTI3MmFhMDE4NSIsInRhZyI6IiJ9">Manage
                      notifications</a>

                    <ul class="navbar-nav p-0">
                      <li class="nav-item dropdown demo dparent p-0">
                        <a class="dropdown-item dropdown-toggle UserActivityLogger" actionname="Link click"
                          sectionofpage="More offerings" pagename="Home - Top Menu"
                          comment="Click on Solutions - More offerings link." href="#" id="navbarDropdownMenuLinkd"
                          role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> More
                          offerings </a>
                        <div class="dropdown-menu dchild" aria-labelledby="navbarDropdownMenuLinkd">
                          <a actionname="Link click" sectionofpage="More offerings"
                            comment="Category Intelligence selected." pagename="Solutions"
                            class="dropdown-item UserActivityLogger" target="_blank"
                            href="https://www.thesmartcube.com/solutions/procurement-supply-chain/category-intelligence/">Category
                            Intelligence</a><a actionname="Link click" sectionofpage="More offerings"
                            comment="Commodity Intelligence selected." pagename="Solutions"
                            class="dropdown-item UserActivityLogger" target="_blank"
                            href="https://www.thesmartcube.com/solutions/procurement-supply-chain/commodity-intelligence/">Commodity
                            Intelligence</a><a actionname="Link click" sectionofpage="More offerings"
                            comment="Procurement Analytics selected." pagename="Solutions"
                            class="dropdown-item UserActivityLogger" target="_blank"
                            href="https://www.thesmartcube.com/solutions/procurement-supply-chain/procurement-analytics/">Procurement
                            Analytics</a><a actionname="Link click" sectionofpage="More offerings"
                            comment="Supplier Risk Intelligence selected." pagename="Solutions"
                            class="dropdown-item UserActivityLogger" target="_blank"
                            href="https://www.thesmartcube.com/solutions/procurement-supply-chain/supplier-risk-intelligence/">Supplier
                            Risk Intelligence</a>
                        </div>
                      </li>
                    </ul>

                    <a class="dropdown-item " actionname="Logout Link click" sectionofpage="Sign out"
                      comment="User logout" pagename="Home - Top Menu" href="https://amplifipro-qa.tsclabs.in/logout"
                      id="logoutCheck" onmouseover="clearStorage();">Sign out</a>

                    <a class="dropdown-item admin-class" href="https://amplifipro-qa.tsclabs.in/tschome">TSC admin</a>
                  </div>
                </li>
                <li class="nav-item ">
                  <a class="nav-link pe-0 UserActivityLogger" data-bs-toggle="tooltip" data-bs-title="WNS Procurement"
                    href="https://www.wnsprocurement.com/" target="_blank" actionname="Link click"
                    pagename="The Smart Cube Logo" sectionofpage="The Smart Cube"
                    comment="thesmartcube.com logo clicked." data-bs-placement="bottom">
                    <img class="hexaIcon"
                      src="https://d29wfqajwlhhm6.cloudfront.net/Amplifipro-UAT/hexalogo_new1.svg"></a>
                </li>
              </ul>
              <ul class="navbar-nav mt-3">
                <!-- Category Menu Top  -->

                <li class="nav-item dropdown MegaMenu">
                  <a class="nav-link dropdown-toggle " href="javascript:void(0);" id="navbarDropdownMenuLink13"
                    role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Category
                    intelligence</a>
                  <div class="dropdown-menu border-0">
                    <main class=" font-size14 bg-white border">
                      <div class="row m-0">
                        <div class="col-sm-2 border-right px-0 leftNavSec" style="
                                       background: #eff9fb;
                                       ">

                          <a class=" d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger"
                            href="https://amplifipro-qa.tsclabs.in/category-insights-dashboard" actionname="Link click"
                            pagename="Top menu" sectionofpage="Category intelligence - Category insights"
                            comment="Category insights page viewed">Category insights</a>

                          <a href="https://amplifipro-qa.tsclabs.in/category-briefings"
                            class="d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger"
                            pagename="Top menu" sectionofpage="Category intelligence -  Category briefings"
                            actionname="Link click" comment="Category briefings page viewed.">Category briefings</a>

                          <a class="d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger rpTypeCSCD"
                            pagename="Top Menu" actionname="Link click"
                            sectionofpage="Category intelligence - Cost structures and drivers"
                            href="https://amplifipro-qa.tsclabs.in/cscd-asset"
                            comment="Cost structures and drivers page viewed." rp_cs_cd="Cost structures and drivers"
                            style="display: block;">Cost structures and drivers</a>

                          <a class="d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger rpTypeCC"
                            pagename="Top Menu" actionname="Link click"
                            sectionofpage="Category intelligence - Cost calculator"
                            href="https://amplifipro-qa.tsclabs.in/costCalculator"
                            comment="Cost calculator page viewed." rp_cs_cd="Cost calculator"
                            style="display: block;">Cost calculator</a>

                          <a class=" d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger"
                            pagename="Top Menu" actionname="Link click"
                            sectionofpage="Category intelligence - Inflation monitor"
                            comment="Inflation monitor page viewed."
                            href="https://amplifipro-qa.tsclabs.in/inflation-indices?cdType=Electricity" id="">Inflation
                            monitor</a>

                          <a href="https://amplifipro-qa.tsclabs.in/reports/list?report_type_id=eyJpdiI6Ik9BMFQ1NmN6MjJDYTl2cUpyMXFHWlE9PSIsInZhbHVlIjoiWFJwM1o5M0R5YmUwUGppRE53YzRaZz09IiwibWFjIjoiMDg0MTYzZDVjNDI5ZDlkOTAwMjNhYTM3MWNiOWNlNDcyZjBiYjczNzdmOTgwYzFkODBhYWEzNTc3Y2Y4OWEwZSIsInRhZyI6IiJ9"
                            class="d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger"
                            pagename="Top menu" sectionofpage="Category intelligence - Mega trends"
                            actionname="Link click" comment="Mega trends page viewed.">Mega trends</a>

                          <a class="d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger"
                            pagename="Top Menu" actionname="Link click"
                            sectionofpage="Category intelligence - Procurement KPIs/benchmarking"
                            comment="Procurement KPIs/benchmarking page viewed."
                            href="https://amplifipro-qa.tsclabs.in/reports/list?report_type_id=eyJpdiI6IlVkZ1Awd3o1eTNpNmVYcVJrempZbnc9PSIsInZhbHVlIjoiK2crTGdWUXZyQ0NWNHpFMDdjTzZHUT09IiwibWFjIjoiMDg2NDI3MzAxYTY0MGZjOWY2YWJjNzQxOGIxMjIxMjQ4ZDliYzc5NmEzYTM5Nzc0MmVhZDY2MTc4YTVjYzRmYiIsInRhZyI6IiJ9"
                            id="procurement_benchmarking_sd_link">Procurement KPIs/benchmarking</a>

                          <a href="https://amplifipro-qa.tsclabs.in/archived_reports"
                            class="d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger"
                            pagename="Top menu" sectionofpage="Category intelligence -  Archived reports"
                            actionname="Link click" comment="Category briefings page viewed.">Archived reports</a>

                          <a href="https://amplifipro-qa.tsclabs.in/reports/list?report_type_id=eyJpdiI6IkozZm53OXZ4N3hxYUZvUnJ2aURCcmc9PSIsInZhbHVlIjoiQzVyVXp1QkYrb1YrZ0YyY0NuSEFoQT09IiwibWFjIjoiMTBhNmM1Yzk2YTJlMDBhOTUzOTlkOGMzYzc5ZjM4YmM0NTBmMzlhNDM2YmZkMGE5YjhkNGMyNjM3NDIwNTZjYSIsInRhZyI6IiJ9"
                            class="d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger"
                            pagename="Top menu" sectionofpage="Category intelligence - CASME insights"
                            actionname="Link click" comment="CASME insights page viewed.">CASME insights </a>

                          <a class="d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger"
                            pagename="Top Menu" actionname="Link click"
                            sectionofpage="Category intelligence - Supplier intelligence by CRAFT"
                            comment="Supplier intelligence by CRAFT page viewed."
                            href="https://amplifipro-qa.tsclabs.in/craft-supplier" id="sd_link1">Supplier intelligence
                            by CRAFT</a>

                        </div>
                        <div class="col-sm-10 pl-4" style="
                                    ">
                          <div class="row">
                            <div class="col-sm-12 pl-2 pt-2">
                              <h4 class="  mb-2" style="font-weight:bold; font-size: 13px;">Categories</h4>
                            </div>
                          </div>
                          <section class="row py-0" id="loadTopCategory">
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6IlZWNTJvNlNDS3RPNEtmUEVrSjZDckE9PSIsInZhbHVlIjoiMDlWeCtxVWY0V3I0ZUx5QXJCVlQxZz09IiwibWFjIjoiMGEzNDgzOTIwNDA5OGQ3OGM0OTdjYzRiNjgzNDU5Mjg5MWZmY2MyMzM5M2ZlMjFhZDM0Y2U1YWM4NzJlMTY5YyIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6IjR2NExHMWpMTUhuVzcxM2tlOUl4ZUE9PSIsInZhbHVlIjoicVMwdXh1TGkydVdNZUJEK09PTEp0NEtEMDJwdkpMNHhLbm1LR2NFSHpJaz0iLCJtYWMiOiJkNWZiODk2OGI2MTY0ZmNlNzg4ZmFlMzBlYjIzNTRlN2Q5NGMxMzU3YzJkZDZiMjg1MTk1NjNjMGUwZjZlNTY3IiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Aerospace and defence equipment and components"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Aerospace and defence equipment and components
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6Ijl4VlZaaUMzanZuNWlUeUNOczkvOGc9PSIsInZhbHVlIjoiRWdxZFdBSmI3WHB4MXZRMG51WEJkUT09IiwibWFjIjoiOWU1NDhlMTJkYzNiMzA0ODI3YjdmZjZjZmNkMjZjMjg2NmIwZjZjZmNiYTMxZWJjZTFiMDQxMGQxZjYzNDQ1MyIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6InZzMmluZWZkSkdPb1JPY1pZOHVHcFE9PSIsInZhbHVlIjoiZWgrQ2lVbFVQd1lBTHhPUzY4M2REYWhPRXNYclAvN1FCcURyWjdQSFB5TT0iLCJtYWMiOiIzOGQ5MDE4ZWU3ODIyZTg5YjhkNzE2N2ExYjRlMjZhMjFkMDFjMThlYmRkYzU2YWFkZDYxNGM0NDZhYTU5YmQ1IiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Agro, meats and ingredients"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Agro, meats and ingredients
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6Ii81ZXdkQkdMWkR3U2dldGhOUTRka3c9PSIsInZhbHVlIjoiYnUzK2YvS2dDTlpEV3NWMjFDeStQQT09IiwibWFjIjoiNjE0ODIwNGQwYzg3ZmJiYTI4NDdkNjMwMjg3ZDAxYTM3YjRlOTcyY2EzMjJjNTg0OTBhMDc5N2QzOGM1ZWE0MSIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6IkxxTVpsWWhxNmsvY0FmTTNlYnhVRHc9PSIsInZhbHVlIjoiSHlUb1gvSVRiK2dwejJQVVB5MDJ5dGg3MWt0QXEzcFZLb3NPaEF0aFdaTT0iLCJtYWMiOiI4NTAwOWJmYzE5NWM3ZjQ1NDc5NDBhOTViZTYzMWFmZjcyM2RkNDE4OTFmNjkyMzZjMmQ5Mzg4YzNhYTAwYmFmIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Ampro_KT_Dec_Bikas"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Ampro_KT_Dec_Bikas
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6Ii9FSlNQWXdDd01OTEE5UGNubnN4eVE9PSIsInZhbHVlIjoiRWRCUVVENTRIekpPaFhib1R2YXNjUT09IiwibWFjIjoiNjhiNWI5MThhYWQ2OWRhNmI2OTcxYTQ3ZGM2MTJmOWJkOTcxNTA4MGVkZjY4NjkwY2YwMjg2N2Y0M2NmZDNiZiIsInRhZyI6IiJ9"
                                data-typeid="6"
                                data-type="eyJpdiI6Ill0QWdHakpXYTZmaEN1alRIMStxakE9PSIsInZhbHVlIjoia1JBczdqMlhmQnJXMUpqcWRRbXYrdjNqNEhxRW9sbzdzaVNGR0svc0FXND0iLCJtYWMiOiJhYTZlODFjOWQ5YTAxZGJiMDEwZTc4ZmYwNjczNWQxNzMzYTk0MDk5OWUyOWJiN2MxNWJlODUzOWNjZmM1OTNkIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Base metals and minerals"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Base metals and minerals
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6Ikg2RjYwQ1V2WmFCTDE2MlpFYkdYU2c9PSIsInZhbHVlIjoiUDYybWxxdlo4STJieTBNc0VraEtNZz09IiwibWFjIjoiZTg1ZmI0ZTc0NGViODEyMTFkZDgwZTAzOTcyMTY2NTY3N2JhODljNmZlMWE4YTAwOTdkNzQxZTAwZTU0ZTBiNSIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6ImdybkxpOGpCaVhuY2N4dlR2SkVaQnc9PSIsInZhbHVlIjoiUjZlU1AzcnM0MTN1TlBEM3VncFdYNXhVbzVkVVBRY3V4V2RhREZRanhjVT0iLCJtYWMiOiIwODlhMmUxM2UxMDI2YWRlNGQ4MjgyMDM3YWYyYjJhYzI4Y2M1OTFjYTY4YjI0MDVmNGMwNzZmYmVhNzc3NTQwIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Business supplies and print"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Business supplies and print
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6ImwxQmZJU3BhYnJ5RS9NR2c4STB1b3c9PSIsInZhbHVlIjoiTjZLRXpxVDZkREt4TW5UMDB4bGJPUT09IiwibWFjIjoiMDgyY2YwYjFkYmUxOTA5MTc2Njk4NDI4ZDhiZTIyNzU1MWFjYjIyZjg5ZDg3YTI2MDVjOGJjYjEzZDE3OWVjYiIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6IjBBcEwybEpIOEN6OUk3VzJmRE1BeFE9PSIsInZhbHVlIjoiUDkvV01QRDdQVDFZRDNEZDQ0ODIzRkdhRG16L3czbzhpSTZIQ2FLYWtlQT0iLCJtYWMiOiI2MTczOWRlMzlmMjk4NTRhYzEwY2FkYTdkNTkyOTEzMGEzMzAwNWFmNzVjYTRmNmUzOWU0Yzg2MmI3NDZkM2Y3IiwidGFnIjoiIn0="
                                actionname="Link click" sectionofpage="Category intelligence - Categories - Chemicals"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Chemicals
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6Ik90Yy9PNmptOWRzN2ZxaExoVUE4eFE9PSIsInZhbHVlIjoiOXBjaThtaEVPL1RDMERISG1MWkJhUT09IiwibWFjIjoiY2UzMmY5YTI0YmViZjBiZTc3Y2IwOGI0ZWIwMDQ1NWJhNDIyYjRkZWI0YzY3YmZhN2Y3ZDRkOTU4MjE2Y2Q2YyIsInRhZyI6IiJ9"
                                data-typeid=""
                                data-type="eyJpdiI6IlVrYWJPb1ZhNHA0RCtzMTNEK2tLVFE9PSIsInZhbHVlIjoiVTZZZFYvcysxV1h3eUxTNHdBMGp1QT09IiwibWFjIjoiNDMxNDA3ZDA4ZjY5N2NkNDIyNjliYzgwYjBkYmZlNDliMDVlOWFlZDA0ZjU4MDIyOGY1NjhjN2JiNDQ4MjYzYyIsInRhZyI6IiJ9"
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Construction and engineering services"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Construction and engineering services
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6Im5nTmNTVmFZeGIzdmtHbExMZFlRZUE9PSIsInZhbHVlIjoibGErcDgxVkNoU09RMmVJOE5pdW4yUT09IiwibWFjIjoiMTNjMmMwMjk1ZTU5NmY5MDM5YjY2ZjEzMGYzZTA1NzcwYWZjZGRmMWY5MGIxMDI0NjQ4ODRiOWJlMzhmNWZjMiIsInRhZyI6IiJ9"
                                data-typeid="6"
                                data-type="eyJpdiI6ImRRVFBnQk5veVk2M0V4TmZzZ0ZuM1E9PSIsInZhbHVlIjoiT1RTOWlVSXVDWnBvMXhFamtQa2IxVnp4TmpqMlpnZmhRNXBIQ293T2RBVT0iLCJtYWMiOiI2YTYzMDk0Zjg3N2M1YmZkM2FjYWJjMGVhOWJiYmExMWFhODdlZDhiZTc4MTU0ZGRlNzYxMWZjM2Y2YmY2MDdmIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Corporate services"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Corporate services
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6IlFHM1A5RnNlaDNPd3BkUVlzL1QycUE9PSIsInZhbHVlIjoiZXFTeW1TbWpIVk1LdGpKS2Zib1FRZz09IiwibWFjIjoiNjA3YjIyN2U5MWNlZmQ0N2NiZWE4MzYyNDk1YmQwZmFmZTM0NmNlYmQ4ZTdjNzJjZGM3NTBkZWU0ZDFkNzk1MCIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6IklMaFRkcGNpdEZJR25iUlNYMzVmOVE9PSIsInZhbHVlIjoicUFGWFk4K0VlYStueGE0U3M1a1dZM2NLNmIzSThUeHRBUXA5VVBSdU5OST0iLCJtYWMiOiJiZWQ4YjJjZmM3ZGVlYmU1MDJlODkxOTc4YWUyZDBmMzExZjIzNWEzN2E5MTIwZWE3ZDY4NTU4NGI0OGMyNWQ4IiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Electrical components and supplies"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Electrical components and supplies
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6InI3SUJBUFpaRVFhcTVCNnFtRE9RMHc9PSIsInZhbHVlIjoibCthTU1PZU1zb3ViaERZY0xXYloxQT09IiwibWFjIjoiMjAxMDE1ZWQ2OWNjNjE2MzRjZjhkZTgxYzI4YmFlY2IxZTJmMTgwMDM1YTYwNTdlYmExYmM5YTMyYTZiYWM0MCIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6InNwVkJ0dDhOVmkzU3BwRDZjQ25IaXc9PSIsInZhbHVlIjoiQUNOT2R4NmJPTldYRHZaTXIxQ1VIZUVpdXhoY3ZzVkM0b0hWY2hpaUxBZz0iLCJtYWMiOiI2YWFlNGVhNDI4MTUzM2E4MjA2MDY5NjdhYjFhNjYxY2UyZGMwMWU0OTFkZjc5OGE4Mzk2NjYzZGQxMzNlYjkyIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Energy and utilities"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Energy and utilities
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6Ikg1VTRzc2VwRWZjWDIrQzF6aEJzdGc9PSIsInZhbHVlIjoidml3NUpUdE5sVU14dTJqSUlEdDVmZz09IiwibWFjIjoiNzA2ZGZiYjEwNzU4ZDdhMjZkYjUzNDA0ZTUxMTE1NjIzYzc2YTc5OWU2NDkyODM4Mzk3MTI2MjhiZDNiOWIxMSIsInRhZyI6IiJ9"
                                data-typeid="6"
                                data-type="eyJpdiI6Ijd5NkZ2dVlHd3NpQmFHMEswdk1BOVE9PSIsInZhbHVlIjoiM2gvdUNqcWpiMlN4Q1JHaVVoelRFbjVKWCtWcnlaVXRTR2h0UFhpdkcvMD0iLCJtYWMiOiJlODRjMTdjYmFmMGMxYWQ1NjY0YjFhOWNkOGY2YTViYzFiMTFkZGVlNmE2MzYzNWU0ZmE0MDczZDM3M2IwMGY2IiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Facilities management and real estate"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Facilities management and real estate
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6ImNScWNZYUVmZVZVNGEvbUU3Um40M1E9PSIsInZhbHVlIjoiWHVrN3NmVjNUckhZQ1hhTEVoKzY0Zz09IiwibWFjIjoiNTJhZGMwM2ExMWRhOWZjMTBiMGVhOGIwODZhMzgyYzEwMzc3OTNjZTc2OWQ1ZTBkMGFlZGYzOGVjYjFlYzI3ZCIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6IlBzeXJlNnNIUkJMOUdCVTFPM3Y4T2c9PSIsInZhbHVlIjoiVlNjdjBRYmRaMnpJbE9iSUFFZzlhaFl0Tit6MGdvR1FINjZYSmNwa0ZmWT0iLCJtYWMiOiJkMGM5NWUzYjNlODJhZDFlODU1YjQwMTU2M2RkZGU4YWQxNmYxYmQ5ZjQzNGExNDdjNWFkMGI3MTdkNzllYzVjIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Forest and wood products"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Forest and wood products
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6InNqS2ZZalpHbmpLZy9NN245Y3BXcFE9PSIsInZhbHVlIjoiU3VnQVUxbTBsR3RMMk1FRVdRUzBVdz09IiwibWFjIjoiOGZiOTIwZjg0MDgxMjBmMTc5NTQzNmU1MWFmOGUwNGFhZjZiNThjMWJhODMyYzU2OTU4NjM4MTJjY2Y2MmY3ZCIsInRhZyI6IiJ9"
                                data-typeid="6"
                                data-type="eyJpdiI6IndWTDE5T3VxSlNZVmtoUjM2RFVYVVE9PSIsInZhbHVlIjoiM2NPTk8wbVhXdVdIOEVxOTNSWEZoeW9RcVNSQVMraGhlOExjMUE2bG16TT0iLCJtYWMiOiI0NDI3MGVhN2ZkMTA4MTAzYTRhMTI4ZjNlN2Y5ZjA0ZDYzYmI4MGQ3N2Q4MzJiZWQ5N2I1NmQ1MmQ1YjdkMmNiIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - IT and telecom" pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                IT and telecom
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6IklWWnQwMFI4N1piMCtQdzkyUWdrcVE9PSIsInZhbHVlIjoiK1FmVFBjODFiY2VvZXBXanFISERGZz09IiwibWFjIjoiZTcxZjQ4MGQ4OWFjOTA3YTRmOWE5MjRiNWQ0MDdmZWI5ZDA2OTJlMTAxYjNiMDJhYjViMzdlMWVkYmNhMzVmOCIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6InhzNTNyOENZR1V0Y2hUaW9mQ1FONGc9PSIsInZhbHVlIjoiWmh4VnBxVjFmcGw0WUkxNkhTZEg3dEszTXlldFQ5VnN3Q0c0RXYybmRkND0iLCJtYWMiOiI0ODQ3M2Y3YTY4ZTI1MTVlMTlkMTZmZDZlY2IzMDEyODYxMGY5Y2RmN2IzNTNkMDEzYWNlYTAxZmU1NzNkZmM0IiwidGFnIjoiIn0="
                                actionname="Link click" sectionofpage="Category intelligence - Categories - KT Category"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                KT Category
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6IlZSeEJMVXZxN0puQ1d1aVBIMnRDRHc9PSIsInZhbHVlIjoiaFgybUZKRjN5b3U1Q25oVE9JUXZjQT09IiwibWFjIjoiODFmNTIzNTY0ODNiNjczYWY3OTE5YTFhNjA1ZWQ3OWM0OTZjMjhmYTM4MTA0ZDU3OWEyNjhkYzI3YTQxMWVmNiIsInRhZyI6IiJ9"
                                data-typeid="6"
                                data-type="eyJpdiI6IlIxeFBycWJ4cmJITjBJREkzOXhxcWc9PSIsInZhbHVlIjoiS2NyNEpYV1VvNThDTXJkc2ZQY3lHd2taR3lEa2g5UjY5OEVDRUZIZUh0VT0iLCJtYWMiOiJmOWQzNzQ0MGRlOTUxZWE3M2ZjZjRmZThiZDAyYjE2NzNkNDY4Zjg5ZmFkODBlZjUwYzEwM2MzMjg5ZDFmYjMzIiwidGFnIjoiIn0="
                                actionname="Link click" sectionofpage="Category intelligence - Categories - Logistics"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Logistics
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6IlYyMHE1WFJ6L0NsS1YzTCtvdFNaS1E9PSIsInZhbHVlIjoiZno0NFk0Wmg1bHdhdU91WmtBRlowdz09IiwibWFjIjoiOGVhMDFhN2Y4NTYzYTdlOWEwYWJlYTJmNGVjMjM4ZjM0YThkYTFmM2U0NDg2OGFlNmUzMzIzZjFiYWIxMjcyMSIsInRhZyI6IiJ9"
                                data-typeid=""
                                data-type="eyJpdiI6IlNlazZsejFZQmVEcXZQZTM2K1BkK3c9PSIsInZhbHVlIjoidlIvVGZnWElLcSs2L29NWFNZTG1mUT09IiwibWFjIjoiODY4YmIxYjA3OTM2ODk0MDEyYzhiMWU3OTJmYjYyNThhYWI5YmE4NjQxMmJhMDU2ZGMyNmEwNmM3NWZjMWU0NCIsInRhZyI6IiJ9"
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Machinery and equipment"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Machinery and equipment
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6IlVxVlB0YWdyOWNFY1dKQ0Q5b0RWU0E9PSIsInZhbHVlIjoiZ0pwY2o4Tzl5N3cyc3RxbTJlVis1dz09IiwibWFjIjoiYjc3YzJhYTgyMzNkMTBkM2FhN2Q4Zjk2MjhkODExNWRjOGU1MzYwYzY1MzliYmQyMzNiNWEzYTZiNzUwMjU2OSIsInRhZyI6IiJ9"
                                data-typeid="6"
                                data-type="eyJpdiI6Ik1UQUp5R2lTdUJVTlhCb0dZOVdWSGc9PSIsInZhbHVlIjoidkxTd2VCeFArcW00RUZiZE1QTjhEYTBxUHZqOWhjdEZKL0xLY2JISmh2TT0iLCJtYWMiOiJiNjQyZWVhYTBhMzQ0YjU4NjM0NDgyNTMxMmQ4MjQ3YmUyZGRhZDhmYzdiODQyMWY2ZGE0Njk3MDY2NjlkNTQyIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Marketing and sales"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Marketing and sales
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6ImhCQzRrcDNHVTU0b2NJYUdQVHVQSXc9PSIsInZhbHVlIjoiREpVRmRBMjdlVU16QjNQVWNrM051QT09IiwibWFjIjoiZjZiOTZlZTg5Y2QzZTlkMTFjYmFkMTU2ZTQwYWFjMjQ0NzI2ODkxMzc5Y2Y2ZjBjZTAyN2RjMTkzMDQ5ZTQxNSIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6Iml2enUvUGh4cHlyQXE3clVweG4xSVE9PSIsInZhbHVlIjoiK3VjOE5mUU1reUVnMHlEOVlVTk9SRS9qL2M4RTE2bzhtYXhpMUhOTUZZWT0iLCJtYWMiOiJjNDM4ZDg1ZDc0MTM0NTgxNjc4Mzc4NTg1NTJiZjhlYWM4NjA3YzYwYzRlNGVkMmUyZTg5Yjc1YmY0Y2YxMDZjIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Medical devices and supplies"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Medical devices and supplies
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6Ik52YWFIR2J0Z3VNbDUwU3JnajZjS1E9PSIsInZhbHVlIjoicWRTZjNvb2xudGZVeHNUcndOSmx1dz09IiwibWFjIjoiNzRiMTM5ZjkxNGRhZmRkMTFmMWU5ODRmYmMwOTI4N2E4ZjdiZDY0ZTY5NmE5ZWZhMzdiMWExMWVlNzVkZmU0NiIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6IitaZ2Z2VHVzNnljYjlIT2xjZExTeHc9PSIsInZhbHVlIjoiRCtTV09xTHJRUHE0cVhzaGhRMlE2SVlwMmk5bVRaVm9qaXdVSW5sd1hQUT0iLCJtYWMiOiIwZjBhM2ExOGViOTUxMzkwODdlYmU5NDc3Zjg5NmZjOTY5ZGU5NTk1ZDZkNDdkNzIyZjlhYjMxZWZlN2VhM2FiIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Metal products" pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Metal products
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6Imc3REpFNmNBanlwYW4wYnZ3OFR3aHc9PSIsInZhbHVlIjoiOVFsM1ZGU1NDVXFrRkFQazI0RDVFdz09IiwibWFjIjoiMzc0YWE4ODliNjY0M2RiZmRmMDNmYzEzZjZlZjMyYjVlNTYwMTQ3OWE4ZWFkOWIxNmFhYTYxODkyMTZjZTcwOCIsInRhZyI6IiJ9"
                                data-typeid="6"
                                data-type="eyJpdiI6IlJVL0x0RkJFUjUrYVFFd24wRkJsSUE9PSIsInZhbHVlIjoiZUFndno3NEp4UnYrUGdvZlYwN3pZTE50OWtDaU15Y2NkUVZCMkNMNDMrTT0iLCJtYWMiOiI2OGMzMGY5ZDdlOGU3YWYxNzdlNzk5ZDQ5YzdiMmEzMzJjM2I1MWMxMDgyMGFlMGQ4MzVjYjMxYzEzNTE0OWI5IiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - MICE and travel" pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                MICE and travel
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6IjlqV3E4NXV3aGEyK0xKUlFwb2hJemc9PSIsInZhbHVlIjoiSXRiQ01CQmFETHU3dEdLSmd0cFlSdz09IiwibWFjIjoiMWQ5YTYxNDYxM2FjZGMzNjUyMWYyYjEwMmZjZjExYjk4NWNlY2U0MTYwOTMzNjQ1NzFhYzYzZWVlMGMwNmEzMyIsInRhZyI6IiJ9"
                                data-typeid=""
                                data-type="eyJpdiI6Iml1NU9Lc05nNHRWMDBZQTFoYlVubVE9PSIsInZhbHVlIjoiRytnY2RIM1VZSmhhRXJWN2VFRkwyQT09IiwibWFjIjoiMTE3YmNmOWM0ZDQzYWUzMDRjZjNjZjA0NWViY2YwNjVmY2QzODFkZDNiNTBhOWNiNGY0YWY1MzcxZGVjMjQyZSIsInRhZyI6IiJ9"
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Mineral products"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Mineral products
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6IkV5UHI3WDNRZ3ZCWllhZFFyMi9qRmc9PSIsInZhbHVlIjoiRU5CUGx1Wm5vRURmUmhpK0gvSGgrQT09IiwibWFjIjoiZWFiN2FjMmNkZWE0MGQxODcyYWU5MDEyYzUyYTUxZjE1YjdmYTk5OGI5OWRkYzE1NjZmY2ZkYTk0YzlhYTczZCIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6IksrT2hzU1p5RGVXdWM2VlhXUGtCb0E9PSIsInZhbHVlIjoiQk5DWHVoNTRvcWJrYXo1Zk14S0l1Q2RHZ1QrTlQ3UkZFVDhHOWhOUmIzRT0iLCJtYWMiOiI0ZGJhMzg5OGY4OGFkOGMwZjZhODI1OWNjMjA4ODc4MzczOGFhMTgyYmFjODQwY2NmYjM3ODEzMzQzYTcyNmUzIiwidGFnIjoiIn0="
                                actionname="Link click" sectionofpage="Category intelligence - Categories - Packaging"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Packaging
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6Im1YemQxZTFsRy9RMDB0NXlSdjB0Z1E9PSIsInZhbHVlIjoiZ2xvaWwvalpYTkZtSW05cnNnZTZrZz09IiwibWFjIjoiZGYzY2UwMTEwMDMyMWExN2Y3OGZmNmRjNzQ3ZWM1ZDEwZTA0NTU0NTEyY2E0Njg0YjM2YWYxMWYyNmYyNzMwZCIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6IjdZRlZBUXhoOGVrSmRoSXJhMCtMaXc9PSIsInZhbHVlIjoiRFRlUDZpRmp0QkNSOFNKS09FUlp1YUpFZjRpUmtVVm1ua3o2WlVhYUJ6ND0iLCJtYWMiOiJlODM0MDAxZjhkNzgzNWY3MjdhMDAwMGRiNzY1Zjk4YzlkNzE4N2M1NWY5YzgxYWYxMWY4OTQ3Y2NjZTQzYzFkIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Pharma APIs, excipients and biologics"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Pharma APIs, excipients and biologics
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6IjNRcDNCNTdqMldISGhvSVhBMkRsT0E9PSIsInZhbHVlIjoiL2x6Ui9CVTBXTzZzTk1BUUU5Nlpxdz09IiwibWFjIjoiMTg3YmZlNDNiZWUzMjkyMzcxYmViNmVkZjExNGRlNjZhNGQ2YmE2OWUyMTFjM2NjNDBiYWM4MGRiNDRmNDcwMyIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6ImJQZW9rVHZhZXo2aW1CVzFNV0pSVUE9PSIsInZhbHVlIjoiamp1bmgyeUNQd25FY2VCYTJOWWlUYUpNY2o4NkYwOGwrRXRKR0tiZlRQbz0iLCJtYWMiOiIyNGVhNjc0MWE3ZmEwMGY4YmIzZGFhZWU0ODRiMGVmYTYyOTM5ZTdmNDkwMDUyM2ZiMDM5NDdjMTE0OGVjOTcyIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Plant and machinery supplies"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Plant and machinery supplies
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6ImdqbTJBSUduaHJIMEtSYVM3WW4xcUE9PSIsInZhbHVlIjoiVGQrU0RkeW5WYnhSRUFvejFXRlNDdz09IiwibWFjIjoiZjdhNDM4YWE1YmM5Zjg2NmQ4ZTc1NGQxOTRhZjZjZmM4YmFkYjg1M2UyZjMwODJiZTM1YWFjZWIwNjRiOTdiNSIsInRhZyI6IiJ9"
                                data-typeid="6"
                                data-type="eyJpdiI6InN3OEVYNnphNkJYSG5oYUQ0d3VSeGc9PSIsInZhbHVlIjoiZURwdFkwV1NyWHd2L2dEd21NcGlJalZENlZIc3UzV1BHamxDYy9vbW5hQT0iLCJtYWMiOiJmZGZkMDE1YzEyMGM5ZTliZjNhMjNhNzgzMWU5MDA5NWYxODE2Yjk0MzljZjY3NDdiYTVhMWNiNTU5OTVmNGUwIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Professional services"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Professional services
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6InFldlpLY0dvQ2RuUmNvZnNUWE8yTEE9PSIsInZhbHVlIjoiK2VHbzVYZWhmNzc4QTZEdlI0OEtjZz09IiwibWFjIjoiMTU2MDExZDhiMmRhMDcyOTg0NWVjMWEzODA5ZDdmZjliYzM1MjdmNWZkMWUxNmRhNDQzZTBjZGE5Njg5N2FiYSIsInRhZyI6IiJ9"
                                data-typeid="6"
                                data-type="eyJpdiI6Ii9rZ0o3bWFheUQ1MDFvcSsrOWJUdlE9PSIsInZhbHVlIjoib1hVemh0S2pDRStPZGdLU3RVdktJVmU4NTI4S3E2c0pxaHdjdENWOFp0UT0iLCJtYWMiOiJkYTg5MzYyM2VkM2VkZjdjM2Y4NDIyZGUwZWMwMmUzYTlmMWIwNTBmMDE4YmQxMTdiN2ZhMjY4Mzg0NWRjMDM2IiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Regression_Sprint_46"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Regression_Sprint_46
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6IjFRblNSYzFXZGFXeXdBWjRkZG5GZXc9PSIsInZhbHVlIjoieWVqZGhleURWenpnYlRIUlUvWTl3QT09IiwibWFjIjoiNWY0MWU2NDQ1YjdhODg2OGQzMTUyNWI0YzBiOGUwOWQ2OTg4YjlmMzRhOTQ3N2U0OWE3YjdkZGIxMDI5ZTk5MCIsInRhZyI6IiJ9"
                                data-typeid="6"
                                data-type="eyJpdiI6IlVxTmVJcnpIVFl3OXZtanZwVnB0Tmc9PSIsInZhbHVlIjoiaC9abkpLcHU2a2lDN3RzYWl5a3lsbGhSMTNwVzNsdGE5YmdFSExWM29tMD0iLCJtYWMiOiJiNjFmN2RhOTMyMDk2ZWFhZGUwYmE4YzI1N2JiYzgzOTZmOWI0ZDk2Yzc3MWE0YzYzZDY2ZWEzMGZjYTBlNWI1IiwidGFnIjoiIn0="
                                actionname="Link click" sectionofpage="Category intelligence - Categories - Test"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Test
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6Ijg2VkRoUHlwY1lybFBweGwxQTJjRWc9PSIsInZhbHVlIjoiWW5RMEhSL21iRkpvWVI5ZzJUMmtKUT09IiwibWFjIjoiZGVmMDU4NjM4YzFhMTFmNWVlODVlMGQ4YzA3ZDkzZDA0OTQ5NzNiZWUzNmEyZjE3MjBjOWIyY2MxMmVjNWIzNSIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6ImFhSDhEQW50eHlYSTE3QXFjaHVpdFE9PSIsInZhbHVlIjoiQk54RitvbVI1azQ1RUxySTFrRkJyZlFUbzFPMlRYc0Y0Q1dLSnhZUkpWUT0iLCJtYWMiOiIwMThmMzBhMjNiM2E4YzdjZjgxYmEyZDQ0YjY1NDNiZDk1YzNjMjMwNWZhYTlkNjkxMmU2NzNmYjdmYzU2YjIzIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Testing for Mega"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Testing for Mega
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6IlAyaGg4dTNIYWtLVkdqaEZlYUtRY2c9PSIsInZhbHVlIjoiMkhBdWcxTmF0aFpORXNBTlFZWnhDQT09IiwibWFjIjoiZjQwMDJmNDliYzg1YjFkMjU2ZTY0MDcxOGVhM2UyMmRkMDBlNDNhOTBiOGUyMjc3M2MxMDIxM2U2MmQyNDYxYiIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6IjF4MWM1RzhaY1VFTEJBZGVXYWoxZVE9PSIsInZhbHVlIjoiV1dKYTBoNTUzY25uTFV3bFBFVFJBbkNpbGc3L3dFL3pPTTgvQkJHTDFHWT0iLCJtYWMiOiJkNTkwZDlmODNiYmRlYmE2OWMxY2E5NjgzODBmMDczZDBjMWZlY2QwZTA4MjgyMDAzOTkyM2RmMTE3Y2Q3YTJkIiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Textile, leather and accessories"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Textile, leather and accessories
                              </a>
                            </aside>
                            <aside class="col-sm-4">
                              <a href="https://amplifipro-qa.tsclabs.in"
                                data-catid="eyJpdiI6IndtVS9YL3gxRFZHTHdsOVNQVy9MNUE9PSIsInZhbHVlIjoiWlFFME15dmZyNDVmUVJoN3BTMSs5Zz09IiwibWFjIjoiYTZmY2FlYjAyYTBkZTc1MDJmMjk4ZjliYTFkYjM0MzA0NjhkNTM0NmRjYTBmNzE1MTA2ODVlMjliOTI4NmJjZCIsInRhZyI6IiJ9"
                                data-typeid="4"
                                data-type="eyJpdiI6Ik5uZzJVeHlWbVlITkNhckhqMzJVd0E9PSIsInZhbHVlIjoiL045aVY5bFkreGRVUUFCS2pnRnptd2tsTTk5aHBQRW13NnM4bXkzVzgwRT0iLCJtYWMiOiJhNDZjNTNlOGE1YzVmMmRjOTlhNjFkOGQzMDkxZTc2N2IwYjc5OWVlNzRmNWQ5MTE1YTA2N2UyOTAxMjcxYzQ5IiwidGFnIjoiIn0="
                                actionname="Link click"
                                sectionofpage="Category intelligence - Categories - Transportation equipment and components"
                                pagename="Top Menu"
                                class="UserActivityLogger d-inline-block py-1 dropdown-item categoryHeaderItems"
                                comment="Category details viewed.">
                                Transportation equipment and components
                              </a>
                            </aside>
                            <div class="loader-info" id="categoryLoader" style="display: none;">
                              <div></div>
                              <div></div>
                              <div></div>
                              <div></div>
                              <div></div>
                              <div></div>
                              <div></div>
                              <div></div>
                              <div></div>
                              <div></div>
                              <div></div>
                              <div></div>
                            </div>
                          </section>
                        </div>
                      </div>

                    </main>
                  </div>
                </li>

                <!-- Ends here  -->

                <div id="backgroundOverlay"></div>


                <!-- Commodity Menu -->
                <div id="backgroundOverlay"></div>
                <li class="nav-item dropdown MegaMenu" style="position: relative;">
                  <a class="nav-link dropdown-toggle " href="javascript:void(0);" id="navbarDropdownMenuLink13"
                    role="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Commodity
                    intelligence</a>
                  <div class="dropdown-menu border-0"
                    style="min-width: 515px;left: -4.4%!important;transform: translateX(8px) !important;">
                    <main class="container-fluid w-100 font-size14 bg-white border">
                      <div class="row">

                        <div class="col-sm-4 border-right px-0 leftNavSec new" style="background: #eff9fb;">
                          <a class="d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger"
                            href="https://amplifipro-qa.tsclabs.in/marketbrief" actionname="Link click"
                            pagename="Top menu" sectionofpage="Commodity intelligence - Market brief"
                            comment="Market brief page viewed.">Market
                            brief </a>

                          <a class="d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger"
                            href="https://amplifipro-qa.tsclabs.in/eventDetails" actionname="Link click"
                            pagename="Top menu" sectionofpage="Commodity intelligence - Event risk impact"
                            comment="Event risk impact page viewed.">Event risk impact
                          </a>
                          <!--  <a class="d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger" href="#"    actionname="Link click" PageName="Top menu" sectionofpage="Commodity intelligence - Cost optimization toolkit" comment="Cost optimization toolkit page viewed.">Cost
                                                                         optimization toolkit
                                                                     </a> -->
                          <a class="d-inline-block py-1 dropdown-item categoryHeaderItems1 UserActivityLogger"
                            href="https://amplifipro-qa.tsclabs.in/commodity" actionname="Link click"
                            pagename="Top menu" sectionofpage="Commodity intelligence - Historical prices"
                            comment="Historical prices page viewed.">Historical prices</a>

                        </div>
                        <div class="col-sm-8 ">
                          <div class="row">
                            <div class="col-sm-12 pl-2 pt-2">
                              <h4 class="  mb-2" style="font-weight:bold; font-size: 13px;">Commodity insights</h4>
                            </div>
                            <!--- -->
                            <div class="col-sm-12 pl-2 ">



                              <a href="https://amplifipro-qa.tsclabs.in/marketbrief?commodityGroup=Agro"
                                class=" d-inline-block py-1 dropdown-item size12  categoryHeaderItems1 UserActivityLogger"
                                actionname="Link click" pagename="Top menu"
                                sectionofpage="Commodity intelligence- Commodity insights - Agro"
                                comment="Commodity list viewed.">
                                Agro
                              </a>



                              <a href="https://amplifipro-qa.tsclabs.in/marketbrief?commodityGroup=Chemicals"
                                class=" d-inline-block py-1 dropdown-item size12  categoryHeaderItems1 UserActivityLogger"
                                actionname="Link click" pagename="Top menu"
                                sectionofpage="Commodity intelligence- Commodity insights - Chemicals"
                                comment="Commodity list viewed.">
                                Chemicals
                              </a>



                              <a href="https://amplifipro-qa.tsclabs.in/marketbrief?commodityGroup=Dairy"
                                class=" d-inline-block py-1 dropdown-item size12  categoryHeaderItems1 UserActivityLogger"
                                actionname="Link click" pagename="Top menu"
                                sectionofpage="Commodity intelligence- Commodity insights - Dairy"
                                comment="Commodity list viewed.">
                                Dairy
                              </a>



                              <a href="https://amplifipro-qa.tsclabs.in/marketbrief?commodityGroup=Metals+and+alloys"
                                class=" d-inline-block py-1 dropdown-item size12  categoryHeaderItems1 UserActivityLogger"
                                actionname="Link click" pagename="Top menu"
                                sectionofpage="Commodity intelligence- Commodity insights - Metals and alloys"
                                comment="Commodity list viewed.">
                                Metals and alloys
                              </a>



                              <a href="https://amplifipro-qa.tsclabs.in/marketbrief?commodityGroup=Wood%2C+Pulp+and+Paper"
                                class=" d-inline-block py-1 dropdown-item size12  categoryHeaderItems1 UserActivityLogger"
                                actionname="Link click" pagename="Top menu"
                                sectionofpage="Commodity intelligence- Commodity insights - Wood, Pulp and Paper"
                                comment="Commodity list viewed.">
                                Wood, Pulp and Paper
                              </a>



                            </div>

                          </div>

                        </div>
                      </div>

                    </main>
                  </div>
                </li>
                <div id="backgroundOverlay"></div>
                <li class="nav-item"><a href="https://amplifipro-qa.tsclabs.in/topical-insights"
                    class="UserActivityLogger nav-link" actionname="Link click" sectionofpage="Topical Insights"
                    pagename="Home - Top Menu"> Topical insights </a>
                </li>
                <li class="nav-item"><a
                    href="https://amplifipro-qa.tsclabs.in/reports/tools?report_type_id=eyJpdiI6Ilp2dVl2N0R1S3NmYzdMTm1ZeEFpbWc9PSIsInZhbHVlIjoiSDI0bFZpbTg0cG90ZWs3QUl2b2xOUT09IiwibWFjIjoiZjcyZDA2YzlkMTY5ZTQ4MDE2OTk0ZTYyZDdlZTZmMWU1NDBjNGY4NDU1MGM4MWZjMTIyZWQzZjJlZWU2ZDJhNiIsInRhZyI6IiJ9"
                    class="UserActivityLogger nav-link" actionname="Link click" sectionofpage="Tools &amp; Templates"
                    pagename="Home - Top Menu"> Tools and templates </a></li>

                <li class="nav-item dropdown">
                  <a class="nav-link  dropdown-toggle UserActivityLogger" href="#" id="navbarDropdownMenuLink1"
                    role="button" data-bs-toggle="dropdown" aria-haspopup="true" actionname="Link click"
                    sectionofpage="Resources" pagename="Home - Top Menu" aria-expanded="false"
                    comment="Resources selected.">Resources</a>
                  <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink1">
                    <a target="_blank" class="dropdown-item UserActivityLogger rs-items" actionname="Link click"
                      sectionofpage="Resources - Thought leadership" comment="Thought leadership selected."
                      pagename="Home - Top Menu"
                      href="https://www.thesmartcube.com/resources/?pg=0&amp;search=&amp;industry=&amp;function=88&amp;keywords=&amp;tab=2">Thought
                      leadership</a><a target="_blank" class="dropdown-item UserActivityLogger rs-items"
                      actionname="Link click" sectionofpage="Resources - Case studies" comment="Case studies selected."
                      pagename="Home - Top Menu"
                      href="https://www.thesmartcube.com/resources/?pg=0&amp;search=&amp;industry=&amp;function=88&amp;keywords=&amp;tab=5">Case
                      studies</a><a target="_blank" class="dropdown-item UserActivityLogger rs-items"
                      actionname="Link click" sectionofpage="Resources - Blogs" comment="Blogs selected."
                      pagename="Home - Top Menu"
                      href="https://www.thesmartcube.com/resources/?pg=0&amp;search=&amp;industry=&amp;function=88&amp;keywords=&amp;tab=4">Blogs</a><a
                      target="_blank" class="dropdown-item UserActivityLogger rs-items" actionname="Link click"
                      sectionofpage="Resources - Podcasts and videos" comment="Podcasts and videos selected."
                      pagename="Home - Top Menu" href="https://www.youtube.com/@TSCInsights/videos">Podcasts and
                      videos</a><a class="dropdown-item UserActivityLogger rs-items" actionname="Link click"
                      sectionofpage="Resources - Spend Matters insights" comment="Spend Matters insights selected."
                      pagename="Home - Top Menu"
                      href="https://amplifipro-qa.tsclabs.in/resource/list?resource_type_id=eyJpdiI6InhXYWFUeVI2V0pMNDA3Sm1HaVZnZ3c9PSIsInZhbHVlIjoiMDRmZnVxcGpEU0lGeTRWNXBSQmd3QT09IiwibWFjIjoiYjc3NGFlZTNiNTU4MTIxNmI5ZGU1ZjRhMTRhYzc4OTQ2MjBiMzBlMGZkNTI3NDQyNTlhMTE0NDQzNzQ0MDJhNSIsInRhZyI6IiJ9"
                      rs_id="2" id="rsId_2">Spend Matters insights</a><a class="dropdown-item UserActivityLogger"
                      sectionofpage="Resources - CASME" actionname="Link click" pagename="Home - Top Menu"
                      comment="CASME selected." href="https://casme.com/" target="_blank">CASME</a>





                  </div>
                </li>
                <div id="backgroundOverlay"></div>

                <div id="backgroundOverlay"></div>
                <!--   //Author:PRASHANT SUTAIL ,US 30418 ,SPRINT 4 -->
                <li class="nav-1item odi"> <a id="OnePointZeroi" class="hello UserActivityLogger assignedPageName"
                    actionname="Link click" sectionofpage="Request Intelligence (CE customer)"
                    comment="Redirected to CE solution."
                    href="https://amplificonnect-test.tsclabs.in/login?param=ai1%2fcHbOeajSqLFS%2byIJGD9by5wAKPAxyXV9yiNrEzKW1RBWFJRxd8DFZRFSwmlDtEUkGZlwumFByr2vtqbwj%2bKXxyHpS2HAJxmPIPCuUbM%3d"
                    target="_blank" pagename="Home">Request custom intelligence</a></li>
                <div id="iTrac"></div>
              </ul>
            </div>
          </div>
        </div>
      </nav>
    </div>
  </div>




  <!-- Dashboard Header -->
  <!-- <header class="app-header">
  <div class="header-left">
    <div class="logo-section">
      <img src="https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/smartcube-new1.svg" alt="TheSmartCube" class="header-logo" />
      <span class="divider"></span>
      <h1>Digital Asset</h1>
    </div>
  </div>
  <div class="header-right">
    <div class="header-actions">
      <div class="header-btn-group">
        <button class="header-btn secondary" id="save-draft-btn" title="Save as draft">
          <i class="las la-save"></i>
          <span>Save</span>
        </button>
        <button class="header-btn primary" id="submit-review-btn" title="Submit for review">
          <i class="las la-paper-plane"></i>
          <span>Publish</span>
        </button>
        <button class="header-btn secondary" id="preview-btn" title="Preview dashboard">
          <i class="las la-eye"></i>
          <span>Preview</span>
        </button>
      </div>
      <div class="user-menu">
        <button class="user-button">
          <div class="user-avatar">HS</div>
          <div class="user-info">
            <span class="user-name">Hemant Saigal</span>
            <span class="user-role">Administrator</span>
          </div>
        </button>
      </div>
      <div class="hexa-logo">
        <img src="https://dhtfm98jq31zo.cloudfront.net/AmplifiPro/hexalogo_new1.svg" alt="Hexa Logo" />
      </div>
    </div>
  </div>
</header> -->


  <!-- Main Content -->
  <div class="container-fluid mt80">
    <!-- Dashboard Header with Breadcrumb -->
    <div class="row mt-3">
      <div class="col-sm-12">
        <ol class="breadcrumb size11">
          <li class="breadcrumb-item">
            <a class="UserActivityLogger" actionname="Link click" sectionofpage="Commodity insights dashboard"
              pagename="Breadcrumb" comment="Homepage viewed" href="https://amplifipro.thesmartcube.com/dashboard">
              Home
            </a>
          </li>
          <li class="breadcrumb-item">
            <a class="UserActivityLogger123" actionname="Link click" sectionofpage="" pagename="" comment=""
              href="javascript:void(0);">
              Commodity intelligence
            </a>
          </li>
          <li class="breadcrumb-item">
            <a class="UserActivityLogger" actionname="Link click" sectionofpage="Commodity intelligence - Market brief"
              pagename="Breadcrumb" comment="Market brief page viewed."
              href="https://amplifipro.thesmartcube.com/marketbrief">
              Market brief
            </a>
          </li>
          <li class="breadcrumb-item active">Aluminium - Global</li>
        </ol>
      </div>
    </div>
    <div class="row mb-0">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center ">
          <!-- Dashboard Title -->
          <h1 class="dashboard-title mb-0" style="font-size: 18px; font-weight: 500;">Aluminium - Global</h1>
          <!-- Action Icons -->
          <div class="icons pr-0 pb-2 text-end d-flex float-end text-light4 size14 reportAction reportViewerIcons">
            <a href="javascript:void(0);" data-bs-toggle="tooltip" data-bs-placement="top" title="Set as homepage"
              class="">
              <i class="las la-home" style="font-size: 20px; color: #02104f"></i>
            </a>

            <a href="javascript:void(0);" data-bs-toggle="tooltip" data-bs-placement="top" title="Add to favourite"
              class="">
              <i class="las la-plus" style="font-size: 20px; color: #02104f"></i>
            </a>

            <a href="javascript:void(0);" data-bs-toggle="tooltip" data-bs-placement="top" title="Share" class="">
              <i class="las la-share" style="font-size: 20px; color: #02104f"></i>
            </a>

            <a href="javascript:void(0);" data-bs-toggle="tooltip" data-bs-placement="top" title="Copy link" class="">
              <i class="las la-copy" style="font-size: 20px; color: #02104f"></i>
            </a>

            <a href="javascript:void(0);" data-bs-toggle="tooltip" data-bs-placement="top" title="Export to PPT"
              class="me-1">
              <i class="las la-file-export" style="font-size: 20px; color: #02104f"></i>
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Grid Container -->
    <section class="">
      <!-- Added Key Insights Widget -->
      <div class="mb-3">
        <div class="grid-stack-item-content">
          <div class="card h-100 key-insights-widget">
            <div class="card-body cloudy cloudLogic pt-0">
              <a href="javascript:void(0);" class="d-block mt-2 text-end" sectionofpage="see more">
                <div id="scroll" class="scroll1 triggerClickKeyInsights" data-value="50" style="right: 49% !important;">
                  <span class="arrow-bounce">↓</span>
                </div>
              </a>
              <header class="d-flex justify-content-between widget-header mb-2 align-items-center">
                <h3 class="fs-5 my-2">Key Insights</h3>
                <div class="widget-icons">
                  <a href="javascript:void(0);" class="focus-icon-link" data-bs-toggle="tooltip" title="Focus mode">
                    <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode.svg" alt="Focus Mode"
                      class="focus-icon default" />
                    <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode-hover.svg" alt="Focus Mode"
                      class="focus-icon hover" />
                  </a>
                  <a href="javascript:void(0);" data-bs-toggle="tooltip" title="Export to PPT">
                    <i class="las la-file-export"></i>
                  </a>
                
                </div>
              </header>
              <div style="padding-left: 0px; padding-right: 0px; overflow: hidden;">
                <div class="ciKeyInsightScrollInsight1b doMagicOnDownCursor fixUL" style="height: 50px; ">
                  <div class="insights-content ciKeyInsightScrollInsight1">
                    <div class="mb-3">
                      <strong>Buying recommendations: Cover H2 2025–Q1 2026
                        requirements in Q2 2025</strong>
                      <ul>
                        <li>
                          Buyers are suggested to cover H2 2025–Q1 2026
                          exposure in Q2 2025 as prices are likely to
                          increase thereafter, supported by a recovery in
                          downstream demand, a likely improvement in
                          macroeconomic sentiments and potential supply
                          concerns in late 2025
                        </li>
                      </ul>
                    </div>

                    <div class="swot-table">
                      <table class="table table-bordered craftTable table-sm w-100 mb-3">
                        <thead>
                          <tr>
                            <th class="fw-bold">
                              <div class="d-flex align-items-center justify-content-around">
                                <div>
                                  <img src="https://amplifipro.thesmartcube.com/public/images/opportunities_icon.svg"
                                    width="25" alt="Opportunities" />
                                  <span class="ms-2">Opportunity</span>
                                </div>
                              </div>
                            </th>
                            <th class="fw-bold">
                              <div class="d-flex align-items-center justify-content-around">
                                <div>
                                  <img src="https://amplifipro.thesmartcube.com/public/images/threats_icon.svg"
                                    width="25" alt="Risks" />
                                  <span class="ms-2">Risk</span>
                                </div>
                              </div>
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td class="text-start">
                              <ul>
                                <li>
                                  Fall in Feedstock Prices: An anticipated
                                  ease in supply concerns in alumina
                                  market along with a foreseen improvement
                                  in bauxite availability in H1 2025 is
                                  expected to reduce production costs for
                                  the producers
                                </li>
                                <li>
                                  Hawkish stance by the US Fed:
                                  Expectations of fewer interest rate cuts
                                  by the US Fed in 2025 amid rising risks
                                  of inflation may dampen aluminium demand
                                </li>
                                <li>
                                  Demand Destruction: An anticipated
                                  demand reduction following a sharp price
                                  uptrend during Q4 2024–Q1 2025 coupled
                                  with bearish macroeconomic sentiments
                                  will likely weigh down on prices
                                </li>
                              </ul>
                            </td>
                            <td class="text-start">
                              <ul>
                                <li>
                                  Imposition of import tariffs by the US:
                                  Concerns over imposition of 25% tariffs
                                  on aluminium imports into the US along
                                  with tariffs on China, Canada and Mexico
                                  may raise supply concerns globally
                                </li>
                                <li>
                                  Announcement of further stimulus: China
                                  may announce further monetary stimulus
                                  to boost its economic growth, which may
                                  infuse demand sentiments
                                </li>
                                <li>
                                  Potential decline in China's aluminium
                                  supply: China's commitment to restrict
                                  aluminium supply to 45 million tonne
                                  annual production capacity in later half
                                  of 2025 may support the price rise
                                </li>
                              </ul>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <footer class="widget-footer">
                    <div class="mb-0">
                      <span class="size10 notes-keyInsight" style="display: none">
                        <i class="las la-clipboard size14"></i> Notes :
                        <span id="notekeyInsight">The Smart Cube The Smart Cube Knowledge
                          Repository; Westmetall</span>
                        <br />
                      </span>

                      <span class="size10 source-keyInsight">
                        <i class="las la-database size14"></i> Source :
                        <span id="sourcekeyInsight">The Smart Cube Research and Analysis</span>

                        <br />
                      </span>
                      <span class="size10 last-update-keyInsight">
                        <i class="las size14 la-calendar-minus"></i> Last
                        update :
                        <span id="lastUpdatekeyInsight">Apr-2025</span>
                      </span>
                      <span class="size10 next-update-keyInsight" id="hidenextUpdatekeyInsight">
                        <i class="las size14 la-calendar-plus"></i> Next
                        update :
                        <span id="nextUpdatekeyInsight">May-2025</span>
                      </span>
                    </div>
                  </footer>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mb-3">
        <div class="row">
          <div class="col-sm-12">
            <div class="card h-100">
              <div class="card-body pt-0 cloudy doMagicOnDownCursor" style="
    height: 635px;
    overflow: hidden;
">
<a href="javascript:void(0);" class="d-block mt-2 text-end" sectionofpage="see more">
                <div id="scroll" class="scroll2aa triggerClickKeyInsights" data-value="635" style="right: 49% !important;">
                  <span class="arrow-bounce">↓</span>
                </div>
              </a>
                <header class="d-flex justify-content-between pt-0 widget-header mb-0 align-items-center">
                  <h3 class="fs-5 my-2">Price analysis</h3>
                  <div class="widget-icons d-none">
                    <a href="javascript:void(0);" class="focus-icon-link" data-bs-toggle="tooltip" title="Focus mode">
                      <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode.svg" alt="Focus Mode"
                        class="focus-icon default" />
                      <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode-hover.svg" alt="Focus Mode"
                        class="focus-icon hover" />
                    </a>
                    <a href="javascript:void(0);" data-bs-toggle="tooltip" title="Export to PPT">
                      <i class="las la-file-export"></i>
                    </a>
                   
                  </div>
                </header>

                <!-- Add Price analysis Widget -->
                <div class="mb-3">
                  <div class="grid-stack-item-content">
                    <div class="p-0 h-100">
                      <div class="card-body px-0 pb-0 pt-2 pt-2 " >
                        <header class="d-flex justify-content-between pt-2 widget-header align-items-center mb-2">
                          <h3 class="fs-5 my-2">Price forecast</h3>
                          <div class="widget-icons">
                            <a href="javascript:void(0);" class="focus-icon-link" data-bs-toggle="tooltip"
                              title="Focus mode">
                              <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode.svg"
                                alt="Focus Mode" class="focus-icon default" />
                              <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode-hover.svg"
                                alt="Focus Mode" class="focus-icon hover" />
                            </a>
                            <a href="javascript:void(0);" data-bs-toggle="tooltip" title="Export to PPT">
                              <i class="las la-file-export"></i>
                            </a>
                            
                          </div>
                        </header>

                        <div class="widget-body p-0">
                          <div class="row h-100">
                            <!-- Left side with chart -->
                            <div class="col-md-6 h-100 pe-0">
                              <div class="p-3 h-100 d-flex flex-column">
                                 <div id="chartdiv1" style="width: 100%; height: 380px;"></div>
                              </div>
                            </div>
                            <!-- Right side with analysis content -->
                            <div class="col-md-6 h-100">
                              <div class="h-100 d-flex">
                                <!-- Historical Analysis Section -->
                                <div class="p-3 analysis-content  overflow-auto" style="
                                  width: 50%;
                                  height: 100%;
                                  border-left: 1px solid #eaeaea;
                                ">
                                  <h6 class="mb-3 fw-bold">
                                    Historical analysis
                                  </h6>
                                  <p class="mb-3">
                                    <strong>Nov 2020:</strong> Prices
                                    increased amid healthy demand and
                                    reduced availability; moreover, a rise
                                    in prices of brown testliner grades in
                                    Oct supported the bullish price
                                    sentiment during the month
                                  </p>

                                  <div class="mb-3 ciKeyInsightScrollInsight">
                                    <ul>
                                      <li class="mb-2">
                                        Demand from certain downstream
                                        markets (especially from automotive
                                        and F&B segments) continued to
                                        remain healthy in Nov

                                        <ul class="list-unstyled ms-4">
                                          <li class="mb-2">
                                            Demand for corrugated board from
                                            the food packaging segment
                                            picked up due to panic buying
                                            amid a growing number of
                                            coronavirus cases in Europe –
                                            this was similar to the buying
                                            pattern witnessed during the
                                            early stages of the pandemic in
                                            the region over Mar-Apr
                                          </li>
                                        </ul>
                                      </li>
                                    </ul>
                                  </div>

                                  
                                </div>

                                <!-- Price Outlook Section -->
                                <div class="p-3 analysis-content overflow-auto" style="
                                  width: 50%;
                                  height: 100%;
                                  border-left: 1px solid #eaeaea;
                                ">
                                  <h6 class="mb-3 fw-bold">
                                    Price outlook
                                  </h6>
                                  <p class="mb-3">
                                    <strong>Dec 2020:</strong> Prices are
                                    expected to increase amid a projected
                                    uptick in offer prices by overseas
                                    suppliers, continued tightness in
                                    regional supply and projections of
                                    healthy demand
                                  </p>

                                  

                                  <div class="mb-3 ciKeyInsightScrollInsight">
                                    <ul class="mb-2">
                                      <li>
                                        Demand for corrugated packaging from
                                        the online shopping segment is
                                        expected to remain healthy in the
                                        near term; the end-of-year festive
                                        season in Europe will likely support
                                        demand further
                                        <ul class="list-unstyled ms-4">
                                          <li class="mb-2">
                                            Given the intensifying
                                            coronavirus situation in the
                                            region, consumers are less
                                            likely to shop at
                                            brick-and-mortar stores and will
                                            increasingly purchase gifts and
                                            other necessities online
                                          </li>
                                        </ul>
                                      </li>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Widget Footer with Update Info and Action Buttons -->
                        <div class="noteSection mt-0">
                          <div class="row">
                            <div class="col-12">
                              <small>
                                <i class="las size14 la-database"></i>
                                <span class="font-weight-bold">Source:</span>
                                The Smart Cube Research and Analysis
                                <br />
                                <i class="las size14 la-calendar-minus"></i>
                                <span class="font-weight-bold">Last Update:</span>
                                23 Dec 2020
                                <br />
                                <i class="las size14 la-calendar-plus"></i>
                                <span class="font-weight-bold">Next Update:</span>
                                07 Jan 2021
                              </small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Add Supply and demand Widget -->
                 <hr class="my-0">
                <div class="mb-3">
                  <div class="grid-stack-item-content">
                    <div class=" p-0 h-100">
                      <div class="card-body px-0 pb-0 pt-2">
                        <header class="d-flex justify-content-between pt-0 widget-header align-items-center mb-2">
                          <h3 class="fs-5 my-2">Supply and demand</h3>
                          <div class="widget-icons">
                            <a href="javascript:void(0);" class="focus-icon-link" data-bs-toggle="tooltip"
                              title="Focus mode">
                              <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode.svg"
                                alt="Focus Mode" class="focus-icon default" />
                              <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode-hover.svg"
                                alt="Focus Mode" class="focus-icon hover" />
                            </a>
                            <a href="javascript:void(0);" data-bs-toggle="tooltip" title="Export to PPT">
                              <i class="las la-file-export"></i>
                            </a>
                            
                          </div>
                        </header>

                        <div class="widget-body p-0">
                          <div class="row h-100">
                            <!-- Left side with chart -->
                            <div class="col-md-6 h-100 pe-0">
                              <div class="p-3 h-100 d-flex flex-column">
                                <div id="chartDiv2" style="width: 100%;flex-grow: 1;min-height: 380px;"></div>
                              </div>
                            </div>
                            <!-- Right side with analysis content -->
                            <div class="col-md-6 h-100">
                              <div class="h-100 d-flex">
                                <!-- Historical Analysis Section -->
                                <div class="p-3 analysis-content overflow-auto" style="
                                  height: 100%;border-left: 1px solid #eaeaea;">
                                  <div class="mb-3 ciKeyInsightScrollInsight">
                                    <ul id="jsonList">
                                      <strong>Short-term outlook (Q3 2025F):</strong><br>
                                      <ul>
                                        <li>Supply: Global aluminium supply is foreseen to increase in Q3 2025 on
                                          account
                                          of a seasonal improvement in aluminium output during rainy season (May–Oct) in
                                          southwestern China driven by a decrease in energy costs due to high hydropower
                                          supply</li>
                                        <li>Demand: Global aluminium demand is forecast to remain subdued in Q3 2025 due
                                          to uncertainty regarding the US’ tariff <ul>
                                            <li>US reciprocal tariffs of 10% on global imports is due to expire by
                                              early-Q3 2025, but persistent uncertainty regarding elevated tariffs is
                                              foreseen to weigh on demand sentiments</li>
                                            <li>Japan's aluminium premium (a benchmark indicating Asia's aluminium
                                              demand)
                                              dropped 20% Q-o-Q for Q3 </li>
                                          </ul>
                                        </li>
                                      </ul><strong>Long-term outlook (2025F):</strong><br>
                                      <ul>
                                        <li>The global aluminium market will likely witness a deficit of ~0.9 million
                                          tonnes (1.2% of the global primary aluminium production) in 2025 as growth in
                                          consumption will outweigh the rise in production</li>
                                        <li>Supply: Global aluminium production may increase 3% Y-o-Y in 2025, reaching
                                          ~74.1 million tonnes, driven by a rise in supply from China and Bahrain <ul>
                                            <li>China's aluminium production is nearing the 45 million tonne annual
                                              capacity limit, driven by increased hydro-power availability in Yunnan
                                            </li>
                                          </ul>
                                        </li>
                                        <li>Demand: The global aluminium consumption is forecast to grow at a higher
                                          rate
                                          of 4% Y-o-Y, reaching 74.5 million tonnes, driven by an expected increase in
                                          demand for energy-efficient vehicles <ul>
                                            <li>Automakers around the world are seeking to reduce vehicle weight by
                                              increasing the proportion of aluminium in the vehicle, which will likely
                                              boost aluminium demand from EVs</li>
                                            <li>Strong demand from the downstream renewable sector (solar panel and wind
                                              turbines) will likely boost aluminium prices</li>
                                            <li>However, pessimistic global economic outlook following imposition of US
                                              reciprocal tariffs may limit the growth in demand </li>
                                          </ul>
                                        </li>
                                      </ul>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Widget Footer with Update Info and Action Buttons -->
                        <div class="noteSection mt-0">
                          <div class="row">
                            <div class="col-12">
                              <small>
                                <i class="las size14 la-database"></i>
                                <span class="font-weight-bold">Source:</span>
                                The Smart Cube Research and Analysis
                                <br />
                                <i class="las size14 la-calendar-minus"></i>
                                <span class="font-weight-bold">Last Update:</span>
                                23 Dec 2020
                                <br />
                                <i class="las size14 la-calendar-plus"></i>
                                <span class="font-weight-bold">Next Update:</span>
                                07 Jan 2021
                              </small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <hr class="my-0">
                <!-- Add Cost structure & Value chain Widget -->
                <div class="mb-3">
                  <div class="grid-stack-item-content">
                    <div class="p-0 h-100">
                      <div class="card-body px-0 pb-0 pt-2">
                        <header class="d-flex justify-content-between pt-0 widget-header align-items-center mb-2">
                          <h3 class="fs-5 my-2">Cost structure & Value chain</h3>
                          <div class="widget-icons">
                            <a href="javascript:void(0);" class="focus-icon-link" data-bs-toggle="tooltip"
                              title="Focus mode">
                              <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode.svg"
                                alt="Focus Mode" class="focus-icon default" />
                              <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode-hover.svg"
                                alt="Focus Mode" class="focus-icon hover" />
                            </a>
                            <a href="javascript:void(0);" data-bs-toggle="tooltip" title="Export to PPT">
                              <i class="las la-file-export"></i>
                            </a>
                            
                          </div>
                        </header>

                        <div class="widget-body p-0">
                          <div class="row h-100">
                            <!-- Left side with chart -->
                            <div class="col-md-5 h-100 pe-0">
                              <div class="p-3 h-100 d-flex flex-column">
                                <div id="chartDiv3" style="width: 100%;flex-grow: 1;height: 309px;"></div>
                              </div>
                            </div>
                            <!-- Right side with analysis content -->
                            <div class="col-md-7 h-100">
                              <div class="h-100">
                                <div class="p-3 analysis-content overflow-auto" style="
                                  height: 100%;border-left: 1px solid #eaeaea;">
                                  <div style="width:100%;height:265px; text-align:center" class="viewReport">
                                    <div class="loader-info-iframe" style="display: none;">
                                      <div class="loader-info">
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>
                                        <div></div>

                                      </div>
                                    </div>
                                    <iframe class="pdfembed" id="loaderFramer"
                                      src="https://amplifipro.wnsprocurement.com/public/plugins/pdfjs/web/viewer.php?file=https://www.princexml.com/samples/invoice-colorful/invoicesample.pdf#zoom=40"
                                      width="100%" height="320px"></iframe>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          <!-- Widget Footer with Update Info and Action Buttons -->
                          <div class="noteSection mt-0">
                            <div class="row">
                              <div class="col-12">
                                <small>
                                  <i class="las size14 la-database"></i>
                                  <span class="font-weight-bold">Source:</span>
                                  The Smart Cube Research and Analysis
                                  <br />
                                  <i class="las size14 la-calendar-minus"></i>
                                  <span class="font-weight-bold">Last Update:</span>
                                  23 Dec 2020
                                  <br />
                                  <i class="las size14 la-calendar-plus"></i>
                                  <span class="font-weight-bold">Next Update:</span>
                                  07 Jan 2021
                                </small>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <hr class="my-0">
                <!-- Add Key driver's impact Widget -->
                <div class="mb-0">
                  <div class="grid-stack-item-content">
                    <div class="p-0 h-100">
                      <div class="card-body px-0 pb-0 pt-2">
                        <header class="d-flex justify-content-between pt-0 widget-header align-items-center mb-2">
                          <h3 class="fs-5 my-2">Key driver's impact</h3>
                          <div class="widget-icons">
                            <a href="javascript:void(0);" class="focus-icon-link" data-bs-toggle="tooltip"
                              title="Focus mode">
                              <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode.svg"
                                alt="Focus Mode" class="focus-icon default" />
                              <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode-hover.svg"
                                alt="Focus Mode" class="focus-icon hover" />
                            </a>
                            <a href="javascript:void(0);" data-bs-toggle="tooltip" title="Export to PPT">
                              <i class="las la-file-export"></i>
                            </a>
                            
                          </div>
                        </header>

                        <div class="widget-body p-0">
                          <div class="row h-100">
                            <!-- Left side with chart -->
                            <div class="col-md-6 h-100 pe-0">
                              <div class="h-100 d-flex flex-column">
                                <table class="table table-bordered craftTable craftTableHarveytable mb-3">
                                  <thead>
                                    <tr>

                                      <th scope="col" class="text-center fw-bold">
                                        Key driver
                                      </th>
                                      <th scope="col" class="text-center fw-bold">
                                        Supply
                                      </th>
                                      <th scope="col" class="text-center fw-bold">
                                        Demand
                                      </th>
                                      <th scope="col" class="text-center fw-bold">
                                        Feedstock
                                      </th>
                                      <th scope="col" class="text-center fw-bold">
                                        Macroeconomic
                                      </th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr>
                                      <td class="">
                                        <div class="px-0" id="overAllSummaryText1" style="text-align: center;">
                                          Current Impact
                                        </div>
                                      </td>
                                      <td class="">
                                        <div class="pie moderate "></div>
                                      </td>
                                      <td class="">
                                        <div class="pie moderate "></div>
                                      </td>
                                      <td class="">
                                        <div class="pie moderatehigh "></div>
                                      </td>
                                      <td class="">
                                        <div class="pie high "></div>
                                      </td>
                                    </tr>
                                    <tr>
                                      <td class="">
                                        <div class="px-0" id="overAllSummaryText1" style="text-align: center;">
                                          Forecast Impact
                                        </div>
                                      </td>
                                      <td class="">
                                        <div class="pie moderatehigh "></div>
                                      </td>
                                      <td class="">
                                        <div class="pie moderate "></div>
                                      </td>
                                      <td class="">
                                        <div class="pie moderatehigh "></div>
                                      </td>
                                      <td class="">
                                        <div class="pie high "></div>
                                      </td>
                                    </tr>
                                    <tr>
                                      <td class="">
                                        <div class="px-0" id="overAllSummaryText1" style="text-align: center;">
                                          Price forecast
                                        </div>
                                      </td>
                                      <td class="text-center">
                                        <i class="las la-angle-down centerAlign"
                                          style="color: #28ba3b;font-size: 19px;text-align: center;"></i>
                                      </td>
                                      <td class="text-center">
                                        <i class="las la-angle-down centerAlign"
                                          style="color: #28ba3b;font-size: 19px;text-align: center;"></i>
                                      </td>
                                      <td class="text-center">
                                        <i class="las la-angle-up centerAlign"
                                          style="color: #BA3909;font-size: 19px;text-align: center;"></i>
                                      </td>
                                      <td class="text-center">
                                        <i class="las la-angle-up centerAlign"
                                          style="color: #BA3909;font-size: 19px;text-align: center;"></i>
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                                <div class="d-flex justify-content-end align-items-end mt-3">
                                  <div class="harveyLegends d-flex align-items-center justify-content-start">
                                    <span>Low </span>
                                    <div class="pie low"></div>
                                    <div class="pie lowmoderate"></div>
                                    <div class="pie moderate"></div>

                                    <div class="pie moderatehigh"></div>
                                    <div class="pie high"></div>
                                    <span> High</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div class="col-md-6 h-100">
                              <div class="h-100 d-flex">
                                <div class="analysis-content overflow-auto"
                                  style="height: 100%;border-left: 1px solid #eaeaea;">
                                  <div class="mb-3 ciKeyInsightScrollInsight">
                                    <ul>
                                      <strong>Q3 2025F</strong><strong>Supply</strong>
                                      <ul>
                                        <li>Global aluminium supply is foreseen to increase in Q3 2025 on account of a
                                          seasonal improvement in aluminium output during rainy season (May–Oct) in
                                          southwestern China driven by a decrease in energy costs due to high hydropower
                                          supply </li>
                                      </ul><strong>Demand</strong>
                                      <ul>
                                        <li>Uncertainty regarding the US’ tariff is expected to keep global aluminium
                                          demand at low levels in Q3 2025 </li>
                                      </ul><strong>Feedstock</strong>
                                      <ul>
                                        <li>Feedstock (alumina) prices are likely to rise during Q3 2025 due to
                                          environmental constraints, upstream (bauxite) shortages, and maintenance at
                                          major alumina refineries in China </li>
                                      </ul><strong>Macroeconomic</strong>
                                      <ul>
                                        <li>Expectations of interest rate cut by the US Fed in Q3 2025 may increase
                                          buying
                                          sentiments in the market, thereby raising demand for aluminium</li>
                                        <li>Further, China may announce further monetary stimulus to boost its economic
                                          growth, which may boost demand sentiments </li>
                                      </ul>
                                    </ul>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Widget Footer with Update Info and Action Buttons -->
                        <div class="noteSection mt-0">
                          <div class="row">
                            <div class="col-12">
                              <small>
                                <i class="las size14 la-database"></i>
                                <span class="font-weight-bold">Source:</span>
                                The Smart Cube Research and Analysis
                                <br />
                                <i class="las size14 la-calendar-minus"></i>
                                <span class="font-weight-bold">Last Update:</span>
                                23 Dec 2020
                                <br />
                                <i class="las size14 la-calendar-plus"></i>
                                <span class="font-weight-bold">Next Update:</span>
                                07 Jan 2021
                              </small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Add Price analysis Widget -->
      <div class="mb-3">
        <div class="grid-stack-item-content">
          <div class="card h-100">
            <div class="card-body">
              <header class="d-flex justify-content-between pt-0 widget-header align-items-center mb-2">
                <h3 class="fs-5 my-2">AI-powered price forecast</h3>
                <div class="widget-icons">
                  <a href="javascript:void(0);" class="focus-icon-link" data-bs-toggle="tooltip" title="Focus mode">
                    <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode.svg" alt="Focus Mode"
                      class="focus-icon default" />
                    <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode-hover.svg" alt="Focus Mode"
                      class="focus-icon hover" />
                  </a>
                  <a href="javascript:void(0);" data-bs-toggle="tooltip" title="Export to PPT">
                    <i class="las la-file-export"></i>
                  </a>
                 
                </div>
              </header>

              <div class="widget-body p-0">
                <div class="row h-100">
                  <!-- Left side with chart -->
                  <div class="col-md-12 h-100 pe-0">
                    <div class="p-3 h-100 d-flex flex-column">
                       <div id="chartDiv4" style="width: 100%; height: 380px;"></div>
                    </div>
                  </div>
                  <!-- Right side with analysis content -->





                   
                  
                </div>
              </div>

              <!-- Widget Footer with Update Info and Action Buttons -->
              <div class="noteSection mt-0">
                <div class="row">
                  <div class="col-12">
                    <small>
                      <i class="las size14 la-database"></i>
                      <span class="font-weight-bold">Source:</span>
                      The Smart Cube Research and Analysis
                      <br />
                      <i class="las size14 la-calendar-minus"></i>
                      <span class="font-weight-bold">Last Update:</span>
                      23 Dec 2020
                      <br />
                      <i class="las size14 la-calendar-plus"></i>
                      <span class="font-weight-bold">Next Update:</span>
                      07 Jan 2021
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Add event impact and commodity News  -->
      <div class="mb-3">
        <div class="row">
          <div class="col-sm-8 pe-md-0">
            <div class="card h-100">
              <div class="card-body">
                <header class="d-flex justify-content-between pt-0 widget-header align-items-center mb-2">
                  <h3 class="fs-5 my-2">Event impact</h3>
                </header>
                <div id="eventDiv">
                  <div style="min-height: 390px">
                    <div class="EventRiskImpact__absolute-center absolute-center" style="min-height: 390px;">
                      <div>
                        <span class="EventRiskImpact__image" style="display: none;"></span>
                        <h1 class="EventRiskImpact__title">Commodity price - <span class="EventRiskImpact__status">No
                            impact</span></h1>
                        <span class="EventRiskImpact__no-record">Current external market events like war, natural
                          calamities, and supply chain issues had no impact on the price of this commodity.</span>
                        <a href="https://amplifipro.thesmartcube.com/eventDetails" class="EventRiskImpact__link">To view
                          other impacted commodities click here</a>
                      </div>
                    </div>
                  </div>

                  <section class="noteSection1 CPInfoSection  d-none priceOutlookDivNotes1 pt-0"
                    style="opacity: 1; position: relative;">
                    <div class="row"
                      style="display: flex;justify-content: space-between;align-items: center;width: 100%; bottom:10px!important;">
                      <div class="col-sm-12">
                        <div class="mb-0">
                        </div>
                      </div>
                    </div>
                  </section>
                </div>
              </div>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="card h-100">
              <div class="card-body">
                <header class="d-flex justify-content-between pt-0 widget-header align-items-center mb-2">
                  <h3 class="fs-5 my-2">Commodity news</h3>
                  <div class="widget-icons">
                    <a href="javascript:void(0);" class="focus-icon-link" data-bs-toggle="tooltip" title="Focus mode">
                      <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode.svg" alt="Focus Mode"
                        class="focus-icon default" />
                      <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode-hover.svg" alt="Focus Mode"
                        class="focus-icon hover" />
                    </a>

                  </div>
                </header>
                <div class="ciScrollNews" style="">
                  <!-- News Groups with original class structure -->
                  <div class="newsGroup">
                    <div class="border calendernews" style="border-color: #fbc20d !important">
                      <span class="date" style="color: #fbc20d !important">13</span>
                      <span class="year">May ' 25</span>
                    </div>

                    <div class="News border-bottom ml-2">
                      <a href="https://www.alcircle.com/press-release/the-most-traded-shfe-aluminium-contract-approached-the-critical-level-of-20-000-with-processing-fees-under-pressure-pulling-back-amidst-generally-slug-114074"
                        class="newsDesc UserActivityLogger" data-title="" data-date_published="2025-05-13 11:43:02"
                        data-supply_demand_macro="['supply', 'demand']" target="_blank" actionname="Link click"
                        sectionofpage="Commodity insights dashboard - Commodity news"
                        pagename="Commodity - Aluminium - Global"
                        comment="The most-traded SHFE aluminium contract approached the critical level of 20,000, with processing fees under pressure, pulling back amidst generally sluggish trading volume link clicked">
                        The most-traded SHFE aluminium contract
                        approached the critical level of 20,000,
                        with processing fees under pressure, pulling
                        back amidst generally sluggish trading
                        volume

                        <div class="size12 my-1" style="color: #231f20">
                          <i class="la la-tag" aria-hidden="true" style="font-size: 13px"></i>
                          <span class="ml-1 tags-widget" data-placement="top">supply, demand</span>
                        </div>
                      </a>
                    </div>
                  </div>

                  <div class="newsGroup">
                    <div class="border calendernews" style="border-color: #fbc20d !important">
                      <span class="date" style="color: #fbc20d !important">13</span>
                      <span class="year">May ' 25</span>
                    </div>

                    <div class="News border-bottom ml-2">
                      <a href="https://www.alcircle.com/press-release/uk-aluminium-federation-calls-for-aluminium-specific-assessment-of-the-uk-cbams-impact-on-sector-competitiveness-114071"
                        class="newsDesc UserActivityLogger"
                        data-title="UK Aluminium Federation calls for aluminium-specific assessment of the UK CBAMs impact on sector competitiveness"
                        data-description="" data-date_published="2025-05-13 10:10:37"
                        data-supply_demand_macro="['demand', 'macroeconomic']" target="_blank" actionname="Link click"
                        sectionofpage="Commodity insights dashboard - Commodity news"
                        pagename="Commodity - Aluminium - Global"
                        comment="UK Aluminium Federation calls for aluminium-specific assessment of the UK CBAMs impact on sector competitiveness link clicked">
                        UK Aluminium Federation calls for
                        aluminium-specific assessment of the UK
                        CBAMs impact on sector competitiveness

                        <div class="size12 my-1" style="color: #231f20">
                          <i class="la la-tag" aria-hidden="true" style="font-size: 13px"></i>
                          <span class="ml-1 tags-widget" data-placement="top">demand, macroeconomic</span>
                        </div>
                      </a>
                    </div>
                  </div>

                  <div class="newsGroup">
                    <div class="border calendernews" style="border-color: #bb3609 !important">
                      <span class="date" style="color: #bb3609 !important">12</span>
                      <span class="year">May ' 25</span>
                    </div>

                    <div class="News border-bottom ml-2">
                      <a href="#" class="newsDesc UserActivityLogger open-news"
                        data-title="Aluminum Demand Remains Steady Despite Price Hikes" data-description=""
                        data-date_published="2025-05-12 23:30:37" data-supply_demand_macro="['demand', 'macroeconomic']"
                        actionname="Link click" sectionofpage="Commodity insights dashboard - Commodity news"
                        pagename="Commodity - Aluminium - Global"
                        comment="Aluminum Demand Remains Steady Despite Price Hikes link clicked">
                        Aluminum Demand Remains Steady Despite Price
                        Hikes

                        <div class="size12 my-1" style="color: #231f20">
                          <i class="la la-tag" aria-hidden="true" style="font-size: 13px"></i>
                          <span class="ml-1 tags-widget" data-placement="top">demand, macroeconomic</span>
                        </div>
                      </a>
                    </div>
                  </div>

                  <!-- Additional news items can be added following the same pattern -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


      <!-- Added Opportunity calculator Widget -->
      <div class="mb-3">
        <div class="grid-stack-item-content">
          <div class="card h-100 key-insights-widget">
            <div class="card-body cloudy cloudLogic">
              <a href="javascript:void(0);" class="d-block mt-2 text-end" sectionofpage="see more">
                <div id="scroll" class="scroll1 triggerClickKeyInsights" data-value="50" style="right: 49% !important;">
                  <span class="arrow-bounce">↓</span>
                </div>
              </a>
              <header class="d-flex justify-content-between widget-header">
                <h3 class="fs-5">Opportunity calculator</h3>
              </header>
              <div style="overflow: hidden;">
                <div class="ciKeyInsightScrollInsight1 doMagicOnDownCursor fixUL"
                  style="height: 352px;overflow: hidden; ">
                  <div class="insights-content ciKeyInsightScrollInsight1 p-0">
                    <div class="table-responsive overflow-hidden">
                      <div class="skeltonLoader table-loader no-animation table-loaderNew dataTableSkeleton"
                        style="position: absolute;top: 116px;left: 324px;z-index: 99;width: 980px;">
                        <style>
                          .table-loader th {
                            padding: 0 !important;
                            width: 100px;
                            border: 1px solid #eee;
                          }

                          .table-loader td {
                            padding: 5px 10px !important;
                            width: 100px;
                            border: 1px solid #eee;
                            border-bottom: none;
                            height: 30px;
                          }

                          .loaderTable0 {
                            width: 161.43px !important;
                            height: 48.8px !important;
                          }

                          .loaderTable1 {
                            padding: 0px !important;
                            width: 100% !important;
                          }

                          .loaderTable2 {
                            width: 100% !important;
                            height: 10px !important;
                          }

                          .loaderTable3 {
                            width: 150.8px !important;
                            height: 48.8px !important;
                          }

                          .loaderTable4 {
                            width: 153.48px !important;
                            height: 48.8px !important;
                          }

                          .loaderTable5 {
                            width: 153.53px !important;
                            height: 48.8px !important;
                          }
                        </style>
                        <table>
                          <tbody>
                            <tr>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable4">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable5">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable4">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable5">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable4">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable5">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable4">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable5">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable4">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable5">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable4">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable5">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable4">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable5">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable4">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable5">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable4">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable5">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable4">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable5">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable4">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable5">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                            </tr>
                            <tr>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable4">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable0">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable3">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                              <td class="loaderTable5">
                                <div class="timeline-item loaderTable1">
                                  <div class="animated-background loaderTable2">
                                  </div>
                                </div>
                              </td>
                            </tr>

                          </tbody>
                        </table>
                      </div>

                      <table class="table craftTable dataTable text-center table-bordered savingTable">
                        <thead>
                          <tr>
                            <th colspan="3" class="class-p17ek">Aluminium - Global</th>
                            <th colspan="3" class="class-ka4ng">Purchase price</th>
                            <th colspan="3" class="class-n48z0">Savings opportunity</th>
                          </tr>
                          <tr>
                            <th>Month</th>
                            <th class="class-yqqxe" width="120">Price forecast<br>(USD per ton)
                            </th>
                            <th class="class-mypzt" style="width: 130px">Spend
                              exposure<br>(in %)
                            </th>
                            <th class="class-6fxkp">At minimum price for rolling 3
                              months
                            </th>
                            <th class="class-szacg">At current price for next 12
                              months
                            </th>
                            <th class="class-rx0jv">At average price for rolling 3
                              months
                            </th>
                            <th class="class-um82w">At minimum price for rolling 3
                              months
                            </th>
                            <th class="class-iljbs">At current price for next 12
                              months
                            </th>
                            <th class="class-b5t4c">At average price for rolling 3
                              months
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td data-previous="2508.95" style="width: 51px !important;">Sep-2025</td>
                            <td class="forcastVal">2547.8</td>
                            <td> <span class="irs irs--round js-irs-2 irs-with-grid"><span class="irs"><span
                                    class="irs-line" tabindex="0"></span><span class="irs-min"
                                    style="visibility: visible;">0</span><span class="irs-max"
                                    style="visibility: visible;">100</span><span class="irs-from"
                                    style="visibility: hidden;">0</span><span class="irs-to"
                                    style="visibility: hidden;">0</span><span class="irs-single"
                                    style="left: 39.6864%;">50</span></span><span class="irs-grid"
                                  style="width: 91.1661%; left: 4.31696%;"><span class="irs-grid-pol"
                                    style="left: 0%"></span><span class="irs-grid-text js-grid-text-0"
                                    style="left: 0%; margin-left: 0%;">0</span><span class="irs-grid-pol small"
                                    style="left: 6.666666666666666%"></span><span class="irs-grid-pol small"
                                    style="left: 3.333333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 10%"></span><span class="irs-grid-text js-grid-text-1"
                                    style="left: 10%; visibility: visible; margin-left: 0%;">10</span><span
                                    class="irs-grid-pol small" style="left: 16.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 13.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 20%"></span><span
                                    class="irs-grid-text js-grid-text-2"
                                    style="left: 20%; visibility: visible; margin-left: 0%;">20</span><span
                                    class="irs-grid-pol small" style="left: 26.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 23.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 30%"></span><span
                                    class="irs-grid-text js-grid-text-3"
                                    style="left: 30%; visibility: visible; margin-left: 0%;">30</span><span
                                    class="irs-grid-pol small" style="left: 36.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 33.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 40%"></span><span
                                    class="irs-grid-text js-grid-text-4"
                                    style="left: 40%; margin-left: 0%;">40</span><span class="irs-grid-pol small"
                                    style="left: 46.666666666666664%"></span><span class="irs-grid-pol small"
                                    style="left: 43.333333333333336%"></span><span class="irs-grid-pol"
                                    style="left: 50%"></span><span class="irs-grid-text js-grid-text-5"
                                    style="left: 50%; visibility: visible; margin-left: 0%;">50</span><span
                                    class="irs-grid-pol small" style="left: 56.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 53.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 60%"></span><span
                                    class="irs-grid-text js-grid-text-6"
                                    style="left: 60%; visibility: visible; margin-left: 0%;">60</span><span
                                    class="irs-grid-pol small" style="left: 66.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 63.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 70%"></span><span
                                    class="irs-grid-text js-grid-text-7"
                                    style="left: 70%; visibility: visible; margin-left: 0%;">70</span><span
                                    class="irs-grid-pol small" style="left: 76.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 73.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 80%"></span><span
                                    class="irs-grid-text js-grid-text-8"
                                    style="left: 80%; margin-left: 0%;">80</span><span class="irs-grid-pol small"
                                    style="left: 86.66666666666667%"></span><span class="irs-grid-pol small"
                                    style="left: 83.33333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 90%"></span><span class="irs-grid-text js-grid-text-9"
                                    style="left: 90%; visibility: visible; margin-left: 0%;">90</span><span
                                    class="irs-grid-pol small" style="left: 96.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 93.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 100%"></span><span
                                    class="irs-grid-text js-grid-text-10"
                                    style="left: 100%; visibility: visible; margin-left: 0%;">100</span></span><span
                                  class="irs-bar irs-bar--single" style="left: 0px; width: 50%;"></span><span
                                  class="irs-shadow shadow-single" style="display: none;"></span><span
                                  class="irs-handle single"
                                  style="left: 45.583%;"><i></i><i></i><i></i></span></span><input type="text"
                                class="js-range-slider weight_range rangeSlider_ irs-hidden-input"
                                data-slug="trading-across-borders" id="rangeSlider_3" name="weight_range" value="50.00"
                                tabindex="-1" readonly="">
                            </td>
                            <td class="minVal" data-minval="2472.59">2472.59</td>
                            <td class="priceForecastCurrentVal">2544.19</td>
                            <td class="avgVal" data-avg="2521.53">2521.53</td>
                            <td class="savingMinVal">0.00</td>
                            <td class="savingCurrentVal">0.00</td>
                            <td class="savingAvgVal">0.00</td>
                          </tr>
                          <tr>
                            <td data-previous="2547.8" style="width: 51px !important;">Oct-2025</td>
                            <td class="forcastVal">2606.48</td>
                            <td> <span class="irs irs--round js-irs-3 irs-with-grid"><span class="irs"><span
                                    class="irs-line" tabindex="0"></span><span class="irs-min"
                                    style="visibility: visible;">0</span><span class="irs-max"
                                    style="visibility: visible;">100</span><span class="irs-from"
                                    style="visibility: hidden;">0</span><span class="irs-to"
                                    style="visibility: hidden;">0</span><span class="irs-single"
                                    style="left: 39.6864%;">50</span></span><span class="irs-grid"
                                  style="width: 91.1661%; left: 4.31696%;"><span class="irs-grid-pol"
                                    style="left: 0%"></span><span class="irs-grid-text js-grid-text-0"
                                    style="left: 0%; margin-left: 0%;">0</span><span class="irs-grid-pol small"
                                    style="left: 6.666666666666666%"></span><span class="irs-grid-pol small"
                                    style="left: 3.333333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 10%"></span><span class="irs-grid-text js-grid-text-1"
                                    style="left: 10%; visibility: visible; margin-left: 0%;">10</span><span
                                    class="irs-grid-pol small" style="left: 16.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 13.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 20%"></span><span
                                    class="irs-grid-text js-grid-text-2"
                                    style="left: 20%; visibility: visible; margin-left: 0%;">20</span><span
                                    class="irs-grid-pol small" style="left: 26.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 23.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 30%"></span><span
                                    class="irs-grid-text js-grid-text-3"
                                    style="left: 30%; visibility: visible; margin-left: 0%;">30</span><span
                                    class="irs-grid-pol small" style="left: 36.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 33.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 40%"></span><span
                                    class="irs-grid-text js-grid-text-4"
                                    style="left: 40%; margin-left: 0%;">40</span><span class="irs-grid-pol small"
                                    style="left: 46.666666666666664%"></span><span class="irs-grid-pol small"
                                    style="left: 43.333333333333336%"></span><span class="irs-grid-pol"
                                    style="left: 50%"></span><span class="irs-grid-text js-grid-text-5"
                                    style="left: 50%; visibility: visible; margin-left: 0%;">50</span><span
                                    class="irs-grid-pol small" style="left: 56.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 53.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 60%"></span><span
                                    class="irs-grid-text js-grid-text-6"
                                    style="left: 60%; visibility: visible; margin-left: 0%;">60</span><span
                                    class="irs-grid-pol small" style="left: 66.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 63.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 70%"></span><span
                                    class="irs-grid-text js-grid-text-7"
                                    style="left: 70%; visibility: visible; margin-left: 0%;">70</span><span
                                    class="irs-grid-pol small" style="left: 76.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 73.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 80%"></span><span
                                    class="irs-grid-text js-grid-text-8"
                                    style="left: 80%; margin-left: 0%;">80</span><span class="irs-grid-pol small"
                                    style="left: 86.66666666666667%"></span><span class="irs-grid-pol small"
                                    style="left: 83.33333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 90%"></span><span class="irs-grid-text js-grid-text-9"
                                    style="left: 90%; visibility: visible; margin-left: 0%;">90</span><span
                                    class="irs-grid-pol small" style="left: 96.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 93.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 100%"></span><span
                                    class="irs-grid-text js-grid-text-10"
                                    style="left: 100%; visibility: visible; margin-left: 0%;">100</span></span><span
                                  class="irs-bar irs-bar--single" style="left: 0px; width: 50%;"></span><span
                                  class="irs-shadow shadow-single" style="display: none;"></span><span
                                  class="irs-handle single"
                                  style="left: 45.583%;"><i></i><i></i><i></i></span></span><input type="text"
                                class="js-range-slider weight_range rangeSlider_ irs-hidden-input"
                                data-slug="trading-across-borders" id="rangeSlider_4" name="weight_range" value="50.00"
                                tabindex="-1" readonly="">
                            </td>
                            <td class="minVal" data-minval="2453.71">2453.71</td>
                            <td class="priceForecastCurrentVal">2544.19</td>
                            <td class="avgVal" data-avg="2517.18">2517.18</td>
                            <td class="savingMinVal">0.00</td>
                            <td class="savingCurrentVal">0.00</td>
                            <td class="savingAvgVal">0.00</td>
                          </tr>
                          <tr>
                            <td data-previous="2547.8" style="width: 51px !important;">Nov-2025</td>
                            <td class="forcastVal">2453.71</td>
                            <td> <span class="irs irs--round js-irs-4 irs-with-grid"><span class="irs"><span
                                    class="irs-line" tabindex="0"></span><span class="irs-min"
                                    style="visibility: visible;">0</span><span class="irs-max"
                                    style="visibility: visible;">100</span><span class="irs-from"
                                    style="visibility: hidden;">0</span><span class="irs-to"
                                    style="visibility: hidden;">0</span><span class="irs-single"
                                    style="left: 39.6864%;">50</span></span><span class="irs-grid"
                                  style="width: 91.1661%; left: 4.31696%;"><span class="irs-grid-pol"
                                    style="left: 0%"></span><span class="irs-grid-text js-grid-text-0"
                                    style="left: 0%; margin-left: 0%;">0</span><span class="irs-grid-pol small"
                                    style="left: 6.666666666666666%"></span><span class="irs-grid-pol small"
                                    style="left: 3.333333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 10%"></span><span class="irs-grid-text js-grid-text-1"
                                    style="left: 10%; visibility: visible; margin-left: 0%;">10</span><span
                                    class="irs-grid-pol small" style="left: 16.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 13.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 20%"></span><span
                                    class="irs-grid-text js-grid-text-2"
                                    style="left: 20%; visibility: visible; margin-left: 0%;">20</span><span
                                    class="irs-grid-pol small" style="left: 26.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 23.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 30%"></span><span
                                    class="irs-grid-text js-grid-text-3"
                                    style="left: 30%; visibility: visible; margin-left: 0%;">30</span><span
                                    class="irs-grid-pol small" style="left: 36.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 33.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 40%"></span><span
                                    class="irs-grid-text js-grid-text-4"
                                    style="left: 40%; margin-left: 0%;">40</span><span class="irs-grid-pol small"
                                    style="left: 46.666666666666664%"></span><span class="irs-grid-pol small"
                                    style="left: 43.333333333333336%"></span><span class="irs-grid-pol"
                                    style="left: 50%"></span><span class="irs-grid-text js-grid-text-5"
                                    style="left: 50%; visibility: visible; margin-left: 0%;">50</span><span
                                    class="irs-grid-pol small" style="left: 56.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 53.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 60%"></span><span
                                    class="irs-grid-text js-grid-text-6"
                                    style="left: 60%; visibility: visible; margin-left: 0%;">60</span><span
                                    class="irs-grid-pol small" style="left: 66.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 63.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 70%"></span><span
                                    class="irs-grid-text js-grid-text-7"
                                    style="left: 70%; visibility: visible; margin-left: 0%;">70</span><span
                                    class="irs-grid-pol small" style="left: 76.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 73.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 80%"></span><span
                                    class="irs-grid-text js-grid-text-8"
                                    style="left: 80%; margin-left: 0%;">80</span><span class="irs-grid-pol small"
                                    style="left: 86.66666666666667%"></span><span class="irs-grid-pol small"
                                    style="left: 83.33333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 90%"></span><span class="irs-grid-text js-grid-text-9"
                                    style="left: 90%; visibility: visible; margin-left: 0%;">90</span><span
                                    class="irs-grid-pol small" style="left: 96.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 93.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 100%"></span><span
                                    class="irs-grid-text js-grid-text-10"
                                    style="left: 100%; visibility: visible; margin-left: 0%;">100</span></span><span
                                  class="irs-bar irs-bar--single" style="left: 0px; width: 50%;"></span><span
                                  class="irs-shadow shadow-single" style="display: none;"></span><span
                                  class="irs-handle single"
                                  style="left: 45.583%;"><i></i><i></i><i></i></span></span><input type="text"
                                class="js-range-slider weight_range rangeSlider_ irs-hidden-input"
                                data-slug="trading-across-borders" id="rangeSlider_5" name="weight_range" value="50.00"
                                tabindex="-1" readonly="">
                            </td>
                            <td class="minVal" data-minval="2453.71">2453.71</td>
                            <td class="priceForecastCurrentVal">2544.19</td>
                            <td class="avgVal" data-avg="2517.18">2517.18</td>
                            <td class="savingMinVal">0.00</td>
                            <td class="savingCurrentVal">0.00</td>
                            <td class="savingAvgVal">0.00</td>
                          </tr>
                          <tr>
                            <td data-previous="2547.8" style="width: 51px !important;">Dec-2025</td>
                            <td class="forcastVal">2491.34</td>
                            <td> <span class="irs irs--round js-irs-5 irs-with-grid"><span class="irs"><span
                                    class="irs-line" tabindex="0"></span><span class="irs-min"
                                    style="visibility: visible;">0</span><span class="irs-max"
                                    style="visibility: visible;">100</span><span class="irs-from"
                                    style="visibility: hidden;">0</span><span class="irs-to"
                                    style="visibility: hidden;">0</span><span class="irs-single"
                                    style="left: 39.6864%;">50</span></span><span class="irs-grid"
                                  style="width: 91.1661%; left: 4.31696%;"><span class="irs-grid-pol"
                                    style="left: 0%"></span><span class="irs-grid-text js-grid-text-0"
                                    style="left: 0%; margin-left: 0%;">0</span><span class="irs-grid-pol small"
                                    style="left: 6.666666666666666%"></span><span class="irs-grid-pol small"
                                    style="left: 3.333333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 10%"></span><span class="irs-grid-text js-grid-text-1"
                                    style="left: 10%; visibility: visible; margin-left: 0%;">10</span><span
                                    class="irs-grid-pol small" style="left: 16.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 13.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 20%"></span><span
                                    class="irs-grid-text js-grid-text-2"
                                    style="left: 20%; visibility: visible; margin-left: 0%;">20</span><span
                                    class="irs-grid-pol small" style="left: 26.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 23.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 30%"></span><span
                                    class="irs-grid-text js-grid-text-3"
                                    style="left: 30%; visibility: visible; margin-left: 0%;">30</span><span
                                    class="irs-grid-pol small" style="left: 36.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 33.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 40%"></span><span
                                    class="irs-grid-text js-grid-text-4"
                                    style="left: 40%; margin-left: 0%;">40</span><span class="irs-grid-pol small"
                                    style="left: 46.666666666666664%"></span><span class="irs-grid-pol small"
                                    style="left: 43.333333333333336%"></span><span class="irs-grid-pol"
                                    style="left: 50%"></span><span class="irs-grid-text js-grid-text-5"
                                    style="left: 50%; visibility: visible; margin-left: 0%;">50</span><span
                                    class="irs-grid-pol small" style="left: 56.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 53.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 60%"></span><span
                                    class="irs-grid-text js-grid-text-6"
                                    style="left: 60%; visibility: visible; margin-left: 0%;">60</span><span
                                    class="irs-grid-pol small" style="left: 66.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 63.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 70%"></span><span
                                    class="irs-grid-text js-grid-text-7"
                                    style="left: 70%; visibility: visible; margin-left: 0%;">70</span><span
                                    class="irs-grid-pol small" style="left: 76.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 73.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 80%"></span><span
                                    class="irs-grid-text js-grid-text-8"
                                    style="left: 80%; margin-left: 0%;">80</span><span class="irs-grid-pol small"
                                    style="left: 86.66666666666667%"></span><span class="irs-grid-pol small"
                                    style="left: 83.33333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 90%"></span><span class="irs-grid-text js-grid-text-9"
                                    style="left: 90%; visibility: visible; margin-left: 0%;">90</span><span
                                    class="irs-grid-pol small" style="left: 96.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 93.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 100%"></span><span
                                    class="irs-grid-text js-grid-text-10"
                                    style="left: 100%; visibility: visible; margin-left: 0%;">100</span></span><span
                                  class="irs-bar irs-bar--single" style="left: 0px; width: 50%;"></span><span
                                  class="irs-shadow shadow-single" style="display: none;"></span><span
                                  class="irs-handle single"
                                  style="left: 45.583%;"><i></i><i></i><i></i></span></span><input type="text"
                                class="js-range-slider weight_range rangeSlider_ irs-hidden-input"
                                data-slug="trading-across-borders" id="rangeSlider_6" name="weight_range" value="50.00"
                                tabindex="-1" readonly="">
                            </td>
                            <td class="minVal" data-minval="2453.71">2453.71</td>
                            <td class="priceForecastCurrentVal">2544.19</td>
                            <td class="avgVal" data-avg="2517.18">2517.18</td>
                            <td class="savingMinVal">0.00</td>
                            <td class="savingCurrentVal">0.00</td>
                            <td class="savingAvgVal">0.00</td>
                          </tr>
                          <tr>
                            <td data-previous="2491.34" style="width: 51px !important;">Jan-2026</td>
                            <td class="forcastVal">2564.89</td>
                            <td> <span class="irs irs--round js-irs-6 irs-with-grid"><span class="irs"><span
                                    class="irs-line" tabindex="0"></span><span class="irs-min"
                                    style="visibility: visible;">0</span><span class="irs-max"
                                    style="visibility: visible;">100</span><span class="irs-from"
                                    style="visibility: hidden;">0</span><span class="irs-to"
                                    style="visibility: hidden;">0</span><span class="irs-single"
                                    style="left: 39.6864%;">50</span></span><span class="irs-grid"
                                  style="width: 91.1661%; left: 4.31696%;"><span class="irs-grid-pol"
                                    style="left: 0%"></span><span class="irs-grid-text js-grid-text-0"
                                    style="left: 0%; margin-left: 0%;">0</span><span class="irs-grid-pol small"
                                    style="left: 6.666666666666666%"></span><span class="irs-grid-pol small"
                                    style="left: 3.333333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 10%"></span><span class="irs-grid-text js-grid-text-1"
                                    style="left: 10%; visibility: visible; margin-left: 0%;">10</span><span
                                    class="irs-grid-pol small" style="left: 16.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 13.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 20%"></span><span
                                    class="irs-grid-text js-grid-text-2"
                                    style="left: 20%; visibility: visible; margin-left: 0%;">20</span><span
                                    class="irs-grid-pol small" style="left: 26.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 23.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 30%"></span><span
                                    class="irs-grid-text js-grid-text-3"
                                    style="left: 30%; visibility: visible; margin-left: 0%;">30</span><span
                                    class="irs-grid-pol small" style="left: 36.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 33.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 40%"></span><span
                                    class="irs-grid-text js-grid-text-4"
                                    style="left: 40%; margin-left: 0%;">40</span><span class="irs-grid-pol small"
                                    style="left: 46.666666666666664%"></span><span class="irs-grid-pol small"
                                    style="left: 43.333333333333336%"></span><span class="irs-grid-pol"
                                    style="left: 50%"></span><span class="irs-grid-text js-grid-text-5"
                                    style="left: 50%; visibility: visible; margin-left: 0%;">50</span><span
                                    class="irs-grid-pol small" style="left: 56.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 53.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 60%"></span><span
                                    class="irs-grid-text js-grid-text-6"
                                    style="left: 60%; visibility: visible; margin-left: 0%;">60</span><span
                                    class="irs-grid-pol small" style="left: 66.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 63.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 70%"></span><span
                                    class="irs-grid-text js-grid-text-7"
                                    style="left: 70%; visibility: visible; margin-left: 0%;">70</span><span
                                    class="irs-grid-pol small" style="left: 76.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 73.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 80%"></span><span
                                    class="irs-grid-text js-grid-text-8"
                                    style="left: 80%; margin-left: 0%;">80</span><span class="irs-grid-pol small"
                                    style="left: 86.66666666666667%"></span><span class="irs-grid-pol small"
                                    style="left: 83.33333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 90%"></span><span class="irs-grid-text js-grid-text-9"
                                    style="left: 90%; visibility: visible; margin-left: 0%;">90</span><span
                                    class="irs-grid-pol small" style="left: 96.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 93.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 100%"></span><span
                                    class="irs-grid-text js-grid-text-10"
                                    style="left: 100%; visibility: visible; margin-left: 0%;">100</span></span><span
                                  class="irs-bar irs-bar--single" style="left: 0px; width: 50%;"></span><span
                                  class="irs-shadow shadow-single" style="display: none;"></span><span
                                  class="irs-handle single"
                                  style="left: 45.583%;"><i></i><i></i><i></i></span></span><input type="text"
                                class="js-range-slider weight_range rangeSlider_ irs-hidden-input"
                                data-slug="trading-across-borders" id="rangeSlider_7" name="weight_range" value="50.00"
                                tabindex="-1" readonly="">
                            </td>
                            <td class="minVal" data-minval="2497.16">2497.16</td>
                            <td class="priceForecastCurrentVal">2544.19</td>
                            <td class="avgVal" data-avg="2549.78">2549.78</td>
                            <td class="savingMinVal">0.00</td>
                            <td class="savingCurrentVal">0.00</td>
                            <td class="savingAvgVal">0.00</td>
                          </tr>
                          <tr>
                            <td data-previous="2491.34" style="width: 51px !important;">Feb-2026</td>
                            <td class="forcastVal">2497.16</td>
                            <td> <span class="irs irs--round js-irs-7 irs-with-grid"><span class="irs"><span
                                    class="irs-line" tabindex="0"></span><span class="irs-min"
                                    style="visibility: visible;">0</span><span class="irs-max"
                                    style="visibility: visible;">100</span><span class="irs-from"
                                    style="visibility: hidden;">0</span><span class="irs-to"
                                    style="visibility: hidden;">0</span><span class="irs-single"
                                    style="left: 39.6864%;">50</span></span><span class="irs-grid"
                                  style="width: 91.1661%; left: 4.31696%;"><span class="irs-grid-pol"
                                    style="left: 0%"></span><span class="irs-grid-text js-grid-text-0"
                                    style="left: 0%; margin-left: 0%;">0</span><span class="irs-grid-pol small"
                                    style="left: 6.666666666666666%"></span><span class="irs-grid-pol small"
                                    style="left: 3.333333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 10%"></span><span class="irs-grid-text js-grid-text-1"
                                    style="left: 10%; visibility: visible; margin-left: 0%;">10</span><span
                                    class="irs-grid-pol small" style="left: 16.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 13.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 20%"></span><span
                                    class="irs-grid-text js-grid-text-2"
                                    style="left: 20%; visibility: visible; margin-left: 0%;">20</span><span
                                    class="irs-grid-pol small" style="left: 26.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 23.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 30%"></span><span
                                    class="irs-grid-text js-grid-text-3"
                                    style="left: 30%; visibility: visible; margin-left: 0%;">30</span><span
                                    class="irs-grid-pol small" style="left: 36.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 33.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 40%"></span><span
                                    class="irs-grid-text js-grid-text-4"
                                    style="left: 40%; margin-left: 0%;">40</span><span class="irs-grid-pol small"
                                    style="left: 46.666666666666664%"></span><span class="irs-grid-pol small"
                                    style="left: 43.333333333333336%"></span><span class="irs-grid-pol"
                                    style="left: 50%"></span><span class="irs-grid-text js-grid-text-5"
                                    style="left: 50%; visibility: visible; margin-left: 0%;">50</span><span
                                    class="irs-grid-pol small" style="left: 56.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 53.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 60%"></span><span
                                    class="irs-grid-text js-grid-text-6"
                                    style="left: 60%; visibility: visible; margin-left: 0%;">60</span><span
                                    class="irs-grid-pol small" style="left: 66.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 63.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 70%"></span><span
                                    class="irs-grid-text js-grid-text-7"
                                    style="left: 70%; visibility: visible; margin-left: 0%;">70</span><span
                                    class="irs-grid-pol small" style="left: 76.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 73.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 80%"></span><span
                                    class="irs-grid-text js-grid-text-8"
                                    style="left: 80%; margin-left: 0%;">80</span><span class="irs-grid-pol small"
                                    style="left: 86.66666666666667%"></span><span class="irs-grid-pol small"
                                    style="left: 83.33333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 90%"></span><span class="irs-grid-text js-grid-text-9"
                                    style="left: 90%; visibility: visible; margin-left: 0%;">90</span><span
                                    class="irs-grid-pol small" style="left: 96.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 93.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 100%"></span><span
                                    class="irs-grid-text js-grid-text-10"
                                    style="left: 100%; visibility: visible; margin-left: 0%;">100</span></span><span
                                  class="irs-bar irs-bar--single" style="left: 0px; width: 50%;"></span><span
                                  class="irs-shadow shadow-single" style="display: none;"></span><span
                                  class="irs-handle single"
                                  style="left: 45.583%;"><i></i><i></i><i></i></span></span><input type="text"
                                class="js-range-slider weight_range rangeSlider_ irs-hidden-input"
                                data-slug="trading-across-borders" id="rangeSlider_8" name="weight_range" value="50.00"
                                tabindex="-1" readonly="">
                            </td>
                            <td class="minVal" data-minval="2497.16">2497.16</td>
                            <td class="priceForecastCurrentVal">2544.19</td>
                            <td class="avgVal" data-avg="2549.78">2549.78</td>
                            <td class="savingMinVal">0.00</td>
                            <td class="savingCurrentVal">0.00</td>
                            <td class="savingAvgVal">0.00</td>
                          </tr>
                          <tr>
                            <td data-previous="2491.34" style="width: 51px !important;">Mar-2026</td>
                            <td class="forcastVal">2587.3</td>
                            <td> <span class="irs irs--round js-irs-8 irs-with-grid"><span class="irs"><span
                                    class="irs-line" tabindex="0"></span><span class="irs-min"
                                    style="visibility: visible;">0</span><span class="irs-max"
                                    style="visibility: visible;">100</span><span class="irs-from"
                                    style="visibility: hidden;">0</span><span class="irs-to"
                                    style="visibility: hidden;">0</span><span class="irs-single"
                                    style="left: 39.6864%;">50</span></span><span class="irs-grid"
                                  style="width: 91.1661%; left: 4.31696%;"><span class="irs-grid-pol"
                                    style="left: 0%"></span><span class="irs-grid-text js-grid-text-0"
                                    style="left: 0%; margin-left: 0%;">0</span><span class="irs-grid-pol small"
                                    style="left: 6.666666666666666%"></span><span class="irs-grid-pol small"
                                    style="left: 3.333333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 10%"></span><span class="irs-grid-text js-grid-text-1"
                                    style="left: 10%; visibility: visible; margin-left: 0%;">10</span><span
                                    class="irs-grid-pol small" style="left: 16.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 13.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 20%"></span><span
                                    class="irs-grid-text js-grid-text-2"
                                    style="left: 20%; visibility: visible; margin-left: 0%;">20</span><span
                                    class="irs-grid-pol small" style="left: 26.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 23.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 30%"></span><span
                                    class="irs-grid-text js-grid-text-3"
                                    style="left: 30%; visibility: visible; margin-left: 0%;">30</span><span
                                    class="irs-grid-pol small" style="left: 36.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 33.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 40%"></span><span
                                    class="irs-grid-text js-grid-text-4"
                                    style="left: 40%; margin-left: 0%;">40</span><span class="irs-grid-pol small"
                                    style="left: 46.666666666666664%"></span><span class="irs-grid-pol small"
                                    style="left: 43.333333333333336%"></span><span class="irs-grid-pol"
                                    style="left: 50%"></span><span class="irs-grid-text js-grid-text-5"
                                    style="left: 50%; visibility: visible; margin-left: 0%;">50</span><span
                                    class="irs-grid-pol small" style="left: 56.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 53.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 60%"></span><span
                                    class="irs-grid-text js-grid-text-6"
                                    style="left: 60%; visibility: visible; margin-left: 0%;">60</span><span
                                    class="irs-grid-pol small" style="left: 66.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 63.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 70%"></span><span
                                    class="irs-grid-text js-grid-text-7"
                                    style="left: 70%; visibility: visible; margin-left: 0%;">70</span><span
                                    class="irs-grid-pol small" style="left: 76.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 73.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 80%"></span><span
                                    class="irs-grid-text js-grid-text-8"
                                    style="left: 80%; margin-left: 0%;">80</span><span class="irs-grid-pol small"
                                    style="left: 86.66666666666667%"></span><span class="irs-grid-pol small"
                                    style="left: 83.33333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 90%"></span><span class="irs-grid-text js-grid-text-9"
                                    style="left: 90%; visibility: visible; margin-left: 0%;">90</span><span
                                    class="irs-grid-pol small" style="left: 96.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 93.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 100%"></span><span
                                    class="irs-grid-text js-grid-text-10"
                                    style="left: 100%; visibility: visible; margin-left: 0%;">100</span></span><span
                                  class="irs-bar irs-bar--single" style="left: 0px; width: 50%;"></span><span
                                  class="irs-shadow shadow-single" style="display: none;"></span><span
                                  class="irs-handle single"
                                  style="left: 45.583%;"><i></i><i></i><i></i></span></span><input type="text"
                                class="js-range-slider weight_range rangeSlider_ irs-hidden-input"
                                data-slug="trading-across-borders" id="rangeSlider_9" name="weight_range" value="50.00"
                                tabindex="-1" readonly="">
                            </td>
                            <td class="minVal" data-minval="2497.16">2497.16</td>
                            <td class="priceForecastCurrentVal">2544.19</td>
                            <td class="avgVal" data-avg="2549.78">2549.78</td>
                            <td class="savingMinVal">0.00</td>
                            <td class="savingCurrentVal">0.00</td>
                            <td class="savingAvgVal">0.00</td>
                          </tr>
                          <tr>
                            <td data-previous="2491.34" style="width: 51px !important;">Jan-2026</td>
                            <td class="forcastVal">2564.89</td>
                            <td> <span class="irs irs--round js-irs-6 irs-with-grid"><span class="irs"><span
                                    class="irs-line" tabindex="0"></span><span class="irs-min"
                                    style="visibility: visible;">0</span><span class="irs-max"
                                    style="visibility: visible;">100</span><span class="irs-from"
                                    style="visibility: hidden;">0</span><span class="irs-to"
                                    style="visibility: hidden;">0</span><span class="irs-single"
                                    style="left: 39.6864%;">50</span></span><span class="irs-grid"
                                  style="width: 91.1661%; left: 4.31696%;"><span class="irs-grid-pol"
                                    style="left: 0%"></span><span class="irs-grid-text js-grid-text-0"
                                    style="left: 0%; margin-left: 0%;">0</span><span class="irs-grid-pol small"
                                    style="left: 6.666666666666666%"></span><span class="irs-grid-pol small"
                                    style="left: 3.333333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 10%"></span><span class="irs-grid-text js-grid-text-1"
                                    style="left: 10%; visibility: visible; margin-left: 0%;">10</span><span
                                    class="irs-grid-pol small" style="left: 16.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 13.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 20%"></span><span
                                    class="irs-grid-text js-grid-text-2"
                                    style="left: 20%; visibility: visible; margin-left: 0%;">20</span><span
                                    class="irs-grid-pol small" style="left: 26.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 23.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 30%"></span><span
                                    class="irs-grid-text js-grid-text-3"
                                    style="left: 30%; visibility: visible; margin-left: 0%;">30</span><span
                                    class="irs-grid-pol small" style="left: 36.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 33.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 40%"></span><span
                                    class="irs-grid-text js-grid-text-4"
                                    style="left: 40%; margin-left: 0%;">40</span><span class="irs-grid-pol small"
                                    style="left: 46.666666666666664%"></span><span class="irs-grid-pol small"
                                    style="left: 43.333333333333336%"></span><span class="irs-grid-pol"
                                    style="left: 50%"></span><span class="irs-grid-text js-grid-text-5"
                                    style="left: 50%; visibility: visible; margin-left: 0%;">50</span><span
                                    class="irs-grid-pol small" style="left: 56.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 53.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 60%"></span><span
                                    class="irs-grid-text js-grid-text-6"
                                    style="left: 60%; visibility: visible; margin-left: 0%;">60</span><span
                                    class="irs-grid-pol small" style="left: 66.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 63.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 70%"></span><span
                                    class="irs-grid-text js-grid-text-7"
                                    style="left: 70%; visibility: visible; margin-left: 0%;">70</span><span
                                    class="irs-grid-pol small" style="left: 76.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 73.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 80%"></span><span
                                    class="irs-grid-text js-grid-text-8"
                                    style="left: 80%; margin-left: 0%;">80</span><span class="irs-grid-pol small"
                                    style="left: 86.66666666666667%"></span><span class="irs-grid-pol small"
                                    style="left: 83.33333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 90%"></span><span class="irs-grid-text js-grid-text-9"
                                    style="left: 90%; visibility: visible; margin-left: 0%;">90</span><span
                                    class="irs-grid-pol small" style="left: 96.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 93.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 100%"></span><span
                                    class="irs-grid-text js-grid-text-10"
                                    style="left: 100%; visibility: visible; margin-left: 0%;">100</span></span><span
                                  class="irs-bar irs-bar--single" style="left: 0px; width: 50%;"></span><span
                                  class="irs-shadow shadow-single" style="display: none;"></span><span
                                  class="irs-handle single"
                                  style="left: 45.583%;"><i></i><i></i><i></i></span></span><input type="text"
                                class="js-range-slider weight_range rangeSlider_ irs-hidden-input"
                                data-slug="trading-across-borders" id="rangeSlider_7" name="weight_range" value="50.00"
                                tabindex="-1" readonly="">
                            </td>
                            <td class="minVal" data-minval="2497.16">2497.16</td>
                            <td class="priceForecastCurrentVal">2544.19</td>
                            <td class="avgVal" data-avg="2549.78">2549.78</td>
                            <td class="savingMinVal">0.00</td>
                            <td class="savingCurrentVal">0.00</td>
                            <td class="savingAvgVal">0.00</td>
                          </tr>
                          <tr>
                            <td data-previous="2491.34" style="width: 51px !important;">Feb-2026</td>
                            <td class="forcastVal">2497.16</td>
                            <td> <span class="irs irs--round js-irs-7 irs-with-grid"><span class="irs"><span
                                    class="irs-line" tabindex="0"></span><span class="irs-min"
                                    style="visibility: visible;">0</span><span class="irs-max"
                                    style="visibility: visible;">100</span><span class="irs-from"
                                    style="visibility: hidden;">0</span><span class="irs-to"
                                    style="visibility: hidden;">0</span><span class="irs-single"
                                    style="left: 39.6864%;">50</span></span><span class="irs-grid"
                                  style="width: 91.1661%; left: 4.31696%;"><span class="irs-grid-pol"
                                    style="left: 0%"></span><span class="irs-grid-text js-grid-text-0"
                                    style="left: 0%; margin-left: 0%;">0</span><span class="irs-grid-pol small"
                                    style="left: 6.666666666666666%"></span><span class="irs-grid-pol small"
                                    style="left: 3.333333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 10%"></span><span class="irs-grid-text js-grid-text-1"
                                    style="left: 10%; visibility: visible; margin-left: 0%;">10</span><span
                                    class="irs-grid-pol small" style="left: 16.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 13.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 20%"></span><span
                                    class="irs-grid-text js-grid-text-2"
                                    style="left: 20%; visibility: visible; margin-left: 0%;">20</span><span
                                    class="irs-grid-pol small" style="left: 26.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 23.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 30%"></span><span
                                    class="irs-grid-text js-grid-text-3"
                                    style="left: 30%; visibility: visible; margin-left: 0%;">30</span><span
                                    class="irs-grid-pol small" style="left: 36.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 33.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 40%"></span><span
                                    class="irs-grid-text js-grid-text-4"
                                    style="left: 40%; margin-left: 0%;">40</span><span class="irs-grid-pol small"
                                    style="left: 46.666666666666664%"></span><span class="irs-grid-pol small"
                                    style="left: 43.333333333333336%"></span><span class="irs-grid-pol"
                                    style="left: 50%"></span><span class="irs-grid-text js-grid-text-5"
                                    style="left: 50%; visibility: visible; margin-left: 0%;">50</span><span
                                    class="irs-grid-pol small" style="left: 56.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 53.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 60%"></span><span
                                    class="irs-grid-text js-grid-text-6"
                                    style="left: 60%; visibility: visible; margin-left: 0%;">60</span><span
                                    class="irs-grid-pol small" style="left: 66.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 63.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 70%"></span><span
                                    class="irs-grid-text js-grid-text-7"
                                    style="left: 70%; visibility: visible; margin-left: 0%;">70</span><span
                                    class="irs-grid-pol small" style="left: 76.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 73.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 80%"></span><span
                                    class="irs-grid-text js-grid-text-8"
                                    style="left: 80%; margin-left: 0%;">80</span><span class="irs-grid-pol small"
                                    style="left: 86.66666666666667%"></span><span class="irs-grid-pol small"
                                    style="left: 83.33333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 90%"></span><span class="irs-grid-text js-grid-text-9"
                                    style="left: 90%; visibility: visible; margin-left: 0%;">90</span><span
                                    class="irs-grid-pol small" style="left: 96.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 93.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 100%"></span><span
                                    class="irs-grid-text js-grid-text-10"
                                    style="left: 100%; visibility: visible; margin-left: 0%;">100</span></span><span
                                  class="irs-bar irs-bar--single" style="left: 0px; width: 50%;"></span><span
                                  class="irs-shadow shadow-single" style="display: none;"></span><span
                                  class="irs-handle single"
                                  style="left: 45.583%;"><i></i><i></i><i></i></span></span><input type="text"
                                class="js-range-slider weight_range rangeSlider_ irs-hidden-input"
                                data-slug="trading-across-borders" id="rangeSlider_8" name="weight_range" value="50.00"
                                tabindex="-1" readonly="">
                            </td>
                            <td class="minVal" data-minval="2497.16">2497.16</td>
                            <td class="priceForecastCurrentVal">2544.19</td>
                            <td class="avgVal" data-avg="2549.78">2549.78</td>
                            <td class="savingMinVal">0.00</td>
                            <td class="savingCurrentVal">0.00</td>
                            <td class="savingAvgVal">0.00</td>
                          </tr>
                          <tr>
                            <td data-previous="2491.34" style="width: 51px !important;">Mar-2026</td>
                            <td class="forcastVal">2587.3</td>
                            <td> <span class="irs irs--round js-irs-8 irs-with-grid"><span class="irs"><span
                                    class="irs-line" tabindex="0"></span><span class="irs-min"
                                    style="visibility: visible;">0</span><span class="irs-max"
                                    style="visibility: visible;">100</span><span class="irs-from"
                                    style="visibility: hidden;">0</span><span class="irs-to"
                                    style="visibility: hidden;">0</span><span class="irs-single"
                                    style="left: 39.6864%;">50</span></span><span class="irs-grid"
                                  style="width: 91.1661%; left: 4.31696%;"><span class="irs-grid-pol"
                                    style="left: 0%"></span><span class="irs-grid-text js-grid-text-0"
                                    style="left: 0%; margin-left: 0%;">0</span><span class="irs-grid-pol small"
                                    style="left: 6.666666666666666%"></span><span class="irs-grid-pol small"
                                    style="left: 3.333333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 10%"></span><span class="irs-grid-text js-grid-text-1"
                                    style="left: 10%; visibility: visible; margin-left: 0%;">10</span><span
                                    class="irs-grid-pol small" style="left: 16.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 13.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 20%"></span><span
                                    class="irs-grid-text js-grid-text-2"
                                    style="left: 20%; visibility: visible; margin-left: 0%;">20</span><span
                                    class="irs-grid-pol small" style="left: 26.666666666666668%"></span><span
                                    class="irs-grid-pol small" style="left: 23.333333333333332%"></span><span
                                    class="irs-grid-pol" style="left: 30%"></span><span
                                    class="irs-grid-text js-grid-text-3"
                                    style="left: 30%; visibility: visible; margin-left: 0%;">30</span><span
                                    class="irs-grid-pol small" style="left: 36.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 33.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 40%"></span><span
                                    class="irs-grid-text js-grid-text-4"
                                    style="left: 40%; margin-left: 0%;">40</span><span class="irs-grid-pol small"
                                    style="left: 46.666666666666664%"></span><span class="irs-grid-pol small"
                                    style="left: 43.333333333333336%"></span><span class="irs-grid-pol"
                                    style="left: 50%"></span><span class="irs-grid-text js-grid-text-5"
                                    style="left: 50%; visibility: visible; margin-left: 0%;">50</span><span
                                    class="irs-grid-pol small" style="left: 56.666666666666664%"></span><span
                                    class="irs-grid-pol small" style="left: 53.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 60%"></span><span
                                    class="irs-grid-text js-grid-text-6"
                                    style="left: 60%; visibility: visible; margin-left: 0%;">60</span><span
                                    class="irs-grid-pol small" style="left: 66.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 63.333333333333336%"></span><span
                                    class="irs-grid-pol" style="left: 70%"></span><span
                                    class="irs-grid-text js-grid-text-7"
                                    style="left: 70%; visibility: visible; margin-left: 0%;">70</span><span
                                    class="irs-grid-pol small" style="left: 76.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 73.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 80%"></span><span
                                    class="irs-grid-text js-grid-text-8"
                                    style="left: 80%; margin-left: 0%;">80</span><span class="irs-grid-pol small"
                                    style="left: 86.66666666666667%"></span><span class="irs-grid-pol small"
                                    style="left: 83.33333333333333%"></span><span class="irs-grid-pol"
                                    style="left: 90%"></span><span class="irs-grid-text js-grid-text-9"
                                    style="left: 90%; visibility: visible; margin-left: 0%;">90</span><span
                                    class="irs-grid-pol small" style="left: 96.66666666666667%"></span><span
                                    class="irs-grid-pol small" style="left: 93.33333333333333%"></span><span
                                    class="irs-grid-pol" style="left: 100%"></span><span
                                    class="irs-grid-text js-grid-text-10"
                                    style="left: 100%; visibility: visible; margin-left: 0%;">100</span></span><span
                                  class="irs-bar irs-bar--single" style="left: 0px; width: 50%;"></span><span
                                  class="irs-shadow shadow-single" style="display: none;"></span><span
                                  class="irs-handle single"
                                  style="left: 45.583%;"><i></i><i></i><i></i></span></span><input type="text"
                                class="js-range-slider weight_range rangeSlider_ irs-hidden-input"
                                data-slug="trading-across-borders" id="rangeSlider_9" name="weight_range" value="50.00"
                                tabindex="-1" readonly="">
                            </td>
                            <td class="minVal" data-minval="2497.16">2497.16</td>
                            <td class="priceForecastCurrentVal">2544.19</td>
                            <td class="avgVal" data-avg="2549.78">2549.78</td>
                            <td class="savingMinVal">0.00</td>
                            <td class="savingCurrentVal">0.00</td>
                            <td class="savingAvgVal">0.00</td>
                          </tr>
                        </tbody>
                      </table>
                      <div class="d-flex align-items-center justify-content-end mt-2"><button type="button"
                          class="btn btn-primary btn-primary btn-sm ms-2" id="editWeightageButton12">Calculate</button>
                        <button type="button" class="btn btn-primary btn-secondary ms-1 btn-sm"
                          id="resetOpportunityCalculator">Reset</button>
                      </div>
                    </div>
                  </div>

                  <footer class="widget-footer">
                    <div class="mb-0">
                      <span class="size10 notes-keyInsight" style="display: none">
                        <i class="las la-clipboard size14"></i> Notes :
                        <span id="notekeyInsight">The Smart Cube The Smart Cube Knowledge
                          Repository; Westmetall</span>
                        <br />
                      </span>

                      <span class="size10 source-keyInsight">
                        <i class="las la-database size14"></i> Source :
                        <span id="sourcekeyInsight">The Smart Cube Research and Analysis</span>

                        <br />
                      </span>
                      <span class="size10 last-update-keyInsight">
                        <i class="las size14 la-calendar-minus"></i> Last
                        update :
                        <span id="lastUpdatekeyInsight">Apr-2025</span>
                      </span>
                      <span class="size10 next-update-keyInsight" id="hidenextUpdatekeyInsight">
                        <i class="las size14 la-calendar-plus"></i> Next
                        update :
                        <span id="nextUpdatekeyInsight">May-2025</span>
                      </span>
                    </div>
                  </footer>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </section>
  </div>
  <!-- Widget Gallery Modal -->
  <div class="modal fade" id="widgetGalleryModal" tabindex="-1" aria-labelledby="widgetGalleryModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="widgetGalleryModalLabel">
            Add Widgets
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-4 mb-3">
              <div class="card h-100 widget-gallery-card">
                <div class="card-body text-center">
                  <i class="las la-font la-2x mb-2"></i>
                  <h5>Key Insights</h5>
                  <p class="small">Add insights widget to your dashboard</p>
                  <button class="btn btn-primary btn-sm" onclick="addKeyInsightsWidget()">
                    Add Widget
                  </button>
                </div>
              </div>
            </div>
            <div class="col-md-4 mb-3">
              <div class="card h-100 widget-gallery-card">
                <div class="card-body text-center">
                  <i class="las la-chart-pie la-2x mb-2"></i>
                  <h5>Pie Chart</h5>
                  <p class="small">Add a pie chart visualization</p>
                  <button class="btn btn-primary btn-sm" onclick="addDummyWidget('Pie Chart', 'las la-chart-pie')">
                    Add Widget
                  </button>
                </div>
              </div>
            </div>
            <div class="col-md-4 mb-3">
              <div class="card h-100 widget-gallery-card">
                <div class="card-body text-center">
                  <i class="las la-chart-bar la-2x mb-2"></i>
                  <h5>Bar Chart</h5>
                  <p class="small">Add a bar chart visualization</p>
                  <button class="btn btn-primary btn-sm" onclick="addDummyWidget('Bar Chart', 'las la-chart-bar')">
                    Add Widget
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Preview Mode Overlay -->
  <div class="preview-overlay" id="preview-overlay">
    <p>Viewing dashboard in preview mode</p>
    <button id="exit-preview-btn">Exit Preview</button>
  </div>

  <div class="footer">
    <div class="container-fluid">
      <div class="row">
        <div class="col-sm-6">
          <a class="UserActivityLogger" actionname="Link click" pagename="Terms &amp; conditions" sectionofpage="Footer"
            comment="Click on terms &amp; conditions" href="https://amplifipro.thesmartcube.com/termsAndConditions">
            Terms &amp; conditions</a>
          |
          <a href="https://amplifipro.thesmartcube.com/faq">FAQ</a>

        </div>
        <div class="col-sm-6 text-end"> © 2025
          <a href="https://www.wnsprocurement.com/" target="_blank" class="UserActivityLogger" actionname="Link click"
            pagename="wnsprocurement.com" sectionofpage="Footer" comment="wnsprocurement.com link clicked.">
            wnsprocurement.com </a>
          All rights reserved
        </div>
      </div>
    </div>
  </div>
  <!-- JavaScript Libraries -->
  <!-- jQuery (required for many Bootstrap components) -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <!-- Bootstrap JS Bundle -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  <!-- GridStack JS -->
  <script src="https://cdn.jsdelivr.net/npm/gridstack@10.3.1/dist/gridstack-all.js"></script>
  <script src="https://amplifipro.thesmartcube.com/public/plugins/slimScroll/jquery.slimscroll.js?v=2.2.32"></script>

  <!-- AmCharts 5 Core - Load these BEFORE any chart creation -->
  <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
  <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
   <script src="https://cdn.amcharts.com/lib/5/percent.js"></script>
  <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>
  <script src="https://amplifipro.thesmartcube.com/public/js/ion.rangeSlider.min.js?v=2.2.32"></script>

  <!-- Widget Sidebar JS -->
  <script src="./js/widget-sidebar.js"></script>

  <!-- Initialize GridStack -->
  <script>
    // Add these global variables at the top to store chart references
    var chartReferences = {
      marketCondition: null,
      futureOutlook: null,
      marketDemand: null,
      priceIndex: null,
    };

    // Helper function to resize all charts
    function resizeAllCharts() {
      // Resize all available charts
      if (
        chartReferences.marketCondition &&
        chartReferences.marketCondition.root
      ) {
        chartReferences.marketCondition.root.resize();
      }
      if (
        chartReferences.futureOutlook &&
        chartReferences.futureOutlook.root
      ) {
        chartReferences.futureOutlook.root.resize();
      }
      if (chartReferences.marketDemand && chartReferences.marketDemand.root) {
        chartReferences.marketDemand.root.resize();
      }
      if (chartReferences.priceIndex && chartReferences.priceIndex.root) {
        chartReferences.priceIndex.root.resize();
      }
    }

    // Check if AmCharts is available
    function isAmChartsReady() {
      return (
        typeof am5 !== "undefined" && am5 && typeof am5.Root !== "undefined"
      );
    }

    // Utility: Check if a container is visible and has height
    function isContainerReady(id) {
      var el = document.getElementById(id);
      if (!el) return false;
      var style = window.getComputedStyle(el);
      return (
        style.display !== "none" &&
        style.visibility !== "hidden" &&
        el.offsetHeight > 0
      );
    }

    // New approach: Delayed chart creation with visibility check
    function createChartWithDelay(chartType, containerId, delay = 500) {
      console.log(
        "Scheduling chart creation for",
        chartType,
        "with container",
        containerId
      );

      // First, make sure the tab is active
      if (containerId === "chartDiv") {
        $("#market-condition-tab").tab("show");
      } else if (containerId === "futureChartDiv") {
        $("#future-outlook-tab").tab("show");
      } else if (containerId === "demandChartDiv") {
        $("#market-demand-tab").tab("show");
      }

      // Then wait for the container to be visible
      setTimeout(function () {
        if (!isAmChartsReady()) {
          console.error(
            "AmCharts not ready when trying to create " + chartType
          );
          return;
        }

        var container = document.getElementById(containerId);
        if (!container) {
          console.error("Container " + containerId + " not found");
          return;
        }

        // Ensure container has dimensions
        if (container.offsetWidth === 0 || container.offsetHeight === 0) {
          console.error(
            "Container " + containerId + " has zero dimensions:",
            container.offsetWidth + "x" + container.offsetHeight
          );
          // Force dimensions for chart container
          container.style.width = "100%";
          container.style.height = "300px";
          container.style.minHeight = "300px";
          container.style.display = "block";
        }

        console.log(
          "Creating " +
          chartType +
          " chart with dimensions: " +
          container.offsetWidth +
          "x" +
          container.offsetHeight
        );

        // Call the appropriate chart creation function
        if (chartType === "marketCondition") {
          createMarketConditionChart();
        } else if (chartType === "futureOutlook") {
          createFutureOutlookChart();
        } else if (chartType === "marketDemand") {
          createMarketDemandChart();
        }
      }, delay);
    }

    document.addEventListener("DOMContentLoaded", function () {
      // Initialize GridStack with drag handle settings
      const grid = GridStack.init({
        column: 12,
        cellHeight: 80,
        margin: 5,
        float: true,
        disableOneColumnMode: false,

        removable: ".trash",
        removeTimeout: 100,
        acceptWidgets: true,
        // Set the drag handle to only the widget-header elements
        draggable: {
          handle: ".widget-header",
          scroll: false,
          appendTo: "body",
        },
        // Configure resize handles
        resizable: {
          handles: "e, se, s, sw, w, nw, n, ne",
          autoHide: true,
        },
      });

      // Initialize tooltips
      const tooltipTriggerList = [].slice.call(
        document.querySelectorAll('[data-bs-toggle="tooltip"]')
      );
      const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
      });

      // Save to window object for external access
      window.grid = grid;

      // Override the removeWidget functionality to use our widget sidebar
      grid.removeWidget = function (el) {
        const element = el.el || el;
        // Instead of removing, we now use our hide widget function
        if (typeof hideWidget === "function") {
          hideWidget(element);
          return element;
        } else {
          // Fallback to original behavior
          return GridStack.prototype.removeWidget.call(this, el, false);
        }
      };

      // Add resize event listener - optimized for chart resizing
      grid.on("resizestop", function (event, element) {
        console.log("GridStack resize event triggered for element:", element);

        // Find chart containers within the resized widget
        const chartContainers = element.querySelectorAll('[id$="ChartDiv"]');
        if (chartContainers.length > 0) {
          chartContainers.forEach(function (container) {
            const chartId = container.id; // e.g., 'chartDiv'
            const chartKey =
              chartId === "chartDiv"
                ? "marketCondition"
                : chartId === "futureChartDiv"
                  ? "futureOutlook"
                  : "marketDemand";

            console.log(
              `Resized container: ${chartId}, dimensions: ${container.offsetWidth}x${container.offsetHeight}`
            );

            // Resize the chart if it exists and its container has dimensions
            if (chartReferences[chartKey] && chartReferences[chartKey].root) {
              if (container.offsetWidth > 0 && container.offsetHeight > 0) {
                console.log(
                  `Before resize - chart dimensions: ${chartReferences[
                    chartKey
                  ].root.width()}x${chartReferences[chartKey].root.height()}`
                );
                chartReferences[chartKey].root.resize();
                console.log(
                  `After resize - chart dimensions: ${chartReferences[
                    chartKey
                  ].root.width()}x${chartReferences[chartKey].root.height()}`
                );
              } else {
                console.log(
                  `Chart ${chartKey} not resized due to zero dimensions (possibly hidden)`
                );
              }
            } else if (
              container.offsetWidth > 0 &&
              container.offsetHeight > 0
            ) {
              // If chart doesn't exist but container is visible, create it
              console.log(
                `Chart ${chartKey} doesn't exist but container is visible - creating it now`
              );
              if (chartKey === "marketCondition") {
                createMarketConditionChart();
              } else if (chartKey === "futureOutlook") {
                createFutureOutlookChart();
              } else if (chartKey === "marketDemand") {
                createMarketDemandChart();
              }
            }
          });
        }
      });

      // Add a general resize handler on the window with debounce
      let resizeTimeout;
      window.addEventListener("resize", function () {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(function () {
          console.log("Window resize event - resizing visible charts");

          // Only resize charts in visible tabs
          if (
            document
              .getElementById("market-condition")
              .classList.contains("active") &&
            chartReferences.marketCondition &&
            chartReferences.marketCondition.root
          ) {
            chartReferences.marketCondition.root.resize();
          }

          if (
            document
              .getElementById("future-outlook")
              .classList.contains("active") &&
            chartReferences.futureOutlook &&
            chartReferences.futureOutlook.root
          ) {
            chartReferences.futureOutlook.root.resize();
          }

          if (
            document
              .getElementById("market-demand")
              .classList.contains("active") &&
            chartReferences.marketDemand &&
            chartReferences.marketDemand.root
          ) {
            chartReferences.marketDemand.root.resize();
          }
        }, 250);
      });

      // Initialize market insights charts if they exist with a longer timeout
      // Make sure AmCharts is fully loaded before initializing charts
      // Wait for AmCharts to load
      let attempts = 0;
      const chartInitInterval = setInterval(function () {
        if (isAmChartsReady()) {
          clearInterval(chartInitInterval);
          console.log("AmCharts is ready, initializing charts");

          // Make sure the first tab is active
          $("#market-condition-tab").tab("show");

          // Delay chart creation to ensure container is visible
          setTimeout(function () {
            if (document.getElementById("chartDiv")) {
              createChartWithDelay("marketCondition", "chartDiv", 100);
            }
          }, 300);
        } else {
          attempts++;
          console.log("Waiting for AmCharts to load... Attempt: " + attempts);
          if (attempts >= 10) {
            clearInterval(chartInitInterval);
            console.error("Failed to load AmCharts after 10 attempts");
          }
        }
      }, 500); // Check every 500ms

      // Add tab click handling to ensure charts are created when switching tabs
      const marketInsightsTabs = document.querySelectorAll(
        "#marketInsightsTabs .nav-link"
      );
      marketInsightsTabs.forEach(function (tab) {
        tab.addEventListener("shown.bs.tab", function (event) {
          // Get the target tab ID
          const targetTabId = event.target.getAttribute("data-bs-target");
          console.log("Tab changed to:", targetTabId);

          // Resize or create chart based on active tab
          if (targetTabId === "#market-condition") {
            if (!chartReferences.marketCondition) {
              createChartWithDelay("marketCondition", "chartDiv", 100);
            } else if (chartReferences.marketCondition.root) {
              chartReferences.marketCondition.root.resize();
              console.log("Resized Market Condition chart on tab switch");
            }
          } else if (targetTabId === "#future-outlook") {
            if (!chartReferences.futureOutlook) {
              createChartWithDelay("futureOutlook", "futureChartDiv", 100);
            } else if (chartReferences.futureOutlook.root) {
              chartReferences.futureOutlook.root.resize();
              console.log("Resized Future Outlook chart on tab switch");
            }
          } else if (targetTabId === "#market-demand") {
            if (!chartReferences.marketDemand) {
              createChartWithDelay("marketDemand", "demandChartDiv", 100);
            } else if (chartReferences.marketDemand.root) {
              chartReferences.marketDemand.root.resize();
              console.log("Resized Market Demand chart on tab switch");
            }
          }
        });
      });

      // Initialize Price analysis tabs and chart
      const priceAnalysisTabs = document.querySelectorAll(
        "#priceAnalysisTabs .nav-link"
      );
      priceAnalysisTabs.forEach(function (tab) {
        tab.addEventListener("shown.bs.tab", function (event) {
          // Reset any existing price charts when switching material tabs
          if (chartReferences.priceIndex && chartReferences.priceIndex.root) {
            chartReferences.priceIndex.root.dispose();
            chartReferences.priceIndex = null;
          }

          // Create a new chart for the active material tab
          setTimeout(function () {
            createPriceIndexChart();
          }, 100);

          console.log(
            "Material tab changed:",
            event.target.getAttribute("data-bs-target")
          );
        });
      });

      // Initialize the price index chart for the default tab
      setTimeout(function () {
        createPriceIndexChart();
      }, 1000);
    });

    // Market insights Chart Functions
    function createMarketConditionChart() {
      // Ensure AmCharts is available
      if (!isAmChartsReady()) {
        console.error(
          "AmCharts is not available when creating Market Condition chart"
        );
        return;
      }

      var container = document.getElementById("chartDiv");
      if (!container) {
        console.error("chartDiv container not found");
        return;
      }

      console.log(
        "Creating Market Condition chart, container dimensions:",
        container.offsetWidth + "x" + container.offsetHeight
      );

      // Clean up any existing chart and observer
      if (
        chartReferences.marketCondition &&
        chartReferences.marketCondition.root
      ) {
        console.log("Disposing existing Market Condition chart");
        chartReferences.marketCondition.root.dispose();
        chartReferences.marketCondition = null;
      }

      if (container.resizeObserver) {
        console.log("Disconnecting existing ResizeObserver for chartDiv");
        container.resizeObserver.disconnect();
        container.resizeObserver = null;
      }

      try {
        // Force container dimensions if needed
        if (container.offsetHeight < 100) {
          container.style.height = "300px";
          container.style.minHeight = "300px";
        }

        // Create root element
        var root = am5.Root.new("chartDiv");

        // Set themes
        root.setThemes([am5themes_Animated.new(root)]);

        // Create chart
        var chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "panX",
            wheelY: "zoomX",
            layout: root.verticalLayout,
          })
        );

        // Store references globally
        chartReferences.marketCondition = {
          root: root,
          chart: chart,
        };

        // Create axes
        var xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "year",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll([{ year: "2019" }, { year: "2025F" }]);

        var yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            min: 150,
            max: 350,
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        // Add series
        var series = chart.series.push(
          am5xy.ColumnSeries.new(root, {
            name: "Market Size ($ billion)",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "year",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY} billion",
            }),
          })
        );

        series.columns.template.setAll({
          cornerRadiusTL: 3,
          cornerRadiusTR: 3,
          fill: am5.color("#00B19C"),
        });

        series.data.setAll([
          { year: "2019", value: 262.61 },
          { year: "2025F", value: 339.95 },
        ]);

        // Add value labels on top of columns
        series.columns.template.children.push(
          am5.Label.new(root, {
            text: "{valueY}",
            centerX: am5.p50,
            centerY: am5.p0,
            dy: -10,
            populateText: true,
          })
        );

        // Add title
        chart.children.unshift(
          am5.Label.new(root, {
            text: "Global Corrugated Board Packaging Market",
            fontSize: 12,
            fontWeight: "500",
            textAlign: "center",
            x: am5.p50,
            centerX: am5.p50,
            paddingTop: 10,
            paddingBottom: 10,
          })
        );

        // Make series show on legend and appear
        series.appear(1000);
        chart.appear(1000, 100);

        // Enhanced ResizeObserver
        const resizeObserver = new ResizeObserver((entries) => {
          console.log("ResizeObserver triggered for chartDiv");
          if (
            root &&
            container.offsetWidth > 0 &&
            container.offsetHeight > 0
          ) {
            console.log(
              "Before resize dimensions:",
              root.width(),
              "x",
              root.height()
            );
            root.resize();
            console.log(
              "After resize dimensions:",
              root.width(),
              "x",
              root.height()
            );
          } else {
            console.log(
              "ResizeObserver skipped: chartDiv has zero dimensions or root is unavailable"
            );
          }
        });

        // Start observing the container
        resizeObserver.observe(container);

        // Store the observer in the container for cleanup
        container.resizeObserver = resizeObserver;

        // Force immediate resize to ensure proper rendering
        setTimeout(function () {
          if (root) {
            root.resize();
          }
        }, 100);

        console.log("Market Condition chart created successfully");
      } catch (error) {
        console.error("Error creating Market Condition chart:", error);
        chartReferences.marketCondition = null;
      }
    }

    function createFutureOutlookChart() {
      // Ensure AmCharts is available
      if (!isAmChartsReady()) {
        console.error(
          "AmCharts is not available when creating Future Outlook chart"
        );
        return;
      }

      var container = document.getElementById("futureChartDiv");
      if (!container) {
        console.error("futureChartDiv container not found");
        return;
      }

      console.log(
        "Creating Future Outlook chart, container dimensions:",
        container.offsetWidth + "x" + container.offsetHeight
      );

      // Clean up any existing chart and observer
      if (
        chartReferences.futureOutlook &&
        chartReferences.futureOutlook.root
      ) {
        console.log("Disposing existing Future Outlook chart");
        chartReferences.futureOutlook.root.dispose();
        chartReferences.futureOutlook = null;
      }

      if (container.resizeObserver) {
        console.log(
          "Disconnecting existing ResizeObserver for futureChartDiv"
        );
        container.resizeObserver.disconnect();
        container.resizeObserver = null;
      }

      try {
        // Force container dimensions if needed
        if (container.offsetHeight < 100) {
          container.style.height = "300px";
          container.style.minHeight = "300px";
        }

        // Create root element
        var root = am5.Root.new("futureChartDiv");

        // Set themes
        root.setThemes([am5themes_Animated.new(root)]);

        // Create chart
        var chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "panX",
            wheelY: "zoomX",
            layout: root.verticalLayout,
          })
        );

        // Store references globally
        chartReferences.futureOutlook = {
          root: root,
          chart: chart,
        };

        // Create axes
        var xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "region",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll([
          { region: "North America" },
          { region: "Europe" },
          { region: "Asia-Pacific" },
          { region: "Others" },
        ]);

        var yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        // Add series
        var series = chart.series.push(
          am5xy.ColumnSeries.new(root, {
            name: "CAGR 2020-2025 (%)",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "region",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}%",
            }),
          })
        );

        series.columns.template.setAll({
          cornerRadiusTL: 3,
          cornerRadiusTR: 3,
          fill: am5.color("#00B19C"),
        });

        series.data.setAll([
          { region: "North America", value: 3.2 },
          { region: "Europe", value: 3.8 },
          { region: "Asia-Pacific", value: 5.9 },
          { region: "Others", value: 3.5 },
        ]);

        // Add value labels on top of columns
        series.columns.template.children.push(
          am5.Label.new(root, {
            text: "{valueY}%",
            centerX: am5.p50,
            centerY: am5.p0,
            dy: -10,
            populateText: true,
          })
        );

        // Add title
        chart.children.unshift(
          am5.Label.new(root, {
            text: "Regional Growth Forecast (CAGR)",
            fontSize: 12,
            fontWeight: "500",
            textAlign: "center",
            x: am5.p50,
            centerX: am5.p50,
            paddingTop: 10,
            paddingBottom: 10,
          })
        );

        // Make series show on legend and appear
        series.appear(1000);
        chart.appear(1000, 100);

        // Enhanced ResizeObserver
        const resizeObserver = new ResizeObserver((entries) => {
          console.log("ResizeObserver triggered for futureChartDiv");
          if (
            root &&
            container.offsetWidth > 0 &&
            container.offsetHeight > 0
          ) {
            console.log(
              "Before resize dimensions:",
              root.width(),
              "x",
              root.height()
            );
            root.resize();
            console.log(
              "After resize dimensions:",
              root.width(),
              "x",
              root.height()
            );
          } else {
            console.log(
              "ResizeObserver skipped: futureChartDiv has zero dimensions or root is unavailable"
            );
          }
        });

        // Start observing the container
        resizeObserver.observe(container);

        // Store the observer in the container for cleanup
        container.resizeObserver = resizeObserver;

        // Force immediate resize to ensure proper rendering
        setTimeout(function () {
          if (root) {
            root.resize();
          }
        }, 100);

        console.log("Future Outlook chart created successfully");
      } catch (error) {
        console.error("Error creating Future Outlook chart:", error);
        chartReferences.futureOutlook = null;
      }
    }

    function createMarketDemandChart() {
      // Ensure AmCharts is available
      if (!isAmChartsReady()) {
        console.error(
          "AmCharts is not available when creating Market Demand chart"
        );
        return;
      }

      var container = document.getElementById("demandChartDiv");
      if (!container) {
        console.error("demandChartDiv container not found");
        return;
      }

      console.log(
        "Creating Market Demand chart, container dimensions:",
        container.offsetWidth + "x" + container.offsetHeight
      );

      // Clean up any existing chart and observer
      if (chartReferences.marketDemand && chartReferences.marketDemand.root) {
        console.log("Disposing existing Market Demand chart");
        chartReferences.marketDemand.root.dispose();
        chartReferences.marketDemand = null;
      }

      if (container.resizeObserver) {
        console.log(
          "Disconnecting existing ResizeObserver for demandChartDiv"
        );
        container.resizeObserver.disconnect();
        container.resizeObserver = null;
      }

      try {
        // Force container dimensions if needed
        if (container.offsetHeight < 100) {
          container.style.height = "300px";
          container.style.minHeight = "300px";
        }

        // Create root element
        var root = am5.Root.new("demandChartDiv");

        // Set themes
        root.setThemes([am5themes_Animated.new(root)]);

        // Create chart
        var chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: false,
            panY: false,
            wheelX: "panX",
            wheelY: "zoomX",
            layout: root.verticalLayout,
          })
        );

        // Store references globally
        chartReferences.marketDemand = {
          root: root,
          chart: chart,
        };

        // Create axes
        var xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "sector",
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.data.setAll([
          { sector: "Food & Beverage" },
          { sector: "E-commerce" },
          { sector: "Electronics" },
          { sector: "Healthcare" },
          { sector: "Others" },
        ]);

        var yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        // Add series
        var series = chart.series.push(
          am5xy.ColumnSeries.new(root, {
            name: "Market Share (%)",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "sector",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}%",
            }),
          })
        );

        series.columns.template.setAll({
          cornerRadiusTL: 3,
          cornerRadiusTR: 3,
          fill: am5.color("#00B19C"),
        });

        series.data.setAll([
          { sector: "Food & Beverage", value: 35 },
          { sector: "E-commerce", value: 22 },
          { sector: "Electronics", value: 15 },
          { sector: "Healthcare", value: 12 },
          { sector: "Others", value: 16 },
        ]);

        // Add value labels on top of columns
        series.columns.template.children.push(
          am5.Label.new(root, {
            text: "{valueY}%",
            centerX: am5.p50,
            centerY: am5.p0,
            dy: -10,
            populateText: true,
          })
        );

        // Add title
        chart.children.unshift(
          am5.Label.new(root, {
            text: "Market Share by End-Use Sector",
            fontSize: 12,
            fontWeight: "500",
            textAlign: "center",
            x: am5.p50,
            centerX: am5.p50,
            paddingTop: 10,
            paddingBottom: 10,
          })
        );

        // Make series show on legend and appear
        series.appear(1000);
        chart.appear(1000, 100);

        // Enhanced ResizeObserver
        const resizeObserver = new ResizeObserver((entries) => {
          console.log("ResizeObserver triggered for demandChartDiv");
          if (
            root &&
            container.offsetWidth > 0 &&
            container.offsetHeight > 0
          ) {
            console.log(
              "Before resize dimensions:",
              root.width(),
              "x",
              root.height()
            );
            root.resize();
            console.log(
              "After resize dimensions:",
              root.width(),
              "x",
              root.height()
            );
          } else {
            console.log(
              "ResizeObserver skipped: demandChartDiv has zero dimensions or root is unavailable"
            );
          }
        });

        // Start observing the container
        resizeObserver.observe(container);

        // Store the observer in the container for cleanup
        container.resizeObserver = resizeObserver;

        // Force immediate resize to ensure proper rendering
        setTimeout(function () {
          if (root) {
            root.resize();
          }
        }, 100);

        console.log("Market Demand chart created successfully");
      } catch (error) {
        console.error("Error creating Market Demand chart:", error);
        chartReferences.marketDemand = null;
      }
    }

    // Function to create Price Index Chart for the Price analysis widget
    function createPriceIndexChart() {
      // Ensure AmCharts is available
      if (!isAmChartsReady()) {
        console.error(
          "AmCharts is not available when creating Price Index chart"
        );
        return;
      }

      var container = document.getElementById("priceIndexChart");
      if (!container) {
        console.error("priceIndexChart container not found");
        return;
      }

      console.log(
        "Creating Price Index chart, container dimensions:",
        container.offsetWidth + "x" + container.offsetHeight
      );

      // Clean up any existing chart and observer
      if (chartReferences.priceIndex && chartReferences.priceIndex.root) {
        console.log("Disposing existing Price Index chart");
        chartReferences.priceIndex.root.dispose();
        chartReferences.priceIndex = null;
      }

      if (container.resizeObserver) {
        console.log(
          "Disconnecting existing ResizeObserver for priceIndexChart"
        );
        container.resizeObserver.disconnect();
        container.resizeObserver = null;
      }

      try {
        // Create root element
        var root = am5.Root.new("priceIndexChart");

        // Set themes
        root.setThemes([am5themes_Animated.new(root)]);

        // Create chart
        var chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: true,
            panY: true,
            wheelX: "panX",
            wheelY: "zoomX",
            maxTooltipDistance: 0,
            paddingLeft: 0,
          })
        );

        // Store references globally if not already stored
        if (!chartReferences.priceIndex) {
          chartReferences.priceIndex = {
            root: root,
            chart: chart,
          };
        } else {
          chartReferences.priceIndex.root = root;
          chartReferences.priceIndex.chart = chart;
        }

        // Create Y-axis
        var yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            min: 95,
            max: 107,
            strictMinMax: true,
            renderer: am5xy.AxisRendererY.new(root, {
              opposite: false,
            }),
          })
        );

        yAxis.get("renderer").labels.template.setAll({
          fontSize: 12,
        });

        // Add Y-axis label
        yAxis.children.push(
          am5.Label.new(root, {
            text: "Index: Jan 2020=100",
            rotation: -90,
            y: am5.p50,
            centerX: am5.p50,
          })
        );

        // Create X-axis
        var xAxis = chart.xAxes.push(
          am5xy.CategoryAxis.new(root, {
            categoryField: "month",
            renderer: am5xy.AxisRendererX.new(root, {
              minGridDistance: 30,
            }),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        xAxis.get("renderer").labels.template.setAll({
          fontSize: 12,
          rotation: -45,
          centerY: am5.p50,
          centerX: am5.p100,
          paddingRight: 15,
        });

        // Define data
        var data = [
          { month: "Jan'20", value: 100 },
          { month: "Feb'20", value: 100 },
          { month: "Mar'20", value: 104.8 },
          { month: "Apr'20", value: 104.8 },
          { month: "May'20", value: 104.8 },
          { month: "Jun'20", value: 100 },
          { month: "Jul'20", value: 100 },
          { month: "Aug'20", value: 100 },
          { month: "Sep'20", value: 96.8 },
          { month: "Oct'20", value: 96.8 },
          { month: "Nov'20", value: 101.5 },
        ];

        // Set data to axis
        xAxis.data.setAll(data);

        // Create series
        var series = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Price Index",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "month",
            fill: am5.color("#00B19C"),
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        // Set line appearance
        series.strokes.template.setAll({
          strokeWidth: 3,
          stroke: am5.color("#00B19C"),
        });

        // Add circle bullets
        series.bullets.push(function () {
          return am5.Bullet.new(root, {
            sprite: am5.Circle.new(root, {
              radius: 5,
              fill: am5.color("#00B19C"),
              stroke: am5.color("#fff"),
              strokeWidth: 2,
            }),
          });
        });

        // Set data to series
        series.data.setAll(data);

        // Add cursor
        chart.set(
          "cursor",
          am5xy.XYCursor.new(root, {
            behavior: "zoomXY",
            xAxis: xAxis,
          })
        );

        xAxis.set(
          "cursor",
          am5xy.XYCursor.new(root, {
            behavior: "zoomX",
          })
        );

        yAxis.set(
          "cursor",
          am5xy.XYCursor.new(root, {
            behavior: "zoomY",
          })
        );

        // Make series appear animation
        series.appear(1000, 100);

        // Make chart appear animation
        chart.appear(1000, 100);

        // Enhanced ResizeObserver
        const resizeObserver = new ResizeObserver((entries) => {
          console.log("ResizeObserver triggered for priceIndexChart");
          if (
            root &&
            container.offsetWidth > 0 &&
            container.offsetHeight > 0
          ) {
            console.log(
              "Before resize dimensions:",
              root.width(),
              "x",
              root.height()
            );
            root.resize();
            console.log(
              "After resize dimensions:",
              root.width(),
              "x",
              root.height()
            );
          } else {
            console.log(
              "ResizeObserver skipped: priceIndexChart has zero dimensions or root is unavailable"
            );
          }
        });

        // Start observing the container
        resizeObserver.observe(container);

        // Store the observer in the container for cleanup
        container.resizeObserver = resizeObserver;

        // Force immediate resize to ensure proper rendering
        setTimeout(function () {
          if (root) {
            root.resize();
          }
        }, 100);

        console.log("Price Index chart created successfully");
      } catch (error) {
        console.error("Error creating Price Index chart:", error);
        chartReferences.priceIndex = null;
      }
    }

    // Function to add a Key Insights widget
    function addKeyInsightsWidget() {
      const widget = window.grid.addWidget({
        x: 0,
        y: 0,
        w: 6,
        h: 6,
        content: `<div class="card h-100 key-insights-widget">
                      <div class="card-body">
                        <header class="d-flex justify-content-between widget-header">
                          <h3 class="fs-5 my-2">Key Insights</h3>
                          <div class="widget-icons">
                            <a href="javascript:void(0);" class="focus-icon-link" data-bs-toggle="tooltip" title="Focus mode">
                              <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode.svg" alt="Focus Mode" class="focus-icon default">
                              <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode-hover.svg" alt="Focus Mode" class="focus-icon hover">
                            </a>
                            <a href="javascript:void(0);" data-bs-toggle="tooltip" title="Export to PPT">
                              <i class="las la-file-export"></i>
                            </a>
                           
                          </div>
                        </header>
                        
                        <div class="insights-content ciKeyInsightScrollInsight">
                          <div class="mb-3">
                            <strong>Buying recommendations: Cover H2 2025–Q1 2026 requirements in Q2 2025</strong>
                            <ul>
                              <li>Buyers are suggested to cover H2 2025–Q1 2026 exposure in Q2 2025 as prices are likely to increase thereafter, supported by a recovery in downstream demand, a likely improvement in macroeconomic sentiments and potential supply concerns in late 2025</li>
                            </ul>
                          </div>

                          <div class="swot-table">
                            <table class="table table-bordered craftTable table-sm w-100 mb-3">
                              <thead>
                                <tr>
                                  <th class="fw-bold">
                                    <div class="d-flex align-items-center justify-content-around">
                                      <div>
                                      <img src="https://amplifipro.thesmartcube.com/public/images/opportunities_icon.svg" width="25" alt="Opportunities">
                                      <span class="ms-2">Opportunity</span>
                                      </div>
                                    </div>
                                  </th>
                                  <th class="fw-bold">
                                    <div class="d-flex align-items-center justify-content-around">
                                     <div>
                                      <img src="https://amplifipro.thesmartcube.com/public/images/threats_icon.svg" width="25" alt="Risks">
                                      <span class="ms-2">Risk</span>
                                    </div>
                                     </div>
                                  </th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr>
                                  <td class="text-start">
                                    <ul>
                                      <li>Fall in Feedstock Prices: An anticipated ease in supply concerns in alumina market</li>
                                      <li>Hawkish stance by the US Fed: Expectations of fewer interest rate cuts</li>
                                      <li>Demand Destruction: An anticipated demand reduction following a sharp price uptrend</li>
                                    </ul>
                                  </td>
                                  <td class="text-start">
                                    <ul>
                                      <li>Imposition of import tariffs by the US: Concerns over imposition of 25% tariffs</li>
                                      <li>Announcement of further stimulus: China may announce further monetary stimulus</li>
                                      <li>Potential decline in China's aluminium supply: China's commitment to restrict supply</li>
                                    </ul>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                        
                        <footer class="widget-footer">
                          <div class="mb-0">
                            <span class="size10 source-keyInsight">
                                <i class="las la-database size14"></i> Source: <span>The Smart Cube Research and Analysis</span>
                            <br>
                            </span>
                            <span class="size10 last-update-keyInsight">
                            <i class="las size14 la-calendar-minus"></i> Last update: <span>Apr-2025</span>
                            </span>
                            <span class="size10 next-update-keyInsight">
                            <i class="las size14 la-calendar-plus"></i> Next update: <span>May-2025</span>
                            </span>
                          </div>
                        </footer>
                      </div>
                    </div>`,
      });

      // Hide the modal
      const modal = bootstrap.Modal.getInstance(
        document.getElementById("widgetGalleryModal")
      );
      if (modal) {
        modal.hide();
      }

      // Initialize tooltips for the new widget
      const tooltips = widget.querySelectorAll('[data-bs-toggle="tooltip"]');
      tooltips.forEach((el) => new bootstrap.Tooltip(el));
    }

    // Function to add dummy widgets
    function addDummyWidget(title, iconClass) {
      window.grid.addWidget({
        x: 0,
        y: 0,
        w: 6,
        h: 4,
        content: `<div class="card h-100">
                      <div class="card-body">
                        <header class="d-flex justify-content-between widget-header">
                          <h3 class="fs-5 my-2">${title}</h3>
                          <div class="widget-icons">
                            <a href="javascript:void(0);" class="focus-icon-link" data-bs-toggle="tooltip" title="Focus mode">
                              <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode.svg" alt="Focus Mode" class="focus-icon default">
                              <img src="https://amplifipro.thesmartcube.com/public/images/Focusmode-hover.svg" alt="Focus Mode" class="focus-icon hover">
                            </a>
                            <a href="javascript:void(0);" data-bs-toggle="tooltip" title="Export to PPT">
                              <i class="las la-file-export"></i>
                            </a>
                         
                          </div>
                        </header>
                        <div class="d-flex align-items-center justify-content-center" style="height: 200px;">
                          <div class="text-center">
                            <i class="${iconClass}" style="font-size: 60px; color: #ccc;"></i>
                            <p class="mt-3">Placeholder for ${title} visualization</p>
                          </div>
                        </div>
                      </div>
                    </div>`,
      });

      // Hide the modal
      const modal = bootstrap.Modal.getInstance(
        document.getElementById("widgetGalleryModal")
      );
      if (modal) {
        modal.hide();
      }
    }

    // Enhanced Preview Functionality is now handled by preview-functionality.js
    // Removed inline preview code to prevent conflicts
  </script>

  <script>
    // GridStack init
    document.addEventListener("DOMContentLoaded", function () {
      const parentOpts = {
        float: true,
        resizable: { handles: "all" },
        draggable: {
          handle: ".card-header1",
          scroll: false,
        },
      };
      const parentGrid = GridStack.init(parentOpts, "#parentGrid");
      const childOpts = {
        float: true,
        resizable: { handles: "all" },
        draggable: {
          handle: ".card-header1",
          scroll: false,
        },
        subGridOpts: {},
      };
      const childGrid = GridStack.init(childOpts, "#child-grid");

      // ensure no widgets accidentally got locked static
      childGrid
        .getGridItems()
        .forEach((el) => childGrid.update(el, { static: false }));

      // Initialize AmChart 5
      am5.ready(function () {
        let root = am5.Root.new("lineChart");

        root.setThemes([am5themes_Animated.new(root)]);

        let chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: true,
            panY: true,
            wheelX: "panX",
            wheelY: "zoomX",
            layout: root.verticalLayout,
          })
        );

        let xAxis = chart.xAxes.push(
          am5xy.DateAxis.new(root, {
            maxDeviation: 0.2,
            baseInterval: { timeUnit: "day", count: 1 },
            renderer: am5xy.AxisRendererX.new(root, {}),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        let yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            renderer: am5xy.AxisRendererY.new(root, {}),
          })
        );

        let series = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Sales",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            valueXField: "date",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
          })
        );

        series.fills.template.setAll({ visible: true, fillOpacity: 0.3 });
        series.strokes.template.setAll({ strokeWidth: 2 });

        // Sample data
        let data = [];
        let value = 100;

        for (let i = 0; i < 30; i++) {
          value += Math.round(
            (Math.random() < 0.5 ? 1 : -1) * Math.random() * 10
          );
          data.push({
            date: new Date(2023, 0, i).getTime(),
            value: value,
          });
        }

        series.data.setAll(data);
        chart.set("cursor", am5xy.XYCursor.new(root, {}));

        // Animate on load
        series.appear(1000);
        chart.appear(1000, 100);
      });
    });

    // Remove function
    function removeSectionContainer(button) {
      const gridItem = button.closest(".grid-stack-item");
      if (gridItem) {
        const grid = GridStack.getClosestGrid(gridItem);
        grid.removeWidget(gridItem);
      }
    }
  </script>

  <script>
    $(function () {
      $("body").addClass('resComIns');
      $('.scroll1').on('click', function () {

        var $arrow = $(this).find('.arrow-bounce');
        var $element = $(this).closest('.card-body').find('.doMagicOnDownCursor');
        var height = $(this).attr('data-value');
        var customHeight = $("#keyInsightNoData").val();

        if ($arrow.text() === '↓') {
          $arrow.text('↑');
          // Expand the element to its full height
          let addHeight = 50;
          if (customHeight == 1) {
            var autoHeight = $element[0].scrollHeight + addHeight;
          } else {
            var autoHeight = $element[0].scrollHeight;
          }

          $element.animate({
            height: autoHeight + 'px'
          }, 500);
          $(this).closest('.card-body.cloudLogic').removeClass('cloudy');

        } else {
          $arrow.text('↓');
          // Collapse the element to a height of 50px
          $element.animate({
            height: height + 'px'
          }, 500);
          $(this).closest('.card-body.cloudLogic').addClass('cloudy');
          // $(".ciKeyInsightScrollInsight").css('height','140px');

        }
      });





      $('.scroll12').on('click', function () {

        var $arrow = $(this).find('.arrow-bounce');
        var $element = $(this).closest('.card-body').find('.doMagicOnDownCursor');
        var height = $(this).attr('data-value');
        var customHeight = $("#keyInsightNoData").val();

        if ($arrow.text() === '↓') {
          $arrow.text('↑');
          // Expand the element to its full height
          let addHeight = 50;
          if (customHeight == 1) {
            var autoHeight = $element[0].scrollHeight + addHeight;
          } else {
            var autoHeight = $element[0].scrollHeight;
          }

          $element.animate({
            height: autoHeight + 'px'
          }, 500);
          $(this).closest('.card-body.cloudLogic').removeClass('cloudy');

        } else {
          $arrow.text('↓');
          // Collapse the element to a height of 50px
          $element.animate({
            height: height + 'px'
          }, 500);
          $(this).closest('.card-body.cloudLogic').addClass('cloudy');
          // $(".ciKeyInsightScrollInsight").css('height','140px');

        }
      });







      $('body').on('click', '.scroll2', function () {

        var $arrow = $(this).find('.arrow-bounce');
        var $element = $(this).closest('.card-body').find('.doMagicOnDownCursor');
        var height = $(this).attr('data-value');


        if ($arrow.text() === '↓') {
          $arrow.text('↑');
          // Expand the element to its full height
          let addHeight = 50;

          var autoHeight = $element[0].scrollHeight;


          $element.animate({
            height: autoHeight + 'px'
          }, 500);
          $(this).closest('.card-body.cloudLogic').removeClass('cloudy');

        } else {
          $arrow.text('↓');
          // Collapse the element to a height of 50px
          $element.animate({
            height: height + 'px'
          }, 500);
          $(this).closest('.card-body.cloudLogic').addClass('cloudy');
          //  $(".ciKeyInsightScrollInsight").css('height','140px');

        }
      });



        $('body').on('click', '.scroll2aa', function () {

        var $arrow = $(this).find('.arrow-bounce');
        var $element = $(this).closest('.card-body')
        var height = $(this).attr('data-value');


        if ($arrow.text() === '↓') {
          $arrow.text('↑');
          // Expand the element to its full height
          let addHeight = 635;

          var autoHeight = $element[0].scrollHeight;


          $element.animate({
            height: autoHeight + 'px'
          }, 500);
          $(this).closest('.card-body.cloudLogic').removeClass('cloudy');

        } else {
          $arrow.text('↓');
          // Collapse the element to a height of 50px
          $element.animate({
            height: height + 'px'
          }, 500);
          $(this).closest('.card-body.cloudLogic').addClass('cloudy');
          //  $(".ciKeyInsightScrollInsight").css('height','140px');

        }
      });

    });


function chart1() {
   am5.ready(function () {
        // Create root element
        const root = am5.Root.new("chartdiv1");

        // Set themes
        root.setThemes([am5themes_Animated.new(root)]);

        // Create chart
        const chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: true,
            panY: true,
            wheelX: "panX",
            wheelY: "zoomX",
            pinchZoomX: true,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 50,
            paddingBottom: 60,
          })
        );

        // Add chart title
        const title = chart.children.unshift(
          am5.Label.new(root, {
            text: "Aluminium cash-settlement (LME)",
            fontSize: 12,
            fontWeight: "600",
            fontFamily: "Montserrat",
            x: am5.p50,
            centerX: am5.p50,
            y: 0,
            centerY: am5.p0,
            marginTop: 10,
            marginBottom: 10,
          })
        );

        // Create axes
        const xAxis = chart.xAxes.push(
          am5xy.DateAxis.new(root, {
            maxZoomCount: 50,
            baseInterval: {
              timeUnit: "month",
              count: 1,
            },
            renderer: am5xy.AxisRendererX.new(root, {
              minGridDistance: 80,
              cellStartLocation: 0.1,
              cellEndLocation: 0.9,
            }),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        // Prepare data for actuals (left axis - USD per ton)
        const actualsData = [
          { date: new Date("2023-04-01").getTime(), value: 2350, volume: 1200 },
          { date: new Date("2023-05-01").getTime(), value: 2270, volume: 1350 },
          { date: new Date("2023-06-01").getTime(), value: 2190, volume: 1100 },
          { date: new Date("2023-07-01").getTime(), value: 2160, volume: 1450 },
          { date: new Date("2023-08-01").getTime(), value: 2150, volume: 1300 },
          { date: new Date("2023-09-01").getTime(), value: 2180, volume: 1250 },
          {
            date: new Date("2023-10-01").getTime(),
            value: 2192.45,
            volume: 1400,
          },
          { date: new Date("2023-11-01").getTime(), value: 2210, volume: 1550 },
          { date: new Date("2023-12-01").getTime(), value: 2160, volume: 1320 },
          { date: new Date("2024-01-01").getTime(), value: 2200, volume: 1480 },
          { date: new Date("2024-02-01").getTime(), value: 2190, volume: 1380 },
          { date: new Date("2024-03-01").getTime(), value: 2220, volume: 1420 },
          { date: new Date("2024-04-01").getTime(), value: 2500, volume: 1600 },
          { date: new Date("2024-05-01").getTime(), value: 2570, volume: 1750 },
          { date: new Date("2024-06-01").getTime(), value: 2500, volume: 1650 },
          { date: new Date("2024-07-01").getTime(), value: 2370, volume: 1520 },
          { date: new Date("2024-08-01").getTime(), value: 2340, volume: 1480 },
          { date: new Date("2024-09-01").getTime(), value: 2460, volume: 1580 },
          { date: new Date("2024-10-01").getTime(), value: 2600, volume: 1720 },
          { date: new Date("2024-11-01").getTime(), value: 2580, volume: 1680 },
          { date: new Date("2024-12-01").getTime(), value: 2540, volume: 1620 },
          { date: new Date("2025-01-01").getTime(), value: 2580, volume: 1700 },
          { date: new Date("2025-02-01").getTime(), value: 2670, volume: 1800 },
          { date: new Date("2025-03-01").getTime(), value: 2690, volume: 1850 },
        ];

        const yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            min: 2100,
            max: 2700,
            renderer: am5xy.AxisRendererY.new(root, {
              strokeOpacity: 1,
              stroke: am5.color("#000000"),
            }),
          })
        );

        // Set left axis title
        // yAxis.set(
        //   "title",
        //   am5.Label.new(root, {
        //     text: "Price (USD/ton)",
        //     fontFamily: "Montserrat",
        //     fontSize: 12,
        //     fontWeight: "400",
        //     rotation: -90,
        //     y: am5.p50,
        //     centerX: am5.p50,
        //   })
        // );

        // Create second Y-axis (right side) for volume data
        const yAxisRight = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            min: 1000,
            max: 2000,
            renderer: am5xy.AxisRendererY.new(root, {
              opposite: true,
              strokeOpacity: 1,
              stroke: am5.color("#000000"),
            }),
          })
        );

        // Set right axis title
        // yAxisRight.set(
        //   "title",
        //   am5.Label.new(root, {
        //     text: "Volume (000 tons)",
        //     fontFamily: "Montserrat",
        //     fontSize: 12,
        //     fontWeight: "400",
        //     rotation: 90,
        //     y: am5.p50,
        //     centerX: am5.p50,
        //   })
        // );

        // Add left axis title as separate element
        const leftAxisTitle = chart.plotContainer.children.push(
          am5.Label.new(root, {
            text: "Price (USD/ton)",
            fontFamily: "Montserrat",
            fontSize: 12,
            fontWeight: "400",
            rotation: -90,
            x: am5.p0,
            y: am5.p50,
            centerX: am5.p50,
            centerY: am5.p50,
            dx: -60,
          })
        );

        // Add right axis title as separate element
        const rightAxisTitle = chart.plotContainer.children.push(
          am5.Label.new(root, {
            text: "Volume (000 tons)",
            fontFamily: "Montserrat",
            fontSize: 12,
            fontWeight: "400",
            rotation: 90,
            x: am5.p100,
            y: am5.p50,
            centerX: am5.p50,
            centerY: am5.p50,
            dx: 60,
          })
        );

        // Set font styles
        const fontSettings = {
          fontFamily: "Montserrat",
          fontSize: 12,
          fontWeight: "400",
        };

        xAxis.get("renderer").labels.template.setAll(fontSettings);
        yAxis.get("renderer").labels.template.setAll(fontSettings);
        yAxisRight.get("renderer").labels.template.setAll(fontSettings);

        // Configure X-axis date format (MMM-YYYY like Jul-2024)
        xAxis.get("renderer").labels.template.setAll({
          ...fontSettings,
          rotation: -45,
          centerY: am5.p50,
          centerX: am5.p100,
        });

        // Set date format for X-axis labels
        xAxis.set("dateFormats", {
          month: "MMM-yyyy",
        });

        xAxis.set("periodChangeDateFormats", {
          month: "MMM-yyyy",
        });

        // Configure axis lines (correct approach for AmCharts v5)
        xAxis.get("renderer").set("strokeOpacity", 1);
        xAxis.get("renderer").set("stroke", am5.color("#000000"));

        // Set axis titles
        // yAxis.set(
        //   "title",
        //   am5.Label.new(root, {
        //     text: "Price (USD/ton)",
        //     ...fontSettings,
        //     rotation: -90,
        //     y: am5.p50,
        //     centerX: am5.p50,
        //     marginRight: 20,
        //   })
        // );

        // yAxisRight.set(
        //   "title",
        //   am5.Label.new(root, {
        //     text: "Volume (000 tons)",
        //     ...fontSettings,
        //     rotation: 90,
        //     y: am5.p50,
        //     centerX: am5.p50,
        //     marginLeft: 20,
        //   })
        // );

        // Create series for price (left axis - solid line)
        const priceSeries = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Price (USD/ton)",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            valueXField: "date",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
            stroke: am5.color("#00b19c"),
            fill: am5.color("#00b19c"),
          })
        );

        // Create series for volume (right axis - dashed line)
        const volumeSeries = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Volume (000 tons)",
            xAxis: xAxis,
            yAxis: yAxisRight,
            valueYField: "volume",
            valueXField: "date",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
            stroke: am5.color("#3bcd3f"),
            fill: am5.color("#3bcd3f"),
          })
        );

        // Set line styles
        priceSeries.strokes.template.setAll({
          strokeWidth: 2,
          strokeDasharray: [],
        });

        volumeSeries.strokes.template.setAll({
          strokeWidth: 2,
          strokeDasharray: [5, 5],
        });

        // Add bullets (markers) for price series
        priceSeries.bullets.push(function () {
          const bulletCircle = am5.Circle.new(root, {
            radius: 4,
            fill: am5.color("#00b19c"),
            stroke: am5.color("#ffffff"),
            strokeWidth: 2,
          });
          return am5.Bullet.new(root, {
            sprite: bulletCircle,
          });
        });

        // Add bullets (markers) for volume series
        volumeSeries.bullets.push(function () {
          const bulletCircle = am5.Circle.new(root, {
            radius: 4,
            fill: am5.color("#3bcd3f"),
            stroke: am5.color("#ffffff"),
            strokeWidth: 2,
          });
          return am5.Bullet.new(root, {
            sprite: bulletCircle,
          });
        });

        // Add legend
        const legend = chart.children.push(
          am5.Legend.new(root, {
            centerX: am5.p50,
            x: am5.p50,
            centerY: am5.p0,
            y: am5.p100,
            marginTop: 15,
            marginBottom: 5,
          })
        );

        legend.labels.template.setAll(fontSettings);
        legend.data.setAll(chart.series.values);

        // Set data
        priceSeries.data.setAll(actualsData);
        volumeSeries.data.setAll(actualsData);

        // Add cursor
        chart.set(
          "cursor",
          am5xy.XYCursor.new(root, {
            behavior: "zoomX",
          })
        );

        // Configure grid
        xAxis.get("renderer").grid.template.setAll({
          stroke: am5.color("#e0e0e0"),
          strokeDasharray: [1, 3],
        });

        yAxis.get("renderer").grid.template.setAll({
          stroke: am5.color("#e0e0e0"),
          strokeDasharray: [1, 3],
        });

        yAxisRight.get("renderer").grid.template.setAll({
          stroke: am5.color("#e0e0e0"),
          strokeDasharray: [1, 3],
        });

        // Make stuff animate on load
        priceSeries.appear(1000);
        volumeSeries.appear(1000);
        chart.appear(1000, 100);
      });
}




function chart2() {
  am5.ready(function () {
        // Create root element
        const root = am5.Root.new("chartDiv2");

        // Set themes
        root.setThemes([am5themes_Animated.new(root)]);

        // Create chart
        const chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: true,
            panY: true,
            wheelX: "panX",
            wheelY: "zoomX",
            pinchZoomX: true,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 50,
            paddingBottom: 60,
          })
        );

        // Add chart title
        const title = chart.children.unshift(
          am5.Label.new(root, {
            text: "Aluminium - supply and demand - Global",
            fontSize: 12,
            fontWeight: "600",
            fontFamily: "Montserrat",
            x: am5.p50,
            centerX: am5.p50,
            y: 0,
            centerY: am5.p0,
            marginTop: 10,
            marginBottom: 10,
          })
        );

        // Create axes - using DateAxis like in dual-axis-chart
        const xAxis = chart.xAxes.push(
          am5xy.DateAxis.new(root, {
            maxZoomCount: 50,
            baseInterval: {
              timeUnit: "year",
              count: 1,
            },
            renderer: am5xy.AxisRendererX.new(root, {
              minGridDistance: 80,
              cellStartLocation: 0.1,
              cellEndLocation: 0.9,
            }),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        // Set font styles
        const fontSettings = {
          fontFamily: "Montserrat",
          fontSize: 12,
          fontWeight: "400",
        };

        // Configure X-axis date format and styling
        xAxis.get("renderer").labels.template.setAll({
          ...fontSettings,
          rotation: -45,
          centerY: am5.p50,
          centerX: am5.p100,
        });

        // Set date format for X-axis labels
        xAxis.set("dateFormats", {
          year: "yyyy",
        });

        xAxis.set("periodChangeDateFormats", {
          year: "yyyy",
        });

        // Configure axis lines
        xAxis.get("renderer").set("strokeOpacity", 1);
        xAxis.get("renderer").set("stroke", am5.color("#000000"));

        // Left Y-axis for Demand
        const yAxisLeft = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            min: 0,
            max: 80000,
            renderer: am5xy.AxisRendererY.new(root, {
              opposite: false,
              strokeOpacity: 1,
              stroke: am5.color("#000000"),
            }),
          })
        );

        yAxisLeft.get("renderer").labels.template.setAll(fontSettings);

        // Right Y-axis for Supply
        const yAxisRight = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            min: 0,
            max: 80000,
            renderer: am5xy.AxisRendererY.new(root, {
              opposite: true,
              strokeOpacity: 1,
              stroke: am5.color("#000000"),
            }),
          })
        );

        yAxisRight.get("renderer").labels.template.setAll(fontSettings);

        // Add Y-axis titles
        const leftTitle = chart.plotContainer.children.push(
          am5.Label.new(root, {
            text: "Thousand tonne",
            fontSize: 12,
            fontWeight: "400",
            fontFamily: "Montserrat",
            rotation: -90,
            x: am5.p0,
            centerX: am5.p50,
            y: am5.p50,
            centerY: am5.p50,
            dx: -60,
          })
        );

        const rightTitle = chart.plotContainer.children.push(
          am5.Label.new(root, {
            text: "Thousand tonne",
            fontSize: 12,
            fontWeight: "400",
            fontFamily: "Montserrat",
            rotation: 90,
            x: am5.p100,
            centerX: am5.p50,
            y: am5.p50,
            centerY: am5.p50,
            dx: 60,
          })
        );

        // Configure grid
        xAxis.get("renderer").grid.template.setAll({
          stroke: am5.color("#e0e0e0"),
          strokeDasharray: [1, 3],
        });

        yAxisLeft.get("renderer").grid.template.setAll({
          stroke: am5.color("#e0e0e0"),
          strokeDasharray: [1, 3],
        });

        yAxisRight.get("renderer").grid.template.setAll({
          stroke: am5.color("#e0e0e0"),
          strokeDasharray: [1, 3],
        });

        // Create Demand series (teal color, solid line)
        const demandSeries = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Demand",
            xAxis: xAxis,
            yAxis: yAxisLeft,
            valueYField: "demand",
            valueXField: "date",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{name}: {valueY}",
            }),
            stroke: am5.color("#5fb3a1"),
            fill: am5.color("#5fb3a1"),
          })
        );

        // Set line styles - solid line for demand
        demandSeries.strokes.template.setAll({
          strokeWidth: 2,
          strokeDasharray: [],
        });

        // Add bullets for demand
        demandSeries.bullets.push(function () {
          return am5.Bullet.new(root, {
            sprite: am5.Circle.new(root, {
              radius: 4,
              fill: am5.color("#5fb3a1"),
              stroke: am5.color("#ffffff"),
              strokeWidth: 2,
            }),
          });
        });

        // Create Supply series (green color, dashed line)
        const supplySeries = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Supply",
            xAxis: xAxis,
            yAxis: yAxisRight,
            valueYField: "supply",
            valueXField: "date",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{name}: {valueY}",
            }),
            stroke: am5.color("#7bc142"),
            fill: am5.color("#7bc142"),
          })
        );

        // Set line styles - dashed line for supply
        supplySeries.strokes.template.setAll({
          strokeWidth: 2,
          strokeDasharray: [5, 5],
        });

        // Add bullets for supply
        supplySeries.bullets.push(function () {
          return am5.Bullet.new(root, {
            sprite: am5.Circle.new(root, {
              radius: 4,
              fill: am5.color("#7bc142"),
              stroke: am5.color("#ffffff"),
              strokeWidth: 2,
            }),
          });
        });

        // Data based on the image - converted to dates
        const data = [
          {
            date: new Date("2017-01-01").getTime(),
            demand: 59000,
            supply: 63000,
          },
          {
            date: new Date("2018-01-01").getTime(),
            demand: 66000,
            supply: 64000,
          },
          {
            date: new Date("2019-01-01").getTime(),
            demand: 63000,
            supply: 64000,
          },
          {
            date: new Date("2020-01-01").getTime(),
            demand: 65000,
            supply: 66000,
          },
          {
            date: new Date("2021-01-01").getTime(),
            demand: 68050,
            supply: 69000,
          },
          {
            date: new Date("2022-01-01").getTime(),
            demand: 68000,
            supply: 70000,
          },
          {
            date: new Date("2023-01-01").getTime(),
            demand: 70000,
            supply: 72000,
          },
          {
            date: new Date("2024-01-01").getTime(),
            demand: 73000,
            supply: 74000,
          },
          {
            date: new Date("2025-01-01").getTime(),
            demand: 75000,
            supply: 76000,
          },
        ];

        // Set data
        demandSeries.data.setAll(data);
        supplySeries.data.setAll(data);

        // Add legend
        const legend = chart.children.push(
          am5.Legend.new(root, {
            centerX: am5.p50,
            x: am5.p50,
            centerY: am5.p0,
            y: am5.p100,
            marginTop: 15,
            marginBottom: 5,
          })
        );

        legend.labels.template.setAll(fontSettings);
        legend.data.setAll(chart.series.values);

        // Add cursor with zoomX behavior
        chart.set(
          "cursor",
          am5xy.XYCursor.new(root, {
            behavior: "zoomX",
          })
        );

        // Make stuff animate on load
        demandSeries.appear(1000);
        supplySeries.appear(1000);
        chart.appear(1000, 100);
      });
}

function chart3(){
   am5.ready(function () {
        // Create root element
        const root = am5.Root.new("chartDiv3");

        // Set themes
        root.setThemes([am5themes_Animated.new(root)]);

        // Create chart
        const chart = root.container.children.push(
          am5percent.PieChart.new(root, {
            layout: root.verticalLayout,
            paddingTop: 0,
            paddingBottom: 0,
            paddingLeft: 0,
            paddingRight: 0,
          })
        );

        // Add chart title
        const title = chart.children.unshift(
          am5.Label.new(root, {
            text: "Aluminium - Global-2023",
            fontSize: 12,
            fontWeight: "600",
            fontFamily: "Montserrat",
            x: am5.p50,
            centerX: am5.p50,
            y: 0,
            centerY: am5.p0,
            marginTop: 10,
            marginBottom: 10,
          })
        );

        // Create series
        const series = chart.series.push(
          am5percent.PieSeries.new(root, {
            valueField: "value",
            categoryField: "category",
            alignLabels: false,
            radius: am5.percent(90),
            innerRadius: am5.percent(0),
          })
        );

        // Add hidden state for animation
        series.states.create("hidden", {
          startAngle: am5.p0,
          endAngle: am5.p0,
        });

        // Set colors to match the image
        series.set(
          "colors",
          am5.ColorSet.new(root, {
            colors: [
              am5.color("#5fb3a1"), // Raw material costs - teal
              am5.color("#7bc142"), // Electricity - green
              am5.color("#2d5a4a"), // Other - dark green
              am5.color("#a8c8d1"), // Labour cost - light blue
            ],
          })
        );

        // Configure slices
        series.slices.template.setAll({
          stroke: am5.color("#ffffff"),
          strokeWidth: 3,
          strokeOpacity: 1,
          tooltipText: "{category}: {valuePercentTotal.formatNumber('#.0')}%",
        });

        // Configure labels (hide them as we'll use legend)
        series.labels.template.setAll({
          text: "",
          visible: false,
        });

        // Configure ticks (hide them)
        series.ticks.template.setAll({
          visible: false,
        });

        // Data matching the image
        const data = [
          { category: "Raw material costs", value: 36.0 },
          { category: "Electricity", value: 30.0 },
          { category: "Other", value: 25.0 },
          { category: "Labour cost", value: 9.0 },
        ];

        // Set data
        series.data.setAll(data);

        // Create legend
        const legend = chart.children.push(
          am5.Legend.new(root, {
            centerX: am5.p50,
            x: am5.p50,
            marginTop: 10,
            marginBottom: 10,
            layout: root.gridLayout,
            maxColumns: 2,
          })
        );

        // Configure legend labels to show percentages
        legend.labels.template.setAll({
          fontFamily: "Montserrat",
          fontSize: 12,
          fontWeight: "400",
        });

        // Configure legend value labels to show percentages
        legend.valueLabels.template.setAll({
          fontFamily: "Montserrat",
          fontSize: 12,
          fontWeight: "400",
          text: "{valuePercentTotal.formatNumber('#.0')}%",
        });

        // Configure legend markers (squares like in the image)
        legend.markers.template.setAll({
          width: 16,
          height: 16,
        });

        // Set legend data
        legend.data.setAll(series.dataItems);

        // Configure tooltip
        series.slices.template.setAll({
          tooltipText: "{category}: {valuePercentTotal.formatNumber('#.0')}%",
        });

        // Animate chart
        series.appear(1000, 100);
        chart.appear(1000, 100);
      });
}
  
function chart4(){

  am5.ready(function () {
        // Create root element
        const root = am5.Root.new("chartDiv4");

        // Set themes
        root.setThemes([am5themes_Animated.new(root)]);

        // Create chart
        const chart = root.container.children.push(
          am5xy.XYChart.new(root, {
            panX: true,
            panY: true,
            wheelX: "panX",
            wheelY: "zoomX",
            pinchZoomX: true,
            paddingLeft: 0,
            paddingRight: 0,
            paddingTop: 50,
            paddingBottom: 0,
          })
        );

        // Add chart title
        const title = chart.children.unshift(
          am5.Label.new(root, {
            text: "Aluminium cash-settlement (LME)",
            fontSize: 12,
            fontWeight: "600",
            fontFamily: "Montserrat",
            x: am5.p50,
            centerX: am5.p50,
            y: 0,
            centerY: am5.p0,
            marginTop: 10,
            marginBottom: 10,
          })
        );

        // Create axes
        const xAxis = chart.xAxes.push(
          am5xy.DateAxis.new(root, {
            maxZoomCount: 50,
            baseInterval: {
              timeUnit: "month",
              count: 1,
            },
            renderer: am5xy.AxisRendererX.new(root, {
              minGridDistance: 80,
              cellStartLocation: 0.1,
              cellEndLocation: 0.9,
            }),
            tooltip: am5.Tooltip.new(root, {}),
          })
        );

        // Prepare data for actuals (left axis - USD per ton)
        const actualsData = [
          { date: new Date("2023-04-01").getTime(), value: 2350, volume: 1200 },
          { date: new Date("2023-05-01").getTime(), value: 2270, volume: 1350 },
          { date: new Date("2023-06-01").getTime(), value: 2190, volume: 1100 },
          { date: new Date("2023-07-01").getTime(), value: 2160, volume: 1450 },
          { date: new Date("2023-08-01").getTime(), value: 2150, volume: 1300 },
          { date: new Date("2023-09-01").getTime(), value: 2180, volume: 1250 },
          {
            date: new Date("2023-10-01").getTime(),
            value: 2192.45,
            volume: 1400,
          },
          { date: new Date("2023-11-01").getTime(), value: 2210, volume: 1550 },
          { date: new Date("2023-12-01").getTime(), value: 2160, volume: 1320 },
          { date: new Date("2024-01-01").getTime(), value: 2200, volume: 1480 },
          { date: new Date("2024-02-01").getTime(), value: 2190, volume: 1380 },
          { date: new Date("2024-03-01").getTime(), value: 2220, volume: 1420 },
          { date: new Date("2024-04-01").getTime(), value: 2500, volume: 1600 },
          { date: new Date("2024-05-01").getTime(), value: 2570, volume: 1750 },
          { date: new Date("2024-06-01").getTime(), value: 2500, volume: 1650 },
          { date: new Date("2024-07-01").getTime(), value: 2370, volume: 1520 },
          { date: new Date("2024-08-01").getTime(), value: 2340, volume: 1480 },
          { date: new Date("2024-09-01").getTime(), value: 2460, volume: 1580 },
          { date: new Date("2024-10-01").getTime(), value: 2600, volume: 1720 },
          { date: new Date("2024-11-01").getTime(), value: 2580, volume: 1680 },
          { date: new Date("2024-12-01").getTime(), value: 2540, volume: 1620 },
          { date: new Date("2025-01-01").getTime(), value: 2580, volume: 1700 },
          { date: new Date("2025-02-01").getTime(), value: 2670, volume: 1800 },
          { date: new Date("2025-03-01").getTime(), value: 2690, volume: 1850 },
        ];

        const yAxis = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            min: 2100,
            max: 2700,
            renderer: am5xy.AxisRendererY.new(root, {
              strokeOpacity: 1,
              stroke: am5.color("#000000"),
            }),
          })
        );

        // Set left axis title
        // yAxis.set(
        //   "title",
        //   am5.Label.new(root, {
        //     text: "Price (USD/ton)",
        //     fontFamily: "Montserrat",
        //     fontSize: 12,
        //     fontWeight: "400",
        //     rotation: -90,
        //     y: am5.p50,
        //     centerX: am5.p50,
        //   })
        // );

        // Create second Y-axis (right side) for volume data
        const yAxisRight = chart.yAxes.push(
          am5xy.ValueAxis.new(root, {
            min: 1000,
            max: 2000,
            renderer: am5xy.AxisRendererY.new(root, {
              opposite: true,
              strokeOpacity: 1,
              stroke: am5.color("#000000"),
            }),
          })
        );

        // Set right axis title
        // yAxisRight.set(
        //   "title",
        //   am5.Label.new(root, {
        //     text: "Volume (000 tons)",
        //     fontFamily: "Montserrat",
        //     fontSize: 12,
        //     fontWeight: "400",
        //     rotation: 90,
        //     y: am5.p50,
        //     centerX: am5.p50,
        //   })
        // );

        // Add left axis title as separate element
        const leftAxisTitle = chart.plotContainer.children.push(
          am5.Label.new(root, {
            text: "Price (USD/ton)",
            fontFamily: "Montserrat",
            fontSize: 12,
            fontWeight: "400",
            rotation: -90,
            x: am5.p0,
            y: am5.p50,
            centerX: am5.p50,
            centerY: am5.p50,
            dx: -60,
          })
        );

        // Add right axis title as separate element
        const rightAxisTitle = chart.plotContainer.children.push(
          am5.Label.new(root, {
            text: "Volume (000 tons)",
            fontFamily: "Montserrat",
            fontSize: 12,
            fontWeight: "400",
            rotation: 90,
            x: am5.p100,
            y: am5.p50,
            centerX: am5.p50,
            centerY: am5.p50,
            dx: 60,
          })
        );

        // Set font styles
        const fontSettings = {
          fontFamily: "Montserrat",
          fontSize: 12,
          fontWeight: "400",
        };

        xAxis.get("renderer").labels.template.setAll(fontSettings);
        yAxis.get("renderer").labels.template.setAll(fontSettings);
        yAxisRight.get("renderer").labels.template.setAll(fontSettings);

        // Configure X-axis date format (MMM-YYYY like Jul-2024)
        xAxis.get("renderer").labels.template.setAll({
          ...fontSettings,
          rotation: -45,
          centerY: am5.p50,
          centerX: am5.p100,
        });

        // Set date format for X-axis labels
        xAxis.set("dateFormats", {
          month: "MMM-yyyy",
        });

        xAxis.set("periodChangeDateFormats", {
          month: "MMM-yyyy",
        });

        // Configure axis lines (correct approach for AmCharts v5)
        xAxis.get("renderer").set("strokeOpacity", 1);
        xAxis.get("renderer").set("stroke", am5.color("#000000"));

        // Set axis titles
        // yAxis.set(
        //   "title",
        //   am5.Label.new(root, {
        //     text: "Price (USD/ton)",
        //     ...fontSettings,
        //     rotation: -90,
        //     y: am5.p50,
        //     centerX: am5.p50,
        //     marginRight: 20,
        //   })
        // );

        // yAxisRight.set(
        //   "title",
        //   am5.Label.new(root, {
        //     text: "Volume (000 tons)",
        //     ...fontSettings,
        //     rotation: 90,
        //     y: am5.p50,
        //     centerX: am5.p50,
        //     marginLeft: 20,
        //   })
        // );

        // Create series for price (left axis - solid line)
        const priceSeries = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Price (USD/ton)",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            valueXField: "date",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
            stroke: am5.color("#00b19c"),
            fill: am5.color("#00b19c"),
          })
        );

        // Create series for volume (right axis - dashed line)
        const volumeSeries = chart.series.push(
          am5xy.LineSeries.new(root, {
            name: "Volume (000 tons)",
            xAxis: xAxis,
            yAxis: yAxisRight,
            valueYField: "volume",
            valueXField: "date",
            tooltip: am5.Tooltip.new(root, {
              labelText: "{valueY}",
            }),
            stroke: am5.color("#3bcd3f"),
            fill: am5.color("#3bcd3f"),
          })
        );

        // Set line styles
        priceSeries.strokes.template.setAll({
          strokeWidth: 2,
          strokeDasharray: [],
        });

        volumeSeries.strokes.template.setAll({
          strokeWidth: 2,
          strokeDasharray: [5, 5],
        });

        // Add bullets (markers) for price series
        priceSeries.bullets.push(function () {
          const bulletCircle = am5.Circle.new(root, {
            radius: 4,
            fill: am5.color("#00b19c"),
            stroke: am5.color("#ffffff"),
            strokeWidth: 2,
          });
          return am5.Bullet.new(root, {
            sprite: bulletCircle,
          });
        });

        // Add bullets (markers) for volume series
        volumeSeries.bullets.push(function () {
          const bulletCircle = am5.Circle.new(root, {
            radius: 4,
            fill: am5.color("#3bcd3f"),
            stroke: am5.color("#ffffff"),
            strokeWidth: 2,
          });
          return am5.Bullet.new(root, {
            sprite: bulletCircle,
          });
        });

        // Add legend
        const legend = chart.children.push(
          am5.Legend.new(root, {
            centerX: am5.p50,
            x: am5.p50,
            centerY: am5.p0,
            y: am5.p100,
            marginTop: 15,
            marginBottom: 5,
          })
        );

        legend.labels.template.setAll(fontSettings);
        legend.data.setAll(chart.series.values);

        // Set data
        priceSeries.data.setAll(actualsData);
        volumeSeries.data.setAll(actualsData);

        // Add cursor
        chart.set(
          "cursor",
          am5xy.XYCursor.new(root, {
            behavior: "zoomX",
          })
        );

        // Configure grid
        xAxis.get("renderer").grid.template.setAll({
          stroke: am5.color("#e0e0e0"),
          strokeDasharray: [1, 3],
        });

        yAxis.get("renderer").grid.template.setAll({
          stroke: am5.color("#e0e0e0"),
          strokeDasharray: [1, 3],
        });

        yAxisRight.get("renderer").grid.template.setAll({
          stroke: am5.color("#e0e0e0"),
          strokeDasharray: [1, 3],
        });

        // Make stuff animate on load
        priceSeries.appear(1000);
        volumeSeries.appear(1000);
        chart.appear(1000, 100);
      });
}

  chart1()
  chart2()
  chart3()
  chart4()

  $(function(){
    $('.ciKeyInsightScrollInsight').slimScroll({
        color: '#8dbac4',
        opacity: 1,
        height: '277px',
        alwaysVisible: true
    });
  })
  </script>
  <!-- Enhanced Preview Functionality - Load at the end -->
  <script src="./js/preview-functionality.js"></script>
</body>

</html>