/**
 * Canvas Studio Integration - Professional Enterprise Interface
 * Modern dashboard builder with intuitive UX flow
 */

class CanvasStudioIntegrationIndex3 {
  constructor() {
    this.containers = new Map();
    this.widgets = [];
    this.selectedContainer = null;
    this.draggedWidget = null;
    this.isInitialized = false;
    this.isDragging = false;
    this.gridInstances = new Map(); // Store GridStack instances for containers

    // Track intervals for cleanup
    this.intervals = new Set();
    this.isRendering = false; // Prevent rendering loops

    // Wizard properties
    this.currentStep = 1;
    this.selectedContainerId = null;
    this.selectedWidgets = [];
    this.escapeKeyHandler = null;

    // Configuration
    this.config = {
      maxContainers: 50,
      autosave: true,
      autosaveInterval: 30000, // Increased from 5000 to 30000 (30 seconds)
      maxWidgetsPerContainer: 100,
      enableKeyboardShortcuts: true,
      enableTooltips: true,
      animation: {
        duration: 300,
        easing: "cubic-bezier(0.4, 0, 0.2, 1)",
      },
      debug: false,
      gridSettings: {
        section: {
          column: 12,
          cellHeight: 60,
          margin: 8,
          float: true,
          animate: true,
          acceptWidgets: true,
          removable: false,
          resizable: {
            handles: "se, sw, ne, nw",
          },
          draggable: {
            handle: ".widget-drag-handle",
          },
        },
        tab: {
          column: 12,
          cellHeight: 60,
          margin: 8,
          float: true,
          animate: true,
          acceptWidgets: true,
          removable: false,
          resizable: {
            handles: "se, sw, ne, nw",
          },
          draggable: {
            handle: ".widget-drag-handle",
          },
        },
      },
    };

    // Bind methods
    this.init = this.init.bind(this);
    this.showCanvasStudio = this.showCanvasStudio.bind(this);
    this.hideCanvasStudio = this.hideCanvasStudio.bind(this);
    this.handleKeyboardShortcuts = this.handleKeyboardShortcuts.bind(this);
    this.cleanup = this.cleanup.bind(this);

    console.log("Canvas Studio constructor initialized");
  }

  /**
   * Initialize Canvas Studio
   */
  async init() {
    try {
      console.log("Initializing Canvas Studio...");

      // Load data
      await this.loadWidgets();
      await this.loadContainers();

      // Setup UI
      this.addHeaderButton();
      this.addModalHTML();
      this.setupEventListeners();

      // Initialize features
      this.setupGridStackDragAndDrop();
      this.setupAutosave();
      this.setupKeyboardShortcuts();

      // ✅ NEW: Setup periodic health check for button
      this.setupButtonHealthCheck();

      // Setup cleanup on page unload
      window.addEventListener("beforeunload", this.cleanup);
      window.addEventListener("unload", this.cleanup);

      this.isInitialized = true;
      console.log("✅ Canvas Studio initialized successfully!");

      this.showToast("Canvas Studio ready!", "success");
    } catch (error) {
      console.error("❌ Canvas Studio initialization failed:", error);
      this.showToast("Failed to initialize Canvas Studio", "error");
    }
  }

  /**
   * Add header button with professional styling
   */
  addHeaderButton() {
    const headerBtnGroup = document.querySelector(".header-btn-group");
    if (!headerBtnGroup) {
      console.warn("Header button group not found");
      return;
    }

    // Check if button already exists
    if (document.getElementById("canvas-studio-btn")) {
      return;
    }

    const button = document.createElement("button");
    button.id = "canvas-studio-btn";
    button.className =
      "btn btn-outline-primary d-flex align-items-center gap-2";
    button.innerHTML = `
      <i class="fas fa-paint-brush"></i>
      <span>Canvas Studio</span>
    `;
    button.title = "Open Canvas Studio";

    headerBtnGroup.appendChild(button);

    // ✅ IMMEDIATE EVENT LISTENER - Attach event listener right after button creation
    button.addEventListener("click", (e) => {
      e.preventDefault();
      e.stopPropagation();
      console.log("Canvas Studio button clicked - delegated listener");
      this.showCanvasStudio();
    });

    console.log("✅ Canvas Studio button added with event listener");
  }

  /**
   * Add modal HTML with modern, clean, and professional structure
   */
  addModalHTML() {
    const modalHTML = `
      <!-- Canvas Studio Step-by-Step Wizard -->
      <div id="canvas-studio-modal" class="canvas-studio-wizard" style="display: none;">
        <!-- Modal Overlay -->
        <div class="wizard-overlay"></div>
        
        <!-- Wizard Container -->
        <div class="wizard-container">
          <!-- Header -->
          <div class="wizard-header">
            <h2><i class="fas fa-magic"></i> Canvas Studio</h2>
            <button class="btn-close" onclick="window.canvasStudio?.hideCanvasStudio()">
              <i class="fas fa-times"></i>
            </button>
          </div>

          <!-- Step Progress -->
          <div class="wizard-progress">
            <div class="step-indicator">
              <div class="step active" data-step="1">
                <span class="step-number">1</span>
                <span class="step-label">Container</span>
              </div>
              <div class="step" data-step="2">
                <span class="step-number">2</span>
                <span class="step-label">Widgets</span>
              </div>
              <div class="step" data-step="3">
                <span class="step-number">3</span>
                <span class="step-label">Save</span>
              </div>
            </div>
          </div>

          <!-- Step Content -->
          <div class="wizard-content">
            
            <!-- Step 1: Container Selection -->
            <div class="step-content active" id="step-1">
              <div class="step-header">
                <h3><i class="fas fa-layer-group"></i> Select or Create Container</h3>
                <p>Choose an existing container or create a new one</p>
              </div>
              
              <div class="container-actions">
                <button class="btn-primary" onclick="window.canvasStudio?.createNewContainer()">
                  <i class="fas fa-plus"></i> Create New Container
                </button>
                <button class="btn-secondary" onclick="window.canvasStudio?.showExistingContainers()">
                  <i class="fas fa-list"></i> Select Existing (<span id="container-count">0</span>)
                </button>
              </div>

              <div class="existing-containers" id="existing-containers" style="display: none;">
                <div class="containers-grid" id="containers-grid">
                  <!-- Containers will be rendered here -->
                </div>
              </div>

              <div class="selected-container" id="selected-container" style="display: none;">
                <div class="container-preview">
                  <h4>Selected Container</h4>
                  <div class="container-info" id="container-info">
                    <!-- Selected container info -->
                  </div>
                </div>
              </div>
            </div>

            <!-- Step 2: Widget Selection -->
            <div class="step-content" id="step-2">
              <div class="step-header">
                <h3><i class="fas fa-puzzle-piece"></i> Add Widgets</h3>
                <p>Drag widgets to your container or click to add</p>
              </div>
              
              <div class="widget-section">
                <div class="widget-search">
                  <input type="text" placeholder="Search widgets..." onkeyup="window.canvasStudio?.filterWidgets(this.value)">
                  <i class="fas fa-search"></i>
                </div>
                
                <div class="widget-categories" id="widget-categories">
                  <!-- Widget categories will be rendered here -->
                </div>
              </div>

              <div class="container-preview-step2">
                <h4>Container Preview</h4>
                <div class="mini-canvas" id="mini-canvas">
                  <!-- Mini preview of container with widgets -->
                </div>
              </div>
            </div>

            <!-- Step 3: Save & Configure -->
            <div class="step-content" id="step-3">
              <div class="step-header">
                <h3><i class="fas fa-save"></i> Save & Configure</h3>
                <p>Review and save your container configuration</p>
              </div>
              
              <div class="final-preview">
                <div class="preview-section">
                  <h4>Container Summary</h4>
                  <div class="summary-info" id="summary-info">
                    <!-- Summary information -->
                  </div>
                </div>
                
                <div class="config-options">
                  <div class="form-group">
                    <label>Container Name</label>
                    <input type="text" id="container-name" placeholder="Enter container name">
                  </div>
                  
                  <div class="form-group">
                    <label>Layout Type</label>
                    <select id="layout-type">
                      <option value="grid">Grid Layout</option>
                      <option value="flex">Flex Layout</option>
                      <option value="tabs">Tab Layout</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Navigation Buttons -->
          <div class="wizard-footer">
            <button class="btn-back" onclick="window.canvasStudio?.previousStep()" style="display: none;">
              <i class="fas fa-arrow-left"></i> Back
            </button>
            <div class="footer-right">
              <button class="btn-cancel" onclick="window.canvasStudio?.hideCanvasStudio()">
                Cancel
              </button>
              <button class="btn-next" onclick="window.canvasStudio?.nextStep()">
                Next <i class="fas fa-arrow-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Confirmation Modals -->
      <div id="clear-all-modal" class="confirmation-modal" style="display: none;">
        <div class="confirmation-overlay"></div>
        <div class="confirmation-dialog">
          <div class="confirmation-header">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>Clear All Containers</h3>
          </div>
          <div class="confirmation-body">
            <p>Are you sure you want to clear all containers?</p>
            <p class="warning">This action cannot be undone.</p>
          </div>
          <div class="confirmation-footer">
            <button class="btn-cancel" onclick="window.canvasStudio?.hideClearAllModal()">Cancel</button>
            <button class="btn-danger" onclick="window.canvasStudio?.confirmClearAll()">Clear All</button>
          </div>
        </div>
      </div>

      <style>
        /* Canvas Studio Wizard Styles */
        .canvas-studio-wizard {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 9999;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .wizard-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.6);
          backdrop-filter: blur(4px);
        }

        .wizard-container {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 90%;
          max-width: 800px;
          height: 80vh;
          max-height: 600px;
          background: white;
          border-radius: 12px;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        /* Header */
        .wizard-header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 20px 24px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-shrink: 0;
        }

        .wizard-header h2 {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .btn-close {
          background: rgba(255, 255, 255, 0.1);
          border: none;
          color: white;
          width: 32px;
          height: 32px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .btn-close:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        /* Progress Steps */
        .wizard-progress {
          background: #f8fafc;
          border-bottom: 1px solid #e2e8f0;
          padding: 20px 24px;
          flex-shrink: 0;
        }

        .step-indicator {
          display: flex;
          justify-content: center;
          gap: 40px;
        }

        .step {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 8px;
          opacity: 0.5;
          transition: all 0.3s ease;
        }

        .step.active {
          opacity: 1;
        }

        .step.completed {
          opacity: 1;
          color: #10b981;
        }

        .step-number {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #e2e8f0;
          color: #64748b;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 14px;
          transition: all 0.3s ease;
        }

        .step.active .step-number {
          background: #3b82f6;
          color: white;
        }

        .step.completed .step-number {
          background: #10b981;
          color: white;
        }

        .step-label {
          font-size: 12px;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        /* Content */
        .wizard-content {
          flex: 1;
          overflow-y: auto;
          position: relative;
        }

        .step-content {
          display: none;
          padding: 24px;
          height: 100%;
        }

        .step-content.active {
          display: block;
        }

        .step-header {
          text-align: center;
          margin-bottom: 24px;
        }

        .step-header h3 {
          margin: 0 0 8px 0;
          font-size: 18px;
          font-weight: 600;
          color: #1e293b;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 10px;
        }

        .step-header p {
          margin: 0;
          color: #64748b;
          font-size: 14px;
        }

        /* Step 1 Styles */
        .container-actions {
          display: flex;
          gap: 16px;
          justify-content: center;
          margin-bottom: 24px;
        }

        .btn-primary, .btn-secondary {
          padding: 12px 24px;
          border: none;
          border-radius: 8px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .btn-primary {
          background: #3b82f6;
          color: white;
        }

        .btn-primary:hover {
          background: #2563eb;
          transform: translateY(-1px);
        }

        .btn-secondary {
          background: #f1f5f9;
          color: #475569;
          border: 1px solid #e2e8f0;
        }

        .btn-secondary:hover {
          background: #e2e8f0;
        }

        .existing-containers {
          margin-top: 20px;
        }

        .containers-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 16px;
          max-height: 200px;
          overflow-y: auto;
        }

        .container-card {
          background: white;
          border: 2px solid #e2e8f0;
          border-radius: 8px;
          padding: 16px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .container-card:hover {
          border-color: #3b82f6;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .container-card.selected {
          border-color: #3b82f6;
          background: #eff6ff;
        }

        .selected-container {
          margin-top: 20px;
          padding: 16px;
          background: #f8fafc;
          border-radius: 8px;
        }

        /* Step 2 Styles */
        .widget-section {
          margin-bottom: 24px;
        }

        .widget-search {
          position: relative;
          margin-bottom: 20px;
        }

        .widget-search input {
          width: 100%;
          padding: 12px 16px 12px 40px;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          font-size: 14px;
        }

        .widget-search i {
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          color: #64748b;
        }

        .widget-categories {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
          gap: 12px;
          max-height: 200px;
          overflow-y: auto;
        }

        .widget-item {
          background: white;
          border: 1px solid #e2e8f0;
          border-radius: 8px;
          padding: 12px;
          text-align: center;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .widget-item:hover {
          border-color: #10b981;
          box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
        }

        .widget-item i {
          font-size: 24px;
          margin-bottom: 8px;
          color: #3b82f6;
        }

        .widget-item span {
          display: block;
          font-size: 12px;
          font-weight: 500;
          color: #374151;
        }

        .container-preview-step2 {
          margin-top: 20px;
          padding: 16px;
          background: #f8fafc;
          border-radius: 8px;
        }

        .mini-canvas {
          min-height: 100px;
          background: white;
          border: 2px dashed #e2e8f0;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #64748b;
          font-size: 14px;
        }

        /* Step 3 Styles */
        .final-preview {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 24px;
        }

        .preview-section, .config-options {
          background: #f8fafc;
          padding: 16px;
          border-radius: 8px;
        }

        .form-group {
          margin-bottom: 16px;
        }

        .form-group label {
          display: block;
          margin-bottom: 6px;
          font-weight: 500;
          color: #374151;
          font-size: 14px;
        }

        .form-group input,
        .form-group select {
          width: 100%;
          padding: 10px 12px;
          border: 1px solid #e2e8f0;
          border-radius: 6px;
          font-size: 14px;
        }

        /* Footer */
        .wizard-footer {
          background: #f8fafc;
          border-top: 1px solid #e2e8f0;
          padding: 16px 24px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-shrink: 0;
        }

        .footer-right {
          display: flex;
          gap: 12px;
        }

        .btn-back, .btn-cancel, .btn-next {
          padding: 10px 20px;
          border: none;
          border-radius: 6px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 6px;
        }

        .btn-back {
          background: #f1f5f9;
          color: #475569;
        }

        .btn-cancel {
          background: #f1f5f9;
          color: #475569;
        }

        .btn-next {
          background: #3b82f6;
          color: white;
        }

        .btn-back:hover, .btn-cancel:hover {
          background: #e2e8f0;
        }

        .btn-next:hover {
          background: #2563eb;
        }

        /* Confirmation Modal */
        .confirmation-modal {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          z-index: 10000;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .confirmation-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.5);
        }

        .confirmation-dialog {
          position: relative;
          background: white;
          border-radius: 12px;
          padding: 0;
          max-width: 400px;
          width: 90%;
          box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .confirmation-header {
          padding: 24px 24px 0 24px;
          text-align: center;
        }

        .confirmation-header i {
          font-size: 48px;
          color: #ef4444;
          margin-bottom: 16px;
        }

        .confirmation-header h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #1e293b;
        }

        .confirmation-body {
          padding: 16px 24px;
          text-align: center;
        }

        .confirmation-body p {
          margin: 0 0 8px 0;
          color: #64748b;
        }

        .confirmation-body .warning {
          color: #ef4444;
          font-weight: 500;
        }

        .confirmation-footer {
          padding: 16px 24px 24px 24px;
          display: flex;
          gap: 12px;
          justify-content: center;
        }

        .btn-danger {
          background: #ef4444;
          color: white;
          padding: 10px 20px;
          border: none;
          border-radius: 6px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .btn-danger:hover {
          background: #dc2626;
        }

        /* Responsive */
        @media (max-width: 768px) {
          .wizard-container {
            width: 95%;
            height: 90vh;
          }
          
          .final-preview {
            grid-template-columns: 1fr;
          }
          
          .container-actions {
            flex-direction: column;
          }
        }
      </style>
    `;

    document.body.insertAdjacentHTML("beforeend", modalHTML);
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    console.log("Setting up Canvas Studio event listeners...");

    // Container selection
    document.addEventListener("click", (e) => {
      if (e.target.closest(".container-item")) {
        const containerId =
          e.target.closest(".container-item").dataset.containerId;
        this.selectContainer(containerId);
      }

      // Track clicks on main section area (outside tab-containers)
      if (e.target.closest(".canvas-content")) {
        const canvasContent = e.target.closest(".canvas-content");
        const tabContainer = e.target.closest(".tab-container-widget");

        // If clicked in canvas but not in a tab-container widget, mark as main section click
        if (!tabContainer) {
          console.log(
            "🎯 Main section area clicked - widgets will be added to section container"
          );
          this.lastMainSectionClick = Date.now();

          // Visual feedback - briefly highlight the section container
          this.highlightSectionContainer();
        }
      }
    });

    // Clear canvas confirmation
    document.addEventListener("click", (e) => {
      if (e.target.id === "clear-canvas-btn") {
        this.showClearAllConfirmation();
      }
    });

    // Create container button
    document.addEventListener("click", (e) => {
      if (e.target.id === "create-container-btn") {
        this.createContainer();
      }
    });

    // Widget search
    const searchInput = document.getElementById("widget-search");
    if (searchInput) {
      searchInput.addEventListener("input", (e) => {
        this.filterWidgets(e.target.value);
      });
    }

    console.log("✅ Event listeners setup complete");
  }

  /**
   * Show Canvas Studio modal with enhanced error handling
   */
  showCanvasStudio() {
    try {
      const modal = document.getElementById("canvas-studio-modal");
      if (!modal) {
        console.error("Canvas Studio modal not found");
        return;
      }

      // Initialize wizard state
      this.currentStep = 1;
      this.selectedContainerId = null;
      this.selectedWidgets = [];

      // Show the modal
      modal.style.display = "block";

      // Load data and initialize
      this.loadContainers();
      this.updateContainerCount();
      this.loadWidgetCategories();

      // Add escape key listener
      this.escapeKeyHandler = (e) => {
        if (e.key === "Escape") {
          this.hideCanvasStudio();
        }
      };
      document.addEventListener("keydown", this.escapeKeyHandler);

      this.showToast("Canvas Studio opened", "success");
    } catch (error) {
      console.error("Error showing Canvas Studio:", error);
      this.showToast("Error opening Canvas Studio", "error");
    }
  }

  hideCanvasStudio() {
    try {
      const modal = document.getElementById("canvas-studio-modal");
      if (!modal) {
        return;
      }

      modal.style.display = "none";

      // Remove event listeners
      if (this.escapeKeyHandler) {
        document.removeEventListener("keydown", this.escapeKeyHandler);
        this.escapeKeyHandler = null;
      }

      this.showToast("Canvas Studio closed", "info");
    } catch (error) {
      console.error("Error hiding Canvas Studio:", error);
    }
  }

  // Wizard Step Navigation
  nextStep() {
    if (this.currentStep === 1) {
      // Validate step 1 - container selection
      if (!this.selectedContainerId) {
        this.showToast("Please select or create a container first", "warning");
        return;
      }
      this.goToStep(2);
    } else if (this.currentStep === 2) {
      this.goToStep(3);
      this.updateSummary();
    } else if (this.currentStep === 3) {
      this.saveContainer();
    }
  }

  previousStep() {
    if (this.currentStep > 1) {
      this.goToStep(this.currentStep - 1);
    }
  }

  goToStep(stepNumber) {
    // Hide all step contents
    document.querySelectorAll(".step-content").forEach((content) => {
      content.classList.remove("active");
    });

    // Hide all step indicators
    document.querySelectorAll(".step").forEach((step) => {
      step.classList.remove("active", "completed");
    });

    // Show current step content
    const currentContent = document.getElementById(`step-${stepNumber}`);
    if (currentContent) {
      currentContent.classList.add("active");
    }

    // Update step indicators
    for (let i = 1; i <= 3; i++) {
      const step = document.querySelector(`[data-step="${i}"]`);
      if (i < stepNumber) {
        step.classList.add("completed");
      } else if (i === stepNumber) {
        step.classList.add("active");
      }
    }

    // Update navigation buttons
    const backBtn = document.querySelector(".btn-back");
    const nextBtn = document.querySelector(".btn-next");

    if (stepNumber === 1) {
      backBtn.style.display = "none";
      nextBtn.innerHTML = 'Next <i class="fas fa-arrow-right"></i>';
    } else if (stepNumber === 2) {
      backBtn.style.display = "flex";
      nextBtn.innerHTML = 'Next <i class="fas fa-arrow-right"></i>';
    } else if (stepNumber === 3) {
      backBtn.style.display = "flex";
      nextBtn.innerHTML = '<i class="fas fa-save"></i> Save Container';
    }

    this.currentStep = stepNumber;
  }

  // Step 1 Functions
  createNewContainer() {
    const containerId = this.generateId();
    const containerName = `Container ${this.containers.size + 1}`;

    const container = {
      id: containerId,
      name: containerName,
      type: "section",
      widgets: [],
      layoutType: "grid",
      columns: 3,
      created: new Date().toISOString(),
    };

    this.containers.set(containerId, container);
    this.selectedContainerId = containerId;
    this.saveConfiguration();
    this.updateContainerCount();
    this.showSelectedContainer();

    this.showToast(`Created "${containerName}"`, "success");
  }

  showExistingContainers() {
    const existingDiv = document.getElementById("existing-containers");
    const containersGrid = document.getElementById("containers-grid");

    if (this.containers.size === 0) {
      this.showToast("No existing containers found", "info");
      return;
    }

    existingDiv.style.display = "block";

    let html = "";
    this.containers.forEach((container, id) => {
      html += `
        <div class="container-card ${
          this.selectedContainerId === id ? "selected" : ""
        }" 
             onclick="window.canvasStudio?.selectContainerInWizard('${id}')">
          <h5>${container.name}</h5>
          <p>${container.widgets.length} widgets</p>
          <small>Created: ${new Date(
            container.created
          ).toLocaleDateString()}</small>
        </div>
      `;
    });

    containersGrid.innerHTML = html;
  }

  selectContainerInWizard(containerId) {
    this.selectedContainerId = containerId;

    // Update visual selection
    document.querySelectorAll(".container-card").forEach((card) => {
      card.classList.remove("selected");
    });
    event.target.closest(".container-card").classList.add("selected");

    this.showSelectedContainer();
    this.showToast("Container selected", "success");
  }

  showSelectedContainer() {
    const selectedDiv = document.getElementById("selected-container");
    const containerInfo = document.getElementById("container-info");

    if (this.selectedContainerId) {
      const container = this.containers.get(this.selectedContainerId);
      selectedDiv.style.display = "block";

      containerInfo.innerHTML = `
        <div class="container-details">
          <h5>${container.name}</h5>
          <p><strong>Type:</strong> ${container.type}</p>
          <p><strong>Layout:</strong> ${container.layoutType}</p>
          <p><strong>Widgets:</strong> ${container.widgets.length}</p>
        </div>
      `;
    } else {
      selectedDiv.style.display = "none";
    }
  }

  updateContainerCount() {
    const countElement = document.getElementById("container-count");
    if (countElement) {
      countElement.textContent = this.containers.size;
    }
  }

  // Step 2 Functions
  loadWidgetCategories() {
    const categoriesDiv = document.getElementById("widget-categories");
    const widgets = [
      { type: "text", name: "Text", icon: "fas fa-font" },
      { type: "button", name: "Button", icon: "fas fa-mouse-pointer" },
      { type: "image", name: "Image", icon: "fas fa-image" },
      { type: "video", name: "Video", icon: "fas fa-video" },
      { type: "chart", name: "Chart", icon: "fas fa-chart-bar" },
      { type: "table", name: "Table", icon: "fas fa-table" },
      { type: "form", name: "Form", icon: "fas fa-wpforms" },
      { type: "card", name: "Card", icon: "fas fa-id-card" },
      {
        type: "tab-container",
        name: "Tab Container",
        icon: "fas fa-folder-open",
      },
      {
        type: "section-container",
        name: "Section Container",
        icon: "fas fa-th-large",
      },
    ];

    let html = "";
    widgets.forEach((widget) => {
      html += `
        <div class="widget-item" onclick="window.canvasStudio?.addWidgetToContainer('${widget.type}')">
          <i class="${widget.icon}"></i>
          <span>${widget.name}</span>
        </div>
      `;
    });

    categoriesDiv.innerHTML = html;
  }

  addWidgetToContainer(widgetType) {
    if (!this.selectedContainerId) {
      this.showToast("No container selected", "error");
      return;
    }

    const widgetId = this.generateId();
    const widget = {
      id: widgetId,
      type: widgetType,
      name: this.getWidgetName(widgetType),
      x: 0,
      y: 0,
      w: 2,
      h: 2,
    };

    const container = this.containers.get(this.selectedContainerId);
    container.widgets.push(widget);
    this.selectedWidgets.push(widget);

    this.updateMiniCanvas();
    this.showToast(`Added ${widget.name}`, "success");
  }

  updateMiniCanvas() {
    const miniCanvas = document.getElementById("mini-canvas");

    if (this.selectedWidgets.length === 0) {
      miniCanvas.innerHTML = "No widgets added yet";
      return;
    }

    let html = '<div class="widget-list">';
    this.selectedWidgets.forEach((widget) => {
      html += `<span class="widget-tag">${widget.name}</span>`;
    });
    html += "</div>";

    miniCanvas.innerHTML = html;
  }

  // Step 3 Functions
  updateSummary() {
    const summaryInfo = document.getElementById("summary-info");
    const containerNameInput = document.getElementById("container-name");
    const layoutTypeSelect = document.getElementById("layout-type");

    if (this.selectedContainerId) {
      const container = this.containers.get(this.selectedContainerId);

      containerNameInput.value = container.name;
      layoutTypeSelect.value = container.layoutType;

      summaryInfo.innerHTML = `
        <div class="summary-details">
          <p><strong>Container:</strong> ${container.name}</p>
          <p><strong>Widgets:</strong> ${this.selectedWidgets.length}</p>
          <p><strong>Layout:</strong> ${container.layoutType}</p>
          <div class="widget-summary">
            <strong>Added Widgets:</strong>
            ${this.selectedWidgets
              .map((w) => `<span class="widget-tag">${w.name}</span>`)
              .join("")}
          </div>
        </div>
      `;
    }
  }

  saveContainer() {
    const containerNameInput = document.getElementById("container-name");
    const layoutTypeSelect = document.getElementById("layout-type");

    if (this.selectedContainerId) {
      const container = this.containers.get(this.selectedContainerId);
      container.name = containerNameInput.value || container.name;
      container.layoutType = layoutTypeSelect.value;

      this.saveConfiguration();
      this.showToast("Container saved successfully!", "success");

      setTimeout(() => {
        this.hideCanvasStudio();
      }, 1000);
    }
  }

  // Confirmation Modal Functions
  showClearAllConfirmation() {
    document.getElementById("clear-all-modal").style.display = "flex";
  }

  hideClearAllModal() {
    document.getElementById("clear-all-modal").style.display = "none";
  }

  confirmClearAll() {
    this.clearAllContainers();
    this.hideClearAllModal();
  }

  /**
   * Create container
   */
  createContainer(type = "section") {
    const containerId = `container-${Date.now()}`;
    const containerName = `${
      type === "section" ? "Section" : "Tab"
    } Container ${this.containers.size + 1}`;

    const container = {
      id: containerId,
      name: containerName,
      type: "section", // Only section containers now
      widgets: [],
      isActive: false,
      settings: {
        backgroundColor: "#ffffff",
        padding: "20px",
        margin: "10px",
        borderRadius: "8px",
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
      },
    };

    this.containers.set(containerId, container);
    this.selectContainer(containerId);
    this.renderCanvas();
    this.renderContainers();
    this.loadWidgetLibrary();

    return containerId;
  }

  /**
   * Load containers from storage
   */
  async loadContainers() {
    try {
      const saved = localStorage.getItem("canvas-studio-containers");
      if (saved) {
        const containersArray = JSON.parse(saved);
        this.containers = new Map(containersArray);

        // Ensure all loaded containers have required properties
        for (const [id, container] of this.containers) {
          if (!container.widgets) {
            container.widgets = [];
          }
          if (!container.type) {
            container.type = "section";
          }
        }
      }
      console.log(`Loaded ${this.containers.size} containers`);
    } catch (error) {
      console.error("Failed to load containers:", error);
      this.containers = new Map();
    }
  }

  /**
   * Load widget library
   */
  async loadWidgets() {
    this.widgets = [
      // Layout Widgets (NEW)
      {
        type: "section-container",
        name: "Section Container",
        category: "Layout",
        description: "Draggable container that can hold multiple widgets",
        icon: "fas fa-th-large",
        defaultSize: { w: 8, h: 6 },
        isContainer: true,
        nestable: true,
      },
      {
        type: "tab-container",
        name: "Tab Container",
        category: "Layout",
        description: "Multi-tab container widget",
        icon: "fas fa-folder-open",
        defaultSize: { w: 8, h: 6 },
        isContainer: true,
        nestable: true,
      },
      {
        type: "grid-layout",
        name: "Grid Layout",
        category: "Layout",
        description: "Flexible grid layout container",
        icon: "fas fa-th",
        defaultSize: { w: 6, h: 4 },
        isContainer: true,
      },
      // Chart Widgets
      {
        type: "bar-chart",
        name: "Bar Chart",
        category: "Charts",
        description: "Display data in vertical bars",
        icon: "fas fa-chart-bar",
        defaultSize: { w: 4, h: 3 },
      },
      {
        type: "pie-chart",
        name: "Pie Chart",
        category: "Charts",
        description: "Show data as pie slices",
        icon: "fas fa-chart-pie",
        defaultSize: { w: 4, h: 3 },
      },
      {
        type: "line-chart",
        name: "Line Chart",
        category: "Charts",
        description: "Track trends over time",
        icon: "fas fa-chart-line",
        defaultSize: { w: 4, h: 3 },
      },
      {
        type: "area-chart",
        name: "Area Chart",
        category: "Charts",
        description: "Filled line chart",
        icon: "fas fa-chart-area",
        defaultSize: { w: 4, h: 3 },
      },
      // Data Widgets
      {
        type: "table",
        name: "Data Table",
        category: "Data",
        description: "Tabular data display",
        icon: "fas fa-table",
        defaultSize: { w: 6, h: 4 },
      },
      {
        type: "kpi",
        name: "KPI Widget",
        category: "Metrics",
        description: "Key performance indicators",
        icon: "fas fa-tachometer-alt",
        defaultSize: { w: 3, h: 2 },
      },
      // Content Widgets
      {
        type: "text",
        name: "Text Widget",
        category: "Content",
        description: "Rich text content",
        icon: "fas fa-font",
        defaultSize: { w: 4, h: 3 },
      },
      {
        type: "image",
        name: "Image Widget",
        category: "Media",
        description: "Display images",
        icon: "fas fa-image",
        defaultSize: { w: 4, h: 3 },
      },
      {
        type: "video",
        name: "Video Widget",
        category: "Media",
        description: "Embed videos",
        icon: "fas fa-video",
        defaultSize: { w: 4, h: 3 },
      },
      {
        type: "map",
        name: "Map Widget",
        category: "Location",
        description: "Interactive maps",
        icon: "fas fa-map-marked-alt",
        defaultSize: { w: 6, h: 4 },
      },
    ];
    console.log(`Loaded ${this.widgets.length} widgets`);
  }

  /**
   * Render containers list
   */
  renderContainers() {
    if (this.isRendering) return;
    this.isRendering = true;

    const containersList = document.querySelector(".containers-list");
    const containerCount = document.getElementById("container-count");

    if (!containersList) {
      this.isRendering = false;
      return;
    }

    // Update container count badge
    if (containerCount) {
      containerCount.textContent = this.containers.size;
    }

    if (this.containers.size === 0) {
      containersList.innerHTML = `
        <div class="text-center py-4 text-muted">
          <i class="fas fa-folder-open fa-2x mb-3 opacity-50"></i>
          <p class="mb-0">No containers yet</p>
          <small>Create your first container to get started</small>
        </div>
      `;
      this.isRendering = false;
      return;
    }

    let html = "";
    for (const [id, container] of this.containers) {
      const widgetCount = container.widgets?.length || 0;
      const isActive = this.selectedContainer === id ? "active" : "";

      html += `
        <div class="container-item ${isActive}" data-container-id="${id}">
          <div class="d-flex justify-content-between align-items-start mb-2">
            <div class="flex-grow-1">
              <h6 class="mb-1 text-dark container-name" data-container-id="${id}">
                <i class="fas fa-th-large me-2 text-primary"></i>
                ${container.name}
              </h6>
              <small class="text-muted">
                <i class="fas fa-cubes me-1"></i>
                ${widgetCount} widget${widgetCount !== 1 ? "s" : ""}
              </small>
            </div>
            <div class="dropdown">
              <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                      type="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <ul class="dropdown-menu">
                <li>
                  <a class="dropdown-item" href="#" onclick="canvasStudio.configureSectionLayout('${id}')">
                    <i class="fas fa-cog me-2"></i>Configure
                  </a>
                </li>
                <li>
                  <a class="dropdown-item" href="#" onclick="canvasStudio.renameContainer('${id}')">
                    <i class="fas fa-edit me-2"></i>Rename
                  </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <a class="dropdown-item text-danger" href="#" onclick="canvasStudio.deleteContainer('${id}')">
                    <i class="fas fa-trash me-2"></i>Delete
                  </a>
                </li>
              </ul>
            </div>
          </div>
          
          ${
            widgetCount > 0
              ? `
            <div class="container-widgets mt-2">
              <div class="d-flex flex-wrap gap-1">
                ${container.widgets
                  .slice(0, 3)
                  .map(
                    (widget) => `
                  <span class="badge bg-light text-dark border">
                    <i class="${this.getWidgetIcon(widget.type)} me-1"></i>
                    ${this.getWidgetName(widget.type)}
                  </span>
                `
                  )
                  .join("")}
                ${
                  widgetCount > 3
                    ? `
                  <span class="badge bg-secondary">+${
                    widgetCount - 3
                  } more</span>
                `
                    : ""
                }
              </div>
            </div>
          `
              : `
            <div class="text-center py-2">
              <small class="text-muted">
                <i class="fas fa-plus-circle me-1"></i>
                Click to add widgets
              </small>
            </div>
          `
          }
        </div>
      `;
    }

    containersList.innerHTML = html;

    // Add click event listeners for container selection
    containersList.querySelectorAll(".container-item").forEach((item) => {
      const containerId = item.dataset.containerId;
      item.addEventListener("click", (e) => {
        // Don't trigger selection if clicking on dropdown or buttons
        if (e.target.closest(".dropdown") || e.target.closest("button")) {
          return;
        }
        this.selectContainer(containerId);
      });
    });

    this.isRendering = false;
  }

  /**
   * Render canvas content
   */
  renderCanvas() {
    const canvasContent = document.querySelector(".canvas-content");
    if (!canvasContent) return;

    if (!this.selectedContainer) {
      canvasContent.innerHTML = `
        <div class="canvas-empty-state">
          <i class="fas fa-paint-brush"></i>
          <h4>Canvas Studio</h4>
          <p>Select a container from the sidebar to start building your dashboard</p>
          <button class="btn btn-primary" onclick="canvasStudio.createContainer('section')">
            <i class="fas fa-plus me-2"></i>Create Your First Section
          </button>
        </div>
      `;
      return;
    }

    const container = this.containers.get(this.selectedContainer);
    if (!container) return;

    // Update canvas header
    const canvasTitle = document.querySelector(".canvas-title h6");
    const canvasSubtitle = document.querySelector(".canvas-subtitle");
    if (canvasTitle) {
      canvasTitle.textContent = container.name;
    }
    if (canvasSubtitle) {
      // Safe access to widgets array with fallback to empty array
      const widgetCount = (container.widgets || []).length;
      const layoutTypeText =
        container.layoutType === "tabs" ? "Tab Container" : "Section Container";
      canvasSubtitle.textContent = `${layoutTypeText} • ${widgetCount} widgets`;
    }

    // Render based on layout type
    if (container.layoutType === "tabs") {
      // Initialize tabs if they don't exist
      if (
        !container.tabs ||
        !Array.isArray(container.tabs) ||
        container.tabs.length === 0
      ) {
        container.tabs = [
          {
            id: `tab-${Date.now()}`,
            name: "Analysis",
            active: true,
            widgets: container.widgets || [],
          },
          {
            id: `tab-${Date.now() + 1}`,
            name: "Forecast",
            active: false,
            widgets: [],
          },
          {
            id: `tab-${Date.now() + 2}`,
            name: "Data",
            active: false,
            widgets: [],
          },
        ];
        // Move existing widgets to first tab
        container.widgets = [];
        this.saveConfiguration();
      }
      this.renderTabContainer(container);
    } else {
      // Default to section container
      this.renderSectionContainer(container);
    }
  }

  /**
   * Render section container
   */
  renderSectionContainer(container) {
    const canvasContent = document.querySelector(".canvas-content");
    if (!canvasContent) return;

    // Safe access to widgets array with fallback to empty array
    const widgets = container.widgets || [];

    const sectionContainerHTML = `
      <div class="section-container" data-container-id="${container.id}">
        <div class="section-header">
          <div class="section-info">
            <h5><i class="fas fa-th-large"></i> ${container.name}</h5>
            <span class="section-widget-count">${widgets.length} widgets</span>
          </div>
          <div class="section-actions">
            <button class="btn btn-sm btn-outline-primary" onclick="canvasStudio.configureSectionLayout('${
              container.id
            }')">
              <i class="fas fa-cog"></i> Configure Layout
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="canvasStudio.renameContainer('${
              container.id
            }')">
              <i class="fas fa-edit"></i> Rename Section
            </button>
          </div>
        </div>
        
        <div class="grid-container">
          <div class="grid-stack" id="grid-${container.id}">
            ${this.renderGridStackPlaceholder(widgets.length === 0, "section")}
          </div>
        </div>
      </div>
    `;

    canvasContent.innerHTML = sectionContainerHTML;

    // Initialize GridStack for section
    setTimeout(() => {
      this.createGridStackContainer(container.id, "section");
      this.loadWidgetsIntoGrid(container.id, "section");
      this.updatePlaceholderVisibility(container.id);
    }, 100);
  }

  /**
   * Render GridStack placeholder
   */
  renderGridStackPlaceholder(isEmpty, containerType) {
    if (!isEmpty) return "";

    const placeholderText =
      containerType === "tab"
        ? "Drag widgets here to add them to this tab"
        : "Drag widgets here to add them to this section";

    return `
      <div class="grid-placeholder">
        <div class="placeholder-content">
          <i class="fas fa-plus-circle"></i>
          <h6>Empty ${containerType === "tab" ? "Tab" : "Section"}</h6>
          <p>${placeholderText}</p>
        </div>
      </div>
    `;
  }

  /**
   * Render tab container
   */
  renderTabContainer(container) {
    const canvasContent = document.querySelector(".canvas-content");
    if (!canvasContent) return;

    // Safety check for container.tabs
    if (
      !container.tabs ||
      !Array.isArray(container.tabs) ||
      container.tabs.length === 0
    ) {
      console.warn("Tab container has no tabs:", container);
      return;
    }

    const activeTab =
      container.tabs.find((tab) => tab.active) || container.tabs[0];
    const tabPosition = container.tabPosition || "bottom";

    const tabContainerHTML = `
      <div class="tab-container" data-container-id="${
        container.id
      }" data-tab-position="${tabPosition}">
        ${
          tabPosition === "top"
            ? this.renderTabNavigation(container, activeTab)
            : ""
        }
        
        <div class="tab-content-area">
          <div class="tab-content active" data-tab-id="${activeTab.id}">
            <div class="tab-header">
              <div class="tab-info">
                <h5><i class="fas fa-layer-group"></i> ${activeTab.name}</h5>
                <span class="tab-widget-count">${
                  activeTab.widgets.length
                } widgets</span>
              </div>
              <div class="tab-actions">
                <button class="btn btn-sm btn-outline-primary" onclick="canvasStudio.configureTabPosition('${
                  container.id
                }')">
                  <i class="fas fa-cog"></i> Configure
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="canvasStudio.renameTab('${
                  container.id
                }', '${activeTab.id}')">
                  <i class="fas fa-edit"></i> Rename Tab
                </button>
              </div>
            </div>
            
            <div class="grid-container">
              <div class="grid-stack" id="grid-${container.id}-${activeTab.id}">
                ${this.renderGridStackPlaceholder(
                  activeTab.widgets.length === 0,
                  "tab"
                )}
              </div>
            </div>
          </div>
        </div>
        
        ${
          tabPosition === "bottom"
            ? this.renderTabNavigation(container, activeTab)
            : ""
        }
      </div>
    `;

    canvasContent.innerHTML = tabContainerHTML;

    // Initialize GridStack for active tab
    setTimeout(() => {
      this.createGridStackContainer(container.id, "tab", activeTab.id);
      this.loadWidgetsIntoGrid(container.id, "tab", activeTab.id);
      this.updatePlaceholderVisibility(container.id, activeTab.id);
    }, 100);
  }

  /**
   * Render tab navigation
   */
  renderTabNavigation(container, activeTab) {
    // Safety check for container.tabs
    if (!container.tabs || !Array.isArray(container.tabs)) {
      console.warn("Cannot render tab navigation: container.tabs is invalid");
      return "";
    }

    return `
      <div class="tab-navigation">
        <div class="tab-nav-scroll">
          <div class="tab-nav-list">
            ${container.tabs
              .map(
                (tab) => `
              <button class="tab-nav-item ${
                tab.id === activeTab.id ? "active" : ""
              }" 
                      data-tab-id="${tab.id}"
                      onclick="canvasStudio.switchTab('${container.id}', '${
                  tab.id
                }')">
                <span class="tab-name">${tab.name}</span>
                <span class="tab-badge">${tab.widgets.length}</span>
                ${
                  container.tabs.length > 1
                    ? `
                  <button class="tab-close" 
                          onclick="event.stopPropagation(); canvasStudio.removeTab('${container.id}', '${tab.id}')"
                          title="Close Tab">
                    <i class="fas fa-times"></i>
                  </button>
                `
                    : ""
                }
              </button>
            `
              )
              .join("")}
            <button class="tab-nav-item add-tab" 
                    onclick="canvasStudio.addTab('${container.id}')"
                    title="Add New Tab">
              <i class="fas fa-plus"></i>
            </button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Render layout groups within the canvas
   */
  renderLayoutGroupsInCanvas(container) {
    if (!container.layoutGroups || container.layoutGroups.length === 0)
      return "";

    return `
      <div class="layout-groups-canvas">
        ${container.layoutGroups
          .map((group) => this.renderLayoutGroupInCanvas(group))
          .join("")}
      </div>
    `;
  }

  /**
   * Render individual layout group in canvas
   */
  renderLayoutGroupInCanvas(group) {
    switch (group.type) {
      case "tabs":
        return this.renderTabGroupInCanvas(group);
      case "collapsible":
        return this.renderCollapsibleGroupInCanvas(group);
      case "grid-area":
        return this.renderGridAreaInCanvas(group);
      default:
        return "";
    }
  }

  /**
   * Render tab group in canvas
   */
  renderTabGroupInCanvas(group) {
    const tabs = group.config.tabs || [];
    const activeTabIndex = group.config.activeTab || 0;

    return `
      <div class="layout-group tab-group" data-group-id="${group.id}">
        <div class="layout-group-header">
          <h6 class="layout-group-title">${group.name}</h6>
          <div class="layout-group-actions">
            <button class="btn btn-sm btn-outline-primary" data-action="add-tab" data-group-id="${
              group.id
            }">
              <i class="fas fa-plus"></i> Add Tab
            </button>
          </div>
        </div>
        <div class="tab-navigation">
          ${tabs
            .map(
              (tab, index) => `
            <button class="tab-btn ${index === activeTabIndex ? "active" : ""}"
                    data-action="switch-group-tab"
                    data-group-id="${group.id}"
                    data-tab-index="${index}">
              ${tab.name}
              ${
                tabs.length > 1
                  ? `<i class="fas fa-times" data-action="remove-group-tab" data-group-id="${group.id}" data-tab-index="${index}"></i>`
                  : ""
              }
            </button>
          `
            )
            .join("")}
        </div>
        <div class="tab-content">
          ${tabs
            .map(
              (tab, index) => `
            <div class="tab-pane ${
              index === activeTabIndex ? "active" : ""
            }" data-tab-index="${index}">
              <div class="mini-grid-container">
                <div id="mini-grid-${group.id}-${index}" class="mini-grid" 
                     data-group-id="${group.id}" data-tab-index="${index}">
                  ${
                    tab.widgets && tab.widgets.length > 0
                      ? this.renderMiniWidgets(tab.widgets)
                      : this.renderMiniPlaceholder()
                  }
                </div>
              </div>
            </div>
          `
            )
            .join("")}
        </div>
      </div>
    `;
  }

  /**
   * Render collapsible group in canvas
   */
  renderCollapsibleGroupInCanvas(group) {
    const isCollapsed = group.config.collapsed || false;
    const widgets = group.widgets || [];

    return `
      <div class="layout-group collapsible-group" data-group-id="${group.id}">
        <div class="layout-group-header collapsible-header" 
             data-action="toggle-collapsible" data-group-id="${group.id}">
          <h6 class="layout-group-title">
            <i class="fas ${
              isCollapsed ? "fa-chevron-right" : "fa-chevron-down"
            }"></i>
            ${group.name}
          </h6>
          <div class="layout-group-meta">${widgets.length} widgets</div>
        </div>
        <div class="collapsible-content" style="display: ${
          isCollapsed ? "none" : "block"
        };">
          <div class="mini-grid-container">
            <div id="mini-grid-${group.id}" class="mini-grid" data-group-id="${
      group.id
    }">
              ${
                widgets.length > 0
                  ? this.renderMiniWidgets(widgets)
                  : this.renderMiniPlaceholder()
              }
            </div>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Render grid area in canvas
   */
  renderGridAreaInCanvas(group) {
    const widgets = group.widgets || [];
    const columns = group.config.columns || 2;

    return `
      <div class="layout-group grid-area-group" data-group-id="${group.id}">
        <div class="layout-group-header">
          <h6 class="layout-group-title">${group.name}</h6>
          <div class="layout-group-meta">${
            widgets.length
          } widgets • ${columns} columns</div>
        </div>
        <div class="grid-area-content" style="grid-template-columns: repeat(${columns}, 1fr);">
          ${
            widgets.length > 0
              ? this.renderMiniWidgets(widgets)
              : this.renderMiniPlaceholder()
          }
        </div>
      </div>
    `;
  }

  /**
   * Render mini widgets for layout groups
   */
  renderMiniWidgets(widgets) {
    return widgets
      .map(
        (widget) => `
        <div class="mini-widget" data-widget-id="${widget.id}">
          <div class="mini-widget-header">
            <i class="${this.getWidgetIcon(widget.type)}"></i>
            <span>${widget.name}</span>
          </div>
          <div class="mini-widget-actions">
            <button class="btn btn-sm btn-outline-danger" data-action="remove-widget" data-widget-id="${
              widget.id
            }">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      `
      )
      .join("");
  }

  /**
   * Render mini placeholder for empty layout groups
   */
  renderMiniPlaceholder() {
    return `
      <div class="mini-placeholder">
        <i class="fas fa-plus-circle"></i>
        <p>Click widgets from the library to add them here</p>
      </div>
    `;
  }

  /**
   * Render GridStack placeholder for empty sections
   */
  renderGridStackPlaceholder(showPlaceholder, containerType = "section") {
    if (!showPlaceholder) return "";

    return `
      <div class="grid-stack-placeholder">
        <div class="placeholder-content">
          <i class="fas fa-plus-circle"></i>
          <p>Click widgets from the library to add them to this ${containerType}</p>
          <small>Drag widgets here or click them to get started</small>
        </div>
      </div>
    `;
  }

  /**
   * Render widgets
   */
  renderWidgets(widgets) {
    const widgetsContent = document.getElementById("widgets-content");
    if (!widgetsContent) return;

    // Define category order and organization
    const categoryOrder = [
      {
        name: "Layout",
        icon: "fas fa-th-large",
        description: "Structural components",
      },
      {
        name: "Charts",
        icon: "fas fa-chart-bar",
        description: "Data visualization",
      },
      {
        name: "Data",
        icon: "fas fa-table",
        description: "Data display components",
      },
      {
        name: "Metrics",
        icon: "fas fa-tachometer-alt",
        description: "Key performance indicators",
      },
      {
        name: "Content",
        icon: "fas fa-file-text",
        description: "Text and content widgets",
      },
      {
        name: "Media",
        icon: "fas fa-image",
        description: "Images and multimedia",
      },
      {
        name: "Location",
        icon: "fas fa-map-marker-alt",
        description: "Maps and location services",
      },
    ];

    // Group widgets by category
    const categories = {};
    widgets.forEach((widget) => {
      if (!categories[widget.category]) {
        categories[widget.category] = [];
      }
      categories[widget.category].push(widget);
    });

    // Create organized HTML with horizontal grid layout
    const widgetsHTML = categoryOrder
      .filter(
        (categoryInfo) =>
          categories[categoryInfo.name] &&
          categories[categoryInfo.name].length > 0
      )
      .map((categoryInfo) => {
        const categoryWidgets = categories[categoryInfo.name];
        return `
          <div class="widget-category">
            <div class="widget-category-header">
              <div class="category-header-content">
                <i class="${categoryInfo.icon} category-icon"></i>
                <div class="category-info">
                  <div class="category-name">${categoryInfo.name}</div>
                  <div class="category-description">${
                    categoryInfo.description
                  }</div>
                </div>
                <div class="category-count">${categoryWidgets.length}</div>
              </div>
            </div>
            <div class="widget-category-content">
              <div class="widgets-grid">
                ${categoryWidgets
                  .map(
                    (widget) => `
                    <div class="widget-item widget-library-item" 
                         data-widget="${widget.type}"
                         data-widget-name="${widget.name}"
                         data-widget-category="${widget.category}"
                         data-widget-type="${widget.type}"
                         data-category="${widget.category}"
                         tabindex="0"
                         role="button"
                         aria-label="Add ${widget.name} widget">
                      <div class="widget-header">
                        <i class="${this.getWidgetIcon(
                          widget.type
                        )} widget-icon"></i>
                        <div class="widget-info">
                          <div class="widget-name">${widget.name}</div>
                          <div class="widget-description">${
                            widget.description
                          }</div>
                        </div>
                      </div>
                    </div>
                  `
                  )
                  .join("")}
              </div>
            </div>
          </div>
        `;
      })
      .join("");

    widgetsContent.innerHTML = widgetsHTML;
  }

  /**
   * Load widget library
   */
  loadWidgetLibrary() {
    this.renderWidgets(this.widgets);
    this.setupWidgetCategoryCollapse();
  }

  /**
   * Setup collapsible widget categories
   */
  setupWidgetCategoryCollapse() {
    // Add click handlers for category headers
    document.addEventListener("click", (e) => {
      if (e.target.closest(".widget-category-header")) {
        const categoryHeader = e.target.closest(".widget-category-header");
        const category = categoryHeader.closest(".widget-category");

        // Toggle collapsed state
        category.classList.toggle("collapsed");

        // Save collapsed state to localStorage
        const categoryName =
          category.querySelector(".category-name").textContent;
        const isCollapsed = category.classList.contains("collapsed");
        this.saveWidgetCategoryState(categoryName, isCollapsed);
      }
    });

    // Restore collapsed states from localStorage
    this.restoreWidgetCategoryStates();
  }

  /**
   * Save widget category collapse state
   */
  saveWidgetCategoryState(categoryName, isCollapsed) {
    const states = JSON.parse(
      localStorage.getItem("canvas-studio-widget-categories") || "{}"
    );
    states[categoryName] = isCollapsed;
    localStorage.setItem(
      "canvas-studio-widget-categories",
      JSON.stringify(states)
    );
  }

  /**
   * Restore widget category collapse states
   */
  restoreWidgetCategoryStates() {
    const states = JSON.parse(
      localStorage.getItem("canvas-studio-widget-categories") || "{}"
    );

    Object.entries(states).forEach(([categoryName, isCollapsed]) => {
      if (isCollapsed) {
        const categoryElement = Array.from(
          document.querySelectorAll(".widget-category")
        ).find((cat) => {
          const categoryNameElement = cat.querySelector(".category-name");
          return (
            categoryNameElement &&
            categoryNameElement.textContent === categoryName
          );
        });

        if (categoryElement) {
          categoryElement.classList.add("collapsed");
        }
      }
    });
  }

  /**
   * Setup GridStack drag and drop
   */
  setupGridStackDragAndDrop() {
    console.log("Setting up GridStack click-to-add functionality...");

    // Setup click events for widget library items instead of drag and drop
    document.addEventListener("click", (e) => {
      if (
        e.target.classList.contains("widget-library-item") ||
        e.target.closest(".widget-library-item")
      ) {
        const widgetItem = e.target.classList.contains("widget-library-item")
          ? e.target
          : e.target.closest(".widget-library-item");

        console.log("🎯 Widget clicked:", widgetItem.dataset.widget);
        this.handleWidgetClick(widgetItem);
      }
    });

    console.log("✅ GridStack click-to-add setup complete");
  }

  handleWidgetClick(widgetElement) {
    console.log("🚀 Widget click handler called");

    // Check if a container is selected
    if (!this.selectedContainer) {
      this.showToast("Please select a container first", "warning");
      return;
    }

    const widget = this.widgets.find(
      (w) => w.type === widgetElement.dataset.widget
    );

    const widgetData = {
      type: widgetElement.dataset.widget,
      name: widget?.name || widgetElement.dataset.widget,
      category: widget?.category || "Unknown",
      icon: this.getWidgetIcon(widgetElement.dataset.widget),
    };

    console.log("🎯 Widget data:", widgetData);

    // Add visual feedback
    widgetElement.classList.add("clicked");
    setTimeout(() => widgetElement.classList.remove("clicked"), 200);

    // Add widget to selected container
    this.addWidgetToSelectedContainer(widgetData);
  }

  addWidgetToSelectedContainer(widgetData) {
    console.log("🎯 Adding widget to selected container:", {
      widgetType: widgetData.type,
      widgetName: widgetData.name,
      selectedContainer: this.selectedContainer,
    });

    const container = this.containers.get(this.selectedContainer);
    if (!container) {
      console.error("❌ Container not found:", this.selectedContainer);
      this.showToast("Container not found", "error");
      return;
    }

    // Create widget instance
    const widgetInstance = {
      id: this.generateId(),
      type: widgetData.type,
      name: widgetData.name,
      category: widgetData.category,
      config: {},
      created: new Date().toISOString(),
    };

    // Special handling for tab-container widgets
    if (widgetData.type === "tab-container") {
      widgetInstance.tabs = [
        {
          id: this.generateId(),
          name: "Tab 1",
          widgets: [],
          active: true,
        },
      ];
      widgetInstance.tabPosition = "top";
      widgetInstance.config = {
        showTabBar: true,
        allowTabReorder: true,
        allowTabClose: true,
      };
      console.log("📋 Created tab-container widget with default tab");
    }

    console.log("✅ Created widget instance:", {
      id: widgetInstance.id,
      type: widgetInstance.type,
      name: widgetInstance.name,
    });

    // Check if user wants to add to main section (override tab context)
    // If user clicked on empty area of section container, bypass tab context
    const forceMainSection = this.shouldAddToMainSection();
    console.log("🔍 Force main section:", forceMainSection);

    // Check if there's an active tab-container widget with an active tab visible
    const activeTabContext = !forceMainSection
      ? this.getActiveTabContext()
      : null;

    console.log("🔍 Active tab context result:", activeTabContext);

    if (activeTabContext && widgetData.type !== "tab-container") {
      console.log("🎯 Adding widget to active tab context");

      // Add widget to the active tab
      const { widgetId, tabId, tab, widget, element } = activeTabContext;

      console.log("📋 Tab container widget:", {
        id: widget.id,
        type: widget.type,
        tabsCount: widget.tabs.length,
      });

      console.log("📋 Active tab:", {
        id: tab.id,
        name: tab.name,
        widgetsCount: tab.widgets.length,
        isActive: tab.active,
      });

      // Set grid position for tab context
      widgetInstance.gridPosition = this.getNextAvailablePositionInTab(tab);
      console.log("📍 Widget position in tab:", widgetInstance.gridPosition);

      // Add to tab's widgets array
      tab.widgets.push(widgetInstance);
      console.log(
        `📦 Added widget to tab data: ${tab.name} (${tab.widgets.length} widgets total)`
      );

      // Add to tab's grid - use the tab-container widget ID, not the section container ID
      console.log("🔄 Calling addWidgetToGrid with:", {
        containerId: widget.id,
        containerType: "tab",
        tabId: tab.id,
        widgetId: widgetInstance.id,
      });

      this.addWidgetToGrid(widget.id, "tab", tab.id, widgetInstance);

      // Update placeholder visibility for tab
      setTimeout(() => {
        this.updatePlaceholderVisibility(widget.id, tab.id);
      }, 100);

      this.showToast(`${widgetData.name} added to ${tab.name}`, "success");
    } else {
      console.log(
        forceMainSection
          ? "🎯 Force adding widget to main section container"
          : "🎯 Adding widget to section container (no active tab or tab-container widget)"
      );

      // Add to section container (default behavior)
      widgetInstance.gridPosition = this.getNextAvailablePosition(container);
      console.log(
        "📍 Widget position in section:",
        widgetInstance.gridPosition
      );

      container.widgets.push(widgetInstance);
      console.log(
        `📦 Added widget to section data (${container.widgets.length} widgets total)`
      );

      this.addWidgetToGrid(
        this.selectedContainer,
        "section",
        null,
        widgetInstance
      );

      // Update placeholder visibility for section
      setTimeout(() => {
        this.updatePlaceholderVisibility(this.selectedContainer, null);
      }, 100);

      this.showToast(`${widgetData.name} added successfully`, "success");
    }

    // Update container
    container.updated = new Date().toISOString();
    this.containers.set(this.selectedContainer, container);

    // Update UI
    this.renderContainers();

    // Save configuration
    if (this.config.autosave) {
      this.saveConfiguration();
    }

    console.log("✅ Widget addition process completed");
  }

  /**
   * Check if widget should be added to main section instead of tab context
   */
  shouldAddToMainSection() {
    // Check if user recently clicked on empty area of section container
    // This gives users a way to bypass tab context
    const recentMainSectionClick =
      this.lastMainSectionClick &&
      Date.now() - this.lastMainSectionClick < 2000; // 2 second window

    if (recentMainSectionClick) {
      console.log(
        "🎯 Recent main section click detected - bypassing tab context"
      );
      return true;
    }

    return false;
  }

  /**
   * Highlight section container to show where widgets will be added
   */
  highlightSectionContainer() {
    const canvasContent = document.querySelector(".canvas-content");
    if (!canvasContent) return;

    // Add highlight class
    canvasContent.classList.add("section-highlight");

    // Show toast message
    this.showToast("Widgets will be added to main section", "info");

    // Remove highlight after 2 seconds
    setTimeout(() => {
      canvasContent.classList.remove("section-highlight");
    }, 2000);
  }

  /**
   * Get the currently active tab context if any
   */
  getActiveTabContext() {
    console.log("🔍 === ACTIVE TAB CONTEXT DEBUG START ===");

    // Get all tab content elements
    const allTabContents = document.querySelectorAll(".tab-content");
    console.log(
      `🔍 Found ${allTabContents.length} total .tab-content elements in DOM`
    );

    // Log details of each tab content element
    allTabContents.forEach((tabContent, index) => {
      const computedStyle = window.getComputedStyle(tabContent);
      console.log(`🔍 Tab Content ${index + 1}:`, {
        tabId: tabContent.dataset.tabId,
        displayValue: computedStyle.display,
        hasActiveClass: tabContent.classList.contains("active"),
        element: tabContent,
      });
    });

    // First try: Look for active tab content with .active class
    let activeTabContent = document.querySelector(".tab-content.active");
    console.log("🔍 Query result for active tab content:", activeTabContent);

    if (activeTabContent) {
      const computedStyle = window.getComputedStyle(activeTabContent);
      console.log("🔍 Active tab content computed style:", {
        display: computedStyle.display,
        visibility: computedStyle.visibility,
        opacity: computedStyle.opacity,
      });
    }

    // Second try: Look for tab content with display: flex OR display: block (both are valid active states)
    if (!activeTabContent) {
      activeTabContent = Array.from(allTabContents).find((tabContent) => {
        const computedStyle = window.getComputedStyle(tabContent);
        return (
          computedStyle.display === "flex" || computedStyle.display === "block"
        );
      });
      console.log("🔍 Flex/Block active tab found:", activeTabContent);
    }

    if (activeTabContent) {
      const tabId = activeTabContent.dataset.tabId;

      // Find the widget element that contains this tab
      const widgetElement = activeTabContent.closest("[data-widget-id]");

      if (!widgetElement) {
        console.log("❌ No widget element found for active tab");
        console.log("🔍 === ACTIVE TAB CONTEXT DEBUG END ===");
        return null;
      }

      const widgetId = widgetElement.dataset.widgetId;
      console.log("🔍 Looking for widget with ID:", widgetId);

      // Debug: Show all available containers and their widgets
      console.log("🔍 Available containers:", this.containers.size);
      console.log("🔍 Current selected container:", this.selectedContainer);
      let containerIndex = 1;
      for (const [containerId, container] of this.containers) {
        console.log(`🔍 Container ${containerIndex}:`, {
          id: container.id,
          name: container.name,
          widgetCount: container.widgets ? container.widgets.length : 0,
          widgets: container.widgets
            ? container.widgets.map((w) => ({
                id: w.id,
                name: w.name,
                type: w.type,
              }))
            : [],
        });
      }

      // Also check the selected container specifically
      if (this.selectedContainer) {
        const selectedContainer = this.containers.get(this.selectedContainer);
        if (selectedContainer) {
          console.log("🔍 Selected container details:", {
            id: selectedContainer.id,
            name: selectedContainer.name,
            widgetCount: selectedContainer.widgets
              ? selectedContainer.widgets.length
              : 0,
            widgets: selectedContainer.widgets
              ? selectedContainer.widgets.map((w) => ({
                  id: w.id,
                  name: w.name,
                  type: w.type,
                }))
              : [],
          });
        }
      }

      // Find the widget in container widgets (not this.widgets)
      let widget = null;
      for (const [containerId, container] of this.containers) {
        if (container.widgets) {
          widget = container.widgets.find((w) => w.id === widgetId);
          if (widget) {
            console.log(
              "✅ Found widget in container:",
              container.id,
              "Widget:",
              { id: widget.id, name: widget.name, type: widget.type }
            );
            break;
          }
        }
      }

      if (!widget) {
        console.log("❌ No widget found in data for active tab");
        console.log("🔍 === ACTIVE TAB CONTEXT DEBUG END ===");
        return null;
      }

      const tab = widget.tabs?.find((t) => t.id === tabId);

      if (!tab) {
        console.log("❌ No tab found in widget data for active tab");
        console.log("🔍 === ACTIVE TAB CONTEXT DEBUG END ===");
        return null;
      }

      console.log("✅ Active tab context found:", {
        widgetId,
        tabId,
        tab: tab.name,
        widget: widget.name,
      });
      console.log("🔍 === ACTIVE TAB CONTEXT DEBUG END ===");

      return {
        widgetId,
        tabId,
        tab,
        widget,
        element: activeTabContent,
      };
    }

    console.log("❌ No active tab content found!");
    console.log("🔍 === ACTIVE TAB CONTEXT DEBUG END ===");
    return null;
  }

  /**
   * Get next available position within a tab
   */
  getNextAvailablePositionInTab(tab) {
    const widgets = tab.widgets || [];

    // Find next available position in grid
    const positions = widgets.map((w) => w.gridPosition).filter((p) => p);
    let x = 0,
      y = 0;

    // Simple positioning algorithm - place widgets in rows
    if (positions.length > 0) {
      const maxY = Math.max(...positions.map((p) => p.y + p.h));
      const lastRow = positions.filter(
        (p) =>
          p.y === maxY - (positions.find((pos) => pos.y === maxY - 1)?.h || 3)
      );

      if (lastRow.length > 0) {
        const maxX = Math.max(...lastRow.map((p) => p.x + p.w));
        if (maxX + 4 <= 12) {
          // If there's space in current row
          x = maxX;
          y = maxY - 3; // Same row
        } else {
          // New row
          x = 0;
          y = maxY;
        }
      } else {
        y = maxY;
      }
    }

    return { x, y, w: 4, h: 3 };
  }

  getNextAvailablePosition(container) {
    // Only section containers now - get widgets directly
    const widgets = container.widgets || [];

    // Find next available position in grid
    const positions = widgets.map((w) => w.gridPosition).filter((p) => p);
    let x = 0,
      y = 0;

    // Simple positioning algorithm - place widgets in rows
    if (positions.length > 0) {
      const maxY = Math.max(...positions.map((p) => p.y + p.h));
      const lastRow = positions.filter(
        (p) =>
          p.y === maxY - (positions.find((pos) => pos.y === maxY - 1)?.h || 3)
      );

      if (lastRow.length > 0) {
        const maxX = Math.max(...lastRow.map((p) => p.x + p.w));
        if (maxX + 4 <= 12) {
          // If there's space in current row
          x = maxX;
          y = maxY - 3; // Same row
        } else {
          // New row
          x = 0;
          y = maxY;
        }
      } else {
        y = maxY;
      }
    }

    return { x, y, w: 4, h: 3 };
  }

  addWidgetToGrid(containerId, containerType, tabId, widgetInstance) {
    const gridId = tabId ? `${containerId}-${tabId}` : containerId;

    console.log("🎯 Adding widget to grid:", {
      containerId,
      containerType,
      tabId,
      gridId,
      widgetType: widgetInstance.type,
      widgetName: widgetInstance.name,
    });

    const grid = this.gridInstances.get(gridId);

    if (!grid) {
      console.log("❌ Grid not found for ID:", gridId);
      console.log("📋 Available grids:", Array.from(this.gridInstances.keys()));

      // If it's a tab grid, try to initialize it
      if (tabId && containerType === "tab") {
        console.log("🔄 Attempting to initialize missing tab grid...");

        // Find the tab-container widget
        const container = this.containers.get(this.selectedContainer);
        if (container) {
          const tabContainerWidget = container.widgets.find(
            (w) => w.id === containerId
          );
          if (
            tabContainerWidget &&
            tabContainerWidget.type === "tab-container"
          ) {
            this.initializeTabGrids(tabContainerWidget);

            // Try again after initialization
            setTimeout(() => {
              const retryGrid = this.gridInstances.get(gridId);
              if (retryGrid) {
                console.log("✅ Grid initialized, retrying widget addition...");
                this.addWidgetToGrid(
                  containerId,
                  containerType,
                  tabId,
                  widgetInstance
                );
              } else {
                console.error(
                  "❌ Grid still not available after initialization"
                );
              }
            }, 200);
            return;
          }
        }
      }

      console.log(
        "⏳ Grid not found, widget will be added when grid is created"
      );
      return;
    }

    // Create widget content
    const widgetContent = this.createGridStackWidgetContent(widgetInstance);

    // Add to grid
    const gridOptions = {
      x: widgetInstance.gridPosition.x,
      y: widgetInstance.gridPosition.y,
      w: widgetInstance.gridPosition.w,
      h: widgetInstance.gridPosition.h,
      content: `<div class="grid-stack-item-content" data-widget-id="${widgetInstance.id}">${widgetContent}</div>`,
    };

    console.log("📍 Grid options:", gridOptions);

    try {
      const addedWidget = grid.addWidget(gridOptions);
      if (addedWidget) {
        addedWidget.dataset.widgetId = widgetInstance.id;
        console.log("✅ Widget added to grid successfully:", {
          gridId,
          widgetId: widgetInstance.id,
          position: `${gridOptions.x},${gridOptions.y}`,
          size: `${gridOptions.w}x${gridOptions.h}`,
        });

        // Verify the widget is in the DOM
        setTimeout(() => {
          const widgetElement = document.querySelector(
            `[data-widget-id="${widgetInstance.id}"]`
          );
          const containerElement = document.querySelector(
            `.grid-stack[data-gs-id="${gridId}"]`
          );
          if (widgetElement) {
            console.log("✅ Widget verified in DOM:", {
              widgetId: widgetInstance.id,
              element: widgetElement,
              visible: widgetElement.offsetParent !== null,
              containerVisible: containerElement
                ? containerElement.offsetParent !== null
                : false,
            });
            return;
          }
          console.log(
            `❌ Widget not found in DOM after addition: ${widgetInstance.id}`
          );
          console.log(`🔄 Forcing re-render of tab content`);

          // Force re-render of tab content
          const tabContentElement = document.querySelector(
            `.tab-content[data-tab-id="${tabId}"]`
          );
          if (tabContentElement) {
            tabContentElement.style.display = "none";
            setTimeout(() => {
              tabContentElement.style.display = "flex";
              // Check again after forced re-render
              setTimeout(() => {
                const widgetElementAfterReRender = document.querySelector(
                  `[data-widget-id="${widgetInstance.id}"]`
                );
                if (widgetElementAfterReRender) {
                  console.log(
                    `✅ Widget found after forced render`,
                    widgetElementAfterReRender
                  );
                } else {
                  console.log(
                    `❌ Widget still not found after forced render: ${widgetInstance.id}`
                  );
                }
              }, 300);
            }, 100);
          } else {
            console.error(
              `❌ Tab content element not found for re-render: ${tabId}`
            );
          }
        }, 500);
      } else {
        console.error("❌ Grid.addWidget returned null/undefined");
      }

      // Initialize tab grids for tab-container widgets
      if (widgetInstance.type === "tab-container") {
        console.log("🔄 Initializing grids for new tab-container widget...");
        setTimeout(() => {
          this.initializeTabGrids(widgetInstance);
        }, 100);
      }
    } catch (error) {
      console.error("❌ Failed to add widget to grid:", error);
      console.error("Grid state:", {
        gridId,
        gridExists: !!grid,
        gridElement: grid?.el,
        widgetOptions: gridOptions,
      });
    }
  }

  /**
   * Initialize GridStack instances for tab-container widget tabs
   */
  initializeTabGrids(widget) {
    if (!widget.tabs) {
      console.log("❌ No tabs found in widget:", widget.id);
      return;
    }

    console.log(
      `🔄 Initializing ${widget.tabs.length} tab grids for widget: ${widget.id}`
    );

    widget.tabs.forEach((tab, index) => {
      const tabGridId = `${widget.id}-${tab.id}`;
      const tabGridElement = document.getElementById(`grid-${tabGridId}`);

      console.log(`🔍 Tab ${index + 1}: ${tab.name} (${tab.id})`);
      console.log(`   Grid ID: ${tabGridId}`);
      console.log(`   DOM Element: ${tabGridElement ? "Found" : "Not Found"}`);
      console.log(
        `   Already initialized: ${this.gridInstances.has(tabGridId)}`
      );

      if (!tabGridElement) {
        console.log(
          `⏳ Grid element not found yet for ${tab.name}, will retry...`
        );

        // Retry after a short delay
        setTimeout(() => {
          const retryElement = document.getElementById(`grid-${tabGridId}`);
          if (retryElement && !this.gridInstances.has(tabGridId)) {
            console.log(`🔄 Retrying grid initialization for ${tab.name}...`);
            this.initializeSingleTabGrid(widget, tab, tabGridId, retryElement);
          } else if (!retryElement) {
            console.error(
              `❌ Grid element still not found for ${tab.name} after retry`
            );
          }
        }, 300);
        return;
      }

      if (this.gridInstances.has(tabGridId)) {
        console.log(`✅ Grid already initialized for ${tab.name}`);
        return;
      }

      this.initializeSingleTabGrid(widget, tab, tabGridId, tabGridElement);
    });
  }

  /**
   * Initialize a single tab grid
   */
  initializeSingleTabGrid(widget, tab, tabGridId, tabGridElement) {
    try {
      console.log(`🚀 Initializing grid for tab: ${tab.name}`);

      const tabGrid = GridStack.init(
        {
          column: 12,
          cellHeight: 60,
          margin: 10,
          disableDrag: false,
          disableResize: false,
          acceptWidgets: false, // Widgets are added via click, not drag
          draggable: {
            handle: ".widget-drag-handle",
            appendTo: "body",
            scroll: true,
            containment: false,
          },
          resizable: {
            handles: "se, sw, ne, nw",
          },
        },
        tabGridElement
      );

      if (!tabGrid) {
        console.error(`❌ GridStack.init failed for tab: ${tab.name}`);
        return;
      }

      // Store grid instance
      this.gridInstances.set(tabGridId, tabGrid);
      console.log(`✅ Tab grid initialized: ${tab.name} (${tabGridId})`);

      // Load existing widgets in this tab
      if (tab.widgets && tab.widgets.length > 0) {
        console.log(
          `📦 Loading ${tab.widgets.length} existing widgets into ${tab.name}...`
        );

        tab.widgets.forEach((tabWidget, widgetIndex) => {
          try {
            const tabWidgetContent =
              this.createGridStackWidgetContent(tabWidget);
            const tabWidgetOptions = {
              x: tabWidget.gridPosition?.x || 0,
              y: tabWidget.gridPosition?.y || 0,
              w: tabWidget.gridPosition?.w || 4,
              h: tabWidget.gridPosition?.h || 3,
              content: `<div class="grid-stack-item-content" data-widget-id="${tabWidget.id}">${tabWidgetContent}</div>`,
            };

            const addedTabWidget = tabGrid.addWidget(tabWidgetOptions);
            if (addedTabWidget) {
              addedTabWidget.dataset.widgetId = tabWidget.id;
              console.log(
                `   ✅ Widget ${widgetIndex + 1}: ${tabWidget.name} loaded`
              );
            } else {
              console.error(`   ❌ Failed to add widget: ${tabWidget.name}`);
            }
          } catch (widgetError) {
            console.error(
              `   ❌ Error loading widget ${tabWidget.name}:`,
              widgetError
            );
          }
        });
      } else {
        console.log(`📋 No existing widgets in ${tab.name}`);
      }

      // Update placeholder visibility
      setTimeout(() => {
        this.updatePlaceholderVisibility(widget.id, tab.id);
      }, 100);
    } catch (error) {
      console.error(`❌ Failed to initialize grid for tab ${tab.name}:`, error);
    }
  }

  // Remove old drag and drop methods (keep for reference but not used)
  handleWidgetDragStart(e) {
    // This method is no longer used with click-to-add
    console.log("Drag start - using click-to-add instead");
  }

  handleWidgetDragEnd(e) {
    // This method is no longer used with click-to-add
    console.log("Drag end - using click-to-add instead");
  }

  enableGridDropZones() {
    // Not needed with click-to-add
  }

  disableGridDropZones() {
    // Not needed with click-to-add
  }

  createGridStackContainer(containerId, containerType, tabId = null) {
    const gridId = tabId ? `${containerId}-${tabId}` : containerId;
    const gridElement = document.getElementById(`grid-${gridId}`);

    if (!gridElement) {
      console.error(`Grid element not found: grid-${gridId}`);
      return null;
    }

    // Check if grid is already initialized
    if (this.gridInstances.has(gridId)) {
      return this.gridInstances.get(gridId);
    }

    const gridConfig = {
      ...this.config.gridSettings[containerType],
      // Ensure dragging is explicitly enabled
      disableDrag: false,
      disableResize: false,
      draggable: {
        handle: ".widget-drag-handle",
        appendTo: "body",
        scroll: true,
        containment: false,
      },
      resizable: {
        handles: "se, sw, ne, nw",
      },
      // Custom drop handling
      acceptWidgets: (el) => {
        return this.isDragging && this.draggedWidget;
      },
    };

    try {
      const grid = GridStack.init(gridConfig, gridElement);

      // Handle widget drops
      grid.on("dropped", (event, previousWidget, newWidget) => {
        if (this.draggedWidget) {
          this.handleGridStackDrop(
            grid,
            newWidget,
            containerId,
            containerType,
            tabId
          );
        }
      });

      // Handle widget changes (position/size)
      grid.on("change", (event, items) => {
        if (items && items.length > 0) {
          this.handleGridStackChange(containerId, containerType, tabId, items);
        }
      });

      // Handle widget removal
      grid.on("removed", (event, items) => {
        if (items && items.length > 0) {
          items.forEach((item) => {
            const widgetId = item.el?.dataset?.widgetId;
            if (widgetId) {
              this.removeWidgetFromContainer(
                widgetId,
                containerId,
                containerType,
                tabId
              );
            }
          });
        }
      });

      this.gridInstances.set(gridId, grid);
      console.log(`GridStack initialized for: ${gridId}`, {
        draggable: gridConfig.draggable,
        disableDrag: gridConfig.disableDrag,
        disableResize: gridConfig.disableResize,
      });

      return grid;
    } catch (error) {
      console.error(`Failed to initialize GridStack for ${gridId}:`, error);
      return null;
    }
  }

  handleGridStackDrop(grid, newWidget, containerId, containerType, tabId) {
    console.log("🎯 GridStack drop handler called:", {
      containerId,
      containerType,
      tabId,
      draggedWidget: this.draggedWidget,
      newWidget,
    });

    if (!this.draggedWidget || !newWidget) {
      console.log("❌ Drop failed: missing draggedWidget or newWidget");
      return;
    }

    const widgetInstance = {
      id: this.generateId(),
      type: this.draggedWidget.type,
      name: this.draggedWidget.name,
      category: this.draggedWidget.category,
      config: {},
      gridPosition: {
        x: newWidget.x,
        y: newWidget.y,
        w: newWidget.w || 4,
        h: newWidget.h || 3,
      },
      created: new Date().toISOString(),
    };

    console.log("✅ Created widget instance:", widgetInstance);

    // Create widget content
    const widgetContent = this.createGridStackWidgetContent(widgetInstance);

    // Update the grid item with proper content
    if (newWidget.el) {
      newWidget.el.dataset.widgetId = widgetInstance.id;
      const contentDiv = newWidget.el.querySelector(".grid-stack-item-content");
      if (contentDiv) {
        contentDiv.innerHTML = widgetContent;
      }
    }

    // Add to container data
    this.addWidgetToContainerData(
      containerId,
      widgetInstance,
      containerType,
      tabId
    );

    // Update UI
    this.renderContainers();

    this.showToast(`${this.draggedWidget.name} added successfully`, "success");
    console.log("✅ Widget drop completed successfully");
  }

  createGridStackWidgetContent(widget) {
    const icon = this.getWidgetIcon(widget.type);

    // Handle special case for section-container widgets
    if (widget.type === "section-container") {
      return this.createSectionContainerWidgetContent(widget);
    }

    // Handle special case for tab-container widgets
    if (widget.type === "tab-container") {
      return this.createTabContainerWidgetContent(widget);
    }

    // Create proper widget structure matching the application's design
    const content = `
      <div class="card h-100 widget-card" data-widget-id="${widget.id}">
        <div class="card-header widget-header">
          <div class="widget-drag-handle" title="Drag to move widget">
            <i class="fas fa-grip-vertical"></i>
          </div>
          <div class="widget-title">
            <i class="${icon}"></i>
            <span>${widget.name}</span>
          </div>
          <div class="widget-actions">
            <button class="btn btn-sm card-btn widget-settings-btn" 
                    title="Widget Settings"
                    onclick="event.stopPropagation();">
              <i class="fas fa-cog"></i>
            </button>
            <button class="btn btn-sm card-btn widget-remove-btn" 
                    data-action="remove-widget"
                    data-widget-id="${widget.id}"
                    title="Remove Widget"
                    onclick="event.stopPropagation();">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        <div class="card-body widget-body">
          <div class="widget-content">
            ${this.createWidgetPreviewContent(widget)}
          </div>
        </div>
      </div>
    `;

    console.log(`Created widget content for ${widget.name} with drag handle`);
    return content;
  }

  /**
   * Create section container widget content with nested grid
   */
  createSectionContainerWidgetContent(widget) {
    const nestedGridId = `nested-grid-${widget.id}`;
    const widgets = widget.childWidgets || [];

    const content = `
      <div class="card h-100 widget-card section-container-widget" data-widget-id="${
        widget.id
      }">
        <div class="card-header widget-header">
          <div class="widget-drag-handle" title="Drag to move container">
            <i class="fas fa-grip-vertical"></i>
          </div>
          <div class="widget-title">
            <i class="fas fa-th-large"></i>
            <span>${widget.name || "Section Container"}</span>
            <span class="widget-count-badge">${widgets.length}</span>
          </div>
          <div class="widget-actions">
            <button class="btn btn-sm card-btn widget-settings-btn" 
                    title="Container Settings"
                    onclick="event.stopPropagation(); canvasStudio.configureSectionContainer('${
                      widget.id
                    }');">
              <i class="fas fa-cog"></i>
            </button>
            <button class="btn btn-sm card-btn widget-add-btn" 
                    title="Add Widget to Container"
                    onclick="event.stopPropagation(); canvasStudio.openWidgetLibraryForContainer('${
                      widget.id
                    }');">
              <i class="fas fa-plus"></i>
            </button>
            <button class="btn btn-sm card-btn widget-remove-btn" 
                    data-action="remove-widget"
                    data-widget-id="${widget.id}"
                    title="Remove Container"
                    onclick="event.stopPropagation();">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        <div class="card-body widget-body p-1">
          <div class="nested-container-wrapper">
            <div class="grid-stack nested-grid-stack" 
                 id="${nestedGridId}"
                 data-parent-widget="${widget.id}">
              ${widgets.length === 0 ? this.renderNestedGridPlaceholder() : ""}
            </div>
          </div>
        </div>
      </div>
    `;

    // Initialize nested grid after content is added to DOM
    setTimeout(() => {
      this.initializeNestedGrid(widget.id, nestedGridId);
    }, 100);

    return content;
  }

  /**
   * Create tab container widget content with nested tabs
   */
  createTabContainerWidgetContent(widget) {
    if (!widget.tabs) {
      widget.tabs = [
        {
          id: `tab-${Date.now()}`,
          name: "Tab 1",
          active: true,
          widgets: [],
        },
      ];
    }

    const activeTab = widget.tabs.find((tab) => tab.active) || widget.tabs[0];
    const tabGridId = `tab-grid-${widget.id}-${activeTab.id}`;

    const content = `
      <div class="card h-100 widget-card tab-container-widget" data-widget-id="${
        widget.id
      }">
        <div class="card-header widget-header">
          <div class="widget-drag-handle" title="Drag to move container">
            <i class="fas fa-grip-vertical"></i>
          </div>
          <div class="widget-title">
            <i class="fas fa-folder-open"></i>
            <span>${widget.name || "Tab Container"}</span>
          </div>
          <div class="widget-actions">
            <button class="btn btn-sm card-btn widget-settings-btn" 
                    title="Tab Settings"
                    onclick="event.stopPropagation(); canvasStudio.configureTabWidget('${
                      widget.id
                    }');">
              <i class="fas fa-cog"></i>
            </button>
            <button class="btn btn-sm card-btn widget-remove-btn" 
                    data-action="remove-widget"
                    data-widget-id="${widget.id}"
                    title="Remove Container"
                    onclick="event.stopPropagation();">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
        <div class="card-body widget-body p-1">
          <div class="nested-tab-container">
            <div class="tab-nav-mini">
              ${widget.tabs
                .map(
                  (tab) => `
                <button class="tab-nav-item-mini ${tab.active ? "active" : ""}" 
                        onclick="canvasStudio.switchTabInWidget('${
                          widget.id
                        }', '${tab.id}')">
                  ${tab.name}
                  <span class="tab-badge-mini">${tab.widgets.length}</span>
                </button>
              `
                )
                .join("")}
              <button class="tab-nav-item-mini add-tab-mini" 
                      onclick="canvasStudio.addTabToWidget('${widget.id}')"
                      title="Add Tab">
                <i class="fas fa-plus"></i>
              </button>
            </div>
            <div class="tab-content-mini">
              <div class="grid-stack nested-grid-stack" 
                   id="${tabGridId}"
                   data-parent-widget="${widget.id}"
                   data-tab-id="${activeTab.id}">
                ${
                  activeTab.widgets.length === 0
                    ? this.renderNestedGridPlaceholder()
                    : ""
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    // Initialize nested grid after content is added to DOM
    setTimeout(() => {
      this.initializeNestedTabGrid(widget.id, activeTab.id, tabGridId);
    }, 100);

    return content;
  }

  /**
   * Render placeholder for nested grids
   */
  renderNestedGridPlaceholder() {
    return `
      <div class="nested-grid-placeholder">
        <div class="placeholder-content-mini">
          <i class="fas fa-plus-circle"></i>
          <p>Drop widgets here</p>
        </div>
      </div>
    `;
  }

  createWidgetPreviewContent(widget) {
    const icon = this.getWidgetIcon(widget.type);

    switch (widget.type) {
      case "bar-chart":
        return `
          <div class="chart-preview">
            <div class="chart-placeholder">
              <i class="${icon}" style="font-size: 2.5rem; color: var(--ocean-teal); opacity: 0.6;"></i>
              <p class="text-muted mt-2 mb-0">Bar Chart</p>
              <small class="text-muted">Interactive bar chart visualization</small>
            </div>
          </div>
        `;
      case "pie-chart":
        return `
          <div class="chart-preview">
            <div class="chart-placeholder">
              <i class="${icon}" style="font-size: 2.5rem; color: var(--forest-green); opacity: 0.6;"></i>
              <p class="text-muted mt-2 mb-0">Pie Chart</p>
              <small class="text-muted">Circular data visualization</small>
            </div>
          </div>
        `;
      case "line-chart":
        return `
          <div class="chart-preview">
            <div class="chart-placeholder">
              <i class="${icon}" style="font-size: 2.5rem; color: var(--denali-blue); opacity: 0.6;"></i>
              <p class="text-muted mt-2 mb-0">Line Chart</p>
              <small class="text-muted">Trend analysis visualization</small>
            </div>
          </div>
        `;
      case "area-chart":
        return `
          <div class="chart-preview">
            <div class="chart-placeholder">
              <i class="${icon}" style="font-size: 2.5rem; color: var(--emerald-green); opacity: 0.6;"></i>
              <p class="text-muted mt-2 mb-0">Area Chart</p>
              <small class="text-muted">Filled area visualization</small>
            </div>
          </div>
        `;
      case "data-table":
        return `
          <div class="table-preview">
            <div class="table-placeholder">
              <i class="${icon}" style="font-size: 2.5rem; color: var(--slate-grey); opacity: 0.6;"></i>
              <p class="text-muted mt-2 mb-0">Data Table</p>
              <small class="text-muted">Structured data display</small>
            </div>
          </div>
        `;
      case "kpi-widget":
        return `
          <div class="kpi-preview">
            <div class="kpi-content">
              <div class="kpi-main">
                <div class="kpi-icon">
                  <i class="${icon}"></i>
                </div>
                <div class="kpi-info">
                  <div class="kpi-value">1,234</div>
                  <div class="kpi-label">Sample KPI</div>
                </div>
              </div>
              <div class="kpi-trend">
                <span class="trend-positive">
                  <i class="fas fa-arrow-up"></i> 12.5%
                </span>
              </div>
            </div>
          </div>
        `;
      case "text-widget":
        return `
          <div class="text-preview">
            <div class="text-placeholder">
              <i class="${icon}" style="font-size: 2.5rem; color: var(--ocean-teal); opacity: 0.6;"></i>
              <p class="text-muted mt-2 mb-0">Text Widget</p>
              <small class="text-muted">Rich text content display</small>
            </div>
          </div>
        `;
      case "image-widget":
        return `
          <div class="image-preview">
            <div class="image-placeholder">
              <i class="${icon}" style="font-size: 2.5rem; color: var(--forest-green); opacity: 0.6;"></i>
              <p class="text-muted mt-2 mb-0">Image Widget</p>
              <small class="text-muted">Image display component</small>
            </div>
          </div>
        `;
      case "video-widget":
        return `
          <div class="video-preview">
            <div class="video-placeholder">
              <i class="${icon}" style="font-size: 2.5rem; color: var(--denali-blue); opacity: 0.6;"></i>
              <p class="text-muted mt-2 mb-0">Video Widget</p>
              <small class="text-muted">Video content player</small>
            </div>
          </div>
        `;
      case "map-widget":
        return `
          <div class="map-preview">
            <div class="map-placeholder">
              <i class="${icon}" style="font-size: 2.5rem; color: var(--emerald-green); opacity: 0.6;"></i>
              <p class="text-muted mt-2 mb-0">Map Widget</p>
              <small class="text-muted">Interactive map component</small>
            </div>
          </div>
        `;
      case "tab-container":
        // Get tabs from widget data (use default if not present)
        const tabs = widget.tabs || [
          { id: this.generateId(), name: "Tab 1", widgets: [], active: true },
        ];

        // Ensure at least one tab is marked as active, preferring the first tab
        const hasActiveTab = tabs.some((tab) => tab.active);
        if (!hasActiveTab && tabs.length > 0) {
          tabs[0].active = true;
        }

        // Ensure only one tab is active
        let foundActive = false;
        tabs.forEach((tab) => {
          if (tab.active && !foundActive) {
            foundActive = true;
          } else {
            tab.active = false;
          }
        });

        const activeTab = tabs.find((tab) => tab.active) || tabs[0];
        const tabPosition = widget.tabPosition || "top";

        return `
          <div class="tab-container-widget" data-widget-id="${
            widget.id
          }" data-tab-position="${tabPosition}">
            ${
              tabPosition === "top"
                ? this.renderTabContainerNavigation(widget, tabs, activeTab)
                : ""
            }
            
            <div class="tab-content-area">
              ${tabs
                .map(
                  (tab) => `
                <div class="tab-content ${
                  tab.active ? "active" : ""
                }" data-tab-id="${tab.id}" style="display: ${
                    tab.active ? "block" : "none"
                  };">
                  <div class="tab-content-inner">
                    <div class="grid-stack tab-grid" id="grid-${widget.id}-${
                    tab.id
                  }">
                      ${this.renderGridStackPlaceholder(
                        (tab.widgets || []).length === 0,
                        "tab"
                      )}
                    </div>
                  </div>
                </div>
              `
                )
                .join("")}
            </div>
            
            ${
              tabPosition === "bottom"
                ? this.renderTabContainerNavigation(widget, tabs, activeTab)
                : ""
            }
            
            <div class="tab-widget-controls mt-2">
              <button class="btn btn-sm btn-outline-primary" 
                      onclick="canvasStudio.configureTabWidget('${widget.id}')"
                      title="Configure Tabs">
                <i class="fas fa-cog"></i> Configure
              </button>
              <button class="btn btn-sm btn-outline-secondary" 
                      onclick="canvasStudio.addTabToWidget('${widget.id}')"
                      title="Add New Tab">
                <i class="fas fa-plus"></i> Add Tab
              </button>
            </div>
          </div>
        `;
      default:
        return `
          <div class="widget-preview">
            <div class="widget-placeholder">
              <i class="${icon}" style="font-size: 2.5rem; color: var(--ocean-teal); opacity: 0.6;"></i>
              <p class="text-muted mt-2 mb-0">${widget.name}</p>
              <small class="text-muted">${widget.category}</small>
            </div>
          </div>
        `;
    }
  }

  handleGridStackChange(containerId, containerType, tabId, items) {
    const container = this.containers.get(containerId);
    if (!container) return;

    items.forEach((item) => {
      const widgetId = item.el?.dataset?.widgetId;
      if (!widgetId) return;

      const updatePosition = (widgets) => {
        const widget = widgets.find((w) => w.id === widgetId);
        if (widget) {
          widget.gridPosition = {
            x: item.x,
            y: item.y,
            w: item.w,
            h: item.h,
          };
        }
      };

      if (containerType === "section") {
        updatePosition(container.widgets);
      } else if (containerType === "tab" && tabId) {
        const tab = container.tabs.find((t) => t.id === tabId);
        if (tab) {
          updatePosition(tab.widgets);
        }
      }
    });

    container.updated = new Date().toISOString();
    this.containers.set(containerId, container);

    if (this.config.autosave) {
      this.saveConfiguration();
    }
  }

  addWidgetToContainerData(containerId, widget, containerType, tabId = null) {
    const container = this.containers.get(containerId);
    if (!container) return;

    if (containerType === "section") {
      container.widgets.push(widget);
    } else if (containerType === "tab" && tabId) {
      const tab = container.tabs.find((t) => t.id === tabId);
      if (tab) {
        tab.widgets.push(widget);
      }
    }

    container.updated = new Date().toISOString();
    this.containers.set(containerId, container);

    if (this.config.autosave) {
      this.saveConfiguration();
    }
  }

  removeWidgetFromContainer(
    widgetId,
    containerId,
    containerType,
    tabId = null
  ) {
    const container = this.containers.get(containerId);
    if (!container) return;

    let removed = false;

    if (containerType === "section") {
      const index = container.widgets.findIndex((w) => w.id === widgetId);
      if (index !== -1) {
        container.widgets.splice(index, 1);
        removed = true;
      }
    } else if (containerType === "tab" && tabId) {
      const tab = container.tabs.find((t) => t.id === tabId);
      if (tab) {
        const index = tab.widgets.findIndex((w) => w.id === widgetId);
        if (index !== -1) {
          tab.widgets.splice(index, 1);
          removed = true;
        }
      }
    }

    if (removed) {
      container.updated = new Date().toISOString();
      this.containers.set(containerId, container);
      this.renderContainers();

      // Update placeholder visibility after widget removal
      if (containerType === "section") {
        setTimeout(() => {
          this.updatePlaceholderVisibility(containerId, null);
        }, 100);
      } else if (containerType === "tab" && tabId) {
        setTimeout(() => {
          this.updatePlaceholderVisibility(containerId, tabId);
        }, 100);
      }

      if (this.config.autosave) {
        this.saveConfiguration();
      }
    }
  }

  /**
   * Load widgets into GridStack
   */
  loadWidgetsIntoGrid(containerId, containerType, tabId = null) {
    const container = this.containers.get(containerId);
    if (!container) return;

    const gridId = tabId ? `${containerId}-${tabId}` : containerId;
    const grid = this.gridInstances.get(gridId);
    if (!grid) return;

    let widgets = [];
    if (containerType === "section") {
      widgets = container.widgets;
    } else if (containerType === "tab" && tabId) {
      const tab = container.tabs.find((t) => t.id === tabId);
      widgets = tab ? tab.widgets : [];
    }

    // Clear existing widgets
    grid.removeAll(false);

    // Add widgets to grid
    widgets.forEach((widget) => {
      const widgetContent = this.createGridStackWidgetContent(widget);
      const gridOptions = {
        x: widget.gridPosition?.x || 0,
        y: widget.gridPosition?.y || 0,
        w: widget.gridPosition?.w || 4,
        h: widget.gridPosition?.h || 3,
        content: widgetContent, // Remove the extra wrapper div
      };

      try {
        const addedWidget = grid.addWidget(gridOptions);
        if (addedWidget) {
          // Set the widget ID on the grid item
          addedWidget.dataset.widgetId = widget.id;
        }
      } catch (error) {
        console.error("Failed to add widget to grid:", error);
      }
    });

    // Verify drag functionality after loading
    this.verifyDragFunctionality(gridId);
  }

  /**
   * Verify and ensure drag functionality is working
   */
  verifyDragFunctionality(gridId) {
    const grid = this.gridInstances.get(gridId);
    if (!grid) return;

    const gridElement = document.getElementById(`grid-${gridId}`);
    if (!gridElement) return;

    const gridItems = gridElement.querySelectorAll(".grid-stack-item");
    console.log(
      `Verifying drag functionality for ${gridItems.length} items in grid ${gridId}`
    );

    gridItems.forEach((item, index) => {
      const dragHandle = item.querySelector(".widget-drag-handle");
      const isDisabled = grid.opts.disableDrag;

      console.log(`Grid item ${index}:`, {
        hasDragHandle: !!dragHandle,
        isDragDisabled: isDisabled,
        hasUiDraggable: item.classList.contains("ui-draggable"),
        dragHandleVisible: dragHandle
          ? window.getComputedStyle(dragHandle).display !== "none"
          : false,
      });

      // Force enable dragging if it's not working
      if (!item.classList.contains("ui-draggable") && !isDisabled) {
        console.log(`Attempting to enable dragging for item ${index}`);
        try {
          // Try to manually enable dragging
          if (window.jQuery && window.jQuery(item).draggable) {
            window.jQuery(item).draggable({
              handle: ".widget-drag-handle",
              containment: "parent",
              grid: [
                grid.opts.cellHeight + grid.opts.margin,
                grid.opts.cellHeight + grid.opts.margin,
              ],
            });
          }
        } catch (error) {
          console.error("Failed to manually enable dragging:", error);
        }
      }
    });
  }

  /**
   * Select container
   */
  selectContainer(containerId) {
    this.selectedContainer = containerId;
    this.renderContainers();
    this.renderLayoutGroups();
    this.renderCanvas();
    console.log(`Container selected: ${containerId}`);
  }

  /**
   * Delete container
   */
  deleteContainer(containerId) {
    // Clean up all associated grids
    const container = this.containers.get(containerId);
    if (container) {
      if (container.type === "section") {
        const grid = this.gridInstances.get(containerId);
        if (grid) {
          grid.destroy(false);
          this.gridInstances.delete(containerId);
        }
      } else if (container.type === "tabs") {
        container.tabs.forEach((tab) => {
          const gridId = `${containerId}-${tab.id}`;
          const grid = this.gridInstances.get(gridId);
          if (grid) {
            grid.destroy(false);
            this.gridInstances.delete(gridId);
          }
        });
      }
    }

    if (this.containers.delete(containerId)) {
      if (this.selectedContainer === containerId) {
        this.selectedContainer = null;
      }
      this.renderContainers();
      this.renderCanvas();
      this.showToast("Container deleted", "info");
    }
  }

  /**
   * Show clear all confirmation modal
   */
  showClearAllConfirmation() {
    if (this.containers.size === 0) {
      this.showToast("No containers to clear", "info");
      return;
    }

    // Create a custom confirmation modal
    const confirmationHTML = `
      <div class="modal fade" id="clear-all-confirmation-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                Clear All Containers
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Warning:</strong> This action cannot be undone!
              </div>
              <p>You are about to delete <strong>${
                this.containers.size
              } container(s)</strong> and all their widgets:</p>
              <ul class="list-unstyled mb-3">
                ${Array.from(this.containers.values())
                  .map(
                    (container) => `
                  <li class="mb-2">
                    <i class="fas ${
                      container.type === "section"
                        ? "fa-layer-group"
                        : "fa-folder"
                    } me-2"></i>
                    <strong>${container.name}</strong>
                    <small class="text-muted">
                      (${
                        container.type === "section"
                          ? container.widgets.length
                          : (container.tabs || []).reduce(
                              (sum, tab) => sum + tab.widgets.length,
                              0
                            )
                      } widgets)
                    </small>
                  </li>
                `
                  )
                  .join("")}
              </ul>
              <p class="mb-0">Are you sure you want to proceed?</p>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                <i class="fas fa-times me-1"></i> Cancel
              </button>
              <button type="button" class="btn btn-danger" id="confirm-clear-all">
                <i class="fas fa-trash-alt me-1"></i> Clear All
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing confirmation modal if present
    const existingModal = document.getElementById(
      "clear-all-confirmation-modal"
    );
    if (existingModal) {
      existingModal.remove();
    }

    // Add confirmation modal to DOM
    document.body.insertAdjacentHTML("beforeend", confirmationHTML);

    // Show the modal
    const modal = document.getElementById("clear-all-confirmation-modal");
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Handle confirmation - Fixed button ID
    const confirmButton = document.getElementById("confirm-clear-all");
    if (confirmButton) {
      confirmButton.addEventListener("click", () => {
        this.clearAllContainers();
        bsModal.hide();
      });
    }

    // Clean up modal after hiding
    modal.addEventListener("hidden.bs.modal", () => {
      modal.remove();
    });
  }

  /**
   * Clear all containers and their data
   */
  clearAllContainers() {
    try {
      console.log("🗑️ Clearing all containers...");

      // Clean up all GridStack instances
      this.gridInstances.forEach((grid, gridId) => {
        try {
          grid.destroy(false);
          console.log(`✅ Destroyed grid: ${gridId}`);
        } catch (error) {
          console.error(`❌ Error destroying grid ${gridId}:`, error);
        }
      });

      // Clear all data
      this.gridInstances.clear();
      this.containers.clear();
      this.selectedContainer = null;

      // Update UI
      this.renderContainers();
      this.renderCanvas();

      // Save the cleared state
      this.saveConfiguration();

      // Show success message
      this.showToast("All containers cleared successfully", "success");
      console.log("✅ All containers cleared successfully");
    } catch (error) {
      console.error("❌ Error clearing containers:", error);
      this.showToast("Error clearing containers", "error");
    }
  }

  /**
   * Update container name
   */
  updateContainerName(containerId, newName) {
    const container = this.containers.get(containerId);
    if (container) {
      container.name = newName;
      container.updated = new Date().toISOString();
      this.containers.set(containerId, container);
      this.renderContainers();
      this.renderCanvas();
      this.saveConfiguration();
    }
  }

  /**
   * Add tab to container
   */
  addTab(containerId) {
    const container = this.containers.get(containerId);
    if (container && container.type === "tab") {
      const newTab = {
        id: this.generateId(),
        name: `Tab ${container.tabs.length + 1}`,
        widgets: [],
        active: false,
      };

      // Set all existing tabs to inactive
      container.tabs.forEach((tab) => (tab.active = false));
      // Set new tab as active
      newTab.active = true;

      container.tabs.push(newTab);
      container.updated = new Date().toISOString();
      this.containers.set(containerId, container);

      this.renderCanvas();
      this.renderContainers();
      this.saveConfiguration();
      this.showToast("New tab added", "success");
    }
  }

  /**
   * Switch tab
   */
  switchTab(containerId, tabId) {
    const container = this.containers.get(containerId);
    if (container && container.type === "tab") {
      // Set all tabs to inactive
      container.tabs.forEach((tab) => (tab.active = false));

      // Set selected tab to active
      const selectedTab = container.tabs.find((tab) => tab.id === tabId);
      if (selectedTab) {
        selectedTab.active = true;
        this.containers.set(containerId, container);
        this.renderCanvas();

        // Update placeholder visibility for the new active tab
        this.updatePlaceholderVisibility(containerId, tabId);
      }
    }
  }

  /**
   * Remove tab
   */
  removeTab(containerId, tabId) {
    const container = this.containers.get(containerId);
    if (container && container.type === "tab" && container.tabs.length > 1) {
      const tabIndex = container.tabs.findIndex((tab) => tab.id === tabId);
      if (tabIndex !== -1) {
        const wasActive = container.tabs[tabIndex].active;
        container.tabs.splice(tabIndex, 1);

        // If the removed tab was active, make the first tab active
        if (wasActive && container.tabs.length > 0) {
          container.tabs[0].active = true;
        }

        container.updated = new Date().toISOString();
        this.containers.set(containerId, container);

        this.renderCanvas();
        this.renderContainers();
        this.saveConfiguration();
        this.showToast("Tab removed", "info");
      }
    }
  }

  /**
   * Filter widgets
   */
  filterWidgets(searchTerm) {
    const filteredWidgets = searchTerm
      ? this.widgets.filter(
          (widget) =>
            widget.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            widget.description
              .toLowerCase()
              .includes(searchTerm.toLowerCase()) ||
            widget.category.toLowerCase().includes(searchTerm.toLowerCase())
        )
      : this.widgets;

    this.renderWidgets(filteredWidgets);
  }

  /**
   * Edit container name
   */
  editContainerName(nameElement) {
    const currentName = nameElement.textContent;
    const input = document.createElement("input");
    input.type = "text";
    input.value = currentName;
    input.className = "form-control form-control-sm";

    nameElement.replaceWith(input);
    input.focus();
    input.select();

    const saveEdit = () => {
      const newName = input.value.trim() || currentName;
      const newNameElement = document.createElement("div");
      newNameElement.className = "container-name";
      newNameElement.textContent = newName;
      input.replaceWith(newNameElement);

      // Find container ID and update
      const containerItem = newNameElement.closest(".container-item");
      if (containerItem) {
        const containerId = containerItem.dataset.containerId;
        this.updateContainerName(containerId, newName);
      }
    };

    input.addEventListener("blur", saveEdit);
    input.addEventListener("keydown", (e) => {
      if (e.key === "Enter") {
        saveEdit();
      } else if (e.key === "Escape") {
        const nameElement = document.createElement("div");
        nameElement.className = "container-name";
        nameElement.textContent = currentName;
        input.replaceWith(nameElement);
      }
    });
  }

  /**
   * Setup autosave
   */
  setupAutosave() {
    if (this.config.autosave && !this.autosaveInterval) {
      this.autosaveInterval = setInterval(() => {
        if (this.isInitialized) {
          this.saveConfiguration();
        }
      }, this.config.autosaveInterval);

      this.intervals.add(this.autosaveInterval);
      console.log(
        "✅ Autosave enabled with interval:",
        this.config.autosaveInterval
      );
    }
  }

  /**
   * Setup keyboard shortcuts
   */
  setupKeyboardShortcuts() {
    if (this.config.enableKeyboardShortcuts) {
      document.addEventListener(
        "keydown",
        this.handleKeyboardShortcuts.bind(this)
      );
    }
  }

  /**
   * Handle keyboard shortcuts
   */
  handleKeyboardShortcuts(e) {
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case "s":
          e.preventDefault();
          this.saveConfiguration();
          this.showToast("Configuration saved", "success");
          break;
        case "k": // ✅ NEW: Ctrl/Cmd + K to open Canvas Studio
          e.preventDefault();
          console.log("🎨 Canvas Studio opened via keyboard shortcut");
          this.showCanvasStudio();
          break;
      }
    }
  }

  /**
   * Save configuration
   */
  saveConfiguration() {
    try {
      const containersArray = Array.from(this.containers.entries());
      localStorage.setItem(
        "canvas-studio-containers",
        JSON.stringify(containersArray)
      );
      console.log("Configuration saved");
    } catch (error) {
      console.error("Failed to save configuration:", error);
      this.showToast("Failed to save configuration", "error");
    }
  }

  /**
   * Get widget name
   */
  getWidgetName(widgetType) {
    const widget = this.widgets.find((w) => w.type === widgetType);
    return widget ? widget.name : widgetType;
  }

  /**
   * Get widget icon
   */
  getWidgetIcon(widgetType) {
    const icons = {
      // Layout widgets
      "section-container": "fas fa-th-large",
      "tab-container": "fas fa-folder-open",
      "grid-layout": "fas fa-th",

      // Chart widgets
      "bar-chart": "fas fa-chart-bar",
      "pie-chart": "fas fa-chart-pie",
      "line-chart": "fas fa-chart-line",
      "area-chart": "fas fa-chart-area",

      // Data widgets
      table: "fas fa-table",
      kpi: "fas fa-tachometer-alt",

      // Content widgets
      text: "fas fa-font",
      image: "fas fa-image",
      video: "fas fa-video",
      map: "fas fa-map-marked-alt",
    };
    return icons[widgetType] || "fas fa-square";
  }

  /**
   * Generate unique ID
   */
  generateId() {
    return "cs_" + Math.random().toString(36).substr(2, 9) + "_" + Date.now();
  }

  /**
   * Show toast notification
   */
  showToast(message, type = "info") {
    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector(".toast-container");
    if (!toastContainer) {
      toastContainer = document.createElement("div");
      toastContainer.className =
        "toast-container position-fixed top-0 end-0 p-3";
      toastContainer.style.zIndex = "9999";
      document.body.appendChild(toastContainer);
    }

    // Create toast
    const toastId = "toast-" + Date.now();
    const toast = document.createElement("div");
    toast.id = toastId;
    toast.className = `toast toast-${type}`;
    toast.setAttribute("role", "alert");
    toast.innerHTML = `
      <div class="toast-header">
        <i class="fas ${
          type === "success"
            ? "fa-check-circle text-success"
            : type === "error"
            ? "fa-exclamation-circle text-danger"
            : "fa-info-circle text-info"
        }"></i>
        <strong class="me-auto ms-2">Canvas Studio</strong>
        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
      </div>
      <div class="toast-body">${message}</div>
    `;

    toastContainer.appendChild(toast);

    // Show toast
    const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
    bsToast.show();

    // Remove toast after it's hidden
    toast.addEventListener("hidden.bs.toast", () => {
      toast.remove();
    });
  }

  /**
   * ✅ NEW: Reinitialize Canvas Studio button if it becomes unresponsive
   */
  reinitializeButton() {
    console.log("🔄 Reinitializing Canvas Studio button...");

    // Remove existing button
    const existingButton = document.getElementById("canvas-studio-btn");
    if (existingButton) {
      existingButton.remove();
    }

    // Recreate button with fresh event listeners
    this.addHeaderButton();
    console.log("✅ Canvas Studio button reinitialized");
    this.showToast("Canvas Studio button reinitialized", "info");
  }

  /**
   * ✅ NEW: Check if Canvas Studio button is responsive
   */
  checkButtonHealth() {
    const button = document.getElementById("canvas-studio-btn");
    if (!button) {
      console.warn("⚠️ Canvas Studio button not found - reinitializing");
      this.reinitializeButton();
      return false;
    }

    // Check if button has event listeners
    const hasListeners = button.onclick || button.addEventListener;
    if (!hasListeners) {
      console.warn(
        "⚠️ Canvas Studio button has no event listeners - reinitializing"
      );
      this.reinitializeButton();
      return false;
    }

    return true;
  }

  /**
   * ✅ NEW: Setup periodic health check for Canvas Studio button
   */
  setupButtonHealthCheck() {
    // Check button health every 2 minutes (reduced from 30 seconds)
    if (!this.healthCheckInterval) {
      this.healthCheckInterval = setInterval(() => {
        if (this.isInitialized) {
          this.checkButtonHealth();
        }
      }, 120000); // 2 minutes

      this.intervals.add(this.healthCheckInterval);
      console.log(
        "✅ Canvas Studio button health check enabled (2 minute interval)"
      );
    }
  }

  /**
   * Update placeholder visibility
   */
  updatePlaceholderVisibility(containerId, tabId) {
    const container = this.containers.get(containerId);
    if (!container) return;

    console.log(
      `🔍 Updating placeholder visibility for container: ${containerId}, tabId: ${tabId}`
    );

    if (container.type === "section") {
      const gridId = containerId;
      const gridElement = document.getElementById(`grid-${gridId}`);
      if (gridElement) {
        const placeholder = gridElement.querySelector(
          ".grid-stack-placeholder"
        );
        const widgets = gridElement.querySelectorAll(".grid-stack-item");

        if (placeholder) {
          if (widgets.length === 0) {
            placeholder.style.display = "block";
            console.log(`📋 Showing placeholder for section: ${containerId}`);
          } else {
            placeholder.style.display = "none";
            console.log(
              `📋 Hiding placeholder for section: ${containerId} (${widgets.length} widgets)`
            );
          }
        }
      }
    } else if (container.type === "tab" && tabId) {
      const tab = container.tabs.find((t) => t.id === tabId);
      if (tab) {
        const gridId = `${containerId}-${tabId}`;
        const gridElement = document.getElementById(`grid-${gridId}`);
        if (gridElement) {
          const placeholder = gridElement.querySelector(
            ".grid-stack-placeholder"
          );
          const widgets = gridElement.querySelectorAll(".grid-stack-item");

          if (placeholder) {
            if (widgets.length === 0) {
              placeholder.style.display = "block";
              console.log(`📋 Showing placeholder for tab: ${tab.name}`);
            } else {
              placeholder.style.display = "none";
              console.log(
                `📋 Hiding placeholder for tab: ${tab.name} (${widgets.length} widgets)`
              );
            }
          }
        }
      }
    } else {
      // Handle tab-container widgets (when containerId is a tab-container widget ID)
      const tabContainerWidget = this.findTabContainerWidget(containerId);
      if (tabContainerWidget && tabId) {
        const tab = tabContainerWidget.tabs.find((t) => t.id === tabId);
        if (tab) {
          const gridId = `${containerId}-${tabId}`;
          const gridElement = document.getElementById(`grid-${gridId}`);
          if (gridElement) {
            const placeholder = gridElement.querySelector(
              ".grid-stack-placeholder"
            );
            const widgets = gridElement.querySelectorAll(".grid-stack-item");

            if (placeholder) {
              // Check both DOM widgets and data widgets
              const dataWidgetCount = (tab.widgets || []).length;
              const domWidgetCount = widgets.length;

              console.log(
                `📋 Tab ${tab.name}: ${dataWidgetCount} data widgets, ${domWidgetCount} DOM widgets`
              );

              if (domWidgetCount === 0 && dataWidgetCount === 0) {
                placeholder.style.display = "block";
                console.log(
                  `📋 Showing placeholder for tab-container tab: ${tab.name}`
                );
              } else {
                placeholder.style.display = "none";
                console.log(
                  `📋 Hiding placeholder for tab-container tab: ${tab.name}`
                );
              }
            }
          }
        }
      }
    }
  }

  /**
   * Find a tab-container widget by ID across all containers
   */
  findTabContainerWidget(widgetId) {
    for (const [containerId, container] of this.containers) {
      const widget = container.widgets.find(
        (w) => w.id === widgetId && w.type === "tab-container"
      );
      if (widget) {
        return widget;
      }
    }
    return null;
  }

  /**
   * Manage tab grid visibility
   */
  manageTabGridVisibility(containerId) {
    const container = this.containers.get(containerId);
    if (!container || container.type !== "tab") return;

    const activeTabIndex = container.activeTab || 0;
    const activeTab = container.tabs[activeTabIndex];

    // Hide all tab grids first
    container.tabs.forEach((tab, index) => {
      const gridElement = document.getElementById(
        `grid-${containerId}-${tab.id}`
      );
      if (gridElement) {
        const gridContainer = gridElement.closest(".grid-stack-container");
        if (gridContainer) {
          if (index === activeTabIndex) {
            gridContainer.style.display = "block";
            // Ensure GridStack is initialized for active tab
            this.createGridStackContainer(containerId, "tab", tab.id);
            this.loadWidgetsIntoGrid(containerId, "tab", tab.id);
            // Update placeholder visibility
            setTimeout(() => {
              this.updatePlaceholderVisibility(containerId, tab.id);
            }, 100);
          } else {
            gridContainer.style.display = "none";
          }
        }
      }
    });
  }

  /**
   * Add layout group to selected container
   */
  addLayoutGroup(groupType) {
    if (!this.selectedContainer) {
      this.showToast("Please select a section first", "warning");
      return;
    }

    const container = this.containers.get(this.selectedContainer);
    if (!container) return;

    const groupId = this.generateId();
    const layoutGroup = {
      id: groupId,
      type: groupType, // 'tabs', 'collapsible', 'grid-area'
      name: this.getLayoutGroupName(groupType),
      widgets: [],
      config: this.getDefaultLayoutGroupConfig(groupType),
      created: new Date().toISOString(),
    };

    container.layoutGroups.push(layoutGroup);
    container.updated = new Date().toISOString();
    this.containers.set(this.selectedContainer, container);

    this.renderLayoutGroups();
    this.renderCanvas();
    this.saveConfiguration();

    this.showToast(`${layoutGroup.name} added`, "success");
  }

  /**
   * Get default name for layout group type
   */
  getLayoutGroupName(groupType) {
    switch (groupType) {
      case "tabs":
        return "Tab Group";
      case "collapsible":
        return "Collapsible Group";
      case "grid-area":
        return "Grid Area";
      default:
        return "Layout Group";
    }
  }

  /**
   * Get default configuration for layout group type
   */
  getDefaultLayoutGroupConfig(groupType) {
    switch (groupType) {
      case "tabs":
        return {
          tabs: [{ id: this.generateId(), name: "Tab 1", widgets: [] }],
          activeTab: 0,
        };
      case "collapsible":
        return {
          collapsed: false,
          allowToggle: true,
        };
      case "grid-area":
        return {
          gridArea: { x: 0, y: 0, w: 6, h: 4 },
          columns: 2,
        };
      default:
        return {};
    }
  }

  /**
   * Render layout groups in sidebar
   */
  renderLayoutGroups() {
    const layoutGroupsSection = document.getElementById(
      "layout-groups-section"
    );
    const layoutGroupsList = document.getElementById("layout-groups-list");

    if (!layoutGroupsSection || !layoutGroupsList) return;

    if (!this.selectedContainer) {
      layoutGroupsSection.style.display = "none";
      return;
    }

    const container = this.containers.get(this.selectedContainer);
    if (
      !container ||
      !container.layoutGroups ||
      container.layoutGroups.length === 0
    ) {
      layoutGroupsSection.style.display = "none";
      return;
    }

    layoutGroupsSection.style.display = "block";

    const groupsHTML = container.layoutGroups
      .map((group) => {
        const widgetCount = group.widgets ? group.widgets.length : 0;
        const icon = this.getLayoutGroupIcon(group.type);

        return `
          <div class="layout-group-item" data-group-id="${group.id}">
            <div class="layout-group-header">
              <div class="layout-group-icon">
                <i class="fas ${icon}"></i>
              </div>
              <div class="layout-group-info">
                <div class="layout-group-name">${group.name}</div>
                <div class="layout-group-meta">${widgetCount} widgets</div>
              </div>
            </div>
            <div class="layout-group-actions">
              <button class="btn btn-sm btn-outline-danger" 
                      data-action="delete-layout-group" 
                      data-group-id="${group.id}"
                      title="Delete group">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
        `;
      })
      .join("");

    layoutGroupsList.innerHTML = groupsHTML;
  }

  /**
   * Get icon for layout group type
   */
  getLayoutGroupIcon(groupType) {
    switch (groupType) {
      case "tabs":
        return "fa-folder";
      case "collapsible":
        return "fa-chevron-down";
      case "grid-area":
        return "fa-th";
      default:
        return "fa-layer-group";
    }
  }

  /**
   * Cleanup function
   */
  cleanup() {
    console.log("🧹 Cleaning up Canvas Studio...");

    // Clear all intervals
    this.intervals.forEach((interval) => {
      clearInterval(interval);
    });
    this.intervals.clear();

    // Clear specific intervals
    if (this.autosaveInterval) {
      clearInterval(this.autosaveInterval);
      this.autosaveInterval = null;
    }

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }

    // Destroy all GridStack instances
    this.gridInstances.forEach((grid, gridId) => {
      try {
        grid.destroy(false);
        console.log(`✅ Destroyed grid: ${gridId}`);
      } catch (error) {
        console.error(`❌ Error destroying grid ${gridId}:`, error);
      }
    });
    this.gridInstances.clear();

    // Reset state
    this.isInitialized = false;
    this.isRendering = false;
    this.selectedContainer = null;
    this.draggedWidget = null;
    this.isDragging = false;

    console.log("✅ Canvas Studio cleanup completed");
  }

  /**
   * Configure section layout
   */
  configureSectionLayout(containerId) {
    const container = this.containers.get(containerId);
    if (!container || container.type !== "section") {
      this.showToast("Invalid section container", "error");
      return;
    }

    // Create layout configuration modal
    const configHTML = `
      <div class="modal fade" id="section-layout-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="fas fa-cog me-2"></i>
                Configure Section Layout
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label class="form-label">Section Name</label>
                <input type="text" class="form-control" id="section-name-input" value="${
                  container.name
                }">
              </div>
              <div class="mb-3">
                <label class="form-label">Grid Columns</label>
                <select class="form-select" id="grid-columns-select">
                  <option value="12" ${
                    container.gridColumns === 12 ? "selected" : ""
                  }>12 Columns</option>
                  <option value="8" ${
                    container.gridColumns === 8 ? "selected" : ""
                  }>8 Columns</option>
                  <option value="6" ${
                    container.gridColumns === 6 ? "selected" : ""
                  }>6 Columns</option>
                  <option value="4" ${
                    container.gridColumns === 4 ? "selected" : ""
                  }>4 Columns</option>
                </select>
              </div>
              <div class="mb-3">
                <label class="form-label">Cell Height (pixels)</label>
                <input type="number" class="form-control" id="cell-height-input" value="${
                  container.cellHeight || 60
                }" min="40" max="200">
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-primary" id="save-section-config">Save Changes</button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById("section-layout-modal");
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML("beforeend", configHTML);

    // Show the modal
    const modal = document.getElementById("section-layout-modal");
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Handle save
    const saveButton = document.getElementById("save-section-config");
    if (saveButton) {
      saveButton.addEventListener("click", () => {
        const newName = document
          .getElementById("section-name-input")
          .value.trim();
        const gridColumns = parseInt(
          document.getElementById("grid-columns-select").value
        );
        const cellHeight = parseInt(
          document.getElementById("cell-height-input").value
        );

        if (newName) {
          container.name = newName;
          container.gridColumns = gridColumns;
          container.cellHeight = cellHeight;
          container.updated = new Date().toISOString();
          this.containers.set(containerId, container);

          // Re-render the interface
          this.renderContainers();
          this.renderCanvas();
          this.saveConfiguration();

          this.showToast("Section layout updated", "success");
          bsModal.hide();
        }
      });
    }

    // Clean up modal after hiding
    modal.addEventListener("hidden.bs.modal", () => {
      modal.remove();
    });
  }

  /**
   * Rename container
   */
  renameContainer(containerId) {
    const container = this.containers.get(containerId);
    if (!container) {
      this.showToast("Container not found", "error");
      return;
    }

    // Create rename modal
    const renameHTML = `
      <div class="modal fade" id="rename-container-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="fas fa-edit me-2"></i>
                Rename ${
                  container.type === "section" ? "Section" : "Tab Container"
                }
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label class="form-label">Container Name</label>
                <input type="text" class="form-control" id="container-name-input" value="${
                  container.name
                }" placeholder="Enter container name">
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-primary" id="save-container-name">Save</button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById("rename-container-modal");
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML("beforeend", renameHTML);

    // Show the modal
    const modal = document.getElementById("rename-container-modal");
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Focus on input
    const nameInput = document.getElementById("container-name-input");
    if (nameInput) {
      nameInput.focus();
      nameInput.select();
    }

    // Handle save
    const saveButton = document.getElementById("save-container-name");
    if (saveButton) {
      saveButton.addEventListener("click", () => {
        const newName = nameInput.value.trim();
        if (newName && newName !== container.name) {
          this.updateContainerName(containerId, newName);
          this.showToast("Container renamed successfully", "success");
          bsModal.hide();
        } else if (!newName) {
          this.showToast("Please enter a valid name", "warning");
        } else {
          bsModal.hide();
        }
      });
    }

    // Handle Enter key
    if (nameInput) {
      nameInput.addEventListener("keydown", (e) => {
        if (e.key === "Enter") {
          saveButton.click();
        }
      });
    }

    // Clean up modal after hiding
    modal.addEventListener("hidden.bs.modal", () => {
      modal.remove();
    });
  }

  /**
   * Configure tab position
   */
  configureTabPosition(containerId) {
    const container = this.containers.get(containerId);
    if (!container || container.type !== "tabs") {
      this.showToast("Invalid tab container", "error");
      return;
    }

    // Create tab position configuration modal
    const configHTML = `
      <div class="modal fade" id="tab-position-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="fas fa-cog me-2"></i>
                Configure Tab Position
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label class="form-label">Tab Position</label>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="tabPosition" id="tab-top" value="top" ${
                    container.tabPosition === "top" ? "checked" : ""
                  }>
                  <label class="form-check-label" for="tab-top">Top</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="tabPosition" id="tab-bottom" value="bottom" ${
                    !container.tabPosition || container.tabPosition === "bottom"
                      ? "checked"
                      : ""
                  }>
                  <label class="form-check-label" for="tab-bottom">Bottom</label>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-primary" id="save-tab-position">Save Changes</button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById("tab-position-modal");
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML("beforeend", configHTML);

    // Show the modal
    const modal = document.getElementById("tab-position-modal");
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Handle save
    const saveButton = document.getElementById("save-tab-position");
    if (saveButton) {
      saveButton.addEventListener("click", () => {
        const selectedPosition = document.querySelector(
          'input[name="tabPosition"]:checked'
        ).value;

        container.tabPosition = selectedPosition;
        container.updated = new Date().toISOString();
        this.containers.set(containerId, container);

        // Re-render the canvas to show the new tab position
        this.renderCanvas();
        this.saveConfiguration();

        this.showToast("Tab position updated", "success");
        bsModal.hide();
      });
    }

    // Clean up modal after hiding
    modal.addEventListener("hidden.bs.modal", () => {
      modal.remove();
    });
  }

  /**
   * Rename tab
   */
  renameTab(containerId, tabId) {
    const container = this.containers.get(containerId);
    if (!container || container.type !== "tabs") {
      this.showToast("Invalid tab container", "error");
      return;
    }

    const tab = container.tabs.find((t) => t.id === tabId);
    if (!tab) {
      this.showToast("Tab not found", "error");
      return;
    }

    // Create rename tab modal
    const renameHTML = `
      <div class="modal fade" id="rename-tab-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="fas fa-edit me-2"></i>
                Rename Tab
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label class="form-label">Tab Name</label>
                <input type="text" class="form-control" id="tab-name-input" value="${tab.name}" placeholder="Enter tab name">
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-primary" id="save-tab-name">Save</button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById("rename-tab-modal");
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML("beforeend", renameHTML);

    // Show the modal
    const modal = document.getElementById("rename-tab-modal");
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Focus on input
    const nameInput = document.getElementById("tab-name-input");
    if (nameInput) {
      nameInput.focus();
      nameInput.select();
    }

    // Handle save
    const saveButton = document.getElementById("save-tab-name");
    if (saveButton) {
      saveButton.addEventListener("click", () => {
        const newName = nameInput.value.trim();
        if (newName && newName !== tab.name) {
          tab.name = newName;
          container.updated = new Date().toISOString();
          this.containers.set(containerId, container);

          // Re-render to show the updated tab name
          this.renderCanvas();
          this.renderContainers();
          this.saveConfiguration();

          this.showToast("Tab renamed successfully", "success");
          bsModal.hide();
        } else if (!newName) {
          this.showToast("Please enter a valid name", "warning");
        } else {
          bsModal.hide();
        }
      });
    }

    // Handle Enter key
    if (nameInput) {
      nameInput.addEventListener("keydown", (e) => {
        if (e.key === "Enter") {
          saveButton.click();
        }
      });
    }

    // Clean up modal after hiding
    modal.addEventListener("hidden.bs.modal", () => {
      modal.remove();
    });
  }

  /**
   * Render tab navigation for tab-container widgets
   */
  renderTabContainerNavigation(widget, tabs, activeTab) {
    return `
      <div class="tab-navigation widget-tab-nav">
        <div class="tab-nav-scroll">
          <div class="tab-nav-list">
            ${tabs
              .map(
                (tab) => `
              <button class="tab-nav-item ${
                tab.id === activeTab.id ? "active" : ""
              }" 
                      data-tab-id="${tab.id}"
                      onclick="canvasStudio.switchTabInWidget('${
                        widget.id
                      }', '${tab.id}')"
                      title="${tab.name}">
                <span class="tab-name">${tab.name}</span>
                <span class="tab-badge">${tab.widgets.length}</span>
                ${
                  tabs.length > 1
                    ? `
                  <button class="tab-close" 
                          onclick="event.stopPropagation(); canvasStudio.removeTabFromWidget('${widget.id}', '${tab.id}')"
                          title="Close Tab">
                    <i class="fas fa-times"></i>
                  </button>
                `
                    : ""
                }
              </button>
            `
              )
              .join("")}
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Configure tab widget settings
   */
  configureTabWidget(widgetId) {
    // Find the widget in the selected container
    const container = this.containers.get(this.selectedContainer);
    if (!container) {
      this.showToast("No container selected", "error");
      return;
    }

    const widget = container.widgets.find((w) => w.id === widgetId);
    if (!widget || widget.type !== "tab-container") {
      this.showToast("Tab widget not found", "error");
      return;
    }

    // Create tab configuration modal
    const configHTML = `
      <div class="modal fade" id="tab-widget-config-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="fas fa-cog me-2"></i>
                Configure Tab Widget
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="mb-3">
                <label class="form-label">Tab Position</label>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="tabPosition" id="widget-tab-top" value="top" ${
                    widget.tabPosition === "top" ? "checked" : ""
                  }>
                  <label class="form-check-label" for="widget-tab-top">Top</label>
                </div>
                <div class="form-check">
                  <input class="form-check-input" type="radio" name="tabPosition" id="widget-tab-bottom" value="bottom" ${
                    !widget.tabPosition || widget.tabPosition === "bottom"
                      ? "checked"
                      : ""
                  }>
                  <label class="form-check-label" for="widget-tab-bottom">Bottom</label>
                </div>
              </div>
              
              <div class="mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="show-tab-bar" ${
                    widget.config?.showTabBar !== false ? "checked" : ""
                  }>
                  <label class="form-check-label" for="show-tab-bar">Show Tab Bar</label>
                </div>
              </div>
              
              <div class="mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="allow-tab-reorder" ${
                    widget.config?.allowTabReorder !== false ? "checked" : ""
                  }>
                  <label class="form-check-label" for="allow-tab-reorder">Allow Tab Reordering</label>
                </div>
              </div>
              
              <div class="mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="allow-tab-close" ${
                    widget.config?.allowTabClose !== false ? "checked" : ""
                  }>
                  <label class="form-check-label" for="allow-tab-close">Allow Tab Closing</label>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-primary" id="save-tab-widget-config">Save Changes</button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById("tab-widget-config-modal");
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML("beforeend", configHTML);

    // Show the modal
    const modal = document.getElementById("tab-widget-config-modal");
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Handle save
    const saveButton = document.getElementById("save-tab-widget-config");
    if (saveButton) {
      saveButton.addEventListener("click", () => {
        const selectedPosition = document.querySelector(
          'input[name="tabPosition"]:checked'
        ).value;
        const showTabBar = document.getElementById("show-tab-bar").checked;
        const allowTabReorder =
          document.getElementById("allow-tab-reorder").checked;
        const allowTabClose =
          document.getElementById("allow-tab-close").checked;

        // Update widget configuration
        widget.tabPosition = selectedPosition;
        widget.config = {
          showTabBar,
          allowTabReorder,
          allowTabClose,
        };

        // Update container
        container.updated = new Date().toISOString();
        this.containers.set(this.selectedContainer, container);

        // Instead of full re-render, update only the tab-container widget
        const tabContainerElement = document.querySelector(
          `[data-widget-id="${widgetId}"]`
        );
        if (tabContainerElement) {
          // Find the widget content container
          const widgetContent = tabContainerElement.querySelector(
            ".grid-stack-item-content"
          );
          if (widgetContent) {
            // Re-render just this widget's content
            const updatedContent = this.createWidgetPreviewContent(widget);
            widgetContent.innerHTML = updatedContent;

            // Initialize grids for the updated widget
            setTimeout(() => {
              this.initializeTabGrids(widget);
            }, 100);
          }
        }

        this.saveConfiguration();

        this.showToast("Tab widget configuration updated", "success");
        bsModal.hide();
      });
    }

    // Clean up modal after hiding
    modal.addEventListener("hidden.bs.modal", () => {
      modal.remove();
    });
  }

  /**
   * Add new tab to widget
   */
  addTabToWidget(widgetId) {
    const container = this.containers.get(this.selectedContainer);
    if (!container) return;

    const widget = container.widgets.find((w) => w.id === widgetId);
    if (!widget || widget.type !== "tab-container") return;

    // Ensure tabs array exists
    if (!widget.tabs) {
      widget.tabs = [];
    }

    // Create new tab
    const newTab = {
      id: this.generateId(),
      name: `Tab ${widget.tabs.length + 1}`,
      widgets: [],
      active: false,
    };

    widget.tabs.push(newTab);

    // Update container
    container.updated = new Date().toISOString();
    this.containers.set(this.selectedContainer, container);

    // Instead of full re-render, update only the tab-container widget
    const tabContainerElement = document.querySelector(
      `[data-widget-id="${widgetId}"]`
    );
    if (tabContainerElement) {
      // Find the widget content container
      const widgetContent = tabContainerElement.querySelector(
        ".grid-stack-item-content"
      );
      if (widgetContent) {
        // Re-render just this widget's content
        const updatedContent = this.createWidgetPreviewContent(widget);
        widgetContent.innerHTML = updatedContent;

        // Initialize grids for the updated widget (including new tab)
        setTimeout(() => {
          this.initializeTabGrids(widget);
        }, 100);
      }
    }

    this.saveConfiguration();
    this.showToast("New tab added", "success");
  }

  /**
   * Switch active tab in widget
   */
  switchTabInWidget(widgetId, tabId) {
    console.log("🔄 ===== TAB SWITCH DEBUG START =====");
    console.log(`🔄 Switching tab in widget: ${widgetId} to tab: ${tabId}`);

    const widget = this.findTabContainerWidget(widgetId);
    if (!widget) {
      console.log("❌ Widget not found for tab switch");
      return;
    }

    console.log("🔄 Widget found:", widget);

    // Update the active tab in data model
    widget.tabs.forEach((tab) => {
      const wasActive = tab.active;
      tab.active = tab.id === tabId;
      console.log(
        `🔄 Tab ${tab.id}: ${wasActive ? "was active" : "was inactive"} → ${
          tab.active ? "now active" : "now inactive"
        }`
      );
    });

    // Update DOM
    const widgetElement = document.querySelector(
      `[data-widget-id="${widgetId}"]`
    );
    if (!widgetElement) {
      console.log("❌ Widget element not found in DOM");
      return;
    }

    console.log("🔄 Widget element found:", widgetElement);

    // Update tab navigation
    const tabNavItems = widgetElement.querySelectorAll(".tab-nav-item");
    console.log(`🔄 Found ${tabNavItems.length} tab nav items`);

    tabNavItems.forEach((navItem) => {
      const navTabId = navItem.getAttribute("data-tab-id");
      const wasActive = navItem.classList.contains("active");

      if (navTabId === tabId) {
        navItem.classList.add("active");
        console.log(`🔄 Tab nav ${navTabId}: activated`);
      } else {
        navItem.classList.remove("active");
        console.log(`🔄 Tab nav ${navTabId}: deactivated`);
      }
    });

    // Update tab content visibility
    const tabContents = widgetElement.querySelectorAll(".tab-content");
    console.log(`🔄 Found ${tabContents.length} tab content elements`);
    tabContents.forEach((content) => {
      const contentTabId = content.dataset.tabId;
      if (contentTabId === tabId) {
        // Show active tab
        content.classList.add("active");
        content.style.display = "flex";
        console.log(
          `🔄 Tab content ${contentTabId}: activated - display set to flex`
        );

        // Verify the change
        const computedStyle = window.getComputedStyle(content);
        console.log(`🔄 Tab content ${contentTabId} verification:`, {
          display: computedStyle.display,
          hasActiveClass: content.classList.contains("active"),
        });
      } else {
        // Hide inactive tab
        content.classList.remove("active");
        content.style.display = "none";
        console.log(
          `🔄 Tab content ${contentTabId}: deactivated - display set to none`
        );
      }
    });

    // Save the updated configuration
    this.saveConfiguration();
    console.log("🔄 Configuration saved after tab switch");

    // Update placeholder visibility for the new active tab
    const containerId = widgetElement
      .closest(".container-item")
      ?.getAttribute("data-container-id");
    if (containerId) {
      this.updatePlaceholderVisibility(containerId, tabId);
      console.log(
        `🔄 Placeholder visibility updated for container ${containerId}, tab ${tabId}`
      );
    }

    console.log("✅ Tab switch completed successfully");
    console.log("🔄 ===== TAB SWITCH DEBUG END =====");
  }

  /**
   * Remove tab from widget
   */
  removeTabFromWidget(widgetId, tabId) {
    const container = this.containers.get(this.selectedContainer);
    if (!container) return;

    const widget = container.widgets.find((w) => w.id === widgetId);
    if (!widget || widget.type !== "tab-container" || !widget.tabs) return;

    // Don't remove if it's the last tab
    if (widget.tabs.length <= 1) {
      this.showToast("Cannot remove the last tab", "warning");
      return;
    }

    // Remove the tab
    const tabIndex = widget.tabs.findIndex((tab) => tab.id === tabId);
    if (tabIndex === -1) return;

    const removedTab = widget.tabs[tabIndex];
    widget.tabs.splice(tabIndex, 1);

    // If removed tab was active, make first tab active
    if (removedTab.active && widget.tabs.length > 0) {
      widget.tabs[0].active = true;
    }

    // Update container
    container.updated = new Date().toISOString();
    this.containers.set(this.selectedContainer, container);

    // Instead of full re-render, update only the tab-container widget
    const tabContainerElement = document.querySelector(
      `[data-widget-id="${widgetId}"]`
    );
    if (tabContainerElement) {
      // Find the widget content container
      const widgetContent = tabContainerElement.querySelector(
        ".grid-stack-item-content"
      );
      if (widgetContent) {
        // Re-render just this widget's content
        const updatedContent = this.createWidgetPreviewContent(widget);
        widgetContent.innerHTML = updatedContent;

        // Initialize grids for the updated widget
        setTimeout(() => {
          this.initializeTabGrids(widget);
        }, 100);
      }
    }

    this.saveConfiguration();
    this.showToast("Tab removed", "success");
  }

  /**
   * Initialize nested grid for section container widgets
   */
  initializeNestedGrid(parentWidgetId, nestedGridId) {
    const gridElement = document.getElementById(nestedGridId);
    if (!gridElement) {
      console.error(`Nested grid element not found: ${nestedGridId}`);
      return null;
    }

    // Check if grid is already initialized
    if (this.gridInstances.has(nestedGridId)) {
      return this.gridInstances.get(nestedGridId);
    }

    const gridConfig = {
      column: 6, // Smaller grid for nested containers
      cellHeight: 50,
      margin: 4,
      float: true,
      animate: true,
      acceptWidgets: true,
      removable: false,
      disableDrag: false,
      disableResize: false,
      draggable: {
        handle: ".widget-drag-handle",
        appendTo: "body",
        scroll: true,
        containment: false,
      },
      resizable: {
        handles: "se, sw, ne, nw",
      },
      // Custom drop handling for nested widgets
      acceptWidgets: (el) => {
        // Accept widgets but not other containers to prevent infinite nesting
        if (this.isDragging && this.draggedWidget) {
          return !this.draggedWidget.isContainer;
        }
        return false;
      },
    };

    try {
      const grid = GridStack.init(gridConfig, gridElement);

      // Handle widget drops in nested grid
      grid.on("dropped", (event, previousWidget, newWidget) => {
        if (this.draggedWidget && !this.draggedWidget.isContainer) {
          this.handleNestedGridDrop(
            grid,
            newWidget,
            parentWidgetId,
            nestedGridId
          );
        }
      });

      // Handle widget changes in nested grid
      grid.on("change", (event, items) => {
        if (items && items.length > 0) {
          this.handleNestedGridChange(parentWidgetId, items);
        }
      });

      // Handle widget removal from nested grid
      grid.on("removed", (event, items) => {
        if (items && items.length > 0) {
          items.forEach((item) => {
            const widgetId = item.el?.dataset?.widgetId;
            if (widgetId) {
              this.removeNestedWidget(parentWidgetId, widgetId);
            }
          });
        }
      });

      this.gridInstances.set(nestedGridId, grid);
      console.log(`✅ Nested GridStack initialized: ${nestedGridId}`);

      // Load existing nested widgets
      this.loadNestedWidgets(parentWidgetId, nestedGridId);

      return grid;
    } catch (error) {
      console.error(`Failed to initialize nested grid ${nestedGridId}:`, error);
      return null;
    }
  }

  /**
   * Initialize nested tab grid
   */
  initializeNestedTabGrid(parentWidgetId, tabId, tabGridId) {
    const gridElement = document.getElementById(tabGridId);
    if (!gridElement) {
      console.error(`Nested tab grid element not found: ${tabGridId}`);
      return null;
    }

    // Check if grid is already initialized
    if (this.gridInstances.has(tabGridId)) {
      return this.gridInstances.get(tabGridId);
    }

    const gridConfig = {
      column: 6,
      cellHeight: 50,
      margin: 4,
      float: true,
      animate: true,
      acceptWidgets: true,
      removable: false,
      disableDrag: false,
      disableResize: false,
      draggable: {
        handle: ".widget-drag-handle",
        appendTo: "body",
        scroll: true,
        containment: false,
      },
      resizable: {
        handles: "se, sw, ne, nw",
      },
      acceptWidgets: (el) => {
        if (this.isDragging && this.draggedWidget) {
          return !this.draggedWidget.isContainer;
        }
        return false;
      },
    };

    try {
      const grid = GridStack.init(gridConfig, gridElement);

      // Handle widget drops in nested tab grid
      grid.on("dropped", (event, previousWidget, newWidget) => {
        if (this.draggedWidget && !this.draggedWidget.isContainer) {
          this.handleNestedTabGridDrop(grid, newWidget, parentWidgetId, tabId);
        }
      });

      // Handle widget changes in nested tab grid
      grid.on("change", (event, items) => {
        if (items && items.length > 0) {
          this.handleNestedTabGridChange(parentWidgetId, tabId, items);
        }
      });

      this.gridInstances.set(tabGridId, grid);
      console.log(`✅ Nested Tab GridStack initialized: ${tabGridId}`);

      // Load existing nested widgets for this tab
      this.loadNestedTabWidgets(parentWidgetId, tabId, tabGridId);

      return grid;
    } catch (error) {
      console.error(
        `Failed to initialize nested tab grid ${tabGridId}:`,
        error
      );
      return null;
    }
  }

  /**
   * Handle widget drops in nested grids
   */
  handleNestedGridDrop(grid, newWidget, parentWidgetId, nestedGridId) {
    console.log("🎯 Nested grid drop:", {
      parentWidgetId,
      draggedWidget: this.draggedWidget,
    });

    if (!this.draggedWidget || !newWidget) {
      console.log("❌ Nested drop failed: missing draggedWidget or newWidget");
      return;
    }

    const widgetInstance = {
      id: this.generateId(),
      type: this.draggedWidget.type,
      name: this.draggedWidget.name,
      category: this.draggedWidget.category,
      config: {},
      gridPosition: {
        x: newWidget.x,
        y: newWidget.y,
        w: newWidget.w || this.draggedWidget.defaultSize?.w || 2,
        h: newWidget.h || this.draggedWidget.defaultSize?.h || 2,
      },
      created: new Date().toISOString(),
      parentWidget: parentWidgetId,
    };

    // Create widget content for nested widget
    const widgetContent = this.createGridStackWidgetContent(widgetInstance);

    // Update the grid item with proper content
    if (newWidget.el) {
      newWidget.el.dataset.widgetId = widgetInstance.id;
      const contentDiv = newWidget.el.querySelector(".grid-stack-item-content");
      if (contentDiv) {
        contentDiv.innerHTML = widgetContent;
      }
    }

    // Add to parent widget's childWidgets
    this.addNestedWidget(parentWidgetId, widgetInstance);

    this.showToast(`${this.draggedWidget.name} added to container`, "success");
    console.log("✅ Nested widget drop completed successfully");
  }

  /**
   * Handle widget drops in nested tab grids
   */
  handleNestedTabGridDrop(grid, newWidget, parentWidgetId, tabId) {
    console.log("🎯 Nested tab grid drop:", {
      parentWidgetId,
      tabId,
      draggedWidget: this.draggedWidget,
    });

    if (!this.draggedWidget || !newWidget) {
      return;
    }

    const widgetInstance = {
      id: this.generateId(),
      type: this.draggedWidget.type,
      name: this.draggedWidget.name,
      category: this.draggedWidget.category,
      config: {},
      gridPosition: {
        x: newWidget.x,
        y: newWidget.y,
        w: newWidget.w || this.draggedWidget.defaultSize?.w || 2,
        h: newWidget.h || this.draggedWidget.defaultSize?.h || 2,
      },
      created: new Date().toISOString(),
      parentWidget: parentWidgetId,
      parentTab: tabId,
    };

    // Create widget content for nested widget
    const widgetContent = this.createGridStackWidgetContent(widgetInstance);

    // Update the grid item with proper content
    if (newWidget.el) {
      newWidget.el.dataset.widgetId = widgetInstance.id;
      const contentDiv = newWidget.el.querySelector(".grid-stack-item-content");
      if (contentDiv) {
        contentDiv.innerHTML = widgetContent;
      }
    }

    // Add to parent widget's tab
    this.addNestedTabWidget(parentWidgetId, tabId, widgetInstance);

    this.showToast(`${this.draggedWidget.name} added to tab`, "success");
    console.log("✅ Nested tab widget drop completed successfully");
  }

  /**
   * Add nested widget to parent container
   */
  addNestedWidget(parentWidgetId, widgetInstance) {
    // Find parent widget in all containers
    for (const [containerId, container] of this.containers) {
      const parentWidget = (container.widgets || []).find(
        (w) => w.id === parentWidgetId
      );
      if (parentWidget) {
        if (!parentWidget.childWidgets) {
          parentWidget.childWidgets = [];
        }
        parentWidget.childWidgets.push(widgetInstance);

        // Update parent widget's count badge
        this.updateContainerWidgetCount(parentWidgetId);

        // Save configuration
        this.saveConfiguration();
        return;
      }
    }
  }

  /**
   * Add nested widget to parent tab
   */
  addNestedTabWidget(parentWidgetId, tabId, widgetInstance) {
    // Find parent widget in all containers
    for (const [containerId, container] of this.containers) {
      const parentWidget = (container.widgets || []).find(
        (w) => w.id === parentWidgetId
      );
      if (parentWidget && parentWidget.tabs) {
        const tab = parentWidget.tabs.find((t) => t.id === tabId);
        if (tab) {
          if (!tab.widgets) {
            tab.widgets = [];
          }
          tab.widgets.push(widgetInstance);

          // Update tab widget count in UI
          this.updateTabWidgetCount(parentWidgetId, tabId);

          // Save configuration
          this.saveConfiguration();
          return;
        }
      }
    }
  }

  /**
   * Remove nested widget
   */
  removeNestedWidget(parentWidgetId, widgetId) {
    for (const [containerId, container] of this.containers) {
      const parentWidget = (container.widgets || []).find(
        (w) => w.id === parentWidgetId
      );
      if (parentWidget && parentWidget.childWidgets) {
        parentWidget.childWidgets = parentWidget.childWidgets.filter(
          (w) => w.id !== widgetId
        );
        this.updateContainerWidgetCount(parentWidgetId);
        this.saveConfiguration();
        return;
      }
    }
  }

  /**
   * Load nested widgets into grid
   */
  loadNestedWidgets(parentWidgetId, nestedGridId) {
    // Find parent widget and load its child widgets
    for (const [containerId, container] of this.containers) {
      const parentWidget = (container.widgets || []).find(
        (w) => w.id === parentWidgetId
      );
      if (parentWidget && parentWidget.childWidgets) {
        const grid = this.gridInstances.get(nestedGridId);
        if (grid) {
          parentWidget.childWidgets.forEach((widget) => {
            this.addWidgetToNestedGrid(grid, widget);
          });
        }
        break;
      }
    }
  }

  /**
   * Load nested tab widgets into grid
   */
  loadNestedTabWidgets(parentWidgetId, tabId, tabGridId) {
    for (const [containerId, container] of this.containers) {
      const parentWidget = (container.widgets || []).find(
        (w) => w.id === parentWidgetId
      );
      if (parentWidget && parentWidget.tabs) {
        const tab = parentWidget.tabs.find((t) => t.id === tabId);
        if (tab && tab.widgets) {
          const grid = this.gridInstances.get(tabGridId);
          if (grid) {
            tab.widgets.forEach((widget) => {
              this.addWidgetToNestedGrid(grid, widget);
            });
          }
        }
        break;
      }
    }
  }

  /**
   * Add widget to nested grid
   */
  addWidgetToNestedGrid(grid, widget) {
    const widgetContent = this.createGridStackWidgetContent(widget);
    const gridItem = {
      content: widgetContent,
      w: widget.gridPosition.w,
      h: widget.gridPosition.h,
      x: widget.gridPosition.x,
      y: widget.gridPosition.y,
      id: widget.id,
    };

    try {
      const addedWidget = grid.addWidget(gridItem);
      if (addedWidget) {
        addedWidget.dataset.widgetId = widget.id;
        console.log(`✅ Added nested widget to grid: ${widget.name}`);
      }
    } catch (error) {
      console.error("Failed to add nested widget to grid:", error);
    }
  }

  /**
   * Update container widget count badge
   */
  updateContainerWidgetCount(parentWidgetId) {
    const badge = document.querySelector(
      `[data-widget-id="${parentWidgetId}"] .widget-count-badge`
    );
    if (badge) {
      // Find the widget and count its children
      for (const [containerId, container] of this.containers) {
        const parentWidget = (container.widgets || []).find(
          (w) => w.id === parentWidgetId
        );
        if (parentWidget) {
          const count = (parentWidget.childWidgets || []).length;
          badge.textContent = count;
          break;
        }
      }
    }
  }

  /**
   * Update tab widget count badge
   */
  updateTabWidgetCount(parentWidgetId, tabId) {
    const badge = document.querySelector(
      `[data-widget-id="${parentWidgetId}"] .tab-badge-mini`
    );
    if (badge) {
      for (const [containerId, container] of this.containers) {
        const parentWidget = (container.widgets || []).find(
          (w) => w.id === parentWidgetId
        );
        if (parentWidget && parentWidget.tabs) {
          const tab = parentWidget.tabs.find((t) => t.id === tabId);
          if (tab) {
            const count = (tab.widgets || []).length;
            badge.textContent = count;
            break;
          }
        }
      }
    }
  }

  /**
   * Handle nested grid changes (position/size updates)
   */
  handleNestedGridChange(parentWidgetId, items) {
    items.forEach((item) => {
      const widgetId = item.el?.dataset?.widgetId;
      if (widgetId) {
        // Update widget position in parent widget's childWidgets
        for (const [containerId, container] of this.containers) {
          const parentWidget = (container.widgets || []).find(
            (w) => w.id === parentWidgetId
          );
          if (parentWidget && parentWidget.childWidgets) {
            const widget = parentWidget.childWidgets.find(
              (w) => w.id === widgetId
            );
            if (widget) {
              widget.gridPosition = {
                x: item.x,
                y: item.y,
                w: item.w,
                h: item.h,
              };
            }
          }
        }
      }
    });

    // Save configuration
    this.saveConfiguration();
  }

  /**
   * Handle nested tab grid changes
   */
  handleNestedTabGridChange(parentWidgetId, tabId, items) {
    items.forEach((item) => {
      const widgetId = item.el?.dataset?.widgetId;
      if (widgetId) {
        // Update widget position in parent widget's tab
        for (const [containerId, container] of this.containers) {
          const parentWidget = (container.widgets || []).find(
            (w) => w.id === parentWidgetId
          );
          if (parentWidget && parentWidget.tabs) {
            const tab = parentWidget.tabs.find((t) => t.id === tabId);
            if (tab && tab.widgets) {
              const widget = tab.widgets.find((w) => w.id === widgetId);
              if (widget) {
                widget.gridPosition = {
                  x: item.x,
                  y: item.y,
                  w: item.w,
                  h: item.h,
                };
              }
            }
          }
        }
      }
    });

    // Save configuration
    this.saveConfiguration();
  }

  /**
   * Configure section container settings
   */
  configureSectionContainer(widgetId) {
    // Find the widget
    let containerWidget = null;
    let parentContainer = null;

    for (const [containerId, container] of this.containers) {
      const widget = (container.widgets || []).find((w) => w.id === widgetId);
      if (widget) {
        containerWidget = widget;
        parentContainer = container;
        break;
      }
    }

    if (!containerWidget) {
      console.error("Container widget not found:", widgetId);
      return;
    }

    // Create configuration modal
    const modalHTML = `
      <div class="modal fade" id="configureSectionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="fas fa-th-large"></i>
                Configure Section Container
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="sectionConfigForm">
                <div class="mb-3">
                  <label class="form-label">Container Name</label>
                  <input type="text" class="form-control" id="containerName" 
                         value="${containerWidget.name || "Section Container"}">
                </div>
                
                <div class="mb-3">
                  <label class="form-label">Background Color</label>
                  <input type="color" class="form-control form-control-color" 
                         id="containerBgColor" value="#ffffff">
                </div>
                
                <div class="mb-3">
                  <label class="form-label">Border Style</label>
                  <select class="form-control" id="containerBorder">
                    <option value="none">No Border</option>
                    <option value="solid">Solid Border</option>
                    <option value="dashed">Dashed Border</option>
                    <option value="dotted">Dotted Border</option>
                  </select>
                </div>
                
                <div class="row">
                  <div class="col-md-6">
                    <label class="form-label">Padding</label>
                    <select class="form-control" id="containerPadding">
                      <option value="0">None</option>
                      <option value="8px">Small</option>
                      <option value="16px" selected>Medium</option>
                      <option value="24px">Large</option>
                    </select>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Border Radius</label>
                    <select class="form-control" id="containerRadius">
                      <option value="0">Square</option>
                      <option value="4px">Rounded</option>
                      <option value="8px" selected>More Rounded</option>
                      <option value="16px">Very Rounded</option>
                    </select>
                  </div>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button" class="btn btn-primary" onclick="canvasStudio.saveSectionConfiguration('${widgetId}')">
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById("configureSectionModal");
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to document
    document.body.insertAdjacentHTML("beforeend", modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(
      document.getElementById("configureSectionModal")
    );
    modal.show();
  }

  /**
   * Save section container configuration
   */
  saveSectionConfiguration(widgetId) {
    const name = document.getElementById("containerName").value;
    const bgColor = document.getElementById("containerBgColor").value;
    const border = document.getElementById("containerBorder").value;
    const padding = document.getElementById("containerPadding").value;
    const radius = document.getElementById("containerRadius").value;

    // Find and update the widget
    for (const [containerId, container] of this.containers) {
      const widget = (container.widgets || []).find((w) => w.id === widgetId);
      if (widget) {
        widget.name = name;
        widget.config = {
          ...widget.config,
          backgroundColor: bgColor,
          border: border,
          padding: padding,
          borderRadius: radius,
        };

        // Update UI
        const widgetElement = document.querySelector(
          `[data-widget-id="${widgetId}"]`
        );
        if (widgetElement) {
          const titleSpan = widgetElement.querySelector(".widget-title span");
          if (titleSpan) {
            titleSpan.textContent = name;
          }

          // Apply styles to the widget
          const cardBody = widgetElement.querySelector(".card-body");
          if (cardBody) {
            cardBody.style.backgroundColor = bgColor;
            cardBody.style.padding = padding;
            cardBody.style.borderRadius = radius;
            if (border !== "none") {
              cardBody.style.border = `1px ${border} #ccc`;
            }
          }
        }

        // Save configuration
        this.saveConfiguration();
        this.showToast("Container configuration saved", "success");

        // Close modal
        const modal = bootstrap.Modal.getInstance(
          document.getElementById("configureSectionModal")
        );
        modal.hide();

        break;
      }
    }
  }

  /**
   * Open widget library for adding widgets to a specific container
   */
  openWidgetLibraryForContainer(parentWidgetId) {
    // Store the target container for the widget library
    this.targetContainerForWidgets = parentWidgetId;

    // Create or show widget library modal
    const modalHTML = `
      <div class="modal fade" id="widgetLibraryModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="fas fa-plus"></i>
                Add Widgets to Container
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="widget-library-content">
                <div class="widget-search mb-3">
                  <input type="text" class="form-control" placeholder="Search widgets..." 
                         onkeyup="canvasStudio.filterWidgetsInLibrary(this.value)">
                </div>
                <div class="widget-categories" id="widgetLibraryCategories">
                  ${this.renderWidgetLibraryContent()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal
    const existingModal = document.getElementById("widgetLibraryModal");
    if (existingModal) {
      existingModal.remove();
    }

    // Add modal to document
    document.body.insertAdjacentHTML("beforeend", modalHTML);

    // Show modal
    const modal = new bootstrap.Modal(
      document.getElementById("widgetLibraryModal")
    );
    modal.show();

    // Set up drag and drop for the library
    setTimeout(() => {
      this.setupWidgetLibraryDragDrop();
    }, 100);
  }

  /**
   * Render widget library content
   */
  renderWidgetLibraryContent() {
    const categories = {};

    // Group widgets by category, exclude container widgets to prevent infinite nesting
    this.widgets.forEach((widget) => {
      if (!widget.isContainer) {
        // Only show non-container widgets for nested adding
        if (!categories[widget.category]) {
          categories[widget.category] = [];
        }
        categories[widget.category].push(widget);
      }
    });

    let html = "";
    Object.entries(categories).forEach(([category, widgets]) => {
      html += `
        <div class="widget-category">
          <div class="widget-category-header">
            <h6><i class="fas fa-chevron-down"></i> ${category}</h6>
          </div>
          <div class="widget-category-content">
            <div class="widget-grid">
              ${widgets
                .map(
                  (widget) => `
                <div class="widget-library-item" 
                     data-widget-type="${widget.type}"
                     data-widget-name="${widget.name}"
                     data-widget-category="${widget.category}"
                     onclick="canvasStudio.addWidgetToTargetContainer('${
                       widget.type
                     }', '${widget.name}', '${widget.category}')">
                  <div class="widget-library-icon">
                    <i class="${this.getWidgetIcon(widget.type)}"></i>
                  </div>
                  <div class="widget-library-info">
                    <div class="widget-library-name">${widget.name}</div>
                    <div class="widget-library-desc">${widget.description}</div>
                  </div>
                </div>
              `
                )
                .join("")}
            </div>
          </div>
        </div>
      `;
    });

    return html;
  }

  /**
   * Add widget to target container
   */
  addWidgetToTargetContainer(widgetType, widgetName, widgetCategory) {
    if (!this.targetContainerForWidgets) {
      console.error("No target container specified");
      return;
    }

    // Find the widget definition
    const widgetDef = this.widgets.find((w) => w.type === widgetType);
    if (!widgetDef) {
      console.error("Widget definition not found:", widgetType);
      return;
    }

    // Create widget instance
    const widgetInstance = {
      id: this.generateId(),
      type: widgetType,
      name: widgetName,
      category: widgetCategory,
      config: {},
      gridPosition: {
        x: 0,
        y: 0,
        w: widgetDef.defaultSize?.w || 2,
        h: widgetDef.defaultSize?.h || 2,
      },
      created: new Date().toISOString(),
      parentWidget: this.targetContainerForWidgets,
    };

    // Add to parent widget
    this.addNestedWidget(this.targetContainerForWidgets, widgetInstance);

    // Add to the nested grid
    const nestedGridId = `nested-grid-${this.targetContainerForWidgets}`;
    const grid = this.gridInstances.get(nestedGridId);
    if (grid) {
      this.addWidgetToNestedGrid(grid, widgetInstance);

      // Hide placeholder if it exists
      const placeholder = document.querySelector(
        `#${nestedGridId} .nested-grid-placeholder`
      );
      if (placeholder) {
        placeholder.style.display = "none";
      }
    }

    this.showToast(`${widgetName} added to container`, "success");

    // Close modal
    const modal = bootstrap.Modal.getInstance(
      document.getElementById("widgetLibraryModal")
    );
    if (modal) {
      modal.hide();
    }
  }

  /**
   * Filter widgets in library modal
   */
  filterWidgetsInLibrary(searchTerm) {
    const items = document.querySelectorAll(".widget-library-item");
    items.forEach((item) => {
      const name = item
        .querySelector(".widget-library-name")
        .textContent.toLowerCase();
      const desc = item
        .querySelector(".widget-library-desc")
        .textContent.toLowerCase();
      const matches =
        name.includes(searchTerm.toLowerCase()) ||
        desc.includes(searchTerm.toLowerCase());
      item.style.display = matches ? "flex" : "none";
    });
  }

  /**
   * Setup drag and drop for widget library
   */
  setupWidgetLibraryDragDrop() {
    const libraryItems = document.querySelectorAll(".widget-library-item");
    libraryItems.forEach((item) => {
      item.draggable = true;

      item.addEventListener("dragstart", (e) => {
        const widgetType = item.dataset.widgetType;
        const widgetDef = this.widgets.find((w) => w.type === widgetType);
        if (widgetDef) {
          this.draggedWidget = widgetDef;
          this.isDragging = true;

          // Add visual feedback
          item.classList.add("dragging");

          console.log("Widget library drag started:", widgetDef);
        }
      });

      item.addEventListener("dragend", (e) => {
        this.isDragging = false;
        item.classList.remove("dragging");
      });
    });
  }

  /**
   * Get widget icon based on type
   */
  getWidgetIcon(type) {
    const iconMap = {
      text: "fas fa-font",
      button: "fas fa-hand-pointer",
      image: "fas fa-image",
      video: "fas fa-video",
      form: "fas fa-wpforms",
      chart: "fas fa-chart-bar",
      table: "fas fa-table",
      card: "fas fa-id-card",
      list: "fas fa-list",
      grid: "fas fa-th",
      tabs: "fas fa-folder-open",
      accordion: "fas fa-bars",
      carousel: "fas fa-images",
      modal: "fas fa-window-restore",
      alert: "fas fa-exclamation-triangle",
      progress: "fas fa-spinner",
      badge: "fas fa-tag",
      breadcrumb: "fas fa-sitemap",
      pagination: "fas fa-ellipsis-h",
      navbar: "fas fa-bars",
      sidebar: "fas fa-columns",
      footer: "fas fa-window-minimize",
      header: "fas fa-window-maximize",
      section: "fas fa-square",
      container: "fas fa-box",
    };
    return iconMap[type] || "fas fa-cube";
  }

  /**
   * Get widget display name based on type
   */
  getWidgetName(type) {
    const nameMap = {
      text: "Text",
      button: "Button",
      image: "Image",
      video: "Video",
      form: "Form",
      chart: "Chart",
      table: "Table",
      card: "Card",
      list: "List",
      grid: "Grid",
      tabs: "Tabs",
      accordion: "Accordion",
      carousel: "Carousel",
      modal: "Modal",
      alert: "Alert",
      progress: "Progress",
      badge: "Badge",
      breadcrumb: "Breadcrumb",
      pagination: "Pagination",
      navbar: "Navbar",
      sidebar: "Sidebar",
      footer: "Footer",
      header: "Header",
      section: "Section",
      container: "Container",
    };
    return nameMap[type] || type.charAt(0).toUpperCase() + type.slice(1);
  }

  /**
   * Configure section layout for a container
   */
  configureSectionLayout(containerId) {
    const container = this.containers.get(containerId);
    if (!container) return;

    // Create configuration modal
    const configModal = document.createElement("div");
    configModal.className = "modal fade";
    configModal.innerHTML = `
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-cog me-2"></i>Configure Section Layout
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <form id="sectionConfigForm">
              <div class="mb-3">
                <label class="form-label">Container Name</label>
                <input type="text" class="form-control" id="containerName" value="${
                  container.name
                }">
              </div>
              
              <div class="mb-3">
                <label class="form-label">Layout Type</label>
                <select class="form-select" id="layoutType">
                  <option value="grid" ${
                    container.layoutType === "grid" ? "selected" : ""
                  }>Grid Layout</option>
                  <option value="flex" ${
                    container.layoutType === "flex" ? "selected" : ""
                  }>Flex Layout</option>
                  <option value="tabs" ${
                    container.layoutType === "tabs" ? "selected" : ""
                  }>Tab Container</option>
                </select>
              </div>
              
              <div class="mb-3">
                <label class="form-label">Columns (for Grid Layout)</label>
                <input type="number" class="form-control" id="gridColumns" 
                       value="${container.columns || 12}" min="1" max="12">
              </div>
              
              <div class="mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="enableTabs" 
                         ${container.enableTabs ? "checked" : ""}>
                  <label class="form-check-label" for="enableTabs">
                    Enable Tab Container
                  </label>
                </div>
              </div>
              
              <div class="mb-3">
                <label class="form-label">Background Color</label>
                <input type="color" class="form-control form-control-color" id="backgroundColor" 
                       value="${container.backgroundColor || "#ffffff"}">
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-primary" onclick="canvasStudio.saveContainerConfig('${containerId}')">
              Save Changes
            </button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(configModal);
    const modal = new bootstrap.Modal(configModal);
    modal.show();

    // Clean up modal after hiding
    configModal.addEventListener("hidden.bs.modal", () => {
      document.body.removeChild(configModal);
    });
  }

  /**
   * Save container configuration
   */
  saveContainerConfig(containerId) {
    const container = this.containers.get(containerId);
    if (!container) return;

    const form = document.getElementById("sectionConfigForm");
    const formData = new FormData(form);

    // Update container properties
    container.name = document.getElementById("containerName").value;
    container.layoutType = document.getElementById("layoutType").value;
    container.columns = parseInt(document.getElementById("gridColumns").value);
    container.enableTabs = document.getElementById("enableTabs").checked;
    container.backgroundColor =
      document.getElementById("backgroundColor").value;

    // Save to storage
    this.saveContainers();

    // Re-render containers
    this.renderContainers();

    // Close modal
    const modal = bootstrap.Modal.getInstance(
      document.querySelector(".modal.show")
    );
    if (modal) modal.hide();

    this.showNotification(
      "Container configuration saved successfully!",
      "success"
    );
  }

  /**
   * Rename container
   */
  renameContainer(containerId) {
    const container = this.containers.get(containerId);
    if (!container) return;

    const newName = prompt("Enter new container name:", container.name);
    if (newName && newName.trim() && newName !== container.name) {
      container.name = newName.trim();
      this.saveContainers();
      this.renderContainers();
      this.showNotification("Container renamed successfully!", "success");
    }
  }

  /**
   * Select container (updated for new UI)
   */
  selectContainer(containerId) {
    this.selectedContainer = containerId;
    this.renderContainers();
    this.renderCanvas();

    // Update UI to show selected state
    document.querySelectorAll(".container-item").forEach((item) => {
      item.classList.remove("active");
    });

    const selectedItem = document.querySelector(
      `[data-container-id="${containerId}"]`
    );
    if (selectedItem) {
      selectedItem.classList.add("active");
    }
  }

  /**
   * Show notification
   */
  showNotification(message, type = "info") {
    // Create notification element
    const notification = document.createElement("div");
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText =
      "top: 20px; right: 20px; z-index: 9999; min-width: 300px;";
    notification.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 3000);
  }
}

// ✅ FIXED INITIALIZATION - Proper global variable assignment
let canvasStudio = null;

document.addEventListener("DOMContentLoaded", () => {
  console.log("DOM loaded, initializing Canvas Studio...");
  canvasStudio = new CanvasStudioIntegrationIndex3();
  canvasStudio
    .init()
    .then(() => {
      // ✅ Export for global access AFTER initialization
      window.canvasStudio = canvasStudio;
      console.log("✅ Canvas Studio globally available");
    })
    .catch((error) => {
      console.error("❌ Canvas Studio initialization failed:", error);
    });
});
