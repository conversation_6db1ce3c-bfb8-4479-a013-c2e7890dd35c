<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aggressive Scroll Prevention Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            height: 200vh; /* Make page scrollable */
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fafafa;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .scroll-test-area {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            background: white;
        }
        
        .scroll-content {
            height: 800px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        
        .section-container-widget {
            border: 2px solid #007bff;
            background: rgba(0, 123, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .nested-grid-container {
            border: 2px dashed #28a745;
            background: rgba(40, 167, 69, 0.1);
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
        }
        
        .grid-stack-nested {
            border: 2px dotted #ffc107;
            background: rgba(255, 193, 7, 0.1);
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .section-content {
            border: 2px solid #dc3545;
            background: rgba(220, 53, 69, 0.1);
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚫 Aggressive Scroll Prevention Test</h1>
        
        <div class="test-section">
            <h3>Test Instructions</h3>
            <p>This page tests the <strong>aggressive scroll prevention</strong> fix for section containers.</p>
            <ol>
                <li>Scroll down to see the test section containers below</li>
                <li>Move your mouse over the section containers (blue, green, yellow, red borders)</li>
                <li>The page should NOT scroll automatically when over these containers</li>
                <li>Only scroll when you're near the viewport edges and NOT over section containers</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>Scroll Test Area</h3>
            <p>Scroll down in this area to create a long page, then test the auto-scroll behavior:</p>
            <div class="scroll-test-area">
                <div class="scroll-content"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test Controls</h3>
            <button class="test-button" onclick="testScrollFix()">Test Scroll Fix</button>
            <button class="test-button" onclick="enableDebug()">Enable Debug Mode</button>
            <button class="test-button" onclick="disableDebug()">Disable Debug Mode</button>
            <button class="test-button" onclick="checkStatus()">Check Status</button>
            <button class="test-button" onclick="testScrollPrevention()">Test Scroll Prevention</button>
        </div>
        
        <div id="status" class="status info">
            Ready to test. Scroll down to see section containers, then move your mouse over them to test scroll prevention.
        </div>
        
        <div class="test-section">
            <h3>Section Container Test Elements</h3>
            <p>These elements should prevent auto-scrolling when your mouse is over them:</p>
            
            <div class="section-container-widget">
                <h4>Section Container Widget</h4>
                <p>This is a test section container widget. Move your mouse over this area - the page should NOT scroll automatically.</p>
                <div class="nested-grid-container">
                    <h5>Nested Grid Container</h5>
                    <p>This is a nested grid container inside the section container.</p>
                    <div class="grid-stack-nested">
                        <h6>Grid Stack Nested</h6>
                        <p>This is a grid-stack-nested element.</p>
                        <div class="section-content">
                            <h6>Section Content</h6>
                            <p>This is a section-content element.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section-container-widget">
                <h4>Another Section Container</h4>
                <p>This is another test section container to ensure the fix works consistently.</p>
                <div class="nested-grid-container">
                    <h5>Another Nested Grid</h5>
                    <p>Test nested grid functionality.</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>What Was Fixed</h3>
            <ul>
                <li><strong>Global Scroll Prevention:</strong> Overrides window.scrollBy and window.scrollTo methods</li>
                <li><strong>Continuous Detection:</strong> Checks every 50ms if mouse is over section containers</li>
                <li><strong>CSS Scroll Prevention:</strong> Adds body.scroll-prevented class with overflow: hidden</li>
                <li><strong>Multiple Prevention Layers:</strong> Stops auto-scroll at multiple checkpoints</li>
                <li><strong>Aggressive CSS Rules:</strong> Forces scroll-behavior: auto and overflow: hidden</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>Expected Behavior</h3>
            <ul>
                <li>✅ Auto-scroll should work when dragging widgets near viewport edges (outside section containers)</li>
                <li>❌ Auto-scroll should NOT work when hovering over section containers</li>
                <li>❌ Auto-scroll should NOT work when dragging within section containers</li>
                <li>❌ Page should NOT scroll when moving mouse over section containers</li>
                <li>✅ Manual scrolling should still work (mouse wheel, scrollbar)</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>Debug Information</h3>
            <div id="debug-info">
                <p>Enable debug mode to see detailed information about scroll prevention.</p>
            </div>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function updateDebugInfo(info) {
            const debugInfo = document.getElementById('debug-info');
            debugInfo.innerHTML = info;
        }
        
        function testScrollFix() {
            updateStatus('Testing scroll fix...', 'info');
            
            setTimeout(() => {
                if (window.GridStackAutoScrollAuto) {
                    updateStatus('✅ GridStack Auto-Scroll is loaded and working!', 'success');
                    
                    // Check scroll prevention status
                    const isInSection = window.GridStackAutoScrollAuto.isInSectionContainer;
                    const scrollPreventionActive = window.GridStackAutoScrollAuto.scrollPreventionActive;
                    const hasScrollInterval = !!window.GridStackAutoScrollAuto.scrollInterval;
                    
                    updateDebugInfo(`
                        <h4>Current Status:</h4>
                        <ul>
                            <li><strong>In Section Container:</strong> ${isInSection ? 'Yes' : 'No'}</li>
                            <li><strong>Scroll Prevention Active:</strong> ${scrollPreventionActive ? 'Yes' : 'No'}</li>
                            <li><strong>Auto-Scroll Active:</strong> ${hasScrollInterval ? 'Yes' : 'No'}</li>
                        </ul>
                    `);
                } else {
                    updateStatus('❌ GridStack Auto-Scroll not found. Make sure the script is loaded.', 'error');
                }
            }, 1000);
        }
        
        function enableDebug() {
            if (window.GridStackAutoScrollAuto) {
                window.GridStackAutoScrollAuto.enableDebug();
                updateStatus('🔍 Debug mode enabled. Check console for detailed logs.', 'info');
            } else {
                updateStatus('❌ GridStack Auto-Scroll not found.', 'error');
            }
        }
        
        function disableDebug() {
            if (window.GridStackAutoScrollAuto) {
                window.GridStackAutoScrollAuto.disableDebug();
                updateStatus('🔍 Debug mode disabled.', 'info');
            } else {
                updateStatus('❌ GridStack Auto-Scroll not found.', 'error');
            }
        }
        
        function checkStatus() {
            if (window.GridStackAutoScrollAuto) {
                const config = window.GridStackAutoScrollAuto.config;
                const isInSection = window.GridStackAutoScrollAuto.isInSectionContainer;
                const scrollPreventionActive = window.GridStackAutoScrollAuto.scrollPreventionActive;
                
                updateStatus(`📊 Auto-Scroll Status: Active | Scroll Zone: ${config.scrollZone}px | Scroll Speed: ${config.scrollSpeed}`, 'success');
                
                updateDebugInfo(`
                    <h4>Configuration:</h4>
                    <ul>
                        <li><strong>Scroll Zone:</strong> ${config.scrollZone}px</li>
                        <li><strong>Scroll Speed:</strong> ${config.scrollSpeed}</li>
                        <li><strong>Scroll Interval:</strong> ${config.scrollInterval}ms</li>
                    </ul>
                    <h4>Current State:</h4>
                    <ul>
                        <li><strong>In Section Container:</strong> ${isInSection ? 'Yes' : 'No'}</li>
                        <li><strong>Scroll Prevention Active:</strong> ${scrollPreventionActive ? 'Yes' : 'No'}</li>
                    </ul>
                `);
            } else {
                updateStatus('❌ GridStack Auto-Scroll not found.', 'error');
            }
        }
        
        function testScrollPrevention() {
            updateStatus('Testing scroll prevention...', 'info');
            
            if (!window.GridStackAutoScrollAuto) {
                updateStatus('❌ GridStack Auto-Scroll not found.', 'error');
                return;
            }
            
            // Test if scroll prevention is working
            const testElement = document.querySelector('.section-container-widget');
            if (testElement) {
                const rect = testElement.getBoundingClientRect();
                const centerX = rect.left + rect.width / 2;
                const centerY = rect.top + rect.height / 2;
                
                // Simulate mouse over section container
                const isOverSection = window.GridStackAutoScrollAuto.isOverSectionContainer(centerX, centerY);
                
                if (isOverSection) {
                    updateStatus('✅ Section container detection is working!', 'success');
                    
                    // Test scroll prevention
                    const shouldPrevent = window.GridStackAutoScrollAuto.shouldPreventScroll();
                    updateDebugInfo(`
                        <h4>Scroll Prevention Test:</h4>
                        <ul>
                            <li><strong>Mouse over section container:</strong> ${isOverSection ? 'Yes' : 'No'}</li>
                            <li><strong>Should prevent scroll:</strong> ${shouldPrevent ? 'Yes' : 'No'}</li>
                            <li><strong>Test position:</strong> (${centerX}, ${centerY})</li>
                        </ul>
                        <p><strong>Result:</strong> ${shouldPrevent ? '✅ Scroll prevention is working!' : '❌ Scroll prevention failed!'}</p>
                    `);
                } else {
                    updateStatus('❌ Section container detection failed!', 'error');
                }
            } else {
                updateStatus('⚠️ No section container found for testing.', 'warning');
            }
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testScrollFix();
            }, 1000);
        });
        
        // Monitor mouse movement for testing
        document.addEventListener('mousemove', (event) => {
            if (window.GridStackAutoScrollAuto && window.GridStackAutoScrollAuto.config.debug) {
                const isOverSection = window.GridStackAutoScrollAuto.isOverSectionContainer(event.clientX, event.clientY);
                if (isOverSection) {
                    console.log('🐭 Mouse over section container - scroll should be prevented');
                }
            }
        });
    </script>
</body>
</html>
