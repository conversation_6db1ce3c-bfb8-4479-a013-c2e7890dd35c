/**
 * Canvas Studio Integration for Workspace Cards
 * Bridges the new Workspace Cards interface with the existing Canvas Studio system
 * Provides advanced tab configuration and widget assignment capabilities
 */

class CanvasStudioIntegration {
  constructor() {
    this.workspaceCards = null;
    this.canvasComposer = null;
    this.tabConfigurations = new Map();
    this.availableWidgets = [];
    this.init();
  }

  init() {
    // Wait for dependencies to load
    document.addEventListener("DOMContentLoaded", () => {
      this.setupIntegration();
    });
  }

  setupIntegration() {
    // Check if Canvas Composer is available
    if (window.canvasComposer) {
      this.canvasComposer = window.canvasComposer;
      console.log("✅ Canvas Composer integration established");
    }

    // Check if Workspace Cards is available (either from window or already set)
    if (!this.workspaceCards && window.workspaceCards) {
      this.workspaceCards = window.workspaceCards;
    }

    if (this.workspaceCards) {
      console.log("✅ Workspace Cards integration established");
    }

    // Load available widgets from Canvas Studio
    this.loadAvailableWidgets().then(() => {
      // Setup integration events
      this.setupIntegrationEvents();
    });
  }

  loadAvailableWidgets() {
    return new Promise((resolve) => {
      // Get widgets from Canvas Composer if available
      if (this.canvasComposer && this.canvasComposer.widgets) {
        this.availableWidgets = Object.values(this.canvasComposer.widgets);
      } else {
        // Fallback to default widget types
        this.availableWidgets = this.getDefaultWidgetTypes();
      }

      console.log(
        `📊 Loaded ${this.availableWidgets.length} available widgets`
      );
      resolve(this.availableWidgets);
    });
  }

  getDefaultWidgetTypes() {
    return [
      {
        id: "chart",
        title: "Chart Widget",
        type: "chart",
        icon: "las la-chart-bar",
      },
      { id: "table", title: "Data Table", type: "table", icon: "las la-table" },
      {
        id: "metric",
        title: "Metric Card",
        type: "metric",
        icon: "las la-tachometer-alt",
      },
      { id: "map", title: "Map Widget", type: "map", icon: "las la-map" },
      { id: "text", title: "Text Widget", type: "text", icon: "las la-font" },
      {
        id: "image",
        title: "Image Widget",
        type: "image",
        icon: "las la-image",
      },
      {
        id: "video",
        title: "Video Widget",
        type: "video",
        icon: "las la-video",
      },
      {
        id: "calendar",
        title: "Calendar Widget",
        type: "calendar",
        icon: "las la-calendar",
      },
      {
        id: "form",
        title: "Form Widget",
        type: "form",
        icon: "las la-wpforms",
      },
      { id: "list", title: "List Widget", type: "list", icon: "las la-list" },
    ];
  }

  setupIntegrationEvents() {
    // Listen for workspace card configuration events
    document.addEventListener("configureWorkspace", (event) => {
      this.showTabConfigurationModal(event.detail.workspaceId);
    });

    // Listen for widget assignment events
    document.addEventListener("assignWidgetToTab", (event) => {
      this.handleWidgetAssignment(event.detail);
    });

    // Listen for Canvas Studio events
    document.addEventListener("canvasStudioReady", () => {
      this.loadAvailableWidgets();
    });
  }

  // ===== TAB CONFIGURATION MODAL =====

  showTabConfigurationModal(workspaceId) {
    console.log("🎯 Opening tab configuration modal for:", workspaceId);

    const workspace = this.workspaceCards.workspaces.get(workspaceId);
    if (!workspace) {
      console.error("❌ Workspace not found:", workspaceId);
      return;
    }

    // Ensure widgets are loaded before showing modal
    if (!this.availableWidgets || this.availableWidgets.length === 0) {
      console.log("⏳ Widgets not loaded yet, loading widgets first...");
      this.loadAvailableWidgets().then(() => {
        console.log("✅ Widgets loaded, now showing modal");
        this.showTabConfigurationModalInternal(workspaceId);
      });
    } else {
      console.log("✅ Widgets already loaded, showing modal");
      this.showTabConfigurationModalInternal(workspaceId);
    }
  }

  showTabConfigurationModalInternal(workspaceId) {
    // Get or create tab configuration
    let tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) {
      const workspace = this.workspaceCards?.workspaces.get(workspaceId);
      if (workspace) {
        tabConfig = this.createDefaultTabConfiguration(workspace);
        this.tabConfigurations.set(workspaceId, tabConfig);
      } else {
        console.error("❌ Workspace not found:", workspaceId);
        return;
      }
    }

    // Show modal with configuration
    const modalHTML = this.getTabConfigurationModalHTML(workspaceId, tabConfig);
    this.showModal("Canvas Studio Integration", modalHTML, "large");

    // Render widgets after modal is shown
    setTimeout(() => {
      this.renderAvailableWidgets(workspaceId);
    }, 100);
  }

  createDefaultTabConfiguration(workspace) {
    return {
      workspaceId: workspace.id,
      workspaceName: workspace.title,
      tabs: [
        {
          id: "tab1",
          name: "Overview",
          icon: "las la-chart-line",
          color: "#3b82f6",
          widgets: [],
          layout: "grid",
        },
        {
          id: "tab2",
          name: "Details",
          icon: "las la-list",
          color: "#10b981",
          widgets: [],
          layout: "grid",
        },
      ],
      activeTab: "tab1",
      maxTabs: 8,
      allowTabReorder: true,
      showTabIcons: true,
    };
  }

  getTabConfigurationModalHTML(workspaceId, tabConfig) {
    return `
      <div class="config-header">
        <h2>Configure Tabs for ${tabConfig.workspaceName}</h2>
        <p>Manage tabs and assign widgets to create your perfect workspace layout</p>
      </div>
      
      <div class="config-main">
        <!-- Left Panel: Tab Management -->
        <div class="config-tabs-panel">
          <div class="panel-header">
            <h3><i class="las la-layer-group"></i> Tab Configuration</h3>
            <button class="btn-primary btn-sm" onclick="canvasStudioIntegration.addNewTab('${workspaceId}')">
              <i class="las la-plus"></i> Add Tab
            </button>
          </div>
          <div class="tabs-list" id="tabs-list-${workspaceId}">
            ${this.renderTabsList(tabConfig)}
          </div>
          <div class="tab-settings">
            <h4>Tab Settings</h4>
            <label class="checkbox-label">
              <input type="checkbox" ${
                tabConfig.allowTabReorder ? "checked" : ""
              } 
                     onchange="canvasStudioIntegration.updateTabSetting('${workspaceId}', 'allowTabReorder', this.checked)">
              Allow tab reordering
            </label>
            <label class="checkbox-label">
              <input type="checkbox" ${tabConfig.showTabIcons ? "checked" : ""} 
                     onchange="canvasStudioIntegration.updateTabSetting('${workspaceId}', 'showTabIcons', this.checked)">
              Show tab icons
            </label>
            <div class="setting-group">
              <label>Maximum tabs: 
                <input type="number" min="1" max="10" value="${
                  tabConfig.maxTabs
                }" 
                       onchange="canvasStudioIntegration.updateTabSetting('${workspaceId}', 'maxTabs', parseInt(this.value))">
              </label>
            </div>
          </div>
        </div>

        <!-- Center Panel: Live Preview -->
        <div class="config-preview-panel">
          <div class="panel-header">
            <h3><i class="las la-eye"></i> Live Preview</h3>
            <button class="btn-secondary btn-sm" onclick="canvasStudioIntegration.togglePreviewMode()">
              <i class="las la-expand"></i> Toggle Mode
            </button>
          </div>
          <div class="preview-content" id="preview-content-${workspaceId}">
            ${this.renderTabPreview(tabConfig)}
          </div>
        </div>

        <!-- Right Panel: Widget Assignment -->
        <div class="config-widgets-panel">
          <div class="panel-header">
            <h3><i class="las la-puzzle-piece"></i> Available Widgets</h3>
            <div class="widget-search">
              <input type="text" placeholder="Search widgets..." 
                     oninput="canvasStudioIntegration.filterWidgets(this.value, '${workspaceId}')">
            </div>
          </div>
          <div class="widgets-grid" id="widgets-grid-${workspaceId}">
            <!-- Widgets will be populated by renderAvailableWidgets -->
          </div>
        </div>
      </div>

      <div class="config-actions">
        <button class="btn-secondary" onclick="canvasStudioIntegration.resetTabConfiguration('${workspaceId}')">
          <i class="las la-undo"></i> Reset
        </button>
        <button class="btn-secondary" onclick="canvasStudioIntegration.exportTabConfiguration('${workspaceId}')">
          <i class="las la-download"></i> Export
        </button>
        <button class="btn-secondary" onclick="canvasStudioIntegration.closeModal()">
          Cancel
        </button>
        <button class="btn-primary" onclick="canvasStudioIntegration.saveTabConfiguration('${workspaceId}')">
          <i class="las la-save"></i> Save Configuration
        </button>
      </div>
    `;
  }

  renderTabsList(tabConfig) {
    return tabConfig.tabs
      .map(
        (tab, index) => `
      <div class="tab-item" data-tab-id="${tab.id}">
        <div class="tab-item-header" style="border-left: 4px solid ${
          tab.color
        }">
          <div class="tab-info">
            <div class="tab-icon-name">
              <i class="${tab.icon}" style="color: ${tab.color}"></i>
              <input type="text" value="${tab.name}" class="tab-name-input"
                     onchange="canvasStudioIntegration.updateTabName('${
                       tabConfig.workspaceId
                     }', '${tab.id}', this.value)">
            </div>
            <div class="tab-meta">
              <span class="widget-count">${tab.widgets.length} widgets</span>
            </div>
          </div>
          <div class="tab-actions">
            <button class="btn-icon" onclick="canvasStudioIntegration.showTabIconPicker('${
              tabConfig.workspaceId
            }', '${tab.id}')" title="Change Icon">
              <i class="las la-palette"></i>
            </button>
            <button class="btn-icon" onclick="canvasStudioIntegration.showTabColorPicker('${
              tabConfig.workspaceId
            }', '${tab.id}')" title="Change Color">
              <i class="las la-fill-drip"></i>
            </button>
            <button class="btn-icon" onclick="canvasStudioIntegration.duplicateTab('${
              tabConfig.workspaceId
            }', '${tab.id}')" title="Duplicate Tab">
              <i class="las la-copy"></i>
            </button>
            ${
              tabConfig.tabs.length > 1
                ? `
              <button class="btn-icon btn-danger" onclick="canvasStudioIntegration.deleteTab('${tabConfig.workspaceId}', '${tab.id}')" title="Delete Tab">
                <i class="las la-trash"></i>
              </button>
            `
                : ""
            }
          </div>
        </div>
        <div class="tab-item-content">
          <div class="assigned-widgets">
            ${tab.widgets
              .map((widgetId) => {
                const widget = this.availableWidgets.find(
                  (w) => w.id === widgetId
                );
                return widget
                  ? `
                <div class="assigned-widget" data-widget-id="${widgetId}">
                  <i class="${widget.icon}"></i>
                  <span>${widget.title}</span>
                  <button class="btn-remove" onclick="canvasStudioIntegration.removeWidgetFromTab('${tabConfig.workspaceId}', '${tab.id}', '${widgetId}')">
                    <i class="las la-times"></i>
                  </button>
                </div>
              `
                  : "";
              })
              .join("")}
            ${
              tab.widgets.length === 0
                ? '<div class="no-widgets">No widgets assigned</div>'
                : ""
            }
          </div>
        </div>
      </div>
    `
      )
      .join("");
  }

  renderAvailableWidgets(workspaceId) {
    const widgetsContainer = document.getElementById(
      `widgets-grid-${workspaceId}`
    );

    if (!widgetsContainer) {
      console.error(
        "❌ Widgets container not found for workspace:",
        workspaceId
      );
      return "";
    }

    if (!this.availableWidgets || this.availableWidgets.length === 0) {
      return `
        <div class="no-widgets-message">
          <p>No widgets available</p>
        </div>
      `;
    }

    const widgetsHTML = this.availableWidgets
      .map(
        (widget) => `
        <div class="widget-item" 
             draggable="true" 
             data-widget-id="${widget.id}"
             ondragstart="canvasStudioIntegration.handleWidgetDragStart(event, '${widget.id}')">
          <div class="widget-icon">
            <i class="${widget.icon}"></i>
          </div>
          <div class="widget-name">${widget.title}</div>
          <div class="widget-description">${widget.type}</div>
        </div>
      `
      )
      .join("");

    widgetsContainer.innerHTML = widgetsHTML;
    return widgetsHTML;
  }

  filterWidgets(searchTerm, workspaceId) {
    const widgetsContainer = document.getElementById(
      `available-widgets-${workspaceId}`
    );
    if (!widgetsContainer) return;

    const filteredWidgets = this.availableWidgets.filter(
      (widget) =>
        widget.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        widget.type.toLowerCase().includes(searchTerm.toLowerCase())
    );

    widgetsContainer.innerHTML = `
      <div class="widgets-grid">
        ${filteredWidgets
          .map(
            (widget) => `
          <div class="widget-item" data-widget-id="${widget.id}" draggable="true"
               ondragstart="canvasStudioIntegration.handleWidgetDragStart(event, '${widget.id}')"
               onclick="canvasStudioIntegration.assignWidgetToSelectedTab('${workspaceId}', '${widget.id}')">
            <div class="widget-icon">
              <i class="${widget.icon}"></i>
            </div>
            <div class="widget-info">
              <div class="widget-title">${widget.title}</div>
              <div class="widget-type">${widget.type}</div>
            </div>
            <div class="widget-actions">
              <button class="btn-assign" title="Assign to selected tab">
                <i class="las la-plus"></i>
              </button>
            </div>
          </div>
        `
          )
          .join("")}
      </div>
    `;
  }

  renderTabPreview(tabConfig) {
    return `
      <div class="tab-preview-container">
        <div class="preview-tabs">
          ${tabConfig.tabs
            .map(
              (tab) => `
            <div class="preview-tab ${
              tab.id === tabConfig.activeTab ? "active" : ""
            }" 
                 style="border-bottom: 3px solid ${tab.color}"
                 onclick="canvasStudioIntegration.setActivePreviewTab('${
                   tabConfig.workspaceId
                 }', '${tab.id}')"
                 ondragover="canvasStudioIntegration.handleDragOver(event)"
                 ondragleave="canvasStudioIntegration.handleDragLeave(event)"
                 ondrop="canvasStudioIntegration.handleDrop(event, '${
                   tabConfig.workspaceId
                 }', '${tab.id}')">
              <i class="${tab.icon}"></i>
              <span>${tab.name}</span>
              <div class="preview-tab-count">${tab.widgets.length}</div>
            </div>
          `
            )
            .join("")}
        </div>
        <div class="preview-content">
          ${this.renderActiveTabPreview(tabConfig)}
        </div>
      </div>
    `;
  }

  renderActiveTabPreview(tabConfig) {
    const activeTab = tabConfig.tabs.find(
      (tab) => tab.id === tabConfig.activeTab
    );
    if (!activeTab) return '<div class="no-preview">No active tab</div>';

    if (activeTab.widgets.length === 0) {
      return `
        <div class="empty-tab-preview"
             ondragover="canvasStudioIntegration.handleDragOver(event)"
             ondragleave="canvasStudioIntegration.handleDragLeave(event)"
             ondrop="canvasStudioIntegration.handleDrop(event, '${tabConfig.workspaceId}', '${activeTab.id}')">
          <i class="las la-inbox"></i>
          <h4>No widgets assigned</h4>
          <p>Drag widgets from the right panel to assign them to this tab</p>
        </div>
      `;
    }

    return `
      <div class="tab-widgets-preview"
           ondragover="canvasStudioIntegration.handleDragOver(event)"
           ondragleave="canvasStudioIntegration.handleDragLeave(event)"
           ondrop="canvasStudioIntegration.handleDrop(event, '${
             tabConfig.workspaceId
           }', '${activeTab.id}')">
        ${activeTab.widgets
          .map((widgetId) => {
            const widget = this.availableWidgets.find((w) => w.id === widgetId);
            return widget
              ? `
            <div class="preview-widget">
              <div class="preview-widget-header">
                <i class="${widget.icon}"></i>
                <span>${widget.title}</span>
                <button class="remove-widget-btn" 
                        onclick="canvasStudioIntegration.removeWidgetFromTab('${tabConfig.workspaceId}', '${activeTab.id}', '${widget.id}')"
                        title="Remove widget from tab">
                  <i class="las la-times"></i>
                </button>
              </div>
              <div class="preview-widget-content">
                <div class="widget-placeholder">
                  ${widget.type} widget content
                </div>
              </div>
            </div>
          `
              : "";
          })
          .join("")}
      </div>
    `;
  }

  // ===== TAB MANAGEMENT METHODS =====

  addNewTab(workspaceId) {
    const tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) return;

    if (tabConfig.tabs.length >= tabConfig.maxTabs) {
      this.showToast(`Maximum ${tabConfig.maxTabs} tabs allowed`, "warning");
      return;
    }

    const newTab = {
      id: `tab${Date.now()}`,
      name: `Tab ${tabConfig.tabs.length + 1}`,
      icon: "las la-file",
      color: this.getRandomTabColor(),
      widgets: [],
      layout: "grid",
    };

    tabConfig.tabs.push(newTab);
    this.refreshTabConfiguration(workspaceId);
    this.showToast("New tab added successfully", "success");
  }

  deleteTab(workspaceId, tabId) {
    const tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) return;

    if (tabConfig.tabs.length <= 1) {
      this.showToast("Cannot delete the last tab", "warning");
      return;
    }

    const tabIndex = tabConfig.tabs.findIndex((tab) => tab.id === tabId);
    if (tabIndex === -1) return;

    const tab = tabConfig.tabs[tabIndex];
    if (tab.widgets.length > 0) {
      if (
        !confirm(
          `This tab contains ${tab.widgets.length} widgets. Are you sure you want to delete it?`
        )
      ) {
        return;
      }
    }

    tabConfig.tabs.splice(tabIndex, 1);

    // Update active tab if necessary
    if (tabConfig.activeTab === tabId) {
      tabConfig.activeTab = tabConfig.tabs[0].id;
    }

    this.refreshTabConfiguration(workspaceId);
    this.showToast("Tab deleted successfully", "success");
  }

  duplicateTab(workspaceId, tabId) {
    const tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) return;

    if (tabConfig.tabs.length >= tabConfig.maxTabs) {
      this.showToast(`Maximum ${tabConfig.maxTabs} tabs allowed`, "warning");
      return;
    }

    const sourceTab = tabConfig.tabs.find((tab) => tab.id === tabId);
    if (!sourceTab) return;

    const newTab = {
      ...sourceTab,
      id: `tab${Date.now()}`,
      name: `${sourceTab.name} Copy`,
      widgets: [...sourceTab.widgets],
    };

    tabConfig.tabs.push(newTab);
    this.refreshTabConfiguration(workspaceId);
    this.showToast("Tab duplicated successfully", "success");
  }

  updateTabName(workspaceId, tabId, newName) {
    const tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) return;

    const tab = tabConfig.tabs.find((t) => t.id === tabId);
    if (tab) {
      tab.name = newName;
      this.refreshTabPreview(workspaceId);
    }
  }

  updateTabSetting(workspaceId, setting, value) {
    const tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) return;

    tabConfig[setting] = value;
    this.refreshTabConfiguration(workspaceId);
  }

  // ===== WIDGET ASSIGNMENT METHODS =====

  assignWidgetToSelectedTab(workspaceId, widgetId) {
    const targetTabSelect = document.getElementById(
      `target-tab-${workspaceId}`
    );
    if (!targetTabSelect) return;

    const targetTabId = targetTabSelect.value;
    this.assignWidgetToTab(workspaceId, targetTabId, widgetId);
  }

  assignWidgetToTab(workspaceId, tabId, widgetId) {
    const tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) return;

    const tab = tabConfig.tabs.find((t) => t.id === tabId);
    if (!tab) return;

    // Check if widget is already assigned to this tab
    if (tab.widgets.includes(widgetId)) {
      this.showToast("Widget is already assigned to this tab", "info");
      return;
    }

    // Remove widget from other tabs first
    tabConfig.tabs.forEach((t) => {
      const index = t.widgets.indexOf(widgetId);
      if (index > -1) {
        t.widgets.splice(index, 1);
      }
    });

    // Add widget to target tab
    tab.widgets.push(widgetId);

    this.refreshTabConfiguration(workspaceId);
    this.showToast("Widget assigned successfully", "success");
  }

  removeWidgetFromTab(workspaceId, tabId, widgetId) {
    const tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) return;

    const tab = tabConfig.tabs.find((t) => t.id === tabId);
    if (!tab) return;

    const index = tab.widgets.indexOf(widgetId);
    if (index > -1) {
      tab.widgets.splice(index, 1);
      this.refreshTabConfiguration(workspaceId);
      this.showToast("Widget removed from tab", "success");
    }
  }

  // ===== DRAG AND DROP METHODS =====

  handleWidgetDragStart(event, widgetId) {
    console.log("Drag started for widget:", widgetId);
    event.dataTransfer.setData("text/plain", widgetId);
    event.dataTransfer.effectAllowed = "move";

    // Add visual feedback
    event.target.classList.add("dragging");

    // Store the widget ID for reference
    this.draggedWidgetId = widgetId;
  }

  handleDragOver(event) {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";

    // Add visual feedback to drop zone
    const dropZone = event.currentTarget;
    dropZone.classList.add("drag-over");
  }

  handleDragLeave(event) {
    // Remove visual feedback from drop zone
    const dropZone = event.currentTarget;
    dropZone.classList.remove("drag-over");
  }

  handleDrop(event, workspaceId, tabId) {
    event.preventDefault();

    // Remove visual feedback
    const dropZone = event.currentTarget;
    dropZone.classList.remove("drag-over");

    // Get the widget ID from the drag data
    const widgetId =
      event.dataTransfer.getData("text/plain") || this.draggedWidgetId;

    if (widgetId && tabId) {
      console.log("Dropping widget", widgetId, "onto tab", tabId);
      this.assignWidgetToTab(workspaceId, tabId, widgetId);
    }

    // Clean up
    this.draggedWidgetId = null;

    // Remove dragging class from all widgets
    document.querySelectorAll(".widget-item.dragging").forEach((item) => {
      item.classList.remove("dragging");
    });
  }

  // ===== UTILITY METHODS =====

  getTotalAssignedWidgets(tabConfig) {
    return tabConfig.tabs.reduce((total, tab) => total + tab.widgets.length, 0);
  }

  getRandomTabColor() {
    const colors = [
      "#3b82f6",
      "#10b981",
      "#f59e0b",
      "#ef4444",
      "#8b5cf6",
      "#06b6d4",
      "#84cc16",
      "#f97316",
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  refreshTabConfiguration(workspaceId) {
    const tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) return;

    // Refresh tabs list
    const tabsList = document.getElementById(`tabs-list-${workspaceId}`);
    if (tabsList) {
      tabsList.innerHTML = this.renderTabsList(tabConfig);
    }

    // Refresh preview
    this.refreshTabPreview(workspaceId);

    // Refresh target tab selector
    const targetTabSelect = document.getElementById(
      `target-tab-${workspaceId}`
    );
    if (targetTabSelect) {
      const currentValue = targetTabSelect.value;
      targetTabSelect.innerHTML = tabConfig.tabs
        .map(
          (tab) => `
        <option value="${tab.id}">
          ${tab.name} (${tab.widgets.length} widgets)
        </option>
      `
        )
        .join("");

      // Restore selection if still valid
      if (tabConfig.tabs.find((tab) => tab.id === currentValue)) {
        targetTabSelect.value = currentValue;
      }
    }
  }

  refreshTabPreview(workspaceId) {
    const tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) return;

    const previewContent = document.getElementById(
      `preview-content-${workspaceId}`
    );
    if (previewContent) {
      previewContent.innerHTML = this.renderTabPreview(tabConfig);
    }
  }

  setActivePreviewTab(workspaceId, tabId) {
    const tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) return;

    tabConfig.activeTab = tabId;
    this.refreshTabPreview(workspaceId);
  }

  saveTabConfiguration(workspaceId) {
    const tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) return;

    // Save to Canvas Composer if available
    if (this.canvasComposer && this.canvasComposer.tabContainers) {
      this.canvasComposer.tabContainers.set(workspaceId, {
        tabs: tabConfig.tabs,
        activeTab: tabConfig.activeTab,
      });
    }

    // Update workspace card
    if (this.workspaceCards) {
      const workspace = this.workspaceCards.workspaces.get(workspaceId);
      if (workspace) {
        workspace.tabConfiguration = tabConfig;
        workspace.widgets = this.getTotalAssignedWidgets(tabConfig);
        this.workspaceCards.renderWorkspaces();
      }
    }

    this.closeModal();
    this.showToast("Tab configuration saved successfully!", "success");
  }

  resetTabConfiguration(workspaceId) {
    if (
      !confirm(
        "Are you sure you want to reset the tab configuration? This will remove all widgets and reset to default settings."
      )
    ) {
      return;
    }

    const workspace = this.workspaceCards?.workspaces.get(workspaceId);
    if (workspace) {
      const defaultConfig = this.createDefaultTabConfiguration(workspace);
      this.tabConfigurations.set(workspaceId, defaultConfig);
      this.refreshTabConfiguration(workspaceId);
      this.showToast("Tab configuration reset successfully", "success");
    }
  }

  exportTabConfiguration(workspaceId) {
    const tabConfig = this.tabConfigurations.get(workspaceId);
    if (!tabConfig) return;

    const exportData = {
      workspaceId: tabConfig.workspaceId,
      workspaceName: tabConfig.workspaceName,
      tabs: tabConfig.tabs,
      settings: {
        allowTabReordering: tabConfig.allowTabReorder,
        showTabIcons: tabConfig.showTabIcons,
        maxTabs: tabConfig.maxTabs,
      },
      exportDate: new Date().toISOString(),
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement("a");
    link.href = url;
    link.download = `${tabConfig.workspaceName.replace(
      /\s+/g,
      "_"
    )}_tab_config.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    this.showToast("Tab configuration exported successfully", "success");
  }

  // ===== MODAL AND UI METHODS =====

  showModal(title, content, size = "medium") {
    const modal = document.createElement("div");
    modal.className = `canvas-studio-modal ${size}`;
    modal.innerHTML = `
      <div class="modal-backdrop" onclick="canvasStudioIntegration.closeModal()"></div>
      <div class="modal-content">
        <div class="modal-header">
          <h3>${title}</h3>
          <button class="modal-close" onclick="canvasStudioIntegration.closeModal()">
            <i class="las la-times"></i>
          </button>
        </div>
        <div class="modal-body">
          ${content}
        </div>
      </div>
    `;

    document.body.appendChild(modal);
    this.currentModal = modal;

    // Add CSS classes for animation
    setTimeout(() => modal.classList.add("show"), 10);
  }

  closeModal() {
    if (this.currentModal) {
      this.currentModal.classList.remove("show");
      setTimeout(() => {
        if (this.currentModal && this.currentModal.parentNode) {
          this.currentModal.parentNode.removeChild(this.currentModal);
        }
        this.currentModal = null;
      }, 300);
    }
  }

  showToast(message, type = "info") {
    const toast = document.createElement("div");
    toast.className = `canvas-studio-toast ${type}`;
    toast.innerHTML = `
      <div class="toast-content">
        <i class="las ${this.getToastIcon(type)}"></i>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(toast);

    setTimeout(() => toast.classList.add("show"), 10);
    setTimeout(() => {
      toast.classList.remove("show");
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 300);
    }, 3000);
  }

  togglePreviewMode() {
    // Toggle between different preview modes if needed
    console.log("🔄 Preview mode toggled");
    this.showToast("Preview mode toggled", "info");
  }

  getToastIcon(type) {
    const icons = {
      success: "la-check-circle",
      error: "la-exclamation-triangle",
      warning: "la-exclamation-circle",
      info: "la-info-circle",
    };
    return icons[type] || icons.info;
  }
}

// Initialize the integration
window.canvasStudioIntegration = new CanvasStudioIntegration();

// Export for module usage
if (typeof module !== "undefined" && module.exports) {
  module.exports = CanvasStudioIntegration;
}
