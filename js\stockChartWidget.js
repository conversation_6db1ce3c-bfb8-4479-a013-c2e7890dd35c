// Add a stock chart widget using amCharts v5

// Add a stock chart widget using amCharts v5
window.addStockChartWidget = function() {
  console.log("Adding stock chart widget");
  const chartId = "stock-chart-" + Date.now();

  // Get the grid instance
  const grid = document.querySelector(".grid-stack").gridstack;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 12,
    h: 10,
    content: `
      <div class="stock-chart-widget p-2" style="height: 100%; display: flex; flex-direction: column;">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
            Stock Chart
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#stockChartSettings"
                    aria-controls="stockChartSettings"
                    onclick="initStockChartSettings('${chartId}')">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
          <div id="${chartId}" class="chart-container" style="flex: 1; width: 100%; height: 100%; min-height: 400px; position: relative;"></div>
        </div>
      </div>
    `,
  });

  // Initialize the chart with a slight delay to ensure DOM is ready
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing chart");
      initStockChart(chartId);
    } catch (error) {
      console.error("Error initializing chart:", error);
    }
  }, 100);

  return widget;
};

// Initialize the stock chart
window.initStockChart = function(chartId) {
  console.log("Initializing stock chart:", chartId);

  // Get the chart container
  const chartContainer = document.getElementById(chartId);
  if (!chartContainer) {
    console.error("Chart container not found:", chartId);
    return;
  }

  // Force overflow visible on parent gridstack item content
  const gridStackItemContent = chartContainer.closest(".grid-stack-item-content");
  if (gridStackItemContent) {
    gridStackItemContent.style.overflow = "visible";
    gridStackItemContent.style.height = "100%";
  }

  // Get the widget container
  const widgetContainer = chartContainer.closest(".stock-chart-widget");
  if (widgetContainer) {
    widgetContainer.style.height = "100%";
    widgetContainer.style.display = "flex";
    widgetContainer.style.flexDirection = "column";
  }

  // Dispose of any existing chart instance
  if (chartContainer.chart) {
    chartContainer.chart.root.dispose();
  }

  // Clear the container
  chartContainer.innerHTML = "";

  try {
    // Create root element
    const root = am5.Root.new(chartId);

    // Set themes
    root.setThemes([am5themes_Animated.new(root)]);

    // Create a stock chart
    const stockChart = root.container.children.push(
      am5stock.StockChart.new(root, {})
    );

    // Apply brand colors if available
    if (window.chartConfig && window.chartConfig.brandColors) {
      // Create a color set with brand colors
      const brandColorSet = am5.ColorSet.new(root, {
        colors: window.chartConfig.brandColors.map(color => am5.color(color)),
        reuse: true
      });

      // Apply the color set to the stock chart
      stockChart.set("colors", brandColorSet);

      // Use specific brand colors for stock chart elements
      // Use the first brand color (#00b19c - teal) for positive values
      stockChart.set("stockPositiveColor", am5.color(window.chartConfig.brandColors[0]));

      // Use the third brand color (#007365 - dark teal) for negative values
      stockChart.set("stockNegativeColor", am5.color(window.chartConfig.brandColors[2]));

      // Use slightly lighter versions for volume by using opacity
      // Create lighter versions by using hex color with opacity
      const volumePositiveColor = am5.color(window.chartConfig.brandColors[0]);
      const volumeNegativeColor = am5.color(window.chartConfig.brandColors[2]);

      // Set the volume colors with the same colors but different opacity
      stockChart.set("volumePositiveColor", volumePositiveColor);
      stockChart.set("volumeNegativeColor", volumeNegativeColor);

      console.log("Applied brand colors to stock chart");
    } else {
      // Default brand colors if not available in window.chartConfig
      const defaultBrandColors = [
        "#00b19c", "#3bcd3f", "#007365", "#8dbac4", "#02104f"
      ];

      // Create a color set with default brand colors
      const brandColorSet = am5.ColorSet.new(root, {
        colors: defaultBrandColors.map(color => am5.color(color)),
        reuse: true
      });

      // Apply the color set to the stock chart
      stockChart.set("colors", brandColorSet);

      // Use specific default brand colors for stock chart elements
      stockChart.set("stockPositiveColor", am5.color(defaultBrandColors[0])); // Teal
      stockChart.set("stockNegativeColor", am5.color(defaultBrandColors[2])); // Dark teal

      // Use slightly lighter versions for volume by using opacity
      // Create lighter versions by using hex color with opacity
      const volumePositiveColor = am5.color(defaultBrandColors[0]);
      const volumeNegativeColor = am5.color(defaultBrandColors[2]);

      // Set the volume colors with the same colors but different opacity
      stockChart.set("volumePositiveColor", volumePositiveColor);
      stockChart.set("volumeNegativeColor", volumeNegativeColor);

      console.log("Applied default brand colors to stock chart");
    }

    // Create main panel (chart)
    const mainPanel = stockChart.panels.push(
      am5stock.StockPanel.new(root, {
        wheelY: "zoomX",
        panX: true,
        panY: true
      })
    );

    // Create value axis
    const valueAxis = mainPanel.yAxes.push(
      am5xy.ValueAxis.new(root, {
        renderer: am5xy.AxisRendererY.new(root, {})
      })
    );

    // Create date axis
    const dateAxis = mainPanel.xAxes.push(
      am5xy.GaplessDateAxis.new(root, {
        baseInterval: { timeUnit: "day", count: 1 },
        renderer: am5xy.AxisRendererX.new(root, {})
      })
    );

    // Create value series
    const valueSeries = mainPanel.series.push(
      am5xy.CandlestickSeries.new(root, {
        name: "Stock",
        valueXField: "Date",
        valueYField: "Close",
        highValueYField: "High",
        lowValueYField: "Low",
        openValueYField: "Open",
        calculateAggregates: true,
        xAxis: dateAxis,
        yAxis: valueAxis,
        legendValueText: "{valueY}",
        tooltip: am5.Tooltip.new(root, {
          pointerOrientation: "horizontal",
          labelText: "Open: {openValueY}\nHigh: {highValueY}\nLow: {lowValueY}\nClose: {valueY}"
        })
      })
    );

    // Apply custom styling to the candlestick series
    valueSeries.columns.template.states.create("hover", {
      strokeWidth: 3
    });

    // Set main series
    stockChart.set("stockSeries", valueSeries);

    // Add stock legend
    const valueLegend = mainPanel.plotContainer.children.push(
      am5stock.StockLegend.new(root, {
        stockChart: stockChart
      })
    );
    valueLegend.data.setAll([valueSeries]);

    // Create volume panel
    const volumePanel = stockChart.panels.push(
      am5stock.StockPanel.new(root, {
        wheelY: "zoomX",
        panX: true,
        panY: true,
        height: am5.percent(30)
      })
    );

    // Create volume axis
    const volumeValueAxis = volumePanel.yAxes.push(
      am5xy.ValueAxis.new(root, {
        numberFormat: "#.#a",
        renderer: am5xy.AxisRendererY.new(root, {})
      })
    );

    // Create volume date axis
    const volumeDateAxis = volumePanel.xAxes.push(
      am5xy.GaplessDateAxis.new(root, {
        baseInterval: { timeUnit: "day", count: 1 },
        renderer: am5xy.AxisRendererX.new(root, {})
      })
    );

    // Create volume series
    const volumeSeries = volumePanel.series.push(
      am5xy.ColumnSeries.new(root, {
        name: "Volume",
        valueXField: "Date",
        valueYField: "Volume",
        xAxis: volumeDateAxis,
        yAxis: volumeValueAxis,
        legendValueText: "{valueY}",
        tooltip: am5.Tooltip.new(root, {
          pointerOrientation: "horizontal",
          labelText: "Volume: {valueY}"
        })
      })
    );

    // Apply custom styling to the volume series
    volumeSeries.columns.template.setAll({
      strokeOpacity: 0,
      fillOpacity: 0.6 // Lower opacity for volume columns
    });

    // Add hover state
    volumeSeries.columns.template.states.create("hover", {
      fillOpacity: 0.8
    });

    // Set up a color adapter for the volume columns
    volumeSeries.columns.template.adapters.add("fill", function(fill, target) {
      if (target.dataItem) {
        // Get the data item
        const dataItem = target.dataItem;

        // Get the close and open values from the data item
        const close = dataItem.dataContext.Close;
        const open = dataItem.dataContext.Open;

        // Determine if it's a positive or negative day
        if (close >= open) {
          // Positive day - use the positive color
          return stockChart.get("volumePositiveColor");
        } else {
          // Negative day - use the negative color
          return stockChart.get("volumeNegativeColor");
        }
      }
      return fill;
    });

    // Set volume series
    stockChart.set("volumeSeries", volumeSeries);

    // Add volume legend
    const volumeLegend = volumePanel.plotContainer.children.push(
      am5stock.StockLegend.new(root, {
        stockChart: stockChart
      })
    );
    volumeLegend.data.setAll([volumeSeries]);

    // Add cursor to both panels
    mainPanel.set("cursor", am5xy.XYCursor.new(root, {
      yAxis: valueAxis,
      xAxis: dateAxis,
      snapToSeries: [valueSeries],
      snapToSeriesBy: "y!"
    }));

    volumePanel.set("cursor", am5xy.XYCursor.new(root, {
      yAxis: volumeValueAxis,
      xAxis: volumeDateAxis,
      snapToSeries: [volumeSeries],
      snapToSeriesBy: "y!"
    }));

    // Add scrollbar
    const scrollbar = mainPanel.set("scrollbarX", am5xy.XYChartScrollbar.new(root, {
      orientation: "horizontal",
      height: 50
    }));
    stockChart.toolsContainer.children.push(scrollbar);

    // Style the scrollbar to match brand colors
    if (window.chartConfig && window.chartConfig.brandColors) {
      // Use the fifth brand color (#02104f - dark blue) for the scrollbar grips
      // This provides good contrast and visibility
      scrollbar.startGrip.get("icon").setAll({
        stroke: am5.color(window.chartConfig.brandColors[4]),
        strokeWidth: 2
      });
      scrollbar.endGrip.get("icon").setAll({
        stroke: am5.color(window.chartConfig.brandColors[4]),
        strokeWidth: 2
      });

      // Style the grip handles
      scrollbar.startGrip.get("background").setAll({
        fill: am5.color(window.chartConfig.brandColors[4]),
        fillOpacity: 0.2
      });
      scrollbar.endGrip.get("background").setAll({
        fill: am5.color(window.chartConfig.brandColors[4]),
        fillOpacity: 0.2
      });
    }

    // Configure scrollbar
    const sbDateAxis = scrollbar.chart.xAxes.push(
      am5xy.GaplessDateAxis.new(root, {
        baseInterval: { timeUnit: "day", count: 1 },
        renderer: am5xy.AxisRendererX.new(root, {})
      })
    );

    const sbValueAxis = scrollbar.chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        renderer: am5xy.AxisRendererY.new(root, {})
      })
    );

    const sbSeries = scrollbar.chart.series.push(
      am5xy.LineSeries.new(root, {
        valueYField: "Close",
        valueXField: "Date",
        xAxis: sbDateAxis,
        yAxis: sbValueAxis
      })
    );

    // Style the scrollbar series
    sbSeries.fills.template.setAll({
      visible: true,
      fillOpacity: 0.3
    });

    sbSeries.strokes.template.setAll({
      strokeWidth: 1
    });

    // Apply brand colors to scrollbar series if available
    if (window.chartConfig && window.chartConfig.brandColors) {
      // Use the fourth brand color (#8dbac4 - pale blue) for the scrollbar
      // This provides good contrast with the main chart colors
      sbSeries.fills.template.setAll({
        fill: am5.color(window.chartConfig.brandColors[3]),
        fillOpacity: 0.3,
        visible: true
      });

      sbSeries.strokes.template.setAll({
        stroke: am5.color(window.chartConfig.brandColors[3]),
        strokeWidth: 1
      });
    }

    // Generate sample data
    const data = generateSampleStockData();

    // Set data
    valueSeries.data.setAll(data);
    volumeSeries.data.setAll(data);
    sbSeries.data.setAll(data);

    // Make stuff animate on load
    valueSeries.appear(1000);
    volumeSeries.appear(1000);
    mainPanel.appear(1000, 100);
    volumePanel.appear(1000, 100);

    // Store chart instance on container for future reference
    chartContainer.chart = stockChart;

    return stockChart;
  } catch (error) {
    console.error("Error creating stock chart:", error);
    chartContainer.innerHTML = `<div class="alert alert-danger">Error creating chart: ${error.message}</div>`;
  }
};

// Function to generate sample stock data
function generateSampleStockData() {
  const data = [];
  const startDate = new Date(2023, 0, 1); // January 1, 2023

  let price = 100;

  for (let i = 0; i < 60; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);

    // Generate random price movements
    const change = (Math.random() - 0.5) * 5;
    const open = price;
    price = Math.max(10, price + change);
    const close = price;
    const high = Math.max(open, close) + Math.random() * 2;
    const low = Math.min(open, close) - Math.random() * 2;

    // Generate random volume
    const volume = Math.round(100000 + Math.random() * 900000);

    data.push({
      Date: date.getTime(),
      Open: open,
      High: high,
      Low: low,
      Close: close,
      Volume: volume
    });
  }

  return data;
}

// Function to initialize settings panel for the chart
window.initStockChartSettings = function(chartId) {
  console.log("Initializing settings for stock chart:", chartId);

  const settingsPanel = document.getElementById("stockChartSettings");
  if (!settingsPanel) {
    console.error("Settings panel not found");
    return;
  }

  // Store the current chart ID
  settingsPanel.dataset.currentChart = chartId;

  // Initialize offcanvas with proper options
  const bsOffcanvas = new bootstrap.Offcanvas(settingsPanel, {
    backdrop: true,
    keyboard: true,
    scroll: false,
  });

  // Remove any existing event listeners
  settingsPanel.removeEventListener(
    "hidden.bs.offcanvas",
    handleBackdropCleanup
  );
  // Add event listener for when offcanvas is hidden
  settingsPanel.addEventListener("hidden.bs.offcanvas", handleBackdropCleanup);

  // Show the offcanvas
  bsOffcanvas.show();

  // Load current chart settings
  loadStockChartSettings(chartId);
};

// Function to load current chart settings
function loadStockChartSettings(chartId) {
  const chartContainer = document.getElementById(chartId);
  if (!chartContainer || !chartContainer.chart) {
    console.error("Chart not found or not initialized");
    return;
  }

  const stockChart = chartContainer.chart;

  // Get chart title
  document.getElementById("stockChartSettings-chartTitle").value = "Stock Price";

  // Get chart type
  const mainPanel = stockChart.panels.getIndex(0);
  if (mainPanel) {
    const series = mainPanel.series.getIndex(0);
    if (series) {
      const seriesType = series.className;
      if (seriesType.includes("Candlestick")) {
        document.getElementById("stockChartSettings-chartType").value = "candlestick";
      } else if (seriesType.includes("OHLC")) {
        document.getElementById("stockChartSettings-chartType").value = "ohlc";
      } else {
        document.getElementById("stockChartSettings-chartType").value = "line";
      }
    }
  }

  // Get volume panel visibility
  const volumePanel = stockChart.panels.getIndex(1);
  if (volumePanel) {
    document.getElementById("stockChartSettings-showVolume").checked = true;
  } else {
    document.getElementById("stockChartSettings-showVolume").checked = false;
  }
}

// Function to apply settings from the settings panel
window.applyStockChartSettings = function() {
  console.log("Applying stock chart settings");

  const settingsPanel = document.getElementById("stockChartSettings");
  const chartId = settingsPanel.dataset.currentChart;

  if (!chartId) {
    console.error("No chart ID found in settings panel");
    return;
  }

  // Get settings values
  const chartTitle = document.getElementById("stockChartSettings-chartTitle").value;
  const chartType = document.getElementById("stockChartSettings-chartType").value;
  const showVolume = document.getElementById("stockChartSettings-showVolume").checked;

  // Re-initialize the chart with new settings
  // For simplicity, we'll just re-create the chart with the new settings
  initStockChart(chartId);

  // Close the settings panel
  const bsOffcanvas = bootstrap.Offcanvas.getInstance(settingsPanel);
  if (bsOffcanvas) {
    bsOffcanvas.hide();
  }
};

// Add the widget to the widget gallery
document.addEventListener("DOMContentLoaded", function () {
  // Add widget to the chart category in the widget gallery
  const chartCategory = document.querySelector('.widget-gallery[data-category="charts"]');
  if (chartCategory) {
    const widgetItem = document.createElement("div");
    widgetItem.className = "widget-item";
    widgetItem.setAttribute("draggable", "true");
    widgetItem.setAttribute("data-gs-width", "12");
    widgetItem.setAttribute("data-gs-height", "10");
    widgetItem.innerHTML = `
      <i class="las la-chart-line"></i>
      <span class="widget-label">Stock Chart</span>
    `;
    widgetItem.addEventListener("click", function () {
      addStockChartWidget();
    });
    chartCategory.appendChild(widgetItem);
  }
});
