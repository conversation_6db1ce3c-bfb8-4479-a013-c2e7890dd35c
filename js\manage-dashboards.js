// Dashboard data state
let dashboardsState = {
  view: "grid", // 'grid' or 'list'
  sortBy: "lastEdited", // 'lastEdited', 'name', 'views'
  sortOrder: "desc", // 'asc' or 'desc'
  filters: [],
  searchQuery: "",
};

// Initialize
document.addEventListener("DOMContentLoaded", () => {
  initializeSearch();
  initializeFilters();
  initializeSort();
  initializeViewSwitch();
  initializeActionButtons();
});

// Initialize action buttons
function initializeActionButtons() {
  const editButtons = document.querySelectorAll(".action-menu-item");
  editButtons.forEach((button) => {
    if (button.textContent.trim() === "Edit Dashboard") {
      button.addEventListener("click", () => {
        // Get the dashboard title from the closest dashboard card
        const dashboardCard = button.closest(".dashboard-card");
        const dashboardTitle = dashboardCard.querySelector("h3").textContent;

        // Encode the dashboard title for URL
        const encodedTitle = encodeURIComponent(dashboardTitle);

        // Navigate to edit-dashboard.html with the dashboard title as a parameter
        window.location.href = `edit-dashboard.html?title=${encodedTitle}`;
      });
    }
  });
}

// Search functionality
function initializeSearch() {
  const searchInput = document.querySelector(".search-box input");
  searchInput.addEventListener("input", (e) => {
    dashboardsState.searchQuery = e.target.value.toLowerCase();
    filterDashboards();
  });
}

// Filter functionality
function initializeFilters() {
  const filterButtons = document.querySelectorAll(".filter-btn");
  filterButtons.forEach((button) => {
    button.addEventListener("click", (e) => {
      const filter = e.currentTarget.dataset.filter;
      toggleFilter(filter);
      updateFilterUI();
      filterDashboards();
    });
  });
}

function toggleFilter(filter) {
  const index = dashboardsState.filters.indexOf(filter);
  if (index === -1) {
    dashboardsState.filters.push(filter);
  } else {
    dashboardsState.filters.splice(index, 1);
  }
}

function updateFilterUI() {
  const filterButtons = document.querySelectorAll(".filter-btn");
  filterButtons.forEach((button) => {
    const filter = button.dataset.filter;
    if (dashboardsState.filters.includes(filter)) {
      button.classList.add("active");
    } else {
      button.classList.remove("active");
    }
  });
}

// Sort functionality
function initializeSort() {
  const sortButton = document.querySelector(".sort-btn");
  sortButton.addEventListener("click", () => {
    // Toggle sort order if clicking the same sort type
    if (dashboardsState.sortBy === sortButton.dataset.sort) {
      dashboardsState.sortOrder =
        dashboardsState.sortOrder === "asc" ? "desc" : "asc";
    } else {
      dashboardsState.sortBy = sortButton.dataset.sort;
      dashboardsState.sortOrder = "desc";
    }
    updateSortUI();
    sortDashboards();
  });
}

function updateSortUI() {
  const sortButton = document.querySelector(".sort-btn");
  const icon = sortButton.querySelector("i");
  icon.className = `las ${
    dashboardsState.sortOrder === "asc" ? "la-sort-up" : "la-sort-down"
  }`;
}

// View switching functionality
function initializeViewSwitch() {
  const viewButtons = document.querySelectorAll(".view-btn");
  viewButtons.forEach((button) => {
    button.addEventListener("click", (e) => {
      const view = e.currentTarget.dataset.view;
      switchView(view);
    });
  });
}

function switchView(view) {
  dashboardsState.view = view;
  updateViewUI();
  updateLayoutView();
}

function updateViewUI() {
  const viewButtons = document.querySelectorAll(".view-btn");
  viewButtons.forEach((button) => {
    if (button.dataset.view === dashboardsState.view) {
      button.classList.add("active");
    } else {
      button.classList.remove("active");
    }
  });
}

function updateLayoutView() {
  const dashboardsGrid = document.querySelector(".dashboards-grid");
  dashboardsGrid.className = `dashboards-grid ${dashboardsState.view}-view`;
}

// Filter dashboards based on search and filters
function filterDashboards() {
  const dashboardCards = document.querySelectorAll(
    ".dashboard-card:not(.add-card)"
  );

  dashboardCards.forEach((card) => {
    const title = card.querySelector("h3").textContent.toLowerCase();
    const tags = Array.from(card.querySelectorAll(".tag")).map((tag) =>
      tag.textContent.toLowerCase().trim()
    );

    // Search matching
    const matchesSearch =
      dashboardsState.searchQuery === "" ||
      title.includes(dashboardsState.searchQuery) ||
      tags.some((tag) => tag.includes(dashboardsState.searchQuery));

    // Filter matching - convert filter to match tag format
    const matchesFilters =
      dashboardsState.filters.length === 0 ||
      dashboardsState.filters.some((filter) => {
        const normalizedFilter = filter.toLowerCase().replace(/-/g, " ");
        return tags.some((tag) => tag === normalizedFilter);
      });

    card.style.display = matchesSearch && matchesFilters ? "flex" : "none";
  });

  // Update filter badges
  updateFilterBadges();
}

// Update filter badges
function updateFilterBadges() {
  const dashboardCards = document.querySelectorAll(
    ".dashboard-card:not(.add-card)"
  );
  const filterButtons = document.querySelectorAll(".filter-btn");
  const tagCounts = {};

  // Count visible cards for each tag
  dashboardCards.forEach((card) => {
    if (card.style.display !== "none") {
      const cardTags = Array.from(card.querySelectorAll(".tag")).map((tag) =>
        tag.textContent.toLowerCase().trim()
      );

      cardTags.forEach((tag) => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
      });
    }
  });

  // Update badge numbers
  filterButtons.forEach((button) => {
    const filterName = button.dataset.filter.toLowerCase().replace(/-/g, " ");
    const badge = button.querySelector(".filter-badge");
    if (badge) {
      const count = tagCounts[filterName] || 0;
      badge.textContent = count;
    }
  });
}

// Sort dashboards
function sortDashboards() {
  const dashboardsGrid = document.querySelector(".dashboards-grid");
  const dashboardCards = Array.from(
    document.querySelectorAll(".dashboard-card:not(.add-card)")
  );
  const addCard = document.querySelector(".add-card");

  dashboardCards.sort((a, b) => {
    let valueA, valueB;

    switch (dashboardsState.sortBy) {
      case "name":
        valueA = a.querySelector("h3").textContent;
        valueB = b.querySelector("h3").textContent;
        break;
      case "views":
        valueA = parseInt(a.querySelector(".stat span").textContent);
        valueB = parseInt(b.querySelector(".stat span").textContent);
        break;
      case "lastEdited":
      default:
        valueA = a.querySelector(".last-edited").textContent;
        valueB = b.querySelector(".last-edited").textContent;
        break;
    }

    if (dashboardsState.sortOrder === "asc") {
      return valueA > valueB ? 1 : -1;
    } else {
      return valueA < valueB ? 1 : -1;
    }
  });

  // Clear and re-append sorted cards
  dashboardsGrid.innerHTML = "";
  dashboardCards.forEach((card) => dashboardsGrid.appendChild(card));
  if (addCard) {
    dashboardsGrid.appendChild(addCard); // Always keep add card at the end
  }
}
