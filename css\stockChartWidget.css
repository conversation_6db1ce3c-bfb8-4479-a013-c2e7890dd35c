/* Specific fixes for stock chart height issues */

/* Override widget-height-fix.css styles */
.stock-chart-widget {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.stock-chart-widget .widget-body {
  flex: 1 1 auto !important;
  min-height: 0 !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
}

.stock-chart-widget .chart-container {
  flex: 1 !important;
  height: 100% !important;
  min-height: 400px !important;
  position: relative !important;
}

/* Ensure grid stack items containing stock charts have proper height */
.grid-stack-item-content:has(.stock-chart-widget) {
  height: 100% !important;
  overflow: visible !important;
}

/* Stock chart specific styles */
.am5stock-control-button {
  cursor: pointer;
}

.am5stock-list-item {
  cursor: pointer;
}

.am5stock-list-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Make sure the stock chart toolbar is visible */
.am5stock-toolbar {
  z-index: 10;
}

/* Ensure the stock chart panels have proper spacing */
.am5stock-chart-panel {
  margin-bottom: 5px;
}
