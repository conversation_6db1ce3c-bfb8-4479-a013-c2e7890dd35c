/* Widget Toolbar Styles */
:root {
  --font-size-base: 12px;
  --font-size-sm: 11px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
}

.widget-toolbar {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.widget-categories {
  display: flex;
  gap: 2rem;
}

.widget-category {
  flex: 1;
}

.category-title {
  font-size: var(--font-size-md);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.widget-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 1rem;
}

.widget-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--background-secondary);
}

.widget-item:hover {
  transform: translateY(-2px);
  background: var(--primary-color);
}

.widget-item:hover .widget-icon,
.widget-item:hover .widget-label {
  color: white;
}

/* Widget Styles */
.widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--background-secondary);
  border-bottom: 1px solid var(--border-color);
}

.widget-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
}

.widget-title i {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
}

.widget-actions {
  display: flex;
  gap: 0.5rem;
}

.widget-actions .btn-link {
  color: var(--text-secondary);
  padding: 0.25rem;
  font-size: var(--font-size-md);
}

.widget-actions .btn-link:hover {
  color: var(--primary-color);
}

.widget-body {
  flex: 1;
  padding: 1rem;
  overflow: auto;
}

/* Chart Container */
.chart-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
}

/* PDF Container */
.pdf-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
  background: var(--background-secondary);
  border-radius: 4px;
}

/* Grid Layout */
.dashboard-grid {
  margin-top: 1rem;
}

.grid-stack {
  background: var(--background-secondary);
  border-radius: 8px;
  padding: 1rem;
}

.grid-stack-item-content {
  inset: 0.5rem;
  position: relative;
  overflow: visible;
  height: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .widget-categories {
    flex-direction: column;
    gap: 1.5rem;
  }

  .widget-items {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.75rem;
  }

  .widget-item {
    padding: 0.75rem;
  }

  .widget-title {
    font-size: var(--font-size-base);
  }

  .widget-title i {
    font-size: var(--font-size-md);
  }

  .widget-actions .btn-link {
    font-size: var(--font-size-base);
  }
}

/* Dark Theme Support */
body.dark-theme .widget-toolbar,
body.dark-theme .widget {
  background: var(--secondary-color);
}

body.dark-theme .widget-header {
  background: rgba(0, 0, 0, 0.1);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme .widget-title {
  color: white;
}

body.dark-theme .widget-actions .btn-link {
  color: rgba(255, 255, 255, 0.7);
}

body.dark-theme .widget-actions .btn-link:hover {
  color: white;
}

body.dark-theme .pdf-container {
  background: rgba(0, 0, 0, 0.2);
}

/* Dashboard Header Styles */
.dashboard-header {
  margin-bottom: 2rem;
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.dashboard-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.dashboard-title-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.dashboard-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dashboard-title i {
  font-size: var(--font-size-lg);
  color: var(--primary-color);
}

.dashboard-subtitle {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
}

.dashboard-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.dashboard-actions button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: var(--font-size-base);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dashboard-actions .btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
}

.dashboard-actions .btn-primary:hover {
  background: var(--primary-color-dark);
}

.dashboard-actions .btn-secondary {
  background: var(--background-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.dashboard-actions .btn-secondary:hover {
  background: var(--background-secondary-dark);
}

.dashboard-metadata {
  display: flex;
  gap: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.metadata-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.metadata-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.metadata-value {
  font-size: var(--font-size-base);
  color: var(--text-primary);
  font-weight: 500;
}

/* Dark Theme Support */
body.dark-theme .dashboard-header {
  background: var(--secondary-color);
}

body.dark-theme .dashboard-title {
  color: white;
}

body.dark-theme .dashboard-subtitle {
  color: rgba(255, 255, 255, 0.7);
}

body.dark-theme .dashboard-actions .btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.1);
  color: white;
}

body.dark-theme .dashboard-actions .btn-secondary:hover {
  background: rgba(255, 255, 255, 0.15);
}

body.dark-theme .metadata-label {
  color: rgba(255, 255, 255, 0.6);
}

body.dark-theme .metadata-value {
  color: rgba(255, 255, 255, 0.9);
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .dashboard-title-section {
    flex-direction: column;
    gap: 1rem;
  }

  .dashboard-title {
    font-size: var(--font-size-lg);
  }

  .dashboard-title i {
    font-size: var(--font-size-md);
  }

  .dashboard-actions {
    width: 100%;
    flex-wrap: wrap;
  }

  .dashboard-actions button {
    flex: 1;
    justify-content: center;
  }

  .dashboard-metadata {
    flex-direction: column;
    gap: 1rem;
  }
}

.grid-stack > .grid-stack-item > .grid-stack-item-content {
  margin: 0;
  position: absolute;
  width: auto;
  overflow: visible;
  padding: 0.75rem;
  height: auto;
}
