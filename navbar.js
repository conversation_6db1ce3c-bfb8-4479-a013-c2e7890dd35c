document.write(`<nav class="navbar navbar-expand-lg navbar-dark bg-primary1 themeBorderBottom py-0" id="tsc_nav_1">
    <a class="navbar-brand" href="#">
        <div class="font-size14 logo-text">
            <img src="https://d29wfqajwlhhm6.cloudfront.net/Amplifipro-UAT/logo.svg" style="height: 30px;"
                onclick="window.location.href='https://amplifipro-qa.tsclabs.in'">
        </div>
    </a>

    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavDropdown"
        aria-controls="navbarNavDropdown" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>

    <div class="collapse navbar-collapse justify-content-end" id="navbarNavDropdown">
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" href="https://amplifipro-qa.tsclabs.in/tschome">Home</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" target="_blank" href="https://amplifipro-qa.tsclabs.in/clear-cache">Clear cache</a>
            </li>

            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="manageContentDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    Manage Content
                </a>
                <ul class="dropdown-menu" aria-labelledby="manageContentDropdown">
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/banner">Manage banners</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/collateral">Manage collateral</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/reportList">Manage reports</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/manage-ci">Manage CI details</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/manage-report-details">Manage reports details</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/ManageThemes">Manage procurement themes</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/productReportMapping">Product report mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/categoryWidgetMapping">Category insights & widget mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/smartriskcategorymapping">Manage Smartrisk category mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/smartrisksuppliermapping">Manage Smartrisk supplier mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/industryCodeMapping">Industry codes mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/commodityWidgetMapping">Client-wise dashboard and widget mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/cost-calculator-upload">Manage cost calculator data</a></li>
                </ul>
            </li>

            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="manageTierDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    Manage Tier
                </a>
                <ul class="dropdown-menu" aria-labelledby="manageTierDropdown">
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/tier">Manage tier master</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/listClientTierMapping">Client configuration (client & tier mapping)</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/manageClientApi">Manage client API limit</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/downloadClientApiUses">Download API usage report</a></li>
                </ul>
            </li>

            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="manageCategoryDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    Manage Category & Mapping
                </a>
                <ul class="dropdown-menu" aria-labelledby="manageCategoryDropdown">
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/category">Manage category</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/categoryReportMapping">Manage category report mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/categoryCSMapping">Manage category cs mapping</a></li>
                    <li><a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/tschome/categoryCommodityIdMappings">Manage category & dashboard mapping</a></li>
                </ul>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="https://amplifipro-qa.tsclabs.in/tschome/tagMasterList">Manage tags</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="https://amplifipro-qa.tsclabs.in/tschome/jobs">Manage Jobs</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="https://amplifipro-qa.tsclabs.in/tschome/addUserRoleMapping">Manage user role mapping</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" target="_blank" href="https://suchna.tsclabs.in/">Notifications</a>
            </li>

            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fa fa-user"></i> Md Qaisar
                </a>
                <ul class="dropdown-menu" aria-labelledby="userDropdown">
                    <li>
                        <a class="dropdown-item" href="https://amplifipro-qa.tsclabs.in/logout" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">Logout</a>
                        <form id="logout-form" action="https://amplifipro-qa.tsclabs.in/logout" method="POST" style="display: none;">
                            <input type="hidden" name="_token" value="8rzjr5thxFJ3PIUsEFzlJjewv2KAi3vkCe93IYpY" autocomplete="off">
                        </form>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
  </nav>`);
