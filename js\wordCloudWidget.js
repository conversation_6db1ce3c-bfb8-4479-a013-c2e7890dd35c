// Add a Word Cloud widget using amCharts v5
function addWordCloudWidget() {
  console.log("Adding word cloud widget");
  const chartId = "wordcloud-" + Date.now();
  const settingsId = "settings-" + chartId;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 8,
    content: `
      <div class="word-cloud-widget p-2">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
            Word Cloud
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#wordCloudSettings"
                    aria-controls="wordCloudSettings"
                    onclick="initWordCloudSettings('${chartId}')">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div id="${chartId}" class="chart-container"></div>
      </div>
    `,
  });

  // Initialize the chart with a slight delay
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing word cloud");
      window.initWordCloud(chartId);
    } catch (error) {
      console.error("Error initializing word cloud:", error);
    }
  }, 1000);

  return widget;
}

// Initialize a word cloud using amCharts v5
window.initWordCloud = function (containerId) {
  console.log("Starting word cloud initialization for container:", containerId);
  const container = document.getElementById(containerId);

  if (!container) {
    console.error("Word cloud container not found:", containerId);
    return;
  }

  // Make sure the container has dimensions
  if (container.offsetWidth === 0 || container.offsetHeight === 0) {
    console.warn(
      "Word cloud container has zero dimensions, setting explicit size"
    );
    container.style.width = "100%";
    container.style.height = "300px";
  }

  console.log(
    "Container dimensions:",
    container.offsetWidth,
    "x",
    container.offsetHeight
  );

  // Clear any existing chart
  container.innerHTML = "";

  // Create root element
  const root = am5.Root.new(containerId);

  // Set themes
  root.setThemes([am5themes_Animated.new(root)]);

  // Create series
  const series = root.container.children.push(
    am5wc.WordCloud.new(root, {
      minFontSize: am5.percent(2),
      maxFontSize: am5.percent(20),
      randomness: 0.2,
      angles: [0, -90], // Horizontal and vertical words
      categoryField: "category",
      valueField: "value",
      colors: am5.ColorSet.new(root, {
        colors: window.chartConfig.brandColors.map((color) => am5.color(color)),
      }),
      tooltipText: "{category}: [bold]{value}[/]",
    })
  );

  // Set data
  series.data.setAll([
    { category: "Digital", value: 65 },
    { category: "Innovation", value: 59 },
    { category: "Technology", value: 54 },
    { category: "Analytics", value: 48 },
    { category: "Data", value: 45 },
    { category: "Transformation", value: 41 },
    { category: "Cloud", value: 39 },
    { category: "Solutions", value: 37 },
    { category: "Strategy", value: 35 },
    { category: "Business", value: 33 },
    { category: "Intelligence", value: 31 },
    { category: "Automation", value: 29 },
    { category: "Insights", value: 27 },
    { category: "AI", value: 25 },
    { category: "Machine Learning", value: 23 },
    { category: "Consulting", value: 21 },
    { category: "Enterprise", value: 19 },
    { category: "Services", value: 17 },
    { category: "Platform", value: 15 },
    { category: "Experience", value: 13 },
  ]);

  // Store chart instance in container for later access
  container.chart = {
    root: root,
    series: series,
    settings: {
      title: "Word Cloud",
      minFontSize: 2,
      maxFontSize: 20,
      randomness: 0.2,
      rotation: "mixed",
      text: "",
    },
  };

  return container.chart;
};

// Initialize the word cloud settings panel
window.initWordCloudSettings = function (chartId) {
  console.log("Initializing word cloud settings for:", chartId);
  const container = document.getElementById(chartId);

  if (!container || !container.chart) {
    console.error("Chart container or chart instance not found");
    return;
  }

  const chart = container.chart;
  const settings = chart.settings;

  // Set current values in settings panel
  document.getElementById("wordCloudSettings-chartTitle").value =
    settings.title || "Word Cloud";
  document.getElementById("wordCloudSettings-minFontSize").value =
    settings.minFontSize || 2;
  document.getElementById("wordCloudSettings-maxFontSize").value =
    settings.maxFontSize || 20;
  document.getElementById("wordCloudSettings-randomness").value =
    settings.randomness || 0.2;
  document.getElementById("wordCloudSettings-rotation").value =
    settings.rotation || "mixed";
  document.getElementById("wordCloudSettings-text").value = settings.text || "";

  // Clear existing data rows
  const dataBody = document.getElementById("wordCloudDataBody");
  dataBody.innerHTML = "";

  // Add data rows from chart
  const data = chart.series.data.values;
  data.forEach((item, index) => {
    addWordCloudDataRow(item.category, item.value);
  });

  // Store the chart ID for later use when applying settings
  document.getElementById("wordCloudSettings").dataset.chartId = chartId;
};

// Add a new row to the word cloud data table
window.addNewWordCloudDataRow = function () {
  addWordCloudDataRow("", 1);
};

// Helper function to add a data row with specified values
function addWordCloudDataRow(word, weight) {
  const dataBody = document.getElementById("wordCloudDataBody");
  const rowId =
    "wordcloud-row-" + Date.now() + "-" + Math.floor(Math.random() * 1000);

  const row = document.createElement("tr");
  row.id = rowId;
  row.innerHTML = `
    <td>
      <input type="text" class="form-control form-control-sm word-input" value="${word}">
    </td>
    <td>
      <input type="number" class="form-control form-control-sm weight-input" min="1" value="${weight}">
    </td>
    <td>
      <button class="btn btn-sm btn-outline-danger" onclick="removeWordCloudDataRow('${rowId}')">
        <i class="las la-trash"></i>
      </button>
    </td>
  `;

  dataBody.appendChild(row);
}

// Remove a row from the word cloud data table
window.removeWordCloudDataRow = function (rowId) {
  const row = document.getElementById(rowId);
  if (row) {
    row.remove();
  }
};

// Apply word cloud settings
window.applyWordCloudSettings = function () {
  const settingsPanel = document.getElementById("wordCloudSettings");
  const chartId = settingsPanel.dataset.chartId;
  const container = document.getElementById(chartId);

  if (!container || !container.chart) {
    console.error("Chart container or chart instance not found");
    return;
  }

  const chart = container.chart;

  // Get settings values
  const title = document.getElementById("wordCloudSettings-chartTitle").value;
  const minFontSize = parseFloat(
    document.getElementById("wordCloudSettings-minFontSize").value
  );
  const maxFontSize = parseFloat(
    document.getElementById("wordCloudSettings-maxFontSize").value
  );
  const randomness = parseFloat(
    document.getElementById("wordCloudSettings-randomness").value
  );
  const rotation = document.getElementById("wordCloudSettings-rotation").value;
  const text = document.getElementById("wordCloudSettings-text").value;

  // Update settings in chart object
  chart.settings.title = title;
  chart.settings.minFontSize = minFontSize;
  chart.settings.maxFontSize = maxFontSize;
  chart.settings.randomness = randomness;
  chart.settings.rotation = rotation;
  chart.settings.text = text;

  // Update chart title in the DOM
  const titleElement = container.parentElement.querySelector(
    ".widget-header div:first-child"
  );
  if (titleElement) {
    titleElement.innerHTML = `<i class="las la-cloud"></i> ${title}`;
  }

  // Update chart settings
  chart.series.set("minFontSize", am5.percent(minFontSize));
  chart.series.set("maxFontSize", am5.percent(maxFontSize));
  chart.series.set("randomness", randomness);

  // Set rotation angles based on selection
  if (rotation === "horizontal") {
    chart.series.set("angles", [0]);
  } else if (rotation === "vertical") {
    chart.series.set("angles", [90]);
  } else {
    chart.series.set("angles", [0, -90]);
  }

  // Collect data from the table
  const dataRows = document.querySelectorAll("#wordCloudDataBody tr");
  const newData = [];

  dataRows.forEach((row) => {
    const wordInput = row.querySelector(".word-input");
    const weightInput = row.querySelector(".weight-input");

    if (wordInput && weightInput && wordInput.value.trim()) {
      newData.push({
        category: wordInput.value.trim(),
        value: parseInt(weightInput.value) || 1,
      });
    }
  });

  // If text is provided, use it instead of the table data
  if (text.trim()) {
    chart.series.set("text", text);
    // Clear any existing data
    chart.series.data.clear();
  } else if (newData.length > 0) {
    // Otherwise use the table data
    chart.series.data.setAll(newData);
  }

  // Hide the settings panel
  const offcanvas = bootstrap.Offcanvas.getInstance(settingsPanel);
  if (offcanvas) {
    offcanvas.hide();
  }
};

// Import data from a file
window.importWordCloudData = function () {
  const fileInput = document.getElementById("wordCloudDataFile");
  const file = fileInput.files[0];

  if (!file) {
    alert("Please select a file to import");
    return;
  }

  const reader = new FileReader();

  reader.onload = function (e) {
    const contents = e.target.result;
    const extension = file.name.split(".").pop().toLowerCase();

    if (extension === "csv") {
      // Process CSV
      const rows = contents.split("\n");

      // Clear existing data
      document.getElementById("wordCloudDataBody").innerHTML = "";

      rows.forEach((row) => {
        if (row.trim()) {
          const [word, weight] = row.split(",");
          if (word && word.trim()) {
            addWordCloudDataRow(word.trim(), parseInt(weight) || 1);
          }
        }
      });
    } else if (extension === "txt") {
      // Process text file as raw text
      document.getElementById("wordCloudSettings-text").value = contents;
    }
  };

  reader.readAsText(file);
};

// Export data to CSV
window.exportWordCloudData = function () {
  const dataRows = document.querySelectorAll("#wordCloudDataBody tr");
  let csvContent = "Word,Weight\n";

  dataRows.forEach((row) => {
    const wordInput = row.querySelector(".word-input");
    const weightInput = row.querySelector(".weight-input");

    if (wordInput && weightInput && wordInput.value.trim()) {
      csvContent += `${wordInput.value.trim()},${
        parseInt(weightInput.value) || 1
      }\n`;
    }
  });

  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");

  link.setAttribute("href", url);
  link.setAttribute("download", "word-cloud-data.csv");
  link.style.visibility = "hidden";

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
