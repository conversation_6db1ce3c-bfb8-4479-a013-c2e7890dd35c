/**
 * Simplified Section Container for Test Widgets
 * 
 * This file provides basic functionality for creating test sections
 * with text and table widgets.
 * 
 * NOTE: Auto-loading is DISABLED - sections will not be created automatically
 * Use createTestSectionWithWidgets() function to create sections manually
 * 
 * <AUTHOR> Thapliyal
 * @version 2.0 (Simplified)
 * @date 2025
 */

// ============================================================================
// CONFIGURATION
// ============================================================================

const NESTED_GRID_CONFIG = {
    column: 12,
    cellHeight: 60,
    margin: 5,
    float: true,
    children: [],
    removable: true,
    removeTimeout: 0,
    disableOneColumnMode: true,
    animate: false,
    sizeToContent: false
};

// ============================================================================
// MAIN FUNCTIONS
// ============================================================================

/**
 * Creates a test section with text and table widgets
 * @param {string} sectionId - Optional section ID
 */
function createTestSectionWithWidgets(sectionId) {
    // Ensure the main grid is initialized
    if (!window.grid) {
        console.error("Main grid not initialized. Please wait for page to load completely.");
        return;
    }

    const widget = window.grid.addWidget({
        x: 0,
        y: 0,
        w: 12,
        h: 8,
        content: `
            <div class="section-container-widget p-2" style="height: 100%; overflow: hidden;">
                <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
                    <div>
                        <i class="las la-layer-group"></i> Test Widgets Section
                    </div>
                    <div>
                        <button class="btn btn-sm btn-link text-dark ms-1" onclick="removeSectionContainer(this)">
                            <i class="las la-times"></i>
                        </button>
                    </div>
                </div>
                <div class="nested-grid-container" style="height: calc(100% - 40px); overflow: hidden;"></div>
            </div>
        `
    });

    setTimeout(() => {
        const nestedGridContainer = widget.querySelector(".nested-grid-container");
        const nestedGridElement = document.createElement("div");
        nestedGridElement.className = "grid-stack";
        nestedGridContainer.appendChild(nestedGridElement);

        const nestedGrid = GridStack.init(NESTED_GRID_CONFIG, nestedGridElement);
        nestedGridElement.gridstack = nestedGrid;

        // Generate unique IDs for widgets
        const textWidgetId = `text-${Date.now()}`;
        const tableWidgetId = `table-${Date.now()}`;

        // Add widgets to the nested grid
        addTextWidgetToNestedGrid(nestedGrid, textWidgetId);
        addTableWidgetToNestedGrid(nestedGrid, tableWidgetId);
    }, 100);
}

/**
 * Adds a text widget to the nested grid (for section containers)
 */
function addTextWidgetToNestedGrid(nestedGrid, widgetId) {
    if (!nestedGrid || typeof nestedGrid.addWidget !== 'function') {
        console.error("Invalid nested grid provided to addTextWidgetToNestedGrid");
        return;
    }

    nestedGrid.addWidget({
        x: 0,
        y: 0,
        w: 6,
        h: 6,
        content: `
            <div class="text-widget p-2" style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
                <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
                    <div>
                        <i class="las la-font"></i> Text Widget
                    </div>
                    <div>
                        <button class="btn btn-sm btn-link text-dark" onclick="removeWidget(this)">
                            <i class="las la-times"></i>
                        </button>
                    </div>
                </div>
                <div id="${widgetId}" class="text-container" style="flex: 1; overflow: auto; padding: 10px;">
                    <div class="text-content" contenteditable="true" style="
                        min-height: 100px;
                        padding: 10px;
                        border: 1px solid #e0e0e0;
                        border-radius: 0px;
                        outline: none;
                        font-family: Arial, sans-serif;
                        font-size: 14px;
                        line-height: 1.5;
                        color: #333;
                        background-color: #fff;
                    ">
                        <p>This is a text widget. Click here to edit the content...</p>
                        <p>You can:</p>
                        <ul>
                            <li>Type any text you want</li>
                            <li>Format it with basic HTML</li>
                            <li>Resize and move this widget</li>
                            <li>Drag it in and out of sections</li>
                        </ul>
                    </div>
                </div>
            </div>
        `
    });
}

/**
 * Adds a table widget to the nested grid (for section containers)
 */
function addTableWidgetToNestedGrid(nestedGrid, widgetId) {
    if (!nestedGrid || typeof nestedGrid.addWidget !== 'function') {
        console.error("Invalid nested grid provided to addTableWidgetToNestedGrid");
        return;
    }

    nestedGrid.addWidget({
        x: 6,
        y: 0,
        w: 6,
        h: 6,
        content: `
            <div class="table-widget p-2" style="height: 100%; display: flex; flex-direction: column; overflow: hidden;">
                <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
                    <div>
                        <i class="las la-table"></i> Table Widget
                    </div>
                    <div>
                        <button class="btn btn-sm btn-link text-dark" onclick="removeWidget(this)">
                            <i class="las la-times"></i>
                        </button>
                    </div>
                </div>
                <div id="${widgetId}" class="table-container" style="flex: 1; overflow: auto;">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th contenteditable="true">Column 1</th>
                                    <th contenteditable="true">Column 2</th>
                                    <th contenteditable="true">Column 3</th>
                                    <th contenteditable="true">Column 4</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td contenteditable="true">Row 1, Col 1</td>
                                    <td contenteditable="true">Row 1, Col 2</td>
                                    <td contenteditable="true">Row 1, Col 3</td>
                                    <td contenteditable="true">Row 1, Col 4</td>
                                </tr>
                                <tr>
                                    <td contenteditable="true">Row 2, Col 1</td>
                                    <td contenteditable="true">Row 2, Col 2</td>
                                    <td contenteditable="true">Row 2, Col 3</td>
                                    <td contenteditable="true">Row 2, Col 4</td>
                                </tr>
                                <tr>
                                    <td contenteditable="true">Row 3, Col 1</td>
                                    <td contenteditable="true">Row 3, Col 2</td>
                                    <td contenteditable="true">Row 3, Col 3</td>
                                    <td contenteditable="true">Row 3, Col 4</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `
    });
}

/**
 * Removes a widget from its parent grid
 */
function removeWidget(button) {
    const widget = button.closest('.grid-stack-item');
    if (widget && widget.gridstack) {
        widget.gridstack.removeWidget(widget);
    } else {
        // Fallback: remove from DOM
        widget.remove();
    }
}

// ============================================================================
// INITIALIZATION
// ============================================================================

/**
 * Initialize when DOM is ready (auto-loading disabled)
 */
document.addEventListener("DOMContentLoaded", function() {
    // Wait for the main grid to be initialized
    const checkGridReady = setInterval(() => {
        if (window.grid) {
            clearInterval(checkGridReady);
            console.log("Section container system ready. Use createTestSectionWithWidgets() to create sections.");
        }
    }, 100);

    // Timeout after 10 seconds
    setTimeout(() => {
        clearInterval(checkGridReady);
        if (!window.grid) {
            console.error("Main grid failed to initialize within timeout period.");
        }
    }, 10000);
});

// ============================================================================
// USAGE EXAMPLES
// ============================================================================

/*
USAGE:

1. Manual Creation:
   - Call createTestSectionWithWidgets() from console or button click
   - Example: createTestSectionWithWidgets()

2. Button Integration:
   - Add onclick="createTestSectionWithWidgets()" to any button
   - The function will create a section with text and table widgets

3. Function Parameters:
   - createTestSectionWithWidgets() - Creates section with default settings
   - createTestSectionWithWidgets("custom-id") - Creates section with custom ID

4. Widget Management:
   - Text widgets are editable - click to edit content
   - Table widgets have editable cells
   - Use remove buttons to delete individual widgets
   - Use section remove button to delete entire section

5. Grid Features:
   - Drag and drop widgets within sections
   - Resize widgets by dragging corners
   - Move sections around the main grid
   - Responsive layout that adapts to different screen sizes
*/
