// Add an area chart widget using amCharts v5

// Add an area chart widget using amCharts v5
window.addAreaChartWidget = function() {
  console.log("Adding area chart widget");
  const chartId = "area-chart-" + Date.now();

  // Get the grid instance
  const grid = document.querySelector(".grid-stack").gridstack;

  // Add the widget to the grid
  const widget = grid.addWidget({
    x: 0,
    y: 0,
    w: 6,
    h: 8,
    content: `
      <div class="area-chart-widget p-2" style="height: 100%; display: flex; flex-direction: column;">
        <div class="widget-header mb-2 fw-bold d-flex justify-content-between align-items-center">
          <div>
            Area Chart
          </div>
          <div>
            <button class="btn btn-sm btn-link text-dark"
                    data-bs-toggle="offcanvas"
                    data-bs-target="#areaChartSettings"
                    aria-controls="areaChartSettings"
                    onclick="initAreaChartSettings('${chartId}')">
              <i class="las la-cog"></i>
            </button>
            <button class="btn btn-sm btn-link text-dark ms-1"
                    onclick="removeWidget(this)">
              <i class="las la-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body" style="flex: 1 1 auto; min-height: 0; position: relative; display: flex; flex-direction: column; height: 100%;">
          <div id="${chartId}" class="chart-container" style="flex: 1; width: 100%; height: 100%; min-height: 300px; position: relative;"></div>
        </div>
      </div>
    `,
  });

  // Initialize the chart with a slight delay to ensure DOM is ready
  window.setTimeout(function () {
    try {
      console.log("Widget added to grid, now initializing chart");
      initAreaChart(chartId);
    } catch (error) {
      console.error("Error initializing chart:", error);
    }
  }, 100);

  return widget;
};

// Initialize the area chart
window.initAreaChart = function(chartId) {
  console.log("Initializing area chart:", chartId);

  // Get the chart container
  const chartContainer = document.getElementById(chartId);
  if (!chartContainer) {
    console.error("Chart container not found:", chartId);
    return;
  }

  // Force overflow visible on parent gridstack item content
  const gridStackItemContent = chartContainer.closest(".grid-stack-item-content");
  if (gridStackItemContent) {
    gridStackItemContent.style.overflow = "visible";
    gridStackItemContent.style.height = "100%";
  }

  // Get the widget container
  const widgetContainer = chartContainer.closest(".area-chart-widget");
  if (widgetContainer) {
    widgetContainer.style.height = "100%";
    widgetContainer.style.display = "flex";
    widgetContainer.style.flexDirection = "column";
  }

  // Dispose of any existing chart instance
  if (chartContainer.chart) {
    chartContainer.chart.root.dispose();
  }

  // Clear the container
  chartContainer.innerHTML = "";

  try {
    // Create root element
    const root = am5.Root.new(chartId);

    // Set themes
    root.setThemes([am5themes_Animated.new(root)]);

    // Create chart
    const chart = root.container.children.push(
      am5xy.XYChart.new(root, {
        panX: false,
        panY: false,
        wheelX: "none",
        wheelY: "none",
        paddingLeft: 0
      })
    );

    // Apply brand colors
    if (window.chartConfig && window.chartConfig.brandColors) {
      const brandColorSet = am5.ColorSet.new(root, {
        colors: window.chartConfig.brandColors.map(color => am5.color(color)),
        reuse: true
      });
      chart.set("colors", brandColorSet);
      console.log("Applied brand colors to area chart");
    } else {
      // Default brand colors if not available in window.chartConfig
      const defaultBrandColors = [
        "#00b19c", "#3bcd3f", "#007365", "#8dbac4", "#02104f"
      ];
      const brandColorSet = am5.ColorSet.new(root, {
        colors: defaultBrandColors.map(color => am5.color(color)),
        reuse: true
      });
      chart.set("colors", brandColorSet);
      console.log("Applied default brand colors to area chart");
    }

    // Add title
    const title = chart.children.push(am5.Label.new(root, {
      text: "Website Traffic Over Time",
      fontSize: 14,
      fontWeight: "500",
      textAlign: "center",
      x: am5.p50,
      centerX: am5.p50,
      paddingTop: 0,
      paddingBottom: 20
    }));

    // Create axes
    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(root, {
        min: 0,
        renderer: am5xy.AxisRendererY.new(root, {})
      })
    );

    const xAxis = chart.xAxes.push(
      am5xy.DateAxis.new(root, {
        baseInterval: { timeUnit: "day", count: 1 },
        renderer: am5xy.AxisRendererX.new(root, {})
      })
    );

    // Create series
    const series = chart.series.push(
      am5xy.LineSeries.new(root, {
        name: "Visitors",
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "value",
        valueXField: "date",
        connect: false,
        tooltip: am5.Tooltip.new(root, {
          labelText: "{valueY}"
        })
      })
    );

    // Make area fill visible with brand colors
    series.fills.template.setAll({
      fillOpacity: 0.5,
      visible: true
    });

    // Set stroke properties with brand color
    series.strokes.template.setAll({
      strokeWidth: 2
    });

    // Create static data
    const data = [
      { date: new Date(2023, 0, 1).getTime(), value: 1000 },
      { date: new Date(2023, 0, 2).getTime(), value: 1200 },
      { date: new Date(2023, 0, 3).getTime(), value: 1600 },
      { date: new Date(2023, 0, 4).getTime(), value: 1800 },
      { date: new Date(2023, 0, 5).getTime(), value: 1300 },
      { date: new Date(2023, 0, 6).getTime(), value: 1100 },
      { date: new Date(2023, 0, 7).getTime(), value: 1400 },
      { date: new Date(2023, 0, 8).getTime(), value: 1700 },
      { date: new Date(2023, 0, 9).getTime(), value: 1900 },
      { date: new Date(2023, 0, 10).getTime(), value: 2100 },
      { date: new Date(2023, 0, 11).getTime(), value: 2300 },
      { date: new Date(2023, 0, 12).getTime(), value: 2000 },
      { date: new Date(2023, 0, 13).getTime(), value: 1800 },
      { date: new Date(2023, 0, 14).getTime(), value: 2100 },
      { date: new Date(2023, 0, 15).getTime(), value: 2400 },
      { date: new Date(2023, 0, 16).getTime(), value: 2700 },
      { date: new Date(2023, 0, 17).getTime(), value: 2900 },
      { date: new Date(2023, 0, 18).getTime(), value: 3100 },
      { date: new Date(2023, 0, 19).getTime(), value: 3300 },
      { date: new Date(2023, 0, 20).getTime(), value: 3400 },
      { date: new Date(2023, 0, 21).getTime(), value: 3200 },
      { date: new Date(2023, 0, 22).getTime(), value: 3500 },
      { date: new Date(2023, 0, 23).getTime(), value: 3800 },
      { date: new Date(2023, 0, 24).getTime(), value: 4100 },
      { date: new Date(2023, 0, 25).getTime(), value: 4300 },
      { date: new Date(2023, 0, 26).getTime(), value: 4000 },
      { date: new Date(2023, 0, 27).getTime(), value: 4200 },
      { date: new Date(2023, 0, 28).getTime(), value: 4500 },
      { date: new Date(2023, 0, 29).getTime(), value: 4700 },
      { date: new Date(2023, 0, 30).getTime(), value: 5000 }
    ];

    // Set data
    series.data.setAll(data);

    // No scrollbar

    // No cursor for zooming

    // Make stuff animate on load
    series.appear(1000);
    chart.appear(1000, 100);

    // Store chart instance on container for future reference
    chartContainer.chart = chart;

    return chart;
  } catch (error) {
    console.error("Error creating area chart:", error);
    chartContainer.innerHTML = `<div class="alert alert-danger">Error creating chart: ${error.message}</div>`;
  }
};

// Function to generate sample data
function generateSampleData() {
  const data = [];

  // Use fixed dates for more predictable behavior
  const startDate = new Date(2023, 0, 1); // January 1, 2023

  console.log("Start date:", startDate);

  // Generate 31 days of data
  for (let i = 0; i < 31; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);

    // Generate a random value with an upward trend
    const baseValue = 1000 + Math.random() * 500;
    const trendValue = baseValue + i * 20;
    const randomFactor = Math.random() * 300 - 150;

    const dataPoint = {
      date: date.getTime(),
      value: Math.round(trendValue + randomFactor)
    };

    data.push(dataPoint);
  }

  // Log the data for debugging
  console.log("Sample data generated:", data.length, "points");
  console.log("First point:", new Date(data[0].date), data[0].value);
  console.log("Last point:", new Date(data[data.length-1].date), data[data.length-1].value);

  return data;
}

// Function to initialize settings panel for the chart
window.initAreaChartSettings = function(chartId) {
  console.log("Initializing settings for area chart:", chartId);

  const settingsPanel = document.getElementById("areaChartSettings");
  if (!settingsPanel) {
    console.error("Settings panel not found");
    return;
  }

  // Store the current chart ID
  settingsPanel.dataset.currentChart = chartId;

  // Initialize offcanvas with proper options
  const bsOffcanvas = new bootstrap.Offcanvas(settingsPanel, {
    backdrop: true,
    keyboard: true,
    scroll: false,
  });

  // Remove any existing event listeners
  settingsPanel.removeEventListener(
    "hidden.bs.offcanvas",
    handleBackdropCleanup
  );
  // Add event listener for when offcanvas is hidden
  settingsPanel.addEventListener("hidden.bs.offcanvas", handleBackdropCleanup);

  // Show the offcanvas
  bsOffcanvas.show();

  // Load current chart settings
  loadAreaChartSettings(chartId);
};

// Function to load current chart settings
function loadAreaChartSettings(chartId) {
  const chartContainer = document.getElementById(chartId);
  if (!chartContainer || !chartContainer.chart) {
    console.error("Chart not found or not initialized");
    return;
  }

  const chart = chartContainer.chart;

  // Get chart title
  const title = chart.children.getIndex(0);
  if (title) {
    document.getElementById("areaChartSettings-chartTitle").value = title.get("text");
  }

  // Get fill opacity
  const series = chart.series.getIndex(0);
  if (series) {
    const fillOpacity = series.fills.template.get("fillOpacity");
    document.getElementById("areaChartSettings-fillOpacity").value = fillOpacity * 100;
    document.querySelector('[for="areaChartSettings-fillOpacity"] .value-display').textContent = Math.round(fillOpacity * 100) + "%";
  }


}

// Function to apply settings from the settings panel
window.applyAreaChartSettings = function() {
  console.log("Applying area chart settings");

  const settingsPanel = document.getElementById("areaChartSettings");
  const chartId = settingsPanel.dataset.currentChart;

  if (!chartId) {
    console.error("No chart ID found in settings panel");
    return;
  }

  const chartContainer = document.getElementById(chartId);
  if (!chartContainer || !chartContainer.chart) {
    console.error("Chart not found or not initialized");
    return;
  }

  const chart = chartContainer.chart;

  // Get settings values
  const chartTitle = document.getElementById("areaChartSettings-chartTitle").value;
  const fillOpacity = document.getElementById("areaChartSettings-fillOpacity").value / 100;

  // Update chart title
  const title = chart.children.getIndex(0);
  if (title) {
    title.set("text", chartTitle);
  }

  // Update fill opacity
  const series = chart.series.getIndex(0);
  if (series) {
    series.fills.template.set("fillOpacity", fillOpacity);
  }



  // Close the settings panel
  const bsOffcanvas = bootstrap.Offcanvas.getInstance(settingsPanel);
  if (bsOffcanvas) {
    bsOffcanvas.hide();
  }
};

// Function to handle fill opacity change
window.handleFillOpacityChange = function(value) {
  document.querySelector('[for="areaChartSettings-fillOpacity"] .value-display').textContent = value + "%";
};

// Add the widget to the widget gallery
document.addEventListener("DOMContentLoaded", function () {
  // Add widget to the chart category in the widget gallery
  const chartCategory = document.querySelector('.widget-gallery[data-category="charts"]');
  if (chartCategory) {
    const widgetItem = document.createElement("div");
    widgetItem.className = "widget-item";
    widgetItem.setAttribute("draggable", "true");
    widgetItem.setAttribute("data-gs-width", "6");
    widgetItem.setAttribute("data-gs-height", "8");
    widgetItem.innerHTML = `
      <i class="las la-chart-area"></i>
      <span class="widget-label">Area Chart</span>
    `;
    widgetItem.addEventListener("click", function () {
      addAreaChartWidget();
    });
    chartCategory.appendChild(widgetItem);
  }
});
